<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover">
    
    <!-- 🔧 PWA模式底部白边强力修复 - 必须最早执行 -->
    <script src="assets/js/pwa-fix.js"></script>

    <!-- 🔧 项目配置文件 - 必须早期加载 -->
    <script src="assets/js/config.js"></script>

    <!-- 新增HBuilder X优化meta标签 -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="format-detection" content="telephone=no">
    <!-- PWA支持 -->
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#d4af37">
    
    <title>一念修仙 - 游戏设置</title>
    
    <!-- 原有CSS保持不变 -->
    <link rel="stylesheet" href="assets/css/global.css">
    <link rel="stylesheet" href="assets/css/settings.css">
    <link rel="stylesheet" href="assets/css/common-navigation.css">
    
    <!-- 🔧 视口高度修复脚本 -->
    <script src="assets/js/viewport-height-fix.js"></script>
    
    <!-- 🔑 全局登录检查系统 -->
    <script src="assets/js/auth-check.js"></script>

</head>
<body>
    <div class="main-container">
        <!-- 头部 -->
        <div class="header">
            <h1>游戏设置</h1>
            <p>管理您的游戏偏好设置</p>
        </div>

        <!-- 设置列表 -->
        <div class="settings-grid">
            <!-- 装备拾取设置 -->
            <div class="setting-card" onclick="openPickupSettingsModal()">
                <div class="setting-header">
                    <div class="setting-title">
                        <span class="setting-icon">⚔️</span>
                        <span class="setting-name">装备拾取设置</span>
                    </div>
                    <span class="setting-arrow">›</span>
                </div>
                <div class="setting-description">设置战斗中掉落装备的拾取过滤条件</div>
            </div>

            <!-- 兑换码 -->
            <div class="setting-card" onclick="openRedeemModal()">
                <div class="setting-header">
                    <div class="setting-title">
                        <span class="setting-icon">🎁</span>
                        <span class="setting-name">兑换码</span>
                    </div>
                    <span class="setting-arrow">›</span>
                </div>
                <div class="setting-description">输入兑换码获取灵石、礼包等游戏奖励</div>
            </div>

            <!-- 游戏说明 -->
            <div class="setting-card" onclick="openGameGuideModal()">
                <div class="setting-header">
                    <div class="setting-title">
                        <span class="setting-icon">📖</span>
                        <span class="setting-name">游戏说明</span>
                    </div>
                    <span class="setting-arrow">›</span>
                </div>
                <div class="setting-description">了解游戏玩法、规则和操作指南</div>
            </div>

            <!-- 音效开关 -->
            <div class="setting-card">
                <div class="setting-header">
                    <div class="setting-title">
                        <span class="setting-icon">🔊</span>
                        <span class="setting-name">音效开关</span>
                    </div>
                    <label class="switch">
                        <input type="checkbox" id="soundSwitch" onchange="toggleSound()">
                        <span class="slider"></span>
                    </label>
                </div>
                <div class="setting-description">开启或关闭游戏背景音乐和音效</div>
            </div>

            <!-- 退出登录 -->
            <div class="setting-card" onclick="confirmLogout()">
                <div class="setting-header">
                    <div class="setting-title">
                        <span class="setting-icon">🚪</span>
                        <span class="setting-name">退出登录</span>
                    </div>
                    <span class="setting-arrow">›</span>
                </div>
                <div class="setting-description">安全退出当前账号</div>
            </div>
        </div>
    </div>

    <!-- 装备拾取设置弹窗 -->
    <div class="modal-overlay" id="pickupSettingsModal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">装备拾取设置</h3>
                <button class="modal-close" onclick="closeModal('pickupSettingsModal')">&times;</button>
            </div>
            <div class="modal-content">
                <div class="pickup-settings-container">
                    <h4 style="color: #d4af37; margin-bottom: 10px; font-size: 14px;">⚔️ 装备品质过滤</h4>
                    <p style="color: #bdc3c7; font-size: 11px; margin-bottom: 10px; line-height: 1.3;">选择要拾取的装备品质，未选中的品质将自动回收为金币</p>
                    
                    <div class="quality-filter-grid" id="qualityFilterGrid">
                        <!-- 品质选项将通过JavaScript动态生成 -->
                    </div>
                    
                    <div class="realm-filter-section">
                        <h4 style="color: #d4af37; margin-bottom: 8px; font-size: 14px;">🏔️ 境界过滤</h4>
                        <label class="realm-filter-option">
                            <input type="checkbox" id="filterBelowRealm">
                            <span class="checkmark"></span>
                            <span class="filter-text">自动回收低于当前大境界的装备</span>
                        </label>
                        <p style="color: #bdc3c7; font-size: 10px; margin-top: 4px; line-height: 1.2;">
                            开启后，等级需求低于当前大境界的装备将被自动回收
                        </p>
                    </div>
                    
                    <div class="pickup-settings-buttons">
                        <button class="pickup-save-button" onclick="savePickupSettings()">保存设置</button>
                        <button class="pickup-reset-button" onclick="resetPickupSettings()">恢复默认</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 兑换码弹窗 -->
    <div class="modal-overlay" id="redeemModal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">兑换码</h3>
                <button class="modal-close" onclick="closeModal('redeemModal')">&times;</button>
            </div>
            <div class="modal-content">
                <p>输入兑换码获取游戏奖励：</p>
                <div class="code-input-group">
                    <input type="text" id="redeemCode" class="code-input" placeholder="请输入兑换码" maxlength="20">
                    <button class="code-button" onclick="redeemCode()">兑换</button>
                </div>
                <div style="font-size: 12px; color: #bdc3c7; margin-top: 10px;">
                    <p>• 兑换码区分大小写</p>
                    <p>• 每个兑换码只能使用一次</p>
                    <p>• 过期兑换码无法使用</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 游戏说明弹窗 -->
    <div class="modal-overlay" id="gameGuideModal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">游戏说明</h3>
                <button class="modal-close" onclick="closeModal('gameGuideModal')">&times;</button>
            </div>
            <div class="modal-content">
                <h4 style="color: #d4af37; margin-bottom: 15px;">🎮 基础玩法</h4>
                <ul>
                    <li>这是一个自动战斗的修仙类游戏</li>
                    <li>玩家通过历练提升等级和境界</li>
                    <li>收集装备和武器增强实力</li>
                    <li>参与各种活动获得丰厚奖励</li>
                </ul>

                <h4 style="color: #d4af37; margin: 20px 0 15px 0;">⚔️ 战斗系统</h4>
                <ul>
                    <li>战斗为自动进行，无需手动操作</li>
                    <li>装备不同武器可使用不同技能</li>
                    <li>技能包括：飞剑、万剑诀、掌心雷、火球术、巨剑术</li>
                    <li>击败敌人可获得经验、金币和装备</li>
                </ul>

                <h4 style="color: #d4af37; margin: 20px 0 15px 0;">💎 资源系统</h4>
                <ul>
                    <li><strong>金币：</strong>用于购买基础物品和装备</li>
                    <li><strong>灵石：</strong>高级货币，可用于更名等特殊功能</li>
                    <li><strong>灵气：</strong>修炼和突破境界所需</li>
                </ul>

                <h4 style="color: #d4af37; margin: 20px 0 15px 0;">🏔️ 境界体系</h4>
                <p style="margin-bottom: 10px; color: #ccc;">游戏包含280个境界等级，按大境界划分如下：</p>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; font-size: 13px;">
                    <div style="color: #87ceeb;">开光期 (1-10)</div>
                    <div style="color: #87ceeb;">灵虚期 (11-20)</div>
                    <div style="color: #87ceeb;">辟谷期 (21-30)</div>
                    <div style="color: #87ceeb;">心动期 (31-40)</div>
                    <div style="color: #98fb98;">元化期 (41-50)</div>
                    <div style="color: #98fb98;">元婴期 (51-60)</div>
                    <div style="color: #98fb98;">离合期 (61-70)</div>
                    <div style="color: #98fb98;">空冥期 (71-80)</div>
                    <div style="color: #dda0dd;">寂灭期 (81-90)</div>
                    <div style="color: #dda0dd;">大乘期 (91-100)</div>
                    <div style="color: #dda0dd;">渡劫期 (101-110)</div>
                    <div style="color: #dda0dd;">凡仙期 (111-120)</div>
                    <div style="color: #ffd700;">地仙期 (121-130)</div>
                    <div style="color: #ffd700;">天仙期 (131-140)</div>
                    <div style="color: #ffd700;">真仙期 (141-150)</div>
                    <div style="color: #ffd700;">太乙真仙期 (151-160)</div>
                    <div style="color: #ff6347;">太乙金仙期 (161-170)</div>
                    <div style="color: #ff6347;">太乙玄仙期 (171-180)</div>
                    <div style="color: #ff6347;">大罗真仙期 (181-190)</div>
                    <div style="color: #ff6347;">大罗金仙期 (191-200)</div>
                    <div style="color: #ff1493;">大罗玄仙期 (201-210)</div>
                    <div style="color: #ff1493;">准圣期 (211-220)</div>
                    <div style="color: #ff1493;">教主期 (221-230)</div>
                    <div style="color: #ff1493;">混元期 (231-240)</div>
                    <div style="color: #8a2be2;">混元金仙期 (241-250)</div>
                    <div style="color: #8a2be2;">混元至仙期 (251-260)</div>
                    <div style="color: #8a2be2;">天道期 (261-270)</div>
                    <div style="color: #8a2be2;">鸿蒙至元期 (271-280)</div>
                </div>
                <p style="margin-top: 10px; color: #ccc; font-size: 12px;">💡 装备掉落与怪物境界相匹配，主要掉落当前大境界装备</p>

                <h4 style="color: #d4af37; margin: 20px 0 15px 0;">🏆 进阶玩法</h4>
                <ul>
                    <li>通关更多地图解锁新内容</li>
                    <li>收集稀有装备提升战力</li>
                    <li>参与排行榜与其他玩家竞争</li>
                    <li>加入宗门体验团队玩法</li>
                </ul>
            </div>
        </div>
    </div>

    <script src="assets/js/global-music-manager.js"></script>
    <script src="assets/js/common-navigation.js"></script>
    <script>
        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            console.log('设置页面加载完成');
            
            // 加载音效设置
            loadSoundSettings();
            
            // 🎵 全局音乐管理器会自动处理音乐播放
            
            // 检查登录状态
            checkLoginStatus();
        });

        // 检查登录状态
        function checkLoginStatus() {
            fetch(window.GameConfig ? window.GameConfig.getApiUrl('user_info.php') : '../src/api/user_info.php')
                .then(response => response.json())
                .then(data => {
                    if (!data.success) {
                        // 未登录，禁用某些功能
                        console.log('用户未登录，禁用部分功能');
                        // 可以在这里禁用需要登录的功能
                    }
                })
                .catch(error => {
                    console.error('检查登录状态失败:', error);
                });
        }

        // 打开装备拾取设置弹窗
        function openPickupSettingsModal() {
            loadPickupSettings();
            document.getElementById('pickupSettingsModal').style.display = 'flex';
        }

        // 加载装备拾取设置
        function loadPickupSettings() {
            console.log('🔄 开始加载装备拾取设置...');
            
            // 先加载品质列表
            fetch(window.GameConfig ? window.GameConfig.getApiUrl('equipment_pickup_settings.php?action=get_quality_list') : '../src/api/equipment_pickup_settings.php?action=get_quality_list')
                .then(response => {
                    console.log('📡 品质列表API响应状态:', response.status);
                    return response.text();
                })
                .then(text => {
                    console.log('📄 品质列表API原始响应:', text);
                    try {
                        const data = JSON.parse(text);
                        if (data.success) {
                            console.log('✅ 品质列表获取成功:', data.qualities);
                            renderQualityFilter(data.qualities);
                            // 然后加载用户设置
                            return fetch(window.GameConfig ? window.GameConfig.getApiUrl('equipment_pickup_settings.php?action=get_settings') : '../src/api/equipment_pickup_settings.php?action=get_settings');
                        } else {
                            throw new Error('获取品质列表失败: ' + data.message);
                        }
                    } catch (e) {
                        console.error('❌ 解析品质列表JSON失败:', e);
                        throw new Error('服务器返回格式错误');
                    }
                })
                .then(response => {
                    console.log('📡 用户设置API响应状态:', response.status);
                    return response.text();
                })
                .then(text => {
                    console.log('📄 用户设置API原始响应:', text);
                    try {
                        const data = JSON.parse(text);
                        if (data.success) {
                            console.log('✅ 用户设置获取成功:', data.settings);
                            applyPickupSettings(data.settings);
                        } else {
                            showMessage('加载设置失败：' + data.message, 'error');
                        }
                    } catch (e) {
                        console.error('❌ 解析用户设置JSON失败:', e);
                        showMessage('服务器返回格式错误', 'error');
                    }
                })
                .catch(error => {
                    console.error('❌ 加载装备拾取设置失败:', error);
                    showMessage('加载设置失败：' + error.message, 'error');
                });
        }

        // 渲染品质过滤选项
        function renderQualityFilter(qualities) {
            console.log('🎨 开始渲染品质过滤选项:', qualities);
            const grid = document.getElementById('qualityFilterGrid');
            
            if (!grid) {
                console.error('❌ 找不到qualityFilterGrid元素');
                return;
            }
            
            grid.innerHTML = '';
            
            if (!qualities || !Array.isArray(qualities)) {
                console.error('❌ 品质数据无效:', qualities);
                return;
            }
            
            qualities.forEach((quality, index) => {
                console.log(`🎨 渲染品质 ${index + 1}:`, quality);
                const qualityItem = document.createElement('div');
                qualityItem.className = 'quality-filter-item';
                qualityItem.innerHTML = `
                    <label class="quality-checkbox">
                        <input type="checkbox" value="${quality.name}" data-quality="${quality.name}">
                        <span class="quality-checkmark" style="border-color: ${quality.color};"></span>
                        <span class="quality-name" style="color: ${quality.color};">${quality.name}</span>
                    </label>
                `;
                grid.appendChild(qualityItem);
            });
            
            console.log('✅ 品质过滤选项渲染完成，共', qualities.length, '个品质');
        }

        // 应用装备拾取设置
        function applyPickupSettings(settings) {
            // 设置品质过滤
            const qualityCheckboxes = document.querySelectorAll('[data-quality]');
            qualityCheckboxes.forEach(checkbox => {
                const quality = checkbox.getAttribute('data-quality');
                checkbox.checked = settings.quality_filter.includes(quality);
            });
            
            // 设置境界过滤
            document.getElementById('filterBelowRealm').checked = settings.filter_below_realm;
        }

        // 保存装备拾取设置
        function savePickupSettings() {
            const qualityFilter = [];
            const qualityCheckboxes = document.querySelectorAll('[data-quality]:checked');
            qualityCheckboxes.forEach(checkbox => {
                qualityFilter.push(checkbox.getAttribute('data-quality'));
            });
            
            const filterBelowRealm = document.getElementById('filterBelowRealm').checked;
            
            const settings = {
                quality_filter: qualityFilter,
                filter_below_realm: filterBelowRealm
            };
            
            // 禁用保存按钮防止重复提交
            const saveButton = document.querySelector('.pickup-save-button');
            saveButton.disabled = true;
            saveButton.textContent = '保存中...';

            fetch(window.GameConfig ? window.GameConfig.getApiUrl('equipment_pickup_settings.php?action=save_settings') : '../src/api/equipment_pickup_settings.php?action=save_settings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(settings)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('装备拾取设置已保存', 'success');
                    closeModal('pickupSettingsModal');
                } else {
                    showMessage('保存失败：' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('保存装备拾取设置失败:', error);
                showMessage('保存失败，请稍后重试', 'error');
            })
            .finally(() => {
                saveButton.disabled = false;
                saveButton.textContent = '保存设置';
            });
        }

        // 重置装备拾取设置
        function resetPickupSettings() {
            if (confirm('确定要恢复默认设置吗？')) {
                // 全选所有品质
                const qualityCheckboxes = document.querySelectorAll('[data-quality]');
                qualityCheckboxes.forEach(checkbox => {
                    checkbox.checked = true;
                });
                
                // 关闭境界过滤
                document.getElementById('filterBelowRealm').checked = false;
                
                showMessage('已恢复默认设置，请点击保存生效', 'info');
            }
        }

        // 打开兑换码弹窗
        function openRedeemModal() {
            document.getElementById('redeemCode').value = '';
            document.getElementById('redeemModal').style.display = 'flex';
        }

        // 打开游戏说明弹窗
        function openGameGuideModal() {
            document.getElementById('gameGuideModal').style.display = 'flex';
        }

        // 关闭弹窗
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // 兑换码功能
        function redeemCode() {
            const code = document.getElementById('redeemCode').value.trim();
            
            if (!code) {
                showMessage('请输入兑换码', 'error');
                return;
            }
            
            // 禁用按钮防止重复提交
            const button = document.querySelector('.code-button');
            button.disabled = true;
            button.textContent = '兑换中...';

            fetch(window.GameConfig ? window.GameConfig.getApiUrl('redeem_code.php') : '../src/api/redeem_code.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    code: code
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage(data.message, 'success');
                    document.getElementById('redeemCode').value = '';
                    closeModal('redeemModal');
                } else {
                    showMessage('兑换失败：' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('兑换码处理失败:', error);
                showMessage('网络错误，请稍后重试', 'error');
            })
            .finally(() => {
                button.disabled = false;
                button.textContent = '兑换';
            });
        }

        // 音效开关
        function toggleSound() {
            const isEnabled = document.getElementById('soundSwitch').checked;
            localStorage.setItem('soundEnabled', isEnabled ? 'true' : 'false');
            
            // 通知音乐管理器更新状态
            if (window.musicManager) {
                window.musicManager.setMusicEnabled(isEnabled);
            }
            
            if (isEnabled) {
                showMessage('音效已开启', 'success');
            } else {
                showMessage('音效已关闭', 'info');
            }
            
            console.log('音效状态:', isEnabled ? '开启' : '关闭');
        }

        // 加载音效设置
        function loadSoundSettings() {
            const soundEnabled = localStorage.getItem('soundEnabled') === 'true'; // 新账号默认关闭
            document.getElementById('soundSwitch').checked = soundEnabled;
        }

        // 确认退出登录
        function confirmLogout() {
            if (confirm('确定要退出登录吗？')) {
                logout();
            }
        }

        // 退出登录
        function logout() {
            fetch(window.GameConfig ? window.GameConfig.getApiUrl('logout.php') : '../src/api/logout.php', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('退出登录成功', 'success');
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 1500);
                } else {
                    showMessage('退出登录失败', 'error');
                }
            })
            .catch(error => {
                console.error('退出登录失败:', error);
                showMessage('退出登录失败', 'error');
            });
        }

        // 显示消息提示
        function showMessage(text, type = 'info') {
            const existingMessage = document.querySelector('.message');
            if (existingMessage) {
                existingMessage.remove();
            }
            
            const message = document.createElement('div');
            message.className = `message ${type}`;
            message.textContent = text;
            document.body.appendChild(message);
            
            setTimeout(() => {
                if (message.parentNode) {
                    message.parentNode.removeChild(message);
                }
            }, 3000);
        }

        // 点击外部关闭弹窗
        document.addEventListener('click', function(event) {
            if (event.target.classList.contains('modal-overlay')) {
                event.target.style.display = 'none';
            }
        });

        // ESC键关闭弹窗
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const modals = document.querySelectorAll('.modal-overlay');
                modals.forEach(modal => {
                    if (modal.style.display === 'flex') {
                        modal.style.display = 'none';
                    }
                });
            }
        });

        // 兑换码输入框回车提交
        document.getElementById('redeemCode').addEventListener('keydown', function(event) {
            if (event.key === 'Enter') {
                redeemCode();
            }
        });
    </script>
</body>
</html> 