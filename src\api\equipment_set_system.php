<?php

/**
 * 装备套装系统 API
 * 基于实际的game_item_sets表结构实现
 */

header('Content-Type: application/json; charset=utf-8');
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/auth.php';

// 检查维护模式
if (isMaintenanceMode()) {
    echo json_encode([
        'success' => false,
        'message' => '游戏正在维护中，请稍后再试',
        'maintenance' => true
    ]);
    exit;
}

// 记录API调用（如果开启了调试）
if (DEBUG_LOG_API_CALLS) {
    writeLog("API调用: equipment_set_system.php", 'DEBUG', 'api.log');
}

// 检查用户认证
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => '用户未登录'], JSON_UNESCAPED_UNICODE);
    exit;
}

$character_id = get_character_id();
if (!$character_id) {
    echo json_encode(['success' => false, 'message' => '角色不存在'], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    $pdo = getDatabase();
    $action = isset($_GET['action']) ? $_GET['action'] : (isset($_POST['action']) ? $_POST['action'] : '');

    switch ($action) {
        case 'get_character_sets':
            echo json_encode(getCharacterSets($pdo, $character_id), JSON_UNESCAPED_UNICODE);
            break;

        case 'get_set_details':
            $set_id = isset($_GET['set_id']) ? intval($_GET['set_id']) : 0;
            echo json_encode(getSetDetails($pdo, $set_id), JSON_UNESCAPED_UNICODE);
            break;

        case 'get_all_sets':
            echo json_encode(getAllSets($pdo), JSON_UNESCAPED_UNICODE);
            break;

        case 'calculate_set_power':
            echo json_encode(calculateSetPower($pdo, $character_id), JSON_UNESCAPED_UNICODE);
            break;

        case 'calculate_real_set_power_difference':
            echo json_encode(calculateRealSetPowerDifference($pdo, $character_id), JSON_UNESCAPED_UNICODE);
            break;

        case 'simulate_set_effect':
            $set_id = isset($_POST['set_id']) ? intval($_POST['set_id']) : 0;
            $pieces_count = isset($_POST['pieces_count']) ? intval($_POST['pieces_count']) : 2;
            echo json_encode(simulateSetEffect($pdo, $set_id, $pieces_count), JSON_UNESCAPED_UNICODE);
            break;

        default:
            echo json_encode(['success' => false, 'message' => '未知的操作'], JSON_UNESCAPED_UNICODE);
    }
} catch (Exception $e) {
    error_log("套装系统错误: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => '系统错误'], JSON_UNESCAPED_UNICODE);
}

/**
 * 获取角色当前套装状态
 */
function getCharacterSets($pdo, $character_id)
{
    try {
        // 获取角色已装备的物品
        $stmt = $pdo->prepare("
            SELECT ce.slot_type, gi.id as item_id, gi.item_name, gi.set_id, 
                   gis.set_name, gis.rarity
            FROM character_equipment ce
            JOIN game_items gi ON ce.item_id = gi.id
            LEFT JOIN game_item_sets gis ON gi.set_id = gis.id
            WHERE ce.character_id = ? AND gi.set_id IS NOT NULL
        ");
        $stmt->execute([$character_id]);
        $equipped_items = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // 按套装组织数据
        $sets = [];
        foreach ($equipped_items as $item) {
            $set_id = $item['set_id'];
            if (!isset($sets[$set_id])) {
                $sets[$set_id] = [
                    'set_id' => $set_id,
                    'set_name' => $item['set_name'],
                    'rarity' => $item['rarity'],
                    'equipped_pieces' => [],
                    'pieces_count' => 0
                ];
            }

            $sets[$set_id]['equipped_pieces'][] = [
                'slot_type' => $item['slot_type'],
                'item_id' => $item['item_id'],
                'item_name' => $item['item_name']
            ];
            $sets[$set_id]['pieces_count']++;
        }

        // 获取每个套装的完整信息和激活效果
        $result = [];
        foreach ($sets as $set_id => $set_data) {
            // 获取套装详细信息
            $stmt = $pdo->prepare("SELECT * FROM game_item_sets WHERE id = ?");
            $stmt->execute([$set_id]);
            $set_info = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($set_info) {
                $effects = json_decode($set_info['effects'], true);
                $pieces_count = $set_data['pieces_count'];

                // 确定激活的效果
                $active_effects = [];
                $total_attributes = [];

                if ($pieces_count >= 2 && isset($effects['two_piece'])) {
                    $active_effects['two_piece'] = $effects['two_piece'];
                    $total_attributes = mergeAttributes($total_attributes, $effects['two_piece']);
                }

                if ($pieces_count >= 4 && isset($effects['four_piece'])) {
                    $active_effects['four_piece'] = $effects['four_piece'];
                    $total_attributes = mergeAttributes($total_attributes, $effects['four_piece']);
                }

                if ($pieces_count >= 6 && isset($effects['six_piece'])) {
                    $active_effects['six_piece'] = $effects['six_piece'];
                    $total_attributes = mergeAttributes($total_attributes, $effects['six_piece']);
                }

                $result[] = [
                    'set_id' => $set_id,
                    'set_name' => $set_info['set_name'],
                    'description' => $set_info['description'],
                    'rarity' => $set_info['rarity'],
                    'realm_requirement' => $set_info['realm_requirement'],
                    'pieces_count' => $pieces_count,
                    'max_pieces' => $set_info['max_pieces'],
                    'equipped_pieces' => $set_data['equipped_pieces'],
                    'active_effects' => $active_effects,
                    'total_attributes' => $total_attributes,
                    'all_effects' => $effects
                ];
            }
        }

        return [
            'success' => true,
            'sets' => $result,
            'total_power_bonus' => calculateTotalSetPowerBonus($result)
        ];
    } catch (Exception $e) {
        return ['success' => false, 'message' => '获取套装状态失败: ' . $e->getMessage()];
    }
}

/**
 * 获取指定套装的详细信息
 */
function getSetDetails($pdo, $set_id)
{
    try {
        // 获取套装基本信息
        $stmt = $pdo->prepare("SELECT * FROM game_item_sets WHERE id = ?");
        $stmt->execute([$set_id]);
        $set_info = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$set_info) {
            return ['success' => false, 'message' => '套装不存在'];
        }

        // 获取套装包含的装备
        $stmt = $pdo->prepare("
            SELECT id, item_name, slot_type, rarity, realm_requirement, icon_image
            FROM game_items 
            WHERE set_id = ? AND is_active = 1
            ORDER BY slot_type
        ");
        $stmt->execute([$set_id]);
        $items = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // 解析效果
        $effects = json_decode($set_info['effects'], true);

        return [
            'success' => true,
            'set_info' => $set_info,
            'items' => $items,
            'effects' => $effects,
            'items_count' => count($items)
        ];
    } catch (Exception $e) {
        return ['success' => false, 'message' => '获取套装详情失败: ' . $e->getMessage()];
    }
}

/**
 * 获取所有激活的套装列表
 */
function getAllSets($pdo)
{
    try {
        $stmt = $pdo->query("
            SELECT gis.*, COUNT(gi.id) as items_count
            FROM game_item_sets gis
            LEFT JOIN game_items gi ON gis.id = gi.set_id
            WHERE gis.status = 'active'
            GROUP BY gis.id
            ORDER BY gis.realm_requirement ASC, gis.rarity DESC
        ");
        $sets = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // 解析每个套装的效果
        foreach ($sets as &$set) {
            $set['effects'] = json_decode($set['effects'], true);
        }

        return [
            'success' => true,
            'sets' => $sets,
            'total_count' => count($sets)
        ];
    } catch (Exception $e) {
        return ['success' => false, 'message' => '获取套装列表失败: ' . $e->getMessage()];
    }
}

/**
 * 计算套装对战力的贡献
 */
function calculateSetPower($pdo, $character_id)
{
    try {
        $character_sets = getCharacterSets($pdo, $character_id);

        if (!$character_sets['success']) {
            return $character_sets;
        }

        $total_power = 0;
        $power_breakdown = [];

        foreach ($character_sets['sets'] as $set) {
            $set_power = calculateSingleSetPower($set['total_attributes']);
            $total_power += $set_power;

            $power_breakdown[] = [
                'set_name' => $set['set_name'],
                'pieces_count' => $set['pieces_count'],
                'power_contribution' => $set_power,
                'attributes' => $set['total_attributes']
            ];
        }

        return [
            'success' => true,
            'total_set_power' => $total_power,
            'breakdown' => $power_breakdown
        ];
    } catch (Exception $e) {
        return ['success' => false, 'message' => '计算套装战力失败: ' . $e->getMessage()];
    }
}

/**
 * 模拟套装效果
 */
function simulateSetEffect($pdo, $set_id, $pieces_count)
{
    try {
        $stmt = $pdo->prepare("SELECT * FROM game_item_sets WHERE id = ?");
        $stmt->execute([$set_id]);
        $set_info = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$set_info) {
            return ['success' => false, 'message' => '套装不存在'];
        }

        $effects = json_decode($set_info['effects'], true);
        $simulated_attributes = [];
        $active_effects = [];

        // 根据件数模拟效果
        if ($pieces_count >= 2 && isset($effects['two_piece'])) {
            $active_effects['two_piece'] = $effects['two_piece'];
            $simulated_attributes = mergeAttributes($simulated_attributes, $effects['two_piece']);
        }

        if ($pieces_count >= 4 && isset($effects['four_piece'])) {
            $active_effects['four_piece'] = $effects['four_piece'];
            $simulated_attributes = mergeAttributes($simulated_attributes, $effects['four_piece']);
        }

        if ($pieces_count >= 6 && isset($effects['six_piece'])) {
            $active_effects['six_piece'] = $effects['six_piece'];
            $simulated_attributes = mergeAttributes($simulated_attributes, $effects['six_piece']);
        }

        $power_contribution = calculateSingleSetPower($simulated_attributes);

        return [
            'success' => true,
            'set_name' => $set_info['set_name'],
            'pieces_count' => $pieces_count,
            'active_effects' => $active_effects,
            'total_attributes' => $simulated_attributes,
            'power_contribution' => $power_contribution
        ];
    } catch (Exception $e) {
        return ['success' => false, 'message' => '模拟套装效果失败: ' . $e->getMessage()];
    }
}

/**
 * 合并属性
 */
function mergeAttributes($base_attrs, $new_attrs)
{
    foreach ($new_attrs as $key => $value) {
        if ($key === 'special_effect') {
            // 特殊效果单独处理
            continue;
        }

        if (isset($base_attrs[$key])) {
            $base_attrs[$key] += $value;
        } else {
            $base_attrs[$key] = $value;
        }
    }

    return $base_attrs;
}

/**
 * 计算单个套装的战力贡献
 */
function calculateSingleSetPower($attributes)
{
    $power = 0;

    // 战力计算权重
    $weights = [
        'physical_attack' => 3,
        'immortal_attack' => 3,
        'physical_defense' => 2,
        'immortal_defense' => 2,
        'max_hp' => 0.5,
        'max_mp' => 0.3,
        'speed' => 10,
        'crit_rate' => 50,        // 暴击率按百分比计算
        'crit_damage' => 40,      // 暴击伤害按百分比计算
        'hit_rate' => 30,         // 命中率按百分比计算
        'dodge_rate' => 30,       // 闪避率按百分比计算
        'block_rate' => 25        // 格挡率按百分比计算
    ];

    foreach ($attributes as $attr => $value) {
        if (isset($weights[$attr])) {
            $power += $value * $weights[$attr];
        }
    }

    return round($power);
}

/**
 * 计算总套装战力加成
 */
function calculateTotalSetPowerBonus($sets)
{
    $total_power = 0;

    foreach ($sets as $set) {
        $set_power = calculateSingleSetPower($set['total_attributes']);
        $total_power += $set_power;

        // 🔧 调试：记录每个套装的战力贡献
        error_log("🔍 套装战力调试 - {$set['set_name']}: 属性=" . json_encode($set['total_attributes']) . ", 战力={$set_power}");
    }

    error_log("🔍 总套装战力: {$total_power}");
    return $total_power;
}

/**
 * 计算真实的套装战力差值
 * 简化版本，直接返回套装属性的战力计算
 */
function calculateRealSetPowerDifference($pdo, $character_id)
{
    try {
        // 获取套装属性加成
        $setBonus = getCharacterSetBonus($pdo, $character_id);

        // 使用与角色总战力相同的权重计算套装贡献
        $attackPower = max(
            $setBonus['physical_attack'] ?? 0,
            $setBonus['immortal_attack'] ?? 0
        );

        $defensePower = (
            ($setBonus['physical_defense'] ?? 0) +
            ($setBonus['immortal_defense'] ?? 0)
        ) / 2;

        $hpPower = ($setBonus['max_hp'] ?? 0) * 0.1;
        $speedPower = $setBonus['speed'] ?? 0;

        // 计算套装对战力的实际贡献
        $realDifference = $attackPower + $defensePower + $hpPower + $speedPower;

        return [
            'success' => true,
            'power_difference' => round($realDifference),
            'current_power' => 0, // 暂不计算
            'power_without_sets' => 0, // 暂不计算
            'debug_info' => [
                'character_id' => $character_id,
                'set_bonus_attributes' => $setBonus,
                'calculation' => [
                    'attack_power' => $attackPower,
                    'defense_power' => $defensePower,
                    'hp_power' => $hpPower,
                    'speed_power' => $speedPower
                ]
            ]
        ];
    } catch (Exception $e) {
        return ['success' => false, 'message' => '计算套装战力失败: ' . $e->getMessage()];
    }
}
