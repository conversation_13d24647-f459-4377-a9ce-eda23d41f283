/**
 * 木系技能模块 - 藤蔓缠绕
 * 适用于 item_skills 表中的 animation_model = 'tengman'
 */

// 木系技能 - 藤蔓缠绕
class TengManSkill extends BaseSkill {
    async execute(skillData, weaponImage) {
        // 使用数据库中的真实技能名称，而不是硬编码
        const skillName = skillData?.skillName || skillData?.displayName || '藤蔓缠绕'; // 提供默认值作为后备
        await this.showSkillShout(skillName);
        
        // 执行藤蔓缠绕动画，传递武器图片
        await this.createTengManAnimation(weaponImage);
    }
    
    async createTengManAnimation(weaponImage) {
        // 🔧 修复：根据技能使用者动态获取位置
        // 如果是敌方技能，则敌人是施法者，玩家是目标；反之亦然
        const casterElement = this.isEnemySkill ? document.querySelector('.enemy') : document.querySelector('.player');
        const targetElement = this.isEnemySkill ? document.querySelector('.player') : document.querySelector('.enemy');
        const containerRect = this.battleSystem.battleContainer.getBoundingClientRect();
        
        // 创建动画容器
        const container = document.createElement('div');
        container.className = 'tengman-container';
        this.effectsContainer.appendChild(container);
        
        // 计算起点和终点位置
        const casterRect = casterElement.getBoundingClientRect();
        const targetRect = targetElement.getBoundingClientRect();
        const startX = casterRect.left - containerRect.left + casterRect.width / 2;
        const startY = casterRect.top - containerRect.top + casterRect.height / 2;
        const endX = targetRect.left - containerRect.left + targetRect.width / 2;
        const endY = targetRect.top - containerRect.top + targetRect.height / 2;
        
        try {
            // === 第一阶段：蓄力 - 自然生机汇聚 ===
            await this.createChargePhase(container, startX, startY, weaponImage);
            
            // === 第二阶段：发射 - 藤蔓生长延伸 ===
            await this.createGrowthPhase(container, startX, startY, endX, endY);
            
            // === 第三阶段：击中 - 一圈小尖锥向内穿透 ===
            await this.createBindPhase(container, endX, endY);
            
        } finally {
            // 清理容器
            setTimeout(() => {
                if (container && container.parentNode) {
                    container.parentNode.removeChild(container);
                }
            }, 100);
        }
    }
    
    // 蓄力阶段：自然生机汇聚
    async createChargePhase(container, startX, startY, weaponImage) {
        // 创建地面魔法阵
        const magicCircle = document.createElement('div');
        magicCircle.className = 'tengman-magic-circle';
        magicCircle.style.left = `${startX}px`;
        magicCircle.style.top = `${startY}px`;
        container.appendChild(magicCircle);
        
        // 创建内圈符文
        const innerRunes = document.createElement('div');
        innerRunes.className = 'tengman-inner-runes';
        innerRunes.style.left = `${startX}px`;
        innerRunes.style.top = `${startY}px`;
        container.appendChild(innerRunes);
        
        // 创建外圈符文
        const outerRunes = document.createElement('div');
        outerRunes.className = 'tengman-outer-runes';
        outerRunes.style.left = `${startX}px`;
        outerRunes.style.top = `${startY}px`;
        container.appendChild(outerRunes);
        
        // 创建武器图片在中心旋转
        if (weaponImage) {
            const weaponSprite = document.createElement('div');
            weaponSprite.className = 'tengman-weapon-sprite';
            weaponSprite.style.left = `${startX}px`;
            weaponSprite.style.top = `${startY}px`;
            
            // 添加武器图片背景
            this.addWeaponImage(weaponSprite, weaponImage);
            // 🗡️ 动态调整武器图片角度
            const weaponImg = weaponSprite.querySelector('.weapon-image');
            if (weaponImg) {
                weaponImg.style.transform = `rotate(${this.calculateInitialSwordRotation()}deg)`;
            }
            
            container.appendChild(weaponSprite);
        }
        
        // 创建蓄力能量核心
        const energyCore = document.createElement('div');
        energyCore.className = 'tengman-energy-core';
        energyCore.style.left = `${startX}px`;
        energyCore.style.top = `${startY}px`;
        container.appendChild(energyCore);
        
        // 创建绿色能量粒子汇聚
        for (let i = 0; i < 25; i++) {
            const particle = document.createElement('div');
            particle.className = 'tengman-charge-particle';
            particle.style.left = `${startX}px`;
            particle.style.top = `${startY}px`;
            
            const angle = Math.random() * Math.PI * 2;
            const radius = 8 + Math.random() * 22;
            const moveX = Math.cos(angle) * radius;
            const moveY = Math.sin(angle) * radius;
            
            particle.style.setProperty('--chargeX', `${moveX}px`);
            particle.style.setProperty('--chargeY', `${moveY}px`);
            particle.style.animationDelay = `${Math.random() * 1.0}s`;
            
            container.appendChild(particle);
        }
        
        // 创建环绕的小草萌发
        for (let i = 0; i < 12; i++) {
            const angle = (i / 12) * Math.PI * 2;
            const radius = 50;
            const grassX = startX + Math.cos(angle) * radius;
            const grassY = startY + Math.sin(angle) * radius;
            
            const grass = document.createElement('div');
            grass.className = 'tengman-charge-grass';
            grass.style.left = `${grassX}px`;
            grass.style.top = `${grassY}px`;
            grass.style.animationDelay = `${i * 0.08}s`;
            container.appendChild(grass);
        }
        
        // 创建能量波纹
        for (let i = 0; i < 4; i++) {
            const ripple = document.createElement('div');
            ripple.className = 'tengman-energy-ripple';
            ripple.style.left = `${startX}px`;
            ripple.style.top = `${startY}px`;
            ripple.style.animationDelay = `${i * 0.2}s`;
            container.appendChild(ripple);
        }
        
        // 等待蓄力完成
        await this.wait(600);
        
        // 移除蓄力效果
        magicCircle.remove();
        innerRunes.remove();
        outerRunes.remove();
        energyCore.remove();
        // 武器图片会随着魔法阵一起消失，不需要单独移除
        document.querySelectorAll('.tengman-charge-particle').forEach(p => p.remove());
        document.querySelectorAll('.tengman-charge-grass').forEach(g => g.remove());
        document.querySelectorAll('.tengman-energy-ripple').forEach(r => r.remove());
        document.querySelectorAll('.tengman-weapon-sprite').forEach(w => w.remove());
    }
    
    // 发射阶段：藤蔓生长延伸
    async createGrowthPhase(container, startX, startY, endX, endY) {
        // 创建预发射地面震动
        const groundShake = document.createElement('div');
        groundShake.className = 'tengman-ground-shake';
        groundShake.style.left = `${startX}px`;
        groundShake.style.top = `${startY}px`;
        container.appendChild(groundShake);
        
        await this.wait(150);
        
        // 计算主要方向角度（从施法者到敌人）
        const mainAngle = Math.atan2(endY - startY, endX - startX);
        const distance = Math.sqrt(Math.pow(endX - startX, 2) + Math.pow(endY - startY, 2));
        
        // 创建6股藤蔓线（原来3股 + 新增3股左右相反）
        const vineLines = [
            // === 原来的3股藤蔓 ===
            { offset: -25 + (Math.random() - 0.5) * 20, delay: 0.0 + Math.random() * 0.2 },    // 左侧股，大幅随机偏移
            { offset: (Math.random() - 0.5) * 15, delay: 0.1 + Math.random() * 0.2 },         // 中间股，中等偏移
            { offset: 25 + (Math.random() - 0.5) * 20, delay: 0.2 + Math.random() * 0.2 },    // 右侧股，大幅随机偏移
            
            // === 新增的3股藤蔓（左右相反） ===
            { offset: 25 + (Math.random() - 0.5) * 20, delay: 0.3 + Math.random() * 0.2 },    // 右侧股变左侧
            { offset: (Math.random() - 0.5) * 15, delay: 0.4 + Math.random() * 0.2 },         // 中间股2
            { offset: -25 + (Math.random() - 0.5) * 20, delay: 0.5 + Math.random() * 0.2 }    // 左侧股变右侧
        ];
        
        // 存储所有藤蔓段，用于管理显示数量
        const allVineSegments = [];
        const maxVisibleSegments = 18; // 从12个增加到18个（6根藤蔓×3段显示）
        const activeIntervals = []; // 存储所有活动的定时器
        const segments = 24; // 移到外部，每股藤蔓的段数
        
        vineLines.forEach((line, lineIndex) => {
            // 计算这股藤蔓的起始位置（垂直于主方向偏移）
            const perpAngle = mainAngle + Math.PI / 2;
            const offsetX = Math.cos(perpAngle) * line.offset;
            const offsetY = Math.sin(perpAngle) * line.offset;
            const lineStartX = startX + offsetX;
            const lineStartY = startY + offsetY;
            
            // 计算这股藤蔓的终点位置（延长到敌人之后）
            let lineEndX, lineEndY;
            if (lineIndex === 1) {
                // 中间股直接瞄准敌人，但延长到画面外
                const extendDistance = distance * 1.8; // 延长80%
                lineEndX = startX + Math.cos(mainAngle) * extendDistance + (Math.random() - 0.5) * 20;
                lineEndY = startY + Math.sin(mainAngle) * extendDistance + (Math.random() - 0.5) * 20;
            } else {
                // 两侧股偏移更大，也延长到画面外
                const extendDistance = distance * 1.8;
                const baseEndX = startX + Math.cos(mainAngle) * extendDistance;
                const baseEndY = startY + Math.sin(mainAngle) * extendDistance;
                const endOffsetX = Math.cos(perpAngle) * line.offset * (0.5 + Math.random() * 0.4);
                const endOffsetY = Math.sin(perpAngle) * line.offset * (0.5 + Math.random() * 0.4);
                lineEndX = baseEndX + endOffsetX + (Math.random() - 0.5) * 30;
                lineEndY = baseEndY + endOffsetY + (Math.random() - 0.5) * 30;
            }
            
            // 创建路径 - 生成控制点
            const pathPoints = [];
            
            // 根据藤蔓类型选择不同的路径生成方式
            // === 所有藤蔓都使用S形路径 ===
            const segmentCount = 8; // S形路径的控制点数量
            
            // 添加起点
            pathPoints.push({ x: lineStartX, y: lineStartY });
            
            // 生成S形路径的控制点
            for (let i = 1; i < segmentCount; i++) {
                const progress = i / segmentCount;
                
                // 基础直线路径
                const baseX = lineStartX + (lineEndX - lineStartX) * progress;
                const baseY = lineStartY + (lineEndY - lineStartY) * progress;
                
                // S形偏移计算（不同藤蔓使用不同的相位）
                let sWave;
                if (lineIndex === 1 || lineIndex === 4) {
                    // 主藤蔓：标准S形和相位相反S形
                    if (lineIndex === 1) {
                        sWave = Math.sin(progress * Math.PI * 2) * 40; // 原来的S波
                    } else {
                        sWave = Math.sin(progress * Math.PI * 2 + Math.PI) * 40; // 相位相反的S波
                    }
                } else {
                    // 副藤蔓：使用较小幅度的S形，不同相位
                    const phaseOffset = lineIndex * Math.PI / 3; // 每根藤蔓不同相位
                    sWave = Math.sin(progress * Math.PI * 2 + phaseOffset) * 25; // 较小幅度
                }
                
                // 垂直于主方向的偏移
                const sWaveOffsetX = Math.cos(perpAngle) * sWave;
                const sWaveOffsetY = Math.sin(perpAngle) * sWave;
                
                pathPoints.push({
                    x: baseX + sWaveOffsetX,
                    y: baseY + sWaveOffsetY
                });
            }
            
            // 添加终点（延长S形路径，不强制指向敌人中心）
            const finalProgress = 1.0;
            const finalBaseX = lineStartX + (lineEndX - lineStartX) * finalProgress;
            const finalBaseY = lineStartY + (lineEndY - lineStartY) * finalProgress;
            
            // 计算最终S形偏移
            let finalSWave;
            if (lineIndex === 1 || lineIndex === 4) {
                if (lineIndex === 1) {
                    finalSWave = Math.sin(finalProgress * Math.PI * 2) * 40;
                } else {
                    finalSWave = Math.sin(finalProgress * Math.PI * 2 + Math.PI) * 40;
                }
            } else {
                const phaseOffset = lineIndex * Math.PI / 3;
                finalSWave = Math.sin(finalProgress * Math.PI * 2 + phaseOffset) * 25;
            }
            
            const finalOffsetX = Math.cos(perpAngle) * finalSWave;
            const finalOffsetY = Math.sin(perpAngle) * finalSWave;
            
            pathPoints.push({ 
                x: finalBaseX + finalOffsetX, 
                y: finalBaseY + finalOffsetY 
            });
            
            // 分批创建藤蔓段，实现蛇身效果
            let currentSegmentIndex = 0;
            const vineInterval = setInterval(() => {
                // 移除最旧的藤蔓段（如果超过最大显示数量）
                if (allVineSegments.length >= maxVisibleSegments) {
                    const oldSegment = allVineSegments.shift();
                    if (oldSegment && oldSegment.parentNode) {
                        // 如果已经在渐隐中，直接移除；否则添加淡出效果
                        if (oldSegment.classList.contains('tengman-vine-penetrating')) {
                            oldSegment.parentNode.removeChild(oldSegment);
                        } else {
                            oldSegment.classList.add('tengman-vine-fade-out');
                            setTimeout(() => {
                                if (oldSegment.parentNode) {
                                    oldSegment.parentNode.removeChild(oldSegment);
                                }
                            }, 200);
                        }
                    }
                }
                
                // 创建新的藤蔓段
                if (currentSegmentIndex < segments) {
                    const progress = currentSegmentIndex / (segments - 1);
                    
                    // 路径计算（S形或闪电式）
                    const scaledProgress = progress * (pathPoints.length - 1);
                    const pointIndex = Math.floor(scaledProgress);
                    const localProgress = scaledProgress - pointIndex;
                    
                    const currentPoint = pathPoints[Math.min(pointIndex, pathPoints.length - 1)];
                    const nextPoint = pathPoints[Math.min(pointIndex + 1, pathPoints.length - 1)];
                    
                    // 在两个控制点之间插值
                    let segmentX = currentPoint.x + (nextPoint.x - currentPoint.x) * localProgress;
                    let segmentY = currentPoint.y + (nextPoint.y - currentPoint.y) * localProgress;
                    
                    // 添加小幅随机抖动（所有藤蔓都使用较小抖动）
                    const jitterStrength = 4; // 统一使用较小的抖动
                    segmentX += (Math.random() - 0.5) * jitterStrength;
                    segmentY += (Math.random() - 0.5) * jitterStrength;
                    
                    const vineSegment = document.createElement('div');
                    vineSegment.className = 'tengman-vine-segment';
                    vineSegment.style.left = `${segmentX}px`;
                    vineSegment.style.top = `${segmentY}px`;
                    
                    // === 藤蔓大小随机性 ===
                    let baseWidth, baseHeight;
                    if (lineIndex === 1 || lineIndex === 4) {
                        // 主藤蔓：较大尺寸
                        baseWidth = 12;
                        baseHeight = 26;
                    } else {
                        // 副藤蔓：中等尺寸
                        baseWidth = 10;
                        baseHeight = 22;
                    }
                    
                    // 随机缩放因子（原来是±20%，现在是±40%）
                    const widthScale = 0.6 + Math.random() * 0.8;  // 0.6到1.4倍
                    const heightScale = 0.6 + Math.random() * 0.8; // 0.6到1.4倍
                    
                    const finalWidth = Math.round(baseWidth * widthScale);
                    const finalHeight = Math.round(baseHeight * heightScale);
                    
                    vineSegment.style.width = `${finalWidth}px`;
                    vineSegment.style.height = `${finalHeight}px`;
                    
                    // 计算藤蔓方向角度
                    let angle;
                    if (currentSegmentIndex < segments - 1) {
                        // 计算下一个点的位置，用于确定藤蔓方向
                        const nextProgress = (currentSegmentIndex + 1) / (segments - 1);
                        const nextScaledProgress = nextProgress * (pathPoints.length - 1);
                        const nextPointIndex = Math.floor(nextScaledProgress);
                        const nextLocalProgress = nextScaledProgress - nextPointIndex;
                        
                        const nextCurrentPoint = pathPoints[Math.min(nextPointIndex, pathPoints.length - 1)];
                        const nextNextPoint = pathPoints[Math.min(nextPointIndex + 1, pathPoints.length - 1)];
                        
                        const nextSegmentX = nextCurrentPoint.x + (nextNextPoint.x - nextCurrentPoint.x) * nextLocalProgress;
                        const nextSegmentY = nextCurrentPoint.y + (nextNextPoint.y - nextCurrentPoint.y) * nextLocalProgress;
                        
                        angle = Math.atan2(nextSegmentY - segmentY, nextSegmentX - segmentX) * 180 / Math.PI;
                    } else {
                        // 最后一段：使用倒数第二段到最后一段的方向，而不是指向敌人
                        if (currentSegmentIndex > 0) {
                            const prevProgress = (currentSegmentIndex - 1) / (segments - 1);
                            const prevScaledProgress = prevProgress * (pathPoints.length - 1);
                            const prevPointIndex = Math.floor(prevScaledProgress);
                            const prevLocalProgress = prevScaledProgress - prevPointIndex;
                            
                            const prevCurrentPoint = pathPoints[Math.min(prevPointIndex, pathPoints.length - 1)];
                            const prevNextPoint = pathPoints[Math.min(prevPointIndex + 1, pathPoints.length - 1)];
                            
                            const prevSegmentX = prevCurrentPoint.x + (prevNextPoint.x - prevCurrentPoint.x) * prevLocalProgress;
                            const prevSegmentY = prevCurrentPoint.y + (prevNextPoint.y - prevCurrentPoint.y) * prevLocalProgress;
                            
                            angle = Math.atan2(segmentY - prevSegmentY, segmentX - prevSegmentX) * 180 / Math.PI;
                        } else {
                            // 如果是第一段，使用路径方向
                            angle = Math.atan2(pathPoints[1].y - pathPoints[0].y, pathPoints[1].x - pathPoints[0].x) * 180 / Math.PI;
                        }
                    }
                    
                    // 修正角度让尖端指向正确方向
                    vineSegment.style.setProperty('--vineAngle', `${angle + 90}deg`);
                    
                    // 为主藤蔓添加特殊标记
                    if (lineIndex === 1 || lineIndex === 4) {
                        vineSegment.classList.add('main-vine');
                    }
                    
                    // 添加穿透标记，用于击中后的行为
                    vineSegment.setAttribute('data-line-index', lineIndex);
                    vineSegment.setAttribute('data-segment-index', currentSegmentIndex);
                    
                    container.appendChild(vineSegment);
                    allVineSegments.push(vineSegment);
                    
                    // 如果处于穿透模式，新生成的藤蔓段立即开始渐隐
                    if (this.isPenetrating) {
                        setTimeout(() => {
                            if (vineSegment && vineSegment.parentNode) {
                                vineSegment.classList.add('tengman-vine-penetrating');
                            }
                        }, 10); // 从50ms缩短到10ms，几乎立即开始渐隐
                    }
                    
                    currentSegmentIndex++;
                } else {
                    // 所有段都创建完毕，清除定时器
                    clearInterval(vineInterval);
                }
            }, 100); // 从80ms增加到100ms，进一步降低移动速度
            
            // 存储定时器引用
            activeIntervals.push(vineInterval);
        });
        
        // 等待藤蔓生长到击中敌人的位置
        // 调整击中时间：24段藤蔓，每100ms一段，到达敌人位置（约70%路径）
        const reachEnemyTime = Math.ceil(segments * 0.7) * 100 + 400; // 1700ms + 400ms = 2100ms
        await this.wait(reachEnemyTime);
        
        // 击中敌人后，让藤蔓穿透并渐隐，不立即停止
        this.penetrateAndFadeVines(activeIntervals, allVineSegments, container, endX, endY);
        
        groundShake.remove();
    }
    
    // 停止所有定时器并清除藤蔓段
    stopAllIntervalsAndRemoveVines(intervals, vineSegments, container) {
        // 停止所有定时器
        intervals.forEach(interval => {
            if (interval) {
                clearInterval(interval);
            }
        });
        
        // 清除所有现有的藤蔓段
        const allVines = container.querySelectorAll('.tengman-vine-segment');
        allVines.forEach((segment, index) => {
            if (segment) {
                segment.classList.add('tengman-vine-instant-fade');
                setTimeout(() => {
                    if (segment && segment.parentNode) {
                        segment.parentNode.removeChild(segment);
                    }
                }, 100);
            }
        });
        
        // 清空数组
        vineSegments.length = 0;
    }
    
    // 击中阶段：光圈扩散 + 尖锥穿透同时进行
    async createBindPhase(container, endX, endY) {
        // === 同时触发：光圈扩散 + 击中特效 + 尖锥穿透 ===
        
        // 1. 绿色光圈扩散效果
        const impactRipple = document.createElement('div');
        impactRipple.className = 'tengman-impact-ripple';
        impactRipple.style.left = `${endX}px`;
        impactRipple.style.top = `${endY}px`;
        container.appendChild(impactRipple);
        
        // 2. 扩散粒子
        const impactParticles = [];
        for (let i = 0; i < 20; i++) {
            const particle = document.createElement('div');
            particle.className = 'tengman-impact-particle';
            particle.style.left = `${endX}px`;
            particle.style.top = `${endY}px`;
            
            const angle = (i / 20) * Math.PI * 2;
            const distance = 60 + Math.random() * 40;
            const moveX = Math.cos(angle) * distance;
            const moveY = Math.sin(angle) * distance;
            
            particle.style.setProperty('--impactX', `${moveX}px`);
            particle.style.setProperty('--impactY', `${moveY}px`);
            particle.style.animationDelay = `${Math.random() * 0.2}s`;
            
            container.appendChild(particle);
            impactParticles.push(particle);
        }
        
        // 3. 标准击中特效（立即显示）
        this.createHitEffect(endX, endY, true);
        
        // 4. 立即创建尖锥穿透（不等待）
        const spikeCount = 8;
        const radius = 80;
        const spikes = [];
        
        for (let i = 0; i < spikeCount; i++) {
            const angle = (i / spikeCount) * Math.PI * 2;
            const spikeX = endX + Math.cos(angle) * radius;
            const spikeY = endY + Math.sin(angle) * radius;
            
            const spike = document.createElement('div');
            spike.className = 'tengman-circle-spike';
            spike.style.left = `${spikeX}px`;
            spike.style.top = `${spikeY}px`;
            
            // 计算指向中心的角度
            const centerAngle = Math.atan2(endY - spikeY, endX - spikeX) * 180 / Math.PI;
            spike.style.setProperty('--spikeAngle', `${centerAngle + 90}deg`);
            spike.style.setProperty('--targetX', `${endX - spikeX}px`);
            spike.style.setProperty('--targetY', `${endY - spikeY}px`);
            spike.style.animationDelay = `${i * 0.03}s`;
            
            container.appendChild(spike);
            spikes.push(spike);
        }
        
        // 5. 立即创建中心爆炸效果（不延迟）
        const explosion = document.createElement('div');
        explosion.className = 'tengman-center-explosion';
        explosion.style.left = `${endX}px`;
        explosion.style.top = `${endY}px`;
        container.appendChild(explosion);
        
        // 6. 爆炸粒子
        const explosionParticles = [];
        for (let i = 0; i < 15; i++) {
            const particle = document.createElement('div');
            particle.className = 'tengman-explosion-particle';
            particle.style.left = `${endX}px`;
            particle.style.top = `${endY}px`;
            
            const angle = Math.random() * Math.PI * 2;
            const distance = 40 + Math.random() * 80;
            const moveX = Math.cos(angle) * distance;
            const moveY = Math.sin(angle) * distance;
            
            particle.style.setProperty('--explodeX', `${moveX}px`);
            particle.style.setProperty('--explodeY', `${moveY}px`);
            particle.style.animationDelay = `${Math.random() * 0.2}s`;
            
            container.appendChild(particle);
            explosionParticles.push(particle);
        }
        
        // 🔧 修复：给被攻击者添加穿透震动效果
        const targetSelector = !this.isEnemySkill ? '.enemy .character-sprite' : '.player .character-sprite';
        const targetSprite = document.querySelector(targetSelector);
        if (targetSprite) {
            // 清除之前的动画
            targetSprite.style.animation = '';
            // 强制重绘
            targetSprite.offsetHeight;
            // 应用新动画
            targetSprite.style.animation = 'tengman-pierce-struck 1.0s ease-out';
            console.log(`✅ 藤蔓缠绕受击动画已应用到${!this.isEnemySkill ? '敌人' : '玩家'}`);
        } else {
            console.warn('⚠️ 未找到目标精灵元素，无法应用受击动画');
        }
        
        // 等待穿透效果和击中动画完成
        await this.wait(800); // 从1500ms缩短到800ms，适应快速消失
        
        // 清理击中阶段的所有元素
        this.cleanupBindPhaseElements(impactRipple, impactParticles, spikes, explosion, explosionParticles);
        
        // 🔧 修复：恢复目标动画（因为不再有余韵阶段）
        if (targetSprite) {
            targetSprite.style.animation = '';
        }
    }
    
    // 清理击中阶段的所有元素
    cleanupBindPhaseElements(impactRipple, impactParticles, spikes, explosion, explosionParticles) {
        // 清理光圈
        if (impactRipple && impactRipple.parentNode) {
            impactRipple.parentNode.removeChild(impactRipple);
        }
        
        // 清理扩散粒子
        impactParticles.forEach(particle => {
            if (particle && particle.parentNode) {
                particle.parentNode.removeChild(particle);
            }
        });
        
        // 清理尖锥
        spikes.forEach(spike => {
            if (spike && spike.parentNode) {
                spike.parentNode.removeChild(spike);
            }
        });
        
        // 清理爆炸效果
        if (explosion && explosion.parentNode) {
            explosion.parentNode.removeChild(explosion);
        }
        
        // 清理爆炸粒子
        explosionParticles.forEach(particle => {
            if (particle && particle.parentNode) {
                particle.parentNode.removeChild(particle);
            }
        });
    }
    
    // 藤蔓穿透并渐隐：击中敌人后继续前进并逐渐消失
    penetrateAndFadeVines(intervals, vineSegments, container, enemyX, enemyY) {
        // 不停止生成新的藤蔓段，让它们继续沿路径移动
        // intervals保持运行，但标记为穿透模式
        
        // 为所有现有藤蔓段添加穿透渐隐效果
        const allVines = container.querySelectorAll('.tengman-vine-segment');
        allVines.forEach((segment, index) => {
            if (segment) {
                // 添加穿透渐隐类，带随机延迟
                setTimeout(() => {
                    if (segment && segment.parentNode) {
                        segment.classList.add('tengman-vine-penetrating');
                    }
                }, index * 10); // 从30ms缩短到10ms间隔，更快速地开始渐隐
            }
        });
        
        // 设置穿透模式标记，让新生成的藤蔓段也立即开始渐隐
        this.isPenetrating = true;
        
        // 延迟停止所有定时器，让藤蔓有时间完全穿透画面
        setTimeout(() => {
            intervals.forEach(interval => {
                if (interval) {
                    clearInterval(interval);
                }
            });
            this.isPenetrating = false;
        }, 400); // 从1000ms缩短到400ms，更快停止生成新段
        
        // 清空数组
        vineSegments.length = 0;
    }
    // 🗡️ 动态计算剑的初始旋转角度
    calculateInitialSwordRotation() {
        // 敌方技能：剑尖向下（指向玩家），我方技能：剑尖向上（指向敌人）
        return this.isEnemySkill ? 0 : 180;
    }
}

// 导出技能类
window.WoodSkills = { TengManSkill }; 