<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 登录功能测试</h1>
        <p>测试登录API是否正常工作</p>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" name="username" value="admin" required>
            </div>
            
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" name="password" value="123456" required>
            </div>
            
            <button type="submit">测试登录</button>
            <button type="button" onclick="testApiConnection()">测试API连接</button>
            <button type="button" onclick="checkSession()">检查会话状态</button>
        </form>
        
        <div id="result"></div>
    </div>

    <script>
        // 配置API路径
        const API_BASE = '../src/api/';
        
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            testLogin();
        });
        
        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            try {
                resultDiv.innerHTML = '<div class="result">正在测试登录...</div>';
                
                const response = await fetch(API_BASE + 'login.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `<div class="result success">✅ 登录成功！
响应数据: ${JSON.stringify(data, null, 2)}</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="result error">❌ 登录失败: ${data.message}
响应数据: ${JSON.stringify(data, null, 2)}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">❌ 请求错误: ${error.message}</div>`;
            }
        }
        
        async function testApiConnection() {
            const resultDiv = document.getElementById('result');
            
            try {
                resultDiv.innerHTML = '<div class="result">正在测试API连接...</div>';
                
                const response = await fetch(API_BASE + 'login.php', {
                    method: 'GET'
                });
                
                const text = await response.text();
                
                resultDiv.innerHTML = `<div class="result ${response.ok ? 'success' : 'error'}">
API连接测试结果:
状态码: ${response.status}
响应内容: ${text}</div>`;
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">❌ API连接失败: ${error.message}</div>`;
            }
        }
        
        async function checkSession() {
            const resultDiv = document.getElementById('result');
            
            try {
                resultDiv.innerHTML = '<div class="result">正在检查会话状态...</div>';
                
                const response = await fetch(API_BASE + 'auth_status.php', {
                    method: 'GET'
                });
                
                const data = await response.json();
                
                resultDiv.innerHTML = `<div class="result ${data.authenticated ? 'success' : 'error'}">
会话状态检查结果:
${JSON.stringify(data, null, 2)}</div>`;
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">❌ 会话检查失败: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
