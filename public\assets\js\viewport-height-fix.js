/**
 * 视口高度修复脚本
 * 解决PWA模式下的视口高度适配问题
 * 特别是底部白条问题
 */

(function() {
    'use strict';
    
    // 设置动态视口高度
    function setDynamicVH() {
        try {
            let vh = window.innerHeight * 0.01;
            document.documentElement.style.setProperty('--vh', `${vh}px`);
            
            // 调试信息
            if (window.matchMedia('(display-mode: standalone)').matches) {
                console.log(`PWA模式 - 视口高度: ${window.innerHeight}px, --vh: ${vh}px`);
                
                // 🔧 PWA模式下强制设置body高度
                document.body.style.height = window.innerHeight + 'px';
                document.documentElement.style.height = window.innerHeight + 'px';
                
                // 🔧 强制移除所有可能的底部间距
                document.body.style.marginBottom = '0';
                document.body.style.paddingBottom = '0';
            }
        } catch (error) {
            console.warn('设置动态视口高度失败:', error);
        }
    }
    
    // 检测PWA模式
    function isPWAMode() {
        return window.matchMedia('(display-mode: standalone)').matches ||
               window.navigator.standalone ||
               document.referrer.includes('android-app://');
    }
    
    // 🔧 强制消除底部白边
    function eliminateBottomGap() {
        if (isPWAMode()) {
            // 强制设置viewport meta标签
            const viewport = document.querySelector('meta[name="viewport"]');
            if (viewport) {
                viewport.setAttribute('content', 
                    'width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover, shrink-to-fit=no'
                );
            }
            
            // 强制设置html和body样式
            const style = document.createElement('style');
            style.textContent = `
                html, body {
                    height: ${window.innerHeight}px !important;
                    min-height: ${window.innerHeight}px !important;
                    margin: 0 !important;
                    padding: 0 !important;
                    overflow-x: hidden !important;
                }
                
                .main-container {
                    height: ${window.innerHeight}px !important;
                    min-height: ${window.innerHeight}px !important;
                    margin: 0 !important;
                }
                
                .bottom-navigation {
                    bottom: 0 !important;
                    margin-bottom: 0 !important;
                    padding-bottom: env(safe-area-inset-bottom, 0px) !important;
                }
            `;
            document.head.appendChild(style);
            
            console.log(`PWA模式 - 强制设置高度: ${window.innerHeight}px`);
        }
    }
    
    // 处理视口变化
    function handleViewportChange() {
        setDynamicVH();
        
        // PWA模式下额外处理
        if (isPWAMode()) {
            // 延迟执行，确保布局稳定
            setTimeout(() => {
                setDynamicVH();
                eliminateBottomGap();
                
                // 强制重绘以解决某些浏览器的渲染问题
                document.body.style.display = 'none';
                document.body.offsetHeight; // 触发重排
                document.body.style.display = '';
            }, 100);
            
            // 再次延迟确保完全生效
            setTimeout(() => {
                setDynamicVH();
                eliminateBottomGap();
            }, 300);
        }
    }
    
    // 防抖函数
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    // 创建防抖版本的处理函数
    const debouncedHandleViewportChange = debounce(handleViewportChange, 150);
    
    // 初始化
    function init() {
        // 立即设置一次
        setDynamicVH();
        
        // PWA模式下立即进行特殊处理
        if (isPWAMode()) {
            console.log('PWA模式已激活，启用特殊适配');
            eliminateBottomGap();
            
            // 监听应用状态变化
            document.addEventListener('visibilitychange', () => {
                if (!document.hidden) {
                    setTimeout(() => {
                        setDynamicVH();
                        eliminateBottomGap();
                    }, 100);
                }
            });
            
            // 监听屏幕方向变化（PWA模式下可能有延迟）
            if (screen.orientation) {
                screen.orientation.addEventListener('change', () => {
                    setTimeout(() => {
                        setDynamicVH();
                        eliminateBottomGap();
                    }, 200);
                });
            }
        }
        
        // 页面加载完成后再设置一次
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setDynamicVH();
                if (isPWAMode()) eliminateBottomGap();
            });
        } else {
            setDynamicVH();
            if (isPWAMode()) eliminateBottomGap();
        }
        
        // 监听各种可能导致视口变化的事件
        window.addEventListener('resize', debouncedHandleViewportChange);
        window.addEventListener('orientationchange', debouncedHandleViewportChange);
        
        // 🔧 针对移动端浏览器的特殊事件
        window.addEventListener('scroll', () => {
            if (isPWAMode()) {
                // 在PWA模式下阻止滚动导致的布局变化
                setTimeout(() => {
                    setDynamicVH();
                    eliminateBottomGap();
                }, 50);
            }
        });
        
        // 安全区域检测
        const safeAreaInsetBottom = getComputedStyle(document.documentElement)
            .getPropertyValue('--safe-area-inset-bottom') || 
            getComputedStyle(document.documentElement)
            .getPropertyValue('env(safe-area-inset-bottom)');
            
        if (safeAreaInsetBottom && safeAreaInsetBottom !== '0px') {
            console.log(`检测到安全区域底部间距: ${safeAreaInsetBottom}`);
        }
    }
    
    // 导出函数供其他脚本使用
    window.viewportHeightFix = {
        setVH: setDynamicVH,
        isPWA: isPWAMode,
        refresh: handleViewportChange,
        eliminate: eliminateBottomGap
    };
    
    // 自动初始化
    init();
    
})(); 