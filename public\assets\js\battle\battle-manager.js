/**
 * 🔧 战斗数据管理器
 * 负责加载和管理所有战斗相关的数据
 */

// 🏭 简化的调试输出函数 - 基于BattleDebugConfig的设置
function debugLog(...args) {
    if (window.BattleDebugConfig && !window.BattleDebugConfig.isProductionMode()) {
        console.log(...args);
    }
}

function debugWarn(...args) {
    if (window.BattleDebugConfig && !window.BattleDebugConfig.isProductionMode()) {
        console.warn(...args); // 警告仍然显示
    }
}

function debugError(...args) {
    // 错误信息始终显示，确保安全
    console.error(...args); // 错误始终显示
}

class BattleDataManager {
    constructor() {
        this.playerStats = null;
        this.enemyData = null;
        this.weaponSlots = [];
        this.currentArea = null;
        this.currentStage = 1;
        this.maxStage = 1;
        this.skillSequence = [];
        this.mapData = null;

        // 🔧 新增：初始化状态管理
        this.isInitialized = false;
        this.isInitializing = false;
        this.initPromise = null;

        // 🔧 新增：API请求缓存
        this.apiCache = new Map();
        this.pendingRequests = new Map();
    }

    // 初始化战斗数据
    async initializeBattleData() {
        // 🔧 修复：如果已经初始化过，直接返回成功
        if (this.isInitialized) {
            debugLog('战斗数据已初始化，跳过重复加载');
            return true;
        }

        // 🔧 修复：如果正在初始化，等待现有的初始化完成
        if (this.isInitializing && this.initPromise) {
            debugLog('战斗数据正在初始化中，等待完成...');
            return await this.initPromise;
        }

        // 🔧 修复：设置初始化状态，创建初始化Promise
        this.isInitializing = true;
        this.initPromise = this._performInitialization();

        try {
            const result = await this.initPromise;
            this.isInitialized = result;
            return result;
        } finally {
            this.isInitializing = false;
            this.initPromise = null;
        }
    }

    // 🔧 主要初始化方法 - 完全移除对battle_unified.php武器数据的依赖
    async _performInitialization() {
        const startTime = Date.now();

        try {
            // 🔧 统一的数据加载策略：所有装备数据由equipment_integrated.php提供
            // 战斗初始化不再依赖battle_unified.php的武器数据
            debugLog('🚀 开始统一数据初始化...');

            // 🏆 新增：检查是否为竞技场模式，如果是则跳过历练相关初始化
            const urlParams = new URLSearchParams(window.location.search);
            const isArenaMode = urlParams.get('arena') === '1';

            if (isArenaMode) {
                debugLog('🏆 竞技场模式：跳过区域信息和关卡进度初始化');
                // 竞技场模式下直接设置基本信息
                this.currentArea = {
                    areaId: 'arena',
                    areaName: '升仙大会',
                    progress: null,
                    total: null,
                    mapId: 'arena',
                };
                this.currentStage = null; // 竞技场不需要关卡概念
            } else {
                // 🔧 修复：步骤0 - 首先初始化区域信息，确保其他方法可以使用
                debugLog('📍 步骤0: 初始化区域信息...');
                this.currentArea = this.getCurrentArea(); // 获取区域信息
                debugLog('✅ 区域信息初始化完成:', this.currentArea);

                // 🔧 修复：步骤0.5 - 获取当前关卡，确保进度正确
                debugLog('📍 步骤0.5: 获取当前关卡进度...');
                this.currentStage = await this.getCurrentStage();
                debugLog('✅ 当前关卡获取完成:', this.currentStage);
            }

            // 1. 并行加载核心数据（不包括武器数据）
            debugLog('📍 步骤1: 并行加载核心数据...');
            const [playerResult, enemyResult] = await Promise.all([
                this.loadPlayerData().catch(err => {
                    debugError('❌ 玩家数据加载失败:', err);
                    return null;
                }),
                this.loadEnemyData().catch(err => {
                    debugError('❌ 敌人数据加载失败:', err);
                    return null;
                }),
            ]);

            debugLog('✅ 核心数据加载完成:', {
                player: playerResult ? '成功' : '失败',
                enemy: enemyResult ? '成功' : '失败',
            });

            // 2. 专门加载武器数据（统一由equipment_integrated.php提供）
            debugLog('🔧 开始加载武器数据（统一API）...');
            const weaponResult = await this.loadWeaponData().catch(err => {
                debugError('❌ 武器数据加载失败:', err);
                return null;
            });

            debugLog('✅ 武器数据加载完成:', weaponResult ? '成功' : '失败');

            // 3. 设置角色头像
            if (this.playerStats && this.playerStats.avatar_image) {
                this.setPlayerAvatar(this.playerStats.avatar_image);
            }

            // 4. 计算最终属性（包含武器加成）
            this.calculateFinalStats();

            // 5. 设置战斗背景
            this.setBattleBackground();

            // 6. 加载奇遇状态（仅非竞技场模式）
            if (!isArenaMode) {
                await this.loadAdventureStatus();
            } else {
                debugLog('🏆 竞技场模式：跳过奇遇状态加载');
            }

            const endTime = Date.now();
            debugLog(`✅ 统一数据初始化完成，耗时: ${endTime - startTime}ms`);

            return true;
        } catch (error) {
            debugError('❌ 统一数据初始化失败:', error);
            return false;
        }
    }

    // 获取当前区域信息
    getCurrentArea() {
        // 🚨 修复：确保方法能同步返回当前区域信息
        // 从localStorage获取当前区域信息
        const areaData = localStorage.getItem('currentArea');
        debugLog('=== 获取当前区域信息 ===');
        debugLog('localStorage中的areaData:', areaData);

        if (areaData) {
            try {
                const parsedArea = JSON.parse(areaData);
                debugLog('解析后的区域信息:', parsedArea);

                // 🚨 修复：确保返回的区域信息包含必要字段
                if (!parsedArea.areaId && parsedArea.map_code) {
                    parsedArea.areaId = parsedArea.map_code;
                }
                if (!parsedArea.areaName && parsedArea.map_name) {
                    parsedArea.areaName = parsedArea.map_name;
                }

                return parsedArea;
            } catch (error) {
                debugError('解析区域数据失败:', error);
            }
        }

        // 🔧 修复：从URL参数获取当前地图信息，而不是强制使用昆仑山脉
        const urlParams = new URLSearchParams(window.location.search);
        const mapCode = urlParams.get('map_code') || 'kunlun';
        const mapId = parseInt(urlParams.get('map_id')) || 1;

        // 🔧 修复：地图名称映射
        const mapNames = {
            kunlun: '昆仑山脉',
            taiyifeng: '太乙峰',
            map_qingyun: '青云山',
            donghai: '东海龙宫',
            jiuyou: '九幽冥界',
            santiansan: '三十三重天',
            huolong: '火龙洞窟',
            hanbing: '寒冰峡谷',
        };

        const defaultArea = {
            areaId: mapCode,
            areaName: mapNames[mapCode] || '未知地图',
            progress: null, // 🔧 修复：不设置默认进度，等待从后端获取
            total: 60, // 默认总关卡数
            map_code: mapCode,
            map_name: mapNames[mapCode] || '未知地图',
            mapId: mapId,
        };

        debugLog('🆕 创建临时区域信息（等待后端确认进度）:', defaultArea);

        // 🔧 修复：不立即保存到localStorage，等获取到正确进度后再保存
        // localStorage.setItem('currentArea', JSON.stringify(defaultArea));

        return defaultArea;
    }

    // 获取当前关卡数
    async getCurrentStage() {
        try {
            // 🔧 修复：确保获取到正确的地图代码，不依赖可能未初始化的currentArea
            let mapCode = 'kunlun'; // 默认值

            // 优先从URL参数获取地图代码
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('map_code')) {
                mapCode = urlParams.get('map_code');
            } else if (this.currentArea && this.currentArea.areaId) {
                mapCode = this.currentArea.areaId;
            }

            debugLog(`🔍 getCurrentStage使用地图代码: ${mapCode}`);

            // 🔧 修改：使用缓存API请求方法
            const apiUrl = window.GameConfig
                ? window.GameConfig.getApiUrl(
                      `battle_drops_unified.php?action=get_user_progress&map_code=${mapCode}`
                  )
                : `../../src/api/battle_drops_unified.php?action=get_user_progress&map_code=${mapCode}`;
            const result = await this.makeApiRequest(apiUrl, 'user_progress');

            if (result.success && result.progress) {
                // 🔧 修复：处理新玩家和游客模式，强制从第一关开始
                if (result.progress.is_new_player) {
                    this.currentStage = 1;
                    this.maxStage = 1;
                    debugLog(`🆕 新玩家模式: 强制从第1关开始`);
                } else if (result.progress.is_guest) {
                    this.currentStage = 1;
                    this.maxStage = 1;
                    debugLog(`👤 游客模式: 强制从第1关开始`);
                } else {
                    // 正常玩家使用API返回的进度
                    this.currentStage = parseInt(result.progress.current_stage) || 1;
                    this.maxStage = parseInt(result.progress.max_stage_reached) || 1;
                    debugLog(
                        `📍 当前关卡: ${this.currentStage}(${typeof this
                            .currentStage}), 最高通关: ${this.maxStage}(${typeof this.maxStage})`
                    );
                }

                // 🔧 修复：同步更新currentArea.progress以确保界面显示正确
                if (this.currentArea) {
                    this.currentArea.progress = this.currentStage;
                    debugLog(
                        `✅ 同步更新区域进度: ${this.currentArea.areaName} ${this.currentArea.progress}/${this.currentArea.total}关`
                    );

                    // 🔧 修复：保存正确的进度到localStorage
                    localStorage.setItem('currentArea', JSON.stringify(this.currentArea));
                    debugLog(`💾 区域进度已保存到localStorage`);
                }
            } else {
                debugError('获取进度失败:', result.message);
                // 🔧 修复：API失败时使用默认第一关
                this.currentStage = 1;
                this.maxStage = 1;
                debugLog(`⚠️ 使用默认关卡: 第1关`);

                // 🔧 修复：即使是默认值，也要同步更新区域进度
                if (this.currentArea) {
                    this.currentArea.progress = this.currentStage;
                    localStorage.setItem('currentArea', JSON.stringify(this.currentArea));
                    debugLog(`💾 默认区域进度已保存到localStorage`);
                }
            }
        } catch (error) {
            debugError('获取当前关卡失败:', error);
            // 🔧 修复：网络错误时使用默认第一关
            this.currentStage = 1;
            this.maxStage = 1;
            debugLog(`❌ 网络错误，使用默认关卡: 第1关`);

            // 🔧 修复：网络错误时也要同步更新区域进度
            if (this.currentArea) {
                this.currentArea.progress = this.currentStage;
                localStorage.setItem('currentArea', JSON.stringify(this.currentArea));
                debugLog(`💾 网络错误时的默认区域进度已保存到localStorage`);
            }
        }

        // 🔧 修复：确保返回当前关卡值
        return this.currentStage;
    }

    // 加载玩家数据
    async loadPlayerData() {
        try {
            // 🔧 修改：使用缓存API请求方法
            const apiUrl = window.GameConfig
                ? window.GameConfig.getApiUrl('cultivation.php?action=get_attributes')
                : '../../src/api/cultivation.php?action=get_attributes';
            const data = await this.makeApiRequest(apiUrl, 'player_attributes');

            debugLog('=== API原始返回数据 ===');
            debugLog(data);

            // 检查是否需要登录
            if (!data.success && data.message && data.message.includes('登录')) {
                debugLog('🎮 未登录用户，使用游客模式');
                this.playerStats = this.getGuestPlayerStats();
                this.setPlayerAvatar(this.playerStats.character_avatar);
                return;
            }

            if (data.success && data.attributes) {
                // 从API获取完整的角色属性（包含通用函数计算的加成）
                const attrs = data.attributes;

                debugLog('=== 属性详细信息 ===');
                debugLog(
                    'physical_attack原始值:',
                    attrs.physical_attack,
                    '(类型:',
                    typeof attrs.physical_attack,
                    ')'
                );
                debugLog(
                    'immortal_attack原始值:',
                    attrs.immortal_attack,
                    '(类型:',
                    typeof attrs.immortal_attack,
                    ')'
                );
                debugLog(
                    'physical_defense原始值:',
                    attrs.physical_defense,
                    '(类型:',
                    typeof attrs.physical_defense,
                    ')'
                );
                debugLog(
                    'immortal_defense原始值:',
                    attrs.immortal_defense,
                    '(类型:',
                    typeof attrs.immortal_defense,
                    ')'
                );

                // 正确处理角色头像
                let characterAvatar = attrs.avatar_image || 'ck';
                if (attrs.character_id && attrs.avatar_image) {
                    // 🔧 修复：使用图片路径管理器处理角色头像
                    if (window.ImagePathManager) {
                        characterAvatar = window.ImagePathManager.getCharacterImage(
                            attrs.avatar_image
                        );
                    } else {
                        debugWarn('图片路径管理器未加载，使用备用方案');
                        characterAvatar = '../../images/char/ck.png';
                    }
                } else {
                    // 游客模式或没有头像，使用默认
                    if (window.ImagePathManager) {
                        characterAvatar = window.ImagePathManager.getCharacterImage('ck');
                    } else {
                        characterAvatar = '../../images/char/ck.png';
                    }
                }

                debugLog('角色头像路径:', characterAvatar);

                // 构建完整的玩家属性对象
                this.playerStats = {
                    name:
                        attrs.character_name && attrs.character_name !== attrs.username
                            ? attrs.character_name
                            : attrs.username || '修仙者',
                    level: parseInt(attrs.realm_id) || 1, // 🔧 修复：使用realm_id作为level
                    hp_bonus: parseInt(attrs.hp) || 300,
                    mp_bonus: parseInt(attrs.mp) || 100, // 法力值
                    physical_attack: parseInt(attrs.physical_attack) || 20,
                    physical_defense: parseInt(attrs.physical_defense) || 10,
                    immortal_attack: parseInt(attrs.immortal_attack) || 15,
                    immortal_defense: parseInt(attrs.immortal_defense) || 8,
                    speed_total: parseInt(attrs.speed) || 50, // 总速度（基础+装备）
                    speed_base: parseInt(attrs.speed_base) || 0, // 角色基础速度
                    speed_bonus: parseInt(attrs.speed_bonus) || 0, // 装备速度加成
                    accuracy_bonus: parseFloat(attrs.accuracy_bonus) || 85, // 🔧 修复：使用数据库字段名
                    dodge_bonus: parseFloat(attrs.dodge_bonus) || 5, // 🔧 修复：使用数据库字段名
                    critical_bonus: parseFloat(attrs.critical_bonus) || 5, // 🔧 修复：统一使用critical_bonus
                    critical_damage: parseFloat(attrs.critical_damage) || 1.5, // 🔧 修复：统一使用critical_damage，小数格式存储
                    critical_resistance: parseFloat(attrs.critical_resistance) || 0, // 🔧 修复：使用数据库字段名

                    // 保存原始数据，以便后续处理
                    character_id: attrs.character_id,
                    realm_id: attrs.realm_id,
                    realm_name: attrs.realm_name,

                    // 添加属性计算来源信息（如果存在）
                    physical_attack_base: attrs.physical_attack_base || 0,
                    physical_attack_equipment: attrs.physical_attack_equipment || 0,

                    // 角色基础属性
                    physique: parseInt(attrs.physique) || 10,
                    comprehension: parseInt(attrs.comprehension) || 10,
                    constitution: parseInt(attrs.constitution) || 10,
                    spirit: parseInt(attrs.spirit) || 10,
                    agility: parseInt(attrs.agility) || 10,

                    // 添加完整的属性来源信息（如果存在）
                    attributeSources: attrs.sources,

                    // 灵根属性（如果存在） - 🔥 修复：使用正确的后端字段名
                    metal_affinity: attrs.gold_root || attrs.metal_root || 0,
                    wood_affinity: attrs.wood_root || 0,
                    water_affinity: attrs.water_root || 0,
                    fire_affinity: attrs.fire_root || 0,
                    earth_affinity: attrs.earth_root || 0,

                    // 头像信息
                    character_avatar: characterAvatar,
                    avatar_image: characterAvatar,

                    // 🔧 重要修复：添加套装特殊效果数据
                    set_special_effects: attrs.set_special_effects || [],
                };

                debugLog('=== 处理后的玩家属性 ===');
                debugLog(
                    'physical_attack:',
                    this.playerStats.physical_attack,
                    '(类型:',
                    typeof this.playerStats.physical_attack,
                    ')'
                );
                debugLog(
                    'immortal_attack:',
                    this.playerStats.immortal_attack,
                    '(类型:',
                    typeof this.playerStats.immortal_attack,
                    ')'
                );
                debugLog(
                    'physical_defense:',
                    this.playerStats.physical_defense,
                    '(类型:',
                    typeof this.playerStats.physical_defense,
                    ')'
                );
                debugLog(
                    'immortal_defense:',
                    this.playerStats.immortal_defense,
                    '(类型:',
                    typeof this.playerStats.immortal_defense,
                    ')'
                );
                debugLog('character_avatar:', this.playerStats.character_avatar);

                // 🔧 新增：套装特殊效果调试信息
                debugLog('=== 套装特殊效果调试 ===');
                debugLog('API返回的set_special_effects:', attrs.set_special_effects);
                debugLog(
                    'playerStats中的set_special_effects:',
                    this.playerStats.set_special_effects
                );
                if (
                    this.playerStats.set_special_effects &&
                    this.playerStats.set_special_effects.length > 0
                ) {
                    debugLog(
                        `✅ 找到 ${this.playerStats.set_special_effects.length} 个套装特殊效果:`
                    );
                    this.playerStats.set_special_effects.forEach((effect, index) => {
                        debugLog(
                            `  效果 ${index + 1}: ${effect.set_name} (${effect.pieces}件套) - ${
                                effect.effect
                            }`
                        );
                    });
                } else {
                    debugLog('❌ 未找到套装特殊效果数据');
                }

                debugLog('完整玩家属性:', this.playerStats);

                // 设置玩家头像
                this.setPlayerAvatar(this.playerStats.character_avatar);
            } else {
                throw new Error('获取玩家属性失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            debugError('加载玩家数据失败:', error);
            debugLog('🎮 使用游客模式默认属性');

            // 使用游客模式默认值
            this.playerStats = this.getGuestPlayerStats();
            this.setPlayerAvatar(this.playerStats.character_avatar);
        }
    }

    // 获取游客模式玩家属性
    getGuestPlayerStats() {
        return {
            name: '修仙者',
            level: 1,
            hp_bonus: 300,
            mp_bonus: 100, // 法力值
            physical_attack: 20,
            physical_defense: 10,
            immortal_attack: 15,
            immortal_defense: 8,
            speed_total: 50, // 总速度
            speed_base: 50, // 角色基础速度
            speed_bonus: 0, // 装备速度加成
            accuracy_bonus: 80, // 🔧 修复：使用数据库字段名
            dodge_bonus: 10, // 🔧 修复：使用数据库字段名
            critical_bonus: 10, // 🔧 修复：统一使用critical_bonus
            critical_damage: 1.5, // 🔧 修复：统一使用critical_damage，小数格式（150%）
            critical_resistance: 5, // 🔧 修复：使用数据库字段名

            // 角色基础属性
            physique: 10,
            comprehension: 10,
            constitution: 10,
            spirit: 10,
            agility: 10,

            // 灵根属性
            metal_affinity: 20,
            wood_affinity: 20,
            water_affinity: 20,
            fire_affinity: 20,
            earth_affinity: 20,

            // 装备和功法加成
            physical_attack_base: 20,
            physical_attack_equipment: 0,
            physical_defense_base: 10,
            physical_defense_equipment: 0,

            // 境界信息
            realm_id: 1,
            realm_name: '练气期',

            // 头像信息
            character_avatar: '../../images/char/ck.png',
            avatar_image: '../../images/char/ck.png',

            // 标记为游客模式
            isGuest: true,

            // 🔧 重要修复：游客模式也需要空的套装特殊效果数组
            set_special_effects: [],
        };
    }

    // 设置玩家头像
    setPlayerAvatar(avatarFileName) {
        try {
            debugLog('设置玩家头像:', avatarFileName);

            // 处理空值或undefined
            if (!avatarFileName) {
                avatarFileName = '../../images/char/ck.png';
                debugLog('头像为空，使用默认头像:', avatarFileName);
            }

            // 如果不是完整路径，添加前缀
            if (
                !avatarFileName.startsWith('assets/') &&
                !avatarFileName.startsWith('/') &&
                !avatarFileName.startsWith('http')
            ) {
                avatarFileName = `../../images/char/${avatarFileName}`;
                debugLog('添加路径前缀:', avatarFileName);
            }

            // 保存到playerStats供其他方法使用
            if (this.playerStats) {
                this.playerStats.character_avatar = avatarFileName;
                this.playerStats.avatar_image = avatarFileName;
            }
        } catch (error) {
            debugError('设置头像时出错:', error);
        }
    }

    // 加载武器数据
    async loadWeaponData() {
        try {
            // 🔧 修改：武器数据缓存时间减少到5秒，因为耐久度会在战斗中变化
            const apiUrl = window.GameConfig
                ? window.GameConfig.getApiUrl('equipment_integrated.php?action=get_weapon_slots')
                : '../../src/api/equipment_integrated.php?action=get_weapon_slots';
            const data = await this.makeApiRequest(apiUrl, 'weapon_slots', 5000);

            // 检查是否需要登录
            if (!data.success && data.message && data.message.includes('登录')) {
                debugLog('🎮 未登录用户，使用默认武器配置');
                this.weaponSlots = this.getDefaultWeaponSlots();
                return;
            }

            if (data.success && data.weapon_slots) {
                debugLog('🔧 [API调试] 原始武器槽位数据:', data.weapon_slots);

                this.weaponSlots = data.weapon_slots.map(slot => {
                    if (slot.inventory_item_id && slot.name) {
                        // 🔧 详细调试：输出每个武器的原始图片字段
                        // console.log(`🖼️ [API调试] 武器"${slot.name}"的原始图片字段:`, {
                        //     icon_image: slot.icon_image,
                        //     model_image: slot.model_image,
                        //     detail_image: slot.detail_image,
                        //     image_url: slot.image_url,
                        //     slot_type: slot.slot_type,
                        //     name: slot.name
                        // });

                        // 🔧 修复武器图片处理逻辑：明确区分不同用途的图片字段
                        // icon_image: 用于武器详情弹窗显示
                        // model_image: 用于战斗动画显示
                        // detail_image: 用于详细信息显示
                        let weaponImage = slot.icon_image || slot.image_url || null; // 优先使用icon_image用于详情显示
                        let iconImage = slot.icon_image || null;
                        let detailImage = slot.detail_image || slot.icon_image || null;
                        let modelImage = slot.model_image || slot.icon_image || null; // model_image用于战斗动画

                        // 🔧 如果API没有提供真实图片字段，则使用image_url作为备选
                        if (!weaponImage && slot.image_url) {
                            weaponImage = slot.image_url;
                        }

                        // 🔧 优化：使用图片路径管理器统一处理图片路径格式，避免重复处理相同路径
                        if (window.ImagePathManager) {
                            // 🔧 创建唯一路径集合，避免重复处理
                            const uniquePaths = new Set();
                            const pathMap = new Map();

                            // 收集所有需要处理的路径
                            if (weaponImage) uniquePaths.add(weaponImage);
                            if (iconImage && iconImage !== weaponImage) uniquePaths.add(iconImage);
                            if (
                                detailImage &&
                                detailImage !== weaponImage &&
                                detailImage !== iconImage
                            )
                                uniquePaths.add(detailImage);
                            if (
                                modelImage &&
                                modelImage !== weaponImage &&
                                modelImage !== iconImage &&
                                modelImage !== detailImage
                            )
                                uniquePaths.add(modelImage);

                            // 批量处理唯一路径
                            uniquePaths.forEach(path => {
                                pathMap.set(path, window.ImagePathManager.getWeaponImage(path));
                            });

                            // 应用处理结果
                            weaponImage = weaponImage ? pathMap.get(weaponImage) : null;
                            iconImage = iconImage ? pathMap.get(iconImage) : null;
                            detailImage = detailImage ? pathMap.get(detailImage) : null;
                            modelImage = modelImage ? pathMap.get(modelImage) : null;
                        } else {
                            debugWarn('图片路径管理器未加载，使用原始图片路径');
                        }

                        return {
                            id: slot.inventory_item_id,
                            inventory_item_id: slot.inventory_item_id, // 添加这个字段
                            name: slot.name || '未知武器',
                            description: slot.description || null,
                            skill_name: slot.skill_name || 'feijian',

                            // 🔧 完整的基础属性 - 修复攻击力计算
                            physical_attack: parseInt(slot.physical_attack) || 0,
                            immortal_attack: parseInt(slot.immortal_attack) || 0,

                            hp_bonus: parseInt(slot.hp_bonus) || 0,
                            mp_bonus: parseInt(slot.mp_bonus) || 0,
                            speed_bonus: parseInt(slot.speed_bonus) || 0,

                            // 🔧 物理/仙术防御力（新字段支持）
                            physical_defense: parseInt(slot.physical_defense) || 0,
                            immortal_defense: parseInt(slot.immortal_defense) || 0,

                            // 🔧 完整的战斗属性
                            critical_bonus: parseFloat(slot.critical_bonus) || 0,
                            critical_damage: parseFloat(slot.critical_damage) || 0,
                            accuracy_bonus: parseFloat(slot.accuracy_bonus) || 0,
                            dodge_bonus: parseFloat(slot.dodge_bonus) || 0,
                            block_bonus: parseFloat(slot.block_bonus) || 0,

                            // 🔧 基础装备属性 - 修复耐久度字段映射
                            slot_type: slot.slot_type || 'sword',
                            // 🔧 修复：只有当inventory_item_id有效时才设置耐久度，避免无效ID显示错误耐久度
                            durability: slot.inventory_item_id
                                ? parseInt(slot.current_durability) || 0
                                : 0,
                            current_durability: slot.inventory_item_id
                                ? parseInt(slot.current_durability) || 0
                                : 0,
                            maxDurability: slot.inventory_item_id
                                ? parseInt(slot.max_durability) || 100
                                : 100,
                            max_durability: slot.inventory_item_id
                                ? parseInt(slot.max_durability) || 100
                                : 100,
                            damage_multiplier: parseFloat(slot.damage_multiplier) || 1.0,
                            rarity: slot.rarity || 'common',
                            level_requirement: parseInt(slot.level_requirement) || 1,
                            enhancement_level: parseInt(slot.enhancement_level) || 0,

                            // 🔧 技能相关属性（完整）
                            element_type: slot.element_type || 'neutral',
                            skill_description: slot.skill_description || null,
                            animation_model: slot.animation_model || 'feijian',
                            mp_cost: parseInt(slot.mp_cost) || 0,
                            cooldown: parseInt(slot.cooldown) || parseInt(slot.cooldown_time) || 0,
                            trigger_chance: parseFloat(slot.trigger_chance) || 0,
                            effect_duration: parseInt(slot.effect_duration) || 0,

                            // 🔧 其他属性
                            special_effects: slot.special_effects || null,
                            sell_price:
                                parseInt(slot.sell_price) ||
                                Math.floor(
                                    Math.max(
                                        parseInt(slot.physical_attack) || 0,
                                        parseInt(slot.immortal_attack) || 0
                                    ) * 2
                                ),

                            // 🔧 图片信息 - 明确区分用途
                            image_url: weaponImage || null, // 主要用于详情弹窗显示
                            icon_image: iconImage || null, // 图标显示用
                            detail_image: detailImage || null, // 详细信息显示用
                            model_image: modelImage || null, // 战斗动画用
                            weaponImage: weaponImage || null, // 兼容性字段，用于详情显示
                        };
                    }
                    return null;
                });

                // 确保有6个槽位
                while (this.weaponSlots.length < 6) {
                    this.weaponSlots.push(null);
                }

                debugLog('武器数据加载成功:', this.weaponSlots);
            } else {
                debugWarn('加载武器数据失败，使用默认武器配置');
                debugWarn('API返回:', data);
                this.weaponSlots = this.getDefaultWeaponSlots();
            }
        } catch (error) {
            debugError('加载武器数据失败:', error);
            debugLog('🎮 使用默认武器配置');
            this.weaponSlots = this.getDefaultWeaponSlots();
        }
    }

    // 获取默认武器槽位
    getDefaultWeaponSlots() {
        const defaultSlots = [];

        // 创建默认的飞剑
        defaultSlots.push({
            id: 1001,
            name: '初学者飞剑',
            skill_name: 'feijian',
            physical_attack: 10,
            immortal_attack: 0,
            slot_type: 'sword',
            durability: 100,
            maxDurability: 100,
            damage_multiplier: 1.0,
            rarity: 'common',
            level_requirement: 1,
            enhancement_level: 0,
            critical_bonus: 5.0, // 🔧 修复：统一使用critical_bonus
            critical_damage: 1.5, // 🔧 修复：暴击伤害统一小数格式（150%）
            accuracy_bonus: 0,
            dodge_bonus: 0,
            mp_cost: 5,
            cooldown: 0,
            image_url: '../../images/battle_sword.png',
            icon_image: '../../images/battle_sword.png',
            detail_image: '../../images/battle_sword.png',
            model_image: '../../images/battle_sword.png',
            animation_model: 'feijian',
        });

        // 创建默认的万剑诀
        defaultSlots.push({
            id: 1002,
            name: '初学者法剑',
            skill_name: 'wanjianjue',
            physical_attack: 0,
            immortal_attack: 15,
            slot_type: 'sword',
            durability: 100,
            maxDurability: 100,
            damage_multiplier: 1.2,
            rarity: 'common',
            level_requirement: 1,
            enhancement_level: 0,
            critical_bonus: 5.0, // 🔧 修复：统一使用critical_bonus
            critical_damage: 1.5, // 🔧 修复：暴击伤害统一小数格式（150%）
            accuracy_bonus: 0,
            dodge_bonus: 0,
            mp_cost: 15,
            cooldown: 2,
            image_url: '../../images/battle_sword.png',
            icon_image: '../../images/battle_sword.png',
            detail_image: '../../images/battle_sword.png',
            model_image: '../../images/battle_sword.png',
            animation_model: 'wanjianjue',
        });

        // 创建默认的掌心雷
        defaultSlots.push({
            id: 1003,
            name: '初学者手套',
            skill_name: 'zhangxinlei',
            physical_attack: 0,
            immortal_attack: 8,
            slot_type: 'glove',
            durability: 100,
            maxDurability: 100,
            damage_multiplier: 1.1,
            rarity: 'common',
            level_requirement: 1,
            enhancement_level: 0,
            critical_bonus: 5.0, // 🔧 修复：统一使用critical_bonus
            critical_damage: 1.5, // 🔧 修复：暴击伤害使用critical_damage
            accuracy_bonus: 0,
            dodge_bonus: 0,
            mp_cost: 10,
            cooldown: 1,
            image_url: '../../images/battle_sword.png',
            icon_image: '../../images/battle_sword.png',
            detail_image: '../../images/battle_sword.png',
            model_image: '../../images/battle_sword.png',
            animation_model: 'zhangxinlei',
        });

        // 如果需要6个槽位，填充其余槽位为null
        while (defaultSlots.length < 6) {
            defaultSlots.push(null);
        }

        return defaultSlots;
    }

    // 加载敌人数据
    async loadEnemyData() {
        let apiUrl = ''; // 🔧 修复：将apiUrl变量声明移到方法开头

        try {
            debugLog('🔄 开始加载敌人数据...');

            // 🔧 修复：检查URL参数，判断是否为竞技场战斗
            const urlParams = new URLSearchParams(window.location.search);
            const isArena = urlParams.get('arena') === '1';

            if (isArena) {
                // 🏆 竞技场模式：加载对手数据
                await this.loadArenaOpponentData(urlParams);
                return;
            }

            // 🔧 修复1：普通战斗模式 - 使用battle_unified.php
            const mapId =
                parseInt(urlParams.get('map_id')) ||
                (this.currentArea ? this.currentArea.mapId : 1);
            const mapCode =
                urlParams.get('map_code') ||
                (this.currentArea ? this.currentArea.areaId : 'kunlun');
            const stageNumber = this.currentStage || 1;

            debugLog('敌人数据加载参数:', { mapId, mapCode, stageNumber });

            // 调用统一战斗API获取敌人数据
            apiUrl = window.GameConfig
                ? window.GameConfig.getApiUrl(
                      `battle_unified.php?action=init_battle&map_id=${mapId}&map_code=${mapCode}&stage_number=${stageNumber}`
                  )
                : `../../src/api/battle_unified.php?action=init_battle&map_id=${mapId}&map_code=${mapCode}&stage_number=${stageNumber}`;
            debugLog('调用敌人数据API:', apiUrl);

            const response = await this.makeApiRequest(
                apiUrl,
                `battle_enemy_${mapId}_${stageNumber}`,
                5000
            );

            if (response.success && response.data && response.data.enemy_data) {
                debugLog('✅ 成功获取敌人数据:', response.data.enemy_data);

                // 🔧 新增：存储地图数据用于最大关卡数检查
                if (response.data.map_info) {
                    this.currentMapData = response.data.map_info;
                    debugLog('✅ 存储地图数据:', this.currentMapData);
                }

                // 确保数据完整性并处理头像路径
                const avatar =
                    response.data.enemy_data.avatarImage ||
                    response.data.enemy_data.avatar ||
                    'yelang';
                const modelImage =
                    response.data.enemy_data.modelImage ||
                    response.data.enemy_data.model_image ||
                    avatar;

                // 🔧 修复：使用图片路径管理器处理敌人头像
                let finalAvatar, finalModelImage;
                if (window.ImagePathManager) {
                    finalAvatar = window.ImagePathManager.getEnemyImage(avatar);
                    finalModelImage = window.ImagePathManager.getEnemyImage(modelImage);
                } else {
                    debugWarn('图片路径管理器未加载，使用备用敌人图片');
                    finalAvatar = finalModelImage = '../../images/enemy/yelang.png';
                }

                // 构建完整的敌人数据
                this.enemyData = {
                    id: response.data.enemy_data.id || 0,
                    name: response.data.enemy_data.name || '未知怪物',
                    type: response.data.enemy_data.type || 'normal',
                    level: parseInt(response.data.enemy_data.level) || 1,
                    hp: parseInt(response.data.enemy_data.hp) || 100,
                    max_hp: parseInt(response.data.enemy_data.hp) || 100, // 🔧 修复：怪物最大生命值
                    mp: parseInt(response.data.enemy_data.mp) || 50,
                    max_mp: parseInt(response.data.enemy_data.mp) || 50, // 🔧 修复：怪物最大法力值
                    attack: parseInt(response.data.enemy_data.attack) || 10,
                    defense: parseInt(response.data.enemy_data.defense) || 5,
                    speed: parseInt(response.data.enemy_data.speed) || 10, // 🔧 注意：敌人使用基础speed字段，不是speed_total
                    accuracy_bonus: parseFloat(response.data.enemy_data.accuracy_bonus) || 85, // 🔧 修复：使用数据库字段名
                    dodge_bonus: parseFloat(response.data.enemy_data.dodge_bonus) || 5, // 🔧 修复：使用数据库字段名
                    critical_bonus: parseFloat(response.data.enemy_data.critical_bonus) || 5, // 🔧 修复：统一使用critical_bonus
                    skills: response.data.enemy_data.skills || ['普通攻击'],
                    avatarImage: finalAvatar,
                    modelImage: finalModelImage,
                    description: response.data.enemy_data.description || '神秘的怪物',
                    isBoss: response.data.enemy_data.isBoss || false,
                    spiritStoneReward: parseInt(response.data.enemy_data.spiritStoneReward) || 10,
                    goldReward: parseInt(response.data.enemy_data.goldReward) || 5,
                };

                debugLog('✅ 敌人数据处理完成:', this.enemyData);
                return;
            } else {
                debugWarn('❌ 统一API返回数据格式不正确:', response);
                throw new Error('统一API响应格式错误');
            }
        } catch (error) {
            debugError('❌ 从统一API加载敌人数据失败:', error);
            debugError('错误详情:', {
                message: error.message,
                stack: error.stack,
                apiUrl: apiUrl, // 🔧 修复：现在apiUrl在正确的作用域中
                requestParams: {
                    mapId: this.currentArea?.mapId,
                    mapCode: this.currentArea?.areaId,
                    stageNumber: this.currentStage,
                },
            });

            // 备用方案：使用默认敌人数据
            debugLog('🔄 使用默认敌人数据作为备用方案');
            this.enemyData = this.getDefaultEnemyData();

            // 🔧 修复：确保默认敌人数据也使用图片路径管理器
            if (window.ImagePathManager && this.enemyData) {
                this.enemyData.avatarImage = window.ImagePathManager.getEnemyImage(
                    this.enemyData.avatarImage || 'yelang'
                );
                this.enemyData.modelImage = window.ImagePathManager.getEnemyImage(
                    this.enemyData.modelImage || 'yelang'
                );
            }

            debugLog('✅ 默认敌人数据设置完成:', this.enemyData);
        }
    }

    /**
     * 🏆 加载竞技场对手数据
     */
    async loadArenaOpponentData(urlParams) {
        try {
            debugLog('🏆 开始加载竞技场对手数据...');

            const opponentId = urlParams.get('opponent_id');
            const isAi = urlParams.get('is_ai') === '1';

            debugLog('🏆 对手参数:', {
                opponentId,
                isAi,
                opponentName: urlParams.get('opponent_name'),
                opponentPower: urlParams.get('opponent_power'),
            });

            // 从竞技场API获取完整对手数据
            const apiUrl = window.GameConfig
                ? window.GameConfig.getApiUrl('immortal_arena.php')
                : '../../src/api/immortal_arena.php';
            const response = await fetch(apiUrl, {
                method: 'POST',
                credentials: 'same-origin',
                headers: {
                    'Content-Type': 'application/json; charset=utf-8',
                },
                body: JSON.stringify({
                    action: 'get_opponent_data',
                    opponent_id: opponentId,
                    is_ai: isAi,
                    opponent_name: urlParams.get('opponent_name'), // 🔧 修复：传递对手名字确保一致性
                }),
            });

            if (!response.ok) {
                throw new Error(`竞技场API请求失败: ${response.status}`);
            }

            const data = await response.json();
            debugLog('🏆 竞技场API响应:', data);

            if (!data.success) {
                throw new Error(data.message || '获取对手数据失败');
            }

            // 设置对手数据
            const opponentData = data.opponent_data;

            // 🏆 标记为竞技场玩家对手
            this.enemy = {
                id: opponentData.character_id || null,
                name: opponentData.character_name || urlParams.get('opponent_name'),
                level: opponentData.realm_level || 1,
                hp: opponentData.max_hp || 1000,
                currentHp: opponentData.max_hp || 1000,
                max_hp: opponentData.max_hp || 1000,
                mp: opponentData.max_mp || 500,
                currentMp: opponentData.max_mp || 500,
                max_mp: opponentData.max_mp || 500,
                attack: opponentData.physical_attack || 100,
                physical_attack: opponentData.physical_attack || 100,
                immortal_attack: opponentData.immortal_attack || 50,
                defense: opponentData.physical_defense || 80,
                physical_defense: opponentData.physical_defense || 80,
                immortal_defense: opponentData.immortal_defense || 40,
                speed_bonus: opponentData.speed_bonus || 50,
                critical_bonus: opponentData.critical_bonus || 5,
                critical_damage: opponentData.critical_damage || 0.5,
                accuracy_bonus: opponentData.accuracy_bonus || 85,
                dodge_bonus: opponentData.dodge_bonus || 5,
                critical_resistance: opponentData.critical_resistance || 0,
                isPlayer: true, // 🏆 标记为玩家对手
                isAi: isAi,
                skill_sequence: opponentData.skill_sequence || [0, 1, 2, 3, 4, 5],
                weapon_skills: opponentData.weapon_skills || ['普通攻击'],
                weapon_skills_data: opponentData.weapon_skills_data || [], // 🔧 修复：添加技能动画数据
                skills: opponentData.weapon_skills || ['普通攻击'],
            };

            debugLog('🏆 竞技场对手数据设置完成:', this.enemy);

            // 🔧 修复：设置enemyData属性，与普通战斗保持一致
            this.enemyData = {
                id: this.enemy.id,
                name: this.enemy.name,
                level: this.enemy.level,
                realm_name: opponentData.realm_name || '开光期', // 🔧 修复：添加境界名称
                realm_id: opponentData.realm_id || 1, // 🔧 修复：添加境界ID
                hp: this.enemy.hp,
                max_hp: this.enemy.max_hp,
                mp: this.enemy.mp,
                max_mp: this.enemy.max_mp,
                attack: this.enemy.attack,
                physical_attack: this.enemy.physical_attack,
                immortal_attack: this.enemy.immortal_attack,
                defense: this.enemy.defense,
                physical_defense: this.enemy.physical_defense,
                immortal_defense: this.enemy.immortal_defense,
                speed_bonus: this.enemy.speed_bonus, // 🔧 修复：使用正确的字段名
                accuracy_bonus: this.enemy.accuracy_bonus,
                dodge_bonus: this.enemy.dodge_bonus,
                critical_bonus: this.enemy.critical_bonus,
                critical_damage: this.enemy.critical_damage,
                critical_resistance: this.enemy.critical_resistance,
                skills: this.enemy.skills,
                weapon_skills: this.enemy.weapon_skills,
                weapon_skills_data: this.enemy.weapon_skills_data, // 🔧 修复：传递技能动画数据到enemyData
                skill_sequence: this.enemy.skill_sequence,
                type: 'arena_player',
                ai_pattern: 'player',
                isPlayer: true,
                isAi: this.enemy.isAi,
                avatarImage: opponentData.avatar_image || 'ck.png', // 🔧 修复：真实玩家头像在char目录
                modelImage: opponentData.avatar_image || 'ck.png',
                spiritStoneReward: 100, // 竞技场固定奖励
                goldReward: 50,
            };

            debugLog('✅ 竞技场对手数据加载完成:', this.enemyData);
        } catch (error) {
            console.error('❌ 加载竞技场对手数据失败:', error);
            alert('加载对手数据失败: ' + error.message);
            // 返回竞技场
            window.location.href = 'immortal_arena.html';
        }
    }

    // 从地图代码获取地图ID
    async getMapIdFromCode(mapCode) {
        try {
            console.log(`尝试从地图代码 ${mapCode} 获取地图ID`);
            // 🔧 修改：使用缓存API请求方法
            const apiUrl = window.GameConfig
                ? window.GameConfig.getApiUrl('adventure_maps.php?action=get_maps')
                : '../../src/api/adventure_maps.php?action=get_maps';
            const data = await this.makeApiRequest(apiUrl, 'all_maps');

            if (data.success && data.maps) {
                // 遍历所有类型的地图
                const allMaps = [
                    ...(data.maps.normal_maps || []),
                    ...(data.maps.dungeons || []),
                    ...(data.maps.special_maps || []),
                    ...(data.maps.pvp_maps || []),
                ];

                // 查找匹配的地图
                const matchedMap = allMaps.find(map => map.map_code === mapCode);
                if (matchedMap) {
                    console.log(`找到匹配的地图: ID=${matchedMap.id}, 名称=${matchedMap.map_name}`);
                    return matchedMap;
                } else {
                    console.warn(`在API返回的地图中找不到代码为 ${mapCode} 的地图`);
                }
            } else {
                console.warn('API调用成功但未返回地图数据');
            }
        } catch (error) {
            console.error(`从地图代码获取地图ID失败:`, error);
        }

        // 地图代码到ID的硬编码映射表（备用）
        const mapCodeToId = {
            kunlun: 1,
            donghai: 2,
            jiuyou: 3,
            santiansan: 4,
            huolong: 5,
            hanbing: 6,
        };

        if (mapCode in mapCodeToId) {
            console.log(`使用硬编码映射获取地图ID: ${mapCode} -> ${mapCodeToId[mapCode]}`);
            return { id: mapCodeToId[mapCode], map_name: this.getMapNameFromCode(mapCode) };
        }

        console.warn(`无法获取地图ID，返回null`);
        return null;
    }

    // 从地图代码获取地图名称（备用）
    getMapNameFromCode(mapCode) {
        const mapNames = {
            kunlun: '昆仑山脉',
            donghai: '东海龙宫',
            jiuyou: '九幽冥界',
            santiansan: '三十三重天',
            huolong: '火龙洞窟',
            hanbing: '寒冰峡谷',
        };

        return mapNames[mapCode] || '未知地图';
    }

    // 获取战斗技能序列
    getBattleSkillSequence() {
        const sequence = [];

        debugLog('=== 生成战斗技能序列 ===');
        debugLog('武器槽位原始数据:', this.weaponSlots);

        for (let i = 0; i < 6; i++) {
            const weapon = this.weaponSlots[i];

            // 🔧 简化：只检查武器是否存在，耐久度检查移至战斗时
            if (weapon && weapon.name && weapon.id) {
                // 🔧 获取武器的基本信息，不进行耐久度逻辑判断
                const currentDurability = parseInt(weapon.durability) || 0;
                const maxDurability =
                    parseInt(weapon.max_durability) || parseInt(weapon.maxDurability) || 100;

                debugLog(
                    `槽位${i + 1}: ${weapon.name} (耐久度: ${currentDurability}/${maxDurability})`
                );

                // 🔧 计算武器攻击力 - 使用physical_attack和immortal_attack的最大值
                const physical_attack = parseInt(weapon.physical_attack) || 0;
                const immortal_attack = parseInt(weapon.immortal_attack) || 0;
                const weaponAttackPower = Math.max(physical_attack, immortal_attack);

                const skillData = {
                    // 🔧 武器基本信息
                    hasWeapon: true, // 暂时标记为有武器，具体状态在战斗时检查
                    weaponId: weapon.id,
                    weaponName: weapon.name,
                    skillName: weapon.skill_name || '剑气外放！',
                    animationType: weapon.animation_model || 'feijian',
                    weaponAttack: weaponAttackPower,
                    weaponType: weapon.slot_type || 'sword',

                    // 🔧 耐久度信息（用于战斗时检查）
                    durability: currentDurability,
                    maxDurability: maxDurability,
                    inventory_item_id: weapon.inventory_item_id,

                    // 🔧 武器属性
                    damageMultiplier: weapon.damage_multiplier || 1.0,
                    elementType: weapon.element_type || 'neutral',
                    element_type: weapon.element_type || 'neutral',
                    mp_cost: weapon.mp_cost || 0,
                    mpCost: weapon.mp_cost || 0,
                    cooldown_time: weapon.cooldown_time || 0,
                    cooldown: weapon.cooldown_time || 0,

                    // 🔧 武器图片信息
                    model_image: weapon.model_image || null,
                    icon_image: weapon.icon_image || null,
                    weaponImage: weapon.model_image || weapon.image_url || null,
                };

                console.log(
                    `  -> 武器: ${weapon.name}, 攻击力: ${weaponAttackPower}, 倍率: ${skillData.damageMultiplier}`
                );
                sequence.push(skillData);
            } else {
                // 🔧 空槽位处理
                const skillData = {
                    hasWeapon: false,
                    weaponId: null,
                    weaponName: '空槽位',
                    skillName: '剑气外放！',
                    animationType: 'feijian',
                    weaponAttack: 0,
                    weaponType: 'sword',
                    durability: 0,
                    maxDurability: 0,
                    damageMultiplier: 1.0,
                    elementType: 'neutral',
                    element_type: 'neutral',
                    mp_cost: 0,
                    mpCost: 0,
                    cooldown_time: 0,
                    cooldown: 0,
                };

                console.log(`  -> 空槽位，使用默认技能`);
                sequence.push(skillData);
            }
        }

        debugLog('✅ 技能序列生成完成，共', sequence.length, '个技能');
        return sequence;
    }

    // 计算最终属性（包含装备和人物属性加成）
    calculateFinalStats() {
        const baseStats = { ...this.playerStats };

        debugLog('=== 最终属性计算开始 ===');
        debugLog('基础属性:', baseStats);

        // 🔧 重要修复：battle_unified.php返回的已经是最终属性值（基础值+装备加成）
        // 不应该再重复计算装备加成，避免双重计算导致属性不一致

        // 🔧 修复：确保所有属性都是数值类型
        baseStats.physical_attack = parseInt(baseStats.physical_attack) || 0;
        baseStats.physical_defense = parseInt(baseStats.physical_defense) || 0;
        baseStats.immortal_attack = parseInt(baseStats.immortal_attack) || 0;
        baseStats.immortal_defense = parseInt(baseStats.immortal_defense) || 0;
        baseStats.hp_bonus = parseInt(baseStats.hp_bonus) || 300;
        baseStats.mp_bonus = parseInt(baseStats.mp_bonus) || 100;
        baseStats.level = parseInt(baseStats.level) || 1;

        // ✅ magic_attack/magic_defense字段已清理，统一使用immortal_attack/immortal_defense

        // 🔧 修复：动态属性使用后端API返回的最终值，避免重复计算
        baseStats.speed_bonus = parseInt(baseStats.speed_bonus) || 0;
        // 🔧 修复：确保speed_total字段存在
        if (!baseStats.speed_total && baseStats.speed) {
            baseStats.speed_total = baseStats.speed;
        }
        baseStats.accuracy_bonus = parseInt(baseStats.accuracy_bonus) || 85;
        baseStats.dodge_bonus = parseInt(baseStats.dodge_bonus) || 5;
        baseStats.critical_bonus = parseFloat(baseStats.critical_bonus) || 5;
        baseStats.criticalDamage = parseFloat(baseStats.criticalDamage) || 150;
        baseStats.critical_resistance = parseFloat(baseStats.critical_resistance) || 0;

        console.log('基础属性类型转换后:', baseStats);
        console.log('hp_bonus类型:', typeof baseStats.hp_bonus, '值:', baseStats.hp_bonus);

        // 🔧 重要修复：直接返回后端计算的最终属性，避免重复计算装备加成
        // battle_unified.php已经返回了包含装备加成的最终属性值
        const finalStats = {
            ...baseStats,
        };

        console.log('=== 最终属性计算结果 ===');
        console.log('最终属性（后端计算）:', finalStats);
        console.log(
            '🔧 修复说明：直接使用后端battle_unified.php返回的最终属性值，避免重复计算装备加成'
        );

        return finalStats;
    }

    // 获取技能伤害
    getSkillDamage(skillData, playerStats) {
        // 🔧 修改：根据武器类型选择正确的攻击力参数，使用新的物理/仙术攻击字段
        let playerAttack;
        if (skillData.weaponType === 'fan') {
            // 扇类武器使用仙术攻击力（使用immortal_attack作为主要仙术攻击力）
            playerAttack = playerStats.immortal_attack || 15;
        } else {
            // 剑类武器使用物理攻击力
            playerAttack = playerStats.physical_attack || 20;
        }

        // 🔧 修改：直接使用数据库中的真实技能倍率，而不是配置文件
        const skillMultiplier = skillData.damageMultiplier || 1.0;

        console.log(`🎯 技能伤害计算: 角色攻击${playerAttack} × 技能倍率${skillMultiplier}`);

        // 伤害计算 - 修复：移除重复的武器攻击计算，因为playerAttack已经包含了所有装备加成
        const attackPower = playerAttack * skillMultiplier;

        // 🔥 新增：五行相克伤害计算
        const elementalDamage = this.calculateElementalDamage(skillData, playerStats);
        const finalAttackPower = attackPower + elementalDamage;

        console.log(
            `🌟 五行伤害加成: 基础攻击${attackPower} + 五行伤害${elementalDamage} = 最终攻击${finalAttackPower}`
        );

        return Math.floor(finalAttackPower);
    }

    // 🔥 新增：计算五行相克伤害
    calculateElementalDamage(skillData, playerStats) {
        try {
            console.log('🔍 五行伤害调试开始:', { skillData, playerStats });

            // 获取技能五行属性
            const skillElement = skillData.elementType || skillData.element_type || 'neutral';

            console.log(`🔍 技能五行属性: ${skillElement}`);
            console.log(`🔍 角色灵根数据:`, {
                metal: playerStats.metal,
                wood: playerStats.wood,
                water: playerStats.water,
                fire: playerStats.fire,
                earth: playerStats.earth,
            });

            // 🔥 新增：详细的原始数据调试
            console.log(`🔍 playerStats完整数据:`, playerStats);
            console.log(`🔍 检查是否存在其他灵根字段:`, {
                gold_root: playerStats.gold_root,
                metal_root: playerStats.metal_root,
                wood_root: playerStats.wood_root,
                water_root: playerStats.water_root,
                fire_root: playerStats.fire_root,
                earth_root: playerStats.earth_root,
            });

            if (!skillElement || skillElement === 'neutral') {
                console.log('⚪ 技能为中性，不产生五行伤害');
                return 0; // 无五行属性技能不产生五行伤害
            }

            // 获取角色对应的五行灵根值
            const elementMapping = {
                metal: playerStats.metal || 10,
                wood: playerStats.wood || 10,
                water: playerStats.water || 10,
                fire: playerStats.fire || 10,
                earth: playerStats.earth || 10,
            };

            const rootValue = elementMapping[skillElement] || 10;

            // 五行伤害计算：灵根值 × 1.2（与后端保持一致）
            const elementalDamage = Math.round(rootValue * 1.2);

            console.log(
                `🌟 五行伤害计算: ${skillElement}灵根${rootValue} × 1.2 = ${elementalDamage}`
            );

            return elementalDamage;
        } catch (error) {
            console.error('计算五行伤害失败:', error);
            return 0;
        }
    }

    // 计算实际伤害 - 🔧 新版本：集成命中和暴击判定
    calculateFinalDamage(attackPower, defense, attacker = null, defender = null, options = {}) {
        // 🔧 如果没有传入攻防双方数据，使用旧版本兼容计算
        if (!attacker || !defender) {
            const attack = parseFloat(attackPower) || 0;
            const def = parseFloat(defense) || 0;

            console.log('⚠️ 使用兼容模式伤害计算');
            console.log('原始攻击力:', attackPower, '(类型:', typeof attackPower, ')');
            console.log('原始防御力:', defense, '(类型:', typeof defense, ')');

            const baseDamage = Math.max(1, attack - def);
            // 🔧 修改：移除随机浮动系数，使伤害更可预测
            const finalDamage = baseDamage;

            return Math.max(1, finalDamage);
        }

        // 🔧 新版本：使用战斗计算器
        if (!this.combatCalculator) {
            this.combatCalculator = new BattleCombatCalculator();
        }

        // 🔧 确保传递技能类型和武器类型信息
        const weaponType = options.weaponType || options.skillData?.weaponType || 'sword';
        const skillType = options.skillType || (weaponType === 'fan' ? 'magic' : 'physical');

        console.log(`🔧 战斗伤害计算: weaponType=${weaponType}, skillType=${skillType}`);

        // 🔥 新增：检查暴走状态
        const combatOptions = {
            ...options,
            weaponType,
            skillType,
            isBerserkMode: false, // 默认非暴走状态
        };

        // 🔥 检查敌人是否处于暴走状态（只有敌人攻击时才判断）
        if (
            attacker &&
            attacker.isEnemy !== false &&
            window.battleSystem &&
            window.battleSystem.enemyBerserkMode
        ) {
            combatOptions.isBerserkMode = true;
            console.log('🔥 检测到敌人暴走状态，启用必暴击必命中');
        }

        // 返回完整的战斗结果，包含命中、暴击判定
        return this.combatCalculator.calculateBattleResult(
            attacker,
            defender,
            attackPower,
            combatOptions
        );
    }

    // 计算战斗掉落
    async calculateBattleDrops(isVictory) {
        try {
            if (!isVictory) {
                console.log('战斗失败，无掉落');
                return [];
            }

            // 🔧 修改为GET请求，使用正确的参数名称
            let monsterType = 'normal';
            if (this.enemyData) {
                if (this.enemyData.type) {
                    monsterType = this.enemyData.type;
                } else if (this.enemyData.monster_tier) {
                    monsterType = this.enemyData.monster_tier;
                } else if (this.enemyData.isBoss || this.currentStage % 10 === 0) {
                    monsterType = 'boss';
                }
            }

            // 🔧 修复：安全获取区域信息，防止currentArea为null
            let mapCode = 'taiyifeng'; // 默认地图
            if (this.currentArea && this.currentArea.areaId) {
                mapCode = this.currentArea.areaId;
            } else {
                // 从URL获取地图代码作为备选
                const urlParams = new URLSearchParams(window.location.search);
                mapCode = urlParams.get('map_code') || 'taiyifeng';
                console.log('🔧 currentArea为空，从URL获取地图代码:', mapCode);
            }

            // 构建GET请求URL
            const params = new URLSearchParams({
                action: 'calculate_drops',
                map_code: mapCode,
                current_stage: this.currentStage,
                monster_type: monsterType,
            });

            console.log('掉落计算参数:', {
                map_code: mapCode,
                current_stage: this.currentStage,
                monster_type: monsterType,
                is_victory: isVictory,
            });

            // 🔧 修改为GET请求
            const apiUrl = window.GameConfig
                ? window.GameConfig.getApiUrl(`battle_drops_unified.php?${params.toString()}`)
                : `../../src/api/battle_drops_unified.php?${params.toString()}`;
            const response = await fetch(apiUrl);

            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status}`);
            }

            const data = await response.json();
            console.log('掉落计算API返回:', data);

            if (data.success) {
                const drops = data.drops || [];
                console.log(`掉落计算成功，获得 ${drops.length} 个物品:`, drops);

                // 🔧 修复：确保掉落物品数据格式正确，映射正确的图片字段
                const processedDrops = drops.map(drop => ({
                    id: drop.id,
                    name: drop.name,
                    type: drop.type,
                    rarity: drop.rarity || 'common',
                    quantity: parseInt(drop.quantity) || 1,
                    icon: drop.icon_image, // 🔧 修复：使用正确的图片字段名
                    icon_image: drop.icon_image, // 🔧 新增：保留原始字段名用于兼容
                    description: drop.description || '暂无描述',
                    sell_price: parseInt(drop.sell_price) || 0,
                    item_data: drop.item_data || {},
                }));

                return processedDrops;
            } else {
                console.error('计算掉落失败:', data.message);
                return [];
            }
        } catch (error) {
            console.error('计算战斗掉落失败:', error);
            return [];
        }
    }

    // 获取地图信息
    async getMapInfo() {
        try {
            // 从URL获取地图代码
            const urlParams = new URLSearchParams(window.location.search);
            const mapCode = urlParams.get('map_code') || 'kunlun';

            console.log('开始获取地图信息, 地图代码:', mapCode);

            // 从数据库获取地图信息
            const apiUrl = window.GameConfig
                ? window.GameConfig.getApiUrl(
                      `battle_drops_unified.php?action=get_map_info&map_code=${mapCode}`
                  )
                : `../../src/api/battle_drops_unified.php?action=get_map_info&map_code=${mapCode}`;
            const response = await fetch(apiUrl, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
            });

            const data = await response.json();
            console.log('地图信息API返回:', data);

            // 更新页面显示
            const areaNameElement = document.getElementById('area-name');
            if (areaNameElement) {
                const displayName =
                    data.success && data.map_info ? data.map_info.map_name : '未知地图';
                areaNameElement.textContent = displayName;
                console.log('设置地图名称为:', displayName);
            } else {
                console.warn('找不到area-name元素');
            }

            return data.success ? data.map_info : null;
        } catch (error) {
            console.error('获取地图信息时出错:', error);
            // 发生错误时显示未知地图
            const areaNameElement = document.getElementById('area-name');
            if (areaNameElement) {
                areaNameElement.textContent = '未知地图';
            }
            return null;
        }
    }

    // Fallback方法：尝试从adventure_maps.php获取怪物数据
    async getEnemyDataFallback(mapId, stageNumber) {
        try {
            console.log(
                `尝试从adventure_maps.php获取怪物数据: mapId=${mapId}, stageNumber=${stageNumber}`
            );

            // 构建API URL
            const apiUrl = window.GameConfig
                ? window.GameConfig.getApiUrl(
                      `adventure_maps.php?action=get_stage_info&map_id=${mapId}&stage_number=${stageNumber}`
                  )
                : `../../src/api/adventure_maps.php?action=get_stage_info&map_id=${mapId}&stage_number=${stageNumber}`;
            console.log('调用备用API:', apiUrl);

            // 🔧 修改：使用缓存API请求方法
            const data = await this.makeApiRequest(
                apiUrl,
                `fallback_enemy_${mapId}_${stageNumber}`
            );
            console.log('备用API返回数据:', data);

            if (data.success && data.stage) {
                const stage = data.stage;

                // 构建标准格式的敌人数据
                return {
                    name: stage.monster_name || '野怪',
                    level: stage.monster_level || stageNumber || 1,
                    hp: parseInt(stage.base_hp || 100) * parseInt(stage.monster_level || 1),
                    hp_bonus: parseInt(stage.base_hp || 100) * parseInt(stage.monster_level || 1),
                    attack:
                        parseInt(stage.base_attack || 10) *
                        (1 + parseInt(stage.monster_level || 1) * 0.1),
                    defense:
                        parseInt(stage.base_defense || 5) *
                        (1 + parseInt(stage.monster_level || 1) * 0.05),
                    speed: parseInt(stage.base_speed || 10),
                    accuracy_bonus: 85,
                    dodge_bonus: 5,
                    critical_bonus: 5, // 🔧 修复：统一使用critical_bonus
                    // 🔧 修复：添加缺失的奖励字段
                    spiritStoneReward: parseInt(stage.spirit_stone_reward) || 10,
                    goldReward: parseInt(stage.gold_reward) || 5,
                    avatarImage: stage.avatar_image || '../../images/enemy/monster_default.png',
                    modelImage: stage.model_image || '../../images/enemy/monster_default.png',
                    // 保存原始数据
                    originalData: stage,
                };
            } else {
                console.warn('备用API未返回有效的怪物数据');
                return null;
            }
        } catch (error) {
            console.error('备用API调用失败:', error);
            return null;
        }
    }

    // 获取默认敌人数据
    getDefaultEnemyData() {
        // 基于当前关卡生成默认怪物
        const stage = this.currentStage || 1;
        const isBoss = stage % 10 === 0;

        // 根据关卡等级调整怪物属性
        const levelMultiplier = 1 + (stage - 1) * 0.05; // 每关增加5%属性
        const baseHp = isBoss ? 200 : 100;
        const baseAttack = isBoss ? 20 : 10;
        const baseDefense = isBoss ? 10 : 5;
        const baseSpeed = isBoss ? 15 : 10;

        // 计算属性
        const hp = Math.round(baseHp * levelMultiplier);
        const attack = Math.round(baseAttack * levelMultiplier);
        const defense = Math.round(baseDefense * levelMultiplier);
        const speed = Math.round(baseSpeed * levelMultiplier);

        // 设置名称和技能
        let monsterName;
        let monsterType;
        let monsterSkills;

        if (isBoss) {
            monsterName = '关卡守护者';
            monsterType = 'boss';
            // Boss拥有更多技能
            monsterSkills = [
                '霸王撞击！',
                '守护者之怒！',
                '天崩地裂！',
                '雷霆万钧！',
                '死亡凝视！',
            ];
        } else if (stage % 5 === 0) {
            monsterName = '精英妖兽';
            monsterType = 'elite';
            // 精英怪物有中等技能
            monsterSkills = ['精英突击！', '狂暴撕咬！', '妖力冲击！', '邪恶凝视！'];
        } else {
            monsterName = '山野妖兽';
            monsterType = 'normal';
            // 普通怪物基础技能
            monsterSkills = ['野兽撕咬！', '利爪攻击！', '咆哮震慑！'];
        }

        // 设置图片
        let avatarImage;
        if (isBoss) {
            avatarImage = '../../images/enemy/boss_default.png';
        } else if (monsterType === 'elite') {
            avatarImage = '../../images/enemy/elite_default.png';
        } else {
            avatarImage = '../../images/enemy/monster_default.png';
        }

        // 返回默认怪物数据
        return {
            name: monsterName,
            level: stage,
            hp: hp,
            max_hp: hp,
            mp: Math.round(30 + stage * 5), // 根据关卡调整MP
            max_mp: Math.round(30 + stage * 5),
            attack: attack,
            defense: defense,
            speed: speed,
            accuracy_bonus: 85,
            dodge_bonus: isBoss ? 10 : 5,
            critical_bonus: isBoss ? 15 : 5, // 🔧 修复：统一使用critical_bonus
            // 🔧 新增：根据怪物类型添加技能
            skills: monsterSkills,
            // 🔧 修复：添加缺失的奖励字段
            spiritStoneReward: isBoss ? 100 : 50,
            goldReward: isBoss ? 70 : 35,
            avatarImage: avatarImage,
            modelImage: avatarImage,
            type: monsterType,
            isBoss: isBoss,
        };
    }

    // 🔧 新增：通用API请求方法，带缓存和去重
    async makeApiRequest(url, cacheKey = null, cacheDuration = 30000) {
        // 使用URL作为默认缓存键
        const key = cacheKey || url;

        // 检查缓存
        if (this.apiCache.has(key)) {
            const cached = this.apiCache.get(key);
            if (Date.now() - cached.timestamp < cacheDuration) {
                console.log(`使用缓存数据: ${key}`);
                return cached.data;
            } else {
                // 缓存过期，删除
                this.apiCache.delete(key);
            }
        }

        // 检查是否有相同的请求正在进行
        if (this.pendingRequests.has(key)) {
            console.log(`等待现有请求完成: ${key}`);
            return await this.pendingRequests.get(key);
        }

        // 创建新请求
        const requestPromise = this._executeApiRequest(url);
        this.pendingRequests.set(key, requestPromise);

        try {
            const result = await requestPromise;

            // 缓存结果
            this.apiCache.set(key, {
                data: result,
                timestamp: Date.now(),
            });

            return result;
        } catch (error) {
            console.error(`API请求失败: ${url}`, error);
            throw error;
        } finally {
            // 清理pending请求
            this.pendingRequests.delete(key);
        }
    }

    // 🔧 新增：强制不使用缓存的API请求方法
    async makeApiRequestNoCache(url) {
        console.log(`🚫 强制不使用缓存请求: ${url}`);

        // 添加时间戳参数防止浏览器缓存
        const separator = url.includes('?') ? '&' : '?';
        const urlWithTimestamp = `${url}${separator}_t=${Date.now()}`;

        try {
            const result = await this._executeApiRequest(urlWithTimestamp);
            console.log(`✅ 无缓存请求完成: ${url}`);
            return result;
        } catch (error) {
            console.error(`❌ 无缓存API请求失败: ${url}`, error);
            throw error;
        }
    }

    // 🔧 新增：实际执行API请求的方法
    async _executeApiRequest(url) {
        const response = await fetch(url);

        if (!response.ok) {
            throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
        }

        const contentType = response.headers.get('content-type');
        let responseText = await response.text();

        // 🔧 修复：检查是否有PHP错误信息在JSON前面
        if (responseText.includes('<br />') && responseText.includes('<b>')) {
            console.warn('检测到PHP警告/错误信息，尝试提取JSON部分');

            // 查找JSON开始位置（第一个{或[）
            const jsonStart = Math.max(responseText.indexOf('{'), responseText.indexOf('['));
            if (jsonStart > 0) {
                const cleanJson = responseText.substring(jsonStart);
                console.log('提取的JSON部分:', cleanJson.substring(0, 100) + '...');
                responseText = cleanJson;
            } else {
                console.error('无法在响应中找到有效的JSON');
                console.error('完整响应:', responseText);
                throw new Error('API返回PHP错误页面，无法找到JSON数据');
            }
        }

        // 🔧 修复：更好的JSON检测
        if (!contentType || !contentType.includes('application/json')) {
            console.warn('API返回非JSON格式:', contentType);
            console.warn('响应内容前200字符:', responseText.substring(0, 200));
        }

        // 🔧 修复：安全的JSON解析
        try {
            return JSON.parse(responseText);
        } catch (jsonError) {
            console.error('JSON解析失败:', jsonError);
            console.error('原始响应内容:', responseText);
            throw new Error(`JSON解析失败: ${jsonError.message}`);
        }
    }

    // 🔧 新增：设置战斗背景图片（使用数据库中的background_image字段）
    setBattleBackground() {
        try {
            console.log('=== 设置战斗背景图片 ===');

            // 🔧 修复：获取正确的容器元素 - battle-ui-container
            const battleContainer = document.querySelector('.battle-ui-container');
            if (!battleContainer) {
                console.error('找不到.battle-ui-container元素');
                return;
            }

            // 检查是否有地图背景配置
            if (this.mapData && this.mapData.background_image) {
                console.log('使用地图背景图片:', this.mapData.background_image);

                // 🔧 修复：使用正确的路径处理
                let backgroundPath;
                if (this.mapData.background_image.startsWith('images/')) {
                    // 数据库存储的相对路径，转换为正确的assets路径
                    backgroundPath = 'assets/' + this.mapData.background_image;
                } else if (this.mapData.background_image.includes('assets/images/')) {
                    // 已经是完整的assets路径
                    backgroundPath = this.mapData.background_image;
                } else {
                    // 纯文件名，默认在battle背景目录
                    backgroundPath = 'assets/images/' + this.mapData.background_image;
                }

                console.log('背景图片路径:', backgroundPath);
                battleContainer.style.backgroundImage = `url('${backgroundPath}')`;
                battleContainer.style.backgroundSize = 'cover';
                battleContainer.style.backgroundPosition = 'center';
                battleContainer.style.backgroundRepeat = 'no-repeat';
            } else {
                // 使用默认背景
                console.log('使用默认战斗背景图片');
                battleContainer.style.backgroundImage = `url('assets/images/battle-bg.jpg')`;
                battleContainer.style.backgroundSize = 'cover';
                battleContainer.style.backgroundPosition = 'center';
                battleContainer.style.backgroundRepeat = 'no-repeat';
            }
        } catch (error) {
            console.error('设置战斗背景图片时出错:', error);
            // 出错时使用默认背景
            const battleContainer = document.querySelector('.battle-ui-container');
            if (battleContainer) {
                battleContainer.style.backgroundImage = `url('assets/images/battle-bg.jpg')`;
                battleContainer.style.backgroundSize = 'cover';
                battleContainer.style.backgroundPosition = 'center';
                battleContainer.style.backgroundRepeat = 'no-repeat';
            }
        }
    }

    // 🔥 新增：验证技能MP消耗
    canUseSkill(skillData, playerStats) {
        try {
            // === 新增：武器耐久判定 ===
            if (skillData.hasWeapon) {
                if (
                    skillData.durability === null ||
                    skillData.durability === undefined ||
                    skillData.maxDurability === null ||
                    skillData.maxDurability === undefined ||
                    skillData.durability <= 0 ||
                    skillData.maxDurability <= 0
                ) {
                    return {
                        canUse: false,
                        reason: 'durability_broken',
                        message: '武器耐久为0或异常，无法使用该技能！',
                    };
                }
            }
            // 检查MP消耗
            const mpCost = skillData.mp_cost || skillData.mpCost || 0;
            const currentMp = playerStats.currentMp || playerStats.mp_bonus || 100;

            console.log(
                `🔍 技能MP检查: 技能消耗${mpCost}MP，当前MP: ${currentMp}，最大MP: ${
                    playerStats.mp_bonus || 100
                }`
            );
            console.log(`🔍 playerStats完整MP信息:`, {
                currentMp: playerStats.currentMp,
                mp_bonus: playerStats.mp_bonus,
                mp: playerStats.mp,
            });

            if (mpCost > 0 && currentMp < mpCost) {
                console.log(`🚫 技能MP不足: 需要${mpCost}MP，当前${currentMp}MP`);
                return {
                    canUse: false,
                    reason: 'mp_insufficient',
                    message: `法力不足，需要${mpCost}点法力`,
                };
            }

            // 检查冷却时间（如果有实现）
            const cooldown = skillData.cooldown_time || skillData.cooldown || 0;
            if (cooldown > 0) {
                // TODO: 实现冷却时间检查
                const skillId = skillData.weaponId || skillData.item_id || skillData.id;
                const lastUsed = this.getSkillLastUsedTime(skillId);
                const now = Date.now();

                if (lastUsed && now - lastUsed < cooldown * 1000) {
                    const remaining = Math.ceil((cooldown * 1000 - (now - lastUsed)) / 1000);
                    console.log(`🚫 技能冷却中: 还需等待${remaining}秒`);
                    return {
                        canUse: false,
                        reason: 'cooldown',
                        message: `技能冷却中，还需${remaining}秒`,
                    };
                }
            }

            console.log(`✅ 技能可以使用`);
            return { canUse: true };
        } catch (error) {
            console.error('验证技能使用失败:', error);
            return { canUse: true }; // 出错时允许使用，避免影响游戏体验
        }
    }

    // 🔥 新增：消耗技能MP
    consumeSkillMp(skillData, playerStats, playerCharacter = null) {
        try {
            const mpCost = skillData.mp_cost || skillData.mpCost || 0;

            if (mpCost > 0) {
                const currentMp = playerStats.currentMp || playerStats.mp_bonus || 100;
                const newMp = Math.max(0, currentMp - mpCost);

                // 更新角色MP数据
                playerStats.currentMp = newMp;

                // 🔥 新增：如果提供了playerCharacter对象，同步更新
                if (playerCharacter && typeof playerCharacter.updateMp === 'function') {
                    playerCharacter.currentMp = newMp;
                    playerCharacter.updateUI();
                    console.log(`🔄 同步更新角色对象MP: ${newMp}`);
                }

                console.log(`🔋 消耗技能MP: ${mpCost}点，当前MP: ${currentMp} -> ${newMp}`);

                // 更新界面显示（如果有MP条）
                this.updateMpDisplay && this.updateMpDisplay(newMp, playerStats.mp_bonus);

                return { success: true, newMp: newMp };
            }

            return { success: true, newMp: playerStats.currentMp };
        } catch (error) {
            console.error('消耗技能MP失败:', error);
            return { success: false };
        }
    }

    // 🔥 新增：记录技能使用时间
    recordSkillUsage(skillData) {
        try {
            const skillId = skillData.weaponId || skillData.item_id || skillData.id;
            const cooldown = skillData.cooldown_time || skillData.cooldown || 0;

            if (cooldown > 0) {
                if (!this.skillCooldowns) {
                    this.skillCooldowns = {};
                }

                this.skillCooldowns[skillId] = Date.now();
                console.log(`⏰ 记录技能使用: 技能${skillId}，冷却${cooldown}秒`);
            }
        } catch (error) {
            console.error('记录技能使用失败:', error);
        }
    }

    // 🔥 新增：获取技能最后使用时间
    getSkillLastUsedTime(skillId) {
        try {
            return this.skillCooldowns && this.skillCooldowns[skillId]
                ? this.skillCooldowns[skillId]
                : null;
        } catch (error) {
            console.error('获取技能使用时间失败:', error);
            return null;
        }
    }

    // 🔥 新增：清除武器数据缓存，用于耐久度变化后立即刷新
    clearWeaponCache() {
        try {
            const weaponCacheKey = 'weapon_slots';
            if (this.apiCache.has(weaponCacheKey)) {
                this.apiCache.delete(weaponCacheKey);
                console.log('🔄 武器缓存已清除，下次加载将获取最新数据');
            }
        } catch (error) {
            console.error('清除武器缓存失败:', error);
        }
    }

    // 🔥 新增：强制重新加载武器数据（跳过缓存）
    async reloadWeaponData() {
        try {
            console.log('🔄 强制重新加载武器数据...');

            // 清除缓存
            this.clearWeaponCache();

            // 重新加载
            await this.loadWeaponData();

            console.log('✅ 武器数据重新加载完成');
        } catch (error) {
            console.error('重新加载武器数据失败:', error);
        }
    }

    // 更新区域进度显示
    async updateAreaProgress() {
        try {
            const areaData = this.getCurrentArea();
            if (areaData && areaData.areaId) {
                const currentStage = await this.getCurrentStage();
                areaData.progress = currentStage;
                localStorage.setItem('areaData', JSON.stringify(areaData));
                console.log(
                    '✅ 同步更新区域进度:',
                    areaData.areaName,
                    `${currentStage}/${areaData.total}关`
                );
            }
        } catch (error) {
            console.error('更新区域进度失败:', error);
        }
    }

    // 🎲 新增：加载奇遇值状态
    async loadAdventureStatus() {
        try {
            const apiUrl = window.GameConfig
                ? window.GameConfig.getApiUrl('adventure_system.php') +
                  '?action=get_adventure_status'
                : '../src/api/adventure_system.php?action=get_adventure_status';
            const response = await fetch(apiUrl, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include', // 包含会话cookie
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();

            if (result.success) {
                this.updateAdventureDisplay(result);
                debugLog('✅ 奇遇值状态加载成功:', result);
            } else {
                // 检查是否是认证错误
                if (result.error === 'auth_required') {
                    debugWarn('⚠️ 用户未登录，奇遇值功能不可用:', result.message);
                    // 显示未登录状态
                    this.updateAdventureDisplay({
                        current_adventure_value: 0,
                        max_adventure_value: 1000,
                        progress_percent: 0,
                        auth_required: true,
                    });
                } else {
                    debugWarn('⚠️ 获取奇遇值状态失败:', result.message);
                    // 即使失败也显示默认状态，不影响战斗
                    this.updateAdventureDisplay({
                        current_adventure_value: 0,
                        max_adventure_value: 1000,
                        progress_percent: 0,
                    });
                }
            }
        } catch (error) {
            debugError('❌ 加载奇遇值状态异常:', error);
            // 网络错误时显示默认状态
            this.updateAdventureDisplay({
                current_adventure_value: 0,
                max_adventure_value: 1000,
                progress_percent: 0,
            });
        }
    }

    // 🎲 新增：更新奇遇值显示
    updateAdventureDisplay(adventureData) {
        try {
            const adventureElement = document.getElementById('adventure-info');
            if (adventureElement) {
                const current =
                    adventureData.current_adventure_value || adventureData.current_value || 0;
                const max = adventureData.max_adventure_value || 1000;
                const percent = adventureData.progress_percent || 0;

                adventureElement.innerHTML = `🎲 奇遇值：${current}/${max} (${percent.toFixed(
                    2
                )}%)`;
                debugLog('✅ 奇遇值显示已更新:', `${current}/${max} (${percent.toFixed(2)}%)`);
            } else {
                debugWarn('⚠️ 未找到奇遇值显示元素');
            }
        } catch (error) {
            debugError('❌ 更新奇遇值显示失败:', error);
        }
    }

    /**
     * 🔧 新增：显示图片缓存统计信息
     */
    showImageCacheStats() {
        if (window.ImagePathManager) {
            const stats = window.ImagePathManager.getCacheStats();
            console.log('📊 图片路径缓存统计:', {
                缓存大小: stats.size,
                缓存项目:
                    stats.entries.length > 10
                        ? `${stats.entries
                              .slice(0, 10)
                              .map(([k, v]) => `${k} -> ${v}`)
                              .join(', ')}...`
                        : stats.entries.map(([k, v]) => `${k} -> ${v}`).join(', '),
            });
        } else {
            console.log('📊 图片路径管理器未初始化');
        }
    }

    /**
     * 🔧 新增：清理图片缓存
     */
    clearImageCache() {
        if (window.ImagePathManager) {
            window.ImagePathManager.clearImageCache();
        }
    }
}

// 导出给全局使用
window.BattleDataManager = BattleDataManager;
