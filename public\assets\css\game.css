        /* 顶部用户信息栏 */
        .user-info-bar {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
            border-radius: 18px;
            padding: 12px 15px;
            margin-bottom: 15px;
            border: 2px solid rgba(212, 175, 55, 0.4);
            backdrop-filter: blur(15px);
            box-shadow: 
                0 8px 25px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .user-avatar {
            width: 60px; /* 增大容器以容纳头像框 */
            height: 60px;
            position: relative; /* 为外框定位做准备 */
            cursor: pointer; /* 添加点击提示 */
        }
        
        /* 头像内容（圆形头像在框内） */
        .user-avatar-content {
            position: absolute;
            top: 6px;
            left: 6px;
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            font-weight: bold;
            color: white;
            box-shadow: 
                0 0 15px rgba(212, 175, 55, 0.6),
                inset 0 0 10px rgba(255, 255, 255, 0.2);
            overflow: hidden; /* 确保图片不会超出圆形边界 */
            z-index: 1;
        }
        
        /* 头像框（四方形，完全显示） */
        .user-avatar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: var(--avatar-frame, url('../images/head/base1.png')); /* 🔧 修复路径：使用相对路径与JS保持一致 */
            background-size: 100% 100%; /* 完全填充容器 */
            background-position: center;
            background-repeat: no-repeat;
            pointer-events: none;
            z-index: 2; /* 确保外框在头像之上 */
        }
        
        /* 当有角色头像时的特殊样式 */
        .user-avatar .user-avatar-content.has-avatar {
            background-size: 200% !important; /* 进一步放大确保头部清晰 */
            background-position: center 15% !important; /* 更精确定位到头部区域 */
        }

        .user-details {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .username {
            font-size: 16px;
            font-weight: bold;
            color: #d4af37;
            text-shadow: 0 0 8px rgba(212, 175, 55, 0.5);
            cursor: pointer;
            /* 去掉下划线 */
            /* text-decoration: underline; */
            transition: all 0.3s ease;
        }
        
        .username:hover {
            color: #ffed4e;
            text-shadow: 0 0 12px rgba(255, 237, 78, 0.7);
            transform: scale(1.02);
        }

        .user-level {
            font-size: 12px;
            color: #bdc3c7;
            transition: color 0.3s ease;
        }

        .realm-text {
            display: inline-block;
            background: linear-gradient(45deg, var(--realm-color-start), var(--realm-color-end));
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
        }

        .realm-icon {
            font-size: 18px;
            margin-right: 4px;
        }

        .user-resources {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .resource-item {
            display: flex;
            align-items: center;
            gap: 5px;
            background: rgba(0, 0, 0, 0.3);
            padding: 6px 10px;
            border-radius: 12px;
            border: 1px solid rgba(212, 175, 55, 0.3);
            backdrop-filter: blur(5px);
        }

        .resource-icon {
            font-size: 14px;
            color: #d4af37;
        }

        .resource-value {
            font-size: 12px;
            font-weight: bold;
            color: #fff;
        }

        /* 游戏主界面 - 简洁设计 */
        .game-world {
            flex: 1;
            position: relative;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.02));
            border-radius: 20px;
            border: 2px solid rgba(212, 175, 55, 0.4);
            backdrop-filter: blur(5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
            overflow: hidden;
            min-height: 500px;
            display: flex;
            flex-direction: column;
            padding: 20px;
        }

        /* 按钮网格容器 */
        .button-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(3, 1fr);
            gap: 35px;          /* ✅ 进一步增加间距：从25px增加到35px */
            flex: 1;
            max-width: 420px;   /* ✅ 进一步增加最大宽度：从350px增加到420px */
            margin: 0 auto;
            justify-items: center;
            align-items: center;
        }

        /* 功能按钮 - 简洁设计 */
        .function-button {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, rgba(212, 175, 55, 0.9), rgba(184, 134, 11, 0.8));
            border: 2px solid rgba(212, 175, 55, 0.8);
            border-radius: 15px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            color: #2c3e50;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .function-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
            background: linear-gradient(135deg, rgba(241, 196, 15, 0.9), rgba(212, 175, 55, 0.8));
            border-color: #f1c40f;
        }

        .function-icon {
            font-size: 24px;
            margin-bottom: 4px;
        }

        .function-text {
            font-size: 11px;
            text-align: center;
            line-height: 1.2;
            font-weight: 600;
        }

        /* 按钮网格位置 - 规整布局 */
        .btn-battle {
            grid-column: 1;
            grid-row: 1;
        }

        .btn-beast {
            grid-column: 2;
            grid-row: 1;
        }

        .btn-dungeon {
            grid-column: 3;
            grid-row: 1;
        }

        .btn-alchemy {
            grid-column: 1;
            grid-row: 2;
        }

        /* 中央修炼台 */
        .cultivation-platform {
            grid-column: 2;
            grid-row: 2;
            width: 90px;
            height: 90px;
            background: radial-gradient(circle, rgba(212, 175, 55, 0.9), rgba(184, 134, 11, 0.7));
            border: 3px solid #d4af37;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 0 20px rgba(212, 175, 55, 0.5);
            transition: all 0.3s ease;
            position: relative;
        }

        .cultivation-platform:hover {
            transform: scale(1.05);
            box-shadow: 0 0 30px rgba(212, 175, 55, 0.7);
        }

        /* 修炼进度环容器 */
        .cultivation-progress-ring {
            position: absolute;
            top: -4.5px;    /* ✅ 修复：精确居中计算 (96-90)/2 + 3px边框 = 7.5px，然后 -4.5px 让圆环居中 */
            left: -4.5px;   /* ✅ 修复：与top保持一致 */
            width: 96px;
            height: 96px;
            transform: rotate(-90deg); /* 从顶部开始 */
            pointer-events: none;
        }

        /* 进度环背景 */
        .cultivation-progress-background {
            fill: none;
            stroke: rgba(255, 255, 255, 0.15);
            stroke-width: 6;
            stroke-linecap: round;
        }

        /* 进度环前景 */
        .cultivation-progress-foreground {
            fill: none;
            stroke: #28db31;
            stroke-width: 6;
            stroke-linecap: round;
            stroke-dasharray: 283; /* 2π × 45 (半径) ≈ 283 */
            stroke-dashoffset: 283; /* 初始为空 */
            transition: stroke-dashoffset 0.8s ease, stroke 0.3s ease, filter 0.3s ease;
            filter: drop-shadow(0 0 8px rgba(212, 175, 55, 0.6));
        }

        /* 进度环呼吸灯效果 */
        .cultivation-progress-foreground {
            animation: cultivation-ring-glow 3s ease-in-out infinite alternate;
        }

        @keyframes cultivation-ring-glow {
            0% {
                filter: drop-shadow(0 0 8px rgba(212, 175, 55, 0.6));
            }
            100% {
                filter: drop-shadow(0 0 15px rgba(212, 175, 55, 0.9)) drop-shadow(0 0 25px rgba(255, 215, 0, 0.5));
            }
        }

        /* 进度满时的强烈发光效果 */
        .cultivation-progress-foreground.full {
            stroke: #ffd700;
            animation: cultivation-glow-full 2s ease-in-out infinite alternate;
        }

        @keyframes cultivation-glow-full {
            0% {
                filter: drop-shadow(0 0 15px #ffd700) drop-shadow(0 0 25px #ffeb3b);
                stroke: #ffd700;
            }
            100% {
                filter: drop-shadow(0 0 25px #ffd700) drop-shadow(0 0 40px #ffeb3b) drop-shadow(0 0 60px #fff);
                stroke: #ffff99;
            }
        }

        /* 修炼台满值时的整体发光 */
        .cultivation-platform.full {
            animation: cultivation-platform-glow 2s ease-in-out infinite alternate;
        }

        @keyframes cultivation-platform-glow {
            0% {
                box-shadow: 0 0 20px rgba(212, 175, 55, 0.5);
            }
            100% {
                box-shadow: 0 0 30px rgba(255, 215, 0, 0.8), 0 0 50px rgba(255, 235, 59, 0.6), 0 0 80px rgba(255, 255, 255, 0.3);
            }
        }

        /* 🆕 魂力受损时的红色进度环效果 */
        .cultivation-progress-foreground.soul-damaged {
            stroke: #e74c3c;
            animation: cultivation-soul-damaged 2s ease-in-out infinite alternate;
        }

        @keyframes cultivation-soul-damaged {
            0% {
                filter: drop-shadow(0 0 15px #e74c3c) drop-shadow(0 0 25px #ff4757);
                stroke: #e74c3c;
            }
            100% {
                filter: drop-shadow(0 0 25px #e74c3c) drop-shadow(0 0 40px #ff4757) drop-shadow(0 0 60px #ff6b6b);
                stroke: #ff6b6b;
            }
        }

        /* 🆕 魂力受损时的修炼台红色发光效果 */
        .cultivation-platform.soul-damaged {
            animation: cultivation-platform-soul-damaged 2s ease-in-out infinite alternate;
        }

        @keyframes cultivation-platform-soul-damaged {
            0% {
                box-shadow: 0 0 20px rgba(231, 76, 60, 0.5);
            }
            100% {
                box-shadow: 0 0 30px rgba(231, 76, 60, 0.8), 0 0 50px rgba(255, 71, 87, 0.6), 0 0 80px rgba(255, 107, 107, 0.4);
            }
        }

        /* 进度文字显示 */
        .cultivation-progress-text {
            position: absolute;
            bottom: -28px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 9px;
            color: #d4af37;
            text-align: center;
            white-space: nowrap;
            text-shadow: 0 0 8px rgba(0, 0, 0, 0.8), 0 0 4px rgba(212, 175, 55, 0.5);
            pointer-events: none;
            font-weight: bold;
        }

        /* 🆕 魂力受损时的进度文字红色样式 */
        .cultivation-platform.soul-damaged .cultivation-progress-text {
            color: #e74c3c;
            text-shadow: 0 0 8px rgba(0, 0, 0, 0.8), 0 0 4px rgba(231, 76, 60, 0.5);
            font-size: 10px; /* 🔧 魂力状态时字体稍小，适应更长的文本 */
        }

        .cultivation-icon {
            font-size: 26px;
            color: #1a3a5c;
            margin-bottom: 4px;
        }

        .cultivation-text {
            font-size: 10px;
            color: #1a3a5c;
            font-weight: 700;
        }

        .btn-spirit {
            grid-column: 3;
            grid-row: 2;
        }

        .btn-shop {
            grid-column: 1;
            grid-row: 3;
        }

        .btn-guild {
            grid-column: 2;
            grid-row: 3;
        }

        .btn-ranking {
            grid-column: 3;
            grid-row: 3;
        }

        /* 特殊按钮颜色 */
        
        /* 论道按钮 - 金色渐变特效 */
        .btn-arena {
            background: linear-gradient(135deg, #ffd700, #ff8c00, #ffd700);
            background-size: 200% 200%;
            animation: arena-glow 3s ease-in-out infinite;
            border: 2px solid #ffd700;
            box-shadow: 
                0 4px 15px rgba(255, 215, 0, 0.3),
                inset 0 2px 0 rgba(255, 255, 255, 0.2);
        }

        .btn-arena:hover {
            background: linear-gradient(135deg, #ffed4e, #ff8c00, #ffed4e);
            box-shadow: 
                0 8px 25px rgba(255, 215, 0, 0.5),
                0 0 30px rgba(255, 215, 0, 0.3),
                inset 0 2px 0 rgba(255, 255, 255, 0.3);
            transform: translateY(-5px) scale(1.05);
        }

        @keyframes arena-glow {
            0%, 100% { 
                background-position: 0% 50%;
                box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
            }
            50% { 
                background-position: 100% 50%;
                box-shadow: 0 4px 20px rgba(255, 215, 0, 0.4);
            }
        }
        
        .btn-battle {
            background: linear-gradient(135deg, rgba(231, 76, 60, 0.9), rgba(192, 57, 43, 0.8));
            border-color: rgba(231, 76, 60, 0.8);
        }

        .btn-battle:hover {
            background: linear-gradient(135deg, rgba(231, 76, 60, 1), rgba(192, 57, 43, 0.9));
        }

        .btn-shop {
            background: linear-gradient(135deg, rgba(52, 152, 219, 0.9), rgba(41, 128, 185, 0.8));
            border-color: rgba(52, 152, 219, 0.8);
        }

        .btn-shop:hover {
            background: linear-gradient(135deg, rgba(52, 152, 219, 1), rgba(41, 128, 185, 0.9));
        }

        .btn-spirit {
            background: linear-gradient(135deg, rgba(46, 204, 113, 0.9), rgba(39, 174, 96, 0.8));
            border-color: rgba(46, 204, 113, 0.8);
        }

        .btn-spirit:hover {
            background: linear-gradient(135deg, rgba(46, 204, 113, 1), rgba(39, 174, 96, 0.9));
        }

        /* 消息提示 */
        .message {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            padding: 12px 20px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            z-index: 1000;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }

        .message.success {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border: 1px solid #4CAF50;
        }

        .message.error {
            background: linear-gradient(135deg, #f44336, #da190b);
            color: white;
            border: 1px solid #f44336;
        }

        .message.info {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            color: white;
            border: 1px solid #2196F3;
        }

        /* 响应式优化 - 网格布局适配 */
        @media (max-width: 480px) {
            .user-info-bar {
                padding: 10px 12px;
                margin-bottom: 12px;
                border-radius: 15px;
            }
            
            .user-avatar {
                width: 55px;
                height: 55px;
            }
            
            .user-avatar-content {
                top: 5px;
                left: 5px;
                width: 45px;
                height: 45px;
                font-size: 16px;
            }
            
            .username {
                font-size: 14px;
            }
            
            .user-level {
                font-size: 11px;
            }
            
            .resource-item {
                padding: 5px 8px;
            }
            
            .resource-icon {
                font-size: 12px;
            }
            
            .resource-value {
                font-size: 11px;
            }
            
            .game-world {
                min-height: 420px;
                padding: 15px;
            }

            .button-grid {
                max-width: 320px;   /* ✅ 进一步增加最大宽度：从280px增加到320px */
                gap: 28px;          /* ✅ 进一步增加间距：从20px增加到28px */
            }
            
            .function-button {
                width: 65px;
                height: 65px;
            }
            
            .function-icon {
                font-size: 20px;
                margin-bottom: 3px;
            }
            
            .function-text {
                font-size: 9px;
            }
            
            .cultivation-platform {
                width: 80px;
                height: 80px;
            }
            
            .cultivation-icon {
                font-size: 22px;
                margin-bottom: 3px;
            }
            
            .cultivation-text {
                font-size: 8px;
            }

            /* 480px移动端进度环适配 */
            .cultivation-progress-ring {
                top: -4px;      /* ✅ 修复：75px按钮，80px圆环，精确居中 */
                left: -4px;     /* ✅ 修复：与top保持一致 */
                width: 80px;
                height: 80px;
            }
            
            .cultivation-progress-background,
            .cultivation-progress-foreground {
                stroke-width: 5;
            }
            
            .cultivation-progress-foreground {
                stroke-dasharray: 236; /* 2π × 37.5 (调整后半径) ≈ 236 */
                stroke-dashoffset: 236;
            }
            
            .cultivation-progress-text {
                font-size: 8px;
                bottom: -22px;
            }
            
            /* 🆕 480px移动端魂力受损文字适配 */
            .cultivation-platform.soul-damaged .cultivation-progress-text {
                font-size: 10px;
            }
        }

        @media (max-width: 375px) {
            .user-resources {
                gap: 10px;
            }
            
            .user-info-bar {
                padding: 8px 10px;
            }
            
            .resource-item {
                padding: 4px 6px;
            }
            
            .resource-value {
                font-size: 10px;
            }
            
            .game-world {
                min-height: 380px;
                padding: 12px;
            }

            .button-grid {
                max-width: 280px;   /* ✅ 进一步增加最大宽度：从250px增加到280px */
                gap: 25px;          /* ✅ 进一步增加间距：从18px增加到25px */
            }
            
            .function-button {
                width: 58px;
                height: 58px;
            }
            
            .function-icon {
                font-size: 18px;
            }
            
            .function-text {
                font-size: 8px;
            }
            
            .cultivation-platform {
                width: 70px;
                height: 70px;
            }
            
            .cultivation-icon {
                font-size: 20px;
            }
            
            .cultivation-text {
                font-size: 7px;
            }

            /* 375px移动端进度环适配 */
            .cultivation-progress-ring {
                top: -3.5px;    /* ✅ 修复：68px按钮，72px圆环，精确居中 */
                left: -3.5px;   /* ✅ 修复：与top保持一致 */
                width: 72px;
                height: 72px;
            }
            
            .cultivation-progress-background,
            .cultivation-progress-foreground {
                stroke-width: 4;
            }
            
            .cultivation-progress-foreground {
                stroke-dasharray: 214; /* 2π × 34 (调整后半径) ≈ 214 */
                stroke-dashoffset: 214;
            }
            
            .cultivation-progress-text {
                font-size: 7px;
                bottom: -20px;
            }
            
            /* 🆕 375px移动端魂力受损文字适配 */
            .cultivation-platform.soul-damaged .cultivation-progress-text {
                font-size: 10px;
            }
        }

        @media (max-width: 320px) {
            .user-info-bar {
                padding: 6px 8px;
                border-radius: 12px;
            }
            
            .user-avatar {
                width: 50px;
                height: 50px;
            }
            
            .user-avatar-content {
                top: 4px;
                left: 4px;
                width: 42px;
                height: 42px;
                font-size: 14px;
            }
            
            .game-world {
                min-height: 350px;
                padding: 10px;
            }

            .button-grid {
                max-width: 250px;   /* ✅ 进一步增加最大宽度：从220px增加到250px */
                gap: 20px;          /* ✅ 进一步增加间距：从15px增加到20px */
            }
            
            .function-button {
                width: 50px;
                height: 50px;
            }
            
            .function-icon {
                font-size: 16px;
            }
            
            .function-text {
                font-size: 7px;
            }
            
            .cultivation-platform {
                width: 60px;
                height: 60px;
            }
            
            .cultivation-icon {
                font-size: 18px;
            }
            
            .cultivation-text {
                font-size: 6px;
            }

            /* 320px移动端进度环适配 */
            .cultivation-progress-ring {
                top: -3px;      /* ✅ 修复：60px按钮，63px圆环，精确居中 */
                left: -3px;     /* ✅ 修复：与top保持一致 */
                width: 63px;
                height: 63px;
            }
            
            .cultivation-progress-background,
            .cultivation-progress-foreground {
                stroke-width: 3;
            }
            
            .cultivation-progress-foreground {
                stroke-dasharray: 189; /* 2π × 30 (调整后半径) ≈ 189 */
                stroke-dashoffset: 189;
            }
            
            .cultivation-progress-text {
                font-size: 6px;
                bottom: -18px;
            }
            
            /* 🆕 320px移动端魂力受损文字适配 */
            .cultivation-platform.soul-damaged .cultivation-progress-text {
                font-size: 8px;
            }
        }

        /* PWA模式下的特殊处理 */
        @media (display-mode: standalone) {
            .game-world {
                min-height: calc(var(--vh, 1vh) * 60);
            }
        }

        /* 更名弹窗样式 */
        .rename-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            backdrop-filter: blur(5px);
        }
        
        .rename-modal {
            background: linear-gradient(135deg, rgba(26, 26, 46, 0.95), rgba(22, 33, 62, 0.95));
            border-radius: 20px;
            padding: 30px;
            width: 90%;
            max-width: 400px;
            border: 2px solid rgba(255, 215, 0, 0.4);
            box-shadow: 
                0 0 30px rgba(255, 215, 0, 0.3),
                inset 0 0 20px rgba(255, 255, 255, 0.1);
            text-align: center;
            color: white;
        }
        
        .rename-title {
            font-size: 24px;
            font-weight: bold;
            color: #ffd700;
            margin-bottom: 20px;
            text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
        }
        
        .rename-current {
            font-size: 16px;
            color: #bdc3c7;
            margin-bottom: 20px;
        }
        
        .rename-form {
            margin-bottom: 20px;
        }
        
        .rename-input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid rgba(255, 215, 0, 0.3);
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.3);
            color: white;
            font-size: 16px;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin-bottom: 15px;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }
        
        .rename-input:focus {
            outline: none;
            border-color: #ffd700;
            box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
        }
        
        .rename-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }
        
        .rename-cost {
            font-size: 14px;
            margin-bottom: 20px;
            padding: 10px;
            border-radius: 8px;
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 215, 0, 0.2);
        }
        
        .rename-cost.free {
            color: #27ae60;
            border-color: rgba(39, 174, 96, 0.3);
        }
        
        .rename-cost.paid {
            color: #e74c3c;
            border-color: rgba(231, 76, 60, 0.3);
        }
        
        .rename-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
        }
        
        .rename-button {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 100px;
        }
        
        .rename-confirm {
            background: linear-gradient(135deg, #27ae60, #219a52);
            color: white;
        }
        
        .rename-confirm:hover {
            background: linear-gradient(135deg, #219a52, #1e8449);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.4);
        }
        
        .rename-confirm:disabled {
            background: linear-gradient(135deg, #666, #555);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .rename-cancel {
            background: linear-gradient(135deg, #666, #888);
            color: white;
        }
        
        .rename-cancel:hover {
            background: linear-gradient(135deg, #777, #999);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
        }
        
        .rename-loading {
            color: #3498db;
        }
        
        @media (max-width: 480px) {
            .rename-modal {
                padding: 20px;
                margin: 20px;
            }
            
            .rename-title {
                font-size: 20px;
            }
            
            .rename-buttons {
                flex-direction: column;
            }
            
            .rename-button {
                min-width: auto;
            }
        }

        /* 头像自定义弹窗样式 */
        .avatar-customization-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1001;
            backdrop-filter: blur(5px);
        }
        
        .avatar-customization-modal {
            background: linear-gradient(135deg, rgba(26, 26, 46, 0.95), rgba(22, 33, 62, 0.95));
            border-radius: 20px;
            padding: 30px;
            width: 90%;
            max-width: 450px;
            max-height: 80vh;
            overflow-y: auto;
            border: 2px solid rgba(255, 215, 0, 0.4);
            box-shadow: 
                0 0 30px rgba(255, 215, 0, 0.3),
                inset 0 0 20px rgba(255, 255, 255, 0.1);
            text-align: center;
            color: white;
        }
        
        .avatar-customization-title {
            font-size: 24px;
            font-weight: bold;
            color: #ffd700;
            margin-bottom: 20px;
            text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
        }
        
        /* 标签页切换 */
        .avatar-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            justify-content: center;
        }
        
        .avatar-tab {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 215, 0, 0.3);
            color: #fff;
            cursor: pointer;
            padding: 10px 20px;
            border-radius: 10px;
            transition: all 0.3s ease;
            font-size: 14px;
        }
        
        .avatar-tab:hover {
            border-color: rgba(255, 215, 0, 0.6);
            transform: translateY(-1px);
        }
        
        .avatar-tab.active {
            background: linear-gradient(135deg, #d4af37, #b8941f);
            color: #1a3a5c;
            border-color: #d4af37;
            box-shadow: 0 0 10px rgba(212, 175, 55, 0.3);
        }
        
        /* 内容面板 */
        .avatar-content-panel {
            margin-bottom: 20px;
        }
        
        .avatar-current {
            font-size: 16px;
            color: #bdc3c7;
            margin-bottom: 15px;
        }
        
        .avatar-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
            gap: 10px;
            max-height: 300px;
            overflow-y: auto;
            padding: 10px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
        }
        
        .avatar-option {
            width: 60px;
            height: 60px;
            border-radius: 10px; /* 外框选项为四方形 */
            border: 2px solid rgba(255, 255, 255, 0.3);
            background-size: 100% 100%;
            background-position: center;
            background-repeat: no-repeat;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .avatar-option:hover {
            transform: scale(1.05);
            border-color: #d4af37;
            box-shadow: 0 0 15px rgba(212, 175, 55, 0.3);
        }
        
        .avatar-option.selected {
            border-color: #d4af37;
            box-shadow: 0 0 15px rgba(212, 175, 55, 0.6);
            transform: scale(1.1);
        }
        
        .avatar-option.locked {
            filter: grayscale(100%) brightness(0.5);
            cursor: not-allowed;
        }
        
        .avatar-option.locked::after {
            content: '🔒';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 16px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* 按钮样式 */
        .avatar-customization-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 20px;
        }
        
        .avatar-customization-button {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 100px;
        }
        
        .avatar-cancel {
            background: linear-gradient(135deg, #666, #888);
            color: white;
        }
        
        .avatar-cancel:hover {
            background: linear-gradient(135deg, #777, #999);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
        }
        
        .avatar-confirm {
            background: linear-gradient(135deg, #27ae60, #219a52);
            color: white;
        }
        
        .avatar-confirm:hover {
            background: linear-gradient(135deg, #219a52, #1e8449);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.4);
        }
        
        .avatar-confirm:disabled {
            background: linear-gradient(135deg, #666, #555);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        @media (max-width: 480px) {
            .avatar-customization-modal {
                padding: 20px;
                width: 95%;
                max-width: 350px;
            }
            
            .avatar-customization-title {
                font-size: 20px;
            }
            
            .avatar-grid {
                gap: 10px;
            }
            
            .avatar-option {
                width: 50px;
                height: 50px;
            }
            
            .avatar-customization-buttons {
                flex-direction: column;
                gap: 10px;
            }
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 4px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(212, 175, 55, 0.5);
            border-radius: 2px;
        }