/**
 * 物品详情弹窗组件
 * 一念修仙游戏 - 通用物品详情显示组件
 * 可在任何界面中使用
 */

class ItemDetailPopup {
    constructor() {
        this.currentItem = null;
        this.currentUser = null;
        this.onEquipCallback = null;
        this.onUnequipCallback = null;
        this.onUseCallback = null;
        this.onRecycleCallback = null;
        this.onRepairCallback = null;

        this.init();
    }

    // 初始化弹窗
    init() {
        // 如果弹窗不存在，创建它
        if (!document.getElementById('item-detail-popup')) {
            this.createPopupHTML();
        }

        // 绑定关闭事件
        this.bindEvents();

        // 🔧 新增：确保遮罩层初始状态是隐藏的
        this.ensureOverlayHidden();
    }

    // 创建弹窗HTML
    createPopupHTML() {
        const popupHTML = `
            <!-- 物品详情弹窗 -->
            <div class="item-detail-popup" id="item-detail-popup">
                <div class="popup-container">
                                    <div class="popup-content-area">
                    <button class="popup-close-button" onclick="ItemDetailPopup.close()">×</button>
                    <div id="popup-content" class="popup-content-inner"></div>
                </div>
                    <div class="popup-button-area" id="popup-button-area">
                        <div class="action-buttons" id="popup-buttons"></div>
                    </div>
                </div>
            </div>

            <!-- 回收确认浮窗 -->
            <div class="recycle-confirm-overlay" id="recycle-confirm-overlay" onclick="ItemDetailPopup.closeRecycleConfirm()"></div>
            <div class="recycle-confirm-popup" id="recycle-confirm-popup">
                <div class="recycle-confirm-header">
                    <h3>回收确认</h3>
                    <button class="close-recycle-popup" onclick="ItemDetailPopup.closeRecycleConfirm()">×</button>
                </div>
                <div class="recycle-confirm-content">
                    <div class="recycle-item-info">
                        <div class="recycle-item-name" id="recycle-item-name">物品名称</div>
                        <div class="recycle-item-details" id="recycle-item-details">物品详情</div>
                    </div>
                    <div class="recycle-quantity-section" id="recycle-quantity-section" style="display: none;">
                        <div class="recycle-quantity-label">回收数量：</div>
                        <div class="recycle-quantity-controls">
                            <button class="quantity-btn" onclick="ItemDetailPopup.adjustRecycleQuantity(-1)">-</button>
                            <input type="number" id="recycle-quantity-input" value="1" min="1" onchange="ItemDetailPopup.updateRecyclePrice()">
                            <button class="quantity-btn" onclick="ItemDetailPopup.adjustRecycleQuantity(1)">+</button>
                            <button class="quantity-max-btn" onclick="ItemDetailPopup.setMaxRecycleQuantity()">全部</button>
                        </div>
                        <div class="quantity-info" id="quantity-info">当前拥有：<span id="current-quantity">1</span> 个</div>
                    </div>
                    <div class="recycle-price-info">
                        <div class="recycle-price-label">回收价格：</div>
                        <div class="recycle-price-value" id="recycle-price-value">0 金币</div>
                        <div class="single-price-info" id="single-price-info" style="display: none;">单价：<span id="single-price">0</span> 金币/个</div>
                    </div>
                    <div class="recycle-warning">
                        ⚠️ 回收后物品将永久消失，无法找回
                    </div>
                </div>
                <div class="recycle-confirm-buttons">
                    <button class="btn-recycle-cancel" onclick="ItemDetailPopup.closeRecycleConfirm()">取消</button>
                    <button class="btn-recycle-confirm" onclick="ItemDetailPopup.confirmRecycle()">确认回收</button>
                </div>
            </div>

            <!-- 修复确认浮窗 -->
            <div class="repair-confirm-overlay" id="repair-confirm-overlay" onclick="ItemDetailPopup.closeRepairConfirm()"></div>
            <div class="repair-confirm-popup" id="repair-confirm-popup">
                <div class="repair-confirm-header">
                    <h3>修复确认</h3>
                    <button class="close-repair-popup" onclick="ItemDetailPopup.closeRepairConfirm()">×</button>
                </div>
                <div class="repair-confirm-content">
                    <div class="repair-item-info">
                        <div class="repair-item-name" id="repair-item-name">武器名称</div>
                        <div class="repair-item-details" id="repair-item-details">武器详情</div>
                    </div>
                    <div class="repair-cost-info">
                        <div class="repair-cost-label">修复费用：</div>
                        <div class="repair-cost-value" id="repair-cost-value">0 金币</div>
                    </div>
                    <div class="repair-warning">
                        🔧 修复后武器耐久度将恢复到最大值
                    </div>
                </div>
                <div class="repair-confirm-buttons">
                    <button class="btn-repair-cancel" onclick="ItemDetailPopup.closeRepairConfirm()">取消</button>
                    <button class="btn-repair-confirm" onclick="ItemDetailPopup.confirmRepair()">确认修复</button>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', popupHTML);
    }

    // 绑定事件
    bindEvents() {
        // ESC键关闭弹窗
        document.addEventListener('keydown', e => {
            if (e.key === 'Escape') {
                this.close();
            }
        });

        // 点击背景关闭弹窗
        const popup = document.getElementById('item-detail-popup');
        if (popup) {
            popup.addEventListener('click', e => {
                if (e.target === popup) {
                    this.close();
                }
            });
        }
    }

    // 🔧 新增：确保遮罩层初始状态是隐藏的
    ensureOverlayHidden() {
        // 延迟执行，确保DOM完全加载
        setTimeout(() => {
            const overlay = document.getElementById('recycle-confirm-overlay');
            const popup = document.getElementById('recycle-confirm-popup');

            if (overlay) {
                // 强制隐藏遮罩层
                overlay.style.display = 'none';
                overlay.style.visibility = 'hidden';
                overlay.style.opacity = '0';
                console.log('🔧 初始化：确保回收确认遮罩层隐藏');
            }

            if (popup) {
                // 强制隐藏弹窗
                popup.style.display = 'none';
                popup.style.visibility = 'hidden';
                popup.style.opacity = '0';
                console.log('🔧 初始化：确保回收确认弹窗隐藏');
            }
        }, 100);
    }

    // 设置用户信息
    setCurrentUser(user) {
        this.currentUser = user;
    }

    // 设置回调函数
    setCallbacks(callbacks = {}) {
        this.onEquipCallback = callbacks.onEquip || null;
        this.onUnequipCallback = callbacks.onUnequip || null;
        this.onUseCallback = callbacks.onUse || null;
        this.onRecycleCallback = callbacks.onRecycle || null;
        this.onRepairCallback = callbacks.onRepair || null;
    }

    // 显示物品详情
    async show(item, options = {}) {
        if (!item) return;

        this.currentItem = item;

        let popup = document.getElementById('item-detail-popup');
        let detailsContainer = document.getElementById('popup-content');
        let buttonsContainer = document.getElementById('popup-buttons');

        if (!popup || !detailsContainer || !buttonsContainer) {
            console.error('物品详情弹窗元素不存在，尝试重新创建...');
            // 🔧 修复：如果HTML模板不存在，重新创建
            this.createPopupHTML();
            this.bindEvents();

            // 重新获取元素
            const retryPopup = document.getElementById('item-detail-popup');
            const retryDetailsContainer = document.getElementById('popup-content');
            const retryButtonsContainer = document.getElementById('popup-buttons');

            if (!retryPopup || !retryDetailsContainer || !retryButtonsContainer) {
                console.error('重新创建HTML模板失败，无法显示物品详情');
                return;
            }

            console.log('✅ HTML模板重新创建成功');

            // 🔧 更新元素引用
            popup = retryPopup;
            detailsContainer = retryDetailsContainer;
            buttonsContainer = retryButtonsContainer;
        }

        // 🔧 修复：先判断丹方，避免与丹药冲突
        const isRecipe =
            item.item_type === 'recipe' ||
            item.slot_type === 'recipe' ||
            (item.name && item.name.includes('丹方'));
        // 🔧 修复：丹药判断要排除丹方和丹炉
        const isPill =
            !isRecipe &&
            (item.item_type === 'pill' ||
                item.slot_type === 'pill' ||
                (item.name &&
                    item.name.includes('丹') &&
                    !item.name.includes('丹方') &&
                    !item.name.includes('丹炉')));

        // 🔧 添加缺失的变量定义 - 修复武器判断逻辑
        const isWeapon = ['sword', 'fan'].includes(item.slot_type);
        const isEquipment = ['ring', 'bracelet', 'chest', 'necklace', 'legs', 'feet'].includes(
            item.slot_type
        );
        const isConsumable = item.item_type === 'consumable' || item.slot_type === 'consumable';

        // 🔧 修复：更精确的战斗界面检测逻辑
        // 只有明确设置了 isBattleContext 标识的才是战斗界面
        const isBattleContext = options.isBattleContext === true;

        // 📋 调试信息
        console.log('🔧 [ButtonLogic] 物品信息:', {
            name: item.name,
            item_type: item.item_type,
            slot_type: item.slot_type,
            isWeapon,
            isEquipment,
            isConsumable,
            isPill,
            isRecipe,
            isBattleContext,
            options,
        });

        // 获取当前角色等级和境界信息
        const currentLevel = this.currentUser ? this.currentUser.level : 1;
        let currentRealm = '凡人';
        let requiredRealm = '凡人';
        let canEquip = true;

        // 🔧 修复：对于已装备的物品或战斗界面，跳过境界判断
        // 🔧 战利品界面需要正确显示境界需求
        if (!item.is_equipped && (!isBattleContext || options.forceShowRealmRequirement)) {
            if (
                this.currentUser &&
                this.currentUser.cultivation_realm &&
                this.currentUser.cultivation_realm.realm_name
            ) {
                currentRealm = this.currentUser.cultivation_realm.realm_name;
            } else if (typeof RealmSystem !== 'undefined') {
                currentRealm = RealmSystem.getLevelRealm(currentLevel);
            }

            // 获取物品需求境界信息
            // 🔧 优先使用realm_requirement字段，如果没有再使用level_requirement
            const itemRequiredLevel = item.realm_requirement || item.level_requirement || 1;

            console.log('🎯 境界需求计算调试:', {
                item_name: item.name,
                realm_requirement: item.realm_requirement,
                level_requirement: item.level_requirement,
                itemRequiredLevel: itemRequiredLevel,
                forceShowRealmRequirement: options.forceShowRealmRequirement,
            });

            try {
                const apiUrl = window.GameConfig
                    ? window.GameConfig.getApiUrl(
                          `cultivation.php?action=get_realm_by_level&level=${itemRequiredLevel}`
                      )
                    : `../src/api/cultivation.php?action=get_realm_by_level&level=${itemRequiredLevel}`;
                const realmResponse = await fetch(apiUrl);
                if (realmResponse.ok) {
                    const realmData = await realmResponse.json();
                    console.log('🎯 境界API返回:', realmData);
                    if (realmData.success && realmData.realm) {
                        requiredRealm = realmData.realm.realm_name;
                    } else if (typeof RealmSystem !== 'undefined') {
                        requiredRealm = RealmSystem.getLevelRealm(itemRequiredLevel);
                    }
                } else if (typeof RealmSystem !== 'undefined') {
                    requiredRealm = RealmSystem.getLevelRealm(itemRequiredLevel);
                }
            } catch (error) {
                console.error('获取境界信息失败:', error);
                if (typeof RealmSystem !== 'undefined') {
                    requiredRealm = RealmSystem.getLevelRealm(itemRequiredLevel);
                }
            }

            // 检查境界需求
            if (
                this.currentUser &&
                this.currentUser.cultivation_realm &&
                this.currentUser.cultivation_realm.level
            ) {
                const currentRealmLevel = this.currentUser.cultivation_realm.level;
                canEquip = currentRealmLevel >= itemRequiredLevel;
            } else {
                canEquip = currentLevel >= itemRequiredLevel;
            }
        } else {
            // 已装备的物品或战斗界面，默认可装备
            canEquip = true;
        }

        // 生成属性列表
        const attributes = await this.generateAttributesList(
            item,
            currentRealm,
            requiredRealm,
            canEquip,
            isBattleContext,
            options
        );

        // 设置内容区域
        detailsContainer.innerHTML = this.generateContentHTML(
            item,
            attributes,
            canEquip,
            isBattleContext
        );

        // 设置按钮区域
        const buttonsHTML = this.generateButtonsHTML(item, canEquip, options);
        buttonsContainer.innerHTML = buttonsHTML;

        // 根据是否有按钮来决定是否显示按钮区域
        const buttonArea = document.getElementById('popup-button-area');
        if (buttonsHTML.trim() === '') {
            buttonArea.classList.add('no-buttons');
        } else {
            buttonArea.classList.remove('no-buttons');
        }

        // 显示弹窗
        popup.style.display = 'flex';

        // 🔥 显示战力值 - 只对装备且非战斗界面显示战力
        // 只在非战斗界面且为装备时显示战力
        if (window.powerRating && (isWeapon || isEquipment) && !isBattleContext) {
            console.log('🔥 准备显示战力值:', {
                isWeapon,
                isEquipment,
                isBattleContext,
                itemId: item.id,
                gameItemId: item.item_id || item.id,
                containerId: `equipment-power-display-${item.id}`,
                powerRatingExists: !!window.powerRating,
            });

            setTimeout(() => {
                // 使用 item_id 而不是 id
                const gameItemId = item.item_id || item.id;
                const containerId = `equipment-power-display-${item.id}`;

                console.log('🔥 调用战力显示:', { gameItemId, containerId });

                // 检查容器是否存在
                const container = document.getElementById(containerId);
                if (!container) {
                    console.error('❌ 战力显示容器不存在:', containerId);
                } else {
                    console.log('✅ 找到战力显示容器:', container);
                }

                window.powerRating.displayEquipmentPower(gameItemId, containerId);
            }, 100);
        } else {
            console.log('🚫 不显示战力值:', {
                powerRatingExists: !!window.powerRating,
                isWeapon,
                isEquipment,
                isBattleContext,
                reason: !window.powerRating
                    ? '战力系统不存在'
                    : isBattleContext
                    ? '战斗界面'
                    : !isWeapon && !isEquipment
                    ? '非装备物品'
                    : '未知原因',
            });
        }
    }

    // 生成属性列表
    async generateAttributesList(
        item,
        currentRealm,
        requiredRealm,
        canEquip,
        isBattleContext = false,
        options = {}
    ) {
        const attributes = [];

        // 🔧 修复：优先使用custom_attributes中的品质信息
        let finalItem = { ...item }; // 复制基础属性

        // 🔧 检查是否有custom_attributes中的品质信息
        if (item.custom_attributes) {
            let customAttrs = null;

            // 如果custom_attributes是字符串，尝试解析
            if (typeof item.custom_attributes === 'string') {
                try {
                    customAttrs = JSON.parse(item.custom_attributes);
                } catch (e) {
                    console.warn('解析custom_attributes失败:', e);
                }
            } else if (typeof item.custom_attributes === 'object') {
                customAttrs = item.custom_attributes;
            }

            // 如果有品质信息，使用它们覆盖基础品质
            if (customAttrs) {
                if (customAttrs.rarity && customAttrs.rarity !== finalItem.rarity) {
                    console.log(
                        '🎯 使用custom_attributes中的品质:',
                        customAttrs.rarity,
                        '(原品质:',
                        finalItem.rarity,
                        ')'
                    );
                    finalItem.rarity = customAttrs.rarity;
                }
                if (customAttrs.rarity_en) {
                    finalItem.rarity_en = customAttrs.rarity_en;
                }
                if (customAttrs.rarity_color) {
                    finalItem.rarity_color = customAttrs.rarity_color;
                }

                // 🔧 新增：调试品质信息
                console.log('🎯 品质处理调试:', {
                    原始品质: item.rarity,
                    custom_attributes品质: customAttrs.rarity,
                    最终品质: finalItem.rarity,
                    品质英文: finalItem.rarity_en,
                    品质颜色: finalItem.rarity_color,
                    完整custom_attributes: customAttrs,
                });
            }
        }

        // 获取物品类型
        const itemType = this.getItemTypeText(finalItem);

        // 基础信息
        if (itemType) {
            attributes.push(
                `<div style="color: #d4af37; font-weight: bold;">类型：${itemType}</div>`
            );
        }
        attributes.push(
            `<div style="color: #d4af37; font-weight: bold;">品质：<span class="rarity-${
                finalItem.rarity
            }">${this.getRarityText(finalItem.rarity)}</span></div>`
        );

        // 🔧 修复：战斗界面和已装备物品不显示境界需求
        // 🔧 新增：对于丹方、属性丹、材料、消耗品等非装备物品也不显示境界需求
        // 🔧 新增：强制显示境界需求的选项
        const isNonEquipmentItem = this.isNonEquipmentItem(finalItem);
        const forceShowRealmRequirement = options.forceShowRealmRequirement || false;

        if (
            (!isBattleContext && !finalItem.is_equipped && !isNonEquipmentItem) ||
            forceShowRealmRequirement
        ) {
            // 境界需求
            console.log('🎯 显示境界需求:', {
                isBattleContext,
                is_equipped: finalItem.is_equipped,
                isNonEquipmentItem,
                forceShowRealmRequirement,
                requiredRealm,
                currentRealm,
                slot_type: finalItem.slot_type,
                item_type: finalItem.item_type,
                name: finalItem.name,
            });

            const realmRequirementStyle = canEquip
                ? 'color: #27ae60;'
                : 'color: #e74c3c; font-weight: bold;';
            attributes.push(
                `<div style="${realmRequirementStyle}">境界需求：${requiredRealm}</div>`
            );

            if (!canEquip) {
                attributes.push(
                    `<div style="color: #f39c12; font-size: 11px;">当前境界：${currentRealm}</div>`
                );
            }
        }

        // 战斗属性（传递finalItem而不是item）
        this.addCombatAttributes(attributes, finalItem);

        // 🔧 新增：套装信息显示
        await this.addSetInformation(attributes, finalItem);

        return attributes;
    }

    // 添加战斗属性
    addCombatAttributes(attributes, item) {
        // 🔧 修复：对于材料类物品，不显示战斗属性，显示其他有用信息
        const isMaterial = item.item_type === 'material' || item.slot_type === 'material';
        const isConsumable = item.item_type === 'consumable' || item.slot_type === 'consumable';

        // 🔧 新增：检查是否为丹炉等特殊工具
        const isSpecialTool =
            item.name &&
            (item.name.includes('丹炉') ||
                item.name.includes('炼丹') ||
                item.name.includes('工具'));

        // 🔧 新增：检查是否可以回收（用于判断是否显示出售价格）
        const canRecycle = !item.is_equipped && !isSpecialTool;

        if (isMaterial && !isSpecialTool) {
            // 材料类物品显示材料相关信息（排除特殊工具）
            if (canRecycle && item.sell_price && item.sell_price > 0) {
                attributes.push(
                    `<div style="color: #f1c40f;">出售价格：${item.sell_price} 金币</div>`
                );
            }

            if (item.quantity && item.quantity > 1) {
                attributes.push(`<div style="color: #3498db;">数量：${item.quantity}</div>`);
            }

            // 🔧 移除：不显示特殊用途，因为通常是技术性字段

            // 材料品质说明
            const rarityText = this.getRarityText(item.rarity);
            if (rarityText !== '普通') {
                attributes.push(
                    `<div style="color: #e67e22;">品质加成：${rarityText}品质材料</div>`
                );
            }

            return; // 材料类物品不显示战斗属性
        }

        // 🔧 新增：特殊工具（如丹炉）单独处理
        if (isSpecialTool) {
            // 丹炉等工具只显示基本信息，不显示出售价格和特殊用途
            if (item.quantity && item.quantity > 1) {
                attributes.push(`<div style="color: #3498db;">数量：${item.quantity}</div>`);
            }

            // 工具品质说明
            const rarityText = this.getRarityText(item.rarity);
            if (rarityText !== '普通') {
                attributes.push(`<div style="color: #e67e22;">品质等级：${rarityText}</div>`);
            }

            return; // 特殊工具不显示其他属性
        }

        if (isConsumable) {
            // 消耗品显示消耗品相关信息
            if (canRecycle && item.sell_price && item.sell_price > 0) {
                attributes.push(
                    `<div style="color: #f1c40f;">出售价格：${item.sell_price} 金币</div>`
                );
            }

            if (item.quantity && item.quantity > 1) {
                attributes.push(`<div style="color: #3498db;">数量：${item.quantity}</div>`);
            }

            // 消耗品效果
            if (item.hp_bonus && item.hp_bonus > 0) {
                attributes.push(`<div style="color: #e74c3c;">恢复生命：+${item.hp_bonus}</div>`);
            }
            if (item.mp_bonus && item.mp_bonus > 0) {
                attributes.push(`<div style="color: #9b59b6;">恢复法力：+${item.mp_bonus}</div>`);
            }

            return; // 消耗品不显示其他战斗属性
        }

        // 🔧 只有装备类物品才显示战斗属性
        const isEquipment =
            [
                'weapon',
                'armor',
                'accessory',
                'sword',
                'fan',
                'ring',
                'bracelet',
                'chest',
                'necklace',
                'legs',
                'feet',
            ].includes(item.slot_type) || ['weapon', 'armor', 'accessory'].includes(item.item_type);

        if (!isEquipment) {
            // 非装备物品显示基本信息
            if (canRecycle && item.sell_price && item.sell_price > 0) {
                attributes.push(
                    `<div style="color: #f1c40f;">出售价格：${item.sell_price} 金币</div>`
                );
            }
            return;
        }

        // 🔧 修复：优先使用custom_attributes中的calculated_attributes
        let finalAttributes = { ...item }; // 复制基础属性

        // 🔧 检查是否有custom_attributes.calculated_attributes
        if (item.custom_attributes) {
            let customAttrs = null;

            // 如果custom_attributes是字符串，尝试解析
            if (typeof item.custom_attributes === 'string') {
                try {
                    customAttrs = JSON.parse(item.custom_attributes);
                } catch (e) {
                    console.warn('解析custom_attributes失败:', e);
                }
            } else if (typeof item.custom_attributes === 'object') {
                customAttrs = item.custom_attributes;
            }

            // 如果有calculated_attributes，使用它们覆盖基础属性
            if (customAttrs && customAttrs.calculated_attributes) {
                console.log('🎯 使用calculated_attributes:', customAttrs.calculated_attributes);
                Object.assign(finalAttributes, customAttrs.calculated_attributes);

                console.log('🎯 最终属性:', {
                    physical_attack: finalAttributes.physical_attack,
                    immortal_attack: finalAttributes.immortal_attack,
                    physical_defense: finalAttributes.physical_defense,
                    immortal_defense: finalAttributes.immortal_defense,
                });
            }
        }

        // 以下是装备类物品的战斗属性显示逻辑（使用finalAttributes而不是item）

        // 🔧 补全：物理攻击力（优先使用新字段）
        if (finalAttributes.physical_attack && finalAttributes.physical_attack > 0) {
            attributes.push(
                `<div style="color: #e74c3c;">物理攻击力：+${finalAttributes.physical_attack}</div>`
            );
        }

        // 🔧 补全：法术攻击力（使用immortal_attack字段）
        if (finalAttributes.immortal_attack && finalAttributes.immortal_attack > 0) {
            attributes.push(
                `<div style="color: #9b59b6;">法术攻击力：+${finalAttributes.immortal_attack}</div>`
            );
        }

        // 🔧 补全：物理防御力（优先使用新字段）
        if (finalAttributes.physical_defense && finalAttributes.physical_defense > 0) {
            attributes.push(
                `<div style="color: #3498db;">物理防御力：+${finalAttributes.physical_defense}</div>`
            );
        }

        // 🔧 补全：法术防御力（使用immortal_defense字段）
        if (finalAttributes.immortal_defense && finalAttributes.immortal_defense > 0) {
            attributes.push(
                `<div style="color: #8e44ad;">法术防御力：+${finalAttributes.immortal_defense}</div>`
            );
        }

        // 🔧 补全：基础属性加成
        if (finalAttributes.hp_bonus && finalAttributes.hp_bonus > 0) {
            attributes.push(
                `<div style="color: #e67e22;">生命值：+${finalAttributes.hp_bonus}</div>`
            );
        }
        if (finalAttributes.mp_bonus && finalAttributes.mp_bonus > 0) {
            attributes.push(
                `<div style="color: #9b59b6;">魔法值：+${finalAttributes.mp_bonus}</div>`
            );
        }
        if (finalAttributes.speed_bonus && finalAttributes.speed_bonus > 0) {
            attributes.push(
                `<div style="color: #1abc9c;">速度：+${finalAttributes.speed_bonus}</div>`
            );
        }

        // 🔧 修复：暴击率是整数数值，不是百分比
        if (finalAttributes.critical_bonus && parseFloat(finalAttributes.critical_bonus) > 0) {
            const critical_bonus = parseFloat(finalAttributes.critical_bonus);
            attributes.push(
                `<div style="color: #f39c12;">暴击率：+${Math.round(critical_bonus)}</div>`
            );
        }
        if (finalAttributes.critical_damage && parseFloat(finalAttributes.critical_damage) > 0) {
            const criticalDamage = parseFloat(finalAttributes.critical_damage);
            // 🔧 修复：统一处理小数格式，直接乘以100转为百分比显示
            const displayValue = (criticalDamage * 100).toFixed(1);
            attributes.push(`<div style="color: #e67e22;">暴击伤害：+${displayValue}%</div>`);
        }
        if (finalAttributes.accuracy_bonus && parseFloat(finalAttributes.accuracy_bonus) > 0) {
            const accuracyBonus = parseFloat(finalAttributes.accuracy_bonus);
            attributes.push(
                `<div style="color: #27ae60;">命中率：+${Math.round(accuracyBonus)}</div>`
            );
        }
        if (finalAttributes.dodge_bonus && parseFloat(finalAttributes.dodge_bonus) > 0) {
            const dodgeBonus = parseFloat(finalAttributes.dodge_bonus);
            attributes.push(
                `<div style="color: #8e44ad;">闪避率：+${Math.round(dodgeBonus)}</div>`
            );
        }
        if (finalAttributes.block_bonus && parseFloat(finalAttributes.block_bonus) > 0) {
            const blockBonus = parseFloat(finalAttributes.block_bonus);
            attributes.push(
                `<div style="color: #34495e;">格挡加成：+${Math.round(blockBonus)}</div>`
            );
        }
        if (
            finalAttributes.critical_resistance &&
            parseFloat(finalAttributes.critical_resistance) > 0
        ) {
            const critical_resistance = parseFloat(finalAttributes.critical_resistance);
            // 如果值小于1，认为是小数形式，需要转换为百分比
            const displayValue =
                critical_resistance < 1
                    ? (critical_resistance * 100).toFixed(1)
                    : critical_resistance.toFixed(1);
            attributes.push(`<div style="color: #e67e22;">免暴率：+${displayValue}%</div>`);
        }

        // 移除不存在字段的显示：resistance_bonus, penetration_bonus, cooldown_reduction, exp_bonus
    }

    // 🔧 新增：添加套装信息
    async addSetInformation(attributes, item) {
        console.log('🔧 [SetInfo] 开始添加套装信息:', {
            name: item.name,
            item_type: item.item_type,
            slot_type: item.slot_type,
            set_id: item.set_id,
            set_name: item.set_name,
        });

        // 只有装备类物品才显示套装信息
        const isEquipment =
            [
                'weapon',
                'armor',
                'accessory',
                'sword',
                'fan',
                'ring',
                'bracelet',
                'chest',
                'necklace',
                'legs',
                'feet',
            ].includes(item.slot_type) || ['weapon', 'armor', 'accessory'].includes(item.item_type);

        if (!isEquipment) {
            return;
        }

        try {
            // 获取物品的套装信息
            // 优先使用 item_id (游戏物品ID)，fallback 到 id
            const gameItemId = item.item_id || item.game_item_id || item.id;
            console.log(
                '🔧 [SetInfo] 调用getItemSetInfo，物品ID:',
                gameItemId,
                '(item.item_id:',
                item.item_id,
                ', item.id:',
                item.id,
                ')'
            );
            const setInfo = await this.getItemSetInfo(item);
            console.log('🔧 [SetInfo] API返回结果:', setInfo);

            if (setInfo && setInfo.success && setInfo.set_info) {
                const setData = setInfo.set_info;
                const equippedCount = setInfo.equipped_count || 0;
                const totalPieces = setInfo.total_pieces || 0;

                // 构建套装信息HTML
                let setHTML = `<div style="color: #d4b174; font-weight: bold; margin-top: 8px;">`;
                setHTML += `${setData.set_name} (${equippedCount}/${totalPieces})`;
                setHTML += `</div>`;

                // 显示套装效果
                if (setData.effects) {
                    let effects = setData.effects;
                    if (typeof effects === 'string') {
                        try {
                            effects = JSON.parse(effects);
                        } catch (e) {
                            console.warn('解析套装效果失败:', e);
                            effects = {};
                        }
                    }

                    // 显示2件套效果
                    if (effects.two_piece) {
                        const isActive = equippedCount >= 2;
                        const color = isActive ? '#1eff00' : '#666';
                        setHTML += `<div style="color: ${color}; font-size: 12px; margin-left: 10px;">`;
                        setHTML += `(2) ${this.formatSetEffect(effects.two_piece)}`;
                        setHTML += `</div>`;
                    }

                    // 显示4件套效果
                    if (effects.four_piece) {
                        const isActive = equippedCount >= 4;
                        const color = isActive ? '#1eff00' : '#666';
                        setHTML += `<div style="color: ${color}; font-size: 12px; margin-left: 10px;">`;
                        setHTML += `(4) ${this.formatSetEffect(effects.four_piece)}`;
                        setHTML += `</div>`;
                    }

                    // 显示6件套效果
                    if (effects.six_piece) {
                        const isActive = equippedCount >= 6;
                        const color = isActive ? '#1eff00' : '#666';
                        setHTML += `<div style="color: ${color}; font-size: 12px; margin-left: 10px;">`;
                        setHTML += `(6) ${this.formatSetEffect(effects.six_piece)}`;
                        setHTML += `</div>`;
                    }
                }

                attributes.push(setHTML);
            }
        } catch (error) {
            console.warn('获取套装信息失败:', error);
        }
    }

    // 🔧 新增：获取物品套装信息
    async getItemSetInfo(item) {
        try {
            // 首先获取物品的set_id
            const response = await fetch(
                window.GameConfig
                    ? window.GameConfig.getApiUrl('equipment_integrated.php')
                    : './src/api/equipment_integrated.php',
                {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `action=get_item_set_info&item_id=${
                        item.item_id || item.game_item_id || item.id
                    }`,
                }
            );

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const data = await response.json();
            return data;
        } catch (error) {
            console.warn('获取套装信息失败:', error);
            return null;
        }
    }

    // 🔧 新增：格式化套装效果显示
    formatSetEffect(effect) {
        if (typeof effect === 'string') {
            return effect;
        }

        if (typeof effect === 'object') {
            const parts = [];
            for (const [key, value] of Object.entries(effect)) {
                if (key === 'special_effect') {
                    parts.push(value);
                } else {
                    const attrName = this.getAttributeDisplayName(key);
                    parts.push(`${attrName}+${value}`);
                }
            }
            return parts.join(', ');
        }

        return '未知效果';
    }

    // 🔧 新增：获取属性显示名称
    getAttributeDisplayName(key) {
        const nameMap = {
            physical_attack: '物攻',
            immortal_attack: '仙攻',
            physical_defense: '物防',
            immortal_defense: '仙防',
            hp_bonus: '生命',
            mp_bonus: '法力',
            speed_bonus: '速度',
            critical_bonus: '暴击率',
            critical_damage: '暴击伤害',
            accuracy_bonus: '命中',
            dodge_bonus: '闪避',
        };
        return nameMap[key] || key;
    }

    // 生成内容HTML
    generateContentHTML(item, attributes, canEquip, isBattleContext = false) {
        const isWeapon = ['weapon', 'sword', 'fan'].includes(item.slot_type);
        const isEquipment = ['ring', 'bracelet', 'chest', 'necklace', 'legs', 'feet'].includes(
            item.slot_type
        );

        // 🔧 修复图片显示逻辑 - 如果没有有效图片URL则不显示图片区域
        let imageHTML = '';
        const imageUrl = this.getItemImageUrl(item);

        // 只有当有有效的图片URL时才显示图片区域
        if (imageUrl && imageUrl !== 'assets/images/1100.png') {
            // 只对装备且非战斗界面显示战力容器
            const powerDisplay =
                (isWeapon || isEquipment) && !isBattleContext
                    ? `
                <div id="equipment-power-display-${item.id}" class="power-rating-display" style="margin-top: 8px; text-align: center;">
                    战力值：计算中...
                </div>
            `
                    : '';

            imageHTML = `
                <div style="text-align: center; margin-bottom: 12px;">
                    <img src="${imageUrl}" 
                         alt="${item.name}" 
                         style="width: 70px; height: 70px; border-radius: 12px; border: 3px solid #d4af37; background: linear-gradient(135deg, rgba(0, 0, 0, 0.5), rgba(212, 175, 55, 0.1)); object-fit: cover; box-shadow: 0 8px 20px rgba(0, 0, 0, 0.5), inset 0 1px 0 rgba(255, 255, 255, 0.1);"
                         onerror="this.style.display='none'; this.parentElement.style.display='none';">
                    ${powerDisplay}
                </div>
            `;
        } else {
            // 即使没有图片，也只对装备且非战斗界面显示战力
            if ((isWeapon || isEquipment) && !isBattleContext) {
                imageHTML = `
                    <div style="text-align: center; margin-bottom: 12px;">
                        <div id="equipment-power-display-${item.id}" class="power-rating-display">
                            战力值：计算中...
                        </div>
                    </div>
                `;
            }
        }

        return `
            <h3 style="color: #d4af37; margin: 0 0 12px 0; font-size: 18px; text-align: center; text-shadow: 0 0 15px rgba(212, 175, 55, 0.8); font-weight: bold; letter-spacing: 1px; padding: 8px 0; background: linear-gradient(135deg, rgba(212, 175, 55, 0.1), rgba(212, 175, 55, 0.05)); border-radius: 12px; border: 1px solid rgba(212, 175, 55, 0.3);">${
                item.name
            }</h3>
            
            ${imageHTML}
            
            <p style="color: #bdc3c7; font-size: 13px; margin: 0 0 12px 0; line-height: 1.3; text-align: center; font-style: italic; background: rgba(0, 0, 0, 0.2); padding: 8px 12px; border-radius: 10px; border: 1px solid rgba(255, 255, 255, 0.1);">${
                item.description || '暂无描述'
            }</p>
            
            ${
                !canEquip && !this.isNonEquipmentItem(item)
                    ? `
                <div style="background: rgba(231, 76, 60, 0.2); padding: 10px 14px; border-radius: 10px; margin-bottom: 12px; border: 1px solid rgba(231, 76, 60, 0.5);">
                    <div style="color: #e74c3c; font-size: 13px; text-align: center; font-weight: bold;">⚠️ 境界不足，无法装备</div>
                </div>
            `
                    : ''
            }
            
            <div style="background: linear-gradient(135deg, rgba(0, 0, 0, 0.3), rgba(26, 35, 50, 0.6)); padding: 12px 16px; border-radius: 12px; margin-bottom: 10px; border: 1px solid rgba(212, 175, 55, 0.2); box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.05), 0 4px 12px rgba(0, 0, 0, 0.3);">
                ${attributes
                    .map(
                        attr =>
                            `<div style="margin: 4px 0; font-size: 13px; line-height: 1.4;">${attr}</div>`
                    )
                    .join('')}
            </div>
            
            ${
                isWeapon && item.current_durability !== undefined
                    ? `
                <div style="margin: 6px 0; padding: 0; background: none; border-radius: 0; border: none; position: relative;">
                    <div style="background: rgba(0, 0, 0, 0.6); height: 14px; border-radius: 7px; overflow: visible; border: 1px solid rgba(255, 255, 255, 0.2); box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.4); position: relative;" data-durability="${
                        item.current_durability
                    }/${item.max_durability}">
                        <div style="height: 100%; background: linear-gradient(90deg, #e74c3c, #f39c12, #27ae60); width: ${this.getDurabilityPercent(
                            item
                        )}%; border-radius: 6px; box-shadow: 0 0 8px rgba(255, 255, 255, 0.4);"></div>
                    </div>
                </div>
            `
                    : ''
            }
            
            ${
                item.skill_name
                    ? `
                <div style="margin: 6px 0; padding: 6px 10px; background: linear-gradient(135deg, rgba(212, 175, 55, 0.15), rgba(26, 35, 50, 0.6)); border-radius: 8px; border: 1px solid rgba(212, 175, 55, 0.3); box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.05), 0 0 20px rgba(212, 175, 55, 0.1);">
                    <div style="font-weight: bold; color: #d4af37; font-size: 12px; margin-bottom: 4px; text-shadow: 0 0 8px rgba(212, 175, 55, 0.8); text-align: center; padding-bottom: 3px; border-bottom: 1px solid rgba(212, 175, 55, 0.2);">武器技能：${
                        item.skill_name
                    }</div>
                    <div style="font-size: 10px; color: #ecf0f1; line-height: 1.3; display: flex; flex-wrap: wrap; gap: 8px; justify-content: center;">
                        <div style="margin: 0; padding: 0; background: none; border: none; border-radius: 0; flex: none;">伤害倍数：${(
                            item.damage_multiplier || 1.0
                        ).toFixed(2)}x</div>
                        ${
                            item.element_type
                                ? `<div style="margin: 0; padding: 0; background: none; border: none; border-radius: 0; flex: none; color: ${this.getElementColor(
                                      item.element_type
                                  )};">五行属性：${this.getElementText(item.element_type)}</div>`
                                : ''
                        }
                        ${
                            item.mp_cost && item.mp_cost > 0
                                ? `<div style="margin: 0; padding: 0; background: none; border: none; border-radius: 0; flex: none; color: #3498db;">消耗法力：${item.mp_cost}</div>`
                                : ''
                        }
                    </div>
                </div>
            `
                    : ''
            }
            
            ${this.getSpecialEffectsDisplay(item)}
        `;
    }

    // 🔧 完善按钮生成逻辑 - 智能判断物品状态并生成相应按钮
    generateButtonsHTML(item, canEquip, options = {}) {
        const buttons = [];
        const isWeapon = ['weapon', 'sword', 'fan'].includes(item.slot_type);
        const isEquipment = ['ring', 'bracelet', 'chest', 'necklace', 'legs', 'feet'].includes(
            item.slot_type
        );
        const isConsumable = item.item_type === 'consumable' || item.slot_type === 'consumable';
        const isMaterial =
            item.item_type === 'material' ||
            item.slot_type === 'material' ||
            (item.name &&
                (item.name.includes('草') ||
                    item.name.includes('花') ||
                    item.name.includes('根') ||
                    item.name.includes('叶') ||
                    item.name.includes('果') ||
                    item.name.includes('石')));

        // 🔧 修复：先判断丹方，避免与丹药冲突
        const isRecipe =
            item.item_type === 'recipe' ||
            item.slot_type === 'recipe' ||
            (item.name && item.name.includes('丹方'));
        // 🔧 修复：丹药判断要排除丹方和丹炉
        const isPill =
            !isRecipe &&
            (item.item_type === 'pill' ||
                item.slot_type === 'pill' ||
                (item.name &&
                    item.name.includes('丹') &&
                    !item.name.includes('丹方') &&
                    !item.name.includes('丹炉')));

        // 🔧 修复：更精确的战斗界面检测逻辑
        // 只有明确设置了 isBattleContext 标识的才是战斗界面
        const isBattleContext = options.isBattleContext === true;

        // 📋 调试信息
        console.log('🔧 [ButtonLogic] 物品信息:', {
            name: item.name,
            item_type: item.item_type,
            slot_type: item.slot_type,
            isWeapon,
            isEquipment,
            isConsumable,
            isMaterial,
            isPill,
            isRecipe,
            isBattleContext,
            options,
        });

        // 🔧 修复：战斗界面中不显示任何按钮（删除关闭按钮）
        if (isBattleContext) {
            console.log('🔧 [ButtonLogic] 战斗界面 - 不显示按钮');
            return '';
        }

        // 🎯 智能按钮逻辑（非战斗界面）
        // 1. 装备相关按钮
        if (isWeapon || isEquipment) {
            if (options.showUnequip || item.is_equipped) {
                // 已装备状态 - 显示卸下按钮
                const buttonText = isWeapon ? '卸下武器' : '卸下装备';
                buttons.push(
                    `<button class="btn btn-unequip" onclick="ItemDetailPopup.handleUnequip()">${buttonText}</button>`
                );
            } else if ((options.showEquip || !item.is_equipped) && canEquip) {
                // 未装备状态且可装备 - 显示装备按钮
                const buttonText = isWeapon ? '装备武器' : '装备';
                buttons.push(
                    `<button class="btn btn-equip" onclick="ItemDetailPopup.handleEquip()">${buttonText}</button>`
                );
            }
        }

        // 2. 丹方学习按钮（优先级高于一般使用按钮）
        if (isRecipe) {
            if (options.showUse !== false) {
                buttons.push(
                    `<button class="btn btn-use" onclick="ItemDetailPopup.handleUse()">学习丹方</button>`
                );
            }
        }
        // 3. 使用相关按钮（排除丹方）
        else if (isPill || isConsumable) {
            // 🚫 渡劫丹和养魂丹不能在背包中使用，直接不显示使用按钮
            const isTribulationPill =
                item.name && (item.name.includes('渡劫丹') || item.name.includes('养魂丹'));

            if (options.showUse !== false && !isTribulationPill) {
                // 默认显示使用按钮，除非明确设置为false或是渡劫丹/养魂丹
                buttons.push(
                    `<button class="btn btn-use" onclick="ItemDetailPopup.handleUse()">使用</button>`
                );
            }
            // 渡劫丹和养魂丹不显示任何使用相关按钮
        }

        // 4. 修复按钮
        if (
            isWeapon &&
            item.current_durability !== undefined &&
            item.max_durability !== undefined
        ) {
            if (options.showRepair) {
                buttons.push(
                    `<button class="btn btn-repair" onclick="ItemDetailPopup.handleRepair()">修复武器</button>`
                );
            }
        }

        // 5. 回收按钮 - 🔧 修复：支持所有可回收物品类型
        const canRecycle =
            (isWeapon || isEquipment || isConsumable || isMaterial || isPill) &&
            !item.is_equipped &&
            !options.showUnequip &&
            (options.showRecycle || !options.hideRecycle);

        if (canRecycle) {
            // 武器、装备、消耗品、材料、丹药都可以回收
            buttons.push(
                `<button class="btn btn-recycle" onclick="ItemDetailPopup.handleRecycle()">回收</button>`
            );
        }

        // 6. 删除关闭按钮（按照要求）

        console.log('🔧 [ButtonLogic] 生成的按钮:', buttons);

        return buttons.join('');
    }

    // 处理装备
    handleEquip() {
        if (this.onEquipCallback && this.currentItem) {
            this.onEquipCallback(this.currentItem);
        }
        this.close();
    }

    // 处理卸下
    handleUnequip() {
        if (this.onUnequipCallback && this.currentItem) {
            this.onUnequipCallback(this.currentItem);
        }
        this.close();
    }

    // 处理使用
    handleUse() {
        if (this.onUseCallback && this.currentItem) {
            this.onUseCallback(this.currentItem);
        }
        this.close();
    }

    // 处理修复
    handleRepair() {
        if (!this.currentItem) return;

        // 计算修复费用
        const repairCost = this.calculateRepairCost(this.currentItem);
        this.showRepairConfirm(this.currentItem, repairCost);
    }

    // 计算修复费用
    calculateRepairCost(item) {
        if (!item.current_durability || !item.max_durability) return 0;

        // 需要修复的耐久点数
        const durabilityToRepair = item.max_durability - item.current_durability;

        // 修复费用 = 需要修复的耐久点数 × 100金币
        const repairCost = durabilityToRepair * 100;

        // 确保至少1金币
        return Math.max(1, repairCost);
    }

    // 显示修复确认
    showRepairConfirm(item, repairCost) {
        document.getElementById('repair-item-name').textContent = item.name;

        const details = [];
        details.push(`品质：${this.getRarityText(item.rarity)}`);
        details.push(`等级：${item.level_requirement}`);
        details.push(`当前耐久：${item.current_durability}/${item.max_durability}`);

        document.getElementById('repair-item-details').textContent = details.join(' | ');
        document.getElementById('repair-cost-value').textContent = `${repairCost} 金币`;

        const overlay = document.getElementById('repair-confirm-overlay');
        const popup = document.getElementById('repair-confirm-popup');

        if (overlay && popup) {
            const isMobile = window.innerWidth <= 768;
            const isSmallScreen = window.innerWidth <= 480;

            overlay.removeAttribute('style');
            popup.removeAttribute('style');

            overlay.style.cssText = `
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                bottom: 0 !important;
                width: 100vw !important;
                height: 100vh !important;
                background: rgba(0, 0, 0, ${isMobile ? '0.8' : '0.85'}) !important;
                z-index: 15000 !important;
                display: block !important;
                overflow: hidden !important;
            `;

            const popupWidth = isSmallScreen ? '98%' : isMobile ? '95%' : '90%';
            const popupMaxWidth = isSmallScreen ? '280px' : isMobile ? '320px' : '350px';

            popup.style.cssText = `
                position: fixed !important;
                top: 50% !important;
                left: 50% !important;
                transform: translate(-50%, -50%) !important;
                background: linear-gradient(145deg, #2c3e50, #34495e) !important;
                border-radius: ${isMobile ? '12px' : '16px'} !important;
                width: ${popupWidth} !important;
                max-width: ${popupMaxWidth} !important;
                border: 2px solid #f39c12 !important;
                z-index: 15001 !important;
                display: block !important;
                box-shadow: 0 10px 30px rgba(0, 0, 0, ${isMobile ? '0.6' : '0.5'}) !important;
                max-height: 90vh !important;
                overflow-y: auto !important;
                margin: 0 !important;
            `;

            overlay.offsetHeight;
            popup.offsetHeight;

            console.log('🔧 修复确认弹窗已显示');
        }

        window.currentRepairCost = repairCost;
    }

    // 确认修复
    confirmRepair() {
        if (this.onRepairCallback && this.currentItem) {
            this.onRepairCallback(this.currentItem, window.currentRepairCost);
        }
        this.closeRepairConfirm();
        this.close();
    }

    // 关闭修复确认
    closeRepairConfirm() {
        const overlay = document.getElementById('repair-confirm-overlay');
        const popup = document.getElementById('repair-confirm-popup');

        if (overlay) {
            overlay.style.cssText = '';
            overlay.style.display = 'none';
            overlay.style.visibility = 'hidden';
            overlay.style.opacity = '0';
            overlay.style.zIndex = '-1';
        }
        if (popup) {
            popup.style.cssText = '';
            popup.style.display = 'none';
            popup.style.visibility = 'hidden';
            popup.style.opacity = '0';
            popup.style.zIndex = '-1';
        }

        window.currentRepairCost = null;
        console.log('🔧 修复确认弹窗已关闭');
    }

    // 处理回收
    handleRecycle() {
        if (!this.currentItem) return;

        // 计算回收价格
        const recyclePrice = this.calculateRecyclePrice(this.currentItem);
        this.showRecycleConfirm(this.currentItem, recyclePrice);
    }

    // 计算回收价格
    calculateRecyclePrice(item) {
        let sellPrice = parseInt(item.sell_price || 0);

        // 获取物品品质
        const itemRarity = item.rarity || '普通';

        // 为没有售价的物品设置默认售价（与后端逻辑保持一致）
        if (sellPrice <= 0) {
            // 设置基础价格
            let basePrice = 1;
            switch (item.item_type) {
                case 'weapon':
                    basePrice = 10;
                    break; // 武器基础售价
                case 'armor':
                case 'equipment':
                    basePrice = 6;
                    break; // 装备基础售价
                case 'consumable':
                    basePrice = 2;
                    break; // 消耗品基础售价
                case 'material':
                    basePrice = 3;
                    break; // 材料基础售价
                case 'currency':
                    basePrice = 1;
                    break; // 货币基础售价
                default:
                    basePrice = 2;
                    break; // 其他物品基础售价
            }

            // 根据品质调整售价（与后端逻辑保持一致）
            let qualityMultiplier = 1.0;
            switch (itemRarity) {
                case '普通':
                    qualityMultiplier = 1.0;
                    break;
                case '稀有':
                    qualityMultiplier = 1.5;
                    break;
                case '史诗':
                    qualityMultiplier = 2.0;
                    break;
                case '传说':
                    qualityMultiplier = 3.0;
                    break;
                case '神话':
                    qualityMultiplier = 5.0;
                    break;
            }

            sellPrice = Math.floor(basePrice * qualityMultiplier);
        }

        // 计算回收价格（品质越高回收比例越高，与后端逻辑保持一致）
        let recycleRatio = 0.4; // 基础回收比例40%
        switch (itemRarity) {
            case '稀有':
                recycleRatio = 0.45;
                break; // 稀有45%
            case '史诗':
                recycleRatio = 0.5;
                break; // 史诗50%
            case '传说':
                recycleRatio = 0.55;
                break; // 传说55%
            case '神话':
                recycleRatio = 0.6;
                break; // 神话60%
        }

        return Math.max(1, Math.floor(sellPrice * recycleRatio));
    }

    // 显示回收确认
    showRecycleConfirm(item, recyclePrice) {
        document.getElementById('recycle-item-name').textContent = item.name;

        const details = [];
        details.push(`品质：${this.getRarityText(item.rarity)}`);
        details.push(`等级：${item.level_requirement}`);

        // 添加属性信息
        if (item.physical_attack && item.physical_attack > 0) {
            details.push(`物理攻击力：+${item.physical_attack}`);
        }
        if (item.immortal_attack && item.immortal_attack > 0) {
            details.push(`法术攻击力：+${item.immortal_attack}`);
        }
        if (item.physical_defense && item.physical_defense > 0) {
            details.push(`物理防御力：+${item.physical_defense}`);
        }
        if (item.immortal_defense && item.immortal_defense > 0) {
            details.push(`法术防御力：+${item.immortal_defense}`);
        }

        document.getElementById('recycle-item-details').textContent = details.join(' | ');

        // 🔧 修复：处理数量逻辑 - 装备类物品只能单件回收
        const currentQuantity = parseInt(item.quantity || 1);
        const isWeaponOrEquipment =
            ['weapon', 'armor', 'equipment', 'accessory'].includes(item.item_type) ||
            [
                'weapon',
                'sword',
                'fan',
                'ring',
                'bracelet',
                'chest',
                'necklace',
                'legs',
                'feet',
            ].includes(item.slot_type);
        const canSelectQuantity = currentQuantity > 1 && !isWeaponOrEquipment;

        // 设置数量相关元素
        const quantitySection = document.getElementById('recycle-quantity-section');
        const singlePriceInfo = document.getElementById('single-price-info');
        const quantityInput = document.getElementById('recycle-quantity-input');
        const currentQuantitySpan = document.getElementById('current-quantity');
        const singlePriceSpan = document.getElementById('single-price');

        if (canSelectQuantity) {
            // 显示数量选择（可堆叠的材料等）
            quantitySection.style.display = 'block';
            singlePriceInfo.style.display = 'block';
            quantityInput.value = 1;
            quantityInput.max = currentQuantity;
            currentQuantitySpan.textContent = currentQuantity;
            singlePriceSpan.textContent = recyclePrice;

            // 存储单价和最大数量
            window.currentSinglePrice = recyclePrice;
            window.currentMaxQuantity = currentQuantity;
        } else {
            // 隐藏数量选择（装备类物品）
            quantitySection.style.display = 'none';
            singlePriceInfo.style.display = 'none';

            // 🔧 关键修复：装备类物品强制设置为1件
            window.currentSinglePrice = recyclePrice;
            window.currentMaxQuantity = 1;
        }

        document.getElementById('recycle-price-value').textContent = `${recyclePrice} 金币`;

        const overlay = document.getElementById('recycle-confirm-overlay');
        const popup = document.getElementById('recycle-confirm-popup');

        if (overlay && popup) {
            const isMobile = window.innerWidth <= 768;
            const isSmallScreen = window.innerWidth <= 480;

            overlay.removeAttribute('style');
            popup.removeAttribute('style');

            overlay.style.cssText = `
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                bottom: 0 !important;
                width: 100vw !important;
                height: 100vh !important;
                background: rgba(0, 0, 0, ${isMobile ? '0.8' : '0.85'}) !important;
                z-index: 15000 !important;
                display: block !important;
                overflow: hidden !important;
            `;

            const popupWidth = isSmallScreen ? '98%' : isMobile ? '95%' : '90%';
            const popupMaxWidth = isSmallScreen ? '280px' : isMobile ? '320px' : '350px';

            popup.style.cssText = `
                position: fixed !important;
                top: 50% !important;
                left: 50% !important;
                transform: translate(-50%, -50%) !important;
                background: linear-gradient(145deg, #2c3e50, #34495e) !important;
                border-radius: ${isMobile ? '12px' : '16px'} !important;
                width: ${popupWidth} !important;
                max-width: ${popupMaxWidth} !important;
                border: 2px solid #e74c3c !important;
                z-index: 15001 !important;
                display: block !important;
                box-shadow: 0 10px 30px rgba(0, 0, 0, ${isMobile ? '0.6' : '0.5'}) !important;
                max-height: 90vh !important;
                overflow-y: auto !important;
                margin: 0 !important;
            `;

            overlay.offsetHeight;
            popup.offsetHeight;

            console.log('🔧 回收确认弹窗已显示');
        }

        window.currentRecyclePrice = recyclePrice;
    }

    // 🔧 新增：调整回收数量
    adjustRecycleQuantity(delta) {
        const quantityInput = document.getElementById('recycle-quantity-input');
        if (!quantityInput) return;

        const currentValue = parseInt(quantityInput.value);
        const newValue = Math.max(
            1,
            Math.min(window.currentMaxQuantity || 1, currentValue + delta)
        );
        quantityInput.value = newValue;
        this.updateRecyclePrice();
    }

    // 🔧 新增：设置最大回收数量
    setMaxRecycleQuantity() {
        const quantityInput = document.getElementById('recycle-quantity-input');
        if (!quantityInput) return;

        quantityInput.value = window.currentMaxQuantity || 1;
        this.updateRecyclePrice();
    }

    // 🔧 新增：更新回收价格
    updateRecyclePrice() {
        const quantityInput = document.getElementById('recycle-quantity-input');
        const priceValue = document.getElementById('recycle-price-value');

        if (!quantityInput || !priceValue) return;

        const quantity = parseInt(quantityInput.value);
        const singlePrice = window.currentSinglePrice || 0;
        const totalPrice = singlePrice * quantity;

        priceValue.textContent = `${totalPrice} 金币`;
        window.currentRecyclePrice = totalPrice;
    }

    // 🔧 修复：确认回收 - 装备类物品强制为1件
    confirmRecycle() {
        if (this.onRecycleCallback && this.currentItem) {
            // 🔧 关键修复：判断是否为装备类物品
            const isWeaponOrEquipment =
                ['weapon', 'armor', 'equipment', 'accessory'].includes(
                    this.currentItem.item_type
                ) ||
                [
                    'weapon',
                    'sword',
                    'fan',
                    'ring',
                    'bracelet',
                    'chest',
                    'necklace',
                    'legs',
                    'feet',
                ].includes(this.currentItem.slot_type);

            let recycleQuantity = 1; // 默认为1

            if (!isWeaponOrEquipment && window.currentMaxQuantity > 1) {
                // 只有非装备类物品且可堆叠时才能选择数量
                const quantityInput = document.getElementById('recycle-quantity-input');
                recycleQuantity = quantityInput ? parseInt(quantityInput.value) || 1 : 1;
            }

            console.log('🔧 [Recycle] 最终回收参数:', {
                item: this.currentItem.name,
                item_id: this.currentItem.item_id,
                item_type: this.currentItem.item_type,
                slot_type: this.currentItem.slot_type,
                isWeaponOrEquipment: isWeaponOrEquipment,
                recycleQuantity: recycleQuantity,
                maxQuantity: window.currentMaxQuantity,
                totalPrice: window.currentRecyclePrice,
            });

            this.onRecycleCallback(this.currentItem, window.currentRecyclePrice, recycleQuantity);
        }
        this.closeRecycleConfirm();
        this.close();
    }

    // 关闭回收确认
    closeRecycleConfirm() {
        // 🔧 修复：确保正确隐藏弹窗
        const overlay = document.getElementById('recycle-confirm-overlay');
        const popup = document.getElementById('recycle-confirm-popup');

        if (overlay) {
            // 🔧 新增：完全清除样式，避免残留
            overlay.style.cssText = '';
            overlay.style.display = 'none';
            overlay.style.visibility = 'hidden';
            overlay.style.opacity = '0';
            overlay.style.zIndex = '-1';
        }
        if (popup) {
            // 🔧 新增：完全清除样式，避免残留
            popup.style.cssText = '';
            popup.style.display = 'none';
            popup.style.visibility = 'hidden';
            popup.style.opacity = '0';
            popup.style.zIndex = '-1';
        }

        window.currentRecyclePrice = null;
        console.log('🔧 回收确认弹窗已关闭');
    }

    // 关闭弹窗
    close() {
        const popup = document.getElementById('item-detail-popup');
        if (popup) {
            popup.style.display = 'none';
        }
        this.currentItem = null;
    }

    // 辅助函数
    getItemTypeText(item) {
        // 🔥 属性丹类型判断 (ID: 314-358)
        if (item.item_id >= 314 && item.item_id <= 358) {
            return '属性丹';
        }

        // 🔧 新增：丹炉类型判断
        if (item.name && (item.name.includes('丹炉') || item.name.includes('炼丹'))) {
            return '炼丹工具';
        }

        // 通过slot_type判断类型
        if (item.slot_type) {
            switch (item.slot_type) {
                case 'weapon':
                    return '武器';
                case 'chest':
                    return '身体';
                case 'head':
                    return '头盔';
                case 'legs':
                    return '腰部';
                case 'feet':
                    return '足部';
                case 'ring':
                    return '手部';
                case 'necklace':
                    return '颈部';
                case 'bracelet':
                    return '腕部';
                case 'bracers':
                    return '护腕';
                case 'belt':
                    return '腰带';
                case 'accessory':
                    return '饰品';
                case 'consumable':
                    return '消耗品';
                case 'spirit':
                    return '灵石';
                case 'technique_manual':
                    return '功法秘籍';
                case 'material':
                    return '材料';
                case 'sword':
                    return '剑';
                case 'fan':
                    return '扇';
                default:
                    break;
            }
        }

        // 通过item_type判断类型（兜底）
        if (item.item_type) {
            switch (item.item_type) {
                case 'consumable':
                    return '消耗品';
                case 'weapon':
                    return '武器';
                case 'equipment':
                    return '装备';
                case 'material':
                    return '材料';
                case 'treasure':
                    return '珍宝';
                default:
                    break;
            }
        }

        // 通过名称判断特殊类型
        if (item.name || item.item_name) {
            const name = item.name || item.item_name;

            if (name.includes('丹方')) {
                return '丹方';
            }
            if (name.includes('功法') || name.includes('心法') || name.includes('秘籍')) {
                return '功法秘籍';
            }
            if (name.includes('灵石')) {
                return '灵石';
            }
            if (name.includes('丹药') || name.includes('丹')) {
                return '丹药';
            }
            if (name.includes('材料')) {
                return '材料';
            }
        }

        return '未知';
    }

    getRarityText(rarity) {
        const rarityMap = {
            common: '普通',
            uncommon: '稀有', // 🔧 修复：uncommon应该对应稀有
            rare: '史诗',
            epic: '传说',
            legendary: '神话',
            artifact: '神器',
            immortal: '仙器',
            // 🔧 新增：支持中文品质直接返回
            普通: '普通',
            稀有: '稀有',
            史诗: '史诗',
            传说: '传说',
            神话: '神话',
        };
        return rarityMap[rarity] || rarity || '未知';
    }

    // 🔧 新增：判断是否为非装备物品（不需要显示境界需求）
    isNonEquipmentItem(item) {
        // 属性丹类型判断 (ID: 314-358)
        if (item.item_id >= 314 && item.item_id <= 358) {
            return true;
        }

        // 丹方类型判断
        if (
            item.item_type === 'recipe' ||
            item.slot_type === 'recipe' ||
            (item.name && item.name.includes('丹方'))
        ) {
            return true;
        }

        // 材料类型判断
        if (item.item_type === 'material' || item.slot_type === 'material') {
            return true;
        }

        // 消耗品类型判断（除了装备类消耗品）
        if (
            (item.item_type === 'consumable' || item.slot_type === 'consumable') &&
            !['weapon', 'armor', 'accessory'].includes(item.slot_type)
        ) {
            return true;
        }

        // 特殊工具判断（丹炉等）
        if (
            item.name &&
            (item.name.includes('丹炉') ||
                item.name.includes('炼丹') ||
                item.name.includes('工具') ||
                item.name.includes('材料'))
        ) {
            return true;
        }

        // 灵石类型判断
        if (
            item.item_type === 'spirit' ||
            item.slot_type === 'spirit' ||
            (item.name && item.name.includes('灵石'))
        ) {
            return true;
        }

        // 功法秘籍类型判断
        if (
            item.item_type === 'technique_manual' ||
            item.slot_type === 'technique_manual' ||
            (item.name &&
                (item.name.includes('功法') ||
                    item.name.includes('心法') ||
                    item.name.includes('秘籍')))
        ) {
            return true;
        }

        return false;
    }

    isMagicItem(item) {
        if (item.slot_type === 'fan') return true;
        if (
            item.name &&
            (item.name.includes('法') || item.name.includes('魔') || item.name.includes('灵'))
        )
            return true;
        return false;
    }

    // 🆕 获取动画模型的中文名称
    getAnimationModelText(animationModel) {
        const animationMap = {
            feijian: '飞剑',
            wanjianjue: '万剑诀',
            jujian: '巨剑',
            zhangxinlei: '掌心雷',
            huoqiushu: '火球术',
            default: '默认',
        };
        return animationMap[animationModel] || animationModel;
    }

    // 🆕 获取五行属性的中文名称
    getElementText(elementType) {
        const elementMap = {
            metal: '金系',
            wood: '木系',
            water: '水系',
            fire: '火系',
            earth: '土系',
            neutral: '中性',
        };
        return elementMap[elementType] || '未知';
    }

    // 🆕 获取五行属性对应的颜色
    getElementColor(elementType) {
        const colorMap = {
            metal: '#FFD700', // 金色
            wood: '#228B22', // 森林绿
            water: '#1E90FF', // 道奇蓝
            fire: '#FF4500', // 橙红色
            earth: '#8B4513', // 马鞍棕
            neutral: '#C0C0C0', // 银色
        };
        return colorMap[elementType] || '#ecf0f1';
    }

    getItemImageUrl(item) {
        console.log('🖼️ [ItemDetailPopup] 获取物品图片URL:', item.name, '所有图片字段:', {
            icon_image: item.icon_image,
            image_url: item.image_url,
            model_image: item.model_image,
            detail_image: item.detail_image,
            item_image: item.item_image,
        });

        // 🔧 修复：优先使用后端处理好的image_url字段
        if (item.image_url && this.isValidImageUrl(item.image_url)) {
            console.log('🖼️ [ItemDetailPopup] 使用image_url字段:', item.image_url);
            return item.image_url;
        }

        // 🔧 次选：使用icon_image字段，但要正确处理路径
        if (item.icon_image && this.isValidImageUrl(item.icon_image)) {
            // 如果icon_image字段已经包含完整路径，直接使用
            if (
                item.icon_image.startsWith('assets/') ||
                item.icon_image.startsWith('/') ||
                item.icon_image.startsWith('http')
            ) {
                console.log('🖼️ [ItemDetailPopup] 使用完整路径:', item.icon_image);
                return item.icon_image;
            }

            // 否则拼接 assets/images/ 路径（不再硬编码equi目录）
            const imageUrl = `assets/images/${item.icon_image}`;
            console.log('🖼️ [ItemDetailPopup] 拼接图片路径:', imageUrl);
            return imageUrl;
        }

        // 🔧 再次：使用item_image字段
        if (item.item_image && this.isValidImageUrl(item.item_image)) {
            console.log('🖼️ [ItemDetailPopup] 使用item_image字段:', item.item_image);
            return item.item_image;
        }

        // 🔧 最后：返回null表示没有有效图片
        console.log('🖼️ [ItemDetailPopup] 没有找到有效图片，返回null');
        return null;
    }

    // 🆕 检查图片URL是否有效
    isValidImageUrl(url) {
        if (!url || typeof url !== 'string') return false;

        // 过滤掉明显无效的URL
        const invalidUrls = [
            '',
            'null',
            'undefined',
            'assets/images/1100.png',
            'assets/images/default.png',
            'images/1100.png',
            '1100.png',
        ];

        if (invalidUrls.includes(url.trim())) return false;

        // 检查是否为有效的图片文件扩展名
        const imageExtensions = ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.bmp', '.svg'];
        const hasValidExtension = imageExtensions.some(ext => url.toLowerCase().includes(ext));

        return hasValidExtension;
    }

    getDurabilityPercent(item) {
        if (!item.current_durability || !item.max_durability) return 0;
        return Math.round((item.current_durability / item.max_durability) * 100);
    }

    getSpecialEffectsDisplay(item) {
        if (!item.special_effects) return '';

        // 🔧 新增：对于丹方，如果没有真正的特殊效果，直接不显示
        const isRecipe =
            item.item_type === 'recipe' ||
            item.slot_type === 'recipe' ||
            (item.name && item.name.includes('丹方'));

        // 🔧 新增：对于丹炉等特殊工具，完全不显示特殊效果
        const isSpecialTool =
            item.name &&
            (item.name.includes('丹炉') ||
                item.name.includes('炼丹') ||
                item.name.includes('工具'));

        // 🔧 新增：对于炼丹材料，也不显示特殊效果
        const isCraftingMaterial =
            item.item_type === 'material' ||
            item.slot_type === 'material' ||
            (item.name &&
                (item.name.includes('材料') ||
                    item.name.includes('草药') ||
                    item.name.includes('矿石')));

        // 🔧 新增：对于属性丹，显示特定的用户友好信息
        const isAttributePill =
            item.name &&
            item.name.includes('丹') &&
            (item.name.includes('腾龙') ||
                item.name.includes('罗刹') ||
                item.name.includes('血气') ||
                item.name.includes('虚灵') ||
                item.name.includes('游龙'));

        if (isSpecialTool || isCraftingMaterial) {
            return ''; // 丹炉、材料等完全不显示特殊效果
        }

        if (isAttributePill) {
            // 属性丹的特殊处理：只显示关键的用户友好信息
            try {
                const effects =
                    typeof item.special_effects === 'object'
                        ? item.special_effects
                        : JSON.parse(item.special_effects);

                if (effects && effects.pill_category === 'attribute_pill') {
                    const friendlyInfo = [];
                    if (effects.attribute_display && effects.attribute_bonus) {
                        friendlyInfo.push(
                            `永久增加${effects.attribute_display}+${effects.attribute_bonus}点`
                        );
                    }
                    if (effects.usage_limit) {
                        friendlyInfo.push(`使用上限：${effects.usage_limit}颗`);
                    }
                    if (effects.pill_tier && effects.pill_tier >= 7) {
                        friendlyInfo.push(`高阶丹药，服用需谨慎丹毒`);
                    }

                    if (friendlyInfo.length > 0) {
                        return `
                            <div style="margin: 8px 0; padding: 10px 14px; background: linear-gradient(135deg, rgba(155, 89, 182, 0.15), rgba(26, 35, 50, 0.6)); border-radius: 10px; border: 1px solid rgba(155, 89, 182, 0.3); box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.05), 0 0 20px rgba(155, 89, 182, 0.1);">
                                <div style="font-weight: bold; color: #9b59b6; font-size: 14px; margin-bottom: 8px; text-shadow: 0 0 8px rgba(155, 89, 182, 0.8); text-align: center; padding-bottom: 6px; border-bottom: 1px solid rgba(155, 89, 182, 0.2);">丹药效果</div>
                                <div style="font-size: 11px; color: #ecf0f1; line-height: 1.4;">
                                    ${friendlyInfo
                                        .map(info => `<div style="margin: 3px 0;">${info}</div>`)
                                        .join('')}
                                </div>
                            </div>
                        `;
                    }
                }
            } catch (e) {
                // 解析失败时不显示
                return '';
            }
        }

        let effectsContent = '';

        // 🔧 增强：处理JSON格式的特殊效果
        if (typeof item.special_effects === 'object' && item.special_effects !== null) {
            // 如果是对象格式，格式化显示
            const effects = [];
            for (const [key, value] of Object.entries(item.special_effects)) {
                if (
                    value !== null &&
                    value !== undefined &&
                    value !== '' &&
                    this.isUserFriendlyEffect(key, value)
                ) {
                    const displayText = this.getEffectDisplayText(key, value);
                    if (
                        displayText &&
                        this.hasChineseContent(displayText) &&
                        this.isUserFriendlyDisplayText(displayText)
                    ) {
                        effects.push(displayText);
                    }
                }
            }
            effectsContent = effects.join('<br>');
        } else if (typeof item.special_effects === 'string') {
            // 尝试解析JSON字符串
            try {
                const parsed = JSON.parse(item.special_effects);
                if (typeof parsed === 'object' && parsed !== null) {
                    const effects = [];
                    for (const [key, value] of Object.entries(parsed)) {
                        if (
                            value !== null &&
                            value !== undefined &&
                            value !== '' &&
                            this.isUserFriendlyEffect(key, value)
                        ) {
                            const displayText = this.getEffectDisplayText(key, value);
                            if (
                                displayText &&
                                this.hasChineseContent(displayText) &&
                                this.isUserFriendlyDisplayText(displayText)
                            ) {
                                effects.push(displayText);
                            }
                        }
                    }
                    effectsContent = effects.join('<br>');
                } else {
                    // 如果是普通字符串，检查是否是用户友好的描述且包含中文
                    if (
                        this.isUserFriendlyDescription(item.special_effects) &&
                        this.hasChineseContent(item.special_effects) &&
                        this.isUserFriendlyDisplayText(item.special_effects)
                    ) {
                        effectsContent = item.special_effects;
                    }
                }
            } catch (e) {
                // 如果不是JSON，检查是否是用户友好的描述且包含中文
                if (
                    this.isUserFriendlyDescription(item.special_effects) &&
                    this.hasChineseContent(item.special_effects) &&
                    this.isUserFriendlyDisplayText(item.special_effects)
                ) {
                    effectsContent = item.special_effects;
                }
            }
        } else {
            const strValue = String(item.special_effects);
            if (
                this.isUserFriendlyDescription(strValue) &&
                this.hasChineseContent(strValue) &&
                this.isUserFriendlyDisplayText(strValue)
            ) {
                effectsContent = strValue;
            }
        }

        // 🔧 加强：如果处理后没有内容，或者是丹方且内容看起来都是技术性的，不显示
        if (!effectsContent || effectsContent.trim() === '') return '';

        // 🔧 新增：对于丹方，如果效果内容都是技术性描述，不显示
        if (isRecipe) {
            const technicalPhrases = [
                'target_realm',
                'learn_requirement',
                'base_success_rate',
                'recipe_type',
                'pill_type',
            ];
            const containsTechnical = technicalPhrases.some(phrase =>
                effectsContent.toLowerCase().includes(phrase)
            );

            // 如果内容过短且包含技术词汇，不显示
            if (effectsContent.length < 10 || containsTechnical) {
                return '';
            }
        }

        // 🔧 新增：最终检查，如果内容包含明显的技术术语，不显示
        const forbiddenTerms = [
            'object Object',
            'undefined',
            'null',
            'alchemy_furnace',
            'item_type',
            'slot_type',
        ];
        if (
            forbiddenTerms.some(term => effectsContent.toLowerCase().includes(term.toLowerCase()))
        ) {
            return '';
        }

        return `
            <div style="margin: 8px 0; padding: 10px 14px; background: linear-gradient(135deg, rgba(155, 89, 182, 0.15), rgba(26, 35, 50, 0.6)); border-radius: 10px; border: 1px solid rgba(155, 89, 182, 0.3); box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.05), 0 0 20px rgba(155, 89, 182, 0.1);">
                <div style="font-weight: bold; color: #9b59b6; font-size: 14px; margin-bottom: 8px; text-shadow: 0 0 8px rgba(155, 89, 182, 0.8); text-align: center; padding-bottom: 6px; border-bottom: 1px solid rgba(155, 89, 182, 0.2);">特殊效果</div>
                <div style="font-size: 11px; color: #ecf0f1; line-height: 1.4;">
                    ${effectsContent
                        .split('<br>')
                        .map(effect => `<div style="margin: 3px 0;">${effect}</div>`)
                        .join('')}
                </div>
            </div>
        `;
    }

    // 🆕 检查文本是否包含中文内容
    hasChineseContent(text) {
        if (!text || typeof text !== 'string') return false;
        return /[\u4e00-\u9fa5]/.test(text);
    }

    // 🆕 检查显示文本是否用户友好（更严格的检查）
    isUserFriendlyDisplayText(text) {
        if (!text || typeof text !== 'string') return false;

        // 更严格的技术术语过滤
        const strictTechnicalTerms = [
            'object Object',
            'undefined',
            'null',
            'alchemy_furnace',
            'item_type',
            'slot_type',
            'base_success_rate',
            'recipe_type',
            'pill_type',
            'furnace_requirement',
            'learn_requirement',
            'target_realm',
            'level_requirement',
            'true',
            'false',
            'weapon',
            'armor',
            'accessory',
            'consumable',
            'material',
            'treasure',
        ];

        const lowerText = text.toLowerCase();

        // 如果包含任何严格的技术术语，不显示
        if (strictTechnicalTerms.some(term => lowerText.includes(term))) {
            return false;
        }

        // 如果是纯英文技术词汇且没有中文，不显示
        if (/^[a-z_\s]+$/i.test(text) && !/[\u4e00-\u9fa5]/.test(text)) {
            return false;
        }

        // 如果内容太短且看起来是技术性的，不显示
        if (text.length < 5 && !/[\u4e00-\u9fa5]/.test(text)) {
            return false;
        }

        return true;
    }

    // 🆕 判断是否是用户友好的效果字段
    isUserFriendlyEffect(key, value) {
        // 🔧 新增：优先显示丹药相关的重要效果
        const importantPillEffects = [
            'bonus_rate',
            'recovery_rate',
            'target_realm',
            'soul_healing',
            'tribulation_success_rate',
            'breakthrough_rate',
            'cultivation_boost',
        ];

        if (importantPillEffects.includes(key)) {
            return true;
        }

        // 🔧 扩展：过滤掉更多技术性字段，特别是属性丹和丹方相关的
        const technicalFields = [
            'type',
            'item_type',
            'slot_type',
            'rarity',
            'durability',
            'max_quantity',
            'min_quantity',
            'success_rate',
            'level_requirement',
            'realm_requirement',
            'is_stackable',
            'is_tradeable',
            'can_have_rarity',
            'weight',
            'sell_price',
            'buy_price',
            'created_at',
            'updated_at',
            'id',
            'item_id',
            'attribute_type',
            // 🔧 属性丹特有的技术字段
            'pill_type',
            'pill_tier',
            'attribute_display',
            'attribute_bonus',
            'pill_category',
            'usage_limit',
            'poison_weight',
            // 🔧 丹方相关的技术字段
            'learn_requirement',
            'materials',
            'crafting_time',
            'base_success_rate',
            'recipe_type',
            'furnace_requirement',
            'result_item_id',
            'ingredient_list',
            'unlock_level',
        ];

        if (technicalFields.includes(key)) {
            return false;
        }

        // 🔧 扩展：过滤掉更多明显的技术值
        const technicalValues = [
            'alchemy_furnace',
            'common',
            'rare',
            'epic',
            'legendary',
            'weapon',
            'armor',
            'consumable',
            'material',
            'special',
            'base',
            'random',
            // 🔧 新增：丹方相关的技术值
            'recipe',
            'pill',
            'alchemy',
            'crafting',
            'furnace',
            'basic',
            'advanced',
            'true',
            'false',
            'null',
            'undefined',
            'object',
            'array',
        ];

        const valueStr = String(value).toLowerCase().trim();
        if (technicalValues.includes(valueStr)) {
            return false;
        }

        // 🔧 新增：过滤纯数字ID（通常是技术性的）
        if (/^\d+$/.test(valueStr) && parseInt(valueStr) > 100) {
            return false;
        }

        // 🔧 新增：过滤布尔值的字符串表示
        if (['1', '0'].includes(valueStr)) {
            return false;
        }

        return true;
    }

    // 🆕 判断是否是用户友好的描述文本
    isUserFriendlyDescription(text) {
        if (!text || typeof text !== 'string') return false;

        // 过滤掉纯技术词汇
        const technicalTerms = [
            'alchemy_furnace',
            'item_type',
            'slot_type',
            'base',
            'random',
            'weapon',
            'armor',
            'consumable',
            'material',
            'special',
        ];

        const lowerText = text.toLowerCase();

        // 如果整个文本就是技术词汇，不显示
        if (technicalTerms.includes(lowerText)) {
            return false;
        }

        // 如果包含中文或者是明显的用户描述，显示
        if (/[\u4e00-\u9fa5]/.test(text) || text.length > 20) {
            return true;
        }

        // 如果是纯英文技术词汇组合，不显示
        if (/^[a-z_]+$/.test(lowerText)) {
            return false;
        }

        return true;
    }

    // 🆕 获取格式化的效果显示文本
    getEffectDisplayText(key, value) {
        const keyText = this.getEffectKeyText(key);

        // 如果键名和原键相同（没有翻译），且值看起来是技术性的，跳过
        if (keyText === key && !this.isUserFriendlyDescription(String(value))) {
            return null;
        }

        // 🧪 特殊效果的格式化处理
        let formattedValue = value;

        // 🔧 新增：特殊处理bonus_rate - 渡劫丹成功率加成
        if (key === 'bonus_rate') {
            const numValue = parseFloat(value);
            if (!isNaN(numValue)) {
                formattedValue = `+${numValue}%`;
            }
            return `渡劫成功率：${formattedValue}`;
        }

        // 🔧 新增：特殊处理target_realm - 适用境界
        if (key === 'target_realm') {
            return `适用境界：${value}`;
        }

        // 🔧 新增：特殊处理recovery_rate - 魂力恢复
        if (key === 'recovery_rate') {
            const numValue = parseFloat(value);
            if (!isNaN(numValue)) {
                if (numValue === 100) {
                    formattedValue = '完全恢复';
                } else {
                    formattedValue = `+${numValue}点`;
                }
            }
            return `魂力恢复：${formattedValue}`;
        }

        // 处理recovery_rate等恢复类效果 - 如果是100则显示为100%恢复
        if (key.includes('recovery') || key.includes('rate')) {
            const numValue = parseFloat(value);
            if (!isNaN(numValue)) {
                if (numValue === 100) {
                    formattedValue = '100%恢复';
                } else if (numValue >= 1) {
                    formattedValue = numValue + '%';
                } else {
                    formattedValue = (numValue * 100).toFixed(1) + '%';
                }
            }
        }

        // 处理boost、enhancement类效果 - 添加数值和单位
        if (key.includes('boost') || key.includes('enhancement') || key.includes('_bonus')) {
            const numValue = parseFloat(value);
            if (!isNaN(numValue) && numValue > 0) {
                if (numValue <= 1) {
                    formattedValue = (numValue * 100).toFixed(1) + '%';
                } else if (numValue < 100) {
                    formattedValue = '+' + numValue + '%';
                } else {
                    formattedValue = '+' + numValue + '点';
                }
            }
        }

        // 处理resistance类效果 - 抗性显示
        if (key.includes('resistance')) {
            const numValue = parseFloat(value);
            if (!isNaN(numValue)) {
                if (numValue <= 1) {
                    formattedValue = (numValue * 100).toFixed(1) + '%';
                } else {
                    formattedValue = numValue + '%';
                }
            }
        }

        // 处理protection类效果 - 保护效果
        if (key.includes('protection')) {
            if (typeof value === 'boolean') {
                formattedValue = value ? '有效' : '无效';
            } else if (value == 1 || value === true || value === 'true') {
                formattedValue = '有效';
            } else if (value == 0 || value === false || value === 'false') {
                formattedValue = '无效';
            }
        }

        return `${keyText}：${formattedValue}`;
    }

    // 🆕 获取特殊效果键名的中文显示
    getEffectKeyText(key) {
        const effectKeyMap = {
            // 🧙‍♂️ 战斗相关效果
            damage_bonus: '伤害加成',
            defense_bonus: '防御加成',
            heal_bonus: '治疗加成',
            mana_regeneration: '法力回复',
            health_regeneration: '生命回复',
            spell_power: '法术强度',
            attack_speed: '攻击速度',
            movement_speed: '移动速度',
            critical_chance: '暴击概率',
            critical_damage: '暴击伤害',
            lifesteal: '生命偷取',
            mana_steal: '法力偷取',
            armor_penetration: '护甲穿透',
            magic_penetration: '法术穿透',
            damage_reduction: '伤害减免',
            status_resistance: '状态抗性',
            elemental_damage: '元素伤害',
            poison_damage: '毒素伤害',
            fire_damage: '火焰伤害',
            ice_damage: '冰霜伤害',
            lightning_damage: '雷电伤害',
            holy_damage: '神圣伤害',
            dark_damage: '暗黑伤害',
            set_bonus: '套装效果',
            passive_skill: '被动技能',
            active_skill: '主动技能',
            unique_effect: '唯一效果',

            // 🧪 丹药相关效果
            recovery_rate: '魂力恢复',
            hp_recovery: '生命恢复',
            mp_recovery: '法力恢复',
            qi_recovery: '真气恢复',
            spirit_recovery: '精神恢复',
            soul_recovery: '神魂恢复',
            energy_recovery: '能量恢复',
            vitality_recovery: '活力恢复',
            stamina_recovery: '体力恢复',
            cultivation_boost: '修炼加成',
            breakthrough_rate: '突破成功率',
            meridian_enhancement: '经脉强化',
            dantian_enhancement: '丹田强化',
            foundation_boost: '根基增强',
            talent_enhancement: '天赋提升',
            comprehension_boost: '悟性加成',
            meditation_boost: '静心效果',
            inner_peace: '内心宁静',
            spiritual_clarity: '神识清明',
            pill_toxin_resistance: '丹毒抗性',
            realm_stability: '境界稳固',
            heavenly_tribulation_resistance: '天劫抗性',
            karma_cleansing: '业力清洗',
            lifespan_extension: '寿元延长',
            physique_enhancement: '体质强化',
            constitution_boost: '筋骨强化',
            agility_boost: '身法提升',
            wisdom_boost: '悟性提升',
            spirit_boost: '神魂提升',

            // 🔥 渡劫丹效果
            bonus_rate: '渡劫成功率',
            tribulation_success_rate: '渡劫成功率提升',
            lightning_resistance: '雷电抗性',
            heavenly_fire_resistance: '天火抗性',
            heart_demon_resistance: '心魔抗性',
            soul_protection: '神魂护佑',
            divine_protection: '天道庇护',
            target_realm: '适用境界',

            // 🌟 养魂丹效果
            recovery_rate: '魂力恢复',
            soul_healing: '神魂治愈',
            spiritual_damage_recovery: '神识损伤恢复',
            mental_clarity: '神志清醒',
            consciousness_restoration: '意识修复',
            spiritual_realm_stabilization: '神识境界稳固',

            // ⚗️ 属性丹效果
            strength_boost: '力量提升',
            agility_enhancement: '敏捷强化',
            intelligence_boost: '智力提升',
            vitality_enhancement: '体力强化',
            luck_boost: '气运提升',
            charisma_enhancement: '魅力增强',
            perception_boost: '感知提升',
        };
        return effectKeyMap[key] || key;
    }

    // 静态方法，供全局调用
    static getInstance() {
        if (!window.itemDetailPopupInstance) {
            window.itemDetailPopupInstance = new ItemDetailPopup();
            // 🔧 修复：确保初始化时调用init方法创建HTML模板
            window.itemDetailPopupInstance.init();
        }
        return window.itemDetailPopupInstance;
    }

    static show(item, options = {}) {
        return ItemDetailPopup.getInstance().show(item, options);
    }

    static close() {
        return ItemDetailPopup.getInstance().close();
    }

    static setCurrentUser(user) {
        return ItemDetailPopup.getInstance().setCurrentUser(user);
    }

    static setCallbacks(callbacks) {
        return ItemDetailPopup.getInstance().setCallbacks(callbacks);
    }

    static handleEquip() {
        return ItemDetailPopup.getInstance().handleEquip();
    }

    static handleUnequip() {
        return ItemDetailPopup.getInstance().handleUnequip();
    }

    static handleUse() {
        return ItemDetailPopup.getInstance().handleUse();
    }

    static handleRepair() {
        return ItemDetailPopup.getInstance().handleRepair();
    }

    static handleRecycle() {
        return ItemDetailPopup.getInstance().handleRecycle();
    }

    static confirmRecycle() {
        return ItemDetailPopup.getInstance().confirmRecycle();
    }

    static closeRecycleConfirm() {
        return ItemDetailPopup.getInstance().closeRecycleConfirm();
    }

    static closeRepairConfirm() {
        return ItemDetailPopup.getInstance().closeRepairConfirm();
    }

    static confirmRepair() {
        return ItemDetailPopup.getInstance().confirmRepair();
    }

    // 🔧 新增：静态方法 - 调整回收数量
    static adjustRecycleQuantity(delta) {
        return ItemDetailPopup.getInstance().adjustRecycleQuantity(delta);
    }

    // 🔧 新增：静态方法 - 设置最大回收数量
    static setMaxRecycleQuantity() {
        return ItemDetailPopup.getInstance().setMaxRecycleQuantity();
    }

    // 🔧 新增：静态方法 - 更新回收价格
    static updateRecyclePrice() {
        return ItemDetailPopup.getInstance().updateRecyclePrice();
    }
}

// 自动初始化
document.addEventListener('DOMContentLoaded', () => {
    ItemDetailPopup.getInstance();

    // 🔧 新增：页面加载完成后再次确保遮罩层隐藏
    setTimeout(() => {
        const overlay = document.getElementById('recycle-confirm-overlay');
        const popup = document.getElementById('recycle-confirm-popup');

        if (overlay && overlay.style.display !== 'none') {
            console.warn('🔧 发现遮罩层异常显示，强制隐藏');
            overlay.style.display = 'none';
            overlay.style.visibility = 'hidden';
            overlay.style.opacity = '0';
        }

        if (popup && popup.style.display !== 'none') {
            console.warn('🔧 发现弹窗异常显示，强制隐藏');
            popup.style.display = 'none';
            popup.style.visibility = 'hidden';
            popup.style.opacity = '0';
        }
    }, 500);
});

// 导出到全局
window.ItemDetailPopup = ItemDetailPopup;
