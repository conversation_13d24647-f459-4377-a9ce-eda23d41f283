/**
 * 装备套装系统样式
 * 基于实际game_item_sets表结构设计
 */

/* 主容器 */
.equipment-sets-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: "Microsoft YaHei", Arial, sans-serif;
}

/* 装备页面中的套装区域 */
.equipment-sets-section {
    padding: 15px;
    margin-top: 10px;
    overflow: auto;
}

/* 套装头部 */
.sets-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px 20px;
    background: linear-gradient(135deg, rgba(212, 177, 116, 0.1), rgba(255, 215, 0, 0.05));
    border: 1px solid rgba(212, 177, 116, 0.3);
    border-radius: 8px;
}

.sets-header h3 {
    margin: 0;
    color: #d4b174;
    font-size: 24px;
    font-weight: bold;
}

.total-power {
    display: flex;
    align-items: center;
    gap: 10px;
}

.total-power .label {
    color: #ccc;
    font-size: 14px;
}

.total-power .value {
    color: #ffd700;
    font-size: 18px;
    font-weight: bold;
}

/* 无套装状态 */
.no-sets {
    text-align: center;
    padding: 40px 20px;
    background: rgba(0, 0, 0, 0.3);
    border: 1px dashed rgba(212, 177, 116, 0.3);
    border-radius: 8px;
    color: #999;
}

.no-sets p {
    margin: 10px 0;
}

.no-sets .hint {
    font-size: 12px;
    color: #666;
}

/* 套装列表 */
.sets-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

/* 套装卡片 */
.set-card {
    background: linear-gradient(145deg, rgba(20, 20, 30, 0.9), rgba(30, 30, 40, 0.9));
    border: 1px solid rgba(212, 177, 116, 0.3);
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.set-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);
    border-color: rgba(212, 177, 116, 0.6);
}

/* 品质边框 */
.set-card.rarity-common {
    border-color: rgba(255, 255, 255, 0.3);
}

.set-card.rarity-rare {
    border-color: rgba(30, 255, 0, 0.5);
}

.set-card.rarity-epic {
    border-color: rgba(0, 112, 221, 0.5);
}

.set-card.rarity-legendary {
    border-color: rgba(163, 53, 238, 0.5);
}

.set-card.rarity-mythic {
    border-color: rgba(255, 128, 0, 0.5);
}

/* 套装头部信息 */
.set-header {
    margin-bottom: 15px;
}

.set-name-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.set-name {
    margin: 0;
    font-size: 18px;
    font-weight: bold;
}

.set-pieces-count {
    background: rgba(212, 177, 116, 0.2);
    color: #d4b174;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: bold;
}

.set-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
}

.set-rarity {
    color: #ccc;
}

.realm-requirement {
    color: #999;
}

/* 套装描述 */
.set-description {
    color: #bbb;
    font-size: 14px;
    line-height: 1.4;
    margin-bottom: 15px;
    padding: 10px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 6px;
}

/* 已装备部件 */
.equipped-pieces h5 {
    margin: 0 0 10px 0;
    color: #d4b174;
    font-size: 14px;
}

.pieces-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 8px;
    margin-bottom: 15px;
}

.piece-item {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    padding: 8px 10px;
    font-size: 12px;
}

.piece-item.equipped {
    background: rgba(30, 255, 0, 0.1);
    border-color: rgba(30, 255, 0, 0.3);
}

.piece-name {
    display: block;
    color: #fff;
    font-weight: bold;
    margin-bottom: 2px;
}

.piece-slot {
    color: #999;
    font-size: 11px;
}

/* 套装效果 */
.set-effects h5 {
    margin: 0 0 10px 0;
    color: #d4b174;
    font-size: 14px;
}

.effect-tier {
    margin-bottom: 12px;
    padding: 10px;
    border-radius: 6px;
    border-left: 3px solid #666;
}

.effect-tier.active {
    background: rgba(30, 255, 0, 0.05);
    border-left-color: #1eff00;
}

.effect-tier.inactive {
    background: rgba(100, 100, 100, 0.05);
    border-left-color: #666;
}

.tier-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
}

.tier-name {
    font-weight: bold;
    color: #fff;
    font-size: 13px;
}

.status-badge {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: bold;
}

.status-badge.active {
    background: rgba(30, 255, 0, 0.2);
    color: #1eff00;
}

.status-badge.inactive {
    background: rgba(100, 100, 100, 0.2);
    color: #999;
}

.effect-content {
    font-size: 12px;
    line-height: 1.4;
}

.effect-tier.active .effect-content {
    color: #ddd;
}

.effect-tier.inactive .effect-content {
    color: #888;
}

.special-effect {
    color: #ffd700;
    font-style: italic;
}

/* 套装操作按钮 */
.set-actions {
    margin-top: 15px;
    text-align: center;
}

.btn-details {
    background: linear-gradient(45deg, #d4b174, #ffd700);
    color: #000;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-details:hover {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(255, 215, 0, 0.3);
}

/* 套装详情模态框 */
.set-details-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    cursor: pointer;
}

.modal-content {
    position: relative;
    background: linear-gradient(145deg, rgba(20, 20, 30, 0.98), rgba(30, 30, 40, 0.98));
    border: 2px solid rgba(212, 177, 116, 0.5);
    border-radius: 12px;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    margin: 20px;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px 15px;
    border-bottom: 1px solid rgba(212, 177, 116, 0.3);
}

.modal-header h3 {
    margin: 0;
    color: #d4b174;
    font-size: 20px;
}

.close-btn {
    background: none;
    border: none;
    color: #ccc;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    color: #fff;
}

.modal-body {
    padding: 20px 25px;
}

/* 套装信息 */
.set-info {
    margin-bottom: 20px;
}

.info-row {
    display: flex;
    margin-bottom: 8px;
    font-size: 14px;
}

.info-row .label {
    min-width: 80px;
    color: #999;
}

.info-row .value {
    color: #ddd;
    flex: 1;
}

/* 套装部件网格 */
.set-items h4,
.detailed-effects h4 {
    color: #d4b174;
    margin: 0 0 15px 0;
    font-size: 16px;
}

.items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    margin-bottom: 20px;
}

.item-card {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    padding: 12px;
    text-align: center;
}

.item-name {
    color: #fff;
    font-weight: bold;
    margin-bottom: 4px;
    font-size: 13px;
}

.item-slot {
    color: #ccc;
    font-size: 12px;
    margin-bottom: 4px;
}

.item-realm {
    color: #999;
    font-size: 11px;
}

/* 详细效果 */
.effect-detail {
    margin-bottom: 15px;
    padding: 15px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    border-left: 3px solid #d4b174;
}

.effect-detail h5 {
    margin: 0 0 8px 0;
    color: #ffd700;
    font-size: 14px;
}

.effect-text {
    color: #ddd;
    font-size: 13px;
    line-height: 1.4;
}

/* 消息提示 */
.set-message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 6px;
    z-index: 10001;
    font-size: 14px;
    font-weight: bold;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.set-message.info {
    background: rgba(0, 123, 255, 0.9);
    color: #fff;
    border: 1px solid rgba(0, 123, 255, 0.5);
}

.set-message.error {
    background: rgba(220, 53, 69, 0.9);
    color: #fff;
    border: 1px solid rgba(220, 53, 69, 0.5);
}

.set-message.success {
    background: rgba(40, 167, 69, 0.9);
    color: #fff;
    border: 1px solid rgba(40, 167, 69, 0.5);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .equipment-sets-container {
        padding: 15px;
    }
    
    .sets-list {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .sets-header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .set-name-row {
        flex-direction: column;
        gap: 8px;
        text-align: center;
    }
    
    .pieces-grid {
        grid-template-columns: 1fr;
    }
    
    .items-grid {
        grid-template-columns: 1fr;
    }
    
    .modal-content {
        margin: 10px;
        max-height: 90vh;
    }
    
    .modal-header,
    .modal-body {
        padding: 15px 20px;
    }
}

@media (max-width: 480px) {
    .equipment-sets-container {
        padding: 10px;
    }
    
    .set-card {
        padding: 15px;
    }
    
    .sets-header h3 {
        font-size: 18px;
    }
    
    .total-power .value {
        font-size: 16px;
    }
    
    .set-name {
        font-size: 16px;
    }
    
    .effect-tier {
        padding: 8px;
    }
    
    .tier-header {
        flex-direction: column;
        gap: 4px;
        align-items: flex-start;
    }
} 