<?php
// 确保没有之前的输出
ob_start();

header('Content-Type: application/json');

// 检查登录状态
function checkLogin() {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    if (!isset($_SESSION['admin_id'])) {
        response(['success' => false, 'message' => '请先登录']);
    }
}

// 检查权限
function checkPermission($action) {
    $role = isset($_SESSION['admin_role']) ? $_SESSION['admin_role'] : '';
    
    // 超级管理员拥有所有权限
    if ($role === 'super_admin') {
        return true;
    }
    
    // 普通管理员权限限制
    if ($role === 'admin') {
        // 禁止删除操作
        if ($action === 'delete') {
            response(['success' => false, 'message' => '权限不足']);
        }
    }
    
    return true;
}

// 记录操作日志
function logAction($action, $type, $targetId, $details = []) {
    try {
        $conn = getConnection();
        $stmt = $conn->prepare("INSERT INTO admin_logs (admin_id, action, target_type, target_id, details, ip) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute([
            $_SESSION['admin_id'],
            $action,
            $type,
            $targetId,
            json_encode($details),
            $_SERVER['REMOTE_ADDR']
        ]);
    } catch(PDOException $e) {
        // 日志记录失败不影响主要功能
        error_log("Failed to log action: " . $e->getMessage());
    }
}

// 数据库连接配置 - 使用主游戏数据库
$db_config = [
    'host' => 'localhost',
    'username' => 'ynxx',
    'password' => 'mjlxz159',
    'database' => 'yn_game'
];

// 建立数据库连接
function getConnection() {
    global $db_config;
    try {
        $conn = new PDO(
            "mysql:host={$db_config['host']};dbname={$db_config['database']};charset=utf8mb4",
            $db_config['username'],
            $db_config['password']
        );
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $conn;
    } catch(PDOException $e) {
        response(['success' => false, 'message' => '数据库连接失败: ' . $e->getMessage()]);
        exit;
    }
}

// 统一响应函数
function response($data) {
    // 清除之前的输出
    if (ob_get_length()) ob_clean();
    
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

// 获取请求参数
$action = isset($_GET['action']) ? $_GET['action'] : '';
$type = isset($_GET['type']) ? $_GET['type'] : '';

// 登录验证检查
if ($action === 'check_login') {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    if (isset($_SESSION['admin_id'])) {
        response(['success' => true, 'users' => $_SESSION['admin_username']]);
    } else {
        response(['success' => false, 'message' => '未登录']);
    }
}

// 其他操作需要登录验证
checkLogin();

// 根据请求类型处理不同的操作
switch($action) {
    case 'list':
        getList($type);
        break;
    case 'get':
        getItem($type);
        break;
    case 'add':
        checkPermission('add');
        addItem($type);
        break;
    case 'edit':
        checkPermission('edit');
        editItem($type);
        break;
    case 'delete':
        checkPermission('delete');
        deleteItem($type);
        break;
    case 'stats':
        getStats();
        break;
    default:
        response(['success' => false, 'message' => '未知的操作类型']);
}

// 获取统计数据
function getStats() {
    $conn = getConnection();
    $stats = [];
    
    try {
        // 先检查表是否存在，只统计存在的表
        $allTables = [];
        $stmt = $conn->query("SHOW TABLES");
        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $allTables[] = $row[0];
        }
        
        // 玩家统计 - 使用users表
        if (in_array('users', $allTables)) {
            $stmt = $conn->query("SELECT COUNT(*) as count FROM users");
            $stats['users'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        } else {
            $stats['users'] = 0;
        }
        
        // 物品统计 - 使用drops表
        if (in_array('drops', $allTables)) {
            $stmt = $conn->query("SELECT COUNT(*) as count FROM drops");
            $stats['items'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        } else {
            $stats['items'] = 0;
        }
        
        // 怪物统计 - 使用monsters表
        if (in_array('monsters', $allTables)) {
            $stmt = $conn->query("SELECT COUNT(*) as count FROM monsters");
            $stats['monsters'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        } else {
            $stats['monsters'] = 0;
        }
        
        // 地图统计 - 使用maps表
        if (in_array('maps', $allTables)) {
            $stmt = $conn->query("SELECT COUNT(*) as count FROM maps");
            $stats['maps'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        } else {
            $stats['maps'] = 0;
        }
        
        // 秘境统计 - 使用dungeons表
        if (in_array('dungeons', $allTables)) {
            $stmt = $conn->query("SELECT COUNT(*) as count FROM dungeons");
            $stats['dungeons'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        } else {
            $stats['dungeons'] = 0;
        }
        
        // 境界统计 - 使用realms表
        if (in_array('realms', $allTables)) {
            $stmt = $conn->query("SELECT COUNT(*) as count FROM realms");
            $stats['realms'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        } else {
            $stats['realms'] = 0;
        }
        
        response(['success' => true, 'data' => $stats]);
    } catch(PDOException $e) {
        // 如果查询失败，至少返回一些调试信息
        $stmt = $conn->query("SHOW TABLES");
        $allTables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        response([
            'success' => false, 
            'message' => '获取统计数据失败: ' . $e->getMessage(),
            'debug_info' => [
                'available_tables' => $allTables,
                'error' => $e->getMessage()
            ]
        ]);
    }
}

// 获取列表数据
function getList($type) {
    $conn = getConnection();
    $table = getTableName($type);
    
    if (!$table) {
        response(['success' => false, 'message' => '无效的数据类型: ' . $type]);
    }
    
    try {
        // 检查表是否存在
        $stmt = $conn->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        if (!$stmt->fetch()) {
            // 获取所有表名用于调试
            $stmt = $conn->query("SHOW TABLES");
            $allTables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            response([
                'success' => false, 
                'message' => "表 '{$table}' 不存在。数据库中的表有: " . implode(', ', $allTables)
            ]);
        }
        
        // 分页参数
        $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
        $limit = isset($_GET['limit']) ? max(1, min(100, intval($_GET['limit']))) : 20;
        $offset = ($page - 1) * $limit;
        
        // 搜索参数
        $search = isset($_GET['search']) ? $_GET['search'] : '';
        $searchField = getSearchField($type);
        
        $whereClause = '';
        $params = [];
        if ($search && $searchField) {
            $whereClause = "WHERE {$searchField} LIKE ?";
            $params[] = "%{$search}%";
        }
        
        // 获取总数
        $countSql = "SELECT COUNT(*) as total FROM {$table} {$whereClause}";
        $stmt = $conn->prepare($countSql);
        $stmt->execute($params);
        $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        // 获取数据
        $dataSql = "SELECT * FROM {$table} {$whereClause} ORDER BY id DESC LIMIT {$limit} OFFSET {$offset}";
        $stmt = $conn->prepare($dataSql);
        $stmt->execute($params);
        $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        response([
            'success' => true, 
            'data' => $data,
            'pagination' => [
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'pages' => ceil($total / $limit)
            ]
        ]);
    } catch(PDOException $e) {
        response(['success' => false, 'message' => '获取数据失败: ' . $e->getMessage()]);
    }
}

// 获取单条数据
function getItem($type) {
    $conn = getConnection();
    $table = getTableName($type);
    $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
    
    if (!$table || !$id) {
        response(['success' => false, 'message' => '参数错误']);
    }
    
    try {
        $stmt = $conn->prepare("SELECT * FROM {$table} WHERE id = ?");
        $stmt->execute([$id]);
        $data = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($data) {
            response(['success' => true, 'data' => $data]);
        } else {
            response(['success' => false, 'message' => '数据不存在']);
        }
    } catch(PDOException $e) {
        response(['success' => false, 'message' => '获取数据失败: ' . $e->getMessage()]);
    }
}

// 添加数据
function addItem($type) {
    $conn = getConnection();
    $table = getTableName($type);
    $data = json_decode(file_get_contents('php://input'), true);
    
    if (!$table || !$data) {
        response(['success' => false, 'message' => '参数错误']);
    }
    
    try {
        // 移除id字段
        unset($data['id']);
        
        $fields = implode(',', array_keys($data));
        $placeholders = implode(',', array_fill(0, count($data), '?'));
        
        $stmt = $conn->prepare("INSERT INTO {$table} ({$fields}) VALUES ({$placeholders})");
        $stmt->execute(array_values($data));
        
        $lastId = $conn->lastInsertId();
        logAction('add', $type, $lastId, $data);
        
        response(['success' => true, 'message' => '添加成功', 'id' => $lastId]);
    } catch(PDOException $e) {
        response(['success' => false, 'message' => '添加失败: ' . $e->getMessage()]);
    }
}

// 编辑数据
function editItem($type) {
    $conn = getConnection();
    $table = getTableName($type);
    $data = json_decode(file_get_contents('php://input'), true);
    $id = isset($data['id']) ? intval($data['id']) : 0;
    
    if (!$table || !$data || !$id) {
        response(['success' => false, 'message' => '参数错误']);
    }
    
    // 移除id字段
    unset($data['id']);
    
    try {
        $sets = implode('=?,', array_keys($data)) . '=?';
        $stmt = $conn->prepare("UPDATE {$table} SET {$sets} WHERE id=?");
        
        $values = array_values($data);
        $values[] = $id;
        
        $stmt->execute($values);
        logAction('edit', $type, $id, $data);
        
        response(['success' => true, 'message' => '更新成功']);
    } catch(PDOException $e) {
        response(['success' => false, 'message' => '更新失败: ' . $e->getMessage()]);
    }
}

// 删除数据
function deleteItem($type) {
    $conn = getConnection();
    $table = getTableName($type);
    $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
    
    if (!$table || !$id) {
        response(['success' => false, 'message' => '参数错误']);
    }
    
    try {
        // 先获取要删除的数据
        $stmt = $conn->prepare("SELECT * FROM {$table} WHERE id = ?");
        $stmt->execute([$id]);
        $item = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$item) {
            response(['success' => false, 'message' => '数据不存在']);
        }
        
        // 执行删除
        $stmt = $conn->prepare("DELETE FROM {$table} WHERE id=?");
        $stmt->execute([$id]);
        
        // 记录日志
        logAction('delete', $type, $id, $item);
        
        response(['success' => true, 'message' => '删除成功']);
    } catch(PDOException $e) {
        response(['success' => false, 'message' => '删除失败: ' . $e->getMessage()]);
    }
}

// 获取对应的表名
function getTableName($type) {
    $tables = [
        // 玩家系统
        'users' => 'users',
        'user_attributes' => 'user_attributes',
        'user_map_progress' => 'user_map_progress',
        'user_weapon_slots' => 'user_weapon_slots',
        'user_equipped_items' => 'user_equipped_items',
        'unified_inventory' => 'user_inventories',
        
        // 游戏物品系统
        'game_items' => 'game_items',
        'item_skills' => 'item_skills',
        
        // 角色系统
        'characters' => 'characters',
        
        // 物品掉落系统
        'drops' => 'drops',
        'map_drops' => 'map_drops',
        
        // 地图怪物系统
        'maps' => 'maps',
        'monsters' => 'monsters',
        'map_monsters' => 'map_monsters',
        
        // 秘境系统
        'dungeons' => 'dungeons',
        
        // 公用数据
        'realms' => 'realms',
        'realm_levels' => 'realm_levels',
        
        // 日志系统
        'login_logs' => 'login_logs',
        'admin_logs' => 'admin_logs'
    ];
    
    return isset($tables[$type]) ? $tables[$type] : $type;
}

// 获取搜索字段
function getSearchField($type) {
    $searchFields = [
        'users' => 'username',
        'drops' => 'name',
        'maps' => 'name',
        'monsters' => 'name',
        'dungeons' => 'name',
        'realms' => 'name',
        'user_attributes' => 'user_id',
        'unified_inventory' => 'character_id',
        'characters' => 'character_name',
        'game_items' => 'item_name',
        'item_skills' => 'skill_name'
    ];
    
    return isset($searchFields[$type]) ? $searchFields[$type] : 'id';
} 