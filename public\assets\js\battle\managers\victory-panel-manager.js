/**
 * 🏆 胜利面板管理器
 * 负责管理战斗结束后的胜利面板显示、物品详情、挂机系统等
 * 从script.js分拆出胜利面板相关的方法
 */

class BattleVictoryPanelManager {
    constructor(battleSystem) {
        this.battleSystem = battleSystem;
        console.log('🏆 胜利面板管理器已创建');
    }

    /**
     * 设置数据管理器引用
     */
    setDataManager(dataManager) {
        this.dataManager = dataManager;
        console.log('🔗 胜利面板管理器已关联数据管理器');
    }

    /**
     * 设置UI管理器引用
     */
    setUIManager(uiManager) {
        this.uiManager = uiManager;
        console.log('🔗 胜利面板管理器已关联UI管理器');
    }

    /**
     * 设置奖励管理器引用
     */
    setRewardManager(rewardManager) {
        this.rewardManager = rewardManager;
        console.log('🔗 胜利面板管理器已关联奖励管理器');
    }

    /**
     * 显示胜利面板
     */
    async showVictoryPanel(message, droppedItems) {
        try {
            console.log('🏆 开始显示胜利面板:', {
                message,
                droppedItemsCount: droppedItems?.length || 0,
            });

            // 🔧 修复：确保胜利面板CSS已加载
            await this.loadVictoryPanelCSS();

            // 🔧 修复：清除可能存在的旧面板
            this.closeVictoryPanel();

            // 🆕 提前定义saveResult变量，确保在整个方法中都可以访问
            const saveResult = this.battleSystem.lastSaveResult || {};

            // 🆕 检查是否为竞技场战斗
            const isArenaBattle = this.checkIsArenaBattle();

            if (isArenaBattle) {
                // 🏆 竞技场专用结算界面 - 胜利和失败都使用专用界面
                console.log('🏆 竞技场模式：使用专用结算界面');
                return this.showArenaVictoryPanel(message, saveResult);
            }

            // 🔧 修复：从HTML模板创建胜利面板
            const gameOverlay = this.createVictoryPanelFromTemplate();
            if (!gameOverlay) {
                console.error('❌ 创建胜利面板失败，使用备用方案');
                return this.createSimpleVictoryPanel(message, droppedItems);
            }

            console.log('✅ 胜利面板创建成功:', gameOverlay);
            console.log('游戏覆盖层元素:', gameOverlay, gameOverlay.style.display);

            // 更新标题和副标题
            const victoryTitle = gameOverlay.querySelector('[data-victory-title]');
            const victorySubtitle = gameOverlay.querySelector('[data-victory-subtitle]');

            const isVictory = message.includes('玩家胜利');
            if (victoryTitle) {
                victoryTitle.textContent = isVictory ? '🎉 胜利！' : '💀 失败！';
                victoryTitle.style.color = isVictory ? '#ffd700' : '#ff6b6b';
            }
            if (victorySubtitle) {
                victorySubtitle.textContent = message;
            }

            // 🔧 处理奖励显示
            const rewardsSection = gameOverlay.querySelector('[data-rewards-section]');

            if (message.includes('玩家胜利')) {
                // 胜利时显示奖励
                const spiritStoneReward = gameOverlay.querySelector('[data-exp-reward]');
                const goldReward = gameOverlay.querySelector('[data-gold-reward]');
                const adventureReward = gameOverlay.querySelector('[data-adventure-reward]');
                const recycleWarning = gameOverlay.querySelector('[data-recycle-warning]');

                console.log('=== 奖励区域元素查询调试 ===');
                console.log('rewardsSection 元素:', rewardsSection);
                console.log('spiritStoneReward 元素:', spiritStoneReward);
                console.log('goldReward 元素:', goldReward);
                console.log('adventureReward 元素:', adventureReward);
                console.log('recycleWarning 元素:', recycleWarning);

                // 🔧 新增：详细的调试信息
                console.log('=== 奖励数据详细分析 ===');
                console.log('完整API返回结果:', saveResult);
                console.log('API结果所有字段:', Object.keys(saveResult));

                // 🔧 修复：优先从API返回结果获取奖励，如果没有则从敌人数据计算
                let spiritStoneGained =
                    saveResult.spirit_stones_gained ||
                    saveResult.exp_gained ||
                    saveResult.spiritual_power_gained ||
                    0;
                let goldGained =
                    saveResult.total_gold_gained ||
                    saveResult.gold_gained ||
                    saveResult.silver_gained ||
                    0;

                // 🎲 新增：获取奇遇值信息
                let adventureValueGained = 0;
                let adventureMessage = '';
                if (saveResult.adventure_result && saveResult.adventure_result.success) {
                    adventureValueGained = saveResult.adventure_result.added_value || 0;
                    adventureMessage = saveResult.adventure_result.message || '';
                    console.log('🎲 奇遇值信息:', {
                        gained: adventureValueGained,
                        current: saveResult.adventure_result.current_value,
                        total: 1000,
                        message: adventureMessage,
                    });
                }

                // 🔧 修复：如果API返回的奖励为0或undefined，从敌人数据重新计算
                if (!spiritStoneGained || !goldGained) {
                    const enemyData = this.dataManager.enemyData;
                    if (!spiritStoneGained) {
                        spiritStoneGained = enemyData.spiritStoneReward || 10;
                        console.log('⚠️ API未返回灵石奖励，使用默认值:', spiritStoneGained);
                    }
                    if (!goldGained) {
                        goldGained = enemyData.goldReward || 5;
                        console.log('⚠️ API未返回金币奖励，使用默认值:', goldGained);
                    }
                }

                console.log('奖励字段检查:');
                console.log(
                    '- spirit_stones_gained:',
                    saveResult.spirit_stones_gained,
                    '(类型:',
                    typeof saveResult.spirit_stones_gained,
                    ')'
                );
                console.log(
                    '- exp_gained:',
                    saveResult.exp_gained,
                    '(类型:',
                    typeof saveResult.exp_gained,
                    ')'
                );
                console.log(
                    '- spiritual_power_gained:',
                    saveResult.spiritual_power_gained,
                    '(类型:',
                    typeof saveResult.spiritual_power_gained,
                    ')'
                );
                console.log(
                    '- gold_gained:',
                    saveResult.gold_gained,
                    '(类型:',
                    typeof saveResult.gold_gained,
                    ')'
                );
                console.log(
                    '- silver_gained:',
                    saveResult.silver_gained,
                    '(类型:',
                    typeof saveResult.silver_gained,
                    ')'
                );
                console.log('最终计算结果:');
                console.log(
                    '- spiritStoneGained (灵石):',
                    spiritStoneGained,
                    '(类型:',
                    typeof spiritStoneGained,
                    ')'
                );
                console.log('- goldGained (金币):', goldGained, '(类型:', typeof goldGained, ')');

                // 🔧 检查回收信息
                const hasRecycledItems = saveResult.has_recycled_items;
                const recycleGold = saveResult.recycle_gold || 0;
                const recycledItems = saveResult.recycled_items || [];
                const totalGoldGained = this.battleSystem.lastSaveResult
                    ? this.battleSystem.lastSaveResult.total_gold_gained ||
                      this.battleSystem.lastSaveResult.gold_gained ||
                      goldGained
                    : goldGained;

                console.log('=== 回收信息检查 ===');
                console.log(
                    'has_recycled_items:',
                    hasRecycledItems,
                    '(类型:',
                    typeof hasRecycledItems,
                    ')'
                );
                console.log('recycle_gold:', recycleGold, '(类型:', typeof recycleGold, ')');
                console.log('recycled_items:', recycledItems, '(长度:', recycledItems.length, ')');

                // 🔧 显示奖励区域
                if (rewardsSection) {
                    rewardsSection.style.display = 'block';
                    console.log('✅ 胜利 - 显示奖励区域');

                    if (spiritStoneReward) {
                        spiritStoneReward.textContent = `+${spiritStoneGained}`;
                        console.log('✅ 更新了灵石奖励:', spiritStoneReward.textContent);
                    }

                    if (goldReward) {
                        goldReward.textContent = `+${goldGained}`;
                        console.log('✅ 更新了金币奖励:', goldReward.textContent);
                    }

                    // 🎲 新增：显示奇遇值奖励
                    if (adventureReward) {
                        if (adventureValueGained > 0) {
                            adventureReward.textContent = `+${adventureValueGained}`;
                            adventureReward.style.color = '#f39c12'; // 橙色高亮
                            console.log('✅ 更新了奇遇值奖励:', adventureReward.textContent);

                            // 🎲 同步更新战斗界面的奇遇值显示
                            if (saveResult.adventure_result && this.dataManager) {
                                this.dataManager.updateAdventureDisplay({
                                    current_value: saveResult.adventure_result.current_value,
                                    progress_percent:
                                        (saveResult.adventure_result.current_value / 1000) * 100,
                                });
                            }

                            // 🎲 新增：检查是否触发了奇遇事件
                            if (
                                saveResult.adventure_result &&
                                saveResult.adventure_result.adventure_event
                            ) {
                                console.log(
                                    '🎲 检测到奇遇事件触发:',
                                    saveResult.adventure_result.adventure_event
                                );
                                // 延迟显示奇遇事件弹窗，让胜利面板先显示
                                setTimeout(() => {
                                    this.showAdventureEventPopup(
                                        saveResult.adventure_result.adventure_event
                                    );
                                }, 1500);
                            }
                        } else {
                            adventureReward.textContent = '+0';
                            adventureReward.style.color = '#95a5a6'; // 灰色
                            console.log('ℹ️ 本次战斗未获得奇遇值');
                        }
                    }

                    // 🔧 新增：显示回收警告
                    if (recycleWarning && hasRecycledItems) {
                        recycleWarning.style.display = 'block';

                        // 🆕 构建回收警告信息
                        let warningText = '⚠️ ';
                        const messages = [];

                        // 背包空间不足回收
                        if (recycledItems && recycledItems.length > 0) {
                            messages.push(
                                `背包空间不足，已自动回收 ${recycledItems.length} 件装备获得 ${recycleGold} 金币`
                            );
                        }

                        // 拾取过滤回收
                        const autoRecycledItems = saveResult.auto_recycled_items || [];
                        const autoRecycleGold = saveResult.auto_recycle_gold || 0;
                        if (autoRecycledItems.length > 0) {
                            messages.push(
                                `因拾取设置过滤，已自动回收 ${autoRecycledItems.length} 件装备获得 ${autoRecycleGold} 金币`
                            );
                        }

                        warningText += messages.join('；');
                        recycleWarning.textContent = warningText;
                        console.log('🚨 显示回收警告:', recycleWarning.textContent);
                    } else if (recycleWarning) {
                        recycleWarning.style.display = 'none';
                        console.log('ℹ️ 隐藏回收警告（无回收物品）');
                    }

                    console.log('✅ 奖励数据已显示:', {
                        spiritStoneGained,
                        goldGained,
                        hasRecycledItems,
                        recycleGold,
                    });
                } else {
                    console.error('❌ 找不到奖励区域元素，尝试手动创建');
                    this.rewardManager.createRewardsSection(
                        gameOverlay,
                        spiritStoneGained,
                        goldGained
                    );
                }
            } else {
                // 🏆 检查是否为竞技场战斗失败
                const isArenaBattleFailure = this.checkIsArenaBattle();

                if (isArenaBattleFailure) {
                    // 🔧 修复：竞技场失败仍显示灵石奖励
                    if (rewardsSection) {
                        rewardsSection.style.display = 'block';
                        console.log('🏆 竞技场失败 - 显示灵石奖励');

                        // 获取竞技场失败奖励数据
                        const failureReward = saveResult.rewards?.spirit_stones || 50; // 默认50灵石

                        // 🔧 修复：查找正确的奖励元素并显示
                        const spiritStoneElement =
                            rewardsSection.querySelector('[data-exp-reward]') ||
                            rewardsSection.querySelector('.reward-value.spirit-stones') ||
                            rewardsSection.querySelector('.spirit-stone-reward');
                        const goldElement =
                            rewardsSection.querySelector('[data-gold-reward]') ||
                            rewardsSection.querySelector('.gold-reward');
                        const adventureElement =
                            rewardsSection.querySelector('[data-adventure-reward]') ||
                            rewardsSection.querySelector('.adventure-reward');

                        if (spiritStoneElement) {
                            spiritStoneElement.textContent = `+${failureReward}`;
                            spiritStoneElement.style.color = '#ff6b6b'; // 失败时用红色
                            console.log('✅ 竞技场失败 - 设置灵石奖励:', failureReward);
                        } else {
                            console.warn('⚠️ 未找到灵石奖励元素，尝试创建');
                            // 如果找不到元素，创建一个简单的奖励显示
                            rewardsSection.innerHTML = `
                                <div class="arena-failure-rewards" style="padding: 10px; text-align: center;">
                                    <div style="color: #ff6b6b; font-size: 14px; margin-bottom: 5px;">💫 论道败阵奖励</div>
                                    <div style="color: #4CAF50; font-size: 16px;">💎 +${failureReward} 灵石</div>
                                </div>
                            `;
                        }

                        if (goldElement) {
                            goldElement.textContent = '+0';
                            goldElement.style.color = '#888';
                        }
                        if (adventureElement) {
                            adventureElement.textContent = '+0';
                            adventureElement.style.color = '#888';
                        }

                        console.log('🏆 竞技场失败奖励显示完成:', failureReward, '灵石');
                    } else {
                        console.warn('⚠️ 竞技场战败但未找到奖励区域，创建简单奖励显示');
                        // 如果没有奖励区域，在面板中创建
                        const gameOverlay = document.querySelector('.game-overlay');
                        if (gameOverlay) {
                            const arenaFailureDiv = document.createElement('div');
                            arenaFailureDiv.className = 'arena-failure-section';
                            arenaFailureDiv.style.cssText = `
                                padding: 15px;
                                margin: 10px 0;
                                background: rgba(255, 107, 107, 0.1);
                                border: 1px solid #ff6b6b;
                                border-radius: 8px;
                                text-align: center;
                            `;
                            arenaFailureDiv.innerHTML = `
                                <div style="color: #ff6b6b; font-size: 16px; margin-bottom: 8px;">💫 论道败阵</div>
                                <div style="color: #4CAF50; font-size: 18px; font-weight: bold;">💎 +${
                                    saveResult.rewards?.spirit_stones || 50
                                } 灵石</div>
                                <div style="color: #888; font-size: 12px; margin-top: 5px;">败阵犹有所得，再接再厉！</div>
                            `;

                            // 插入到按钮区域之前
                            const buttonArea =
                                gameOverlay.querySelector('.button-area') ||
                                gameOverlay.querySelector('.victory-panel');
                            if (buttonArea && buttonArea.parentNode) {
                                buttonArea.parentNode.insertBefore(arenaFailureDiv, buttonArea);
                            } else {
                                gameOverlay.appendChild(arenaFailureDiv);
                            }

                            console.log('✅ 创建竞技场失败奖励显示区域');
                        }
                    }
                } else {
                    // 普通战斗失败时隐藏奖励区域
                    if (rewardsSection) {
                        rewardsSection.style.display = 'none';
                        console.log('✅ 普通战斗失败 - 隐藏奖励区域');
                    }
                    console.log('💀 普通战斗失败，不显示奖励');
                }
            }

            // 🔧 修复：处理掉落物品区域 - 只有胜利时才显示
            const dropsSection = gameOverlay.querySelector('[data-drops-section]');

            // 🆕 使用完整的掉落物品数据（包括回收的物品）
            const allDroppedItems = saveResult.all_dropped_items || droppedItems || [];
            console.log('=== 掉落物品数据检查 ===');
            console.log('传入的droppedItems:', droppedItems?.length || 0, '件');
            console.log('API返回的all_dropped_items:', allDroppedItems?.length || 0, '件');
            console.log('完整掉落数据:', allDroppedItems);

            if (message.includes('玩家胜利') && allDroppedItems && allDroppedItems.length > 0) {
                // 胜利且有掉落物品时才显示
                const dropsTitle = gameOverlay.querySelector('[data-drops-title]');
                const dropsList = gameOverlay.querySelector('[data-drops-list]');

                if (dropsSection && dropsTitle && dropsList) {
                    dropsSection.style.display = 'block';
                    dropsTitle.textContent = `获得战利品 (${allDroppedItems.length}件)`;

                    // 🔧 完全重写：完全按照背包物品的样式渲染战利品（包括图片和名称）
                    for (const [index, dropData] of allDroppedItems.entries()) {
                        // 🔧 不使用模板，直接创建元素（与背包物品一致）
                        const itemDisplay = document.createElement('div');
                        itemDisplay.className = 'drop-item';

                        // 🔧 添加品质样式（使用rarity-前缀，与新样式匹配）
                        const rarity = dropData.rarity || 'common';
                        itemDisplay.classList.add(`rarity-${rarity}`);

                        // 🆕 检查物品是否被回收
                        // 🔧 检查物品是否被回收（支持多种判断方式）
                        const isRecycled =
                            dropData.recycled === true ||
                            dropData.status === 'recycled' ||
                            (saveResult.recycled_items &&
                                saveResult.recycled_items.includes(dropData.name)) ||
                            (saveResult.auto_recycled_items &&
                                saveResult.auto_recycled_items.includes(dropData.name));

                        // 🆕 如果被回收，添加回收样式类
                        if (isRecycled) {
                            itemDisplay.classList.add('item-recycled');
                            console.log(`♻️ 物品"${dropData.name}"已被标记为回收`);
                        }

                        // 🔧 获取物品图片URL
                        const itemImageUrl = await this.getItemImageUrl(dropData);

                        // 🚫 如果没有图片就直接报错，不显示任何图片
                        if (!itemImageUrl) {
                            console.error(
                                '❌ [Victory Panel] 物品图片为空，不显示图片:',
                                dropData.name
                            );
                        }

                        // 🔧 设置数据属性
                        itemDisplay.setAttribute('data-item-id', dropData.id || 0);
                        itemDisplay.setAttribute('data-item-name', dropData.name || '未知物品');
                        itemDisplay.setAttribute('data-item-quantity', dropData.quantity || 1);
                        itemDisplay.setAttribute('data-item-rarity', rarity);
                        itemDisplay.setAttribute(
                            'data-item-recycled',
                            isRecycled ? 'true' : 'false'
                        );

                        // 🔧 修复：分步设置图片，避免模板字符串中的复杂CSS

                        // 先设置基础HTML结构
                        itemDisplay.innerHTML = `
                        ${
                            dropData.quantity && dropData.quantity > 1
                                ? `<div class="item-quantity">${dropData.quantity}</div>`
                                : ''
                        }
                        <div class="item-image"></div>
                        <div class="item-name-box">
                            <div class="item-name-text">
                                ${dropData.name || '未知物品'}
                            </div>
                        </div>
                        ${
                            isRecycled
                                ? (() => {
                                      // 🔧 判断回收原因，决定标签样式和文字
                                      const isFilterRecycled =
                                          dropData.recycle_reason === 'pickup_filter' ||
                                          (saveResult.auto_recycled_items &&
                                              saveResult.auto_recycled_items.includes(
                                                  dropData.name
                                              ));
                                      const badgeClass = isFilterRecycled
                                          ? 'sold-badge'
                                          : 'recycled-badge';
                                      const badgeText = isFilterRecycled ? '已出售' : '已回收';
                                      return `<div class="${badgeClass}">${badgeText}</div>`;
                                  })()
                                : ''
                        }
                    `;

                        // 🔧 分别设置图片和名称区域的样式
                        const itemImageDiv = itemDisplay.querySelector('.item-image');
                        const itemNameBox = itemDisplay.querySelector('.item-name-box');
                        const itemNameText = itemDisplay.querySelector('.item-name-text');

                        if (itemImageDiv) {
                            // 🚫 取消默认图片逻辑，如果没有图片就不显示
                            if (!itemImageUrl) {
                                // 不设置任何背景图片，让图片区域为空
                                itemImageDiv.style.cssText = `
                                width: 100%;
                                height: 60%;
                                position: absolute;
                                top: 0;
                                left: 0;
                                z-index: 1;
                                background: transparent;
                                border: 2px dashed #ccc;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                color: #999;
                                font-size: 12px;
                                text-align: center;
                            `;
                                itemImageDiv.innerHTML = '❌<br>无图片';
                            } else {
                                // 🔧 直接加载图片，失败就报错
                                const testImg = new Image();

                                testImg.onload = () => {
                                    itemImageDiv.style.cssText = `
                                    background-image: url('${itemImageUrl}');
                                    background-size: contain;
                                    background-repeat: no-repeat;
                                    background-position: center;
                                    width: 100%;
                                    height: 60%;
                                    position: absolute;
                                    top: 0;
                                    left: 0;
                                    z-index: 1;
                                `;
                                    console.log('✅ [Victory Panel] 图片加载成功:', itemImageUrl);
                                };

                                testImg.onerror = () => {
                                    console.error('❌ [Victory Panel] 图片加载失败:', itemImageUrl);
                                    itemImageDiv.style.cssText = `
                                    width: 100%;
                                    height: 60%;
                                    position: absolute;
                                    top: 0;
                                    left: 0;
                                    z-index: 1;
                                    background: transparent;
                                    border: 2px dashed red;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    color: red;
                                    font-size: 12px;
                                    text-align: center;
                                `;
                                    itemImageDiv.innerHTML = '🚫<br>加载失败';
                                };

                                testImg.src = itemImageUrl;
                            }
                        }

                        if (itemNameBox) {
                            itemNameBox.style.cssText = `
                            position: absolute;
                            bottom: 1px;
                            left: 1px;
                            right: 1px;
                            height: 40%;
                            background: linear-gradient(to top, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.7), transparent);
                            display: flex;
                            align-items: flex-end;
                            justify-content: center;
                            z-index: 2;
                            border-radius: 0 0 10px 10px;
                        `;
                        }

                        if (itemNameText) {
                            itemNameText.style.cssText = `
                            color: #ecf0f1;
                            font-weight: bold;
                            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
                            line-height: 1.1;
                            text-align: center;
                            word-break: break-all;
                            display: -webkit-box;
                            -webkit-line-clamp: 2;
                            -webkit-box-orient: vertical;
                            max-height: 14px;
                            font-size: 8px;
                            padding: 2px;
                        `;
                        }

                        // 🆕 如果被回收，添加视觉效果
                        if (isRecycled) {
                            itemDisplay.style.filter = 'grayscale(50%) brightness(0.7)';
                            itemDisplay.style.opacity = '0.8';
                            itemDisplay.style.cursor = 'default'; // 回收物品不可点击

                            // 🆕 根据回收原因添加不同的悬停提示
                            if (dropData.recycle_reason === 'pickup_filter') {
                                itemDisplay.title = '此物品因拾取设置过滤而被自动出售，已获得金币';
                            } else {
                                itemDisplay.title = '此物品因背包空间不足而被自动回收，已获得金币';
                            }
                        } else {
                            // 🔧 点击事件（只有未回收的物品才可以点击查看详情）
                            itemDisplay.style.cursor = 'pointer';
                            itemDisplay.addEventListener('click', () => {
                                console.log('🔍 点击查看物品详情:', dropData);
                                this.showDropItemDetail(dropData);
                            });
                        }

                        dropsList.appendChild(itemDisplay);

                        console.log(
                            `🖼️ 战利品创建完成 - 名称: ${dropData.name}, 品质: ${rarity}, 图片: ${itemImageUrl}, 是否回收: ${isRecycled}`
                        );
                    }

                    console.log('✅ 掉落物品区域已显示，物品数量:', droppedItems.length);
                } else {
                    console.log('ℹ️ 无掉落物品或查找模板失败');
                }
            } else {
                // 失败或无掉落时隐藏掉落区域
                if (dropsSection) {
                    dropsSection.style.display = 'none';
                    console.log('✅ 隐藏掉落物品区域');
                }
            }

            // 🔧 修复：恢复原版的按钮状态设置逻辑
            // 在设置按钮事件前，先设置按钮的显示状态
            const autoBattleButton = gameOverlay.querySelector('[data-auto-battle-button]');
            const nextStageButton = gameOverlay.querySelector('[data-next-stage-button]');
            const exitBattleButton = gameOverlay.querySelector('[data-exit-battle-button]');

            // 🔧 修复：下一层按钮逻辑 - 只有胜利时可点击
            if (nextStageButton) {
                if (message.includes('玩家胜利')) {
                    // 🔧 新增：检查是否达到最大层数
                    const currentArea = this.dataManager.getCurrentArea();
                    const currentStage = parseInt(this.dataManager.currentStage) || 1;

                    // 🔧 修复：优先使用后端返回的max_stages，其次使用total，最后使用地图ID配置
                    let maxStage = 140; // 默认值

                    if (
                        this.dataManager.currentMapData &&
                        this.dataManager.currentMapData.max_stages
                    ) {
                        maxStage = parseInt(this.dataManager.currentMapData.max_stages);
                    } else if (currentArea && currentArea.total) {
                        maxStage = parseInt(currentArea.total);
                    } else if (currentArea && currentArea.mapId) {
                        // 使用地图ID配置作为备用
                        const mapStages = {
                            1: 60, // 太乙峰
                            2: 90, // 碧水寒潭
                            3: 120, // 赤焰谷
                            4: 140, // 幽冥鬼域
                            5: 150, // 青云仙山
                            6: 140, // 星辰古战场
                            7: 140, // 混元虚空
                            8: 175, // 洪荒秘境
                        };
                        maxStage = mapStages[currentArea.mapId] || 140;
                    }

                    console.log(`🎯 层数检查: 当前${currentStage}层, 最大${maxStage}层`);

                    if (currentStage >= maxStage) {
                        // 🔧 达到最大层数，禁用下一层按钮
                        nextStageButton.disabled = true;
                        nextStageButton.classList.add('max-stage-disabled');
                        console.log('🚫 已达到地图最大层数，禁用下一层按钮');

                        // 可选：修改按钮文字提示
                        const nextStageText = nextStageButton.querySelector('span');
                        if (nextStageText) {
                            nextStageText.textContent = '已通关';
                        }
                    } else {
                        // 🔧 未达到最大层数，启用下一层按钮
                        nextStageButton.disabled = false;
                        nextStageButton.classList.remove('max-stage-disabled');
                        console.log('✅ 未达到最大层数，启用下一层按钮');
                    }
                } else {
                    // 🔧 修复：失败时启用上一层按钮（在setupVictoryPanelButtons中会设置为"上一层"）
                    nextStageButton.disabled = false;
                    nextStageButton.classList.remove('disabled', 'max-stage-disabled');
                    console.log('✅ 战斗失败，启用上一层按钮');
                }
            }

            // 绑定按钮事件
            this.setupVictoryPanelButtons(gameOverlay, message);
        } catch (error) {
            console.error('💥 显示胜利面板时出错:', error);

            // 🔧 修复：出错时使用简单备用面板
            console.log('🛠️ 使用简单备用胜利面板');
            this.createSimpleVictoryPanel(message, droppedItems);
        }
    }

    /**
     * 🔧 新增：从HTML模板创建胜利面板
     */
    createVictoryPanelFromTemplate() {
        try {
            // 查找胜利面板模板
            const template = document.querySelector('#victory-panel-template');
            if (!template) {
                console.error('❌ 找不到胜利面板模板 #victory-panel-template');
                return null;
            }

            // 克隆模板内容
            const clone = template.content.cloneNode(true);
            const gameOverlay = clone.querySelector('.game-overlay');

            if (!gameOverlay) {
                console.error('❌ 模板中找不到 .game-overlay 元素');
                return null;
            }

            // 添加到页面
            document.body.appendChild(clone);

            // 返回已添加到DOM的元素
            const addedOverlay = document.querySelector('.game-overlay');
            console.log('✅ 成功从模板创建胜利面板');

            return addedOverlay;
        } catch (error) {
            console.error('❌ 从模板创建胜利面板失败:', error);
            return null;
        }
    }

    /**
     * 🔧 新增：退出战斗方法
     */
    exitBattle() {
        try {
            console.log('🚪 退出战斗');

            // 关闭胜利面板
            this.closeVictoryPanel();

            // 🔧 修复：完全清除挂机状态
            console.log('🛑 退出战斗：清除挂机状态');

            // 停止挂机模式（如果正在运行）
            if (this.battleSystem.isAutoBattleMode) {
                this.battleSystem.stopAutoBattle();
            }

            // 清除挂机管理器状态
            if (this.battleSystem.autoBattleManager) {
                this.battleSystem.autoBattleManager.stopAutoBattle();
            }

            // 清除localStorage中的挂机状态
            localStorage.removeItem('autoBattleMode');
            // 🆕 退出战斗时清除失败标识
            localStorage.removeItem('autoBattleFailedFlag');
            console.log('🗑️ 已清除localStorage中的挂机状态和失败标识');

            // 清除倒计时定时器
            if (this.battleSystem.autoBattleCountdown) {
                clearInterval(this.battleSystem.autoBattleCountdown);
                this.battleSystem.autoBattleCountdown = null;
                console.log('🗑️ 已清除挂机倒计时定时器');
            }

            // 重置战斗系统挂机状态
            this.battleSystem.isAutoBattleMode = false;

            // 🎵 切换回背景音乐
            if (window.musicManager) {
                window.musicManager.playBackgroundMusic();
            }

            // 🏆 检查是否为竞技场模式，决定返回页面
            const urlParams = new URLSearchParams(window.location.search);
            const isArena = urlParams.get('arena') === '1';

            if (isArena) {
                console.log('🏆 竞技场战斗结束，返回竞技场界面');
                window.location.href = 'immortal_arena.html';
            } else {
                console.log('✅ 挂机状态完全清除，返回冒险地图');
                window.location.href = 'adventure.html';
            }
        } catch (error) {
            console.error('❌ 退出战斗时出错:', error);
            // 即使出错也要尝试清除状态并返回
            try {
                localStorage.removeItem('autoBattleMode');
                localStorage.removeItem('autoBattleFailedFlag');
                if (this.battleSystem.autoBattleCountdown) {
                    clearInterval(this.battleSystem.autoBattleCountdown);
                }
            } catch (cleanupError) {
                console.error('❌ 清理状态时出错:', cleanupError);
            }

            // 🏆 错误情况下也要检查竞技场模式
            const urlParams = new URLSearchParams(window.location.search);
            const isArena = urlParams.get('arena') === '1';

            if (isArena) {
                window.location.href = 'immortal_arena.html';
            } else {
                window.location.href = 'adventure.html';
            }
        }
    }

    /**
     * 🆕 处理挂机逻辑（简化版）
     */
    async handleDefeatAutoBattle(message) {
        console.log('🤖 开始挂机，在本层循环:', message);

        try {
            // 🔧 简化：失败和胜利都在本层挂机，不复杂处理
            if (this.battleSystem.autoBattleManager) {
                await this.battleSystem.autoBattleManager.handleBattleFailure();
            }

            // 设置挂机模式
            this.battleSystem.isAutoBattleMode = true;

            // 关闭当前面板
            this.closeVictoryPanel();

            // 启动挂机倒计时，开始循环当前层
            this.startAutoBattleMode();

            console.log('✅ 挂机已启动，将在当前层循环');
        } catch (error) {
            console.error('❌ 挂机启动失败:', error);
            alert('挂机启动失败，请重试');
        }
    }

    /**
     * 设置胜利面板按钮事件
     */
    setupVictoryPanelButtons(gameOverlay, message = '') {
        console.log('🔧 设置胜利面板按钮事件...');

        // 🏆 检查是否为竞技场模式
        const urlParams = new URLSearchParams(window.location.search);
        const isArena = urlParams.get('arena') === '1';

        const autoBattleButton = gameOverlay.querySelector('[data-auto-battle-button]');
        const nextStageButton = gameOverlay.querySelector('[data-next-stage-button]');
        const exitBattleButton = gameOverlay.querySelector('[data-exit-battle-button]');

        console.log('🔍 按钮查询结果:', {
            isArena: isArena,
            autoBattleButton: !!autoBattleButton,
            nextStageButton: !!nextStageButton,
            exitBattleButton: !!exitBattleButton,
        });

        // 🏆 竞技场模式：完全不同的按钮逻辑
        if (isArena) {
            console.log('🏆 竞技场模式：使用专用按钮逻辑，不使用历练系统按钮');

            // 🚫 隐藏所有历练相关按钮
            if (autoBattleButton) {
                autoBattleButton.style.display = 'none';
                console.log('🚫 隐藏挂机按钮（竞技场不需要）');
            }
            if (nextStageButton) {
                nextStageButton.style.display = 'none';
                console.log('🚫 隐藏下一层按钮（竞技场不需要）');
            }

            // 🏆 只保留退出按钮，修改为返回竞技场
            if (exitBattleButton) {
                const exitText = exitBattleButton.querySelector('span');
                if (exitText) {
                    exitText.textContent = '返回竞技场';
                }

                exitBattleButton.addEventListener('click', () => {
                    console.log('🏆 竞技场：点击返回竞技场');
                    this.exitBattle();
                });
                console.log('✅ 竞技场退出按钮配置完成');
            }

            // 🏆 竞技场专用逻辑，不执行历练系统的按钮绑定
            console.log('🏆 竞技场按钮配置完成，跳过历练系统逻辑');
            return;
        }

        // 🎯 修复：恢复挂机状态的按钮显示
        if (autoBattleButton) {
            const autoBattleText = autoBattleButton.querySelector('[data-auto-battle-text]');
            const countdownDiv = autoBattleButton.querySelector('[data-auto-battle-countdown]');

            // 🔧 简化：从localStorage读取挂机状态
            const savedAutoBattleMode = localStorage.getItem('autoBattleMode') === 'true';
            this.battleSystem.isAutoBattleMode = savedAutoBattleMode;

            console.log('🤖 胜利面板中恢复挂机状态:', {
                localStorage值: localStorage.getItem('autoBattleMode'),
                isAutoBattleMode: this.battleSystem.isAutoBattleMode,
                倒计时元素: !!countdownDiv,
            });

            if (this.battleSystem.isAutoBattleMode) {
                // 如果当前是挂机模式，固定显示"停止挂机"
                autoBattleButton.classList.add('stop-mode');
                autoBattleButton.classList.remove('paused-mode');
                if (autoBattleText) {
                    autoBattleText.textContent = '停止挂机';
                }
                console.log('🤖 恢复挂机按钮状态: 停止挂机');

                // 🔧 关键修复：挂机模式恢复后自动启动倒计时
                if (countdownDiv) {
                    console.log('🚀 挂机模式恢复，自动启动倒计时');
                    this.startAutoBattleCountdown(countdownDiv);
                } else {
                    console.error('❌ 找不到倒计时元素，无法启动自动挂机');
                }
            } else {
                // 非挂机状态：显示"开始挂机"
                autoBattleButton.classList.remove('stop-mode');
                autoBattleButton.classList.remove('paused-mode');
                if (autoBattleText) {
                    autoBattleText.textContent = '开始挂机';
                }
                console.log('🤖 恢复挂机按钮状态: 开始挂机');
            }

            // 隐藏循环模式提示
            const modeHint = gameOverlay.querySelector('[data-auto-battle-mode-hint]');
            if (modeHint) {
                modeHint.style.display = 'none';
            }
        }

        // 🔧 添加下一层按钮事件
        const nextStageBtn = gameOverlay.querySelector('.next-stage-button');
        const nextStageText =
            gameOverlay.querySelector('[data-next-stage-text]') ||
            nextStageBtn?.querySelector('span') ||
            nextStageBtn;

        if (nextStageBtn && nextStageText) {
            // 🆕 根据胜利/失败状态设置按钮文字
            const isVictory = message && (message.includes('胜利') || message.includes('你赢了'));
            const isDefeat = message && (message.includes('失败') || message.includes('你败了'));

            // 🔧 修复：确保按钮在失败状态下可以点击
            nextStageBtn.disabled = false;
            nextStageBtn.classList.remove('disabled', 'max-stage-disabled');

            if (isDefeat) {
                nextStageText.textContent = '上一层';
                console.log('🔻 失败状态：按钮设置为"上一层"');
            } else {
                nextStageText.textContent = '下一层';
                console.log('🎉 胜利状态：按钮设置为"下一层"');
            }

            nextStageBtn.addEventListener('click', event => {
                // 防止事件冒泡
                event.preventDefault();
                event.stopPropagation();

                console.log('🎯 点击层数切换按钮');

                // 使用立即执行的异步函数包装，避免返回Promise
                (async () => {
                    try {
                        if (isDefeat) {
                            // 🔻 失败状态：回退到上一层
                            await this.goToPreviousStage();
                        } else {
                            // 🎉 胜利状态：前进到下一层
                            await this.goToNextStage();
                        }
                    } catch (error) {
                        console.error('❌ 层数切换失败:', error);
                    }
                })();
            });

            console.log('✅ 层数切换按钮事件已绑定');
        } else {
            console.error('❌ 找不到层数切换按钮元素');
        }

        // 🔧 修复：确保挂机按钮在失败状态下可以点击
        if (autoBattleButton) {
            autoBattleButton.disabled = false;
            autoBattleButton.classList.remove('disabled');

            autoBattleButton.addEventListener('click', event => {
                // 防止事件冒泡
                event.preventDefault();
                event.stopPropagation();

                console.log('🤖 点击挂机按钮');

                // 使用立即执行的异步函数包装，避免返回Promise
                (async () => {
                    try {
                        // 🆕 检查当前是否是失败状态
                        const isDefeatState = message && message.includes('失败');
                        const isVictoryState = message && message.includes('胜利');

                        // 🆕 检查是否处于暂停状态
                        const isAutoBattlePaused =
                            this.battleSystem.autoBattleManager &&
                            this.battleSystem.autoBattleManager.isAutoBattlePausedState();

                        if (this.battleSystem.isAutoBattleMode) {
                            // 停止挂机
                            this.battleSystem.isAutoBattleMode = false;
                            if (this.battleSystem.autoBattleManager) {
                                this.battleSystem.autoBattleManager.stopAutoBattle();
                            }
                            console.log('🛑 停止挂机模式');
                        } else {
                            // 开始挂机：在本层循环
                            console.log('🤖 开始挂机：在本层循环');
                            this.startAutoBattleMode();
                        }
                    } catch (error) {
                        console.error('❌ 挂机操作失败:', error);
                    }
                })();
            });
            console.log('✅ 挂机按钮事件已绑定');
        } else {
            console.error('❌ 找不到挂机按钮元素');
        }

        // 🔧 修复：确保退出按钮在失败状态下可以点击
        if (exitBattleButton) {
            exitBattleButton.disabled = false;
            exitBattleButton.classList.remove('disabled');

            exitBattleButton.addEventListener('click', () => {
                console.log('🚪 点击退出战斗按钮');
                this.exitBattle();
            });
            console.log('✅ 退出战斗按钮事件已绑定');
        } else {
            console.error('❌ 找不到退出战斗按钮元素');
        }
    }

    /**
     * 显示掉落物品详情
     */
    async showDropItemDetail(dropData) {
        console.log('🔍 开始显示掉落物品详情:', dropData);
        console.log('🔍 dropData的类型:', typeof dropData);
        console.log('🔍 dropData包含的字段:', Object.keys(dropData || {}));

        try {
            // 🔧 修复：防止重复打开
            const existingPopup = document.getElementById('item-detail-popup');
            if (existingPopup) {
                console.log('ℹ️ 物品详情弹窗已存在，先关闭');
                existingPopup.remove();
            }

            // 🔧 修复：使用奖励管理器构造物品详情（现在是异步方法）
            console.log('🔍 原始掉落数据:', dropData);
            const itemDetail = await this.rewardManager.constructItemDetailFromDrop(dropData);
            console.log('🎯 构造的物品详情:', itemDetail);
            console.log('🎯 物品境界需求字段:', {
                realm_requirement: itemDetail.realm_requirement,
                level_requirement: itemDetail.level_requirement,
            });

            // 🔧 修复：调用全局ItemDetailPopup组件显示物品详情
            if (typeof window.ItemDetailPopup !== 'undefined' && window.ItemDetailPopup.show) {
                console.log('✅ 使用全局ItemDetailPopup组件显示物品详情');

                // 🔧 新增：检查是否是回收的物品
                const isRecycled =
                    this.battleSystem.lastSaveResult &&
                    this.battleSystem.lastSaveResult.recycled_items &&
                    this.battleSystem.lastSaveResult.recycled_items.some(
                        item => item.name === dropData.name && item.rarity === dropData.rarity
                    );

                console.log('🔍 检查物品是否被回收:', {
                    isRecycled,
                    lastSaveResult: this.battleSystem.lastSaveResult,
                    recycledItems: this.battleSystem.lastSaveResult
                        ? this.battleSystem.lastSaveResult.recycled_items
                        : 'N/A',
                });

                // 🔧 修复：战利品显示应该显示境界需求但不显示操作按钮
                // 🚨 重要：移除await，因为show方法不是异步的
                window.ItemDetailPopup.show(itemDetail, {
                    isBattleContext: false, // 🔧 允许显示境界需求信息
                    showEquip: false, // 🔧 战利品不显示装备按钮
                    showUnequip: false, // 🔧 战利品不显示卸下按钮
                    showRecycle: false, // 🔧 战利品不显示回收按钮
                    showUse: false, // 🔧 战利品不显示使用按钮
                    showRepair: false, // 🔧 战利品不显示修复按钮
                    isDroppedItem: true, // 🔧 标记为掉落物品
                    originalDropData: dropData, // 🔧 保留原始掉落数据
                    dropDisplayOnly: true, // 🔧 新增：仅用于展示的标识
                    forceShowRealmRequirement: true, // 🔧 强制显示境界需求
                });

                console.log('🎯 战利品详情显示配置:', {
                    isBattleContext: false,
                    showEquip: false,
                    showUnequip: false,
                    showRecycle: false,
                    showUse: false,
                    showRepair: false,
                    isDroppedItem: true,
                    dropDisplayOnly: true,
                });

                // 🔧 新增：如果物品被回收了，显示回收遮罩
                if (isRecycled) {
                    console.log('🗑️ 物品已被回收，添加遮罩层');
                    setTimeout(() => {
                        this.rewardManager.addRecycledOverlay();
                    }, 100); // 稍微延迟以确保弹窗完全显示
                }
            } else {
                console.error('❌ ItemDetailPopup组件未加载或未定义');
                throw new Error('ItemDetailPopup组件不可用');
            }
        } catch (error) {
            console.error('💥 显示掉落物品详情时出错:', error);

            // 🔧 备用：创建简单的物品详情显示
            this.showSimpleItemDetail(dropData);
        }
    }

    /**
     * 简单物品详情显示（备用）
     */
    showSimpleItemDetail(dropData) {
        const popup = document.createElement('div');
        popup.id = 'simple-item-detail';
        popup.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #1a1a2e;
            border: 2px solid #ffd700;
            border-radius: 8px;
            padding: 20px;
            z-index: 10000;
            color: white;
            max-width: 400px;
        `;

        popup.innerHTML = `
            <h3 style="color: #ffd700; margin-top: 0;">${dropData.name || '未知物品'}</h3>
            <p><strong>品质:</strong> ${dropData.rarity || 'common'}</p>
            <p><strong>数量:</strong> ${dropData.quantity || 1}</p>
            <p><strong>描述:</strong> ${dropData.description || '暂无描述'}</p>
            <button onclick="this.parentElement.remove()" style="
                background: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                cursor: pointer;
                margin-top: 10px;
            ">关闭</button>
        `;

        document.body.appendChild(popup);
    }

    /**
     * 创建简单胜利面板（备用）
     */
    createSimpleVictoryPanel(message, droppedItems) {
        console.log('🛠️ 创建简单胜利面板:', {
            message,
            droppedItemsCount: droppedItems?.length || 0,
        });

        try {
            // 🔧 修复：清除可能存在的旧面板
            const existingOverlay = document.querySelector('.simple-victory-overlay');
            if (existingOverlay) {
                existingOverlay.remove();
                console.log('ℹ️ 移除了现有的简单胜利面板');
            }

            // 创建覆盖层
            const overlay = document.createElement('div');
            overlay.className = 'simple-victory-overlay';
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 9999;
                backdrop-filter: blur(5px);
            `;

            // 创建面板内容
            const panel = document.createElement('div');
            panel.className = 'simple-victory-panel';
            panel.style.cssText = `
                background: linear-gradient(135deg, #1a1a2e, #16213e);
                border: 3px solid #ffd700;
                border-radius: 12px;
                padding: 30px;
                text-align: center;
                color: white;
                box-shadow: 0 0 30px rgba(255, 215, 0, 0.3);
                max-width: 500px;
                width: 90%;
                max-height: 80vh;
                overflow-y: auto;
            `;

            // 🔧 根据结果显示不同的标题和颜色
            const isVictory = message.includes('玩家胜利');
            const titleText = isVictory ? '🎉 胜利！' : '💀 失败！';
            const titleColor = isVictory ? '#ffd700' : '#ff6b6b';

            let panelHTML = `
                <h2 style="color: ${titleColor}; margin-top: 0; font-size: 24px; text-shadow: 0 0 10px ${titleColor};">
                    ${titleText}
                </h2>
                <p style="font-size: 16px; margin-bottom: 20px; color: #ccc;">
                    ${message}
                </p>
            `;

            // 🔧 检查是否为竞技场战斗
            const isArenaBattle = this.checkIsArenaBattle();

            // 🔧 胜利时或竞技场失败时都显示奖励
            if (isVictory || isArenaBattle) {
                // 显示奖励信息
                const enemyData = this.dataManager.enemyData;
                let expGained, goldGained;

                if (isArenaBattle && !isVictory) {
                    // 竞技场失败奖励
                    const saveResult = this.battleSystem.lastSaveResult || {};
                    expGained = saveResult.rewards?.spirit_stones || 50; // 失败50灵石
                    goldGained = 0; // 竞技场不给金币
                } else {
                    // 普通战斗或竞技场胜利奖励
                    expGained = enemyData.spiritStoneReward || this.dataManager.currentStage * 10;
                    goldGained = enemyData.goldReward || this.dataManager.currentStage * 5;
                }

                const rewardTitle = isArenaBattle
                    ? isVictory
                        ? '🏆 论道胜利奖励'
                        : '💫 论道败阵奖励'
                    : '🎁 战斗奖励';

                panelHTML += `
                    <div style="margin: 20px 0; padding: 15px; background: rgba(255, 215, 0, 0.1); border-radius: 8px;">
                        <h3 style="color: ${
                            isVictory ? '#ffd700' : '#ff6b6b'
                        }; margin-top: 0;">${rewardTitle}</h3>
                        <p style="margin: 5px 0;">🔹 灵石: +${expGained}</p>
                        ${
                            isArenaBattle
                                ? ''
                                : `<p style="margin: 5px 0;">💰 金币: +${goldGained}</p>`
                        }
                        ${
                            isArenaBattle && !isVictory
                                ? '<p style="font-size: 12px; color: #888; margin: 5px 0;">💫 败阵亦有所得</p>'
                                : ''
                        }
                    </div>
                `;

                // 显示掉落物品
                if (droppedItems && droppedItems.length > 0) {
                    panelHTML += `
                        <div style="margin: 20px 0; padding: 15px; background: rgba(138, 43, 226, 0.1); border-radius: 8px;">
                            <h3 style="color: #8a2be2; margin-top: 0;">📦 获得物品 (${droppedItems.length}件)</h3>
                            <div style="text-align: left; max-height: 200px; overflow-y: auto;">
                    `;

                    droppedItems.forEach((item, index) => {
                        const rarityColor = this.getRarityColor(item.rarity || 'common');
                        panelHTML += `
                            <div style="
                                margin: 5px 0; 
                                padding: 8px; 
                                border-left: 3px solid ${rarityColor}; 
                                background: rgba(255, 255, 255, 0.05);
                                border-radius: 4px;
                                cursor: pointer;
                            " onclick="battleSystem.victoryPanelManager.showDropItemDetail(${JSON.stringify(
                                item
                            ).replace(/"/g, '&quot;')})">
                                <span style="color: ${rarityColor}; font-weight: bold;">
                                    ${item.name || '未知物品'}
                                </span>
                                ${item.quantity && item.quantity > 1 ? ` x${item.quantity}` : ''}
                                <small style="color: #999; display: block; font-size: 12px;">
                                    点击查看详情
                                </small>
                            </div>
                        `;
                    });

                    panelHTML += `
                            </div>
                        </div>
                    `;
                }
            }

            // 添加操作按钮
            panelHTML += `
                <div style="margin-top: 25px; display: flex; gap: 10px; justify-content: center; flex-wrap: wrap;">
            `;

            if (isVictory) {
                panelHTML += `
                    <button onclick="battleSystem.victoryPanelManager.continueNextStage()" style="
                        background: linear-gradient(135deg, #4CAF50, #45a049);
                        color: white;
                        border: none;
                        padding: 10px 20px;
                        border-radius: 6px;
                        cursor: pointer;
                        font-size: 14px;
                        font-weight: bold;
                        text-shadow: 0 1px 2px rgba(0,0,0,0.3);
                        transition: all 0.3s ease;
                    " onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                        🎯 继续下一关
                    </button>
                `;
            }

            // 🔧 修复：失败和胜利都显示挂机按钮
            panelHTML += `
                <button onclick="battleSystem.victoryPanelManager.handleDefeatAutoBattle('${message}')" style="
                    background: linear-gradient(135deg, #2196F3, #1976D2);
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 6px;
                    cursor: pointer;
                    font-size: 14px;
                    font-weight: bold;
                    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
                    transition: all 0.3s ease;
                " onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                    🤖 ${isVictory ? '开始挂机' : '失败后挂机'}
                </button>
            `;

            panelHTML += `
                    <button onclick="battleSystem.victoryPanelManager.closeVictoryPanel()" style="
                        background: linear-gradient(135deg, #f44336, #d32f2f);
                        color: white;
                        border: none;
                        padding: 10px 20px;
                        border-radius: 6px;
                        cursor: pointer;
                        font-size: 14px;
                        font-weight: bold;
                        text-shadow: 0 1px 2px rgba(0,0,0,0.3);
                        transition: all 0.3s ease;
                    " onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                        ❌ 关闭
                    </button>
                </div>
            `;

            panel.innerHTML = panelHTML;
            overlay.appendChild(panel);
            document.body.appendChild(overlay);

            // 🔧 添加淡入动画
            overlay.style.opacity = '0';
            panel.style.transform = 'scale(0.8)';

            requestAnimationFrame(() => {
                overlay.style.transition = 'opacity 0.3s ease';
                panel.style.transition = 'transform 0.3s ease';
                overlay.style.opacity = '1';
                panel.style.transform = 'scale(1)';
            });

            console.log('✅ 简单胜利面板创建成功');
        } catch (error) {
            console.error('❌ 创建简单胜利面板失败:', error);
            alert(`战斗结果: ${message}`);
        }
    }

    /**
     * 根据品质获取颜色
     */
    getRarityColor(rarity) {
        const colors = {
            common: '#ffffff',
            uncommon: '#1eff00',
            rare: '#0070dd',
            epic: '#a335ee',
            legendary: '#ff8000',
        };
        return colors[rarity] || colors.common;
    }

    /**
     * 🔧 简化：直接获取物品图片URL - 从数据库获取正确信息
     */
    async getItemImageUrl(item) {
        console.log('🔍 [Victory Panel] 获取物品图片 - 物品名称:', item.name);

        // 如果已经有正确的图片路径（且不是默认图片），直接使用
        if (
            item.icon_image &&
            !item.icon_image.includes('battle_sword.png') &&
            item.icon_image !== 'assets/images/battle_sword.png'
        ) {
            console.log('✅ [Victory Panel] 使用现有图片:', item.icon_image);
            return item.icon_image;
        }

        // 直接从数据库获取正确的图片信息
        try {
            const apiUrl = window.GameConfig
                ? window.GameConfig.getApiUrl('equipment_integrated.php')
                : '../../../src/api/equipment_integrated.php';
            const response = await fetch(
                `${apiUrl}?action=get_item_image&item_name=${encodeURIComponent(item.name)}`
            );
            if (response.ok) {
                const data = await response.json();
                if (data.success && data.icon_image) {
                    console.log('✅ [Victory Panel] 从数据库获取到正确图片:', data.icon_image);
                    return data.icon_image;
                } else {
                    console.log('⚠️ [Victory Panel] API调用成功但未找到图片:', data.message);
                }
            } else {
                console.error('❌ [Victory Panel] API响应错误，状态码:', response.status);
            }
        } catch (error) {
            console.error('❌ [Victory Panel] 获取物品图片失败:', error);
        }

        console.error('❌ [Victory Panel] 无法获取物品图片，物品名称:', item.name);
        return null;
    }

    /**
     * 🔧 新增：获取默认物品图片（与背包物品保持一致）
     */
    getDefaultItemImage(item) {
        console.log(`🔧 [Victory Panel] 获取默认物品图片:`, item.name, '物品数据:', item);

        // 根据物品名称和类型生成默认图片路径
        const realmKeywords = {
            开光: 1,
            灵虚: 2,
            辟谷: 3,
            心动: 4,
            元化: 5,
            元婴: 6,
            离合: 7,
            空冥: 8,
            寂灭: 9,
            大乘: 10,
        };

        let realmLevel = 1;
        const itemName = item.name || item.item_name || '';
        for (const [keyword, level] of Object.entries(realmKeywords)) {
            if (itemName.includes(keyword)) {
                realmLevel = level;
                break;
            }
        }

        console.log(
            `🔧 [Victory Panel] 物品名称: "${itemName}", 境界等级: ${realmLevel}, 物品类型: ${
                item.item_type || item.type
            }`
        );
        console.log(`🔧 [Victory Panel] 槽位类型: ${item.slot_type}`);

        // 🔧 先检查是否是靴子
        if (
            itemName.includes('靴') ||
            itemName.includes('鞋') ||
            itemName.includes('履') ||
            itemName.includes('云靴')
        ) {
            console.log(`🦶 [Victory Panel] 识别为靴子类装备`);
            const feetImages = [
                'zb_30101.png',
                'zb_30201.png',
                'zb_30202.png',
                'zb_30261.png',
                'zb_30301.png',
                'zb_30302.png',
                'zb_30361.png',
                'zb_30401.png',
                'zb_30402.png',
                'zb_30403.png',
            ];
            const selectedImage = `assets/images/${feetImages[realmLevel - 1] || feetImages[0]}`;
            console.log(`🦶 [Victory Panel] 选择靴子图片: ${selectedImage}`);
            return selectedImage;
        }

        if (item.item_type === 'weapon' || item.slot_type === 'weapon') {
            if (item.slot_type === 'sword' || itemName.includes('剑')) {
                const swordImages = [
                    'bw_11001.png',
                    'bw_11002.png',
                    'bw_11003.png',
                    'bw_11004.png',
                    'bw_11301.png',
                    'bw_11601.png',
                    'bw_11602.png',
                    'bw_11603.png',
                    'bw_11604.png',
                    'bw_11605.png',
                ];
                return `assets/images/${swordImages[realmLevel - 1] || swordImages[0]}`;
            } else if (item.slot_type === 'fan' || itemName.includes('扇')) {
                const fanImages = [
                    'bw_10301.png',
                    'bw_10302.png',
                    'bw_10601.png',
                    'bw_10602.png',
                    'bw_10801.png',
                    'bw_11801.png',
                    'bw_11802.png',
                    'bw_11606.png',
                    'bw_11607.png',
                    'bw_11608.png',
                ];
                return `assets/images/${fanImages[realmLevel - 1] || fanImages[0]}`;
            }
            return 'assets/images/bw_11001.png';
        } else if (
            item.item_type === 'equipment' ||
            item.item_type === 'armor' ||
            item.type === 'armor'
        ) {
            if (item.slot_type === 'ring' || itemName.includes('戒') || itemName.includes('指环')) {
                const ringImages = [
                    '1200.png',
                    '1201.png',
                    '1202.png',
                    '1203.png',
                    '1204.png',
                    '1205.png',
                    '1206.png',
                    '1207.png',
                    '1208.png',
                    '1209.png',
                ];
                return `assets/images/${ringImages[realmLevel - 1] || ringImages[0]}`;
            } else if (
                item.slot_type === 'bracers' ||
                itemName.includes('镯') ||
                itemName.includes('腕')
            ) {
                const bracersImages = [
                    'zb_10101.png',
                    'zb_10201.png',
                    'zb_10202.png',
                    'zb_10261.png',
                    'zb_10301.png',
                    'zb_10302.png',
                    'zb_10361.png',
                    'zb_10401.png',
                    'zb_10402.png',
                    'zb_10403.png',
                ];
                return `assets/images/${bracersImages[realmLevel - 1] || bracersImages[0]}`;
            } else if (
                item.slot_type === 'chest' ||
                itemName.includes('袍') ||
                itemName.includes('衣') ||
                itemName.includes('甲')
            ) {
                const chestImages = [
                    'zb_20101.png',
                    'zb_20201.png',
                    'zb_20202.png',
                    'zb_20261.png',
                    'zb_20301.png',
                    'zb_20302.png',
                    'zb_20361.png',
                    'zb_20401.png',
                    'zb_20402.png',
                    'zb_20403.png',
                ];
                return `assets/images/${chestImages[realmLevel - 1] || chestImages[0]}`;
            } else if (
                item.slot_type === 'feet' ||
                itemName.includes('靴') ||
                itemName.includes('鞋') ||
                itemName.includes('履')
            ) {
                // 🔧 新增：靴子类装备的图片
                const feetImages = [
                    'zb_30101.png',
                    'zb_30201.png',
                    'zb_30202.png',
                    'zb_30261.png',
                    'zb_30301.png',
                    'zb_30302.png',
                    'zb_30361.png',
                    'zb_30401.png',
                    'zb_30402.png',
                    'zb_30403.png',
                ];
                return `assets/images/${feetImages[realmLevel - 1] || feetImages[0]}`;
            } else if (
                item.slot_type === 'legs' ||
                itemName.includes('腰') ||
                itemName.includes('带') ||
                itemName.includes('裤')
            ) {
                // 🔧 新增：腰带类装备的图片
                const legsImages = [
                    'zb_40101.png',
                    'zb_40201.png',
                    'zb_40202.png',
                    'zb_40261.png',
                    'zb_40301.png',
                    'zb_40302.png',
                    'zb_40361.png',
                    'zb_40401.png',
                    'zb_40402.png',
                    'zb_40403.png',
                ];
                return `assets/images/${legsImages[realmLevel - 1] || legsImages[0]}`;
            } else if (
                item.slot_type === 'necklace' ||
                itemName.includes('项链') ||
                itemName.includes('符') ||
                itemName.includes('坠')
            ) {
                // 🔧 新增：项链类装备的图片
                const necklaceImages = [
                    'zb_50101.png',
                    'zb_50201.png',
                    'zb_50202.png',
                    'zb_50261.png',
                    'zb_50301.png',
                    'zb_50302.png',
                    'zb_50361.png',
                    'zb_50401.png',
                    'zb_50402.png',
                    'zb_50403.png',
                ];
                return `assets/images/${necklaceImages[realmLevel - 1] || necklaceImages[0]}`;
            }
            return 'assets/images/battle_sword.png'; // 默认返回确实存在的图片
        }

        // 🔧 最后的智能识别：根据物品名称判断类型
        if (itemName.includes('靴') || itemName.includes('鞋') || itemName.includes('履')) {
            const feetImages = [
                'zb_30101.png',
                'zb_30201.png',
                'zb_30202.png',
                'zb_30261.png',
                'zb_30301.png',
                'zb_30302.png',
                'zb_30361.png',
                'zb_30401.png',
                'zb_30402.png',
                'zb_30403.png',
            ];
            return `assets/images/${feetImages[realmLevel - 1] || feetImages[0]}`;
        } else if (itemName.includes('腰') || itemName.includes('带')) {
            const legsImages = [
                'zb_40101.png',
                'zb_40201.png',
                'zb_40202.png',
                'zb_40261.png',
                'zb_40301.png',
                'zb_40302.png',
                'zb_40361.png',
                'zb_40401.png',
                'zb_40402.png',
                'zb_40403.png',
            ];
            return `assets/images/${legsImages[realmLevel - 1] || legsImages[0]}`;
        } else if (
            itemName.includes('项链') ||
            itemName.includes('符') ||
            itemName.includes('坠')
        ) {
            const necklaceImages = [
                'zb_50101.png',
                'zb_50201.png',
                'zb_50202.png',
                'zb_50261.png',
                'zb_50301.png',
                'zb_50302.png',
                'zb_50361.png',
                'zb_50401.png',
                'zb_50402.png',
                'zb_50403.png',
            ];
            return `assets/images/${necklaceImages[realmLevel - 1] || necklaceImages[0]}`;
        } else if (itemName.includes('镯') || itemName.includes('腕')) {
            const bracersImages = [
                'zb_10101.png',
                'zb_10201.png',
                'zb_10202.png',
                'zb_10261.png',
                'zb_10301.png',
                'zb_10302.png',
                'zb_10361.png',
                'zb_10401.png',
                'zb_10402.png',
                'zb_10403.png',
            ];
            return `assets/images/${bracersImages[realmLevel - 1] || bracersImages[0]}`;
        } else if (itemName.includes('戒') || itemName.includes('指环')) {
            const ringImages = [
                '1200.png',
                '1201.png',
                '1202.png',
                '1203.png',
                '1204.png',
                '1205.png',
                '1206.png',
                '1207.png',
                '1208.png',
                '1209.png',
            ];
            return `assets/images/${ringImages[realmLevel - 1] || ringImages[0]}`;
        } else if (itemName.includes('袍') || itemName.includes('衣') || itemName.includes('甲')) {
            const chestImages = [
                'zb_20101.png',
                'zb_20201.png',
                'zb_20202.png',
                'zb_20261.png',
                'zb_20301.png',
                'zb_20302.png',
                'zb_20361.png',
                'zb_20401.png',
                'zb_20402.png',
                'zb_20403.png',
            ];
            return `assets/images/${chestImages[realmLevel - 1] || chestImages[0]}`;
        }

        // 🔧 最终默认图片：如果以上所有判断都失败，使用通用默认图片
        console.log(
            `🔧 [Victory Panel] 无法确定物品类型，使用通用默认图片，物品名称: "${itemName}"`
        );
        return 'assets/images/battle_sword.png'; // 使用确实存在的默认图片
    }

    /**
     * 继续下一关
     */
    async continueNextStage() {
        try {
            console.log('🎯 开始处理继续下一关');

            // 禁用所有按钮防止重复点击
            const nextStageButton = document.querySelector('[data-next-stage-button]');
            const autoBattleButton = document.querySelector('[data-auto-battle-button]');
            const exitBattleButton = document.querySelector('[data-exit-battle-button]');

            if (nextStageButton) nextStageButton.disabled = true;
            if (autoBattleButton) autoBattleButton.disabled = true;
            if (exitBattleButton) exitBattleButton.disabled = true;

            console.log('🎮 继续下一层...');

            // 🔧 修复：不要停止挂机模式，只清除当前战斗的定时器
            if (this.battleSystem.autoBattleCountdown) {
                clearInterval(this.battleSystem.autoBattleCountdown);
                this.battleSystem.autoBattleCountdown = null;
                console.log('🗑️ 清除挂机倒计时定时器');
            }

            // 移除胜利面板
            const overlayToRemove = document.querySelector('.game-overlay');
            if (overlayToRemove) overlayToRemove.remove();

            // 🚨 正常情况：检查当前saveVictoryResult的返回状态
            if (this.battleSystem.lastSaveResult && this.battleSystem.lastSaveResult.success) {
                console.log('✅ 战斗结果已保存，检查后端返回的层数信息');

                // 🔧 修复：使用后端返回的next_stage而不是前端计算
                const nextStage = this.battleSystem.lastSaveResult.next_stage;
                if (nextStage && nextStage > this.dataManager.currentStage) {
                    console.log(
                        `🎮 后端确认进入第${nextStage}层（从第${this.dataManager.currentStage}层）`
                    );

                    // 更新本地数据
                    const currentArea = this.dataManager.getCurrentArea();
                    this.dataManager.currentStage = nextStage;
                    currentArea.progress = nextStage;
                    localStorage.setItem('currentArea', JSON.stringify(currentArea));

                    console.log('🎮 本地数据已同步，当前第', this.dataManager.currentStage, '层');
                } else {
                    console.log(`⚠️ 后端未更新层数，保持当前第${this.dataManager.currentStage}层`);
                }

                // 移除胜利面板
                const overlayToRemove = document.querySelector('.game-overlay');
                if (overlayToRemove) overlayToRemove.remove();

                // 🚀 使用无刷新方式进入下一关
                await this.proceedToNextStageWithoutRefresh();
                return;
            }

            // 🚨 如果saveVictoryResult失败，使用备用API方案
            console.log('⚠️ 战斗结果保存可能失败，使用备用API验证');

            this.handleNextStageWithAPI();
        } catch (error) {
            console.error('继续下一关失败:', error);
            window.location.reload();
        }
    }

    /**
     * 🔧 修复双重JSON问题：直接使用本地数据处理下一层逻辑，不再调用API
     * 因为saveVictoryResult已经更新了进度，无需重复调用API
     */
    async handleNextStageWithAPI() {
        try {
            console.log('🔧 修复：直接使用本地数据处理下一层，避免双重JSON问题');

            const currentArea = this.dataManager.getCurrentArea();

            // 🔧 修复：检查是否有后端返回的层数信息
            if (this.battleSystem.lastSaveResult && this.battleSystem.lastSaveResult.next_stage) {
                const nextStage = this.battleSystem.lastSaveResult.next_stage;
                if (nextStage > this.dataManager.currentStage) {
                    console.log(`🎮 使用后端返回的层数: ${nextStage}`);
                    this.dataManager.currentStage = nextStage;
                    currentArea.progress = nextStage;
                    localStorage.setItem('currentArea', JSON.stringify(currentArea));
                } else {
                    console.log(`⚠️ 后端未更新层数，保持当前第${this.dataManager.currentStage}层`);
                }
            } else {
                console.log('⚠️ 没有后端层数信息，保持当前层数不变');
            }

            console.log('🎮 当前层数:', this.dataManager.currentStage);

            // 更新区域信息显示
            this.uiManager.updateAreaInfo();

            // 移除胜利面板
            const overlayToRemove = document.querySelector('.game-overlay');
            if (overlayToRemove) overlayToRemove.remove();

            // 🚀 使用无刷新方式进入下一关
            await this.proceedToNextStageWithoutRefresh();
        } catch (error) {
            console.error('❌ 下一层处理失败:', error);

            // 🔧 备用方案：如果出错，保持当前层数不变
            console.log('🔧 使用备用方案：保持当前层数不变');

            const currentArea = this.dataManager.getCurrentArea();
            console.log(
                '🎮 保持当前层数:',
                this.dataManager.currentStage,
                '(类型:',
                typeof this.dataManager.currentStage,
                ')'
            );

            // 移除胜利面板
            const overlayToRemove = document.querySelector('.game-overlay');
            if (overlayToRemove) overlayToRemove.remove();

            // 🚀 使用无刷新方式进入下一关
            await this.proceedToNextStageWithoutRefresh();
        }
    }

    /**
     * 开始挂机模式
     */
    startAutoBattleMode() {
        console.log('🤖 开始挂机模式');

        // 🔧 修复：同步设置所有相关的挂机状态
        this.battleSystem.isAutoBattleMode = true;
        if (this.battleSystem.autoBattleManager) {
            this.battleSystem.autoBattleManager.setAutoBattleMode(true);
        }

        // 🎯 保存挂机状态到localStorage
        localStorage.setItem('autoBattleMode', 'true');
        console.log('💾 挂机状态已保存到localStorage');

        // 更新按钮状态
        const autoBattleButton = document.querySelector('[data-auto-battle-button]');
        const autoBattleText = document.querySelector('[data-auto-battle-text]');
        const countdownDiv = document.querySelector('[data-auto-battle-countdown]');

        if (autoBattleButton) {
            autoBattleButton.classList.add('stop-mode');
        }

        if (autoBattleText) {
            autoBattleText.textContent = '停止挂机';
        }

        // 开始3秒倒计时
        this.startAutoBattleCountdown(countdownDiv);
    }

    /**
     * 开始挂机倒计时
     */
    startAutoBattleCountdown(countdownDiv) {
        if (!countdownDiv) {
            console.error('❌ 找不到倒计时元素');
            return;
        }

        // 🔧 新增：防止重复启动倒计时
        if (this.battleSystem.autoBattleCountdown) {
            console.log('⚠️ 倒计时已在运行，先清除旧的倒计时');
            clearInterval(this.battleSystem.autoBattleCountdown);
            this.battleSystem.autoBattleCountdown = null;
        }

        console.log('🚀 开始挂机倒计时（3秒）');
        let countdown = 3;
        const autoBattleButton = document.querySelector('[data-auto-battle-button]');
        const autoBattleSpan = autoBattleButton ? autoBattleButton.querySelector('span') : null;

        // 隐藏按钮图标
        if (autoBattleSpan) {
            autoBattleSpan.style.setProperty('--hide-icon', 'true');
        }

        const updateCountdown = () => {
            // 🔧 修复：每次更新前检查是否还在挂机模式
            if (!this.battleSystem.isAutoBattleMode) {
                clearInterval(this.battleSystem.autoBattleCountdown);
                this.battleSystem.autoBattleCountdown = null;
                if (countdownDiv) {
                    countdownDiv.innerHTML = '';
                }
                if (autoBattleSpan) {
                    autoBattleSpan.style.removeProperty('--hide-icon');
                }
                return;
            }

            if (countdown > 0) {
                countdownDiv.innerHTML = `<span class="countdown-text">${countdown}</span>`;
                countdown--;
            } else {
                // 倒计时结束，开始战斗
                clearInterval(this.battleSystem.autoBattleCountdown);
                this.battleSystem.autoBattleCountdown = null;

                if (countdownDiv) {
                    countdownDiv.innerHTML = '';
                }

                // 恢复按钮图标显示
                if (autoBattleSpan) {
                    autoBattleSpan.style.removeProperty('--hide-icon');
                }

                // 🔧 简化：挂机模式只在当前层循环
                if (this.battleSystem.isAutoBattleMode) {
                    // 关闭胜利面板，开始新的战斗
                    const overlayToRemove = document.querySelector('.game-overlay');
                    if (overlayToRemove) overlayToRemove.remove();

                    console.log('🤖 挂机模式：循环当前层，不推进进度');
                    this.restartBattleWithoutRefresh(true); // 保持当前层数
                }
            }
        };

        // 立即执行一次
        updateCountdown();

        // 每秒更新一次
        this.battleSystem.autoBattleCountdown = setInterval(updateCountdown, 1000);
    }

    /**
     * 🚀 新增：无刷新重新开始战斗
     * 使用AJAX管理器重新获取数据，避免页面刷新
     */
    async restartBattleWithoutRefresh(keepCurrentStage = false) {
        try {
            console.log('🚀 开始无刷新挂机重启战斗，保持层数:', keepCurrentStage);

            // 显示加载状态
            this.battleSystem.updateBattleStatus('🔄 挂机中，正在准备下一轮战斗...');

            // 关闭胜利面板
            const overlayToRemove = document.querySelector('.game-overlay');
            if (overlayToRemove) overlayToRemove.remove();

            // 重置战斗状态
            this.battleSystem.isGameOver = false;
            this.battleSystem.battleEndTime = null;

            // 🔧 修复：根据keepCurrentStage决定是否保持层数
            console.log('🔄 统一重新加载所有战斗数据...');
            await this.reloadAllBattleData(true, keepCurrentStage); // 传递保持层数标识

            // 重新初始化角色对象
            await this.reinitializeBattleCharacters();

            // 开始新的战斗
            this.battleSystem.updateBattleStatus('🎯 挂机战斗开始！');

            // 延迟一点开始战斗，让用户看到状态更新
            setTimeout(async () => {
                if (!this.battleSystem.isGameOver && this.battleSystem.isAutoBattleMode) {
                    await this.battleSystem.battleFlowManager.autoBattle();
                }
            }, 500);

            console.log('✅ 无刷新挂机重启完成');
        } catch (error) {
            console.error('❌ 无刷新挂机重启失败:', error);

            // 错误处理：回退到页面刷新方式
            this.battleSystem.updateBattleStatus('⚠️ 挂机数据更新失败，使用备用方案...');

            // 给用户一点时间看到错误信息
            setTimeout(() => {
                console.log('🔄 回退到页面刷新方式');
                window.location.reload();
            }, 2000);
        }
    }

    /**
     * 🚀 新增：统一的数据重新加载函数（消除重复加载）
     */
    async reloadAllBattleData(includeWeapons = true, keepCurrentStage = false) {
        console.log('🔄 开始统一重新加载战斗数据...');
        console.log('🔍 模式检查:', { includeWeapons, keepCurrentStage });
        const loadStartTime = Date.now();

        try {
            // 🔧 调试：检查初始状态
            console.log('🔍 调试信息 - 初始状态:', {
                dataManager存在: !!this.dataManager,
                battleSystem存在: !!this.battleSystem,
                AJAX管理器存在: !!window.ajaxManager,
                ImagePathManager存在: !!window.ImagePathManager,
                includeWeapons: includeWeapons,
            });

            // 🔧 第一步：清除相关缓存，强制重新获取数据
            if (this.dataManager.apiCache) {
                console.log('🔄 彻底清除所有API缓存，强制获取真实数据');

                // 🔧 修复：彻底清除所有缓存
                this.dataManager.apiCache.clear();
                console.log('✅ 所有API缓存已清除');

                // 🔧 新增：清除pending请求
                if (this.dataManager.pendingRequests) {
                    this.dataManager.pendingRequests.clear();
                    console.log('✅ 所有pending请求已清除');
                }
            }

            // 🔧 第二步：确保currentArea数据正确
            const currentArea = this.dataManager.getCurrentArea();
            console.log('🔄 当前区域信息:', currentArea);

            if (!currentArea || !currentArea.mapId) {
                console.warn('⚠️ 区域信息不完整，尝试修复...');
                // 从URL获取mapId作为备用
                const urlParams = new URLSearchParams(window.location.search);
                const mapId = parseInt(urlParams.get('map_id')) || 1;
                const mapCode = urlParams.get('map_code') || 'kunlun';

                // 🔧 修复：不要在这里写死进度，保持原有进度或从后端获取
                const currentArea = this.dataManager.getCurrentArea();
                const existingProgress = currentArea ? currentArea.progress : null;

                const fixedArea = {
                    mapId: mapId,
                    areaId: mapCode,
                    areaName: currentArea ? currentArea.areaName : '太乙峰', // 使用现有名称或默认
                    progress: existingProgress || this.dataManager.currentStage || 1, // 🔧 优先使用已存在的进度
                    total: currentArea ? currentArea.total : 60,
                    map_code: mapCode, // 🔧 添加：保持一致性
                    map_name: currentArea ? currentArea.map_name : '太乙峰', // 🔧 添加：保持一致性
                };

                // 🔧 修复：只有在进度确实有变化时才更新localStorage
                if (!currentArea || JSON.stringify(currentArea) !== JSON.stringify(fixedArea)) {
                    localStorage.setItem('currentArea', JSON.stringify(fixedArea));
                    console.log('🔧 修复后的区域信息:', fixedArea);
                } else {
                    console.log('🔧 区域信息已正确，无需修复:', currentArea);
                }
            }

            // 🔧 修复：先更新层数，再加载数据，确保敌人数据使用正确的层数
            if (keepCurrentStage) {
                console.log(
                    '🤖 挂机模式：保持原有层数不变，当前第',
                    this.dataManager.currentStage,
                    '层'
                );
            } else {
                // 🔧 只有非挂机模式才强制重新获取当前层数
                console.log('🔄 正常模式：强制重新获取当前层数...');

                // 🔧 修复：临时替换API请求方法为无缓存版本
                const originalMakeApiRequest = this.dataManager.makeApiRequest;
                this.dataManager.makeApiRequest = this.dataManager.makeApiRequestNoCache;

                try {
                    const freshStage = await this.dataManager.getCurrentStage();
                    if (freshStage && freshStage !== this.dataManager.currentStage) {
                        console.log(
                            `🔄 层数已更新: ${this.dataManager.currentStage} → ${freshStage}`
                        );
                        this.dataManager.currentStage = freshStage;

                        // 同步更新区域进度
                        const currentArea = this.dataManager.getCurrentArea();
                        if (currentArea) {
                            currentArea.progress = freshStage;
                            localStorage.setItem('currentArea', JSON.stringify(currentArea));
                        }
                    } else {
                        console.log(`✅ 层数确认: 当前第${this.dataManager.currentStage}层`);
                    }
                } finally {
                    // 恢复原始的API请求方法
                    this.dataManager.makeApiRequest = originalMakeApiRequest;
                }
            }

            // 🔧 第三步：并行加载基础数据（提高效率）
            console.log('🔄 开始并行加载基础数据...');
            const loadPromises = [];

            // 🔧 修复：强制使用无缓存的数据加载方法
            console.log('🚫 强制使用无缓存API请求，确保获取真实数据');

            // 临时替换API请求方法为无缓存版本
            const originalMakeApiRequest = this.dataManager.makeApiRequest;
            this.dataManager.makeApiRequest = this.dataManager.makeApiRequestNoCache;

            try {
                // 加载玩家数据
                loadPromises.push(
                    this.dataManager.loadPlayerData().catch(error => {
                        console.error('❌ 加载玩家数据失败:', error);
                        throw new Error('玩家数据加载失败: ' + error.message);
                    })
                );

                // 加载敌人数据（现在使用更新后的层数）
                loadPromises.push(
                    this.dataManager.loadEnemyData().catch(error => {
                        console.error('❌ 加载敌人数据失败:', error);
                        throw new Error('敌人数据加载失败: ' + error.message);
                    })
                );

                // 只有需要时才加载武器数据
                if (includeWeapons) {
                    loadPromises.push(
                        this.dataManager.loadWeaponData().catch(error => {
                            console.error('❌ 加载武器数据失败:', error);
                            throw new Error('武器数据加载失败: ' + error.message);
                        })
                    );
                }

                await Promise.all(loadPromises);
                console.log('✅ 基础数据并行加载完成（无缓存）');
            } finally {
                // 恢复原始的API请求方法
                this.dataManager.makeApiRequest = originalMakeApiRequest;
                console.log('🔄 已恢复原始API请求方法');
            }

            // 🔧 修复：移除重复的getCurrentStage调用（已在上面处理过）
            console.log('✅ 进度获取已完成，当前第', this.dataManager.currentStage, '层');

            // 🔧 第四步：重新计算最终属性
            console.log('🔄 重新计算玩家最终属性...');
            this.battleSystem.playerStats = this.dataManager.calculateFinalStats();
            console.log('✅ 玩家属性计算完成:', {
                hp: this.battleSystem.playerStats.hp_bonus,
                mp: this.battleSystem.playerStats.mp_bonus,
                attack: this.battleSystem.playerStats.physical_attack,
                defense: this.battleSystem.playerStats.physical_defense,
            });

            // 🔧 第五步：更新技能序列
            console.log('🔄 更新技能序列...');
            this.battleSystem.skillSequence = this.dataManager.getBattleSkillSequence();
            console.log('✅ 技能序列更新完成:', this.battleSystem.skillSequence);

            // 🔧 修复：重置技能索引，确保从第一个技能开始使用
            console.log('🔄 重置技能索引...');
            const oldAttackCount = this.battleSystem.attackCount;
            this.battleSystem.attackCount = 0;
            console.log(`✅ 技能索引已重置: ${oldAttackCount} → 0，下次将使用第1个技能`);

            // 🔧 第六步：更新UI显示
            console.log('🔄 更新UI显示...');

            // 🔧 新增：确保敌人数据同步到battleSystem
            if (this.dataManager.enemyData) {
                console.log('🔄 同步敌人数据到战斗系统:', this.dataManager.enemyData);
                // 这里不重新创建敌人对象，只更新引用，实际创建在reinitializeBattleCharacters中完成
                this.battleSystem.enemyData = this.dataManager.enemyData;
            }

            // 🔧 新增：强制更新武器显示，确保耐久度等属性正确显示
            if (includeWeapons && this.battleSystem.updateWeaponDisplay) {
                console.log('🔄 强制更新武器显示...');
                this.battleSystem.updateWeaponDisplay();
                console.log('✅ 武器显示强制更新完成');
            } else if (includeWeapons && this.uiManager && this.uiManager.updateWeaponDisplay) {
                console.log('🔄 通过UI管理器更新武器显示...');
                this.uiManager.updateWeaponDisplay();
                console.log('✅ 武器显示更新完成');
            }

            if (this.dataManager.setBattleBackground) {
                this.dataManager.setBattleBackground();
            }
            if (this.battleSystem.updateAreaInfo) {
                this.battleSystem.updateAreaInfo();
            }
            console.log('✅ UI显示更新完成');

            const loadTime = Date.now() - loadStartTime;
            console.log(`✅ 统一数据重新加载完成，耗时: ${loadTime}ms`);
        } catch (error) {
            console.error('❌ 统一数据重新加载失败:', error);

            // 🔧 详细错误信息
            console.error('❌ 错误详情:', {
                message: error.message,
                stack: error.stack,
                currentArea: this.dataManager.getCurrentArea(),
                currentStage: this.dataManager.currentStage,
            });

            throw error;
        }
    }

    /**
     * 🚀 修改：重新初始化战斗角色对象（简化版）
     */
    async reinitializeBattleCharacters() {
        console.log('🔄 重新初始化战斗角色对象');

        // 修复HP/MP数据
        // 🔧 修复：统一使用hp_total/mp_total字段
        this.battleSystem.playerStats.hp_total = this.battleSystem.playerStats.hp_bonus || 300;
        this.battleSystem.playerStats.mp_total = this.battleSystem.playerStats.mp_bonus || 100;
        this.battleSystem.playerStats.max_hp = this.battleSystem.playerStats.hp_total; // 兼容性
        this.battleSystem.playerStats.max_mp = this.battleSystem.playerStats.mp_total; // 兼容性
        this.battleSystem.playerStats.currentMp = this.battleSystem.playerStats.mp_total;

        // 🔧 修复：使用正确的DOM选择器获取角色容器
        const playerElement = document.querySelector('.character.player');
        const enemyElement = document.querySelector('.character.enemy');

        console.log('🔍 DOM元素检查:', {
            playerElement存在: !!playerElement,
            enemyElement存在: !!enemyElement,
        });

        // 检查DOM元素是否存在
        if (!playerElement || !enemyElement) {
            console.error('❌ 无法找到角色DOM元素，跳过角色重新创建');
            console.error('  playerElement:', playerElement);
            console.error('  enemyElement:', enemyElement);
            return;
        }

        // 🔧 修复：使用正确的构造函数创建角色对象
        try {
            // 创建新的玩家角色
            if (this.battleSystem.playerStats) {
                this.battleSystem.player = new Character(playerElement, {
                    name: this.battleSystem.playerStats.name || '修仙者',
                    max_hp: this.battleSystem.playerStats.max_hp,
                    max_mp: this.battleSystem.playerStats.max_mp,
                    currentHp: this.battleSystem.playerStats.max_hp,
                    currentMp: this.battleSystem.playerStats.max_mp,
                    level: this.battleSystem.playerStats.level,
                    avatar:
                        this.battleSystem.playerStats.character_avatar ||
                        'assets/images/char/ck.png', // 🔧 修复：正确传递玩家头像
                    isPlayer: true,
                });

                console.log(
                    '✅ 玩家角色对象重新创建完成, 头像:',
                    this.battleSystem.playerStats.character_avatar
                );
            }

            // 创建新的敌人角色
            if (this.dataManager.enemyData) {
                this.battleSystem.enemy = new Character(enemyElement, {
                    name: this.dataManager.enemyData.name || '未知怪物',
                    max_hp: this.dataManager.enemyData.max_hp,
                    max_mp: this.dataManager.enemyData.max_mp,
                    currentHp: this.dataManager.enemyData.max_hp,
                    currentMp: this.dataManager.enemyData.max_mp,
                    level: this.dataManager.enemyData.level,
                    avatar:
                        this.dataManager.enemyData.avatarImage ||
                        this.dataManager.enemyData.avatar ||
                        'assets/images/enemy/yelang.png', // 🔧 修复：正确传递敌人头像
                    type: this.dataManager.enemyData.type, // 🔧 修复：传递敌人类型，用于设置名字颜色
                    isEnemy: true, // 🔧 修复：使用isEnemy而不是isPlayer
                    isPlayer: false,
                });

                // 设置敌人的所有属性
                Object.assign(this.battleSystem.enemy, this.dataManager.enemyData);

                console.log(
                    '✅ 敌人角色对象重新创建完成:',
                    this.battleSystem.enemy.name,
                    '类型:',
                    this.dataManager.enemyData.type,
                    '头像:',
                    this.dataManager.enemyData.avatarImage
                );
            }
        } catch (error) {
            console.error('❌ 重新创建角色对象失败:', error);
            throw error;
        }

        console.log('✅ 战斗角色对象重新初始化完成');

        // 🔧 新增：强制更新UI显示，确保敌人属性正确显示
        try {
            // 强制更新玩家信息显示
            if (this.battleSystem.player && this.battleSystem.playerStats) {
                const playerInfo = document.querySelector('.character.player .character-info');
                if (playerInfo) {
                    const nameElement = playerInfo.querySelector('.character-name');
                    const levelElement = playerInfo.querySelector('.character-level');
                    const avatarElement = playerInfo.querySelector('.character-avatar');

                    if (nameElement)
                        nameElement.textContent = this.battleSystem.playerStats.name || '修仙者';
                    if (levelElement) {
                        // 🔧 修复：使用境界系统显示境界而不是等级
                        const level = this.battleSystem.playerStats.level || 1;
                        const realmText =
                            typeof RealmSystem !== 'undefined' && RealmSystem.getShortRealm
                                ? RealmSystem.getShortRealm(level)
                                : `Lv.${level}`;
                        levelElement.textContent = realmText;
                    }
                    if (avatarElement) {
                        const avatar =
                            this.battleSystem.playerStats.character_avatar ||
                            'assets/images/char/ck.png';
                        avatarElement.src = avatar;
                        console.log('🔄 强制更新玩家头像:', avatar);
                    }
                }
            }

            // 强制更新敌人信息显示
            if (this.battleSystem.enemy && this.dataManager.enemyData) {
                const enemyInfo = document.querySelector('.character.enemy .character-info');
                if (enemyInfo) {
                    const nameElement = enemyInfo.querySelector('.character-name');
                    const levelElement = enemyInfo.querySelector('.character-level');
                    const avatarElement = enemyInfo.querySelector('.character-avatar');

                    if (nameElement)
                        nameElement.textContent = this.dataManager.enemyData.name || '未知怪物';
                    if (levelElement) {
                        // 🔧 修复：使用境界系统显示境界而不是等级
                        const level = this.dataManager.enemyData.level || 1;
                        const realmText =
                            typeof RealmSystem !== 'undefined' && RealmSystem.getShortRealm
                                ? RealmSystem.getShortRealm(level)
                                : `Lv.${level}`;
                        levelElement.textContent = realmText;
                    }
                    if (avatarElement) {
                        const avatar =
                            this.dataManager.enemyData.avatarImage ||
                            this.dataManager.enemyData.avatar ||
                            'assets/images/enemy/yelang.png';
                        avatarElement.src = avatar;
                        console.log('🔄 强制更新敌人头像:', avatar);
                    }
                }
            }

            // 🔧 新增：强制更新HP/MP显示
            if (this.uiManager && typeof this.uiManager.updateCharacterStats === 'function') {
                console.log('🔄 强制更新角色状态显示...');
                this.uiManager.updateCharacterStats();
            }

            console.log('✅ UI强制更新完成');
        } catch (uiError) {
            console.error('⚠️ UI更新时出现非关键错误:', uiError);
            // UI更新失败不应该影响战斗系统
        }
    }

    /**
     * 🚀 新增：无刷新进入下一关
     * 与重启挂机类似，但是会进入下一关而不是重复当前关
     */
    async proceedToNextStageWithoutRefresh() {
        try {
            console.log('🎯 开始无刷新进入下一关');

            // 显示加载状态
            this.battleSystem.updateBattleStatus('🔄 正在进入下一关...');

            // 关闭胜利面板
            const overlayToRemove = document.querySelector('.game-overlay');
            if (overlayToRemove) overlayToRemove.remove();

            // 重置战斗状态
            this.battleSystem.isGameOver = false;
            this.battleSystem.battleEndTime = null;

            // 🔧 修复：使用优化后的统一数据重新加载函数（不包含武器数据，因为下一关武器不变）
            console.log('🔄 统一重新加载下一关数据（包含武器数据）...');
            await this.reloadAllBattleData(true, false); // 🔧 修复：进入下一关，不保持层数

            // 重新初始化角色对象
            await this.reinitializeBattleCharacters();

            // 🔧 修复：确保怪物名字颜色正确显示
            await this.updateEnemyNameColor();

            // 开始新的战斗
            this.battleSystem.updateBattleStatus('🎯 下一关战斗开始！');

            // 延迟一点开始战斗，让用户看到状态更新
            setTimeout(async () => {
                if (!this.battleSystem.isGameOver) {
                    console.log('🎯 启动下一关战斗');
                    await this.battleSystem.battleFlowManager.autoBattle();
                }
            }, 500);

            console.log('✅ 无刷新进入下一关完成');
        } catch (error) {
            console.error('❌ 无刷新进入下一关失败:', error);

            // 错误处理：回退到页面刷新方式
            this.battleSystem.updateBattleStatus('⚠️ 下一关数据加载失败，使用备用方案...');

            // 给用户一点时间看到错误信息
            setTimeout(() => {
                console.log('🔄 回退到页面刷新方式');
                window.location.reload();
            }, 2000);
        }
    }

    /**
     * 关闭胜利面板
     */
    closeVictoryPanel() {
        // 关闭标准胜利面板
        const gameOverlay = document.querySelector('.game-overlay');
        if (gameOverlay) {
            gameOverlay.remove();
            console.log('✅ 标准胜利面板已关闭');
        }

        // 关闭简单胜利面板
        const simpleOverlay = document.querySelector('.simple-victory-overlay');
        if (simpleOverlay) {
            simpleOverlay.remove();
            console.log('✅ 简单胜利面板已关闭');
        }

        // 关闭任何物品详情弹窗
        const itemPopup = document.getElementById('item-detail-popup');
        if (itemPopup) {
            itemPopup.remove();
            console.log('✅ 物品详情弹窗已关闭');
        }

        const simpleItemDetail = document.getElementById('simple-item-detail');
        if (simpleItemDetail) {
            simpleItemDetail.remove();
            console.log('✅ 简单物品详情已关闭');
        }
    }

    /**
     * 🔧 新增：加载胜利面板CSS样式
     */
    async loadVictoryPanelCSS() {
        try {
            // 检查是否已经加载过CSS
            if (document.querySelector('#victory-panel-styles')) {
                console.log('✅ 胜利面板CSS已存在，跳过重复加载');
                return;
            }

            // 从victory-panel.html加载CSS样式
            const response = await fetch('pages/victory-panel.html');
            const html = await response.text();

            // 创建临时容器来解析HTML
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = html;

            // 提取style标签内容
            const styleElement = tempDiv.querySelector('style');
            if (styleElement) {
                const newStyle = document.createElement('style');
                newStyle.id = 'victory-panel-styles';
                newStyle.textContent = styleElement.textContent;
                document.head.appendChild(newStyle);
                console.log('✅ 胜利面板CSS样式已加载');
            } else {
                console.warn('⚠️ 在victory-panel.html中找不到style标签');
            }
        } catch (error) {
            console.error('❌ 加载胜利面板CSS失败:', error);
        }
    }

    /**
     * 🎲 新增：显示奇遇事件弹窗
     */
    showAdventureEventPopup(adventureEvent) {
        console.log('🎲 显示奇遇事件弹窗:', adventureEvent);

        if (!adventureEvent || !adventureEvent.success) {
            console.log('⚠️ 奇遇事件数据无效，跳过显示');
            return;
        }

        // 创建奇遇事件弹窗
        const adventurePopup = document.createElement('div');
        adventurePopup.className = 'adventure-event-popup';
        adventurePopup.innerHTML = `
            <div class="adventure-event-overlay" style="
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                opacity: 0;
                transition: opacity 0.3s ease;
            ">
                <div class="adventure-event-content" style="
                    background: linear-gradient(135deg, #2c3e50, #34495e);
                    border: 2px solid #f39c12;
                    border-radius: 15px;
                    padding: 20px;
                    max-width: 400px;
                    width: 90%;
                    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
                    color: #ecf0f1;
                    text-align: center;
                    position: relative;
                    animation: adventurePopupIn 0.5s ease-out;
                ">
                    <div class="adventure-event-header" style="
                        border-bottom: 1px solid #f39c12;
                        padding-bottom: 15px;
                        margin-bottom: 20px;
                        position: relative;
                    ">
                        <h2 style="
                            margin: 0;
                            color: #f39c12;
                            font-size: 24px;
                            text-shadow: 0 0 10px rgba(243, 156, 18, 0.5);
                        ">🎲 奇遇事件</h2>
                        <button class="adventure-close-btn" onclick="this.closest('.adventure-event-popup').remove()" style="
                            position: absolute;
                            top: -5px;
                            right: -5px;
                            background: #e74c3c;
                            color: white;
                            border: none;
                            border-radius: 50%;
                            width: 30px;
                            height: 30px;
                            font-size: 18px;
                            cursor: pointer;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        ">×</button>
                    </div>
                    <div class="adventure-event-body">
                        <div class="adventure-event-title" style="margin-bottom: 15px;">
                            <h3 style="
                                margin: 0;
                                color: #e74c3c;
                                font-size: 20px;
                                text-shadow: 0 0 8px rgba(231, 76, 60, 0.5);
                            ">${
                                adventureEvent.event_name || adventureEvent.eventName || '神秘奇遇'
                            }</h3>
                        </div>
                        <div class="adventure-event-message" style="margin-bottom: 20px;">
                            <p style="
                                margin: 0;
                                font-size: 16px;
                                line-height: 1.5;
                                color: #bdc3c7;
                            ">${
                                adventureEvent.description ||
                                adventureEvent.message ||
                                '恭喜！您触发了奇遇事件！'
                            }</p>
                        </div>
                        <div class="adventure-event-rewards">
                            <h4 style="
                                margin: 0 0 15px 0;
                                color: #3498db;
                                font-size: 18px;
                            ">🎁 奇遇奖励：</h4>
                            <div class="adventure-rewards-list" style="
                                background: rgba(0, 0, 0, 0.3);
                                border-radius: 10px;
                                padding: 15px;
                                margin-bottom: 20px;
                            ">
                                ${this.renderAdventureRewards(adventureEvent.rewards || [])}
                            </div>
                        </div>
                    </div>
                    <div class="adventure-event-footer">
                        <button class="adventure-confirm-btn" onclick="this.closest('.adventure-event-popup').remove()" style="
                            background: linear-gradient(135deg, #27ae60, #2ecc71);
                            color: white;
                            border: none;
                            border-radius: 25px;
                            padding: 12px 30px;
                            font-size: 16px;
                            font-weight: bold;
                            cursor: pointer;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
                        " onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                            确认收取
                        </button>
                    </div>
                </div>
            </div>
        `;

        // 添加动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes adventurePopupIn {
                0% {
                    transform: scale(0.5) rotate(-10deg);
                    opacity: 0;
                }
                50% {
                    transform: scale(1.1) rotate(5deg);
                }
                100% {
                    transform: scale(1) rotate(0deg);
                    opacity: 1;
                }
            }
        `;
        document.head.appendChild(style);

        // 添加到页面
        document.body.appendChild(adventurePopup);

        // 添加淡入效果
        setTimeout(() => {
            const overlay = adventurePopup.querySelector('.adventure-event-overlay');
            if (overlay) {
                overlay.style.opacity = '1';
            }
        }, 10);

        console.log('✅ 奇遇事件弹窗已显示');
    }

    /**
     * 🎲 辅助函数：渲染奇遇奖励列表
     */
    renderAdventureRewards(rewards) {
        if (!rewards || rewards.length === 0) {
            return '<p class="no-rewards" style="color: #95a5a6; font-style: italic;">暂无奖励信息</p>';
        }

        return rewards
            .map(reward => {
                let rewardIcon = '🎁';
                let rewardColor = '#f39c12';

                switch (reward.type) {
                    case 'spirit_stones':
                        rewardIcon = '💎';
                        rewardColor = '#3498db';
                        break;
                    case 'item':
                        rewardIcon = '📦';
                        rewardColor = '#e74c3c';
                        break;
                    case 'equipment':
                        rewardIcon = '⚔️';
                        rewardColor = '#9b59b6';
                        break;
                    case 'recipe_learned':
                        rewardIcon = '📜';
                        rewardColor = '#f1c40f';
                        break;
                    case 'secret_realm_key':
                        rewardIcon = '🗝️';
                        rewardColor = '#e67e22';
                        break;
                    default:
                        rewardIcon = '🎁';
                        rewardColor = '#f39c12';
                        break;
                }

                return `
                <div class="adventure-reward-item" style="
                    display: flex;
                    align-items: center;
                    margin-bottom: 10px;
                    padding: 8px;
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 8px;
                    color: ${rewardColor};
                ">
                    <span class="reward-icon" style="
                        font-size: 20px;
                        margin-right: 10px;
                    ">${rewardIcon}</span>
                    <span class="reward-description" style="
                        flex: 1;
                        font-size: 14px;
                        font-weight: bold;
                    ">${reward.description}</span>
                </div>
            `;
            })
            .join('');
    }

    /**
     * 🔧 新增：关闭物品详情弹窗
     */
    closeItemDetailPopup() {
        const itemDetailPopup = document.querySelector('.item-detail-popup');
        if (itemDetailPopup) {
            itemDetailPopup.remove();
            console.log('✅ 物品详情弹窗已关闭');
        }

        const simpleItemDetail = document.getElementById('simple-item-detail');
        if (simpleItemDetail) {
            simpleItemDetail.remove();
            console.log('✅ 简单物品详情已关闭');
        }
    }

    /**
     * 🆕 检查是否为竞技场战斗
     */
    checkIsArenaBattle() {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('arena') === '1';
    }

    /**
     * 🏆 竞技场专用胜利面板
     */
    async showArenaVictoryPanel(message, saveResult) {
        console.log('🏆 开始显示竞技场专用胜利面板:', { message, saveResult });

        // 清除可能存在的旧面板
        this.closeVictoryPanel();

        // 创建竞技场专用面板
        const arenaOverlay = this.createArenaVictoryPanel(message, saveResult);
        if (!arenaOverlay) {
            console.error('❌ 创建竞技场胜利面板失败');
            return;
        }

        // 添加到页面
        document.body.appendChild(arenaOverlay);

        // 显示面板
        setTimeout(() => {
            arenaOverlay.style.opacity = '1';
        }, 50);

        console.log('✅ 竞技场胜利面板显示成功');
    }

    /**
     * 🎨 创建竞技场专用胜利面板HTML
     */
    createArenaVictoryPanel(message, saveResult) {
        const isVictory = message.includes('玩家胜利') || message.includes('论道胜利');
        const isAiOpponent = saveResult.rewards?.is_ai_opponent || false;
        const spiritStones = saveResult.rewards?.spirit_stones || 0;
        const rankPoints = saveResult.rewards?.rank_points || 0;

        // 根据战斗结果确定标题和样式
        const title = isVictory ? '🏆 论道胜利!' : '💫 论道败阵';
        const titleColor = isVictory ? '#FFD700' : '#FF6B6B';
        const subtitle = isAiOpponent
            ? isVictory
                ? '击败灵智傀儡'
                : '败于灵智傀儡'
            : isVictory
            ? '真人论道胜利'
            : '真人论道败阵';

        const overlayHTML = `
            <div class="game-overlay arena-victory-overlay" style="opacity: 0; transition: opacity 0.3s ease-in-out;">
                <div class="victory-panel arena-victory-panel compact">
                    <!-- 竞技场专用标题区域 -->
                    <div class="arena-victory-header">
                        <h2 class="arena-victory-title" style="color: ${titleColor}; margin: 5px 0; font-size: 18px;">${title}</h2>
                        <p class="arena-victory-subtitle" style="margin: 3px 0; font-size: 12px;">${subtitle}</p>
                    </div>
                    
                    <!-- 竞技场奖励区域 -->
                    <div class="arena-rewards-section" style="margin: 8px 0; padding: 8px;">
                        <h3 style="margin: 3px 0; font-size: 14px;">📿 论道奖励</h3>
                        <div class="arena-reward-item" style="display: flex; justify-content: space-between; margin: 3px 0; font-size: 12px;">
                            <span><span class="reward-icon">💎</span> 灵石奖励:</span>
                            <span class="reward-value spirit-stones">+${spiritStones}</span>
                        </div>
                        <div class="arena-reward-item" style="display: flex; justify-content: space-between; margin: 3px 0; font-size: 12px;">
                            <span><span class="reward-icon">⭐</span> 段位积分:</span>
                            <span class="reward-value rank-points">${
                                rankPoints >= 0 ? '+' : ''
                            }${rankPoints}</span>
                        </div>
                        ${
                            isAiOpponent
                                ? '<p class="ai-puppet-notice" style="font-size: 10px; margin: 3px 0; color: #888;">📝 灵智傀儡奖励已调整</p>'
                                : ''
                        }
                    </div>
                    
                    <!-- 竞技场统计信息 -->
                    <div class="arena-stats-section" style="margin: 8px 0; padding: 8px;">
                        <h3 style="margin: 3px 0; font-size: 14px;">📊 论道统计</h3>
                        <div class="arena-stats-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: 5px; font-size: 11px;">
                            <div class="stat-item">
                                <span class="stat-label">今日挑战:</span>
                                <span class="stat-value">${
                                    saveResult.arena_stats?.arena_daily_attempts || 0
                                }/10</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">连胜记录:</span>
                                <span class="stat-value">${
                                    saveResult.arena_stats?.arena_win_streak || 0
                                }场</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">总胜率:</span>
                                <span class="stat-value">${this.calculateWinRate(
                                    saveResult.arena_stats
                                )}%</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 竞技场专用按钮 -->
                    <div class="arena-victory-buttons" style="display: flex; gap: 8px; margin-top: 10px;">
                        <button class="victory-btn continue-btn" onclick="window.arenaVictoryManager.continueArena()" style="flex: 1; padding: 8px; font-size: 12px; background: #28a745; color: white; border: none; border-radius: 4px;">
                            🏆 继续论道
                        </button>
                        <button class="victory-btn exit-btn" onclick="window.arenaVictoryManager.exitToArena()" style="flex: 1; padding: 8px; font-size: 12px; background: #6c757d; color: white; border: none; border-radius: 4px;">
                            📋 返回大厅
                        </button>
                    </div>
                </div>
            </div>
        `;

        // 创建DOM元素
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = overlayHTML;
        const overlay = tempDiv.firstElementChild;

        // 设置全局引用便于按钮调用
        window.arenaVictoryManager = {
            continueArena: () => this.continueArena(),
            exitToArena: () => this.exitToArena(),
        };

        return overlay;
    }

    /**
     * 🧮 计算胜率
     */
    calculateWinRate(arenaStats) {
        if (!arenaStats) return 0;
        const wins = arenaStats.arena_total_wins || 0;
        const battles = arenaStats.arena_total_battles || 0;
        return battles > 0 ? Math.round((wins / battles) * 100) : 0;
    }

    /**
     * 🔄 继续论道（立即开始新一轮匹配）
     */
    continueArena() {
        console.log('🔄 继续论道 - 立即开始新匹配');
        this.closeVictoryPanel();
        // 跳转回竞技场并自动开始匹配
        window.location.href = 'immortal_arena.html?auto_match=1';
    }

    /**
     * 🚪 退出到竞技场大厅（查看战绩、购买次数等）
     */
    exitToArena() {
        console.log('🚪 退出到竞技场大厅 - 查看战绩和管理');
        this.closeVictoryPanel();
        // 返回竞技场主页（不自动匹配）
        window.location.href = 'immortal_arena.html';
    }

    /**
     * 🆕 前进到下一层（胜利时使用）
     */
    async goToNextStage() {
        console.log('🎉 胜利：前进到下一层');

        try {
            // 移除胜利面板
            const overlayToRemove = document.querySelector('.game-overlay');
            if (overlayToRemove) overlayToRemove.remove();

            // 🔧 修复：获取地图ID
            const currentArea = this.battleSystem.dataManager.getCurrentArea();
            const mapId = currentArea ? currentArea.mapId : 1;

            // 🆕 发送请求更新地图进度到下一层
            const apiUrl = window.GameConfig
                ? window.GameConfig.getApiUrl('update_map_progress.php')
                : '../src/api/update_map_progress.php';
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=advance_stage&current_stage=${this.battleSystem.dataManager.currentStage}&map_id=${mapId}`,
            });

            const responseText = await response.text();
            console.log('🔍 前进API响应内容:', responseText);

            let result;
            try {
                result = JSON.parse(responseText);
            } catch (parseError) {
                console.error('❌ JSON解析失败:', parseError);
                console.error('❌ 响应内容:', responseText);
                throw new Error('服务器返回无效JSON格式: ' + responseText.substring(0, 100));
            }

            if (result.success) {
                console.log('✅ 地图进度已更新到下一层');
                // 🚀 使用无刷新方式进入下一关
                await this.proceedToNextStageWithoutRefresh();
            } else {
                console.error('❌ 更新地图进度失败:', result.message);
                this.battleSystem.updateBattleStatus('⚠️ 进入下一层失败，请重试');
            }
        } catch (error) {
            console.error('❌ 前进到下一层时发生错误:', error);
            this.battleSystem.updateBattleStatus('⚠️ 进入下一层失败，请重试');
        }
    }

    /**
     * 🆕 回退到上一层（失败时使用）
     */
    async goToPreviousStage() {
        console.log('🔻 失败：回退到上一层');

        try {
            // 移除胜利面板
            const overlayToRemove = document.querySelector('.game-overlay');
            if (overlayToRemove) overlayToRemove.remove();

            // 🔧 修复：获取地图ID
            const currentArea = this.battleSystem.dataManager.getCurrentArea();
            const mapId = currentArea ? currentArea.mapId : 1;

            // 🆕 发送请求更新地图进度到上一层
            const apiUrl = window.GameConfig
                ? window.GameConfig.getApiUrl('update_map_progress.php')
                : '../src/api/update_map_progress.php';
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=retreat_stage&current_stage=${this.battleSystem.dataManager.currentStage}&map_id=${mapId}`,
            });

            const responseText = await response.text();
            console.log('🔍 回退API响应内容:', responseText);

            let result;
            try {
                result = JSON.parse(responseText);
            } catch (parseError) {
                console.error('❌ JSON解析失败:', parseError);
                console.error('❌ 响应内容:', responseText);
                throw new Error('服务器返回无效JSON格式: ' + responseText.substring(0, 100));
            }

            if (result.success) {
                console.log('✅ 地图进度已回退到上一层');
                // 🚀 使用无刷新方式回到上一关
                await this.goBackToPreviousStageWithoutRefresh();
            } else {
                console.error('❌ 更新地图进度失败:', result.message);
                this.battleSystem.updateBattleStatus('⚠️ 回到上一层失败，请重试');
            }
        } catch (error) {
            console.error('❌ 回退到上一层时发生错误:', error);
            this.battleSystem.updateBattleStatus('⚠️ 回到上一层失败，请重试');
        }
    }

    /**
     * 🆕 无刷新方式回到上一关
     */
    async goBackToPreviousStageWithoutRefresh() {
        console.log('🔄 无刷新方式回到上一关');

        try {
            // 显示加载状态
            this.battleSystem.updateBattleStatus('🔄 正在回到上一关...');

            // 关闭胜利面板
            const overlayToRemove = document.querySelector('.game-overlay');
            if (overlayToRemove) overlayToRemove.remove();

            // 🔧 关键修复：完全重置战斗状态，与下一层方法保持一致
            this.battleSystem.isGameOver = false;
            this.battleSystem.battleEndTime = null;
            this.battleSystem.attackCount = 0; // 🔧 重置攻击计数
            console.log('🔄 重置战斗状态: {isGameOver: false, attackCount: 0}');

            // 计算上一层的层数
            const previousStage = Math.max(1, this.battleSystem.dataManager.currentStage - 1);
            this.battleSystem.dataManager.currentStage = previousStage;

            // 🔧 修复：使用优化后的统一数据重新加载函数（包含武器数据，不保持层数）
            console.log('🔄 统一重新加载上一关数据（包含武器数据）...');
            await this.reloadAllBattleData(true, false); // 包含武器数据，不保持当前层数

            // 重新初始化角色对象
            await this.reinitializeBattleCharacters();

            // 🔧 修复：确保怪物名字颜色正确显示
            await this.updateEnemyNameColor();

            // 开始新的战斗
            this.battleSystem.updateBattleStatus('🎯 上一关战斗开始！');

            // 延迟一点开始战斗，让用户看到状态更新
            setTimeout(async () => {
                if (!this.battleSystem.isGameOver) {
                    console.log('🎯 启动上一关战斗');
                    await this.battleSystem.battleFlowManager.autoBattle();
                }
            }, 500);

            console.log(`✅ 已切换到第${previousStage}层并开始战斗`);
        } catch (error) {
            console.error('❌ 回到上一关时发生错误:', error);

            // 错误处理：回退到页面刷新方式
            this.battleSystem.updateBattleStatus('⚠️ 上一关数据加载失败，使用备用方案...');

            // 给用户一点时间看到错误信息
            setTimeout(() => {
                console.log('🔄 回退到页面刷新方式');
                window.location.reload();
            }, 2000);
        }
    }

    /**
     * 🎨 更新敌人名字颜色
     */
    async updateEnemyNameColor() {
        try {
            // 🔧 修复：直接调用敌人角色对象的updateUI方法，确保颜色正确显示
            if (this.battleSystem.enemy && typeof this.battleSystem.enemy.updateUI === 'function') {
                this.battleSystem.enemy.updateUI();
                console.log(
                    `🎨 敌人UI已更新: ${this.battleSystem.enemy.name} (类型: ${this.battleSystem.enemy.type})`
                );
            } else {
                console.log('⚠️ 敌人角色对象不存在或无updateUI方法，跳过UI更新');
            }
        } catch (error) {
            console.error('❌ 更新敌人名字颜色失败:', error);
        }
    }
}
