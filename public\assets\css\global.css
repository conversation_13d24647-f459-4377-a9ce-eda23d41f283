/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
}

/* 🍎 iOS系统全局优化 */
/* 检测iOS设备并应用特定优化 */
@supports (-webkit-touch-callout: none) {
    /* iOS Safari 专用优化 */
    html {
        /* 防止iOS Safari地址栏影响视口高度 */
        height: 100vh;
        height: calc(var(--vh, 1vh) * 100);
        min-height: 100vh;
        min-height: calc(var(--vh, 1vh) * 100);        
        /* iOS滚动优化 */
        -webkit-overflow-scrolling: touch;
        /* 防止iOS缩放 */
        -webkit-text-size-adjust: 100%;
        /* 优化触摸响应 */
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        user-select: none;
    }
    
    body {
        /* iOS Safari 视口高度修复 */
        height: 100vh;
        height: calc(var(--vh, 1vh) * 100);
        min-height: 100vh;
        min-height: calc(var(--vh, 1vh) * 100);
        /* iOS滚动优化 */
        -webkit-overflow-scrolling: touch;
        /* 防止iOS橡皮筋效果 */
        overscroll-behavior: contain;
        /* iOS触摸优化 */
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        user-select: none;
        /* iOS字体渲染优化 */
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
    
    /* iOS输入框优化 */
    input, textarea, select {
        -webkit-appearance: none;
        appearance: none;
        -webkit-border-radius: 0;
        border-radius: 0;
        /* 防止iOS自动缩放 */
        font-size: 16px;
        /* iOS输入框阴影移除 */
        -webkit-box-shadow: none;
        box-shadow: none;
        /* 允许输入框选择文本 */
        -webkit-user-select: text;
        user-select: text;
    }
    
    /* iOS按钮优化 */
    button {
        -webkit-appearance: none;
        appearance: none;
        -webkit-border-radius: 0;
        border-radius: 0;
        /* iOS按钮触摸反馈 */
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
        /* iOS按钮文字选择禁用 */
        -webkit-user-select: none;
        user-select: none;
    }
    
    /* iOS链接优化 */
    a {
        -webkit-touch-callout: none;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    }
    
    /* iOS滚动容器优化 */
    .main-container,
    .attribute-panel,
    .modal-content {
        -webkit-overflow-scrolling: touch;
        /* iOS滚动惯性 */
        scroll-behavior: smooth;
        /* 防止iOS滚动回弹 */
        overscroll-behavior: contain;
    }
}

/* 🍎 iOS设备特定优化 - 适用于非PWA模式 */
@media (display-mode: browser) and (-webkit-touch-callout: none) {
/* iPhone X及以上设备（刘海屏） */
@media only screen 
    and (device-width: 375px) 
    and (device-height: 812px) 
    and (-webkit-device-pixel-ratio: 3) {
    .main-container {
        padding-top: calc(10px + 44px); /* 状态栏高度 */
        padding-bottom: calc(90px + 34px); /* Home指示器高度 */
    }
}

/* iPhone XR, 11 */
@media only screen 
    and (device-width: 414px) 
    and (device-height: 896px) 
    and (-webkit-device-pixel-ratio: 2) {
    .main-container {
        padding-top: calc(10px + 44px);
        padding-bottom: calc(90px + 34px);
    }
}

/* iPhone 12, 13, 14 */
@media only screen 
    and (device-width: 390px) 
    and (device-height: 844px) 
    and (-webkit-device-pixel-ratio: 3) {
    .main-container {
        padding-top: calc(10px + 47px);
        padding-bottom: calc(90px + 34px);
    }
}

/* iPhone 12 Pro Max, 13 Pro Max, 14 Plus */
@media only screen 
    and (device-width: 428px) 
    and (device-height: 926px) 
    and (-webkit-device-pixel-ratio: 3) {
    .main-container {
        padding-top: calc(10px + 47px);
        padding-bottom: calc(90px + 34px);
        }
    }
}

/* 🍎 iOS触摸事件优化 */
@supports (-webkit-touch-callout: none) {
    /* 游戏界面触摸优化 */
    .game-button,
    .menu-button,
    .nav-btn,
    .attribute-compact-item {
        /* iOS触摸反馈 */
        -webkit-tap-highlight-color: rgba(255, 215, 0, 0.2);
        /* iOS触摸延迟消除 */
        touch-action: manipulation;
        /* iOS按钮3D触摸禁用 */
        -webkit-touch-callout: none;
    }
    
    /* iOS滚动区域优化 */
    .attributes-section,
    .inventory-grid,
    .battle-log {
        /* iOS惯性滚动 */
        -webkit-overflow-scrolling: touch;
        /* iOS滚动边界处理 */
        overscroll-behavior: contain;
        /* iOS滚动性能优化 */
        will-change: scroll-position;
    }
    
    /* 🍎 iOS弹窗遮罩层修复 */
    .item-detail-popup,
    .modal-overlay,
    .popup-overlay {
        /* iOS遮罩层显示修复 */
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
        /* iOS背景模糊修复 */
        -webkit-backdrop-filter: blur(8px);
        backdrop-filter: blur(8px);
        /* iOS层级修复 */
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        /* iOS安全区域适配 */
        padding-top: env(safe-area-inset-top);
        padding-bottom: env(safe-area-inset-bottom);
        padding-left: env(safe-area-inset-left);
        padding-right: env(safe-area-inset-right);
        /* iOS滚动禁用 */
        overflow: hidden;
        /* iOS触摸穿透防止 */
        touch-action: none;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        user-select: none;
    }
    
    /* iOS弹窗内容区域修复 */
    .popup-container,
    .popup-content-area {
        /* iOS内容区域定位修复 */
        position: relative !important;
        /* iOS层级确保 */
        z-index: inherit;
        /* iOS触摸恢复 */
        touch-action: auto;
        -webkit-user-select: text;
        user-select: text;
        /* iOS滚动恢复 */
        -webkit-overflow-scrolling: touch;
    }
}

/* iOS滚动兼容 */
html {
    width: 100%;
    height: 100vh; /* 🔧 改为100vh确保PWA模式下正确 */
    height: calc(var(--vh, 1vh) * 100); /* 🔧 新增：支持动态视口高度 */
    margin: 0;
    padding: 0;
    overflow: auto; /* 🔧 改为auto允许滚动 */
    -webkit-overflow-scrolling: touch;
}

body {
    height: 100vh; /* 🔧 改为100vh确保PWA模式下正确 */
    height: calc(var(--vh, 1vh) * 100); /* 🔧 新增：支持动态视口高度 */
    width: 100%;
    max-width: 648px;
    margin: 0 auto;
    overflow: auto; /* 🔧 改为auto允许滚动 */
    -webkit-overflow-scrolling: touch;
    overscroll-behavior-y: contain; /* 🔧 改为contain而不是none，允许内部滚动 */
    position: relative; /* 🔧 改为relative，避免PWA模式下的fixed问题 */
    font-family: 'Microsoft YaHei', serif;
    background: url(../images/game_bg.jpg) no-repeat center center fixed;
    background-size: cover;
    z-index: 0;
    /* 🔧 新增：确保页面完全填充视口 */
    padding: 0 !important;
}

/* 中国风背景装饰 - 半透明遮罩 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        linear-gradient(rgba(26, 58, 92, 0.7), rgba(45, 89, 132, 0.7)),
        radial-gradient(circle at 20% 30%, rgba(255, 215, 0, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 80% 70%, rgba(255, 215, 0, 0.06) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

.bottom-navigation{
    position: fixed;
    max-width: 648px;
    margin: 0 auto !important;
}

/* 主要内容区域 */
.main-container {
    padding: 10px 10px 80px 10px; /* 增加底部空间为80px，给导航栏留出足够空间 */
    height: 100vh;
    height: calc(var(--vh, 1vh) * 100); /* 🔧 添加动态高度支持 */
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    overflow: auto; /* 🔧 改为auto允许滚动 */
    -webkit-overflow-scrolling: touch; /* 🔧 添加iOS平滑滚动 */
    margin: 0; /* 🔧 确保没有边距 */
    /* 🔧 新增：PWA模式下特殊处理 */
    box-sizing: border-box;
}

/* 🔧 移动端主容器适配 */
@media (max-width: 768px) {
    .main-container {
        padding-bottom: calc(70px + env(safe-area-inset-bottom, 0px) + 15px) !important;
        height: 100vh !important;
        height: calc(var(--vh, 1vh) * 100) !important;
    }
}

/* iPhone SE2 (375x667) 和类似小尺寸手机适配 */
@media (max-width: 375px) {
    .main-container {
        padding-bottom: calc(65px + env(safe-area-inset-bottom, 0px) + 15px) !important;
        height: 100vh !important;
        height: calc(var(--vh, 1vh) * 100) !important;
    }
}

/* iPhone SE1 (320x568) 和超小屏手机适配 */
@media (max-width: 320px) {
    .main-container {
        padding-bottom: calc(60px + env(safe-area-inset-bottom, 0px) + 15px) !important;
        height: 100vh !important;
        height: calc(var(--vh, 1vh) * 100) !important;
    }
}

/* 横屏模式适配 */
@media (orientation: landscape) and (max-height: 500px) {
    .main-container {
        padding-bottom: calc(55px + env(safe-area-inset-bottom, 0px) + 15px) !important;
        height: 100vh !important;
        height: calc(var(--vh, 1vh) * 100) !important;
    }
}

/* 特别针对iPhone SE2的高度适配 */
@media (max-width: 375px) and (max-height: 667px) {
    .main-container {
        padding-bottom: calc(65px + env(safe-area-inset-bottom, 0px) + 15px) !important;
        padding-top: 8px !important;
        padding-left: 8px !important;
        padding-right: 8px !important;
        height: 100vh !important;
        height: calc(var(--vh, 1vh) * 100) !important;
    }
}

/* 🔧 新增：PWA模式下的特殊处理 */
@media (display-mode: standalone) {
    html, body {
        /* PWA模式下完全填充屏幕 */
        height: 100vh !important;
        height: calc(var(--vh, 1vh) * 100) !important;
        width: 100vw !important;
        /* PWA模式下的边距和填充重置 */
        margin: 0 !important;
        padding: 0 !important;
        /* PWA模式下的定位和溢出处理 */
        position: relative !important;
        overflow: hidden !important; /* 🔧 改为hidden防止滚动条 */
        -webkit-overflow-scrolling: touch !important;
        /* PWA模式下的外观优化 */
        -webkit-user-select: none;
        user-select: none;
    }
    
    /* PWA模式下的body特殊处理 */
    body {
        /* 确保背景图片正确显示 */
        background: url(../images/game_bg.jpg) no-repeat center center !important;
        background-size: cover !important;
        background-attachment: fixed !important;
        /* 强制填充整个屏幕包括安全区域 */
        min-height: 100vh !important;
        min-height: calc(var(--vh, 1vh) * 100) !important;
        /* PWA模式下的底部贴合 - 移除所有padding避免白色区域 */
        padding: 0 !important;
        margin: 0 !important;
        /* 确保背景色填充（防止图片加载失败时出现白色） */
        background-color: #1a3a5c !important;
        /* 强制溢出隐藏 */
        overflow: hidden !important;
    }
    
    /* PWA模式下的背景遮罩层 */
    body::before {
        /* 确保遮罩层完全覆盖包括安全区域 */
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        height: calc(var(--vh, 1vh) * 100) !important;
        background: 
            linear-gradient(rgba(26, 58, 92, 0.7), rgba(45, 89, 132, 0.7)),
            radial-gradient(circle at 20% 30%, rgba(255, 215, 0, 0.08) 0%, transparent 50%),
            radial-gradient(circle at 80% 70%, rgba(255, 215, 0, 0.06) 0%, transparent 50%) !important;
        pointer-events: none !important;
        z-index: -1 !important;
    }
    
    .main-container {
        /* PWA模式下的容器尺寸 */
        height: 100vh !important;
        height: calc(var(--vh, 1vh) * 100) !important;
        width: 100vw !important;
        /* PWA模式下的边距重置 */
        margin: 0 !important;
        /* PWA模式下的定位 */
        position: relative !important;
        /* PWA模式下的填充调整 - 正确处理安全区域 */
        padding-top: calc(env(safe-area-inset-top, 0px) + 20px) !important;
        padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 90px) !important;
        padding-left: calc(env(safe-area-inset-left, 0px) + 15px) !important;
        padding-right: calc(env(safe-area-inset-right, 0px) + 15px) !important;
        /* PWA模式下的盒模型 */
        box-sizing: border-box !important;
        /* PWA模式下的滚动处理 */
        overflow-y: auto !important;
        overflow-x: hidden !important;
        -webkit-overflow-scrolling: touch !important;
        /* PWA模式下的显示优化 */
        display: flex !important;
        flex-direction: column !important;
    }
    
    /* 🔧 特别针对PWA模式的底部处理 - 确保完全贴合 */
    html {
        /* 确保html元素完全填充 */
        min-height: 100vh !important;
        min-height: calc(var(--vh, 1vh) * 100) !important;
        background: #1a3a5c !important; /* 设置兜底背景色 */
    }
    
    /* PWA模式下视口元标签支持 */
    @supports (padding: max(0px)) {
        .main-container {
            padding-top: max(env(safe-area-inset-top, 0px), 20px) !important;
            padding-bottom: max(env(safe-area-inset-bottom, 0px), 90px) !important;
            padding-left: max(env(safe-area-inset-left, 0px), 15px) !important;
            padding-right: max(env(safe-area-inset-right, 0px), 15px) !important;
        }
    }
    
    /* 🔧 PWA模式下遮罩层强制覆盖 - 只针对物品详情弹窗 */
    .item-detail-popup,
    .modal-overlay,
    .popup-overlay {
        z-index: 99999 !important;
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        height: calc(var(--vh, 1vh) * 100) !important;
        /* 强制覆盖所有内容包括导航栏 */
        -webkit-transform: translateZ(0) !important;
        transform: translateZ(0) !important;
        /* 确保遮罩层完全不透明的背景 */
        background: rgba(0, 0, 0, 0.7) !important;
        /* 禁用所有交互 */
        touch-action: none !important;
        -webkit-touch-callout: none !important;
        -webkit-user-select: none !important;
        user-select: none !important;
        /* 确保遮罩层在最顶层 */
        isolation: isolate !important;
    }
    
    /* 🔧 属性系统遮罩层不强制覆盖，保持原始样式 */
    .attribute-popup-overlay {
        /* 重置为属性系统原始样式，不强制覆盖 */
        z-index: auto !important;
        background: transparent !important;
        position: static !important;
        width: auto !important;
        height: auto !important;
    }
    
    /* 🔧 属性系统遮罩层在PWA模式下的特殊处理 */
    .attribute-popup-overlay {
        /* 在PWA模式下保持属性系统原有的遮罩层样式 */
        position: inherit !important;
        z-index: inherit !important;
        background: inherit !important;
        width: inherit !important;
        height: inherit !important;
        /* 确保属性系统的遮罩层不被全局PWA样式覆盖 */
        top: inherit !important;
        left: inherit !important;
        right: inherit !important;
        bottom: inherit !important;
    }
    
    /* PWA模式下导航栏层级调整 */
    .bottom-nav,
    .navigation-bar,
    .nav-container {
        z-index: 9998 !important; /* 比遮罩层低一级 */
        /* 确保导航栏正确贴合底部 */
        position: fixed !important;
        bottom: env(safe-area-inset-bottom, 0px) !important;
        left: env(safe-area-inset-left, 0px) !important;
        right: env(safe-area-inset-right, 0px) !important;
    }
}

/* 🔧 新增：强制消除所有可能的间隙 */
html, body, .main-container {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

/* 🔧 新增：移动端浏览器特殊处理 */
@supports (-webkit-touch-callout: none) {
    /* iOS Safari */
    html, body {
        height: 100vh;
        height: calc(var(--vh, 1vh) * 100);
        min-height: 100vh;
        min-height: calc(var(--vh, 1vh) * 100);
    }
}

/* 弹窗滚动优化 */
.modal-content {
    max-height: calc(100vh - 160px);
    max-height: calc(var(--vh, 1vh) * 100 - 160px);
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

/* 滚动条美化 */
::-webkit-scrollbar {
    width: 4px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
}

::-webkit-scrollbar-thumb {
    background: rgba(212, 175, 55, 0.5);
    border-radius: 2px;
}

/* 禁用默认滚动回弹效果 */
.no-bounce {
    overscroll-behavior: none;
    -webkit-overflow-scrolling: auto;
}

/* 🆕 循环挂机模式提示动画 */
@keyframes hintGlow {
    from {
        text-shadow: 0 0 4px rgba(255, 165, 0, 0.6);
        opacity: 0.8;
    }
    to {
        text-shadow: 0 0 8px rgba(255, 165, 0, 0.9), 0 0 12px rgba(255, 165, 0, 0.5);
        opacity: 1;
    }
} 

