/**
 * 🏗️ 战斗系统管理器注册中心
 * 统一管理所有战斗系统管理器，解决循环依赖和耦合度问题
 * 实现依赖注入和统一错误处理
 */
class ManagerRegistry {
    constructor() {
        this.managers = new Map();
        this.dependencies = new Map();
        this.errorHandlers = new Map();
        this.initializationOrder = [];
        
        console.log('🏗️ 管理器注册中心已初始化');
    }

    /**
     * 注册管理器
     * @param {string} name 管理器名称
     * @param {Object} manager 管理器实例
     * @param {Array} dependencies 依赖的管理器名称列表
     */
    register(name, manager, dependencies = []) {
        if (this.managers.has(name)) {
            console.warn(`⚠️ 管理器 ${name} 已存在，将被覆盖`);
        }

        this.managers.set(name, manager);
        this.dependencies.set(name, dependencies);
        
        // 添加标准方法
        if (manager && typeof manager === 'object') {
            // 添加错误处理方法
            manager.handleError = (error, context) => this.handleError(name, error, context);
            
            // 添加获取依赖方法
            manager.getDependency = (depName) => this.get(depName);
            
            // 添加管理器标识
            manager._managerName = name;
        }

        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('manager-registry', `✅ 管理器已注册: ${name}, 依赖: [${dependencies.join(', ')}]`);
        }
    }

    /**
     * 获取管理器实例
     * @param {string} name 管理器名称
     * @returns {Object|null} 管理器实例
     */
    get(name) {
        const manager = this.managers.get(name);
        if (!manager) {
            console.warn(`⚠️ 管理器 ${name} 未找到`);
            return null;
        }
        return manager;
    }

    /**
     * 检查管理器是否存在
     * @param {string} name 管理器名称
     * @returns {boolean}
     */
    has(name) {
        return this.managers.has(name);
    }

    /**
     * 解析依赖关系并确定初始化顺序
     * @returns {Array} 按依赖关系排序的管理器名称列表
     */
    resolveDependencies() {
        const resolved = [];
        const visiting = new Set();
        const visited = new Set();

        const visit = (name) => {
            if (visited.has(name)) return;
            if (visiting.has(name)) {
                throw new Error(`检测到循环依赖: ${name}`);
            }

            visiting.add(name);
            const deps = this.dependencies.get(name) || [];
            
            for (const dep of deps) {
                if (!this.managers.has(dep)) {
                    throw new Error(`依赖的管理器不存在: ${dep} (被 ${name} 依赖)`);
                }
                visit(dep);
            }

            visiting.delete(name);
            visited.add(name);
            resolved.push(name);
        };

        // 遍历所有管理器
        for (const name of this.managers.keys()) {
            visit(name);
        }

        this.initializationOrder = resolved;
        console.log('🔗 依赖关系解析完成，初始化顺序:', resolved);
        return resolved;
    }

    /**
     * 按依赖关系初始化所有管理器
     * @returns {Promise} 初始化完成的Promise
     */
    async initializeAll() {
        try {
            const order = this.resolveDependencies();
            
            for (const name of order) {
                const manager = this.managers.get(name);
                const dependencies = this.dependencies.get(name) || [];
                
                console.log(`🔄 初始化管理器: ${name}`);
                
                // 注入依赖
                this.injectDependencies(manager, dependencies);
                
                // 调用初始化方法（如果存在）
                if (manager && typeof manager.initialize === 'function') {
                    await manager.initialize();
                    console.log(`✅ ${name} 初始化完成`);
                }
            }
            
            console.log('🎉 所有管理器初始化完成');
            return true;
            
        } catch (error) {
            console.error('❌ 管理器初始化失败:', error);
            this.handleError('registry', error, 'initializeAll');
            return false;
        }
    }

    /**
     * 为管理器注入依赖
     * @param {Object} manager 管理器实例
     * @param {Array} dependencies 依赖列表
     */
    injectDependencies(manager, dependencies) {
        if (!manager || typeof manager !== 'object') return;

        for (const depName of dependencies) {
            const dependency = this.managers.get(depName);
            if (dependency) {
                // 使用setter方法注入（如果存在）
                const setterName = `set${depName.charAt(0).toUpperCase()}${depName.slice(1)}`;
                if (typeof manager[setterName] === 'function') {
                    manager[setterName](dependency);
                } else {
                    // 直接设置属性
                    const propName = depName.toLowerCase();
                    manager[propName] = dependency;
                }
                console.log(`🔗 ${manager._managerName} <- ${depName}`);
            }
        }
    }

    /**
     * 注册错误处理器
     * @param {string} managerName 管理器名称
     * @param {Function} handler 错误处理函数
     */
    registerErrorHandler(managerName, handler) {
        this.errorHandlers.set(managerName, handler);
    }

    /**
     * 统一错误处理
     * @param {string} managerName 发生错误的管理器名称
     * @param {Error} error 错误对象
     * @param {string} context 错误上下文
     */
    handleError(managerName, error, context = '') {
        const errorInfo = {
            manager: managerName,
            error: error,
            context: context,
            timestamp: new Date().toISOString()
        };

        // 记录错误日志
        console.error(`❌ [${managerName}] ${context}:`, error);

        // 调用特定的错误处理器
        const handler = this.errorHandlers.get(managerName);
        if (handler && typeof handler === 'function') {
            try {
                handler(errorInfo);
            } catch (handlerError) {
                console.error(`❌ 错误处理器执行失败 [${managerName}]:`, handlerError);
            }
        }

        // 调用全局错误处理器
        const globalHandler = this.errorHandlers.get('global');
        if (globalHandler && typeof globalHandler === 'function') {
            try {
                globalHandler(errorInfo);
            } catch (handlerError) {
                console.error('❌ 全局错误处理器执行失败:', handlerError);
            }
        }

        // 发送错误事件（供外部监听）
        if (typeof window !== 'undefined' && window.dispatchEvent) {
            const event = new CustomEvent('battleSystemError', {
                detail: errorInfo
            });
            window.dispatchEvent(event);
        }
    }

    /**
     * 获取管理器统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            totalManagers: this.managers.size,
            managerNames: Array.from(this.managers.keys()),
            initializationOrder: this.initializationOrder,
            dependencyGraph: Object.fromEntries(this.dependencies)
        };
    }

    /**
     * 清理所有管理器
     */
    async cleanup() {
        console.log('🧹 开始清理所有管理器...');
        
        // 按初始化顺序的反序清理
        const cleanupOrder = [...this.initializationOrder].reverse();
        
        for (const name of cleanupOrder) {
            const manager = this.managers.get(name);
            if (manager && typeof manager.cleanup === 'function') {
                try {
                    await manager.cleanup();
                    console.log(`✅ ${name} 清理完成`);
                } catch (error) {
                    console.error(`❌ ${name} 清理失败:`, error);
                }
            }
        }
        
        // 清空注册表
        this.managers.clear();
        this.dependencies.clear();
        this.errorHandlers.clear();
        this.initializationOrder = [];
        
        console.log('🧹 所有管理器清理完成');
    }

    /**
     * 重新加载管理器
     * @param {string} name 管理器名称
     */
    async reload(name) {
        const manager = this.managers.get(name);
        const dependencies = this.dependencies.get(name) || [];
        
        if (!manager) {
            console.warn(`⚠️ 管理器 ${name} 不存在，无法重新加载`);
            return false;
        }

        try {
            // 清理现有管理器
            if (typeof manager.cleanup === 'function') {
                await manager.cleanup();
            }

            // 重新注入依赖并初始化
            this.injectDependencies(manager, dependencies);
            
            if (typeof manager.initialize === 'function') {
                await manager.initialize();
            }

            console.log(`🔄 管理器 ${name} 重新加载完成`);
            return true;
            
        } catch (error) {
            console.error(`❌ 管理器 ${name} 重新加载失败:`, error);
            this.handleError(name, error, 'reload');
            return false;
        }
    }
}

// 创建全局管理器注册中心实例
window.ManagerRegistry = new ManagerRegistry();

// 全局错误处理器
window.ManagerRegistry.registerErrorHandler('global', (errorInfo) => {
    // 可以在这里添加错误上报、用户提示等逻辑
    console.log('🚨 全局错误处理:', errorInfo);
});

console.log('🏗️ 管理器注册中心已创建'); 