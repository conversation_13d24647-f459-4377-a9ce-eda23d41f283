<?php
// 🔧 最优先：完全禁用所有错误输出
error_reporting(0);
ini_set('display_errors', 0);
ini_set('log_errors', 0);

require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/equipment_stats_manager.php';

// 🔧 临时禁用错误输出，确保JSON响应纯净
error_reporting(0);
ini_set('display_errors', 0);

setJsonResponse();

// 添加调试模式
$debug = isset($_GET['debug']) ? true : false;

// 调试：检查会话状态
if ($debug) {
    error_log("Cultivation API Debug - Session ID: " . session_id());
    error_log("Cultivation API Debug - Session Data: " . json_encode($_SESSION));
    error_log("Cultivation API Debug - Cookies: " . json_encode($_COOKIE));
}

if (!isLoggedIn()) {
    $debugInfo = [];
    if ($debug) {
        $debugInfo = [
            'session_id' => session_id(),
            'session_status' => session_status(),
            'session_data' => $_SESSION,
            'cookies' => $_COOKIE,
            'headers' => getallheaders()
        ];
    }

    echo json_encode([
        'success' => false,
        'message' => '请先登录',
        'debug_info' => $debug ? $debugInfo : null
    ]);
    exit;
}

$pdo = getDatabase();
if (!$pdo) {
    echo json_encode(['success' => false, 'message' => '数据库连接失败']);
    exit;
}

$user = getCurrentUser();
$userId = $user['id'];

$action = isset($_GET['action']) ? $_GET['action'] : (isset($_POST['action']) ? $_POST['action'] : '');

try {
    switch ($action) {
        case 'check_db_structure':
            // 临时调试：检查数据库结构
            echo json_encode([
                'success' => true,
                'debug' => checkDatabaseStructure($pdo, $userId)
            ]);
            break;
            
        case 'get_cultivation_info':
            getCultivationInfo($pdo, $userId, $debug);
            break;
            
        case 'circulation':
            runCirculation($pdo, $userId);
            break;
            
        case 'breakthrough':
            attemptBreakthrough($pdo, $userId);
            break;
            
        case 'auto_cultivate':
            autoCultivate($pdo, $userId);
            break;
            
        case 'learn_technique':
            learnTechnique($pdo, $userId);
            break;
            
        case 'get_resources':
            getResources($pdo, $userId);
            break;
            
        case 'get_attributes':
            getAttributes($pdo, $userId);
            break;

        case 'get_user_data':
            // 🆕 统一的用户数据获取接口，替代equipment_integrated.php中的get_user_stats
            getUserData($pdo, $userId);
            break;
            
        case 'get_pills':
            getPills($pdo, $userId);
            break;
            
        case 'get_soul_status':
            getSoulStatus($pdo, $userId);
            break;
            
        case 'use_soul_pill':
            useSoulPill($pdo, $userId);
            break;
            
        case 'get_soul_pills':
            getSoulPills($pdo, $userId);
            break;
            
        // 🆕 新增：处理离线修炼收益
        case 'handle_offline_cultivation':
            $result = handleOfflineCultivation($pdo, $userId);
            echo json_encode($result);
            break;
            
        case 'get_realm_by_level':
            $level = isset($_GET['level']) ? intval($_GET['level']) : 1;
            
            try {
                $stmt = $pdo->prepare("SELECT * FROM realm_levels WHERE realm_level = ? LIMIT 1");
                $stmt->execute([$level]);
                $realm = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($realm) {
                    echo json_encode([
                        'success' => true,
                        'realm' => $realm
                    ]);
                } else {
                    echo json_encode([
                        'success' => false,
                        'message' => '找不到对应境界信息'
                    ]);
                }
            } catch (Exception $e) {
                echo json_encode([
                    'success' => false,
                    'message' => '获取境界信息失败: ' . $e->getMessage()
                ]);
            }
            break;
            
        case 'switch_technique':
            switchTechnique($pdo, $userId);
            break;
            
        case 'use_tribulation_pill':
            // 🆕 使用渡劫丹
            $input = json_decode(file_get_contents('php://input'), true);
            $pillId = isset($input['pill_id']) ? $input['pill_id'] : null;
            $pillName = isset($input['pill_name']) ? $input['pill_name'] : '';
            $successRateBonus = isset($input['success_rate_bonus']) ? $input['success_rate_bonus'] : 0;
            
            if (!$pillId) {
                throw new Exception('未指定渡劫丹ID');
            }
            
            $result = useTribulationPill($pdo, $userId, $pillId, $successRateBonus);
            echo json_encode($result);
            break;
            
        default:
            echo json_encode(['success' => false, 'message' => '未知的操作']);
            break;
    }
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => '系统错误: ' . $e->getMessage(), 'debug' => $debug ? $e->getTraceAsString() : null]);
}

// 获取修炼信息
function getCultivationInfo($pdo, $userId, $debug = false) {
    try {
        if ($debug) {
            error_log("=== 开始获取修炼信息 ===");
            error_log("用户ID: " . $userId);
        }
        
        // 检查表结构并获取境界信息
        $realmInfo = getUserRealmInfo($pdo, $userId, $debug);
        
        // 获取用户资源
        $resources = getUserResources($pdo, $userId, $debug);
        
        $result = [
            'success' => true,
            'cultivation' => [
                'current_realm' => $realmInfo['current_realm'],
                'next_realm' => $realmInfo['next_realm'],
                'breakthrough_rate' => $realmInfo['breakthrough_rate'],
                'can_breakthrough' => $realmInfo['can_breakthrough']
            ],
            'resources' => $resources
        ];
        
        if ($debug) {
            $result['debug'] = [
                'realm_info' => $realmInfo,
                'resources' => $resources,
                'breakthrough_rate' => $realmInfo['breakthrough_rate']
            ];
            error_log("修炼信息获取成功: " . json_encode($result));
        }
        
        echo json_encode($result);
        
    } catch (Exception $e) {
        $errorMsg = '获取修炼信息失败: ' . $e->getMessage();
        if ($debug) {
            error_log($errorMsg);
            error_log($e->getTraceAsString());
        }
        echo json_encode(['success' => false, 'message' => $errorMsg, 'debug' => $debug ? $e->getTraceAsString() : null]);
    }
}

// 获取用户境界信息（兼容不同数据库结构）
function getUserRealmInfo($pdo, $userId, $debug = false) {
    try {
        if ($debug) {
            error_log("=== 获取用户境界信息 ===");
        }
        
        // 获取角色的境界和修炼数据
        $stmt = $pdo->prepare("SELECT c.cultivation_points, c.realm_id, c.realm_progress FROM characters c WHERE c.user_id = ? LIMIT 1");
            $stmt->execute([$userId]);
        $characterData = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$characterData) {
            throw new Exception("角色不存在");
        }
        
        // 默认境界ID为1（开光期一阶）
        $currentRealmId = isset($characterData['realm_id']) ? $characterData['realm_id'] : 1;
        $currentQi = isset($characterData['cultivation_points']) ? $characterData['cultivation_points'] : 0;
        $realmProgress = isset($characterData['realm_progress']) ? $characterData['realm_progress'] : 0;
        
        // 从realm_levels表获取境界信息
        $stmt = $pdo->prepare("SELECT * FROM realm_levels WHERE id = ?");
        $stmt->execute([$currentRealmId]);
        $realmInfo = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$realmInfo) {
            // 如果找不到境界信息，使用默认值
            $realmInfo = [
                'id' => 1,
                'realm_name' => '开光期一阶',
                'realm_level' => 1,
                'max_experience' => 1000
            ];
        }
        
        // 获取下一境界信息
        $nextRealmId = $currentRealmId + 1;
        $stmt = $pdo->prepare("SELECT * FROM realm_levels WHERE id = ?");
        $stmt->execute([$nextRealmId]);
        $nextRealmInfo = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // 判断是否可以突破
        $canBreakthrough = $currentQi >= $realmInfo['max_experience'];
        
        // 计算突破成功率 - 基于境界等级计算基础成功率，再加上失败次数加成
        $breakthroughAttempts = getBreakthroughAttempts($pdo, $userId, $currentRealmId);
        $realmLevel = getCurrentRealmLevel($pdo, $currentRealmId);
        $baseSuccessRate = calculateBreakthroughRateByRealm($realmLevel);
        $attemptBonus = $breakthroughAttempts * 10; // 每次失败+10%
        $breakthroughRate = round(min($baseSuccessRate + $attemptBonus, 95)); // 🔧 四舍五入到整数，最高95%
        
        if ($debug) {
            error_log("角色数据: " . json_encode($characterData));
            error_log("境界信息: " . json_encode($realmInfo));
            error_log("下一境界: " . json_encode($nextRealmInfo));
            error_log("可以突破: " . ($canBreakthrough ? '是' : '否'));
            error_log("突破成功率计算 - 境界等级: {$realmLevel}, 基础成功率: {$baseSuccessRate}%, 失败次数: {$breakthroughAttempts}, 失败加成: {$attemptBonus}%, 最终成功率: {$breakthroughRate}%");
        }
        
        // 构造当前境界信息
        $currentRealm = [
            'realm_name' => $realmInfo['realm_name'],
            'level' => $realmInfo['realm_level'],
            'current_qi' => $currentQi,
            'exp_required' => $realmInfo['max_experience'],
            'breakthrough_attempts' => $breakthroughAttempts,
            'realm_id' => $currentRealmId
        ];
        
        // 构造下一境界信息
        $nextRealm = null;
        if ($nextRealmInfo) {
            $nextRealm = [
                'realm_name' => $nextRealmInfo['realm_name'],
                'level' => $nextRealmInfo['realm_level'],
                'exp_required' => $nextRealmInfo['max_experience'],
                'realm_id' => $nextRealmId
            ];
        }
        
        return [
            'current_realm' => $currentRealm,
                'next_realm' => $nextRealm,
                'breakthrough_rate' => $breakthroughRate,
            'can_breakthrough' => $canBreakthrough
        ];
        
    } catch (Exception $e) {
        if ($debug) {
            error_log("获取用户境界信息失败: " . $e->getMessage());
            error_log($e->getTraceAsString());
        }
        
        // 返回默认境界信息
        return [
            'current_realm' => [
                'realm_name' => '开光期一阶',
                'level' => 1,
                'current_qi' => 0,
                'exp_required' => 1000,
                'breakthrough_attempts' => 0,
                'realm_id' => 1
            ],
            'next_realm' => [
                'realm_name' => '开光期二阶',
                'level' => 2,
                'exp_required' => 2000,
                'realm_id' => 2
            ],
            'breakthrough_rate' => 50,
            'can_breakthrough' => false
        ];
    }
}

// 获取用户资源（兼容不同数据库结构）
function getUserResources($pdo, $userId, $debug = false) {
    try {
        if ($debug) {
            error_log("=== 获取用户资源信息 ===");
        }
        
        // 从users表获取基础资源
        $stmt = $pdo->prepare("SELECT spirit_stones, gold FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $userResources = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$userResources) {
            throw new Exception("用户不存在");
        }
        
        // 检查user_resources表是否存在，获取额外资源
        $extraResources = [];
        $stmt = $pdo->prepare("SHOW TABLES LIKE 'user_resources'");
        $stmt->execute();
        $userResourcesExists = $stmt->rowCount() > 0;
        
        if ($userResourcesExists) {
            $stmt = $pdo->prepare("SELECT * FROM user_resources WHERE user_id = ?");
            $stmt->execute([$userId]);
            $extraData = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($extraData) {
                $extraResources = $extraData;
            }
        }
        
        // 合并资源数据
        $resources = [
            'spirit_stones' => isset($userResources['spirit_stones']) ? $userResources['spirit_stones'] : 100,
            'silver' => isset($userResources['gold']) ? $userResources['gold'] : 10000, // 注意：数据库中是gold字段
            'qi_energy' => isset($extraResources['qi_energy']) ? $extraResources['qi_energy'] : 1000,
            'tribulation_pills' => isset($extraResources['tribulation_pills']) ? $extraResources['tribulation_pills'] : 5,
            'soul_pills' => isset($extraResources['soul_pills']) ? $extraResources['soul_pills'] : 3
        ];
        
        if ($debug) {
            error_log("用户基础资源: " . json_encode($userResources));
            error_log("用户额外资源: " . json_encode($extraResources));
            error_log("最终资源数据: " . json_encode($resources));
        }
        
        return $resources;
        
    } catch (Exception $e) {
        if ($debug) {
            error_log("获取用户资源失败: " . $e->getMessage());
            error_log($e->getTraceAsString());
        }
        
        // 返回默认资源
        return [
            'spirit_stones' => 100,
            'silver' => 10000,
            'qi_energy' => 1000,
            'tribulation_pills' => 5,
            'soul_pills' => 3
        ];
    }
}

// 周天运行（消耗灵气，稳固修为）
function runCirculation($pdo, $userId) {
    try {
        // 获取角色ID
        $stmt = $pdo->prepare("SELECT id FROM characters WHERE user_id = ? LIMIT 1");
        $stmt->execute([$userId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$character) {
            echo json_encode(['success' => false, 'message' => '角色不存在']);
            return;
        }
        
        $characterId = $character['id'];
        
        // 检查资源（检查user_resources表）
        $stmt = $pdo->prepare("SELECT * FROM user_resources WHERE user_id = ?");
        $stmt->execute([$userId]);
        $resources = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$resources) {
            echo json_encode(['success' => false, 'message' => '用户资源数据不存在']);
            return;
        }
        
        if ($resources['qi_energy'] < 50) {
            echo json_encode(['success' => false, 'message' => '灵气不足，无法运行周天（需要50灵气）']);
            return;
        }
        
        // 计算稳固的修为
        $stabilizedQi = rand(15, 25);
        
        // 🔧 获取角色功法数据
        $stmt = $pdo->prepare("SELECT cultivation_techniques, current_technique FROM characters WHERE id = ?");
        $stmt->execute([$characterId]);
        $characterData = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // 开始事务
        $pdo->beginTransaction();
        
        // 扣除灵气
        $stmt = $pdo->prepare("UPDATE user_resources SET qi_energy = qi_energy - 50 WHERE user_id = ?");
        $stmt->execute([$userId]);
        
        // 🔧 修改：更新characters表中的修为，而不是user_realm_progress表
        $stmt = $pdo->prepare("UPDATE characters SET cultivation_points = cultivation_points + ? WHERE id = ?");
        $stmt->execute([$stabilizedQi, $characterId]);
        
        // 🆕 添加功法经验增长系统
        $techniqueUpdated = false;
        $techniqueMessage = '';
        
        if ($characterData && $characterData['cultivation_techniques'] && $characterData['current_technique']) {
            try {
                $techniques = json_decode($characterData['cultivation_techniques'], true);
                $currentTechniqueId = $characterData['current_technique'];
                
                // 🔧 修改：只为当前使用的功法增加经验
                if (isset($techniques[$currentTechniqueId])) {
                    $currentTechnique = $techniques[$currentTechniqueId];
                    
                    // 🔧 修改：功法经验增长改为固定每次+1，但圆满等级不再增长经验
                    $currentLevel = isset($currentTechnique['level']) ? $currentTechnique['level'] : 1;
                    $maxLevel = 9; // 功法最大等级

                    // 🔧 修复：圆满等级(9级)不再增长经验
                    if ($currentLevel >= $maxLevel) {
                        $techniqueExpGain = 0; // 圆满等级不增长经验
                        $newTechniqueExp = isset($currentTechnique['exp']) ? $currentTechnique['exp'] : 0;
                    } else {
                        $techniqueExpGain = 1; // 固定每次修炼+1经验
                        $newTechniqueExp = (isset($currentTechnique['exp']) ? $currentTechnique['exp'] : 0) + $techniqueExpGain;
                    }
                    
                    // 检查是否可以升级
                    $requiredExp = $currentLevel * 100; // 每级需要的经验：等级×100
                    $levelsGained = 0;
                    
                    while ($newTechniqueExp >= $requiredExp && $currentLevel < $maxLevel) {
                        $newTechniqueExp -= $requiredExp;
                        $currentLevel++;
                        $levelsGained++;
                        $requiredExp = $currentLevel * 100; // 下一级需要的经验
                        
                        if ($currentLevel >= $maxLevel) {
                            $newTechniqueExp = min($newTechniqueExp, $requiredExp - 1); // 满级时经验不超过上限
                            break;
                        }
                    }
                    
                    // 🔧 只更新当前使用的功法数据
                    $techniques[$currentTechniqueId]['exp'] = $newTechniqueExp;
                    $techniques[$currentTechniqueId]['level'] = $currentLevel;
                    
                    // 保存更新后的功法数据
                    $updatedTechniquesJson = json_encode($techniques, JSON_UNESCAPED_UNICODE);
                    $stmt = $pdo->prepare("UPDATE characters SET cultivation_techniques = ? WHERE id = ?");
                    $stmt->execute([$updatedTechniquesJson, $characterId]);
                    
                    $techniqueUpdated = true;
                    
                    // 构建功法升级消息
                    if ($levelsGained > 0) {
                        $techniqueMessage = "，{$currentTechnique['name']} 升级到 {$currentLevel} 级！";
                    } else if ($techniqueExpGain > 0) {
                        $techniqueMessage = "，{$currentTechnique['name']} 获得 {$techniqueExpGain} 点经验";
                    }
                } else {
                    // 如果当前功法不存在，初始化基础功法
                    $techniques['ningqi'] = [
                        'id' => 'ningqi',
                        'name' => '凝气决', 
                        'level' => 1,
                        'exp' => 1, // 直接给1点经验
                        'type' => 'basic',
                        'unlocked' => true,
                        'efficiency_bonus' => 0
                    ];
                    
                    // 更新当前功法ID
                    $updatedTechniquesJson = json_encode($techniques, JSON_UNESCAPED_UNICODE);
                    $stmt = $pdo->prepare("UPDATE characters SET cultivation_techniques = ?, current_technique = 'ningqi' WHERE id = ?");
                    $stmt->execute([$updatedTechniquesJson, $characterId]);
                    
                    $techniqueUpdated = true;
                    $techniqueMessage = "，凝气决 获得 1 点经验";
                }
    } catch (Exception $e) {
                error_log("功法经验更新失败: " . $e->getMessage());
                // 功法更新失败不影响修为获得
            }
        } else {
            // 如果没有功法数据，初始化基础功法
            try {
                $defaultTechniques = [
                    'ningqi' => [
                        'id' => 'ningqi',
                        'name' => '凝气决',
                        'level' => 1,
                        'exp' => 1, // 直接给1点经验
                        'type' => 'basic',
                        'unlocked' => true,
                        'efficiency_bonus' => 0
                    ]
                ];
                
                $techniquesJson = json_encode($defaultTechniques, JSON_UNESCAPED_UNICODE);
                $stmt = $pdo->prepare("UPDATE characters SET cultivation_techniques = ?, current_technique = 'ningqi' WHERE id = ?");
                $stmt->execute([$techniquesJson, $characterId]);
                
                $techniqueUpdated = true;
                $techniqueMessage = "，凝气决 获得 1 点经验";
            } catch (Exception $e) {
                error_log("初始化功法数据失败: " . $e->getMessage());
            }
        }
        
        $pdo->commit();
        
        // 获取更新后的境界信息
        $realmInfo = getUserRealmInfo($pdo, $userId);
        
        // 构建返回消息
        $message = "周天运行成功！稳固修为 {$stabilizedQi} 点" . $techniqueMessage;
            
            echo json_encode([
                'success' => true,
            'message' => $message,
            'qi_gained' => $stabilizedQi,
            'technique_updated' => $techniqueUpdated
        ]);
        
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
        $pdo->rollBack();
        }
        echo json_encode(['success' => false, 'message' => '周天运行失败: ' . $e->getMessage()]);
    }
}

// 尝试突破境界（重新设计）
function attemptBreakthrough($pdo, $userId) {
    try {
        // 🆕 确保last_breakthrough_time字段存在
        try {
            $stmt = $pdo->query("SHOW COLUMNS FROM characters LIKE 'last_breakthrough_time'");
            if ($stmt->rowCount() == 0) {
                // 字段不存在，添加字段
                $pdo->exec("ALTER TABLE characters ADD COLUMN last_breakthrough_time TIMESTAMP NULL DEFAULT NULL COMMENT '最后突破时间'");
                error_log("已添加last_breakthrough_time字段到characters表");
            }
        } catch (Exception $e) {
            error_log("检查/添加last_breakthrough_time字段失败: " . $e->getMessage());
        }
        
        // 🆕 检查突破间隔（3秒冷却时间）
        $stmt = $pdo->prepare("SELECT last_breakthrough_time FROM characters WHERE user_id = ? LIMIT 1");
        $stmt->execute([$userId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($character && isset($character['last_breakthrough_time']) && $character['last_breakthrough_time']) {
            $lastBreakthroughTime = strtotime($character['last_breakthrough_time']);
            $currentTime = time();
            $timeDiff = $currentTime - $lastBreakthroughTime;
            
            if ($timeDiff < 3) { // 3秒冷却时间
                $remainingTime = 3 - $timeDiff;
                throw new Exception("突破冷却中，请等待 {$remainingTime} 秒后再试");
            }
        }
        
        // 🆕 更新最后突破时间
        $stmt = $pdo->prepare("UPDATE characters SET last_breakthrough_time = NOW() WHERE user_id = ?");
        $stmt->execute([$userId]);
        
        // 开始事务
        $pdo->beginTransaction();
        
        // 获取用户当前境界信息
        $realmInfo = getUserRealmInfo($pdo, $userId);
        if (!$realmInfo || !isset($realmInfo['current_realm'])) {
            throw new Exception("无法获取境界信息");
        }
        
        $currentRealm = $realmInfo['current_realm'];
        $currentQi = isset($currentRealm['current_qi']) ? $currentRealm['current_qi'] : 0;
        $expRequired = isset($currentRealm['exp_required']) ? $currentRealm['exp_required'] : 1000;
        $currentRealmId = isset($currentRealm['realm_id']) ? $currentRealm['realm_id'] : 1;
        $breakthroughAttempts = isset($currentRealm['breakthrough_attempts']) ? $currentRealm['breakthrough_attempts'] : 0;
        
        // 检查是否满足突破条件
        if ($currentQi < $expRequired) {
            throw new Exception("修为不足，需要 {$expRequired} 点修为才能突破");
        }
        
        // 获取角色ID
        $stmt = $pdo->prepare("SELECT id FROM characters WHERE user_id = ? LIMIT 1");
        $stmt->execute([$userId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);
        if (!$character) {
            throw new Exception("角色不存在");
        }
        $characterId = $character['id'];
        
        // 🔧 修改：基于境界等级的成功率计算
        $realmLevel = getCurrentRealmLevel($pdo, $currentRealmId);
        $baseSuccessRate = calculateBreakthroughRateByRealm($realmLevel);
        $attemptBonus = $breakthroughAttempts * 10; // 每次失败+10%
        $totalSuccessRate = min($baseSuccessRate + $attemptBonus, 95); // 最高95%
        
        // 🔧 修复：从请求中获取累计的渡劫丹成功率加成
        $input = json_decode(file_get_contents('php://input'), true);
        $tribulationBonus = isset($input['tribulation_success_rate_bonus']) ? (int)$input['tribulation_success_rate_bonus'] : 0;
        
        // 🔧 修复：应用渡劫丹加成，最高可以到100%
        $finalSuccessRate = min($totalSuccessRate + $tribulationBonus, 100);
        
        // 🔧 修复：添加详细的调试日志
        error_log("🎯 [渡劫判定] 用户ID: {$userId}, 境界等级: {$realmLevel}, 基础成功率: {$baseSuccessRate}%, 失败次数: {$breakthroughAttempts}, 失败加成: {$attemptBonus}%, 渡劫丹加成: {$tribulationBonus}%, 最终成功率: {$finalSuccessRate}%");
        
        // 🔧 修复：100%成功率时直接成功，无需随机判定
        if ($finalSuccessRate >= 100) {
            $isSuccess = true;
            error_log("🎯 [渡劫判定] 成功率100%，直接成功！");
        } else {
            // 进行突破判定
            $random = rand(1, 100);
            $isSuccess = $random <= $finalSuccessRate;
            // 🔧 修复：记录判定结果
            error_log("🎲 [渡劫判定] 随机数: {$random}, 成功率: {$finalSuccessRate}%, 判定结果: " . ($isSuccess ? '成功' : '失败'));
        }
        
        if ($isSuccess) {
            // 突破成功 - 进入下一个境界
            $nextRealmId = $currentRealmId + 1;
            
            // 检查下一个境界是否存在
            $stmt = $pdo->prepare("SELECT * FROM realm_levels WHERE id = ?");
            $stmt->execute([$nextRealmId]);
            $nextRealm = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($nextRealm) {
                // 🔧 修改：突破成功时只扣除突破所需的修为，不归零
                $remainingQi = $currentQi - $expRequired;
                
                // 🆕 境界突破属性增加逻辑
                // 每次突破增加基础属性：筋骨、悟性、体魄、神魂、身法各+2
                $attributeIncrease = 2;
                
                // 确保characters表有基础属性字段
                try {
                    // 检查属性字段是否存在，如果不存在则添加
                    $attributeFields = ['physique', 'comprehension', 'constitution', 'spirit', 'agility'];
                    foreach ($attributeFields as $field) {
                        $stmt = $pdo->query("SHOW COLUMNS FROM characters LIKE '{$field}'");
                        if ($stmt->rowCount() == 0) {
                            $pdo->exec("ALTER TABLE characters ADD COLUMN {$field} INT DEFAULT 10 COMMENT '基础属性'");
                            error_log("已添加{$field}字段到characters表");
                        }
                    }
                } catch (Exception $e) {
                    error_log("检查/添加属性字段失败: " . $e->getMessage());
                }
                
                // 更新境界和修为，同时增加基础属性
                $stmt = $pdo->prepare("
                    UPDATE characters SET 
                        realm_id = ?, 
                        cultivation_points = ?,
                        physique = physique + ?,
                        comprehension = comprehension + ?,
                        constitution = constitution + ?,
                        spirit = spirit + ?,
                        agility = agility + ?
                    WHERE id = ?
                ");
                $stmt->execute([
                    $nextRealmId, 
                    $remainingQi, 
                    $attributeIncrease, 
                    $attributeIncrease, 
                    $attributeIncrease, 
                    $attributeIncrease, 
                    $attributeIncrease, 
                    $characterId
                ]);
                
                // 记录成功的渡劫日志
                $stmt = $pdo->prepare("
                    INSERT INTO character_growth_logs 
                    (character_id, log_type, change_type, breakthrough, breakthrough_level, description, created_at) 
                    VALUES (?, 'cultivation', 'tribulation', TRUE, ?, ?, NOW())
                ");
                $successDesc = "渡劫成功！从{$currentRealm['realm_name']}突破到{$nextRealm['realm_name']}（境界ID:{$currentRealmId}→{$nextRealmId}），消耗{$expRequired}点修为，剩余{$remainingQi}点修为，全属性+{$attributeIncrease}";
                if ($tribulationBonus > 0) {
                    $successDesc .= "，使用渡劫丹累计加成+{$tribulationBonus}%";
                }
                $stmt->execute([$characterId, $nextRealmId, $successDesc]);
                
                $message = "突破成功！进入 {$nextRealm['realm_name']}，消耗 {$expRequired} 点修为，剩余 {$remainingQi} 点修为，全属性+{$attributeIncrease}";
                if ($tribulationBonus > 0) {
                    $message .= "（渡劫丹累计加成：+{$tribulationBonus}%）";
                }
            } else {
                $message = "突破成功！但已达到最高境界";
            }
            
            $pdo->commit();
            echo json_encode(['success' => true, 'message' => $message, 'breakthrough_success' => true]);
            
        } else {
            // 突破失败 - 🆕 添加魂力受损机制
            $lostQi = intval($currentQi * 0.3);
            $newCultivationPoints = $currentQi - $lostQi;
            
            // 🆕 魂力受损设计
            // 计算魂力修复时间：基础600秒（10分钟），每个大境界增加1分钟
            $baseRecoveryTime = 600; // 基础10分钟
            $realmLevel = getCurrentRealmLevel($pdo, $currentRealmId);
            $majorRealmLevel = ceil($realmLevel / 10); // 大境界等级
            $realmBonus = ($majorRealmLevel - 1) * 60; // 每个大境界增加1分钟
            $totalRecoveryTime = $baseRecoveryTime + $realmBonus;
            
            // 魂力修复公式：每30秒恢复1点，总共需要100点
            $recoveryPerSecond = 100 / $totalRecoveryTime; // 每秒恢复的魂力点数
            
            // 记录魂力受损时间
            $soulDamageTime = time();
            
            $stmt = $pdo->prepare("UPDATE characters SET cultivation_points = ?, soul_damage_time = ?, soul_recovery_per_second = ?, current_soul_power = 0 WHERE id = ?");
            $stmt->execute([$newCultivationPoints, $soulDamageTime, $recoveryPerSecond, $characterId]);
            
            // 记录失败的渡劫日志到character_growth_logs表
            $stmt = $pdo->prepare("
                INSERT INTO character_growth_logs 
                (character_id, log_type, change_type, breakthrough, change_amount, description, created_at) 
                VALUES (?, 'cultivation', 'tribulation', FALSE, ?, ?, NOW())
            ");
            $failDesc = "渡劫失败！在{$currentRealm['realm_name']}失败，损失{$lostQi}点修为（境界ID:{$currentRealmId}），魂力受损需要".intval($totalRecoveryTime/60)."分钟恢复";
            if ($tribulationBonus > 0) {
                $failDesc .= "，渡劫丹累计加成+{$tribulationBonus}%已消耗";
            }
            $stmt->execute([$characterId, -$lostQi, $failDesc]);
            
            $message = "突破失败！损失 {$lostQi} 点修为，魂力受损需要 ".intval($totalRecoveryTime/60)." 分钟恢复，下次成功率+10%";
            if ($tribulationBonus > 0) {
                $message .= "（渡劫丹累计加成：+{$tribulationBonus}%已消耗）";
            }
            
            $pdo->commit();
            echo json_encode([
                'success' => true,
                'message' => $message,
                'breakthrough_success' => false,
                'lost_qi' => $lostQi,
                'soul_damaged' => true, // 🆕 魂力受损标识
                'soul_recovery_time' => $totalRecoveryTime, // 🆕 总恢复时间（秒）
                'soul_damage_timestamp' => $soulDamageTime // 🆕 受损时间戳
            ]);
        }
        
    } catch (Exception $e) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => '突破失败: ' . $e->getMessage()]);
    }
}

// 获取用户背包中适用的丹药
function getAvailablePills($pdo, $userId, $realmId) {
    try {
        // 获取角色ID
        $stmt = $pdo->prepare("SELECT id FROM characters WHERE user_id = ? LIMIT 1");
        $stmt->execute([$userId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$character) {
            return [];
        }
        
        $characterId = $character['id'];
        
        // 获取当前境界信息
        $stmt = $pdo->prepare("SELECT realm_name FROM realm_levels WHERE id = ?");
        $stmt->execute([$realmId]);
        $realmInfo = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$realmInfo) {
            return [];
        }
        
        $currentRealmName = $realmInfo['realm_name'];
        
        // 🔧 修改：只能使用当前大境界的丹药
        // 提取大境界名称（如"心动期一阶" -> "心动期"）
        $majorRealm = '';
        $realmPatterns = [
            '开光期' => '开光期',
            '灵虚期' => '灵虚期',
            '辟谷期' => '辟谷期',
            '心动期' => '心动期',
            '元化期' => '元化期',
            '元婴期' => '元婴期',
            '离合期' => '离合期',
            '空冥期' => '空冥期',
            '寂灭期' => '寂灭期',
            '大乘期' => '大乘期',
            '渡劫期' => '渡劫期',
            '凡仙期' => '凡仙期',
            '地仙期' => '地仙期',
            '天仙期' => '天仙期',
            '真仙期' => '真仙期',
            '太乙真仙期' => '太乙真仙期',
            '太乙金仙期' => '太乙金仙期',
            '太乙玄仙期' => '太乙玄仙期',
            '大罗真仙期' => '大罗真仙期',
            '大罗金仙期' => '大罗金仙期',
            '大罗玄仙期' => '大罗玄仙期',
            '准圣期' => '准圣期',
            '教主期' => '教主期',
            '混元期' => '混元期',
            '混元金仙期' => '混元金仙期',
            '混元至仙期' => '混元至仙期',
            '天道期' => '天道期',
            '鸿蒙至元期' => '鸿蒙至元期'
        ];
        
        foreach ($realmPatterns as $pattern => $targetRealm) {
            if (strpos($currentRealmName, $pattern) !== false) {
                $majorRealm = $targetRealm;
                break;
            }
        }
        
        if (empty($majorRealm)) {
            return [];
        }
        
        // 查询背包中适合当前境界的渡劫丹
        $stmt = $pdo->prepare("
            SELECT ui.item_id, ui.quantity, gi.item_name, gi.item_code, gi.special_effects
            FROM user_inventories ui
            JOIN game_items gi ON ui.item_id = gi.id
            WHERE ui.character_id = ? 
            AND ui.quantity > 0 
            AND gi.item_name LIKE '%渡劫丹%'
            AND gi.item_name NOT LIKE '%丹方%'
            AND gi.special_effects IS NOT NULL
            ORDER BY gi.item_name
        ");
        $stmt->execute([$characterId]);
        $inventoryPills = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $availablePills = [];
        
        foreach ($inventoryPills as $pill) {
            // 解析special_effects JSON
            $effects = json_decode($pill['special_effects'], true);
            if (!$effects || !isset($effects['target_realm'])) {
                continue;
            }
            
            // 检查目标境界是否匹配
            if ($effects['target_realm'] === $majorRealm) {
                $successRateBonus = isset($effects['bonus_rate']) ? $effects['bonus_rate'] : 5;
                $effectDescription = "渡劫成功率 +{$successRateBonus}%";
                
                $availablePills[] = [
                    'item_id' => $pill['item_id'],
                    'item_name' => $pill['item_name'],
                    'quantity' => $pill['quantity'],
                    'success_rate_bonus' => $successRateBonus,
                    'effect_description' => $effectDescription
                ];
            }
        }
        
        return $availablePills;
        
    } catch (Exception $e) {
        error_log("获取可用渡劫丹失败: " . $e->getMessage());
        return [];
    }
}

// 获取丹药的成功率加成
function getPillBonus($pillName) {
    if (strpos($pillName, '渡劫丹') !== false) {
        // 🔧 修复：从丹药名称中提取品质
        $qualityBonus = 0;
        
        // 品质等级对应的加成
        $qualityMap = [
            '下品' => 5,    // 下品：+5%
            '中品' => 10,   // 中品：+10%
            '上品' => 15,   // 上品：+15%
            '极品' => 20    // 极品：+20%
        ];
        
        // 检查品质
        foreach ($qualityMap as $quality => $bonus) {
            if (strpos($pillName, $quality) !== false) {
                $qualityBonus = $bonus;
                break;
            }
        }
        
        // 🔧 兼容旧的品级系统（一品、二品等）
        if ($qualityBonus == 0 && preg_match('/([一二三四五六七八九])品/', $pillName, $matches)) {
            $grade = convertChineseToNumber($matches[1]);
            $qualityBonus = $grade * 5; // 每品级+5%成功率（更平衡）
        }
        
        // 默认最低5%加成
        return max($qualityBonus, 5);
    }
    return 0;
}

// 将中文数字转换为阿拉伯数字
function convertChineseToNumber($chinese) {
    $map = [
        '一' => 1, '二' => 2, '三' => 3, '四' => 4, '五' => 5,
        '六' => 6, '七' => 7, '八' => 8, '九' => 9
    ];
    return isset($map[$chinese]) ? $map[$chinese] : 1;
}

// 获取资源信息
function getResources($pdo, $userId) {
    try {
        $resources = getUserResources($pdo, $userId);
        
        echo json_encode([
            'success' => true,
            'resources' => $resources
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => '获取资源信息失败: ' . $e->getMessage()]);
    }
}

// 获取属性信息
function getAttributes($pdo, $userId) {
    try {
        // 获取用户基础信息
        $stmt = $pdo->prepare("SELECT id, username, email, nickname, status, spirit_stones, gold FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $userInfo = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$userInfo) {
            throw new Exception('用户不存在');
        }
        
        // 获取角色信息
        $stmt = $pdo->prepare("
            SELECT
                c.id as character_id,
                c.character_name,
                c.realm_id,
                c.avatar_image,
                c.avatar_frame,
                c.physique,
                c.comprehension,
                c.constitution,
                c.spirit,
                c.agility,
                c.current_hp,
                c.current_mp,
                c.cultivation_techniques,
                c.current_technique,
                c.attribute_pill_count,
                r.realm_name,
                r.realm_level as level,
                r.hp_multiplier,
                r.mp_multiplier,
                r.attack_multiplier,
                r.defense_multiplier,
                r.speed_multiplier
            FROM characters c
            LEFT JOIN realm_levels r ON c.realm_id = r.id
            WHERE c.user_id = ?
            LIMIT 1
        ");
        $stmt->execute([$userId]);
        $characterInfo = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // 如果没有角色信息，使用默认值
        if (!$characterInfo) {
            $characterInfo = [
                'character_id' => null,
                'character_name' => '未创建角色',
                'realm_id' => 1,
                'avatar_image' => 'assets/images/avatars/default_male.png',
                'physique' => 10,
                'comprehension' => 10,
                'constitution' => 10,
                'spirit' => 10,
                'agility' => 10,
                'current_hp' => 100,
                'current_mp' => 100,
                'realm_name' => '练气期',
                'level' => 1,
                'hp_multiplier' => 1.0,
                'mp_multiplier' => 1.0,
                'attack_multiplier' => 1.0,
                'defense_multiplier' => 1.0,
                'speed_multiplier' => 1.0
            ];
            
            // 直接返回默认数据
            $combinedData = array_merge($userInfo, $characterInfo, [
                'physical_attack_total' => 10,
                'immortal_attack_total' => 10,
                'physical_defense_total' => 5,
                'immortal_defense_total' => 5,
                'hp_total' => 100,
                'mp_total' => 50,
                'speed_total' => 50,
                'accuracy_bonus_total' => 85.0,
                'dodge_bonus_total' => 5.0,
                'critical_bonus_total' => 5.0,
                'critical_resistance_total' => 0.0,
                'critical_damage_total' => 0.0,
                // 保持旧字段兼容
                'physical_attack' => 10,
                'immortal_attack' => 10,
                'physical_defense' => 5,
                'immortal_defense' => 5,
                'hp' => 100,
                'mp' => 50,
                'speed' => 50,
                'accuracy_bonus' => 85.0,
                'dodge_bonus' => 5.0,
                'critical_bonus' => 5.0,
                'critical_resistance' => 0.0
            ]);
            
            echo json_encode([
                'success' => true,
                'attributes' => $combinedData
            ]);
            return;
        }
        
        // 获取灵根属性
        $rootAttributes = getRootAttributes($pdo, $characterInfo['character_id']);
        
        // 获取装备加成数据（统一获取所有装备包括武器）
        $equipmentBonus = EquipmentStatsManager::getAllEquipmentStats($pdo, $characterInfo['character_id']);

        // 🔧 移除功法属性加成 - 功法不应该提供战斗属性加成
        $cultivationBonus = [];

        // 获取套装属性加成
        $setBonus = getCharacterSetBonus($pdo, $characterInfo['character_id']);

        // 计算最终属性值 - 使用统一的装备加成和套装加成
        $calculatedAttributes = calculateCharacterAttributes($characterInfo, $equipmentBonus, $cultivationBonus, [], $setBonus);
        
        // 🔧 修复：移除重复的属性计算，直接使用calculateCharacterAttributes的结果
        // calculateCharacterAttributes函数已经包含了完整的基础属性、装备加成和明细分解
        $attributeBreakdown = [];
        
        // 合并所有信息，包括属性分解数据
        $combinedData = array_merge($userInfo, $characterInfo, $calculatedAttributes, $rootAttributes, $attributeBreakdown);
        
        // 🔧 确保向后兼容 - 添加旧字段名映射到新字段
        $compatibilityData = [
            // 旧字段映射到新的total字段
            'physical_attack' => isset($calculatedAttributes['physical_attack_total']) ? $calculatedAttributes['physical_attack_total'] : 0,
            'immortal_attack' => isset($calculatedAttributes['immortal_attack_total']) ? $calculatedAttributes['immortal_attack_total'] : 0,
            'physical_defense' => isset($calculatedAttributes['physical_defense_total']) ? $calculatedAttributes['physical_defense_total'] : 0,
            'immortal_defense' => isset($calculatedAttributes['immortal_defense_total']) ? $calculatedAttributes['immortal_defense_total'] : 0,
            'hp' => isset($calculatedAttributes['hp_total']) ? $calculatedAttributes['hp_total'] : 0,
            'mp' => isset($calculatedAttributes['mp_total']) ? $calculatedAttributes['mp_total'] : 0,
            'speed' => isset($calculatedAttributes['speed_total']) ? $calculatedAttributes['speed_total'] : 0,
            'accuracy_bonus' => isset($calculatedAttributes['accuracy_bonus_total']) ? $calculatedAttributes['accuracy_bonus_total'] : 0,
            'dodge_bonus' => isset($calculatedAttributes['dodge_bonus_total']) ? $calculatedAttributes['dodge_bonus_total'] : 0,
            'critical_bonus' => isset($calculatedAttributes['critical_bonus_total']) ? $calculatedAttributes['critical_bonus_total'] : 0,
            'critical_damage' => isset($calculatedAttributes['critical_damage_total']) ? $calculatedAttributes['critical_damage_total'] : 0,
            'critical_resistance' => isset($calculatedAttributes['critical_resistance_total']) ? $calculatedAttributes['critical_resistance_total'] : 0,
        ];
        
        $finalData = array_merge($combinedData, $compatibilityData);
        
        echo json_encode([
            'success' => true,
            'attributes' => $finalData
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => '获取属性信息失败: ' . $e->getMessage()]);
    }
}

// 🆕 统一的用户数据获取函数 - 完整版本
function getUserData($pdo, $userId) {
    try {
        // 获取用户基础信息
        $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$user) {
            throw new Exception('用户不存在');
        }

        // 获取角色信息 - 使用与equipment_integrated.php相同的查询
        $stmt = $pdo->prepare("
            SELECT c.*, r.realm_name, r.realm_level
            FROM characters c
            LEFT JOIN realm_levels r ON c.realm_id = r.id
            WHERE c.user_id = ?
        ");
        $stmt->execute([$userId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$character) {
            // 没有角色时返回完整的默认用户信息
            echo json_encode([
                'success' => true,
                'user_id' => $userId,
                'username' => $user['username'],
                'character_name' => $user['username'],
                'character_avatar' => 'ck.png',
                'avatar_image' => 'ck.png',
                'avatar_frame' => 'base1.png',
                'attributes' => [
                    'level' => 1,
                    'gold' => 0,
                    'spirit_stones' => 100,
                    'silver' => 0,
                    'spiritual_power' => 0,
                    'current_realm' => '开光期一阶',
                    'realm_level' => 1,
                    'realm_id' => 1,
                    'hp' => 100,
                    'mp' => 50,
                    'attack' => 10,
                    'defense' => 5,
                    'speed' => 8,
                    'critical_rate' => 5,
                    'critical_damage' => 150,
                    'accuracy' => 90,
                    'dodge' => 10,
                    // 角色基础属性
                    'physique' => 10,
                    'constitution' => 10,
                    'comprehension' => 10,
                    'spirit' => 10,
                    'agility' => 10,
                    'inventory_slots' => 30
                ]
            ]);
            return;
        }

        // 获取装备加成 - 使用与equipment_integrated.php相同的逻辑
        require_once __DIR__ . '/../includes/equipment_stats_manager.php';
        $allEquipmentStats = EquipmentStatsManager::getAllEquipmentStats($pdo, $character['id']);

        // 获取套装加成
        $setBonus = getCharacterSetBonus($pdo, $character['id']);

        // 计算最终属性 - 使用与equipment_integrated.php相同的计算逻辑
        $finalStats = calculateCharacterAttributes($character, $allEquipmentStats, [], [], $setBonus);

        // 🔧 数据完整性：确保所有字段都有值，与equipment_integrated.php保持一致
        $stringFields = ['character_name', 'character_avatar', 'avatar_image', 'avatar_frame', 'realm_name', 'username'];

        foreach ($finalStats as $key => $value) {
            if (in_array($key, $stringFields)) {
                if ($value === null || $value === '') {
                    if (in_array($key, ['character_avatar', 'avatar_image'])) {
                        $finalStats[$key] = 'ck.png';
                    } elseif ($key === 'avatar_frame') {
                        $finalStats[$key] = 'base1.png';
                    } else {
                        $finalStats[$key] = '';
                    }
                }
            } elseif (is_numeric($value)) {
                $finalStats[$key] = floatval($value);
            } elseif ($value === null) {
                $finalStats[$key] = 0;
            }
        }

        // 构建完整的返回数据 - 与equipment_integrated.php格式兼容
        $userData = [
            'success' => true,
            'user_id' => $userId,
            'username' => $user['username'],
            'character_name' => $character['character_name'] ?: $user['username'],
            'character_avatar' => $character['avatar_image'] ?: 'ck.png', // 🔧 修复：使用正确的字段名
            'avatar_image' => $character['avatar_image'] ?: 'ck.png',
            'avatar_frame' => $character['avatar_frame'] ?: 'base1.png',

            // 🔧 完整属性数据 - 包含所有equipment_integrated.php的字段
            'attributes' => [
                // 基础信息
                'level' => $character['level'] ?: 1,
                'gold' => $user['gold'] ?: 0,  // 🔧 修复：从users表获取金币
                'spirit_stones' => $user['spirit_stones'] ?: 100,  // 🔧 修复：从users表获取灵石
                'silver' => $user['silver'] ?: 0,
                'spiritual_power' => $user['spiritual_power'] ?: 0,

                // 境界信息
                'current_realm' => $character['realm_name'] ?: '开光期一阶',
                'realm_level' => $character['realm_level'] ?: 1,
                'realm_id' => $character['realm_id'] ?: 1,

                // 角色基础属性
                'physique' => $character['physique'] ?: 10,
                'constitution' => $character['constitution'] ?: 10,
                'comprehension' => $character['comprehension'] ?: 10,
                'spirit' => $character['spirit'] ?: 10,
                'agility' => $character['agility'] ?: 10,

                // 战斗属性 (计算后的总值)
                'hp' => $finalStats['hp_total'] ?: 100,
                'mp' => $finalStats['mp_total'] ?: 50,
                'attack' => $finalStats['attack_total'] ?: 10,
                'defense' => $finalStats['defense_total'] ?: 5,
                'speed' => $finalStats['speed_total'] ?: 8,
                'critical_rate' => $finalStats['critical_rate_total'] ?: 5,
                'critical_damage' => $finalStats['critical_damage_total'] ?: 150,
                'accuracy' => $finalStats['accuracy_total'] ?: 90,
                'dodge' => $finalStats['dodge_total'] ?: 10,

                // 系统信息
                'inventory_slots' => $character['inventory_slots'] ?: 30
            ],

            // 🔧 兼容性：提供与equipment_integrated.php相同的额外数据
            'user' => $finalStats, // 扁平结构的完整数据
            'equipment_stats' => $allEquipmentStats, // 装备属性详情
            'base_stats' => [] // 基础属性 (可根据需要填充)
        ];

        echo json_encode($userData);

    } catch (Exception $e) {
        error_log("获取用户数据失败: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => '获取用户数据失败: ' . $e->getMessage()]);
    }
}

// 获取装备加成
// 🔧 重构：使用统一装备管理器
require_once __DIR__ . '/../includes/equipment_stats_manager.php';

function getEquipmentBonus($pdo, $characterId) {
    return EquipmentStatsManager::getEquipmentBonusCompatible($pdo, $characterId);
}

// 辅助函数

// 计算小境界所需灵气
function calculateSubLevelQi($baseQi, $subLevel) {
    return intval($baseQi * $subLevel * 0.1);
}

// 计算突破成功率
function calculateBreakthroughRate($attempts) {
    $baseRate = 50; // 基础成功率50%
    $bonusRate = $attempts * 10; // 每次失败+10%
    return min($baseRate + $bonusRate, 95); // 最高95%
}

// 🆕 获取当前境界等级
function getCurrentRealmLevel($pdo, $realmId) {
    try {
        $stmt = $pdo->prepare("SELECT realm_level FROM realm_levels WHERE id = ?");
        $stmt->execute([$realmId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ? intval($result['realm_level']) : 1;
    } catch (Exception $e) {
        error_log("获取境界等级失败: " . $e->getMessage());
        return 1;
    }
}

// 🆕 根据境界等级计算突破成功率
function calculateBreakthroughRateByRealm($realmLevel) {
    // 🔧 修复：前20级必定成功，辟谷期开始使用真实渡劫几率
    if ($realmLevel <= 20) {
        // 开光期（1-10级）和灵虚期（11-20级）：100%必定成功
        return 100;
    } else if ($realmLevel <= 40) {
        // 辟谷期到心动期（21-40级）：85% → 60%，每级减少约1.25%
        $baseRate = 85 - (($realmLevel - 20) * (25.0 / 20.0));
        return round($baseRate);
    } else {
        // 心动期之后（41级以上）：固定60%
        return 60;
    }
}

// 获取当前境界的渡劫失败次数
function getBreakthroughAttempts($pdo, $userId, $realmId) {
    try {
        // 获取角色ID
        $stmt = $pdo->prepare("SELECT id FROM characters WHERE user_id = ? LIMIT 1");
        $stmt->execute([$userId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);
        if (!$character) {
            return 0;
        }
        $characterId = $character['id'];
        
        // 查询character_growth_logs表中的渡劫失败记录
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as attempt_count 
            FROM character_growth_logs 
            WHERE character_id = ? 
            AND log_type = 'cultivation' 
            AND change_type = 'tribulation' 
            AND breakthrough = FALSE
            AND description LIKE ?
        ");
        $stmt->execute([$characterId, "%境界ID:{$realmId}%"]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return isset($result['attempt_count']) ? intval($result['attempt_count']) : 0;
        
    } catch (Exception $e) {
        error_log("获取渡劫失败次数错误: " . $e->getMessage());
        return 0;
    }
}

// 更新境界属性加成
function updateAttributesForRealm($pdo, $userId, $realmId, $subLevel) {
    try {
        // 根据境界等级增加属性
        $stmt = $pdo->prepare("SELECT level FROM realms WHERE id = ?");
        $stmt->execute([$realmId]);
        $realm = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($realm) {
            $totalLevel = ($realm['level'] - 1) * 10 + $subLevel;
            $attributeBonus = $totalLevel * 2; // 每级增加2点属性
            
            $stmt = $pdo->prepare("
                UPDATE user_attributes SET 
                    hp = hp + ?,
                    mp = mp + ?,
                    physical_attack = physical_attack + 1,
                    immortal_attack = immortal_attack + 1,
                    physical_defense = physical_defense + 1,
                    immortal_defense = immortal_defense + 1
                WHERE user_id = ?
            ");
            $stmt->execute([$attributeBonus, intval($attributeBonus/2), $userId]);
        }
    } catch (Exception $e) {
        // 记录错误但不抛出异常
        error_log("更新境界属性失败: " . $e->getMessage());
    }
}

// 自动修炼 - 每30秒增长修为
function autoCultivate($pdo, $userId) {
    try {
        // 首先获取用户的角色ID
        $stmt = $pdo->prepare("SELECT id FROM characters WHERE user_id = ? LIMIT 1");
        $stmt->execute([$userId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$character) {
            throw new Exception("角色不存在");
        }
        
        $characterId = $character['id'];
        
        // 🔧 修改：获取角色最高通关关卡数（使用current_stage）
        $maxCurrentStage = 1; // 默认关卡数
        
        try {
            // 检查user_map_progress表是否存在
            $stmt = $pdo->query("SHOW TABLES LIKE 'user_map_progress'");
            if ($stmt->rowCount() > 0) {
                // 🔧 修改：使用character_id查询所有地图的最高current_stage
                $stmt = $pdo->prepare("SELECT MAX(current_stage) as max_current_stage FROM user_map_progress WHERE character_id = ?");
                $stmt->execute([$characterId]);
                $stageResult = $stmt->fetch(PDO::FETCH_ASSOC);
                $maxCurrentStage = $stageResult && $stageResult['max_current_stage'] ? intval($stageResult['max_current_stage']) : 1;
                
                // 📊 调试日志
                error_log("地图进度计算 - 角色ID: {$characterId}, 最高通关关卡: {$maxCurrentStage}");
            }
        } catch (Exception $mapError) {
            // 如果地图查询失败，使用默认值
            error_log("地图进度查询失败: " . $mapError->getMessage());
            $maxCurrentStage = 1;
        }
        
        // 🔧 获取角色数据用于功法分析
        $stmt = $pdo->prepare("SELECT cultivation_techniques, current_technique FROM characters WHERE id = ?");
        $stmt->execute([$characterId]);
        $characterData = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // 🔧 注释：地图进度不再影响修为计算（仅依赖功法）
        
        // 🔧 修改：计算所有已学会功法的基础修为之和
        $techniqueBaseQi = 10; // 默认功法基础修为（至少有一个基础功法）
        $efficiencyBonus = 0; // 默认效率加成
        
        // 获取所有功法的基础修为和效率加成
        if ($characterData && $characterData['cultivation_techniques']) {
            try {
                $techniques = json_decode($characterData['cultivation_techniques'], true);
                
                if ($techniques && is_array($techniques)) {
                    error_log("在线修炼 - 计算所有功法基础修为: " . json_encode($techniques));
                    
                    // 计算所有已学会功法的基础修为之和
                    $totalTechniqueBaseQi = 0;
                    $totalEfficiencyBonus = 0;
                    
                    foreach ($techniques as $techniqueId => $technique) {
                        if ($technique && isset($technique['name']) && isset($technique['level'])) {
                            $techniqueName = $technique['name'];
                            $techniqueLevel = $technique['level'];
                            
                            // 计算单个功法的基础修为和效率加成
                            $singleTechniqueBaseQi = getTechniqueBaseQi($techniqueName, $techniqueLevel);
                            $singleEfficiencyBonus = getTechniqueEfficiencyBonus($techniqueName, $techniqueLevel);
                            
                            $totalTechniqueBaseQi += $singleTechniqueBaseQi;
                            $totalEfficiencyBonus += $singleEfficiencyBonus;
                            
                            error_log("在线修炼 - {$techniqueName} (等级{$techniqueLevel}): 基础修为 {$singleTechniqueBaseQi}点, 效率 +{$singleEfficiencyBonus}%");
                        }
                    }
                    
                    // 如果有功法数据，使用计算出的总和；否则使用默认值
                    if ($totalTechniqueBaseQi > 0) {
                        $techniqueBaseQi = $totalTechniqueBaseQi;
                    }
                    if ($totalEfficiencyBonus > 0) {
                        $efficiencyBonus = $totalEfficiencyBonus;
                    }
                    
                    error_log("在线修炼 - 所有功法基础修为总和: {$techniqueBaseQi}, 所有功法效率加成总和: {$efficiencyBonus}%");
                }
            } catch (Exception $e) {
                error_log("在线修炼 - 解析功法数据失败: " . $e->getMessage());
                // 使用默认值
                $techniqueBaseQi = getTechniqueBaseQi('凝气决', 1); // 默认凝气决1级
            }
        } else {
            // 没有功法数据时使用默认功法
            $techniqueBaseQi = getTechniqueBaseQi('凝气决', 1); // 默认凝气决1级
        }
        
        // 🔧 修改：基础修为 = 功法基础修为总和 + 地图进度修为
        $mapProgressQi = max(0, $maxCurrentStage - 1); // 地图进度修为：每通关1关+1点基础修为（第1关不计算）
        $baseQiGain = $techniqueBaseQi + $mapProgressQi;
        
        // 🆕 计算境界效率加成
        $realmBonus = 0;
        try {
            // 获取角色境界信息
            $stmt = $pdo->prepare("SELECT realm_id FROM characters WHERE id = ?");
            $stmt->execute([$characterId]);
            $charRealm = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($charRealm && $charRealm['realm_id']) {
                $realmLevel = getCurrentRealmLevel($pdo, $charRealm['realm_id']);
                // 境界每1级+1%效率
                $realmBonus = $realmLevel * 1; // 每级+1%
            }
        } catch (Exception $e) {
            error_log("计算境界效率加成失败: " . $e->getMessage());
            $realmBonus = 0;
        }
        
        // 🆕 计算地图效率加成
        $mapBonus = 0;
        if ($maxCurrentStage > 1) {
            // 每多1关+0.5%效率（第1关不计算，从第2关开始）
            $mapBonus = ($maxCurrentStage - 1) * 0.5;
        }
        
        // 🔧 计算总效率加成：功法 + 境界 + 地图
        $totalEfficiencyBonus = $efficiencyBonus + $realmBonus + $mapBonus;
        
        // 应用总效率加成
        $qiGained = intval($baseQiGain * (1 + $totalEfficiencyBonus / 100));
        
        // 📊 详细调试日志
        error_log("修为计算详情 - 角色ID: {$characterId}, 功法基础修为: {$techniqueBaseQi}, 地图进度修为: {$mapProgressQi}, 基础修为: {$baseQiGain}, 功法效率: {$efficiencyBonus}%, 境界效率: {$realmBonus}%, 地图效率: {$mapBonus}%, 总效率: {$totalEfficiencyBonus}%, 最终修为: {$qiGained}");
        
        // 🔧 添加：获取当前修为并检查上限
        $stmt = $pdo->prepare("SELECT cultivation_points FROM characters WHERE id = ?");
        $stmt->execute([$characterId]);
        $currentData = $stmt->fetch(PDO::FETCH_ASSOC);
        $currentQi = $currentData ? intval($currentData['cultivation_points']) : 0;
        
        // 检查修为上限
        $newQi = $currentQi + $qiGained;
        $limitCheck = checkCultivationLimit($pdo, $characterId, $newQi);
        
        $finalQi = $limitCheck['final_qi'];
        $actualGained = $finalQi - $currentQi;
        
        // 开始事务
        $pdo->beginTransaction();
        
        // 🔧 修改：更新修为的同时更新最后修炼时间
        $stmt = $pdo->prepare("UPDATE characters SET cultivation_points = ?, last_cultivation_time = NOW() WHERE id = ?");
        $stmt->execute([$finalQi, $characterId]);
        
        if ($stmt->rowCount() == 0) {
            throw new Exception("更新修为失败");
        }
        
        // 🆕 添加功法经验增长系统
        $techniqueUpdated = false;
        $techniqueMessage = '';
        
        if ($characterData && $characterData['cultivation_techniques'] && $characterData['current_technique']) {
            try {
                $techniques = json_decode($characterData['cultivation_techniques'], true);
                $currentTechniqueId = $characterData['current_technique'];
                
                // 🔧 修改：只为当前使用的功法增加经验
                if (isset($techniques[$currentTechniqueId])) {
                    $currentTechnique = $techniques[$currentTechniqueId];
                    
                    // 🔧 修改：功法经验增长改为固定每次+1，但圆满等级不再增长经验
                    $currentLevel = isset($currentTechnique['level']) ? $currentTechnique['level'] : 1;
                    $maxLevel = 9; // 功法最大等级

                    // 🔧 修复：圆满等级(9级)不再增长经验
                    if ($currentLevel >= $maxLevel) {
                        $techniqueExpGain = 0; // 圆满等级不增长经验
                        $newTechniqueExp = isset($currentTechnique['exp']) ? $currentTechnique['exp'] : 0;
                    } else {
                        $techniqueExpGain = 1; // 固定每次修炼+1经验
                        $newTechniqueExp = (isset($currentTechnique['exp']) ? $currentTechnique['exp'] : 0) + $techniqueExpGain;
                    }
                    
                    // 检查是否可以升级
                    $requiredExp = $currentLevel * 100; // 每级需要的经验：等级×100
                    $levelsGained = 0;
                    
                    while ($newTechniqueExp >= $requiredExp && $currentLevel < $maxLevel) {
                        $newTechniqueExp -= $requiredExp;
                        $currentLevel++;
                        $levelsGained++;
                        $requiredExp = $currentLevel * 100; // 下一级需要的经验
                        
                        if ($currentLevel >= $maxLevel) {
                            $newTechniqueExp = min($newTechniqueExp, $requiredExp - 1); // 满级时经验不超过上限
                            break;
                        }
                    }
                    
                    // 🔧 只更新当前使用的功法数据
                    $techniques[$currentTechniqueId]['exp'] = $newTechniqueExp;
                    $techniques[$currentTechniqueId]['level'] = $currentLevel;
                    
                    // 保存更新后的功法数据
                    $updatedTechniquesJson = json_encode($techniques, JSON_UNESCAPED_UNICODE);
                    $stmt = $pdo->prepare("UPDATE characters SET cultivation_techniques = ? WHERE id = ?");
                    $stmt->execute([$updatedTechniquesJson, $characterId]);
                    
                    $techniqueUpdated = true;
                    
                    // 构建功法升级消息
                    if ($levelsGained > 0) {
                        $techniqueMessage = "，{$currentTechnique['name']} 升级到 {$currentLevel} 级！";
                    } else if ($techniqueExpGain > 0) {
                        $techniqueMessage = "，{$currentTechnique['name']} 获得 {$techniqueExpGain} 点经验";
                    }
                } else {
                    // 如果当前功法不存在，初始化基础功法
                    $techniques['ningqi'] = [
                        'id' => 'ningqi',
                        'name' => '凝气决', 
                        'level' => 1,
                        'exp' => 1, // 直接给1点经验
                        'type' => 'basic',
                        'unlocked' => true,
                        'efficiency_bonus' => 0
                    ];
                    
                    // 更新当前功法ID
                    $updatedTechniquesJson = json_encode($techniques, JSON_UNESCAPED_UNICODE);
                    $stmt = $pdo->prepare("UPDATE characters SET cultivation_techniques = ?, current_technique = 'ningqi' WHERE id = ?");
                    $stmt->execute([$updatedTechniquesJson, $characterId]);
                    
                    $techniqueUpdated = true;
                    $techniqueMessage = "，凝气决 获得 1 点经验";
                }
            } catch (Exception $e) {
                error_log("功法经验更新失败: " . $e->getMessage());
                // 功法更新失败不影响修为获得
            }
        } else {
            // 如果没有功法数据，初始化基础功法
            try {
                $defaultTechniques = [
                    'ningqi' => [
                        'id' => 'ningqi',
                        'name' => '凝气决',
                        'level' => 1,
                        'exp' => 1, // 直接给1点经验
                        'type' => 'basic',
                        'unlocked' => true,
                        'efficiency_bonus' => 0
                    ]
                ];
                
                $techniquesJson = json_encode($defaultTechniques, JSON_UNESCAPED_UNICODE);
                $stmt = $pdo->prepare("UPDATE characters SET cultivation_techniques = ?, current_technique = 'ningqi' WHERE id = ?");
                $stmt->execute([$techniquesJson, $characterId]);
                
                $techniqueUpdated = true;
                $techniqueMessage = "，凝气决 获得 1 点经验";
            } catch (Exception $e) {
                error_log("初始化功法数据失败: " . $e->getMessage());
            }
        }
        
        $pdo->commit();
        
        // 获取更新后的境界信息
        $realmInfo = getUserRealmInfo($pdo, $userId);
        
        // 构建返回消息
        $message = "修炼完成，获得 {$actualGained} 点修为";
        if ($limitCheck['limited']) {
            $message .= "（修为已达上限 {$limitCheck['limit']}，超出部分已忽略）";
        }
        $message .= $techniqueMessage; // 添加功法升级信息
        
        echo json_encode([
            'success' => true,
            'qi_gained' => $actualGained,
            'original_gain' => $qiGained,
            'base_qi' => $baseQiGain,
            'technique_efficiency_bonus' => $efficiencyBonus,
            'realm_efficiency_bonus' => $realmBonus,
            'map_efficiency_bonus' => $mapBonus,
            'total_efficiency_bonus' => $totalEfficiencyBonus,
            'cultivation_limit' => $limitCheck['limit'],
            'is_limited' => $limitCheck['limited'],
            'realm_info' => $realmInfo['current_realm'], // 只返回当前境界信息
            'can_breakthrough' => $realmInfo['can_breakthrough'],
            'technique_updated' => $techniqueUpdated,
            'new_qi' => $finalQi,
            'message' => $message
        ]);
        
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        echo json_encode([
            'success' => false, 
            'message' => '自动修炼失败: ' . $e->getMessage()
        ]);
    }
}

// 🆕 获取当前使用功法的效率加成
function getCurrentTechniqueBonus($pdo, $characterId) {
    try {
        // 获取角色的功法数据
        $stmt = $pdo->prepare("SELECT cultivation_techniques, current_technique FROM characters WHERE id = ?");
        $stmt->execute([$characterId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$result || !$result['cultivation_techniques'] || !$result['current_technique']) {
            return 0;
        }
        
        $techniques = json_decode($result['cultivation_techniques'], true);
        $currentTechniqueId = $result['current_technique'];
        
        if (!$techniques || !isset($techniques[$currentTechniqueId])) {
            return 0;
        }
        
        $currentTechnique = $techniques[$currentTechniqueId];
        
        if (!isset($currentTechnique['name']) || !isset($currentTechnique['level'])) {
            return 0;
        }
        
        // 计算当前功法的效率加成
        $techniqueName = $currentTechnique['name'];
        $techniqueLevel = $currentTechnique['level'];
        
        return getTechniqueEfficiencyBonus($techniqueName, $techniqueLevel);
        
    } catch (Exception $e) {
        error_log("获取当前功法效率加成失败: " . $e->getMessage());
        return 0;
    }
}

// 获取功法效率加成
function getTechniqueEfficiencyBonus($techniqueName, $techniqueLevel = 1) {
    // 基础效率值 - 所有功法都来源于奇遇
    $baseBonuses = [
        '凝气决' => 0,      // 奇遇功法，无加成
        '先天功' => 2,      // 奇遇功法，+2%基础效率
        '聚灵决' => 4,      // 奇遇功法，+4%基础效率
        '炼神术' => 6,      // 奇遇功法，+6%基础效率
        '太极真经' => 8,    // 奇遇功法，+8%基础效率
        '九转玄功' => 10,   // 奇遇功法，+10%基础效率
        '混沌诀' => 12      // 奇遇功法，+12%基础效率
    ];
    
    $baseBonus = isset($baseBonuses[$techniqueName]) ? $baseBonuses[$techniqueName] : 0;
    
    // 等级加成：每级额外增加1%效率
    $levelBonus = ($techniqueLevel - 1) * 1;
    
    return $baseBonus + $levelBonus;
}

// 🆕 获取功法基础修为值
function getTechniqueBaseQi($techniqueName, $techniqueLevel = 1) {
    // 功法基础修为值配置 - 所有功法都来源于奇遇
    $baseQiValues = [
        '凝气决' => 10,     // 奇遇功法，基础修为10
        '先天功' => 12,     // 奇遇功法，基础修为12  
        '聚灵决' => 15,     // 奇遇功法，基础修为15
        '炼神术' => 18,     // 奇遇功法，基础修为18
        '太极真经' => 22,   // 奇遇功法，基础修为22
        '九转玄功' => 26,   // 奇遇功法，基础修为26
        '混沌诀' => 30      // 奇遇功法，基础修为30
    ];
    
    $baseQi = isset($baseQiValues[$techniqueName]) ? $baseQiValues[$techniqueName] : 10;
    
    // 等级加成：基础修为值 × 等级
    $finalBaseQi = $baseQi * $techniqueLevel;
    
    return $finalBaseQi;
}

// 🆕 获取所有已激活功法的效率加成总和
function getAllActiveTechniqueBonus($pdo, $characterId) {
    try {
        // 获取角色的功法数据
        $stmt = $pdo->prepare("SELECT cultivation_techniques FROM characters WHERE id = ?");
        $stmt->execute([$characterId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$result || !$result['cultivation_techniques']) {
            return 0;
        }
        
        $techniques = json_decode($result['cultivation_techniques'], true);
        if (!$techniques) {
            return 0;
        }
        
        $totalBonus = 0;
        
        // 遍历所有功法，计算总效率加成
        foreach ($techniques as $techniqueId => $technique) {
            if (isset($technique['name']) && isset($technique['level'])) {
                $techniqueName = $technique['name'];
                $techniqueLevel = $technique['level'];
                
                // 计算该功法的效率加成
                $techniqueBonus = getTechniqueEfficiencyBonus($techniqueName, $techniqueLevel);
                $totalBonus += $techniqueBonus;
            }
        }
        
        return $totalBonus;
        
    } catch (Exception $e) {
        error_log("获取所有功法效率加成失败: " . $e->getMessage());
        return 0;
    }
}

// 更新用户资源（兼容不同数据库结构）
function updateUserResource($pdo, $userId, $resourceType, $amount) {
    try {
        // 安全检查：只允许特定的资源类型
        $allowedResources = ['qi_energy', 'spirit_stones', 'silver', 'immortal_jade', 'tribulation_pills', 'soul_pills'];
        if (!in_array($resourceType, $allowedResources)) {
            error_log("不允许的资源类型: " . $resourceType);
            return false;
        }
        
        // 检查user_resources表是否存在
        $stmt = $pdo->prepare("SHOW TABLES LIKE 'user_resources'");
        $stmt->execute();
        $userResourcesExists = $stmt->rowCount() > 0;
        
        if ($userResourcesExists) {
            // 检查字段是否存在
            $stmt = $pdo->prepare("SHOW COLUMNS FROM user_resources LIKE ?");
            $stmt->execute([$resourceType]);
            $fieldExists = $stmt->rowCount() > 0;
            
            if ($fieldExists) {
                // 检查用户记录是否存在
                $stmt = $pdo->prepare("SELECT user_id FROM user_resources WHERE user_id = ?");
                $stmt->execute([$userId]);
                $userExists = $stmt->rowCount() > 0;
                
                if ($userExists) {
                    // 更新现有记录
                    $sql = "UPDATE user_resources SET `{$resourceType}` = `{$resourceType}` + ? WHERE user_id = ?";
                    $stmt = $pdo->prepare($sql);
                    $stmt->execute([$amount, $userId]);
                } else {
                    // 创建新记录
                    $sql = "INSERT INTO user_resources (user_id, `{$resourceType}`) VALUES (?, ?) ON DUPLICATE KEY UPDATE `{$resourceType}` = `{$resourceType}` + ?";
                    $stmt = $pdo->prepare($sql);
                    $stmt->execute([$userId, $amount, $amount]);
                }
            } else {
                error_log("user_resources表中不存在字段: " . $resourceType);
                return false;
            }
        } else {
            // 检查users表中的字段
            $stmt = $pdo->prepare("SHOW COLUMNS FROM users LIKE ?");
            $stmt->execute([$resourceType]);
            $fieldExists = $stmt->rowCount() > 0;
            
            if ($fieldExists) {
                // 使用users表
                $sql = "UPDATE users SET `{$resourceType}` = COALESCE(`{$resourceType}`, 0) + ? WHERE id = ?";
                $stmt = $pdo->prepare($sql);
                $stmt->execute([$amount, $userId]);
            } else {
                error_log("users表中不存在字段: " . $resourceType);
                return false;
            }
        }
        
        return true;
        
    } catch (Exception $e) {
        error_log("更新用户资源失败: " . $e->getMessage());
        return false;
    }
}

// 学习功法
function learnTechnique($pdo, $userId) {
    try {
        // 获取POST数据
        $input = json_decode(file_get_contents('php://input'), true);
        $techniqueId = isset($input['technique_id']) ? $input['technique_id'] : '';
        $cost = isset($input['cost']) ? intval($input['cost']) : 0;
        $costType = isset($input['cost_type']) ? $input['cost_type'] : 'spirit_stones';
        
        if (empty($techniqueId) || $cost <= 0) {
            echo json_encode(['success' => false, 'message' => '参数错误']);
            return;
        }
        
        // 检查资源
        $stmt = $pdo->prepare("SELECT * FROM user_resources WHERE user_id = ?");
        $stmt->execute([$userId]);
        $resources = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$resources) {
            echo json_encode(['success' => false, 'message' => '用户资源数据不存在']);
            return;
        }
        
        // 检查是否有足够的资源
        $resourceField = $costType === 'spirit_stones' ? 'spirit_stones' : 'immortal_jade';
        if ($resources[$resourceField] < $cost) {
            $resourceName = $costType === 'spirit_stones' ? '灵石' : '仙玉';
            echo json_encode(['success' => false, 'message' => "{$resourceName}不足，无法学习功法"]);
            return;
        }
        
        $pdo->beginTransaction();
        
        // 扣除资源
        $stmt = $pdo->prepare("UPDATE user_resources SET {$resourceField} = {$resourceField} - ? WHERE user_id = ?");
        $stmt->execute([$cost, $userId]);
        
        // 这里可以添加功法学习记录到数据库
        // 暂时只返回成功消息
        
        $pdo->commit();
        
        echo json_encode([
            'success' => true,
            'message' => "成功学习功法",
            'technique_id' => $techniqueId
        ]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => '学习功法失败: ' . $e->getMessage()]);
    }
}

// 临时调试函数：检查数据库结构
function checkDatabaseStructure($pdo, $userId) {
    $result = [];
    
    try {
        // 查看所有表
        $stmt = $pdo->query('SHOW TABLES');
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        $result['tables'] = $tables;
        
        // 查看users表结构
        $stmt = $pdo->query('SHOW COLUMNS FROM users');
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $result['users_columns'] = array_map(function($col) {
            return $col['Field'] . ' (' . $col['Type'] . ')';
        }, $columns);
        
        // 如果有user_resources表
        if (in_array('user_resources', $tables)) {
            $stmt = $pdo->query('SHOW COLUMNS FROM user_resources');
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $result['user_resources_columns'] = array_map(function($col) {
                return $col['Field'] . ' (' . $col['Type'] . ')';
            }, $columns);
        }
        
        // 查看用户数据
        $stmt = $pdo->prepare('SELECT * FROM users WHERE id = ?');
        $stmt->execute([$userId]);
        $userData = $stmt->fetch(PDO::FETCH_ASSOC);
        $result['user_data'] = [];
        foreach ($userData as $key => $value) {
            if (strpos($key, 'qi') !== false || strpos($key, 'realm') !== false || strpos($key, 'spirit') !== false || strpos($key, 'level') !== false) {
                $result['user_data'][$key] = $value;
            }
        }
        
        // 如果有user_resources表，查看资源数据
        if (in_array('user_resources', $tables)) {
            $stmt = $pdo->prepare('SELECT * FROM user_resources WHERE user_id = ?');
            $stmt->execute([$userId]);
            $resources = $stmt->fetch(PDO::FETCH_ASSOC);
            $result['user_resources_data'] = $resources ?: [];
        }
        
    } catch (Exception $e) {
        $result['error'] = $e->getMessage();
    }
    
    return $result;
}

// 检查修为上限并处理
function checkCultivationLimit($pdo, $characterId, $newQi) {
    try {
        // 获取角色最高通关地图（使用character_id）
        $maxMapId = 1; // 默认地图等级
        
        try {
            // 检查user_map_progress表是否存在
            $stmt = $pdo->query("SHOW TABLES LIKE 'user_map_progress'");
            if ($stmt->rowCount() > 0) {
                // 使用character_id查询最高通关地图
                $stmt = $pdo->prepare("SELECT MAX(map_id) as max_map_id FROM user_map_progress WHERE character_id = ?");
                $stmt->execute([$characterId]);
                $mapResult = $stmt->fetch(PDO::FETCH_ASSOC);
                $maxMapId = $mapResult && $mapResult['max_map_id'] ? intval($mapResult['max_map_id']) : 1;
            }
        } catch (Exception $mapError) {
            // 如果地图查询失败，使用默认值
            error_log("地图进度查询失败: " . $mapError->getMessage());
            $maxMapId = 1;
        }
        
        // 计算每30秒的修为增长
        // 🔧 使用与在线修炼相同的计算逻辑
        $calculation = calculateOfflineQiGain($pdo, $characterId);
        $baseQiGain = $calculation['base_qi'];
        $efficiencyBonus = $calculation['efficiency_bonus'];
        $qiPerCycle = intval($baseQiGain * (1 + $efficiencyBonus / 100));
        
        // 48小时 = 48 * 60 * 60 / 30 = 5760个周期
        $cultivationLimit = $qiPerCycle * 5760;
        
        // 检查是否超过上限
        if ($newQi > $cultivationLimit) {
            return [
                'limited' => true,
                'limit' => $cultivationLimit,
                'excess' => $newQi - $cultivationLimit,
                'final_qi' => $cultivationLimit
            ];
        }
        
        return [
            'limited' => false,
            'limit' => $cultivationLimit,
            'final_qi' => $newQi
        ];
        
    } catch (Exception $e) {
        error_log("检查修为上限失败: " . $e->getMessage());
        // 出错时不限制
        return [
            'limited' => false,
            'limit' => 999999999,
            'final_qi' => $newQi
        ];
    }
}

// 🆕 处理离线修炼收益
function handleOfflineCultivation($pdo, $userId) {
    try {
        // 🔧 确保数据库字段存在
        ensureCultivationFields($pdo);
        
        // 获取角色ID
        $stmt = $pdo->prepare("SELECT id FROM characters WHERE user_id = ? LIMIT 1");
        $stmt->execute([$userId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$character) {
            throw new Exception("角色不存在");
        }
        
        $characterId = $character['id'];
        
        // 获取最后修炼时间
        $stmt = $pdo->prepare("SELECT last_cultivation_time FROM characters WHERE id = ?");
        $stmt->execute([$characterId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $lastCultivationTime = $result['last_cultivation_time'] ? strtotime($result['last_cultivation_time']) : time();
        $currentTime = time();
        $offlineTime = $currentTime - $lastCultivationTime;
        
        // 如果离线时间少于30秒，不进行离线修炼计算
        if ($offlineTime < 30) {
            return [
                'success' => true,
                'offline_cycles' => 0,
                'offline_qi_gained' => 0,
                'offline_time' => $offlineTime,
                'message' => '无离线修炼收益'
            ];
        }
        
        // 计算离线修炼周期数（每30秒一个周期）
        $offlineCycles = floor($offlineTime / 30);
        
        // 限制最大离线时间为48小时（21600个周期）
        $maxCycles = 48 * 60 * 60 / 30; // 48小时
        $offlineCycles = min($offlineCycles, $maxCycles);
        
        if ($offlineCycles <= 0) {
            return [
                'success' => true,
                'offline_cycles' => 0,
                'offline_qi_gained' => 0,
                'offline_time' => $offlineTime,
                'message' => '离线时间不足一个修炼周期'
            ];
        }
        
        // 🔧 使用与在线修炼相同的计算逻辑
        $qiCalculation = calculateOfflineQiGain($pdo, $characterId);
        $qiPerCycle = $qiCalculation['qi_per_cycle'];
        $totalOfflineQi = $qiPerCycle * $offlineCycles;
        
        // 获取当前修为
        $stmt = $pdo->prepare("SELECT cultivation_points FROM characters WHERE id = ?");
        $stmt->execute([$characterId]);
        $currentData = $stmt->fetch(PDO::FETCH_ASSOC);
        $currentQi = $currentData ? intval($currentData['cultivation_points']) : 0;
        
        // 检查修为上限（48小时限制）
        $newQi = $currentQi + $totalOfflineQi;
        $limitCheck = checkCultivationLimit($pdo, $characterId, $newQi);
        
        $finalQi = $limitCheck['final_qi'];
        $actualGained = $finalQi - $currentQi;
        
        // 开始事务
        $pdo->beginTransaction();
        
        // 更新修为和最后修炼时间
        $stmt = $pdo->prepare("UPDATE characters SET cultivation_points = ?, last_cultivation_time = NOW() WHERE id = ?");
        $stmt->execute([$finalQi, $characterId]);
        
        // 🆕 处理离线期间的功法经验增长
        $techniqueUpdated = false;
        $techniqueExpGained = 0;
        
        // 获取角色功法数据
        $stmt = $pdo->prepare("SELECT cultivation_techniques, current_technique FROM characters WHERE id = ?");
        $stmt->execute([$characterId]);
        $characterData = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($characterData && $characterData['cultivation_techniques'] && $characterData['current_technique']) {
            try {
                $techniques = json_decode($characterData['cultivation_techniques'], true);
                $currentTechniqueId = $characterData['current_technique'];
                
                if (isset($techniques[$currentTechniqueId])) {
                    $currentTechnique = $techniques[$currentTechniqueId];
                    
                    // 计算离线期间获得的功法经验（每个周期+1经验）
                    $expGained = $offlineCycles;
                    $newExp = (isset($currentTechnique['exp']) ? $currentTechnique['exp'] : 0) + $expGained;
                    $currentLevel = isset($currentTechnique['level']) ? $currentTechnique['level'] : 1;
                    $expRequired = $currentLevel * 100; // 每级需要 等级*100 经验
                    
                    // 检查是否可以升级
                    while ($newExp >= $expRequired && $currentLevel < 10) {
                        $newExp -= $expRequired;
                        $currentLevel++;
                        $expRequired = $currentLevel * 100;
                        $techniqueUpdated = true;
                    }
                    
                    // 更新功法数据
                    $techniques[$currentTechniqueId]['exp'] = $newExp;
                    $techniques[$currentTechniqueId]['level'] = $currentLevel;
                    $techniqueExpGained = $expGained;
                    
                    // 保存更新后的功法数据
                    $stmt = $pdo->prepare("UPDATE characters SET cultivation_techniques = ? WHERE id = ?");
                    $stmt->execute([json_encode($techniques), $characterId]);
                }
            } catch (Exception $e) {
                error_log("离线功法经验更新失败: " . $e->getMessage());
            }
        }
        
        $pdo->commit();
        
        // 记录离线修炼日志
        error_log("离线修炼 - 角色ID: {$characterId}, 离线时间: {$offlineTime}秒, 周期数: {$offlineCycles}, 获得修为: {$actualGained}, 功法经验: {$techniqueExpGained}");
        
        return [
            'success' => true,
            'offline_cycles' => $offlineCycles,
            'offline_qi_gained' => $actualGained,
            'offline_time' => $offlineTime,
            'offline_time_hours' => round($offlineTime / 3600, 1),
            'technique_exp_gained' => $techniqueExpGained,
            'technique_updated' => $techniqueUpdated,
            'qi_per_cycle' => $qiPerCycle,
            'is_limited' => $limitCheck['limited'],
            'limit_info' => $limitCheck,
            'message' => "离线修炼 {$offlineCycles} 个周期，获得 {$actualGained} 修为" . ($techniqueUpdated ? "，功法升级！" : "")
        ];
        
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        
        error_log("处理离线修炼失败: " . $e->getMessage());
        return [
            'success' => false,
            'message' => '处理离线修炼失败: ' . $e->getMessage()
        ];
    }
}

// 🆕 计算离线修炼的单周期修为获得
function calculateOfflineQiGain($pdo, $characterId) {
    try {
        // 获取角色数据（包括功法信息）
        $stmt = $pdo->prepare("SELECT cultivation_techniques, current_technique FROM characters WHERE id = ?");
        $stmt->execute([$characterId]);
        $characterData = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // 🔧 获取地图进度修为（与在线修炼逻辑相同）
        $maxCurrentStage = 1; // 默认关卡数
        
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE 'user_map_progress'");
            if ($stmt->rowCount() > 0) {
                $stmt = $pdo->prepare("SELECT MAX(current_stage) as max_current_stage FROM user_map_progress WHERE character_id = ?");
                $stmt->execute([$characterId]);
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($result && $result['max_current_stage']) {
                    $maxCurrentStage = intval($result['max_current_stage']);
                }
            }
        } catch (Exception $e) {
            error_log("获取地图进度失败: " . $e->getMessage());
        }
        
        // 🔧 注释：地图进度不再影响修为计算（与在线修炼保持一致）
        
        // 🔧 修改：计算所有已学会功法的基础修为之和
        $techniqueBaseQi = 10; // 默认功法基础修为（至少有一个基础功法）
        $efficiencyBonus = 0; // 默认效率加成
        
        // 获取所有功法的基础修为和效率加成
        if ($characterData && $characterData['cultivation_techniques']) {
            try {
                $techniques = json_decode($characterData['cultivation_techniques'], true);
                
                if ($techniques && is_array($techniques)) {
                    error_log("离线修炼 - 计算所有功法基础修为: " . json_encode($techniques));
                    
                    // 计算所有已学会功法的基础修为之和
                    $totalTechniqueBaseQi = 0;
                    $totalEfficiencyBonus = 0;
                    
                    foreach ($techniques as $techniqueId => $technique) {
                        if ($technique && isset($technique['name']) && isset($technique['level'])) {
                            $techniqueName = $technique['name'];
                            $techniqueLevel = $technique['level'];
                            
                            // 计算单个功法的基础修为和效率加成
                            $singleTechniqueBaseQi = getTechniqueBaseQi($techniqueName, $techniqueLevel);
                            $singleEfficiencyBonus = getTechniqueEfficiencyBonus($techniqueName, $techniqueLevel);
                            
                            $totalTechniqueBaseQi += $singleTechniqueBaseQi;
                            $totalEfficiencyBonus += $singleEfficiencyBonus;
                            
                            error_log("离线修炼 - {$techniqueName} (等级{$techniqueLevel}): 基础修为 {$singleTechniqueBaseQi}点, 效率 +{$singleEfficiencyBonus}%");
                        }
                    }
                    
                    // 如果有功法数据，使用计算出的总和；否则使用默认值
                    if ($totalTechniqueBaseQi > 0) {
                        $techniqueBaseQi = $totalTechniqueBaseQi;
                    }
                    if ($totalEfficiencyBonus > 0) {
                        $efficiencyBonus = $totalEfficiencyBonus;
                    }
                    
                    error_log("离线修炼 - 所有功法基础修为总和: {$techniqueBaseQi}, 所有功法效率加成总和: {$efficiencyBonus}%");
                }
            } catch (Exception $e) {
                error_log("离线修炼 - 解析功法数据失败: " . $e->getMessage());
            }
        }
        
        // 🔧 计算最终修为（与在线修炼公式保持一致：基础修为 = 功法基础修为总和 + 地图进度修为）
        $mapProgressQi = max(0, $maxCurrentStage - 1); // 地图进度修为：每通关1关+1点基础修为（第1关不计算）
        $baseQiGain = $techniqueBaseQi + $mapProgressQi;
        
        // 🆕 计算境界效率加成（与在线修炼保持一致）
        $realmBonus = 0;
        try {
            // 获取角色境界信息
            $stmt = $pdo->prepare("SELECT realm_id FROM characters WHERE id = ?");
            $stmt->execute([$characterId]);
            $charRealm = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($charRealm && $charRealm['realm_id']) {
                $realmLevel = getCurrentRealmLevel($pdo, $charRealm['realm_id']);
                // 境界每1级+1%效率
                $realmBonus = $realmLevel * 1; // 每级+1%
            }
        } catch (Exception $e) {
            error_log("离线修炼 - 计算境界效率加成失败: " . $e->getMessage());
            $realmBonus = 0;
        }
        
        // 🆕 计算地图效率加成（与在线修炼保持一致）
        $mapBonus = 0;
        if ($maxCurrentStage > 1) {
            // 每多1关+0.5%效率（第1关不计算，从第2关开始）
            $mapBonus = ($maxCurrentStage - 1) * 0.5;
        }
        
        // 🔧 计算总效率加成：功法 + 境界 + 地图
        $totalEfficiencyBonus = $efficiencyBonus + $realmBonus + $mapBonus;
        
        $qiPerCycle = intval($baseQiGain * (1 + $totalEfficiencyBonus / 100));
        
        error_log("离线修炼 - 修为计算明细: 功法基础修为={$techniqueBaseQi}, 地图进度修为={$mapProgressQi}, 基础修为={$baseQiGain}, 功法效率={$efficiencyBonus}%, 境界效率={$realmBonus}%, 地图效率={$mapBonus}%, 总效率={$totalEfficiencyBonus}%, 最终修为={$qiPerCycle}");
        
        return [
            'qi_per_cycle' => $qiPerCycle,
            'base_qi' => $baseQiGain,
            'technique_base_qi' => $techniqueBaseQi,
            'map_progress_qi' => $mapProgressQi,
            'technique_efficiency_bonus' => $efficiencyBonus,
            'realm_efficiency_bonus' => $realmBonus,
            'map_efficiency_bonus' => $mapBonus,
            'total_efficiency_bonus' => $totalEfficiencyBonus,
            'max_current_stage' => $maxCurrentStage
        ];
        
    } catch (Exception $e) {
        error_log("计算离线修为失败: " . $e->getMessage());
        return [
            'qi_per_cycle' => 10, // 默认值（仅功法基础修为）
            'base_qi' => 10,
            'technique_base_qi' => 10,
            'map_progress_qi' => 0,
            'technique_efficiency_bonus' => 0,
            'realm_efficiency_bonus' => 0,
            'map_efficiency_bonus' => 0,
            'total_efficiency_bonus' => 0,
            'max_current_stage' => 1
        ];
    }
}

// 🔧 修复：添加缺失的getPills函数
function getPills($pdo, $userId) {
    try {
        $realmInfo = getUserRealmInfo($pdo, $userId);
        // 🔧 修复：使用正确的realm_id参数路径
        $realmId = isset($realmInfo['current_realm']['realm_id']) ? $realmInfo['current_realm']['realm_id'] : 1;
        $pills = getAvailablePills($pdo, $userId, $realmId);
        echo json_encode([
            'success' => true,
            'pills' => $pills,
            'realm_info' => $realmInfo,
            'debug' => [
                'realm_id' => $realmId,
                'realm_structure' => $realmInfo['current_realm']
            ]
        ]);
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => '获取丹药失败: ' . $e->getMessage()]);
    }
}

// 🆕 确保数据库表有必要的字段
function ensureCultivationFields($pdo) {
    try {
        // 检查并添加last_cultivation_time字段
        $stmt = $pdo->query("SHOW COLUMNS FROM characters LIKE 'last_cultivation_time'");
        if ($stmt->rowCount() == 0) {
            $pdo->exec("ALTER TABLE characters ADD COLUMN last_cultivation_time TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后修炼时间'");
            error_log("已添加 last_cultivation_time 字段");
        }
        
        return true;
    } catch (Exception $e) {
        error_log("添加修炼字段失败: " . $e->getMessage());
        return false;
    }
}

// 🆕 切换当前使用的功法
function switchTechnique($pdo, $userId) {
    try {
        // 获取请求数据
        $input = json_decode(file_get_contents('php://input'), true);
        $techniqueId = isset($input['technique_id']) ? $input['technique_id'] : '';
        
        if (empty($techniqueId)) {
            echo json_encode(['success' => false, 'message' => '功法ID不能为空']);
            return;
        }
        
        // 获取角色信息
        $stmt = $pdo->prepare("SELECT id, cultivation_techniques FROM characters WHERE user_id = ?");
        $stmt->execute([$userId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$character) {
            echo json_encode(['success' => false, 'message' => '角色不存在']);
            return;
        }
        
        // 解析功法数据
        $techniques = [];
        if (!empty($character['cultivation_techniques'])) {
            $techniques = json_decode($character['cultivation_techniques'], true);
            if (!$techniques) {
                $techniques = [];
            }
        }
        
        // 检查是否已学会该功法
        if (!isset($techniques[$techniqueId])) {
            echo json_encode(['success' => false, 'message' => '尚未学会此功法']);
            return;
        }
        
        // 更新当前使用的功法
        $stmt = $pdo->prepare("UPDATE characters SET current_technique = ? WHERE user_id = ?");
        $result = $stmt->execute([$techniqueId, $userId]);
        
        if ($result) {
            $techniqueName = isset($techniques[$techniqueId]['name']) ? $techniques[$techniqueId]['name'] : '未知功法';
            echo json_encode([
                'success' => true,
                'message' => "成功切换到 {$techniqueName}",
                'current_technique' => $techniqueId,
                'technique_name' => $techniqueName
            ]);
        } else {
            echo json_encode(['success' => false, 'message' => '功法切换失败']);
        }
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => '功法切换失败: ' . $e->getMessage()]);
    }
}

// 🆕 获取魂力状态
function getSoulStatus($pdo, $userId) {
    try {
        // 获取角色ID
        $stmt = $pdo->prepare("SELECT id FROM characters WHERE user_id = ? LIMIT 1");
        $stmt->execute([$userId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$character) {
            throw new Exception('角色不存在');
        }
        
        $characterId = $character['id'];
        
        // 获取魂力相关信息
        $stmt = $pdo->prepare("
            SELECT 
                current_soul_power,
                soul_damage_time,
                soul_recovery_per_second,
                realm_id
            FROM characters 
            WHERE id = ?
        ");
        $stmt->execute([$characterId]);
        $soulData = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$soulData) {
            throw new Exception('魂力数据不存在');
        }
        
        $currentTime = time();
        $soulPower = (int)$soulData['current_soul_power'];
        $damageTime = $soulData['soul_damage_time'];
        $recoveryRate = (float)$soulData['soul_recovery_per_second'];
        $realmId = (int)$soulData['realm_id'];
        
        // 计算实时魂力值
        if ($damageTime && $soulPower < 100 && $recoveryRate > 0) {
            $timePassed = $currentTime - $damageTime;
            $recoveredPower = $timePassed * $recoveryRate;
            $soulPower = min(100, $soulPower + $recoveredPower);
            
            // 如果魂力已完全恢复，清除受损状态
            if ($soulPower >= 100) {
                $stmt = $pdo->prepare("
                    UPDATE characters 
                    SET current_soul_power = 100, soul_damage_time = NULL, soul_recovery_per_second = NULL 
                    WHERE id = ?
                ");
                $stmt->execute([$characterId]);
                $soulPower = 100;
                $damageTime = null;
                $recoveryRate = null;
            }
        }
        
        // 获取当前境界的养魂丹
        $availableSoulPills = getAvailableSoulPills($pdo, $userId, $realmId);
        
        echo json_encode([
            'success' => true,
            'soul_status' => [
                'current_power' => round($soulPower, 2),
                'is_damaged' => $soulPower < 100,
                'damage_time' => $damageTime,
                'recovery_rate' => $recoveryRate,
                'time_to_full' => $recoveryRate > 0 ? ceil((100 - $soulPower) / $recoveryRate) : 0,
                'available_pills' => $availableSoulPills
            ]
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

// 🆕 使用养魂丹
function useSoulPill($pdo, $userId) {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        $pillId = (int)(isset($input['pill_id']) ? $input['pill_id'] : 0);
        
        if (!$pillId) {
            throw new Exception('请选择要使用的养魂丹');
        }
        
        // 获取角色ID
        $stmt = $pdo->prepare("SELECT id FROM characters WHERE user_id = ? LIMIT 1");
        $stmt->execute([$userId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$character) {
            throw new Exception('角色不存在');
        }
        
        $characterId = $character['id'];
        
        // 开始事务
        $pdo->beginTransaction();
        
        // 检查养魂丹是否存在且数量充足
        $stmt = $pdo->prepare("
            SELECT ui.quantity, gi.item_name, gi.special_effects
            FROM user_inventories ui
            JOIN game_items gi ON ui.item_id = gi.id
            WHERE ui.character_id = ? AND ui.item_id = ? AND ui.quantity > 0
        ");
        $stmt->execute([$characterId, $pillId]);
        $pill = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$pill) {
            throw new Exception('养魂丹不足或不存在');
        }
        
        // 获取当前魂力状态
        $stmt = $pdo->prepare("
            SELECT current_soul_power, soul_damage_time, soul_recovery_per_second
            FROM characters 
            WHERE id = ?
        ");
        $stmt->execute([$characterId]);
        $soulData = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $currentSoulPower = (int)$soulData['current_soul_power'];
        
        if ($currentSoulPower >= 100) {
            throw new Exception('魂力已满，无需使用养魂丹');
        }
        
        // 🔧 修复：从game_items表的special_effects JSON字段获取recovery_rate
        $recoveryRate = 100; // 默认100%恢复
        if (!empty($pill['special_effects'])) {
            try {
                $effects = json_decode($pill['special_effects'], true);
                if ($effects && isset($effects['recovery_rate'])) {
                    $recoveryRate = (int)$effects['recovery_rate'];
                }
            } catch (Exception $e) {
                error_log("解析special_effects失败: " . $e->getMessage());
                // 如果JSON解析失败，尝试从物品名称推断
                if (preg_match('/(\d+)/', $pill['item_name'], $matches)) {
                    $recoveryRate = (int)$matches[1];
                }
            }
        }
        
        // 计算恢复后的魂力值
        $newSoulPower = min(100, $currentSoulPower + $recoveryRate);
        
        // 扣除养魂丹
        $stmt = $pdo->prepare("
            UPDATE user_inventories 
            SET quantity = quantity - 1 
            WHERE character_id = ? AND item_id = ? AND quantity > 0
        ");
        $stmt->execute([$characterId, $pillId]);
        
        if ($stmt->rowCount() == 0) {
            throw new Exception('扣除养魂丹失败');
        }
        
        // 更新魂力值
        if ($newSoulPower >= 100) {
            // 完全恢复，清除受损状态
            $stmt = $pdo->prepare("
                UPDATE characters 
                SET current_soul_power = 100, soul_damage_time = NULL, soul_recovery_per_second = NULL 
                WHERE id = ?
            ");
            $stmt->execute([$characterId]);
        } else {
            // 部分恢复，保持受损状态
            $stmt = $pdo->prepare("
                UPDATE characters 
                SET current_soul_power = ? 
                WHERE id = ?
            ");
            $stmt->execute([$newSoulPower, $characterId]);
        }
        
        $pdo->commit();
        
        echo json_encode([
            'success' => true,
            'message' => "使用 {$pill['item_name']} 成功！魂力恢复 {$recoveryRate} 点",
            'soul_power' => [
                'old_value' => $currentSoulPower,
                'new_value' => $newSoulPower,
                'recovered' => $recoveryRate,
                'is_full' => $newSoulPower >= 100
            ]
        ]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

// 🆕 获取可用的养魂丹
function getAvailableSoulPills($pdo, $userId, $realmId) {
    try {
        // 获取角色ID
        $stmt = $pdo->prepare("SELECT id FROM characters WHERE user_id = ? LIMIT 1");
        $stmt->execute([$userId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$character) {
            return [];
        }
        
        $characterId = $character['id'];
        
        // 获取境界信息来确定大境界
        $stmt = $pdo->prepare("SELECT realm_name FROM realm_levels WHERE id = ?");
        $stmt->execute([$realmId]);
        $realm = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$realm) {
            return [];
        }
        
        $realmName = $realm['realm_name'];
        
        // 提取大境界名称（去掉阶数）
        $majorRealm = '';
        $realmKeywords = ['开光', '灵虚', '辟谷', '筑基', '结丹', '元婴', '心动', '元化', '离合', '空冥', '寂灭', '大乘', '渡劫', 
                         '凡仙', '地仙', '天仙', '真仙', '太乙真仙', '太乙金仙', '太乙玄仙',
                         '大罗真仙', '大罗金仙', '大罗玄仙', '准圣', '教主', '混元', 
                         '混元金仙', '混元至仙', '天道', '鸿蒙至元'];
        
        foreach ($realmKeywords as $keyword) {
            if (strpos($realmName, $keyword) !== false) {
                $majorRealm = $keyword;
                break;
            }
        }
        
        if (empty($majorRealm)) {
            return [];
        }
        
        // 🔧 修复：只能使用当前大境界的养魂丹
        $stmt = $pdo->prepare("
            SELECT ui.item_id, ui.quantity, gi.item_name, gi.special_effects
            FROM user_inventories ui
            JOIN game_items gi ON ui.item_id = gi.id
            WHERE ui.character_id = ? 
            AND ui.quantity > 0 
            AND gi.item_name LIKE ? 
            AND gi.item_name LIKE '%养魂丹%'
            ORDER BY gi.item_name
        ");
        $stmt->execute([$characterId, "%{$majorRealm}%"]);
        $pills = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $result = [];
        foreach ($pills as $pill) {
            // 🔧 修复：从game_items表的special_effects JSON字段获取recovery_rate
            $recoveryRate = 100; // 默认100%
            
            if (!empty($pill['special_effects'])) {
                try {
                    $effects = json_decode($pill['special_effects'], true);
                    if ($effects && isset($effects['recovery_rate'])) {
                        $recoveryRate = (int)$effects['recovery_rate'];
                    }
                } catch (Exception $e) {
                    error_log("解析special_effects失败: " . $e->getMessage());
                    // 如果JSON解析失败，尝试从物品名称推断
                    if (preg_match('/(\d+)/', $pill['item_name'], $matches)) {
                        $recoveryRate = (int)$matches[1];
                    }
                }
            }
            
            $result[] = [
                'item_id' => (int)$pill['item_id'],
                'item_name' => $pill['item_name'],
                'quantity' => (int)$pill['quantity'],
                'recovery_rate' => $recoveryRate,
                'effect_text' => "魂力恢复 {$recoveryRate} 点"
            ];
        }
        
        return $result;
        
    } catch (Exception $e) {
        error_log("获取可用养魂丹失败: " . $e->getMessage());
        return [];
    }
}

// 🆕 获取可用的养魂丹（API接口）
function getSoulPills($pdo, $userId) {
    try {
        // 从POST数据获取境界等级
        $input = json_decode(file_get_contents('php://input'), true);
        $realmLevel = isset($input['realm_level']) ? (int)$input['realm_level'] : 1;
        
        // 获取角色信息
        $stmt = $pdo->prepare("SELECT id, realm_id FROM characters WHERE user_id = ? LIMIT 1");
        $stmt->execute([$userId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$character) {
            throw new Exception('角色不存在');
        }
        
        $characterId = $character['id'];
        $realmId = $character['realm_id'] ?: 1;
        
        // 获取可用的养魂丹
        $soulPills = getAvailableSoulPills($pdo, $userId, $realmId);
        
        echo json_encode([
            'success' => true,
            'soul_pills' => $soulPills,
            'debug' => [
                'character_id' => $characterId,
                'realm_id' => $realmId,
                'pill_count' => count($soulPills)
            ]
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

// 🆕 使用渡劫丹功能
function useTribulationPill($pdo, $userId, $pillId, $successRateBonus) {
    try {
        // 获取角色ID
        $stmt = $pdo->prepare("SELECT id FROM characters WHERE user_id = ? LIMIT 1");
        $stmt->execute([$userId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$character) {
            throw new Exception('角色不存在');
        }
        
        $characterId = $character['id'];
        
        // 检查渡劫丹是否存在且数量充足
        $stmt = $pdo->prepare("
            SELECT ui.quantity, gi.item_name
            FROM user_inventories ui
            JOIN game_items gi ON ui.item_id = gi.id
            WHERE ui.character_id = ? AND ui.item_id = ? AND ui.quantity > 0
        ");
        $stmt->execute([$characterId, $pillId]);
        $pill = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$pill) {
            throw new Exception('渡劫丹不足或不存在');
        }
        
        // 扣除一个渡劫丹
        $stmt = $pdo->prepare("
            UPDATE user_inventories 
            SET quantity = quantity - 1 
            WHERE character_id = ? AND item_id = ? AND quantity > 0
        ");
        $stmt->execute([$characterId, $pillId]);
        
        if ($stmt->rowCount() == 0) {
            throw new Exception('扣除渡劫丹失败');
        }
        
        // 清理数量为0的记录
        $stmt = $pdo->prepare("
            DELETE FROM user_inventories 
            WHERE character_id = ? AND item_id = ? AND quantity <= 0
        ");
        $stmt->execute([$characterId, $pillId]);
        
        return [
            'success' => true,
            'message' => "成功使用{$pill['item_name']}",
            'success_rate_bonus' => $successRateBonus,
            'remaining_quantity' => max(0, $pill['quantity'] - 1)
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => $e->getMessage()
        ];
    }
}

?> 