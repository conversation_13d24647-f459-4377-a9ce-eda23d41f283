<?php
/**
 * 装备品质系统
 * 处理装备品质随机生成、属性计算等功能
 */

class EquipmentQualitySystem {
    
    /**
     * 品质配置
     * 🔧 修复：严格按照设计文档的品质概率设置
     */
    private static $RARITY_CONFIG = [
        '普通' => [
            'name' => '普通',
            'en_name' => 'common',
            'color' => '#95a5a6',
            'multiplier' => 1.0,
            'probability' => 60,  // 设计文档标准：60%
            'sell_multiplier' => 1.0
        ],
        '稀有' => [
            'name' => '稀有',
            'en_name' => 'uncommon', 
            'color' => '#27ae60',
            'multiplier' => 1.2,
            'probability' => 25,  // 设计文档标准：25%
            'sell_multiplier' => 1.5
        ],
        '史诗' => [
            'name' => '史诗',
            'en_name' => 'rare',
            'color' => '#3498db',
            'multiplier' => 1.5,
            'probability' => 12,  // 设计文档标准：12%
            'sell_multiplier' => 2.0
        ],
        '传说' => [
            'name' => '传说', 
            'en_name' => 'epic',
            'color' => '#9b59b6',
            'multiplier' => 2.0,
            'probability' => 3,   // 设计文档标准：3%
            'sell_multiplier' => 3.0
        ],
        '神话' => [
            'name' => '神话',
            'en_name' => 'legendary',
            'color' => '#d4af37',
            'multiplier' => 2.5,
            'probability' => 0,   // 特殊获得，不参与普通随机
            'sell_multiplier' => 5.0
        ]
    ];
    
    /**
     * 随机生成装备品质
     * 🔧 修复：严格按照设计文档的品质概率分布
     * @param string $contextType 获得方式类型 ('normal', 'boss', 'special', 'craft')
     * @param int $levelFactor 等级因子，影响高品质概率
     * @return string 品质名称（中文）
     */
    public static function generateRandomRarity($contextType = 'normal', $levelFactor = 1) {
        $weights = [];
        
        // 🔧 修复：基础权重严格按照设计文档设置
        foreach (self::$RARITY_CONFIG as $rarity => $config) {
            if ($config['probability'] > 0) {
                $weights[$rarity] = $config['probability'];
            }
        }
        
        // 🔧 修复：根据获得方式调整权重，但保持合理性
        switch ($contextType) {
            case 'boss':
                // BOSS掉落：适度提升高品质几率，不过度
                $weights['普通'] = 35;      // 从60%降到35%
                $weights['稀有'] = 30;      // 从25%提升到30%
                $weights['史诗'] = 25;      // 从12%提升到25%
                $weights['传说'] = 8;       // 从3%提升到8%
                $weights['神话'] = 2;       // 特殊情况下可获得
                break;
                
            case 'special':
                // 特殊事件掉落：中等提升
                $weights['普通'] = 45;      // 从60%降到45%
                $weights['稀有'] = 30;      // 从25%提升到30%
                $weights['史诗'] = 18;      // 从12%提升到18%
                $weights['传说'] = 6;       // 从3%提升到6%
                $weights['神话'] = 1;       // 极小概率
                break;
                
            case 'craft':
                // 制作装备：轻微提升中等品质
                $weights['普通'] = 50;      // 从60%降到50%
                $weights['稀有'] = 30;      // 从25%提升到30%
                $weights['史诗'] = 15;      // 从12%提升到15%
                $weights['传说'] = 5;       // 从3%提升到5%
                // 神话不参与制作
                break;
                
            default: // 'normal'
                // 普通掉落：严格按照设计文档标准
                $weights['普通'] = 60;      // 标准60%
                $weights['稀有'] = 25;      // 标准25%
                $weights['史诗'] = 12;      // 标准12%
                $weights['传说'] = 3;       // 标准3%
                // 神话不参与普通掉落
                break;
        }
        
        // 🔧 修复：等级因子影响（高等级区域轻微提升高品质概率）
        if ($levelFactor > 5) {
            $bonus = min(($levelFactor - 5) * 2, 10); // 最大10%的调整
            
            // 从普通品质中转移一部分概率到高品质
            $transferAmount = min($bonus, $weights['普通'] * 0.1); // 最多转移普通品质的10%
            
            $weights['普通'] -= $transferAmount;
            $weights['稀有'] += $transferAmount * 0.5;
            $weights['史诗'] += $transferAmount * 0.3;
            $weights['传说'] += $transferAmount * 0.2;
        }
        
        // 🔧 修复：权重随机选择
        $totalWeight = array_sum($weights);
        $random = mt_rand(1, intval($totalWeight));
        $currentWeight = 0;
        
        foreach ($weights as $rarity => $weight) {
            $currentWeight += $weight;
            if ($random <= $currentWeight) {
                error_log("品质生成: {$rarity} (方式: {$contextType}, 等级因子: {$levelFactor}, 概率: " . round($weight, 1) . "%)");
                return $rarity;
            }
        }
        
        // 默认返回普通品质
        error_log("品质生成失败，返回默认品质: 普通");
        return '普通';
    }
    
    /**
     * 获取品质倍率
     * @param string $rarity 品质名称（中文）
     * @return float 属性倍率
     */
    public static function getRarityMultiplier($rarity) {
        return isset(self::$RARITY_CONFIG[$rarity]) ? self::$RARITY_CONFIG[$rarity]['multiplier'] : 1.0;
    }
    
    /**
     * 获取品质售价倍率
     * @param string $rarity 品质名称（中文）
     * @return float 售价倍率
     */
    public static function getSellPriceMultiplier($rarity) {
        return isset(self::$RARITY_CONFIG[$rarity]) ? self::$RARITY_CONFIG[$rarity]['sell_multiplier'] : 1.0;
    }
    
    /**
     * 获取品质颜色
     * @param string $rarity 品质名称（中文）
     * @return string 颜色代码
     */
    public static function getRarityColor($rarity) {
        return isset(self::$RARITY_CONFIG[$rarity]) ? self::$RARITY_CONFIG[$rarity]['color'] : '#95a5a6';
    }
    
    /**
     * 获取品质英文名称
     * @param string $rarity 品质名称（中文）
     * @return string 英文名称
     */
    public static function getRarityEnglishName($rarity) {
        return isset(self::$RARITY_CONFIG[$rarity]) ? self::$RARITY_CONFIG[$rarity]['en_name'] : 'common';
    }
    
    /**
     * 计算装备属性（应用品质倍率）
     * @param array $baseAttributes 基础属性
     * @param string $rarity 品质
     * @return array 计算后的属性
     */
    public static function calculateEquipmentAttributes($baseAttributes, $rarity) {
        $multiplier = self::getRarityMultiplier($rarity);
        $calculatedAttributes = [];
        
        // 🔧 修复：使用数据库实际字段名
        $scalableAttributes = [
            'physical_attack',      // 🔧 修复：使用实际数据库字段名
            'immortal_attack',         // 🔧 修复：使用实际数据库字段名
            'physical_defense',     // 🔧 修复：使用实际数据库字段名
            'immortal_defense',        // 🔧 修复：使用实际数据库字段名
            'hp_bonus',
            'mp_bonus',
            'speed_bonus',
            'critical_bonus', // 🔧 修复：使用正确的数据库字段名
            'critical_damage',
            'accuracy_bonus',
            'dodge_bonus',
            'critical_resistance',
            // 🔧 保留：向后兼容字段
            
        ];
        
        foreach ($baseAttributes as $key => $value) {
            if (in_array($key, $scalableAttributes) && is_numeric($value)) {
                // 应用品质倍率，并保留合理的小数位数
                if (in_array($key, ['critical_damage'])) {
                    // 🔧 修复：只有暴击伤害是百分比属性（小数存储）
                    $calculatedAttributes[$key] = round($value * $multiplier, 4);
                } else {
                    // 🔧 修复：其他属性都是整数数值（包括暴击率）
                    $calculatedAttributes[$key] = ceil($value * $multiplier);
                }
            } else {
                // 不受品质影响的属性保持原值
                $calculatedAttributes[$key] = $value;
            }
        }
        
        // 🔧 新增：根据品质生成随机附加属性
        $randomAttributeCount = 0;
        
        // 🔧 修复：根据品质确定随机属性条数
        if ($rarity === '普通' || $rarity === 'common') {
            $randomAttributeCount = 0;  // 普通品质：无随机附加属性
        } elseif ($rarity === '稀有' || $rarity === 'uncommon') {
            $randomAttributeCount = 1;  // 稀有品质：必定附加1条随机属性
        } elseif ($rarity === '史诗' || $rarity === 'rare') {
            $randomAttributeCount = 2;  // 史诗品质：必定附加2条随机属性
        } elseif ($rarity === '传说' || $rarity === 'epic') {
            $randomAttributeCount = 3;  // 传说品质：必定附加3条随机属性
        } elseif ($rarity === '神话' || $rarity === 'legendary') {
            $randomAttributeCount = 4;  // 神话品质：必定附加4条随机属性
        }
        
        error_log("🎯 [品质系统] 品质 '{$rarity}' 将附加 {$randomAttributeCount} 条随机属性");
        
        // 🔧 生成随机属性（如果需要）
        if ($randomAttributeCount > 0) {
            // 🔧 定义可用的随机属性池
            $availableAttributes = [
                'hp_bonus',
                'mp_bonus', 
                'speed_bonus',
                'physical_attack',
                'immortal_attack',
                'physical_defense',
                'immortal_defense',
                'critical_bonus', // 🔧 修复：使用正确的数据库字段名
                'critical_damage',
                'accuracy_bonus',
                'dodge_bonus',
                'critical_resistance'
            ];
            
            // 🔧 随机选择指定数量的属性（不重复）
            $selectedAttributes = array_rand(array_flip($availableAttributes), min($randomAttributeCount, count($availableAttributes)));
            
            // 如果只选择了一个属性，array_rand返回字符串而不是数组
            if (!is_array($selectedAttributes)) {
                $selectedAttributes = [$selectedAttributes];
            }
            
            error_log("🎲 [品质系统] 选择的随机属性: " . implode(', ', $selectedAttributes));
            
            // 🔧 为每个选中的属性生成随机加成值
            foreach ($selectedAttributes as $randomAttr) {
                $bonusValue = self::generateRandomAttributeBonus($randomAttr, $rarity, $multiplier);
                
                // 🔧 修复：暴击伤害已经在生成时转为小数格式，直接使用
                if ($randomAttr === 'critical_damage') {
                    // 暴击伤害：已经是小数格式，直接使用
                    $calculatedAttributes[$randomAttr] += floatval($bonusValue);
                } else {
                    // 其他属性：整数数值（包括暴击率）
                    $calculatedAttributes[$randomAttr] += intval($bonusValue);
                }
                
                error_log("🎯 [品质系统] 添加随机属性: {$randomAttr} = +{$bonusValue}");
            }
            

        }
        
        return $calculatedAttributes;
    }
    
    /**
     * 🔧 新增：生成随机属性加成值（品质系统统一版本）
     * @param string $attributeType 属性类型
     * @param string $rarity 品质
     * @param float $rarityMultiplier 品质倍率
     * @return int 随机属性值
     */
    public static function generateRandomAttributeBonus($attributeType, $rarity, $rarityMultiplier) {
        // 🔧 修复：使用数据库实际字段名，critical_damage使用整数范围
        $baseValues = [
            // 🔧 基础属性
            'hp_bonus' => [30, 50],
            'mp_bonus' => [10, 30],
            'speed_bonus' => [1, 5],
            
            // 🔧 攻击属性
            'physical_attack' => [2, 8],
            'immortal_attack' => [2, 8],
            
            // 🔧 防御属性
            'physical_defense' => [1, 6],
            'immortal_defense' => [1, 6],
            
            // 🔧 修复：所有属性都使用整数范围，暴击伤害后续特殊处理（降低一半）
            'critical_bonus' => [2, 5],     // 2-7点 (整数数值) - 降低一半
            'critical_damage' => [1, 5],    // 🔧 修复：2-7整数，代表2%-7%，后续转为小数（降低一半）
            'accuracy_bonus' => [2, 7],     // 2-7点 (整数数值) - 降低一半
            'dodge_bonus' => [1, 3],        // 1-5点 (整数数值) - 降低一半
            'critical_resistance' => [1, 3],        // 1-5点 (整数数值) - 降低一半
        ];
        
        if (!isset($baseValues[$attributeType])) {
            error_log("⚠️ [品质系统] 未知的随机属性类型: {$attributeType}");
            return 0;
        }
        
        $min = $baseValues[$attributeType][0];
        $max = $baseValues[$attributeType][1];
        
        // 随机生成基础值
        $baseValue = mt_rand($min, $max);
        
        // 🔧 修复：暴击伤害特殊处理，先计算整数值再转为小数
        if ($attributeType === 'critical_damage') {
            // 暴击伤害：先计算整数百分比，然后转为小数格式
            $integerPercentage = intval($baseValue * $rarityMultiplier);
            $finalValue = $integerPercentage / 100.0;  // 转为小数格式
            error_log("🎲 [品质系统] 暴击伤害生成: {$attributeType} = {$finalValue} (整数%: {$integerPercentage}, 基础: {$baseValue}, 倍率: {$rarityMultiplier})");
        } else {
            // 其他属性：直接使用整数值
            $finalValue = intval($baseValue * $rarityMultiplier);
            error_log("🎲 [品质系统] 随机属性生成: {$attributeType} = {$finalValue} (基础: {$baseValue}, 倍率: {$rarityMultiplier})");
        }
        
        return $finalValue;
    }
    
    /**
     * 获取装备到背包时的完整属性数据
     * @param int $gameItemId 游戏物品ID
     * @param string $rarity 品质（可选，如果不提供则随机生成）
     * @param string $contextType 获得方式
     * @return array 包含品质和计算后属性的数据
     */
    public static function getEquipmentForInventory($gameItemId, $rarity = null, $contextType = 'normal') {
        // 🔧 修复：数据库连接路径问题
        $pdo = null; // 初始化变量
        $dbPath = null;
        
        if (file_exists(__DIR__ . '/../config/database.php')) {
            $dbPath = __DIR__ . '/../config/database.php';
        } elseif (file_exists(__DIR__ . '/../../src/config/database.php')) {
            $dbPath = __DIR__ . '/../../src/config/database.php';
        } elseif (file_exists(__DIR__ . '/../includes/functions.php')) {
            // 尝试使用functions.php中的数据库连接
            require_once __DIR__ . '/../includes/functions.php';
            $pdo = getDatabase();
        }
        
        if (!$pdo && $dbPath) {
            require_once $dbPath;
            $database = new Database();
            $pdo = $database->getConnection();
        }
        
        if (!$pdo) {
            // 最后的备用方案：使用functions.php
            require_once __DIR__ . '/../includes/functions.php';
            $pdo = getDatabase();
        }
        
        try {
            // 获取基础装备信息
            $stmt = $pdo->prepare("
                SELECT gi.* 
                FROM game_items gi
                WHERE gi.id = ?
            ");
            $stmt->execute([$gameItemId]);
            $item = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$item) {
                throw new Exception("装备不存在: ID {$gameItemId}");
            }
            
            // 确定品质
            if ($rarity === null) {
                // 检查是否支持品质变化
                if ($item['can_have_rarity']) {
                    $levelFactor = ceil($item['level_requirement'] / 10);
                    $rarity = self::generateRandomRarity($contextType, $levelFactor);
                } else {
                    $rarity = $item['rarity']; // 使用固定品质
                }
            }
            
            // 🔧 修复：提取基础属性，使用数据库实际字段名
            $baseAttributes = [
                'physical_attack' => intval($item['physical_attack']),      // 🔧 修复：使用实际字段名
                'immortal_attack' => intval($item['immortal_attack']),            // 🔧 修复：使用实际字段名
                'physical_defense' => intval($item['physical_defense']),    // 🔧 修复：使用实际字段名
                'immortal_defense' => intval($item['immortal_defense']),          // 🔧 修复：使用实际字段名
                'hp_bonus' => intval($item['hp_bonus']),
                'mp_bonus' => intval($item['mp_bonus']),
                'speed_bonus' => intval($item['speed_bonus']),
                'critical_bonus' => floatval($item['critical_bonus']), // 🔧 修复：使用正确的数据库字段名
                'critical_damage' => floatval($item['critical_damage']),
                'accuracy_bonus' => floatval($item['accuracy_bonus']),
                'dodge_bonus' => floatval($item['dodge_bonus']),
                'critical_resistance' => floatval($item['critical_resistance'])
            ];
            
            // 计算最终属性
            $finalAttributes = self::calculateEquipmentAttributes($baseAttributes, $rarity);
            
            // 返回完整数据
            return [
                'item_id' => $gameItemId,
                'rarity' => $rarity,
                'rarity_en' => self::getRarityEnglishName($rarity),
                'rarity_color' => self::getRarityColor($rarity),
                'multiplier' => self::getRarityMultiplier($rarity),
                'attributes' => $finalAttributes,
                'item_info' => $item
            ];
            
        } catch (Exception $e) {
            error_log("装备品质系统错误: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 获取所有品质配置（用于前端显示）
     * @return array 品质配置数组
     */
    public static function getAllRarityConfigs() {
        return self::$RARITY_CONFIG;
    }
}

?> 