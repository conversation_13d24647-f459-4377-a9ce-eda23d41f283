/**
 * 水系技能模块 - 水龙卷
 * 适用于 item_skills 表中的 animation_model = 'shuilongjuan'
 */

// 水系技能 - 水龙卷
class ShuiLongJuanSkill extends BaseSkill {
    async execute(skillData, weaponImage) {
        // 使用数据库中的真实技能名称，而不是硬编码
        const skillName = skillData?.skillName || skillData?.displayName || '水龙卷'; // 提供默认值作为后备
        await this.showSkillShout(skillName);
        
        // 执行水龙卷动画，传递武器图片
        await this.createShuiLongJuanAnimation(weaponImage);
    }
    
    async createShuiLongJuanAnimation(weaponImage) {
        // 🔧 动态判断位置映射
        let casterPos, targetPos;
        
        if (this.isEnemySkill) {
            // 敌方技能：敌人是施法者，玩家是目标
            casterPos = this.getCharacterPosition(false); // 敌人位置
            targetPos = this.getCharacterPosition(true);  // 玩家位置
        } else {
            // 我方技能：玩家是施法者，敌人是目标
            casterPos = this.getCharacterPosition(true);  // 玩家位置
            targetPos = this.getCharacterPosition(false); // 敌人位置
        }
        
        // 创建动画容器
        const container = document.createElement('div');
        container.className = 'shuilongjuan-container';
        this.effectsContainer.appendChild(container);
        
        // 使用动态位置数据
        const startX = casterPos.x;
        const startY = casterPos.y;
        const endX = targetPos.x;
        const endY = targetPos.y;
        
        try {
            // === 第一阶段：蓄力 - 水元素汇聚 ===
            await this.createChargePhase(container, startX, startY, weaponImage);
            
            // === 第二阶段：发射 - 水龙卷形成并飞行 ===
            await this.createLaunchPhase(container, startX, startY, endX, endY);
            
            // === 第三阶段：击中 - 螺旋爆发和水花四溅 ===
            await this.createImpactPhase(container, endX, endY);
            
        } finally {
            // 清理容器
            setTimeout(() => {
                if (container && container.parentNode) {
                    container.parentNode.removeChild(container);
                }
            }, 100);
        }
    }
    
    // 蓄力阶段：水元素汇聚
    async createChargePhase(container, startX, startY, weaponImage) {
        // 创建水元素魔法阵
        const waterCircle = document.createElement('div');
        waterCircle.className = 'shuilongjuan-magic-circle';
        waterCircle.style.left = `${startX}px`;
        waterCircle.style.top = `${startY}px`;
        container.appendChild(waterCircle);
        
        // 创建内圈水纹
        const innerRipples = document.createElement('div');
        innerRipples.className = 'shuilongjuan-inner-ripples';
        innerRipples.style.left = `${startX}px`;
        innerRipples.style.top = `${startY}px`;
        container.appendChild(innerRipples);
        
        // 创建外圈水纹
        const outerRipples = document.createElement('div');
        outerRipples.className = 'shuilongjuan-outer-ripples';
        outerRipples.style.left = `${startX}px`;
        outerRipples.style.top = `${startY}px`;
        container.appendChild(outerRipples);
        
        // 创建武器图片在中心旋转
        if (weaponImage) {
            const weaponSprite = document.createElement('div');
            weaponSprite.className = 'shuilongjuan-weapon-sprite';
            weaponSprite.style.left = `${startX}px`;
            weaponSprite.style.top = `${startY}px`;
            
            // 添加武器图片背景
            this.addWeaponImage(weaponSprite, weaponImage);
            // 🗡️ 动态调整武器图片角度
            const weaponImg = weaponSprite.querySelector('.weapon-image');
            if (weaponImg) {
                weaponImg.style.transform = `rotate(${this.calculateInitialSwordRotation()}deg)`;
            }
            
            container.appendChild(weaponSprite);
        }
        
        // 创建蓄力能量核心
        const energyCore = document.createElement('div');
        energyCore.className = 'shuilongjuan-energy-core';
        energyCore.style.left = `${startX}px`;
        energyCore.style.top = `${startY}px`;
        container.appendChild(energyCore);
        
        // 创建水滴汇聚效果（增加到30个）
        for (let i = 0; i < 30; i++) {
            const droplet = document.createElement('div');
            droplet.className = 'shuilongjuan-charge-droplet';
            droplet.style.left = `${startX}px`;
            droplet.style.top = `${startY}px`;
            
            const angle = Math.random() * Math.PI * 2;
            const radius = 15 + Math.random() * 35;
            const moveX = Math.cos(angle) * radius;
            const moveY = Math.sin(angle) * radius;
            
            droplet.style.setProperty('--chargeX', `${moveX}px`);
            droplet.style.setProperty('--chargeY', `${moveY}px`);
            droplet.style.animationDelay = `${Math.random() * 1.2}s`;
            
            container.appendChild(droplet);
        }
        
        // 创建环绕的水流螺旋
        for (let i = 0; i < 16; i++) {
            const angle = (i / 16) * Math.PI * 2;
            const radius = 60;
            const spiralX = startX + Math.cos(angle) * radius;
            const spiralY = startY + Math.sin(angle) * radius;
            
            const spiral = document.createElement('div');
            spiral.className = 'shuilongjuan-charge-spiral';
            spiral.style.left = `${spiralX}px`;
            spiral.style.top = `${spiralY}px`;
            spiral.style.animationDelay = `${i * 0.06}s`;
            container.appendChild(spiral);
        }
        
        // 创建水元素波纹
        for (let i = 0; i < 5; i++) {
            const ripple = document.createElement('div');
            ripple.className = 'shuilongjuan-energy-ripple';
            ripple.style.left = `${startX}px`;
            ripple.style.top = `${startY}px`;
            ripple.style.animationDelay = `${i * 0.15}s`;
            container.appendChild(ripple);
        }
        
        // 创建蒸汽效果
        for (let i = 0; i < 8; i++) {
            const steam = document.createElement('div');
            steam.className = 'shuilongjuan-steam';
            steam.style.left = `${startX + (Math.random() - 0.5) * 40}px`;
            steam.style.top = `${startY + (Math.random() - 0.5) * 40}px`;
            steam.style.animationDelay = `${Math.random() * 0.8}s`;
            container.appendChild(steam);
        }
        
        // 等待蓄力完成
        await this.wait(1000);
        
        // 移除蓄力效果
        waterCircle.remove();
        innerRipples.remove();
        outerRipples.remove();
        energyCore.remove();
        document.querySelectorAll('.shuilongjuan-charge-droplet').forEach(d => d.remove());
        document.querySelectorAll('.shuilongjuan-charge-spiral').forEach(s => s.remove());
        document.querySelectorAll('.shuilongjuan-energy-ripple').forEach(r => r.remove());
        document.querySelectorAll('.shuilongjuan-steam').forEach(s => s.remove());
        document.querySelectorAll('.shuilongjuan-weapon-sprite').forEach(w => w.remove());
    }
    
    // 发射阶段：水龙卷形成并飞行
    async createLaunchPhase(container, startX, startY, endX, endY) {
        // 创建预发射水流震动
        const waterShake = document.createElement('div');
        waterShake.className = 'shuilongjuan-water-shake';
        waterShake.style.left = `${startX}px`;
        waterShake.style.top = `${startY}px`;
        container.appendChild(waterShake);
        
        await this.wait(200);
        
        // 计算飞行距离和时间 - 更快的移动速度
        const distance = Math.sqrt(Math.pow(endX - startX, 2) + Math.pow(endY - startY, 2));
        const flyTime = Math.max(0.8, distance / 600); // 最少0.8秒，更快的速度
        
        // 创建主要水龙卷
        const mainTornado = document.createElement('div');
        mainTornado.className = 'shuilongjuan-main-tornado';
        mainTornado.style.left = `${startX}px`;
        mainTornado.style.top = `${startY}px`;
        mainTornado.style.setProperty('--targetX', `${endX - startX}px`);
        mainTornado.style.setProperty('--targetY', `${endY - startY}px`);
        mainTornado.style.setProperty('--flyTime', `${flyTime}s`);
        container.appendChild(mainTornado);
        
        // 创建螺旋水流层（3层不同大小的螺旋）
        for (let layer = 0; layer < 3; layer++) {
            const spiralLayer = document.createElement('div');
            spiralLayer.className = `shuilongjuan-spiral-layer-${layer + 1}`;
            spiralLayer.style.left = `${startX}px`;
            spiralLayer.style.top = `${startY}px`;
            spiralLayer.style.setProperty('--targetX', `${endX - startX}px`);
            spiralLayer.style.setProperty('--targetY', `${endY - startY}px`);
            spiralLayer.style.setProperty('--flyTime', `${flyTime}s`);
            spiralLayer.style.animationDelay = `${layer * 0.1}s`;
            container.appendChild(spiralLayer);
        }
        
        // 创建水流拖尾效果（增强版）
        for (let i = 0; i < 8; i++) {
            const trail = document.createElement('div');
            trail.className = 'shuilongjuan-enhanced-trail';
            trail.style.left = `${startX}px`;
            trail.style.top = `${startY}px`;
            trail.style.setProperty('--targetX', `${endX - startX}px`);
            trail.style.setProperty('--targetY', `${endY - startY}px`);
            trail.style.setProperty('--flyTime', `${flyTime}s`);
            trail.style.animationDelay = `${i * 0.08}s`;
            trail.style.setProperty('--trailIndex', i);
            container.appendChild(trail);
        }
        
        // 创建飞行过程中的水花飞溅
        const splashInterval = setInterval(() => {
            const splash = document.createElement('div');
            splash.className = 'shuilongjuan-flight-splash';
            splash.style.left = `${startX + Math.random() * (endX - startX)}px`;
            splash.style.top = `${startY + Math.random() * (endY - startY)}px`;
            container.appendChild(splash);
            
            setTimeout(() => splash.remove(), 800);
        }, 150);
        
        await this.wait(flyTime * 1000);
        clearInterval(splashInterval);
        
        // 移除发射效果
        waterShake.remove();
        mainTornado.remove();
        document.querySelectorAll('[class*="shuilongjuan-spiral-layer"]').forEach(s => s.remove());
        document.querySelectorAll('.shuilongjuan-enhanced-trail').forEach(t => t.remove());
        document.querySelectorAll('.shuilongjuan-flight-splash').forEach(s => s.remove());
    }
    
    // 击中阶段：螺旋爆发和水花四溅
    async createImpactPhase(container, endX, endY) {
        // 第一阶段：瞬间冲击波
        const impactFlash = document.createElement('div');
        impactFlash.className = 'shuilongjuan-impact-flash';
        impactFlash.style.left = `${endX}px`;
        impactFlash.style.top = `${endY}px`;
        container.appendChild(impactFlash);
        
        // 第二阶段：螺旋爆发核心
        const spiralCore = document.createElement('div');
        spiralCore.className = 'shuilongjuan-spiral-core';
        spiralCore.style.left = `${endX}px`;
        spiralCore.style.top = `${endY}px`;
        container.appendChild(spiralCore);
        
        // 第三阶段：多层冲击波
        setTimeout(() => {
            for (let i = 0; i < 4; i++) {
                const shockwave = document.createElement('div');
                shockwave.className = 'shuilongjuan-impact-shockwave';
                shockwave.style.left = `${endX}px`;
                shockwave.style.top = `${endY}px`;
                shockwave.style.animationDelay = `${i * 0.12}s`;
                container.appendChild(shockwave);
            }
        }, 100);
        
        // 第四阶段：螺旋水花爆发
        setTimeout(() => {
            for (let i = 0; i < 32; i++) {
                const splash = document.createElement('div');
                splash.className = 'shuilongjuan-spiral-splash';
                splash.style.left = `${endX}px`;
                splash.style.top = `${endY}px`;
                
                const angle = (i / 32) * Math.PI * 2;
                const distance = 30 + Math.random() * 60;
                const spiralOffset = Math.sin(angle * 3) * 20; // 螺旋效果
                
                splash.style.setProperty('--splashAngle', `${angle * 180 / Math.PI}deg`);
                splash.style.setProperty('--splashDistance', `${distance}px`);
                splash.style.setProperty('--spiralOffset', `${spiralOffset}px`);
                splash.style.animationDelay = `${i * 0.02}s`;
                
                container.appendChild(splash);
            }
        }, 200);
        
        // 第五阶段：水雾弥漫
        setTimeout(() => {
            for (let i = 0; i < 12; i++) {
                const mist = document.createElement('div');
                mist.className = 'shuilongjuan-impact-mist';
                mist.style.left = `${endX + (Math.random() - 0.5) * 80}px`;
                mist.style.top = `${endY + (Math.random() - 0.5) * 80}px`;
                mist.style.animationDelay = `${i * 0.1}s`;
                container.appendChild(mist);
            }
        }, 300);
        
        // 第六阶段：水滴回落
        setTimeout(() => {
            for (let i = 0; i < 20; i++) {
                const droplet = document.createElement('div');
                droplet.className = 'shuilongjuan-falling-droplet';
                droplet.style.left = `${endX + (Math.random() - 0.5) * 120}px`;
                droplet.style.top = `${endY - 60 - Math.random() * 40}px`;
                droplet.style.animationDelay = `${i * 0.05}s`;
                container.appendChild(droplet);
            }
        }, 400);
        
        // 击中特效
        this.createHitEffect(endX, endY, true);
        
        // 🔧 修复：给被攻击者添加水流冲击效果
        const targetSelector = !this.isEnemySkill ? '.enemy .character-sprite' : '.player .character-sprite';
        const targetSprite = document.querySelector(targetSelector);
        if (targetSprite) {
            targetSprite.style.animation = 'water-hit 1.5s ease-out, water-shake 0.15s ease-in-out 6';
        }
        
        await this.wait(1200);
        
        // 🔧 修复：清理击中效果
        if (targetSprite) {
            targetSprite.style.animation = '';
        }
    }
    // 🗡️ 动态计算剑的初始旋转角度
    calculateInitialSwordRotation() {
        // 敌方技能：剑尖向下（指向玩家），我方技能：剑尖向上（指向敌人）
        return this.isEnemySkill ? 0 : 180;
    }
}

// 导出技能类
window.WaterSkills = { ShuiLongJuanSkill }; 