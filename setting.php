<?php

/**
 * 一念修仙项目全局配置文件
 * 定义项目的所有目录路径、基本设置和常量
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-06-27
 */

// 防止直接访问
if (!defined('YINIAN_SETTING_LOADED')) {
    define('YINIAN_SETTING_LOADED', true);
}

// ==========================================
// 🎮 游戏基本信息设置
// ==========================================

// 游戏基本信息
define('GAME_NAME', '一念修仙');
define('GAME_VERSION', '1.0.0');
define('GAME_AUTHOR', '一念修仙开发团队');
define('GAME_DESCRIPTION', '一款基于Web的修仙类RPG游戏');

// 游戏环境设置
define('GAME_ENV', 'production'); // development, production, testing
define('GAME_DEBUG', false); // 调试模式开关
define('GAME_MAINTENANCE', false); // 维护模式开关

// ==========================================
// 📁 项目根目录和基础路径设置
// ==========================================

// 项目根目录（绝对路径）
define('PROJECT_ROOT', dirname(__FILE__));

// Web根目录（相对于项目根目录）
define('WEB_ROOT', PROJECT_ROOT);

// 公共访问目录
define('PUBLIC_ROOT', PROJECT_ROOT . DIRECTORY_SEPARATOR . 'public');

// ==========================================
// 🗂️ 核心目录路径定义
// ==========================================

// 后端核心目录
define('SRC_DIR', PROJECT_ROOT . DIRECTORY_SEPARATOR . 'src');
define('API_DIR', SRC_DIR . DIRECTORY_SEPARATOR . 'api');
define('CONFIG_DIR', SRC_DIR . DIRECTORY_SEPARATOR . 'config');
define('INCLUDES_DIR', SRC_DIR . DIRECTORY_SEPARATOR . 'includes');
define('CONSTANTS_DIR', SRC_DIR . DIRECTORY_SEPARATOR . 'constants');

// 前端资源目录
define('ASSETS_DIR', PUBLIC_ROOT . DIRECTORY_SEPARATOR . 'assets');
define('CSS_DIR', ASSETS_DIR . DIRECTORY_SEPARATOR . 'css');
define('JS_DIR', ASSETS_DIR . DIRECTORY_SEPARATOR . 'js');
define('IMAGES_DIR', ASSETS_DIR . DIRECTORY_SEPARATOR . 'images');
define('AUDIO_DIR', ASSETS_DIR . DIRECTORY_SEPARATOR . 'audio');

// 数据相关目录
define('DATABASE_DIR', PROJECT_ROOT . DIRECTORY_SEPARATOR . 'database');
define('DATA_DIR', PROJECT_ROOT . DIRECTORY_SEPARATOR . 'data');
define('SQL_DIR', PROJECT_ROOT . DIRECTORY_SEPARATOR . 'sql');
define('BACKUP_DIR', DATA_DIR . DIRECTORY_SEPARATOR . 'backups');

// 工具和脚本目录
define('SCRIPTS_DIR', PROJECT_ROOT . DIRECTORY_SEPARATOR . 'scripts');
define('TOOLS_DIR', PROJECT_ROOT . DIRECTORY_SEPARATOR . 'tools');
define('BACKEND_DIR', PROJECT_ROOT . DIRECTORY_SEPARATOR . 'backend');
define('ADMIN_DIR', PROJECT_ROOT . DIRECTORY_SEPARATOR . 'admin');

// 日志和文档目录
define('LOGS_DIR', PROJECT_ROOT . DIRECTORY_SEPARATOR . 'logs');
define('DOCS_DIR', PROJECT_ROOT . DIRECTORY_SEPARATOR . 'docs');
define('ARCHIVE_DIR', PROJECT_ROOT . DIRECTORY_SEPARATOR . 'archive');

// ==========================================
// 🌐 Web访问路径定义（相对于Web根目录）
// ==========================================

// API访问路径
define('API_PATH', '/yinian/src/api/');
define('ASSETS_PATH', '/yinian/public/assets/');
define('CSS_PATH', '/yinian/public/assets/css/');
define('JS_PATH', '/yinian/public/assets/js/');
define('IMAGES_PATH', '/yinian/public/assets/images/');
define('AUDIO_PATH', '/yinian/public/assets/audio/');

// ==========================================
// 🗄️ 数据库配置
// ==========================================

// 数据库基本配置
define('DB_HOST', 'localhost');
define('DB_NAME', 'yn_game');
define('DB_USER', 'ynxx');
define('DB_PASS', 'mjlxz159');
define('DB_CHARSET', 'utf8mb4');
define('DB_PORT', 3306);

// 数据库连接选项（不能用define定义数组，改为函数）
function getDbOptions()
{
    return [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
    ];
}

// ==========================================
// 🎯 游戏核心设置
// ==========================================

// 会话设置
define('SESSION_NAME', 'YINIAN_SESSION');
define('SESSION_LIFETIME', 86400); // 24小时
define('SESSION_SECURE', false); // HTTPS环境设为true
define('SESSION_HTTPONLY', true);

// 安全设置
define('CSRF_TOKEN_NAME', 'csrf_token');
define('CSRF_TOKEN_LIFETIME', 3600); // 1小时
define('PASSWORD_MIN_LENGTH', 6);
define('PASSWORD_MAX_LENGTH', 50);

// 文件上传设置
define('UPLOAD_MAX_SIZE', 2 * 1024 * 1024); // 2MB
define('UPLOAD_ALLOWED_TYPES', ['jpg', 'jpeg', 'png', 'gif']);
define('UPLOAD_DIR', DATA_DIR . DIRECTORY_SEPARATOR . 'uploads');

// ==========================================
// 🎮 游戏玩法设置
// ==========================================

// 角色设置
define('MAX_CHARACTERS_PER_USER', 3);
define('CHARACTER_NAME_MIN_LENGTH', 2);
define('CHARACTER_NAME_MAX_LENGTH', 12);

// 战斗设置
define('BATTLE_TIMEOUT', 30); // 战斗超时时间（秒）
define('BATTLE_MAX_ROUNDS', 100); // 最大回合数

// 修炼设置
define('CULTIVATION_COOLDOWN', 300); // 修炼冷却时间（秒）
define('CULTIVATION_MAX_LEVEL', 100);

// 装备设置
define('EQUIPMENT_MAX_LEVEL', 100);
define('EQUIPMENT_QUALITIES', ['普通', '稀有', '史诗', '传说', '神话']);

// 背包设置
define('INVENTORY_DEFAULT_SIZE', 50);
define('INVENTORY_MAX_SIZE', 200);

// ==========================================
// 📊 调试和日志设置
// ==========================================

// 调试设置
define('DEBUG_LEVEL', GAME_DEBUG ? 3 : 0); // 0=关闭, 1=错误, 2=警告, 3=全部
define('DEBUG_LOG_QUERIES', GAME_DEBUG);
define('DEBUG_LOG_API_CALLS', GAME_DEBUG);

// 日志设置
define('LOG_LEVEL', 'INFO'); // DEBUG, INFO, WARNING, ERROR
define('LOG_MAX_SIZE', 10 * 1024 * 1024); // 10MB
define('LOG_MAX_FILES', 5);

// 错误报告设置
if (GAME_DEBUG) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    ini_set('log_errors', 1);
} else {
    error_reporting(E_ERROR | E_WARNING | E_PARSE);
    ini_set('display_errors', 0);
    ini_set('log_errors', 1);
}

// 设置错误日志文件
ini_set('error_log', LOGS_DIR . DIRECTORY_SEPARATOR . 'php_errors.log');

// ==========================================
// 🛠️ 实用函数定义
// ==========================================

/**
 * 获取项目根目录路径
 * @return string
 */
function getProjectRoot()
{
    return PROJECT_ROOT;
}

/**
 * 获取指定目录的绝对路径
 * @param string $dir 目录常量名或路径
 * @return string
 */
function getDir($dir)
{
    // 如果是已定义的常量，直接返回
    if (defined($dir)) {
        return constant($dir);
    }

    // 如果是相对路径，转换为绝对路径
    if (!is_absolute_path($dir)) {
        return PROJECT_ROOT . DIRECTORY_SEPARATOR . $dir;
    }

    return $dir;
}

/**
 * 获取Web访问路径
 * @param string $path 相对路径
 * @return string
 */
function getWebPath($path)
{
    return rtrim($path, '/') . '/';
}

/**
 * 检查路径是否为绝对路径
 * @param string $path
 * @return bool
 */
function is_absolute_path($path)
{
    return (DIRECTORY_SEPARATOR === '/' && $path[0] === '/') ||
        (DIRECTORY_SEPARATOR === '\\' && preg_match('/^[a-zA-Z]:/', $path));
}

/**
 * 安全地创建目录
 * @param string $dir 目录路径
 * @param int $permissions 权限
 * @return bool
 */
function createDirSafely($dir, $permissions = 0755)
{
    if (!is_dir($dir)) {
        return mkdir($dir, $permissions, true);
    }
    return true;
}

/**
 * 获取API文件的完整路径
 * @param string $apiFile API文件名
 * @return string
 */
function getApiPath($apiFile)
{
    return API_DIR . DIRECTORY_SEPARATOR . $apiFile;
}

/**
 * 获取API的Web访问URL
 * @param string $apiFile API文件名
 * @return string
 */
function getApiUrl($apiFile)
{
    return API_PATH . $apiFile;
}

/**
 * 获取配置文件路径
 * @param string $configFile 配置文件名
 * @return string
 */
function getConfigPath($configFile)
{
    return CONFIG_DIR . DIRECTORY_SEPARATOR . $configFile;
}

/**
 * 获取包含文件路径
 * @param string $includeFile 包含文件名
 * @return string
 */
function getIncludePath($includeFile)
{
    return INCLUDES_DIR . DIRECTORY_SEPARATOR . $includeFile;
}

/**
 * 获取静态资源的Web路径
 * @param string $type 资源类型 (css, js, images, audio)
 * @param string $file 文件名
 * @return string
 */
function getAssetUrl($type, $file)
{
    $basePath = ASSETS_PATH;
    switch ($type) {
        case 'css':
            return $basePath . 'css/' . $file;
        case 'js':
            return $basePath . 'js/' . $file;
        case 'images':
            return $basePath . 'images/' . $file;
        case 'audio':
            return $basePath . 'audio/' . $file;
        default:
            return $basePath . $type . '/' . $file;
    }
}

/**
 * 获取日志文件路径
 * @param string $logFile 日志文件名
 * @return string
 */
function getLogPath($logFile)
{
    createDirSafely(LOGS_DIR);
    return LOGS_DIR . DIRECTORY_SEPARATOR . $logFile;
}

/**
 * 写入日志
 * @param string $message 日志消息
 * @param string $level 日志级别
 * @param string $logFile 日志文件名
 */
function writeLog($message, $level = 'INFO', $logFile = 'game.log')
{
    if (!GAME_DEBUG && $level === 'DEBUG') {
        return;
    }

    $logPath = getLogPath($logFile);
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;

    file_put_contents($logPath, $logEntry, FILE_APPEND | LOCK_EX);
}

/**
 * 获取数据库连接 (兼容原有的getDatabase函数)
 * @return PDO|false
 */
function getDatabaseConnection()
{
    try {
        $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET . ";port=" . DB_PORT;
        $pdo = new PDO($dsn, DB_USER, DB_PASS, getDbOptions());
        return $pdo;
    } catch (PDOException $e) {
        writeLog("数据库连接失败: " . $e->getMessage(), 'ERROR', 'database.log');
        return false;
    }
}

/**
 * 获取数据库连接 (向后兼容函数)
 * @return PDO|false
 */
if (!function_exists('getDatabase')) {
    function getDatabase()
    {
        return getDatabaseConnection();
    }
}

/**
 * 检查是否为调试模式
 * @return bool
 */
function isDebugMode()
{
    return GAME_DEBUG;
}

/**
 * 检查是否为维护模式
 * @return bool
 */
function isMaintenanceMode()
{
    return GAME_MAINTENANCE;
}

/**
 * 安全启动会话 - 统一的会话管理
 * @return bool
 */
function safeStartSession()
{
    if (session_status() === PHP_SESSION_NONE) {
        // 设置会话cookie参数
        session_set_cookie_params([
            'lifetime' => 0,
            'path' => '/yinian/',
            'domain' => '',
            'secure' => false,
            'httponly' => true,
            'samesite' => 'Lax'
        ]);
        return session_start();
    }
    return true;
}

/**
 * 获取游戏配置信息
 * @return array
 */
function getGameConfig()
{
    return [
        'name' => GAME_NAME,
        'version' => GAME_VERSION,
        'author' => GAME_AUTHOR,
        'description' => GAME_DESCRIPTION,
        'environment' => GAME_ENV,
        'debug' => GAME_DEBUG,
        'maintenance' => GAME_MAINTENANCE
    ];
}

// ==========================================
// 🚀 初始化设置
// ==========================================

// 确保必要的目录存在
$requiredDirs = [
    LOGS_DIR,
    BACKUP_DIR,
    UPLOAD_DIR
];

foreach ($requiredDirs as $dir) {
    createDirSafely($dir);
}

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 记录配置加载日志
if (GAME_DEBUG) {
    writeLog("一念修仙配置文件已加载 - 版本: " . GAME_VERSION, 'INFO', 'system.log');
}

// ==========================================
// 📝 配置验证
// ==========================================

/**
 * 验证配置是否正确
 * @return array 验证结果
 */
function validateConfig()
{
    $errors = [];
    $warnings = [];

    // 检查必要目录
    $requiredDirs = [
        'SRC_DIR' => SRC_DIR,
        'API_DIR' => API_DIR,
        'CONFIG_DIR' => CONFIG_DIR,
        'PUBLIC_ROOT' => PUBLIC_ROOT
    ];

    foreach ($requiredDirs as $name => $dir) {
        if (!is_dir($dir)) {
            $errors[] = "必要目录不存在: {$name} ({$dir})";
        }
    }

    // 检查数据库配置
    if (empty(DB_HOST) || empty(DB_NAME) || empty(DB_USER)) {
        $errors[] = "数据库配置不完整";
    }

    // 检查权限
    if (!is_writable(LOGS_DIR)) {
        $warnings[] = "日志目录不可写: " . LOGS_DIR;
    }

    return [
        'valid' => empty($errors),
        'errors' => $errors,
        'warnings' => $warnings
    ];
}

// 如果是调试模式，自动验证配置
if (GAME_DEBUG) {
    $validation = validateConfig();
    if (!$validation['valid']) {
        writeLog("配置验证失败: " . implode(', ', $validation['errors']), 'ERROR', 'system.log');
    }
    if (!empty($validation['warnings'])) {
        writeLog("配置警告: " . implode(', ', $validation['warnings']), 'WARNING', 'system.log');
    }
}
