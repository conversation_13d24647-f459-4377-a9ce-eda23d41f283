<?php
// 确保没有之前的输出
ob_start();

// 显示所有错误
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置响应头
header('Content-Type: application/json');

// 只在需要时启动session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// 数据库连接配置 - 使用主游戏数据库
$db_config = [
    'host' => 'localhost',
    'username' => 'ynxx',
    'password' => 'mjlxz159',
    'database' => 'yn_game'
];

// 建立数据库连接
function getConnection() {
    global $db_config;
    try {
        $conn = new PDO(
            "mysql:host={$db_config['host']};dbname={$db_config['database']};charset=utf8mb4",
            $db_config['username'],
            $db_config['password']
        );
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $conn;
    } catch(PDOException $e) {
        response(['success' => false, 'message' => '数据库连接失败: ' . $e->getMessage()]);
        exit;
    }
}

// 统一响应函数
function response($data) {
    // 清除之前的输出
    if (ob_get_length()) ob_clean();
    
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

// 获取操作类型
$action = isset($_GET['action']) ? $_GET['action'] : 'login';

// 记录请求数据
error_log("Request data: " . file_get_contents('php://input'));

switch($action) {
    case 'login':
        handleLogin();
        break;
    case 'logout':
        handleLogout();
        break;
    default:
        response(['success' => false, 'message' => '未知的操作类型']);
}

// 处理登录
function handleLogin() {
    try {
        $input = file_get_contents('php://input');
        error_log("Raw input: " . $input);
        
        $data = json_decode($input, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("JSON decode error: " . json_last_error_msg());
            response(['success' => false, 'message' => 'JSON解析错误: ' . json_last_error_msg()]);
        }
        
        $username = isset($data['username']) ? $data['username'] : '';
        $password = isset($data['password']) ? $data['password'] : '';

        error_log("Login attempt for username: " . $username);

        if (empty($username) || empty($password)) {
            response(['success' => false, 'message' => '用户名和密码不能为空']);
        }

        $conn = getConnection();
        $stmt = $conn->prepare("SELECT * FROM admin_users WHERE username = ?");
        $stmt->execute([$username]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        error_log("User found: " . ($user ? 'yes' : 'no'));
        
        if ($user && password_verify($password, $user['password'])) {
            // 更新最后登录时间
            $stmt = $conn->prepare("UPDATE admin_users SET last_login = NOW() WHERE id = ?");
            $stmt->execute([$user['id']]);
            
            // 记录登录日志
            $stmt = $conn->prepare("INSERT INTO admin_logs (admin_id, action, target_type, target_id, details, ip) VALUES (?, 'login', 'system', 0, ?, ?)");
            $stmt->execute([
                $user['id'],
                json_encode(['username' => $username]),
                $_SERVER['REMOTE_ADDR']
            ]);
            
            // 设置session
            $_SESSION['admin_id'] = $user['id'];
            $_SESSION['admin_username'] = $user['username'];
            $_SESSION['admin_role'] = $user['role'];
            
            error_log("Login successful for user: " . $username);
            response(['success' => true, 'message' => '登录成功']);
        } else {
            error_log("Login failed for user: " . $username . " (Invalid credentials)");
            response(['success' => false, 'message' => '用户名或密码错误']);
        }
    } catch(PDOException $e) {
        error_log("Database error: " . $e->getMessage());
        response(['success' => false, 'message' => '登录失败: ' . $e->getMessage()]);
    } catch(Exception $e) {
        error_log("General error: " . $e->getMessage());
        response(['success' => false, 'message' => '系统错误: ' . $e->getMessage()]);
    }
}

// 处理退出
function handleLogout() {
    try {
        if (isset($_SESSION['admin_id'])) {
            // 记录退出日志
            $conn = getConnection();
            $stmt = $conn->prepare("INSERT INTO admin_logs (admin_id, action, target_type, target_id, details, ip) VALUES (?, 'logout', 'system', 0, ?, ?)");
            $stmt->execute([
                $_SESSION['admin_id'],
                json_encode(['username' => $_SESSION['admin_username']]),
                $_SERVER['REMOTE_ADDR']
            ]);
        }
        
        // 清除session
        session_destroy();
        
        response(['success' => true, 'message' => '退出成功']);
    } catch(PDOException $e) {
        error_log("Logout error: " . $e->getMessage());
        response(['success' => false, 'message' => '退出失败: ' . $e->getMessage()]);
    }
} 