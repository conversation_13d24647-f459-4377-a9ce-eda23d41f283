// 🔧 Character类已移动到 core/character.js 文件中

class BattleSystem {
    constructor() {
        // 检查必要的依赖
        if (typeof BattleStateMachine === 'undefined') {
            throw new Error('BattleStateMachine未加载，无法初始化战斗系统');
        }

        // 初始化基本属性
        this.isInitialized = false;
        this.isInitializing = false;

        // 初始化技能名称
        this.skillNames = {
            feijian: '剑气外放！',
            wanjianjue: '万剑诀！',
            zhangxinlei: '掌心雷！',
            huoqiushu: '火球术！',
            jujian: '巨剑术！',
            normal: '普通攻击！',
            putong: '普通攻击！',
            // 🔧 新增：更多技能映射
            xuanbingjian: '玄冰剑！',
            huoliuxing: '火流星！',
            tengman: '藤蔓！',
            yanshituci: '岩石突刺！',
            fengrensu: '风刃术！',
            bingzhuishu: '冰锥术！',
        };

        // 敌人技能名称
        this.enemySkillNames = [
            '妖爪撕裂！',
            '魔焰冲击！',
            '毒牙突刺！',
            '暗影斩击！',
            '雷霆一击！',
            '寒冰刺骨！',
            '烈火焚身！',
            '狂风呼啸！',
        ];

        // 初始化DOM元素
        this.battleContainer = document.querySelector('.battle-container');
        this.effectsContainer = document.querySelector('.effects-container');

        // 初始化战斗状态
        this.attackCount = 0;
        this.isGameOver = false;
        this.battleStartTime = null;
        this.battleEndTime = null;
        this.enemyBerserkMode = false; // 🔥 新增：敌人暴走状态

        // 🔧 挂机状态将由挂机管理器管理，这里只保留兼容性属性
        // 实际状态从挂机管理器获取

        // 创建effectsContainer（如果不存在）
        if (!this.effectsContainer && this.battleContainer) {
            this.effectsContainer = document.createElement('div');
            this.effectsContainer.className = 'effects-container';
            this.battleContainer.appendChild(this.effectsContainer);
        }

        // 🔥 新增：初始化数据管理器（必须最先初始化，其他管理器依赖它）
        this.dataManager = new BattleDataManager();

        // 直接创建状态机实例
        this.stateMachine = new BattleStateMachine(this);

        // 初始化UI管理器
        this.uiManager = new BattleUIManager(this);

        // 🔥 新增：初始化战斗流程管理器
        this.battleFlowManager = new BattleFlowManager(this);

        // 在现有的管理器初始化代码后添加
        this.rewardManager = new BattleRewardManager(this);
        this.victoryPanelManager = new BattleVictoryPanelManager(this);

        // 🔥 新增：初始化挂机系统管理器
        this.autoBattleManager = new BattleAutoBattleManager(this);

        console.log('🤖 挂机管理器已创建');
        console.log('BattleSystem构造完成');
        console.log('DOM元素状态:', {
            battleContainer: !!this.battleContainer,
            effectsContainer: !!this.effectsContainer,
        });
    }

    // 🔧 新增：敌人套装攻击特殊效果处理
    applyEnemySetAttackEffects(baseDamage) {
        if (!this.enemy.set_special_effects || this.enemy.set_special_effects.length === 0) {
            return { damage: baseDamage, ignoreDefense: false, extraAttacks: 0 };
        }

        let modifiedDamage = baseDamage;
        let ignoreDefense = false;
        let extraAttacks = 0;
        const triggeredEffects = [];

        for (const setEffect of this.enemy.set_special_effects) {
            const effect = setEffect.effect;

            // 🗡️ 攻击类效果处理

            // 暴击伤害处理
            if (effect.includes('暴击伤害') || effect.includes('必杀')) {
                const percentMatch = effect.match(/(\d+)%/);
                if (percentMatch && Math.random() < 0.15) {
                    // 15%触发概率
                    const multiplier = parseInt(percentMatch[1]) / 100;
                    modifiedDamage = Math.floor(modifiedDamage * (1 + multiplier));
                    triggeredEffects.push(`敌人${setEffect.set_name}暴击！`);
                }
            }

            // 连击处理
            if (effect.includes('连击') && Math.random() < 0.12) {
                // 12%触发概率
                extraAttacks = 1;
                triggeredEffects.push(`敌人${setEffect.set_name}连击！`);
            }

            // 破甲处理
            if ((effect.includes('破甲') || effect.includes('无视防御')) && Math.random() < 0.1) {
                // 10%触发概率
                ignoreDefense = true;
                triggeredEffects.push(`敌人${setEffect.set_name}破甲！`);
            }

            // 吸血处理
            if (effect.includes('吸血') && Math.random() < 0.15) {
                // 15%触发概率
                const percentMatch = effect.match(/(\d+)%/);
                if (percentMatch) {
                    const healPercent = parseInt(percentMatch[1]) / 100;
                    const healAmount = Math.floor(modifiedDamage * healPercent);
                    this.enemy.currentHp = Math.min(
                        this.enemy.max_hp,
                        this.enemy.currentHp + healAmount
                    );
                    triggeredEffects.push(
                        `敌人${setEffect.set_name}吸血回复${healAmount}点生命值！`
                    );
                }
            }
        }

        // 显示触发的效果
        triggeredEffects.forEach(effect => this.showSetEffectMessage(effect));

        console.log('🔥 敌人套装攻击效果:', {
            baseDamage,
            modifiedDamage,
            ignoreDefense,
            extraAttacks,
            setEffectsApplied: this.enemy.set_special_effects?.length || 0,
            triggeredEffects,
        });

        return { damage: modifiedDamage, ignoreDefense, extraAttacks };
    }

    // 🔧 新增：敌人套装防御特殊效果处理
    applyEnemySetDefenseEffects(incomingDamage) {
        if (!this.enemy.set_special_effects || this.enemy.set_special_effects.length === 0) {
            return { damage: incomingDamage, counterDamage: 0 };
        }

        let modifiedDamage = incomingDamage;
        let counterDamage = 0;
        const triggeredEffects = [];

        for (const setEffect of this.enemy.set_special_effects) {
            const effect = setEffect.effect;

            // 🛡️ 防御类效果处理

            // 格挡 / 减少伤害
            if (effect.includes('格挡') || (effect.includes('减少') && effect.includes('伤害'))) {
                const percentMatch = effect.match(/(\d+)%/);
                if (percentMatch && Math.random() < 0.2) {
                    // 20%触发概率
                    const reduction = parseInt(percentMatch[1]) / 100;
                    modifiedDamage = Math.floor(modifiedDamage * (1 - reduction));
                    triggeredEffects.push(`敌人${setEffect.set_name}格挡！`);
                }
            }

            // 反击
            if (effect.includes('反击') && Math.random() < 0.15) {
                // 15%触发概率
                const percentMatch = effect.match(/(\d+)%/);
                if (percentMatch) {
                    const counterPercent = parseInt(percentMatch[1]) / 100;
                    counterDamage = Math.floor(incomingDamage * counterPercent);
                } else {
                    counterDamage = Math.floor(this.enemy.attack * 0.5); // 默认50%攻击力反击
                }
                triggeredEffects.push(`敌人${setEffect.set_name}反击！`);
            }

            // 免疫 / 无效化
            if (effect.includes('免疫') && Math.random() < 0.08) {
                // 8%触发概率
                modifiedDamage = 0;
                triggeredEffects.push(`敌人${setEffect.set_name}免疫伤害！`);
            }

            // 荆棘 / 伤害反弹
            if (effect.includes('荆棘') || effect.includes('反弹')) {
                const percentMatch = effect.match(/(\d+)%/);
                if (percentMatch && Math.random() < 0.25) {
                    // 25%触发概率
                    const reflectPercent = parseInt(percentMatch[1]) / 100;
                    const reflectDamage = Math.floor(incomingDamage * reflectPercent);
                    counterDamage += reflectDamage;
                    triggeredEffects.push(
                        `敌人${setEffect.set_name}荆棘反弹${reflectDamage}点伤害！`
                    );
                }
            }
        }

        // 显示触发的效果
        triggeredEffects.forEach(effect => this.showSetEffectMessage(effect));

        console.log('🛡️ 敌人套装防御效果:', {
            incomingDamage,
            modifiedDamage,
            counterDamage,
            setEffectsApplied: this.enemy.set_special_effects?.length || 0,
            triggeredEffects,
        });

        return { damage: modifiedDamage, counterDamage };
    }

    // 重定向到状态机的方法
    async initialize() {
        console.log('🔄 调用状态机 initialize 方法');
        return await this.stateMachine.initialize();
    }

    async initializeBattle() {
        console.log('🔄 调用状态机 initializeBattle 方法');
        return await this.stateMachine.initializeBattle();
    }

    async gameOver(message) {
        // 🔧 修复：防止重复调用gameOver方法
        if (this.isGameOver) {
            console.log('游戏已经结束，忽略重复调用');
            return;
        }

        // 🔧 新增：设置胜负标记供竞技场结算使用
        this.isPlayerVictory = message.includes('胜利');
        this.battleEnded = true;

        console.log('🔄 调用状态机 gameOver 方法:', message);
        console.log('🏆 设置战斗结果:', this.isPlayerVictory ? '胜利' : '失败');

        await this.stateMachine.gameOver(message);
        // 🔧 修复：调用实际的游戏结束处理逻辑
        return await this.handleGameOver(message);
    }

    async handleBattleEnd(isVictory, droppedItems = []) {
        try {
            // 显示战斗结果面板
            await this.showBattleResultPanel(isVictory, droppedItems);

            // 处理挂机模式 - 使用挂机管理器的状态
            if (this.autoBattleManager.isInAutoBattleMode()) {
                await this.autoBattleManager.handleAutoBattleContinuation(isVictory);
            }
        } catch (error) {
            console.error('战斗结束处理出错:', error);
            this.uiManager.showBattleMessage('战斗结束处理出现错误，请刷新页面重试', 'error');
        }
    }

    async showBattleResultPanel(isVictory, droppedItems) {
        const message = isVictory ? '🎉 玩家胜利！战斗结束！' : '💀 战斗失败！';
        await this.showVictoryPanel(message, isVictory ? droppedItems : []);
    }

    // 🔥 挂机管理器代理方法
    getAutoBattleElements() {
        console.log('🔄 调用挂机管理器 getAutoBattleElements 方法');
        return this.autoBattleManager.getAutoBattleElements();
    }

    // 🗑️ 已删除：不必要的代理方法，直接使用battleFlowManager.autoBattle()

    stopAutoBattle() {
        console.log('🔄 调用挂机管理器 stopAutoBattle 方法');
        return this.autoBattleManager.stopAutoBattle();
    }

    // 更新区域信息显示
    updateAreaInfo() {
        this.uiManager.updateAreaInfo();
    }
    // 代理方法：将调用转发到UI管理器
    updateBattleStatus(message) {
        this.uiManager.updateBattleStatus(message);
    }

    updateWeaponDisplay() {
        this.uiManager.updateWeaponDisplay();
    }

    updateCurrentSkill(skillName) {
        this.uiManager.updateCurrentSkill(skillName);
    }

    highlightCurrentWeapon(slotIndex) {
        this.uiManager.highlightCurrentWeapon(slotIndex);
    }

    showBattleMessage(message, type = 'info') {
        this.uiManager.showBattleMessage(message, type);
    }

    fixImagePath(imagePath) {
        return this.uiManager.fixImagePath(imagePath);
    }

    // 🔥 新增：战斗流程管理器代理方法
    async autoBattle() {
        console.log('🔄 调用战斗流程管理器 autoBattle 方法');
        return await this.battleFlowManager.autoBattle();
    }

    async enemyCounter() {
        console.log('🔄 调用战斗流程管理器 enemyCounter 方法');
        return await this.battleFlowManager.enemyCounter();
    }

    async showSkillShout(skillName, isEnemy = false) {
        console.log('=== 显示技能喊话 ===');
        console.log('技能名称:', skillName);
        console.log('是否敌人:', isEnemy);
        console.log('effectsContainer存在:', !!this.effectsContainer);
        console.log('battleContainer存在:', !!this.battleContainer);

        // 🔥 新增：清理之前的喊话文字，防止叠加
        const existingShouts = this.effectsContainer.querySelectorAll('.skill-shout');
        existingShouts.forEach(shout => {
            shout.remove();
            console.log('清理旧喊话元素');
        });

        // 检查必要的DOM元素
        if (!this.effectsContainer) {
            console.error('effectsContainer不存在，无法显示技能喊话');
            return;
        }

        if (!this.battleContainer) {
            console.error('battleContainer不存在，无法定位技能喊话');
            return;
        }

        const characterElement = document.querySelector(isEnemy ? '.enemy' : '.player');
        if (!characterElement) {
            console.error(`${isEnemy ? '敌人' : '玩家'}元素不存在，无法定位技能喊话`);
            return;
        }

        const shout = document.createElement('div');
        shout.className = 'skill-shout';

        // 🔧 修复：支持传入技能名称或动画类型
        let shoutText;
        if (!isEnemy && this.skillNames[skillName]) {
            // 玩家技能：如果是动画类型代码（如'wanjianjue'），直接使用映射
            shoutText = this.skillNames[skillName];
        } else if (!isEnemy) {
            // 玩家技能：如果是技能名称（如'万剑诀'），直接使用并添加感叹号
            shoutText = skillName + '！';
        } else {
            // 敌人技能：直接使用传入的技能名称
            shoutText = skillName;
        }

        shout.textContent = shoutText;
        console.log('喊话文本:', shoutText);

        // 获取位置信息
        const characterRect = characterElement.getBoundingClientRect();
        const containerRect = this.battleContainer.getBoundingClientRect();

        console.log(`${isEnemy ? '敌人' : '玩家'}位置:`, characterRect);
        console.log('容器位置:', containerRect);

        const leftPos = characterRect.left - containerRect.left + characterRect.width / 2 - 50;
        const topPos = characterRect.top - containerRect.top - 20; // 稍微向上偏移

        shout.style.left = `${leftPos}px`;
        shout.style.top = `${topPos}px`;

        // 🔧 新增：敌人喊话使用不同颜色
        if (isEnemy) {
            shout.style.color = '#ff6b6b';
            shout.style.textShadow = '2px 2px 4px rgba(255, 0, 0, 0.5)';
        }

        console.log('喊话位置:', { left: leftPos, top: topPos });

        // 添加到效果容器
        this.effectsContainer.appendChild(shout);
        console.log('喊话元素已添加到DOM');

        // 缩短喊话持续时间
        setTimeout(() => {
            if (shout && shout.parentNode) {
                shout.remove();
                console.log('喊话元素已移除');
            }
        }, 800);
    }

    getCurrentWeaponImage(skillData) {
        console.log('🔄 调用战斗流程管理器 getCurrentWeaponImage 方法');
        return this.battleFlowManager.getCurrentWeaponImage(skillData);
    }

    async performSkillDamageCalculation(currentSkillData) {
        console.log('🔄 调用战斗流程管理器 performSkillDamageCalculation 方法');
        return await this.battleFlowManager.performSkillDamageCalculation(currentSkillData);
    }

    // 奖励管理器代理方法
    async saveVictoryResult(droppedItems) {
        console.log('🔄 调用奖励管理器 saveVictoryResult 方法');
        const result = await this.rewardManager.saveVictoryResult(droppedItems);
        this.lastSaveResult = result; // 保存结果供其他方法使用
        return result;
    }

    createRewardsSection(gameOverlay, spiritStoneGained, goldGained) {
        console.log('🔄 调用奖励管理器 createRewardsSection 方法');
        return this.rewardManager.createRewardsSection(gameOverlay, spiritStoneGained, goldGained);
    }

    async constructItemDetailFromDrop(dropData) {
        console.log('🔄 调用奖励管理器 constructItemDetailFromDrop 方法');
        return await this.rewardManager.constructItemDetailFromDrop(dropData);
    }

    addRecycledOverlay() {
        console.log('🔄 调用奖励管理器 addRecycledOverlay 方法');
        return this.rewardManager.addRecycledOverlay();
    }

    // 胜利面板管理器代理方法
    async showVictoryPanel(message, droppedItems) {
        console.log('🔄 调用胜利面板管理器 showVictoryPanel 方法');
        return await this.victoryPanelManager.showVictoryPanel(message, droppedItems);
    }

    async showDropItemDetail(dropData) {
        console.log('🔄 调用胜利面板管理器 showDropItemDetail 方法');
        return await this.victoryPanelManager.showDropItemDetail(dropData);
    }

    createSimpleVictoryPanel(message, droppedItems) {
        console.log('🔄 调用胜利面板管理器 createSimpleVictoryPanel 方法');
        return this.victoryPanelManager.createSimpleVictoryPanel(message, droppedItems);
    }

    continueNextStage() {
        console.log('🔄 调用胜利面板管理器 continueNextStage 方法');
        return this.victoryPanelManager.continueNextStage();
    }

    // 🗑️ 已删除：不必要的代理方法，直接使用victoryPanelManager的方法

    async handleGameOver(message) {
        // 注意：isGameOver标志位已经在状态机中设置，这里不再检查

        console.log('游戏结束:', message);
        // 注意：isGameOver标志位已经在状态机中设置

        // 🔧 修复：清除任何可能的定时器，确保战斗完全停止
        if (this.battleTimeout) {
            clearTimeout(this.battleTimeout);
            this.battleTimeout = null;
        }

        let droppedItems = [];

        // 🔧 修复：正确调用武器耐久度损耗处理，传递技能序列和战斗结果
        await this.handleWeaponDurabilityLoss(message);

        if (message.includes('玩家胜利')) {
            console.log('玩家胜利，计算掉落物品...');
            // 🔧 修复：不再在战斗场景中显示掉落物品，直接获取掉落数据
            droppedItems = await this.dataManager.calculateBattleDrops(true);

            // 🔧 修复：胜利时总是保存战斗结果，不管是否有掉落物品
            console.log('保存战斗胜利结果...');

            // 🔧 处理掉落数据格式
            const itemsData = droppedItems
                ? droppedItems.map(item => {
                      const itemData = {
                          name: item.name || '未知物品',
                          type: item.type || 'material',
                          rarity: item.rarity || item.quality || 'common', // 🔧 修复：优先使用rarity字段
                          quantity: parseInt(item.quantity) || 1,
                          sell_price: parseInt(item.sell_price) || 0,
                          description: item.description || '暂无描述', // 🔧 修复：添加缺失的描述字段
                          item_data: item.item_data || {},
                      };
                      console.log('处理物品数据:', itemData);
                      return itemData;
                  })
                : []; // 🔧 修复：如果没有掉落物品，传递空数组

            // 🔧 修复：总是调用saveVictoryResult，确保奖励被保存和返回
            const saveResult = await this.saveVictoryResult(itemsData);
            if (saveResult.success) {
                console.log('✅ 战斗结果已保存:', saveResult);

                // 🔧 新增：保存API返回结果供胜利面板使用
                this.lastSaveResult = saveResult;
                // 🔧 修复BUG：直接使用后端返回的完整掉落物品数据
                // 避免从背包API查询旧的掉落物品造成的误导
                let droppedItems = [];

                if (saveResult.all_dropped_items && Array.isArray(saveResult.all_dropped_items)) {
                    droppedItems = saveResult.all_dropped_items;
                    console.log('✅ 使用后端返回的完整战利品数据:', droppedItems);
                    console.log(
                        `📊 本次战斗统计: 总计${saveResult.total_dropped_count || 0}件，已保存${
                            saveResult.saved_count || 0
                        }件，已回收${saveResult.recycled_count || 0}件`
                    );

                    // 检查是否有回收物品
                    const recycledItems = droppedItems.filter(item => item.recycled === true);
                    if (recycledItems.length > 0) {
                        console.log(
                            '♻️ 检测到回收物品:',
                            recycledItems.map(item => item.name)
                        );
                    }
                } else {
                    console.log('⚠️ 后端未返回完整掉落数据，使用原始数据');
                    droppedItems = itemsData;
                }

                console.log('✅ 奖励数据已保存到lastSaveResult:', {
                    exp_gained: saveResult.exp_gained,
                    gold_gained: saveResult.gold_gained,
                    spiritual_power_gained: saveResult.spiritual_power_gained,
                    silver_gained: saveResult.silver_gained,
                    has_recycled_items: saveResult.has_recycled_items,
                    recycled_items: saveResult.recycled_items,
                    recycle_gold: saveResult.recycle_gold,
                    total_gold_gained: saveResult.total_gold_gained,
                });
            } else {
                console.error('❌ 保存战斗结果失败:', saveResult.message);
                // 🔧 修复：即使保存失败，也要设置基础奖励数据
                const enemyData = this.dataManager.enemyData;
                const spiritStoneGained = enemyData.spiritStoneReward || 10;
                const goldGained = enemyData.goldReward || 5;

                this.lastSaveResult = {
                    success: false,
                    exp_gained: spiritStoneGained,
                    gold_gained: goldGained,
                    spiritual_power_gained: spiritStoneGained,
                    silver_gained: goldGained,
                    message: '保存失败，但显示计算的奖励',
                };

                console.log('⚠️ 使用计算的奖励数据:', this.lastSaveResult);
            }
        } else {
            // 🆕 修改：战斗失败不自动回退，等待用户手动操作
            console.log('🔻 战斗失败，保持当前层数不变，等待用户手动操作');

            // 设置一个简单的失败结果，不包含进度变化
            this.lastSaveResult = {
                success: true,
                exp_gained: 0,
                gold_gained: 0,
                spiritual_power_gained: 0,
                silver_gained: 0,
                message: '战斗失败，等待用户操作',
            };

            // 设置失败时的奖励数据（通常为0）
            if (!this.lastSaveResult) {
                this.lastSaveResult = {
                    success: true,
                    exp_gained: 0,
                    gold_gained: 0,
                    spiritual_power_gained: 0,
                    silver_gained: 0,
                    message: '战斗失败，无奖励',
                };
            }
        }

        // 🔧 修复：立即显示胜利面板，不再等待掉落动画
        await this.showVictoryPanel(message, droppedItems);
        document.querySelector('.battle-status').textContent = `战斗结束 - ${message}`;

        // 🎯 修复：使用挂机管理器检查和恢复挂机状态
        const isAutoBattleMode = this.autoBattleManager.checkAndRestoreAutoBattleState();

        // 🆕 处理挂机模式的战斗结果
        if (isAutoBattleMode) {
            console.log('🤖 检测到挂机模式，处理战斗结果');
            const isVictory = message.includes('玩家胜利');
            await this.autoBattleManager.handleAutoBattleContinuation(isVictory);
            console.log('🤖 挂机模式战斗结果处理完成，胜利面板将处理后续挂机逻辑');
        } else {
            console.log('🤖 非挂机模式，等待用户操作');
        }
    }

    // 🔧 新增：安全的整数解析方法 - 已迁移到 BattleDataUtils.safeParseInt()
    safeParseInt(value) {
        return BattleDataUtils.safeParseInt(value);
    }

    // 🔧 新增：安全的浮点数解析方法 - 已迁移到 BattleDataUtils.safeParseFloat()
    safeParseFloat(value) {
        return BattleDataUtils.safeParseFloat(value);
    }

    // 🔧 新增：根据品质获取属性倍率 - 已迁移到 BattleDataUtils.getRarityMultiplier()
    getRarityMultiplier(rarity) {
        return BattleDataUtils.getRarityMultiplier(rarity);
    }

    // 🔧 新增：保存单个物品到背包 - 已迁移到 BattleItemUtils.saveSingleItem()
    saveSingleItem(itemData) {
        return BattleItemUtils.saveSingleItem(itemData);
    }

    // 🔧 新增：显示更多物品信息 - 已迁移到 BattleItemUtils.showMoreItemInfo()
    showMoreItemInfo(itemData) {
        return BattleItemUtils.showMoreItemInfo(itemData);
    }

    // 创建飞剑击中特效 - 已迁移到 BattleEffectUtils.createSwordHitEffect()
    createSwordHitEffect(x, y, isPlayerAttack) {
        return BattleEffectUtils.createSwordHitEffect(x, y, isPlayerAttack, this.effectsContainer);
    }

    // 🔧 新增：处理武器耐久损耗 - 已迁移到 BattleWeaponUtils.handleWeaponDurabilityLoss()
    async handleWeaponDurabilityLoss(gameResult) {
        return await BattleWeaponUtils.handleWeaponDurabilityLoss(this.skillSequence, gameResult);
    }

    // 🔧 已迁移到 BattleFlowManager.performSkillDamageCalculation()
    async performSkillDamageCalculation(currentSkillData) {
        console.log('🔄 调用战斗流程管理器 performSkillDamageCalculation 方法');
        return await this.battleFlowManager.performSkillDamageCalculation(currentSkillData);
    }

    /**
     * 🔋 更新玩家MP条显示
     */
    updateMpBar() {
        try {
            const mpBar = document.querySelector('.character.player .mp-bar');
            if (!mpBar) {
                console.warn('⚠️ 未找到玩家MP条元素');
                return;
            }

            const mpFill = mpBar.querySelector('.mp-fill');
            const mpText = mpBar.querySelector('.mp-text');
            const playerMpElement = mpBar.querySelector('#player-mp');
            const playerMaxMpElement = mpBar.querySelector('#player-max-mp');

            if (mpFill && this.playerStats.currentMp !== undefined && this.playerStats.mp_bonus) {
                const mpPercentage = Math.max(
                    0,
                    Math.min(100, (this.playerStats.currentMp / this.playerStats.mp_bonus) * 100)
                );
                mpFill.style.width = `${mpPercentage}%`;

                // 根据MP量变化MP条颜色
                if (mpPercentage > 50) {
                    mpFill.style.backgroundColor = '#2196F3'; // 蓝色
                } else if (mpPercentage > 25) {
                    mpFill.style.backgroundColor = '#9C27B0'; // 紫色
                } else {
                    mpFill.style.backgroundColor = '#607D8B'; // 深灰色
                }
            }

            if (playerMpElement) {
                playerMpElement.textContent = Math.max(
                    0,
                    Math.floor(this.playerStats.currentMp || 0)
                );
            }

            if (playerMaxMpElement) {
                playerMaxMpElement.textContent = Math.floor(this.playerStats.mp_bonus || 100);
            }
        } catch (error) {
            console.error('更新MP条时出错:', error);
        }
    }

    /**
     * 🔋 显示恢复效果
     */
    showRegenEffects(playerHp, playerMp, enemyHp, enemyMp) {
        // 玩家恢复效果
        if (playerHp > 0 || playerMp > 0) {
            const playerPos = this.getCharacterPosition(true);
            this.createRegenEffect(
                playerPos.x,
                playerPos.y,
                `+${playerHp}HP +${playerMp}MP`,
                '#4CAF50',
                true
            );
        }

        // 敌人恢复效果
        if (enemyHp > 0 || enemyMp > 0) {
            const enemyPos = this.getCharacterPosition(false);
            this.createRegenEffect(
                enemyPos.x,
                enemyPos.y,
                `+${enemyHp}HP +${enemyMp}MP`,
                '#FF9800',
                false
            );
        }
    }

    /**
     * 🔋 创建恢复效果动画
     */
    createRegenEffect(x, y, text, color, isPlayer) {
        const effect = document.createElement('div');
        effect.className = 'regen-effect';
        effect.textContent = text;
        effect.style.cssText = `
            position: absolute;
            left: ${x}px;
            top: ${y - 20}px;
            color: ${color};
            font-weight: bold;
            font-size: 14px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            pointer-events: none;
            z-index: 1000;
            transform: translate(-50%, -50%);
            animation: regenFloat 2s ease-out forwards;
        `;

        // 添加CSS动画（如果还没有的话）
        if (!document.querySelector('#regen-animation-styles')) {
            const style = document.createElement('style');
            style.id = 'regen-animation-styles';
            style.textContent = `
                @keyframes regenFloat {
                    0% { opacity: 1; transform: translate(-50%, -50%) scale(0.8); }
                    50% { opacity: 1; transform: translate(-50%, -80px) scale(1.0); }
                    100% { opacity: 0; transform: translate(-50%, -120px) scale(0.6); }
                }
            `;
            document.head.appendChild(style);
        }

        this.effectsContainer.appendChild(effect);

        // 2秒后清理
        setTimeout(() => {
            if (effect && effect.parentNode) {
                effect.parentNode.removeChild(effect);
            }
        }, 2000);
    }
}

// 🔧 删除重复的实例创建 - 实例化逻辑已移至battle.html
