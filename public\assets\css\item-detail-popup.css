/* 物品详情弹窗样式 - 简洁圆角设计 */
.item-detail-popup {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.85);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    backdrop-filter: blur(8px);
    /* iOS兼容性修复 */
    -webkit-backdrop-filter: blur(8px);
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    width: 100vw;
    height: 100vh;
    overflow: hidden;
}

/* 弹窗容器 - 为外部按钮预留空间 */
.item-detail-popup .popup-container {
    position: relative;
    width: 95%;
    max-width: 420px;
    /* iOS导航栏遮挡修复 */
    margin-top: 0;
    margin-bottom: 0;
}

.item-detail-popup .popup-content-area {
    background: linear-gradient(145deg, #2c3e50, #34495e);
    border-radius: 20px;
    width: 100%;
    /* 为外部按钮预留60px高度 */
    max-height: calc(95vh - 80px);
    overflow: hidden;
    position: relative;
    border: 2px solid #d4af37;
    box-shadow: 
        0 15px 35px rgba(0, 0, 0, 0.7),
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        0 0 50px rgba(212, 175, 55, 0.2);
    padding: 0;
    /* 下边距为按钮区域预留空间 */
    margin-bottom: 60px;
}

/* 内容区域 */
.popup-content-inner {
    padding: 16px 32px 20px 32px !important;
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.05), rgba(26, 35, 50, 0.2)) !important;
    overflow-y: auto !important;
    max-height: calc(95vh - 140px) !important;
    box-sizing: border-box !important;
}

/* 标题样式 - 紧凑版 */
.popup-content-area h3 {
    color: #d4af37 !important;
    margin: 0 0 12px 0 !important;
    font-size: 18px !important;
    text-align: center !important;
    text-shadow: 0 0 15px rgba(212, 175, 55, 0.8) !important;
    font-weight: bold !important;
    letter-spacing: 1px;
    position: relative;
    padding: 8px 0;
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.1), rgba(212, 175, 55, 0.05));
    border-radius: 12px;
    border: 1px solid rgba(212, 175, 55, 0.3);
}

/* 图片样式 - 稍小 */
.popup-content-area img {
    width: 70px !important;
    height: 70px !important;
    border-radius: 12px !important;
    border: 3px solid #d4af37 !important;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.5), rgba(212, 175, 55, 0.1)) !important;
    object-fit: cover !important;
    box-shadow: 
        0 8px 20px rgba(0, 0, 0, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
    margin: 0 auto 12px auto !important;
    display: block !important;
}

/* 描述文字 - 紧凑版 */
.popup-content-area p {
    color: #bdc3c7 !important;
    font-size: 13px !important;
    margin: 0 0 12px 0 !important;
    line-height: 1.3 !important;
    text-align: center !important;
    font-style: italic !important;
    background: rgba(0, 0, 0, 0.2);
    padding: 8px 12px;
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 主要信息面板 - 紧凑版 */
.popup-content-area > div:first-child > div:nth-child(4),
.popup-content-area > div:first-child > div:nth-child(5),
.popup-content-area > div:first-child > div:nth-child(6),
.popup-content-area > div:first-child > div:nth-child(7) {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.3), rgba(26, 35, 50, 0.6)) !important;
    padding: 12px 16px !important;
    border-radius: 12px !important;
    margin: 10px 0 !important;
    border: 1px solid rgba(212, 175, 55, 0.2) !important;
    box-shadow: 
        inset 0 1px 0 rgba(255, 255, 255, 0.05),
        0 4px 12px rgba(0, 0, 0, 0.3);
}


.popup-content-area div[style*="margin: 6px 0"]:hover {
    background: none !important;
    transform: none !important;
}

/* 耐久度面板 - 极简版 */
.popup-content-area div[style*="耐久度"] {
    background: none !important;
    padding: 0 !important;
    border-radius: 0 !important;
    border: none !important;
    margin: 6px 0 !important;
    position: relative;
}

.popup-content-area div[style*="耐久度"] > div:first-child {
    display: none !important;
}

.popup-content-area div[style*="耐久度"] > div:last-child {
    background: rgba(0, 0, 0, 0.6) !important;
    height: 14px !important;
    border-radius: 7px !important;
    overflow: visible !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.4) !important;
    position: relative;
}

.popup-content-area div[style*="耐久度"] > div:last-child > div {
    height: 100% !important;
    background: linear-gradient(90deg, #e74c3c, #f39c12, #27ae60) !important;
    border-radius: 6px !important;
    box-shadow: 0 0 8px rgba(255, 255, 255, 0.4) !important;
}

/* 耐久度数值显示在进度条上 - 修复选择器 */
.popup-content-area [data-durability]::after {
    content: attr(data-durability);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 10px;
    font-weight: bold;
    text-shadow: 0 0 4px rgba(0, 0, 0, 0.8);
    z-index: 1;
    pointer-events: none;
    white-space: nowrap;
}

/* 技能面板 - 极简版 */
.popup-content-area div[style*="武器技能"] {
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.15), rgba(26, 35, 50, 0.6)) !important;
    padding: 6px 10px !important;
    border-radius: 8px !important;
    border: 1px solid rgba(212, 175, 55, 0.3) !important;
    margin: 6px 0 !important;
    box-shadow: 
        inset 0 1px 0 rgba(255, 255, 255, 0.05),
        0 0 20px rgba(212, 175, 55, 0.1);
}

.popup-content-area div[style*="武器技能"] > div:first-child {
    font-weight: bold !important;
    color: #d4af37 !important;
    font-size: 12px !important;
    margin-bottom: 4px !important;
    text-shadow: 0 0 8px rgba(212, 175, 55, 0.8);
    text-align: center;
    padding-bottom: 3px;
    border-bottom: 1px solid rgba(212, 175, 55, 0.2);
}

.popup-content-area div[style*="武器技能"] > div:last-child {
    font-size: 10px !important;
    color: #ecf0f1 !important;
    line-height: 1.3 !important;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
}

.popup-content-area div[style*="武器技能"] > div:last-child > div {
    margin: 0 !important;
    padding: 0 !important;
    background: none !important;
    border: none !important;
    border-radius: 0 !important;
    flex: none;
}

/* 特殊效果面板 - 紧凑版 */
.popup-content-area div[style*="特殊效果"] {
    background: linear-gradient(135deg, rgba(155, 89, 182, 0.15), rgba(26, 35, 50, 0.6)) !important;
    padding: 10px 14px !important;
    border-radius: 10px !important;
    border: 1px solid rgba(155, 89, 182, 0.3) !important;
    margin: 8px 0 !important;
    box-shadow: 
        inset 0 1px 0 rgba(255, 255, 255, 0.05),
        0 0 20px rgba(155, 89, 182, 0.1);
}

.popup-content-area div[style*="特殊效果"] > div:first-child {
    font-weight: bold !important;
    color: #9b59b6 !important;
    font-size: 14px !important;
    margin-bottom: 8px !important;
    text-shadow: 0 0 8px rgba(155, 89, 182, 0.8);
    text-align: center;
    padding-bottom: 6px;
    border-bottom: 1px solid rgba(155, 89, 182, 0.2);
}

.popup-content-area div[style*="特殊效果"] > div:last-child {
    font-size: 11px !important;
    color: #ecf0f1 !important;
    line-height: 1.4 !important;
}

.popup-content-area div[style*="特殊效果"] > div:last-child > div {
    margin: 0 !important;
    padding: 0 !important;
    background: none !important;
    border: none !important;
    border-radius: 0 !important;
    flex: none;
}

/* 外部按钮区域 - 一排布局 */
.popup-button-area {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 6px 12px;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(26, 35, 50, 0.9));
    border-radius: 12px;
    border: 1px solid rgba(212, 175, 55, 0.3);
    box-shadow: 
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        0 -2px 8px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
}

/* 隐藏按钮区域外框 */
.popup-button-area.no-buttons {
    display: none !important;
}

/* 关闭按钮 */
.popup-close-button {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 24px;
    height: 24px;
    background: rgba(231, 76, 60, 0.8);
    border: none;
    border-radius: 50%;
    color: white;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    z-index: 100;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.popup-close-button:hover {
    background: rgba(231, 76, 60, 1);
    transform: scale(1.1);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.4);
}

.action-buttons {
    display: flex;
    gap: 6px;
    align-items: center;
    justify-content: center;
}

.item-detail-popup .btn {
    padding: 5px 12px;
    border: none;
    border-radius: 8px;
    font-size: 11px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 
        0 3px 8px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    white-space: nowrap;
    text-align: center;
    min-height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    position: relative;
    overflow: hidden;
    flex: 1;
}

.item-detail-popup .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.item-detail-popup .btn:hover::before {
    left: 100%;
}

.item-detail-popup .btn:hover {
    transform: translateY(-1px) scale(1.02);
    box-shadow: 
        0 4px 12px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.item-detail-popup .btn:active {
    transform: translateY(0) scale(0.98);
}

.item-detail-popup .btn-equip {
    background: linear-gradient(135deg, #27ae60, #2ecc71, #16a085);
    color: white;
}

.item-detail-popup .btn-equip:hover {
    background: linear-gradient(135deg, #2ecc71, #27ae60, #1abc9c);
}

.item-detail-popup .btn-unequip {
    background: linear-gradient(135deg, #e74c3c, #c0392b, #e67e22);
    color: white;
}

.item-detail-popup .btn-unequip:hover {
    background: linear-gradient(135deg, #c0392b, #e74c3c, #d35400);
}

.item-detail-popup .btn-use {
    background: linear-gradient(135deg, #3498db, #2980b9, #5dade2);
    color: white;
}

.item-detail-popup .btn-use:hover {
    background: linear-gradient(135deg, #2980b9, #3498db, #85c1e9);
}

.item-detail-popup .btn-repair {
    background: linear-gradient(135deg, #f39c12, #e67e22, #f1c40f);
    color: white;
}

.item-detail-popup .btn-repair:hover {
    background: linear-gradient(135deg, #e67e22, #f39c12, #f4d03f);
}

.item-detail-popup .btn-recycle {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d, #bdc3c7);
    color: white;
}

.item-detail-popup .btn-recycle:hover {
    background: linear-gradient(135deg, #7f8c8d, #95a5a6, #ecf0f1);
}

.item-detail-popup .btn-close {
    background: linear-gradient(135deg, #34495e, #2c3e50, #5d6d7e);
    color: white;
}

.item-detail-popup .btn-close:hover {
    background: linear-gradient(135deg, #2c3e50, #34495e, #85929e);
}

/* 品质颜色 - 保持不变 */
.rarity-common { 
    color: #95a5a6; 
    background: linear-gradient(135deg, rgba(149, 165, 166, 0.2), rgba(149, 165, 166, 0.1));
    padding: 2px 6px;
    border-radius: 4px;
    text-shadow: 0 0 8px rgba(149, 165, 166, 0.5);
}

.rarity-uncommon { 
    color: #27ae60; 
    background: linear-gradient(135deg, rgba(39, 174, 96, 0.3), rgba(39, 174, 96, 0.15));
    padding: 2px 6px;
    border-radius: 4px;
    text-shadow: 0 0 8px rgba(39, 174, 96, 0.6);
    box-shadow: 0 0 10px rgba(39, 174, 96, 0.3);
}

.rarity-rare { 
    color: #3498db; 
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.3), rgba(52, 152, 219, 0.15));
    padding: 2px 6px;
    border-radius: 4px;
    text-shadow: 0 0 8px rgba(52, 152, 219, 0.6);
    box-shadow: 0 0 10px rgba(52, 152, 219, 0.3);
}

.rarity-epic { 
    color: #9b59b6; 
    background: linear-gradient(135deg, rgba(155, 89, 182, 0.3), rgba(155, 89, 182, 0.15));
    padding: 2px 6px;
    border-radius: 4px;
    text-shadow: 0 0 8px rgba(155, 89, 182, 0.6);
    box-shadow: 0 0 10px rgba(155, 89, 182, 0.3);
}

.rarity-legendary { 
    color: #f39c12; 
    background: linear-gradient(135deg, rgba(243, 156, 18, 0.3), rgba(243, 156, 18, 0.15));
    padding: 2px 6px;
    border-radius: 4px;
    text-shadow: 0 0 8px rgba(243, 156, 18, 0.6);
    box-shadow: 0 0 10px rgba(243, 156, 18, 0.3);
}

.rarity-artifact { 
    color: #e74c3c; 
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.3), rgba(231, 76, 60, 0.15));
    padding: 2px 6px;
    border-radius: 4px;
    text-shadow: 0 0 8px rgba(231, 76, 60, 0.6);
    box-shadow: 0 0 10px rgba(231, 76, 60, 0.3);
}

.rarity-immortal { 
    color: #d4af37; 
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.3), rgba(212, 175, 55, 0.15));
    padding: 2px 6px;
    border-radius: 4px;
    text-shadow: 0 0 8px rgba(212, 175, 55, 0.6);
    box-shadow: 0 0 10px rgba(212, 175, 55, 0.3);
}

/* 🔧 新增：支持中文品质名称的样式 */
.rarity-普通 { 
    color: #95a5a6; 
    background: linear-gradient(135deg, rgba(149, 165, 166, 0.2), rgba(149, 165, 166, 0.1));
    padding: 2px 6px;
    border-radius: 4px;
    text-shadow: 0 0 8px rgba(149, 165, 166, 0.5);
}

.rarity-稀有 { 
    color: #27ae60; 
    background: linear-gradient(135deg, rgba(39, 174, 96, 0.3), rgba(39, 174, 96, 0.15));
    padding: 2px 6px;
    border-radius: 4px;
    text-shadow: 0 0 8px rgba(39, 174, 96, 0.6);
    box-shadow: 0 0 10px rgba(39, 174, 96, 0.3);
}

.rarity-史诗 { 
    color: #3498db; 
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.3), rgba(52, 152, 219, 0.15));
    padding: 2px 6px;
    border-radius: 4px;
    text-shadow: 0 0 8px rgba(52, 152, 219, 0.6);
}

.rarity-传说 { 
    color: #9b59b6; 
    background: linear-gradient(135deg, rgba(155, 89, 182, 0.3), rgba(155, 89, 182, 0.15));
    padding: 2px 6px;
    border-radius: 4px;
    text-shadow: 0 0 8px rgba(155, 89, 182, 0.6);
    box-shadow: 0 0 10px rgba(155, 89, 182, 0.3);
}

.rarity-神话 { 
    color: #f39c12; 
    background: linear-gradient(135deg, rgba(243, 156, 18, 0.3), rgba(243, 156, 18, 0.15));
    padding: 2px 6px;
    border-radius: 4px;
    text-shadow: 0 0 8px rgba(243, 156, 18, 0.6);
    box-shadow: 0 0 10px rgba(243, 156, 18, 0.3);
}

/* 回收确认弹窗 - 统一设计风格 */
.recycle-confirm-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.85);
    display: none;
    z-index: 2100;
    backdrop-filter: blur(8px);
}

.recycle-confirm-popup {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(145deg, #2c3e50, #34495e);
    border-radius: 16px;
    width: 90%;
    max-width: 350px;
    padding: 0;
    border: 2px solid #e74c3c;
    display: none;
    z-index: 2101;
    box-shadow: 
        0 15px 35px rgba(0, 0, 0, 0.7),
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        0 0 30px rgba(231, 76, 60, 0.2);
    overflow: hidden;
}

.recycle-confirm-header {
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.1), rgba(231, 76, 60, 0.05));
    border-bottom: 1px solid rgba(231, 76, 60, 0.3);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
}

.recycle-confirm-header h3 {
    margin: 0;
    color: #e74c3c;
    font-size: 16px;
    font-weight: bold;
    text-shadow: 0 0 8px rgba(231, 76, 60, 0.8);
}

.close-recycle-popup {
    background: rgba(231, 76, 60, 0.8);
    border: none;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.close-recycle-popup:hover {
    background: rgba(231, 76, 60, 1);
    transform: scale(1.1);
}

.recycle-confirm-content {
    padding: 16px;
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.05), rgba(26, 35, 50, 0.2));
}

.recycle-item-info {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.3), rgba(26, 35, 50, 0.6));
    padding: 12px;
    border-radius: 10px;
    margin-bottom: 12px;
    border: 1px solid rgba(212, 175, 55, 0.2);
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.recycle-item-name {
    color: #d4af37;
    font-weight: bold;
    font-size: 14px;
    margin-bottom: 6px;
    text-shadow: 0 0 8px rgba(212, 175, 55, 0.8);
}

.recycle-item-details {
    color: #bdc3c7;
    font-size: 12px;
    line-height: 1.4;
}

.recycle-price-info {
    display: flex;
    justify-content: space-between;
    margin: 12px 0;
    font-size: 13px;
    padding: 8px 12px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.recycle-price-label {
    color: #ecf0f1;
}

.recycle-price-value {
    color: #f39c12;
    font-weight: bold;
    text-shadow: 0 0 4px rgba(243, 156, 18, 0.6);
}

.recycle-warning {
    color: #e74c3c;
    font-size: 11px;
    text-align: center;
    font-style: italic;
    background: rgba(231, 76, 60, 0.1);
    padding: 6px 8px;
    border-radius: 6px;
    border: 1px solid rgba(231, 76, 60, 0.3);
}

/* 🔧 新增：回收数量选择样式 */
.recycle-quantity-section {
    padding: 12px 16px;
    border-bottom: 1px solid rgba(231, 76, 60, 0.2);
    background: rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    margin: 8px 0;
}

.recycle-quantity-label {
    color: #ecf0f1;
    font-size: 13px;
    margin-bottom: 8px;
    font-weight: bold;
}

.recycle-quantity-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 6px;
}

.quantity-btn {
    background: rgba(231, 76, 60, 0.8);
    border: 1px solid rgba(231, 76, 60, 0.6);
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.quantity-btn:hover {
    background: rgba(231, 76, 60, 1);
    transform: scale(1.1);
}

.quantity-max-btn {
    background: rgba(52, 152, 219, 0.8);
    border: 1px solid rgba(52, 152, 219, 0.6);
    color: white;
    padding: 6px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    font-weight: bold;
    transition: all 0.3s ease;
}

.quantity-max-btn:hover {
    background: rgba(52, 152, 219, 1);
    transform: scale(1.05);
}

#recycle-quantity-input {
    background: rgba(44, 62, 80, 0.8);
    border: 1px solid rgba(231, 76, 60, 0.4);
    color: #ecf0f1;
    width: 60px;
    height: 32px;
    text-align: center;
    border-radius: 6px;
    font-size: 14px;
    font-weight: bold;
}

#recycle-quantity-input:focus {
    outline: none;
    border-color: rgba(231, 76, 60, 0.8);
    box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.2);
}

.quantity-info {
    color: #bdc3c7;
    font-size: 12px;
    text-align: center;
}

.single-price-info {
    color: #bdc3c7;
    font-size: 12px;
    text-align: center;
    margin-top: 4px;
}

.recycle-confirm-buttons {
    padding: 12px 16px;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.4), rgba(26, 35, 50, 0.6));
    border-top: 1px solid rgba(231, 76, 60, 0.3);
    display: flex;
    gap: 8px;
}

.btn-recycle-cancel,
.btn-recycle-confirm {
    flex: 1;
    padding: 8px 12px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 12px;
    font-weight: bold;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 32px;
}

.btn-recycle-cancel {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
    color: white;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
}

.btn-recycle-confirm {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
}

.btn-recycle-cancel:hover,
.btn-recycle-confirm:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

/* 移动端适配 - 简洁版 */
@media (max-width: 768px) {
    .item-detail-popup .popup-container {
        width: 98% !important;
        max-width: 380px !important;
    }

    .item-detail-popup .popup-content-area {
        border-radius: 16px !important;
        max-height: calc(95vh - 70px) !important;
        margin-bottom: 55px !important;
    }

    .popup-content-inner {
        padding: 14px 26px 18px 26px !important;
        max-height: calc(95vh - 130px) !important;
    }

    .popup-content-area h3 {
        font-size: 17px !important;
        margin: 0 0 10px 0 !important;
        padding: 7px 0 !important;
        border-radius: 10px !important;
    }

    .popup-content-area img {
        width: 65px !important;
        height: 65px !important;
        margin: 0 auto 10px auto !important;
        border-radius: 10px !important;
    }

    .popup-content-area p {
        font-size: 12px !important;
        margin: 0 0 10px 0 !important;
        padding: 7px 10px !important;
        border-radius: 8px !important;
    }

    .popup-content-area > div:first-child > div:nth-child(4),
    .popup-content-area > div:first-child > div:nth-child(5),
    .popup-content-area > div:first-child > div:nth-child(6),
    .popup-content-area > div:first-child > div:nth-child(7) {
        padding: 10px 14px !important;
        margin: 8px 0 !important;
        border-radius: 10px !important;
    }

    .popup-content-area div[style*="margin: 4px 0"] {
        font-size: 12px !important;
    }

    .popup-content-area div[style*="耐久度"] {
        padding: 8px 12px !important;
        margin: 6px 0 !important;
        border-radius: 8px !important;
    }

    .popup-content-area div[style*="耐久度"] > div:first-child {
        font-size: 11px !important;
    }

    .popup-content-area div[style*="武器技能"] {
        padding: 8px 12px !important;
        margin: 6px 0 !important;
        border-radius: 8px !important;
    }

    .popup-content-area div[style*="武器技能"] > div:first-child {
        font-size: 13px !important;
        margin-bottom: 6px !important;
    }

    .popup-content-area div[style*="武器技能"] > div:last-child {
        font-size: 10px !important;
    }

    .popup-content-area div[style*="特殊效果"] {
        padding: 8px 12px !important;
        margin: 6px 0 !important;
        border-radius: 8px !important;
    }

    .popup-content-area div[style*="特殊效果"] > div:first-child {
        font-size: 13px !important;
        margin-bottom: 6px !important;
    }

    .popup-content-area div[style*="特殊效果"] > div:last-child {
        font-size: 10px !important;
    }

    .popup-button-area {
        padding: 6px 10px !important;
        border-radius: 10px !important;
    }

    .action-buttons {
        gap: 4px !important;
    }

    .item-detail-popup .btn {
        padding: 5px 8px !important;
        font-size: 10px !important;
        min-height: 28px !important;
        border-radius: 6px !important;
    }
    
    .recycle-confirm-popup {
        width: 95% !important;
        max-width: 320px !important;
        border-radius: 12px !important;
    }
    
    .recycle-confirm-header {
        padding: 10px 14px !important;
    }
    
    .recycle-confirm-content {
        padding: 14px !important;
    }
    
    .recycle-confirm-buttons {
        padding: 10px 14px !important;
    }
    
    .repair-confirm-popup {
        width: 95% !important;
        max-width: 320px !important;
        border-radius: 12px !important;
    }
    
    .repair-confirm-header {
        padding: 10px 14px !important;
    }
    
    .repair-confirm-content {
        padding: 14px !important;
    }
    
    .repair-confirm-buttons {
        padding: 10px 14px !important;
    }
}

@media (max-width: 480px) {
    .item-detail-popup .popup-container {
        width: 99% !important;
        max-width: 320px !important;
    }

    .item-detail-popup .popup-content-area {
        border-radius: 14px !important;
        max-height: calc(95vh - 65px) !important;
        margin-bottom: 50px !important;
    }

    .popup-content-inner {
        padding: 12px 24px 16px 24px !important;
        max-height: calc(95vh - 120px) !important;
    }

    .popup-content-area h3 {
        font-size: 16px !important;
        margin: 0 0 8px 0 !important;
        padding: 6px 0 !important;
        border-radius: 8px !important;
    }

    .popup-content-area img {
        width: 60px !important;
        height: 60px !important;
        margin: 0 auto 8px auto !important;
        border-radius: 8px !important;
    }

    .popup-content-area p {
        font-size: 11px !important;
        margin: 0 0 8px 0 !important;
        padding: 6px 8px !important;
        border-radius: 6px !important;
    }

    .popup-content-area > div:first-child > div:nth-child(4),
    .popup-content-area > div:first-child > div:nth-child(5),
    .popup-content-area > div:first-child > div:nth-child(6),
    .popup-content-area > div:first-child > div:nth-child(7) {
        padding: 8px 12px !important;
        margin: 6px 0 !important;
        border-radius: 8px !important;
    }

    .popup-content-area div[style*="margin: 4px 0"] {
        font-size: 11px !important;
        margin: 3px 0 !important;
    }

    .popup-content-area div[style*="耐久度"] {
        padding: 6px 10px !important;
        margin: 5px 0 !important;
        border-radius: 6px !important;
    }

    .popup-content-area div[style*="耐久度"] > div:first-child {
        font-size: 10px !important;
    }

    .popup-content-area div[style*="武器技能"] {
        padding: 6px 10px !important;
        margin: 5px 0 !important;
        border-radius: 6px !important;
    }

    .popup-content-area div[style*="武器技能"] > div:first-child {
        font-size: 12px !important;
        margin-bottom: 4px !important;
    }

    .popup-content-area div[style*="武器技能"] > div:last-child {
        font-size: 9px !important;
    }

    .popup-content-area div[style*="武器技能"] > div:last-child > div {
        margin: 2px 0 !important;
    }

    .popup-content-area div[style*="特殊效果"] {
        padding: 6px 10px !important;
        margin: 5px 0 !important;
        border-radius: 6px !important;
    }

    .popup-content-area div[style*="特殊效果"] > div:first-child {
        font-size: 12px !important;
        margin-bottom: 4px !important;
    }

    .popup-content-area div[style*="特殊效果"] > div:last-child {
        font-size: 9px !important;
    }

    .popup-content-area div[style*="特殊效果"] > div:last-child > div {
        margin: 2px 0 !important;
    }

    .popup-button-area {
        padding: 5px 8px !important;
        border-radius: 8px !important;
    }

    .item-detail-popup .btn {
        padding: 4px 6px !important;
        font-size: 9px !important;
        min-height: 26px !important;
        border-radius: 5px !important;
    }

    .recycle-confirm-popup {
        width: 98% !important;
        max-width: 280px !important;
        border-radius: 10px !important;
    }
    
    .recycle-confirm-header {
        padding: 8px 12px !important;
    }
    
    .recycle-confirm-header h3 {
        font-size: 14px !important;
    }
    
    .recycle-confirm-content {
        padding: 12px !important;
    }
    
    .recycle-confirm-buttons {
        padding: 8px 12px !important;
        gap: 6px !important;
    }
    
    .btn-recycle-cancel,
    .btn-recycle-confirm {
        font-size: 11px !important;
        min-height: 28px !important;
    }
    
    .repair-confirm-popup {
        width: 98% !important;
        max-width: 280px !important;
        border-radius: 10px !important;
    }
    
    .repair-confirm-header {
        padding: 8px 12px !important;
    }
    
    .repair-confirm-header h3 {
        font-size: 14px !important;
    }
    
    .repair-confirm-content {
        padding: 12px !important;
    }
    
    .repair-confirm-buttons {
        padding: 8px 12px !important;
        gap: 6px !important;
    }
    
    .btn-repair-cancel,
    .btn-repair-confirm {
        font-size: 11px !important;
        min-height: 28px !important;
    }
}

/* 修复确认弹窗样式 */
.repair-confirm-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.85);
    display: none;
    z-index: 2200;
    backdrop-filter: blur(8px);
}

.repair-confirm-popup {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(145deg, #2c3e50, #34495e);
    border-radius: 16px;
    width: 90%;
    max-width: 350px;
    padding: 0;
    border: 2px solid #f39c12;
    display: none;
    z-index: 2201;
    box-shadow: 
        0 15px 35px rgba(0, 0, 0, 0.7),
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        0 0 30px rgba(243, 156, 18, 0.2);
    overflow: hidden;
}

.repair-confirm-header {
    background: linear-gradient(135deg, rgba(243, 156, 18, 0.1), rgba(243, 156, 18, 0.05));
    border-bottom: 1px solid rgba(243, 156, 18, 0.3);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
}

.repair-confirm-header h3 {
    margin: 0;
    color: #f39c12;
    font-size: 16px;
    font-weight: bold;
    text-shadow: 0 0 8px rgba(243, 156, 18, 0.8);
}

.close-repair-popup {
    background: rgba(243, 156, 18, 0.8);
    border: none;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.close-repair-popup:hover {
    background: rgba(243, 156, 18, 1);
    transform: scale(1.1);
}

.repair-confirm-content {
    padding: 16px;
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.05), rgba(26, 35, 50, 0.2));
}

.repair-item-info {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.3), rgba(26, 35, 50, 0.6));
    padding: 12px;
    border-radius: 10px;
    margin-bottom: 12px;
    border: 1px solid rgba(212, 175, 55, 0.2);
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.repair-item-name {
    color: #d4af37;
    font-weight: bold;
    font-size: 14px;
    margin-bottom: 6px;
    text-shadow: 0 0 8px rgba(212, 175, 55, 0.8);
}

.repair-item-details {
    color: #bdc3c7;
    font-size: 12px;
    line-height: 1.4;
}

.repair-cost-info {
    display: flex;
    justify-content: space-between;
    margin: 12px 0;
    font-size: 13px;
    padding: 8px 12px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.repair-cost-label {
    color: #ecf0f1;
}

.repair-cost-value {
    color: #f39c12;
    font-weight: bold;
    text-shadow: 0 0 4px rgba(243, 156, 18, 0.6);
}

.repair-warning {
    color: #f39c12;
    font-size: 11px;
    text-align: center;
    font-style: italic;
    background: rgba(243, 156, 18, 0.1);
    padding: 6px 8px;
    border-radius: 6px;
    border: 1px solid rgba(243, 156, 18, 0.3);
}

.repair-confirm-buttons {
    padding: 12px 16px;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.4), rgba(26, 35, 50, 0.6));
    border-top: 1px solid rgba(243, 156, 18, 0.3);
    display: flex;
    gap: 8px;
}

.btn-repair-cancel,
.btn-repair-confirm {
    flex: 1;
    padding: 8px 12px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 12px;
    font-weight: bold;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 32px;
}

.btn-repair-cancel {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
    color: white;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
}

.btn-repair-confirm {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
}

.btn-repair-cancel:hover,
.btn-repair-confirm:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
} 
 
/* 🍎 iOS设备特定修复 */
@supports (-webkit-touch-callout: none) {
    /* iOS物品详情弹窗修复 */
    .item-detail-popup {
        /* iOS遮罩层强制显示 */
        background: rgba(0, 0, 0, 0.85) !important;
        /* iOS层级强制提升 */
        z-index: 9999 !important;
        /* iOS定位强制修复 */
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        /* iOS硬件加速 */
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
        will-change: transform;
        /* iOS触摸事件 */
        touch-action: none;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        user-select: none;
    }
    
    /* iOS弹窗容器定位修复 */
    .item-detail-popup .popup-container {
        /* iOS安全区域适配 */
        margin-top: env(safe-area-inset-top, 10px);
        margin-bottom: calc(env(safe-area-inset-bottom, 10px) + 60px);
        /* iOS导航栏避让 */
        padding-top: 15px;
        padding-bottom: 15px;
        /* iOS最大高度限制 - 增加内容显示区域 */
        max-height: calc(100vh - env(safe-area-inset-top, 10px) - env(safe-area-inset-bottom, 10px) - 90px);
        /* iOS居中对齐 */
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        /* iOS宽度优化 */
        width: 95%;
        max-width: 400px;
    }
    
    /* iOS弹窗内容区域修复 */
    .item-detail-popup .popup-content-area {
        /* iOS最大高度限制 - 增加内容显示区域 */
        max-height: calc(100vh - env(safe-area-inset-top, 10px) - env(safe-area-inset-bottom, 10px) - 130px);
        /* iOS滚动优化 */
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
        /* iOS触摸恢复 */
        touch-action: auto;
        -webkit-user-select: text;
        user-select: text;
        /* iOS内容区域优化 */
        flex: 1;
        min-height: 300px;
    }
    
    /* iOS按钮区域修复 */
    .popup-button-area {
        /* iOS底部导航避让 */
        margin-bottom: calc(env(safe-area-inset-bottom, 0px) + 10px);
        /* iOS定位修复 */
        position: relative;
        bottom: auto;
    }
    
    /* 🔧 iOS按钮大小修复 - 确保按钮在iOS上有合适的大小 */
    .item-detail-popup .btn {
        /* iOS按钮字体大小修复 */
        font-size: 13px !important;
        /* iOS按钮内边距修复 */
        padding: 8px 12px !important;
        /* iOS按钮最小高度修复 */
        min-height: 36px !important;
        /* iOS按钮边框半径 */
        border-radius: 8px !important;
        /* iOS触摸目标大小优化 */
        min-width: 60px !important;
        /* iOS字体渲染优化 */
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        /* iOS按钮文字粗细 */
        font-weight: 600 !important;
        /* iOS按钮行高 */
        line-height: 1.2 !important;
    }
    
    /* iOS确认按钮特殊处理 */
    .btn-recycle-cancel,
    .btn-recycle-confirm,
    .btn-repair-cancel,
    .btn-repair-confirm {
        font-size: 13px !important;
        padding: 8px 12px !important;
        min-height: 36px !important;
        border-radius: 8px !important;
        font-weight: 600 !important;
    }
}

/* iOS PWA模式特殊修复 */
@media (display-mode: standalone) and (-webkit-touch-callout: none) {
    .item-detail-popup .popup-container {
        /* iOS PWA模式下的额外边距 - 优化内容显示 */
        margin-top: calc(env(safe-area-inset-top, 10px) + 15px);
        margin-bottom: calc(env(safe-area-inset-bottom, 10px) + 70px);
        /* iOS PWA模式下的最大高度优化 */
        max-height: calc(100vh - env(safe-area-inset-top, 10px) - env(safe-area-inset-bottom, 10px) - 100px);
    }
    
    .item-detail-popup .popup-content-area {
        /* iOS PWA模式下内容区域高度优化 */
        max-height: calc(100vh - env(safe-area-inset-top, 10px) - env(safe-area-inset-bottom, 10px) - 140px);
        min-height: 350px;
    }
}