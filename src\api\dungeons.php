<?php
require_once __DIR__ . '/../includes/functions.php';

setJsonResponse();

if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => '请先登录']);
    exit;
}

$pdo = getDatabase();
$user = getCurrentUser();
$userId = $user['id'];

$action = isset($_GET['action']) ? $_GET['action'] : (isset($_POST['action']) ? $_POST['action'] : '');

try {
    switch ($action) {
        case 'get_dungeons':
            getDungeons($pdo, $userId);
            break;
            
        case 'enter_dungeon':
            enterDungeon($pdo, $userId, $_POST['dungeon_id']);
            break;
            
        case 'battle_monster':
            battleMonster($pdo, $userId, $_POST['dungeon_id']);
            break;
            
        case 'battle_boss':
            battleBoss($pdo, $userId, $_POST['dungeon_id']);
            break;
            
        case 'get_progress':
            getDungeonProgress($pdo, $userId);
            break;
            
        default:
            echo json_encode(['success' => false, 'message' => '无效的操作']);
            break;
    }
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => '系统错误: ' . $e->getMessage()]);
}

// 获取副本列表
function getDungeons($pdo, $userId) {
    try {
        // 🔧 修改：从characters表获取用户境界等级
        $stmt = $pdo->prepare("
            SELECT r.realm_level
            FROM characters c
            JOIN realm_levels r ON c.realm_id = r.id
            WHERE c.user_id = ?
        ");
        $stmt->execute([$userId]);
        $userRealm = $stmt->fetch(PDO::FETCH_ASSOC);
        $userLevel = $userRealm ? $userRealm['realm_level'] : 1;
        
        // 获取副本列表
        $stmt = $pdo->prepare("
            SELECT d.*, 
                CASE WHEN udp.id IS NULL THEN 0 ELSE 1 END as unlocked,
                COALESCE(udp.current_monster, 1) as current_monster,
                COALESCE(udp.is_completed, 0) as is_completed,
                COALESCE(udp.clear_count, 0) as clear_count
            FROM dungeons d
            LEFT JOIN user_dungeon_progress udp ON d.id = udp.dungeon_id AND udp.user_id = ?
            WHERE d.is_active = 1 AND d.min_level <= ?
            ORDER BY d.type, d.chapter
        ");
        $stmt->execute([$userId, $userLevel]);
        $dungeons = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'success' => true,
            'dungeons' => $dungeons,
            'user_level' => $userLevel
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => '获取副本列表失败: ' . $e->getMessage()]);
    }
}

// 进入副本
function enterDungeon($pdo, $userId, $dungeonId) {
    try {
        // 检查副本是否存在且可进入
        $stmt = $pdo->prepare("SELECT * FROM dungeons WHERE id = ? AND is_active = 1");
        $stmt->execute([$dungeonId]);
        $dungeon = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$dungeon) {
            echo json_encode(['success' => false, 'message' => '副本不存在或已关闭']);
            return;
        }
        
        // 🔧 修改：从characters表检查用户等级
        $stmt = $pdo->prepare("
            SELECT r.realm_level
            FROM characters c
            JOIN realm_levels r ON c.realm_id = r.id
            WHERE c.user_id = ?
        ");
        $stmt->execute([$userId]);
        $userRealm = $stmt->fetch(PDO::FETCH_ASSOC);
        $userLevel = $userRealm ? $userRealm['realm_level'] : 1;
        
        if ($userLevel < $dungeon['min_level']) {
            echo json_encode(['success' => false, 'message' => '等级不足，无法进入此副本']);
            return;
        }
        
        // 创建或更新副本进度
        $stmt = $pdo->prepare("
            INSERT INTO user_dungeon_progress (user_id, dungeon_id, current_monster, last_attempt)
            VALUES (?, ?, 1, NOW())
            ON DUPLICATE KEY UPDATE last_attempt = NOW()
        ");
        $stmt->execute([$userId, $dungeonId]);
        
        echo json_encode([
            'success' => true,
            'message' => '成功进入副本: ' . $dungeon['name'],
            'dungeon' => $dungeon
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => '进入副本失败: ' . $e->getMessage()]);
    }
}

// 战斗小怪
function battleMonster($pdo, $userId, $dungeonId) {
    try {
        // 获取副本和进度信息
        $stmt = $pdo->prepare("
            SELECT d.*, udp.current_monster, udp.is_completed
            FROM dungeons d
            LEFT JOIN user_dungeon_progress udp ON d.id = udp.dungeon_id AND udp.user_id = ?
            WHERE d.id = ?
        ");
        $stmt->execute([$userId, $dungeonId]);
        $dungeonInfo = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$dungeonInfo) {
            echo json_encode(['success' => false, 'message' => '副本信息获取失败']);
            return;
        }
        
        $currentMonster = $dungeonInfo['current_monster'] ?: 1;
        
        if ($currentMonster > $dungeonInfo['monsters_count']) {
            echo json_encode(['success' => false, 'message' => '已完成所有小怪，可以挑战Boss']);
            return;
        }
        
        // 简单的战斗逻辑（随机胜负，胜率70%）
        $isWin = rand(1, 100) <= 70;
        
        if ($isWin) {
            // 胜利，推进进度
            $newMonster = $currentMonster + 1;
            $stmt = $pdo->prepare("
                UPDATE user_dungeon_progress 
                SET current_monster = ?, last_attempt = NOW()
                WHERE user_id = ? AND dungeon_id = ?
            ");
            $stmt->execute([$newMonster, $userId, $dungeonId]);
            
            // 奖励血魄石
            $bloodEssenceReward = $dungeonInfo['chapter'] * 10;
            $stmt = $pdo->prepare("
                UPDATE user_resources 
                SET blood_essence = blood_essence + ?
                WHERE user_id = ?
            ");
            $stmt->execute([$bloodEssenceReward, $userId]);
            
            $message = "击败第{$currentMonster}只小怪！获得 {$bloodEssenceReward} 血魄石";
            if ($newMonster > $dungeonInfo['monsters_count']) {
                $message .= "，现在可以挑战Boss了！";
            }
            
            echo json_encode([
                'success' => true,
                'battle_result' => 'win',
                'message' => $message,
                'rewards' => ['blood_essence' => $bloodEssenceReward],
                'next_monster' => $newMonster
            ]);
        } else {
            // 失败
            echo json_encode([
                'success' => true,
                'battle_result' => 'lose',
                'message' => "战斗失败！请提升实力后再来挑战",
                'current_monster' => $currentMonster
            ]);
        }
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => '战斗失败: ' . $e->getMessage()]);
    }
}

// 战斗Boss
function battleBoss($pdo, $userId, $dungeonId) {
    try {
        // 检查是否有资格挑战Boss
        $stmt = $pdo->prepare("
            SELECT d.*, udp.current_monster, udp.is_completed
            FROM dungeons d
            LEFT JOIN user_dungeon_progress udp ON d.id = udp.dungeon_id AND udp.user_id = ?
            WHERE d.id = ?
        ");
        $stmt->execute([$userId, $dungeonId]);
        $dungeonInfo = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$dungeonInfo) {
            echo json_encode(['success' => false, 'message' => '副本信息获取失败']);
            return;
        }
        
        $currentMonster = $dungeonInfo['current_monster'] ?: 1;
        
        if ($currentMonster <= $dungeonInfo['monsters_count']) {
            echo json_encode(['success' => false, 'message' => '请先完成所有小怪']);
            return;
        }
        
        // 检查血魄石是否足够
        $stmt = $pdo->prepare("SELECT blood_essence FROM user_resources WHERE user_id = ?");
        $stmt->execute([$userId]);
        $resources = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $bloodEssenceCost = $dungeonInfo['monsters_count'] * $dungeonInfo['chapter'] * 10;
        
        if ($resources['blood_essence'] < $bloodEssenceCost) {
            echo json_encode(['success' => false, 'message' => "血魄石不足！需要 {$bloodEssenceCost} 个"]);
            return;
        }
        
        // Boss战斗逻辑（胜率50%）
        $isWin = rand(1, 100) <= 50;
        
        $pdo->beginTransaction();
        
        // 消耗血魄石
        $stmt = $pdo->prepare("
            UPDATE user_resources 
            SET blood_essence = blood_essence - ?
            WHERE user_id = ?
        ");
        $stmt->execute([$bloodEssenceCost, $userId]);
        
        if ($isWin) {
            // 胜利，完成副本
            $stmt = $pdo->prepare("
                UPDATE user_dungeon_progress 
                SET is_completed = 1, clear_count = clear_count + 1, last_attempt = NOW()
                WHERE user_id = ? AND dungeon_id = ?
            ");
            $stmt->execute([$userId, $dungeonId]);
            
            // 奖励（灵石、银两等）
            $spiritStoneReward = $dungeonInfo['chapter'] * 5;
            $silverReward = $dungeonInfo['chapter'] * 1000;
            
            $stmt = $pdo->prepare("
                UPDATE user_resources 
                SET spirit_stones = spirit_stones + ?, silver = silver + ?
                WHERE user_id = ?
            ");
            $stmt->execute([$spiritStoneReward, $silverReward, $userId]);
            
            $pdo->commit();
            
            echo json_encode([
                'success' => true,
                'battle_result' => 'win',
                'message' => "击败Boss！副本通关！",
                'rewards' => [
                    'spirit_stones' => $spiritStoneReward,
                    'silver' => $silverReward
                ]
            ]);
        } else {
            // 失败
            $pdo->commit();
            
            echo json_encode([
                'success' => true,
                'battle_result' => 'lose',
                'message' => "Boss战失败！血魄石已消耗，请重新收集",
                'blood_essence_lost' => $bloodEssenceCost
            ]);
        }
        
    } catch (Exception $e) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => 'Boss战失败: ' . $e->getMessage()]);
    }
}

// 获取副本进度
function getDungeonProgress($pdo, $userId) {
    try {
        $stmt = $pdo->prepare("
            SELECT d.id, d.name, d.type, d.chapter, 
                udp.current_monster, udp.is_completed, udp.clear_count
            FROM user_dungeon_progress udp
            JOIN dungeons d ON udp.dungeon_id = d.id
            WHERE udp.user_id = ?
            ORDER BY d.type, d.chapter
        ");
        $stmt->execute([$userId]);
        $progress = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'success' => true,
            'progress' => $progress
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => '获取进度失败: ' . $e->getMessage()]);
    }
}
?> 