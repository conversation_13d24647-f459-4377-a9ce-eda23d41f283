<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <!-- 移动端适配核心meta标签 -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover">
    
    <!-- 🔧 PWA模式底部白边强力修复 - 必须最早执行 -->
    <script src="assets/js/pwa-fix.js"></script>
    
    <!-- 🎛️ 全局调试开关 - 必须最早加载 -->
    <script src="assets/js/global-debug-switch.js"></script>

    <!-- 🔧 项目配置文件 - 必须早期加载 -->
    <script src="assets/js/config.js"></script>

    <!-- WebView优化配置 -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="screen-orientation" content="portrait">
    <meta name="x5-orientation" content="portrait">
    <meta name="full-screen" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta name="browsermode" content="application">
    <meta name="x5-page-mode" content="app">
    <!-- 禁用长按菜单 -->
    <meta name="format-detection" content="telephone=no,email=no,address=no">
    <!-- 强制使用最新版本 -->
    <meta http-equiv="Cache-Control" content="no-siteapp">
    <meta http-equiv="Cache-Control" content="no-transform">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <!-- 启用硬件加速 -->
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <!-- iOS图标 -->
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/images/app-icon-180.png">
    <link rel="apple-touch-icon" sizes="167x167" href="/assets/images/app-icon-167.png">
    <link rel="apple-touch-icon" sizes="152x152" href="/assets/images/app-icon-152.png">
    <link rel="apple-touch-icon" sizes="120x120" href="/assets/images/app-icon-120.png">
    <!-- PWA支持 -->
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#d4af37">

    <title>一念仙魔-主页</title>
    <!-- 🔧 新增：引入全局样式文件 -->
    <link rel="stylesheet" href="assets/css/global.css">
    <link rel="stylesheet" href="assets/css/game.css">
    
    <!-- 🔧 视口高度修复脚本 -->
    <script src="assets/js/viewport-height-fix.js"></script>
    
    <!-- 引入通用导航样式 -->
    <link rel="stylesheet" href="assets/css/common-navigation.css">
    <!-- 引入境界系统 -->
    <script src="assets/js/realm-system.js"></script>
    <!-- 🔑 全局登录检查系统 -->
    <script src="assets/js/auth-check.js"></script>
    <!-- 🎵 全局音乐管理器 -->
    <script src="assets/js/global-music-manager.js"></script>
</head>
<body class="game-page">
    <div class="main-container game-interface">
        <!-- 顶部用户信息栏 -->
        <div class="user-info-bar">
            <div class="user-profile">
                <div class="user-avatar" id="userAvatar">
                    <div class="user-avatar-content" id="userAvatarContent"></div>
                </div>
                <div class="user-details">
                    <div class="username" id="username" onclick="openRenameModal()">xxx</div>
                    <div class="user-level">
                        <span class="realm-text" id="userLevel">开光期 初期</span>
                    </div>
                </div>
            </div>
            <div class="user-resources">
                <div class="resource-item">
                    <span class="resource-icon">💰</span>
                    <span class="resource-value" id="goldValue">999</span>
                </div>
                <div class="resource-item">
                    <span class="resource-icon">🔹</span>
                    <span class="resource-value" id="spiritStoneValue">999</span>
                </div>
            </div>
        </div>

        <!-- 游戏主界面 -->
        <div class="game-world">
            <!-- 按钮网格布局 -->
            <div class="button-grid">
                <!-- 第一行 -->
                <a href="immortal_arena.html" class="function-button btn-arena">
                    <div class="function-icon">🏆</div>
                    <div class="function-text">论道</div>
                </a>
                
                <div class="function-button btn-beast" onclick="openBeastSystem()">
                    <div class="function-icon">🐲</div>
                    <div class="function-text">异兽</div>
                </div>

                <div class="function-button btn-dungeon" onclick="openDungeon()">
                    <div class="function-icon">🏔️</div>
                    <div class="function-text">秘境</div>
                </div>

                <!-- 第二行 -->
                <div class="function-button btn-alchemy" onclick="openAlchemySystem()">
                    <div class="function-icon">⚗️</div>
                    <div class="function-text">炼丹</div>
                </div>

                <!-- 中央修炼台 -->
                <div class="cultivation-platform" onclick="openCultivation()">
                    <!-- SVG进度环 -->
                    <svg class="cultivation-progress-ring" viewBox="0 0 96 96">
                        <circle class="cultivation-progress-background" cx="48" cy="48" r="45"></circle>
                        <circle class="cultivation-progress-foreground" cx="48" cy="48" r="45" id="cultivationProgressCircle"></circle>
                    </svg>
                    
                    <div class="cultivation-icon">⚡</div>
                    <div class="cultivation-text">修炼</div>
                    
                    <!-- 进度文字 -->
                    <div class="cultivation-progress-text" id="cultivationProgressText">0%</div>
                </div>

                <div class="function-button btn-spirit" onclick="openSpiritRootSystem()">
                    <div class="function-icon">🌳</div>
                    <div class="function-text">灵根</div>
                </div>

                <!-- 第三行 -->
                <div class="function-button btn-shop" onclick="openShop()">
                    <div class="function-icon">🏪</div>
                    <div class="function-text">市场</div>
                </div>

                <div class="function-button btn-guild" onclick="openGuild()">
                    <div class="function-icon">🏛️</div>
                    <div class="function-text">宗门</div>
                </div>

                <div class="function-button btn-ranking" onclick="openRanking()">
                    <div class="function-icon">🏆</div>
                    <div class="function-text">排行</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 更名弹窗 -->
    <div class="rename-overlay" id="renameOverlay">
        <div class="rename-modal">
            <div class="rename-title">更改道号</div>
            <div class="rename-current" id="renameCurrent">当前道号：黄汇清</div>
            
            <div class="rename-form">
                <input type="text" id="renameInput" class="rename-input" placeholder="请输入新的道号" maxlength="8">
                <div class="rename-cost" id="renameCost">
                    <span id="renameCostText">首次更名免费</span>
                </div>
            </div>
            
            <div class="rename-buttons">
                <button class="rename-button rename-cancel" onclick="closeRenameModal()">取消</button>
                <button class="rename-button rename-confirm" id="renameConfirm" onclick="confirmRename()">确认更名</button>
            </div>
        </div>
    </div>

    <!-- 头像外框选择弹窗 -->
    <div class="avatar-customization-overlay" id="avatarCustomizationOverlay">
        <div class="avatar-customization-modal">
            <div class="avatar-customization-title">头像外框</div>
            
            <!-- 头像外框选择 -->
            <div class="avatar-content-panel">
                <div class="avatar-current" id="frameCurrent">当前外框：默认</div>
                <div class="avatar-grid" id="frameGrid">
                    <!-- 头像外框列表 -->
                </div>
            </div>
            
            <div class="avatar-customization-buttons">
                <button class="avatar-customization-button avatar-cancel" onclick="closeAvatarCustomization()">取消</button>
                <button class="avatar-customization-button avatar-confirm" id="avatarConfirm" onclick="confirmAvatarCustomization()">确认</button>
            </div>
        </div>
    </div>

    <!-- 底部导航栏会由 common-navigation.js 自动插入 -->
    
    <script src="assets/js/common-navigation.js"></script>
    <script>
        let currentUser = null;

        // 页面加载
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('游戏主界面加载...');
            
            // 🎵 全局音乐管理器会自动处理音乐播放
            
            // 清理损坏的localStorage数据
            try {
                const lastRenameResult = localStorage.getItem('lastRenameResult');
                if (lastRenameResult) {
                    const renameData = JSON.parse(lastRenameResult);
                    if (!renameData.new_name || typeof renameData.new_name !== 'string' || renameData.new_name.trim() === '') {
                        console.log('🧹 [清理] 清除损坏的localStorage更名数据');
                        localStorage.removeItem('lastRenameResult');
                    }
                }
            } catch (e) {
                console.log('🧹 [清理] 清除损坏的localStorage更名数据');
                localStorage.removeItem('lastRenameResult');
            }
            
            try {
                // 检查是否有最近的更名结果需要恢复
                try {
                    const lastRenameResult = localStorage.getItem('lastRenameResult');
                    console.log('🎯 [恢复] localStorage原始数据:', lastRenameResult);
                    
                    if (lastRenameResult) {
                        const renameData = JSON.parse(lastRenameResult);
                        console.log('🎯 [恢复] 解析后的更名记录:', renameData);
                        console.log('🎯 [恢复] 更名记录中的new_name:', renameData.new_name);
                        console.log('🎯 [恢复] new_name类型:', typeof renameData.new_name);
                        
                        const timeDiff = Date.now() - renameData.timestamp;
                        console.log('🎯 [恢复] 发现上次更名记录:', renameData);
                        console.log('🎯 [恢复] 时间差:', timeDiff, 'ms');
                        
                        // 验证更名数据的有效性
                        if (renameData.new_name && typeof renameData.new_name === 'string' && renameData.new_name.trim() !== '') {
                            // 如果更名是在5分钟内进行的，恢复名称显示
                            if (timeDiff < 5 * 60 * 1000) {
                                console.log('✅ [恢复] 恢复最近的更名结果:', renameData.new_name);
                                const usernameElement = document.getElementById('username');
                                if (usernameElement) {
                                    usernameElement.textContent = renameData.new_name;
                                    console.log('✅ [恢复] 用户名显示已恢复为:', renameData.new_name);
                                    console.log('🎯 [恢复] 元素当前显示内容:', usernameElement.textContent);
                                } else {
                                    console.error('❌ [恢复] 找不到用户名元素');
                                }
                            } else {
                                console.log('🎯 [恢复] 更名记录过期，清除');
                                localStorage.removeItem('lastRenameResult');
                            }
                        } else {
                            console.error('❌ [恢复] 更名记录中的名称无效:', renameData.new_name);
                            console.log('🎯 [恢复] 清除无效的更名记录');
                            localStorage.removeItem('lastRenameResult');
                        }
                    } else {
                        console.log('🎯 [恢复] 没有找到更名记录');
                    }
                } catch (e) {
                    console.error('❌ [恢复] 恢复更名结果失败:', e);
                    console.log('🎯 [恢复] 清除损坏的更名记录');
                    localStorage.removeItem('lastRenameResult');
                }
                
                // 🌙 先处理离线修炼收益，再加载用户数据
                await handleOfflineCultivationOnGameLoad();
                
                await loadUserData();
                console.log('用户数据加载完成');
                
                // 🌟 初始化修炼进度环系统
                await cultivationProgressSystem.init();
                
                // 添加触摸反馈
                addTouchFeedback();
                
            } catch (error) {
                console.error('页面加载失败:', error);
                showMessage('加载失败，请刷新页面', 'error');
            }
        });

        // 加载用户数据
        async function loadUserData() {
            console.log('🎯 [加载] 开始加载用户数据');
            let recentRenameName = null;
            
            // 检查是否有最近的更名结果需要恢复
            try {
                const lastRenameResult = localStorage.getItem('lastRenameResult');
                console.log('🎯 [加载] localStorage原始数据:', lastRenameResult);
                
                if (lastRenameResult) {
                    const renameData = JSON.parse(lastRenameResult);
                    console.log('🎯 [加载] 解析后的更名记录:', renameData);
                    console.log('🎯 [加载] 更名记录中的new_name:', renameData.new_name);
                    
                    const timeDiff = Date.now() - renameData.timestamp;
                    console.log('🎯 [加载] 时间差:', timeDiff, 'ms');
                    
                    // 验证数据有效性
                    if (renameData.new_name && typeof renameData.new_name === 'string' && renameData.new_name.trim() !== '') {
                        if (timeDiff < 5 * 60 * 1000) { // 5分钟内
                            recentRenameName = renameData.new_name.trim();
                            console.log('🎯 [加载] 发现最近更名结果:', recentRenameName);
                        } else {
                            console.log('🎯 [加载] 更名记录过期');
                            localStorage.removeItem('lastRenameResult');
                        }
                    } else {
                        console.error('❌ [加载] 更名记录中的名称无效:', renameData.new_name);
                        localStorage.removeItem('lastRenameResult');
                    }
                }
            } catch (e) {
                console.error('❌ [加载] 检查更名结果失败:', e);
                localStorage.removeItem('lastRenameResult');
            }
            
            try {
                // 🔧 优化：使用新的统一用户数据API
                console.log('🎯 [加载] 开始调用统一用户数据API...');
                const response = await fetch(window.GameConfig ? window.GameConfig.getApiUrl('cultivation.php?action=get_user_data') : '../src/api/cultivation.php?action=get_user_data');

                console.log('🎯 [加载] API响应状态:', response.status);

                const data = await response.json();
                console.log('🎯 [加载] API返回数据:', data);
                
                if (data.success) {
                    console.log('✅ [加载] 数据获取成功');

                    // 🔧 优化：使用新的统一API响应格式
                    currentUser = {
                        id: data.user_id || 1,
                        username: data.username || '修仙者',
                        character_name: data.character_name || '修仙者',
                        character_avatar: data.character_avatar || data.avatar_image || 'ck.png',
                        level: data.attributes?.level || 1,
                        gold: data.attributes?.gold || 0,
                        spirit_stones: data.attributes?.spirit_stones || 0,
                        avatar_frame: data.avatar_frame || 'base1.png',
                        cultivation_realm: {
                            realm_name: data.attributes?.current_realm || '开光期一阶',
                            level: data.attributes?.realm_level || 1
                        }
                    };

                    console.log('🎯 [加载] 构建的用户数据:', currentUser);
                    
                    // 如果有最近的更名结果，使用更名后的名称
                    if (recentRenameName) {
                        console.log('🎯 [加载] 使用最近更名结果覆盖API数据');
                        currentUser.character_name = recentRenameName;
                    }
                    
                    console.log('🎯 [加载] 设置currentUser:', currentUser);
                    updateUserDisplay(currentUser);
                } else {
                    console.log('❌ [加载] API返回失败，使用默认数据:', data.message);
                    // 如果API失败，使用默认数据
                    currentUser = {
                        id: 1,
                        username: recentRenameName || '修仙者',
                        character_name: recentRenameName || '修仙者',
                        character_avatar: 'ck.png',
                        avatar_frame: 'base1.png',
                        level: 1,
                        gold: 0,
                        spirit_stones: 100,
                        cultivation_realm: {
                            realm_name: '开光期一阶',
                            level: 1
                        }
                    };
                    console.log('🎯 [加载] 使用默认数据:', currentUser);
                    updateUserDisplay(currentUser);
                }
            } catch (error) {
                console.error('❌ [加载] 网络错误:', error);
                // 网络错误时使用基础默认数据
                currentUser = {
                    id: 1,
                    username: recentRenameName || '修仙者',
                    character_name: recentRenameName || '修仙者',
                    character_avatar: 'ck.png',
                    avatar_frame: 'base1.png',
                    level: 1,
                    gold: 0,
                    spirit_stones: 100,
                    cultivation_realm: {
                        realm_name: '开光期一阶',
                        level: 1
                    }
                };
                console.log('🎯 [加载] 错误后使用默认数据:', currentUser);
                updateUserDisplay(currentUser);
                showMessage('网络连接失败，数据可能不是最新的', 'error');
            }
        }

        // 更新用户显示
        function updateUserDisplay(user) {
            console.log('🎯 [显示] 开始更新用户显示');
            console.log('🎯 [显示] 用户数据:', user);
            
            // 🔧 安全检查：确保user参数有效
            if (!user) {
                console.error('❌ [显示] 用户数据为空，使用默认数据');
                user = {
                    username: '游客',
                    character_name: '游客',
                    level: 1,
                    gold: 0,
                    spirit_stones: 0
                };
            }
            
            // 优先使用角色名，如果角色名不同于用户名则使用角色名，否则使用用户名
            const displayName = (user.character_name && user.character_name !== user.username) 
                ? user.character_name 
                : user.username;
            
            console.log('🎯 [显示] 计算显示名称:', displayName);
            console.log('🎯 [显示] 用户名:', user.username);
            console.log('🎯 [显示] 角色名:', user.character_name);
            
            // 设置头像
            const avatarElement = document.getElementById('userAvatar');
            const avatarContentElement = document.getElementById('userAvatarContent');
            console.log('🎯 [显示] 头像元素:', avatarElement);
            console.log('🎯 [显示] 头像内容元素:', avatarContentElement);
            
            if (user.character_avatar) {
                // 使用角色头像图片
                avatarContentElement.style.backgroundImage = `url('assets/images/char/${user.character_avatar}')`;
                avatarContentElement.style.backgroundSize = 'cover';
                avatarContentElement.style.backgroundPosition = 'center 15%'; // 进一步向上定位到头部
                avatarContentElement.style.backgroundRepeat = 'no-repeat';
                avatarContentElement.classList.add('has-avatar'); // 添加特殊样式类
                avatarContentElement.textContent = ''; // 清空文字
                console.log('✅ [显示] 设置头像图片:', user.character_avatar);
            } else {
                // 使用默认文字头像
                avatarContentElement.style.backgroundImage = '';
                avatarContentElement.classList.remove('has-avatar'); // 移除特殊样式类
                avatarContentElement.textContent = displayName.charAt(0);
                console.log('✅ [显示] 设置文字头像:', displayName.charAt(0));
            }
            
            // 设置头像外框
            if (user.avatar_frame) {
                console.log('🎯 [显示] 设置头像外框:', user.avatar_frame);
                avatarElement.style.setProperty('--avatar-frame', `url('../images/head/${user.avatar_frame}')`);
            } else {
                console.log('🎯 [显示] 使用默认头像外框');
                avatarElement.style.setProperty('--avatar-frame', `url('../images/head/base1.png')`);
            }
            
            // 添加头像点击事件
            avatarElement.onclick = openAvatarCustomization;
            avatarElement.title = '点击自定义头像';
            
            // 设置用户名并添加点击提示
            const usernameElement = document.getElementById('username');
            console.log('🎯 [显示] 用户名元素:', usernameElement);
            console.log('🎯 [显示] 设置前的显示名称:', usernameElement ? usernameElement.textContent : 'null');
            
            if (usernameElement) {
                usernameElement.textContent = displayName;
                usernameElement.title = '点击更改道号';
                console.log('✅ [显示] 用户名显示已更新为:', displayName);
                console.log('🎯 [显示] 设置后的显示名称:', usernameElement.textContent);
            } else {
                console.error('❌ [显示] 找不到用户名元素 #username');
            }
            
            // 存储用户数据供更名功能使用
            window.currentUserData = user;
            console.log('✅ [显示] 用户数据已存储到window.currentUserData');
            
            // 🔧 修改：使用真实的修炼境界信息而不是老的等级系统
            let realmText = '凡人';
            let realmColor = '#ffffff';
            
            if (user.cultivation_realm && user.cultivation_realm.realm_name) {
                realmText = user.cultivation_realm.realm_name;
                console.log('🎯 [显示] 使用修炼系统境界:', realmText);
                
                // 根据境界名称设置颜色
                if (realmText.includes('开光')) {
                    realmColor = '#ffffff';
                } else if (realmText.includes('灵虚')) {
                    realmColor = '#c0c0c0';
                } else if (realmText.includes('辟谷')) {
                    realmColor = '#ffb6c1';
                } else if (realmText.includes('心动')) {
                    realmColor = '#87ceeb';
                } else if (realmText.includes('元化')) {
                    realmColor = '#9370db';
                } else if (realmText.includes('元婴')) {
                    realmColor = '#4169e1';
                } else if (realmText.includes('离合')) {
                    realmColor = '#9932cc';
                } else if (realmText.includes('空冥')) {
                    realmColor = '#8a2be2';
                } else if (realmText.includes('寂灭')) {
                    realmColor = '#4b0082';
                } else if (realmText.includes('大乘')) {
                    realmColor = '#800080';
                } else if (realmText.includes('渡劫')) {
                    realmColor = '#ff4500';
                } else if (realmText.includes('仙')) {
                    realmColor = '#ffd700';
                } else {
                    realmColor = '#00ffff'; // 仙界颜色
                }
            } else {
                console.log('🎯 [显示] 无修炼境界信息，使用老系统境界');
                // 如果没有修炼境界信息，使用老的等级系统作为后备
                realmText = RealmSystem.getLevelRealm(user.level || 1);
                realmColor = RealmSystem.getRealmColor(user.level || 1);
            }
            
            console.log('🎯 [显示] 最终境界信息:', { realmText, realmColor });
            
            document.getElementById('userLevel').textContent = realmText;
            document.getElementById('userLevel').style.setProperty('--realm-color-start', realmColor);
            document.getElementById('userLevel').style.setProperty('--realm-color-end', shiftColor(realmColor, 30));
            
            document.getElementById('goldValue').textContent = formatNumber(user.gold);
            document.getElementById('spiritStoneValue').textContent = formatNumber(user.spirit_stones);
            
            console.log('✅ [显示] 用户显示更新完成');
        }

        // 🌙 游戏主页载入时处理离线修炼收益
        async function handleOfflineCultivationOnGameLoad() {
            console.log('🌙 [主页离线修炼] 开始处理离线修炼收益...');
            
            // 🔍 检查是否刚刚登录，决定是否显示弹窗
            const justLoggedIn = localStorage.getItem('just_logged_in');
            const loginTime = localStorage.getItem('login_time');
            let shouldShowModal = false;
            
            if (justLoggedIn === 'true' && loginTime) {
                const timeSinceLogin = Date.now() - parseInt(loginTime);
                // 如果登录时间在5分钟内，认为是刚登录，显示弹窗
                if (timeSinceLogin < 5 * 60 * 1000) {
                    shouldShowModal = true;
                    console.log('🌙 [主页离线修炼] 检测到刚登录，将显示离线收益弹窗');
                }
                
                // 清除登录标记，避免重复显示
                localStorage.removeItem('just_logged_in');
                localStorage.removeItem('login_time');
            } else {
                console.log('🌙 [主页离线修炼] 非登录状态，离线收益将静默增加');
            }
            
            try {
                const response = await fetch(window.GameConfig ? window.GameConfig.getApiUrl('cultivation.php?action=handle_offline_cultivation') : '../src/api/cultivation.php?action=handle_offline_cultivation', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('🌙 [主页离线修炼] API响应:', data);
                
                if (data.success) {
                    if (data.offline_cycles > 0) {
                        console.log(`🌙 [主页离线修炼] 获得收益: ${data.offline_qi_gained} 修为，${data.offline_cycles} 个周期`);
                        
                        if (shouldShowModal) {
                            // 🎉 显示离线修炼收益详情弹窗（仅登录时）
                            console.log('🌙 [主页离线修炼] 显示离线收益弹窗');
                            showOfflineRewardModal(data);
                        } else {
                            // 🔇 静默增加收益，不显示任何弹窗或消息
                            console.log('🌙 [主页离线修炼] 静默增加离线收益，更新进度条');
                            // 静默更新修炼进度条
                            if (window.cultivationProgressSystem) {
                                await window.cultivationProgressSystem.loadCultivationProgress();
                                window.cultivationProgressSystem.updateProgressDisplay();
                            }
                        }
                        
                    } else {
                        console.log('🌙 [主页离线修炼] 无离线收益');
                    }
                
                } else {
                    console.error('🌙 [主页离线修炼] 处理失败:', data.message);
                    // 不显示错误消息，避免影响用户体验
                }
                
            } catch (error) {
                console.error('🌙 [主页离线修炼] 网络错误:', error);
                // 不显示错误消息，离线修炼失败不影响正常使用
            }
        }
        
        // 🆕 显示离线修炼收益弹窗
        function showOfflineRewardModal(rewardData) {
            // 创建弹窗HTML
            const modalHtml = `
                <div class="offline-reward-modal" id="offlineRewardModal" style="
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
                    display: flex;
        justify-content: center;
        align-items: center;
                    z-index: 1003;
        backdrop-filter: blur(5px);
    ">
        <div style="
            background: linear-gradient(135deg, rgba(26, 26, 46, 0.95), rgba(22, 33, 62, 0.95));
            border-radius: 20px;
                        padding: 25px;
            width: 90%;
                        max-width: 400px;
                        border: 2px solid rgba(255, 193, 7, 0.6);
                        box-shadow: 0 0 30px rgba(255, 193, 7, 0.4);
            text-align: center;
            color: white;
        ">
                        <div style="font-size: 18px; font-weight: bold; color: #ffd700; margin-bottom: 20px;">
                            🌙 离线修炼收益 🌙
            </div>
            
                        <div style="margin-bottom: 15px; font-size: 14px; line-height: 1.6;">
                            <div style="margin-bottom: 8px;">
                                ⏰ 离线时间: <span style="color: #4CAF50; font-weight: bold;">${rewardData.offline_time_hours}小时</span>
                            </div>
                            <div style="margin-bottom: 8px;">
                                🔄 修炼周期: <span style="color: #4CAF50; font-weight: bold;">${rewardData.offline_cycles}次</span>
                            </div>
                            <div style="margin-bottom: 8px;">
                                ⚡ 修为获得: <span style="color: #ffd700; font-weight: bold; font-size: 16px;">+${rewardData.offline_qi_gained}</span>
                            </div>
                            ${rewardData.technique_exp_gained > 0 ? `
                                <div style="margin-bottom: 8px;">
                                    📖 功法经验: <span style="color: #9c27b0; font-weight: bold;">+${rewardData.technique_exp_gained}</span>
                                </div>
                            ` : ''}
                            ${rewardData.technique_updated ? `
                                <div style="margin-bottom: 8px; color: #ffd700; font-weight: bold;">
                                    🎉 功法升级了！
                                </div>
                            ` : ''}
                            ${rewardData.is_limited ? `
                                <div style="margin-bottom: 8px; color: #ff9800; font-size: 12px;">
                                    ⚠️ 已达到48小时修炼上限
                                </div>
                            ` : ''}
                        </div>
                        
                        <button onclick="closeOfflineRewardModal()" style="
                            background: linear-gradient(135deg, #4CAF50, #45a049);
                            border: none;
                            border-radius: 8px;
                            color: white;
                            padding: 10px 20px;
                            font-size: 14px;
                            cursor: pointer;
                            transition: all 0.3s ease;
                        " onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                            确定
                        </button>
                    </div>
                </div>
            `;
            
            // 添加到页面
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            
            // 添加动画效果
            const modal = document.getElementById('offlineRewardModal');
            modal.style.opacity = '0';
            modal.style.transform = 'scale(0.8)';
            
            requestAnimationFrame(() => {
                modal.style.transition = 'all 0.3s ease';
                modal.style.opacity = '1';
                modal.style.transform = 'scale(1)';
            });
        }
        
        // 关闭离线修炼收益弹窗
        function closeOfflineRewardModal() {
            const modal = document.getElementById('offlineRewardModal');
            if (modal) {
                modal.style.transition = 'all 0.3s ease';
                modal.style.opacity = '0';
                modal.style.transform = 'scale(0.8)';
                
                setTimeout(() => {
                    modal.remove();
                }, 300);
            }
        }

        // 格式化数字显示
        function formatNumber(num) {
            // 🔧 安全检查：处理undefined、null或非数字值
            if (num === undefined || num === null || isNaN(num)) {
                return '0';
            }
            
            // 确保是数字类型
            num = Number(num);
            
            if (num >= 10000) {
                return (num / 10000).toFixed(1) + '万';
            }
            return num.toString();
        }

        // 辅助函数：调整颜色亮度
        function shiftColor(hex, percent) {
            let r = parseInt(hex.substring(1,3), 16);
            let g = parseInt(hex.substring(3,5), 16);
            let b = parseInt(hex.substring(5,7), 16);

            r = parseInt(r * (100 + percent) / 100);
            g = parseInt(g * (100 + percent) / 100);
            b = parseInt(b * (100 + percent) / 100);

            r = (r < 255) ? r : 255;
            g = (g < 255) ? g : 255;
            b = (b < 255) ? b : 255;

            const rr = ((r.toString(16).length == 1) ? "0" + r.toString(16) : r.toString(16));
            const gg = ((g.toString(16).length == 1) ? "0" + g.toString(16) : g.toString(16));
            const bb = ((b.toString(16).length == 1) ? "0" + b.toString(16) : b.toString(16));

            return "#" + rr + gg + bb;
        }

        // 添加触摸反馈效果
        function addTouchFeedback() {
            document.querySelectorAll('.function-button, .cultivation-platform, .nav-btn').forEach(element => {
                element.addEventListener('touchstart', function() {
                    this.style.transform = this.style.transform.replace('scale(1.1)', 'scale(1.05)');
                }, { passive: true });
                
                element.addEventListener('touchend', function() {
                    // 恢复原始transform
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                }, { passive: true });
            });
        }

        // 功能按钮点击事件
        function openCultivation() {
            showMessage('进入修炼界面...', 'info');
            window.location.href = 'cultivation.html';
        }

        function openRealmBreakthrough() {
            // 境界系统已合并到修炼系统中
            showMessage('境界功能已合并到修炼系统中...', 'info');
            setTimeout(() => {
                window.location.href = 'cultivation.html';
            }, 1000);
        }

        function openShop() {
            console.log('🏪 [商城] 跳转到商城页面');
            window.location.href = 'shop.html';
        }

        function openSpiritRootSystem() {
            window.location.href = 'spirit_root.html';
        }

        function openDungeon() {
            showMessage('秘境功能开发中...', 'info');
            // TODO: 跳转到秘境界面
        }

        function openGuild() {
            showMessage('宗门功能开发中...', 'info');
            // TODO: 跳转到宗门界面
        }

        function openRanking() {
            showMessage('排行榜功能开发中...', 'info');
            // TODO: 跳转到排行榜界面
        }

        function openSettings() {
            showMessage('设置功能开发中...', 'info');
            // TODO: 跳转到设置界面
        }

        // 更名相关变量
        let renameInfo = null;
        
        // 打开更名弹窗
        function openRenameModal() {
            console.log('🎯 [更名] 开始打开更名弹窗');
            console.log('🎯 [更名] 当前用户数据:', window.currentUserData);
            
            if (!window.currentUserData) {
                console.error('❌ [更名] 用户数据不存在，未登录');
                showMessage('请先登录', 'error');
                return;
            }
            
            console.log('🎯 [更名] 开始获取更名信息...');
            
            // 获取更名信息
            fetch(window.GameConfig ? window.GameConfig.getApiUrl('rename_character.php?action=get_rename_info') : '../src/api/rename_character.php?action=get_rename_info')
                .then(response => {
                    console.log('🎯 [更名] API响应状态:', response.status);
                    console.log('🎯 [更名] API响应头:', response.headers);
                    return response.json();
                })
                .then(data => {
                    console.log('🎯 [更名] API返回数据:', data);
                    
                    if (data.success) {
                        console.log('✅ [更名] 获取更名信息成功');
                        renameInfo = data.data;
                        console.log('🎯 [更名] 更名信息:', renameInfo);
                        showRenameModal();
                    } else {
                        console.error('❌ [更名] 获取更名信息失败:', data.message);
                        if (data.message === '请先登录') {
                            showMessage('请先登录后再使用更名功能', 'error');
                            // 可以跳转到登录页面
                            setTimeout(() => {
                                window.location.href = 'login.html';
                            }, 2000);
                        } else {
                            showMessage('获取更名信息失败：' + data.message, 'error');
                        }
                    }
                })
                .catch(error => {
                    console.error('❌ [更名] 获取更名信息网络错误:', error);
                    showMessage('获取更名信息失败，请稍后重试', 'error');
                });
        }
        
        // 显示更名弹窗
        function showRenameModal() {
            console.log('🎯 [更名] 开始显示更名弹窗');
            console.log('🎯 [更名] renameInfo数据:', renameInfo);
            
            const overlay = document.getElementById('renameOverlay');
            const currentEl = document.getElementById('renameCurrent');
            const costEl = document.getElementById('renameCost');
            const costTextEl = document.getElementById('renameCostText');
            const confirmBtn = document.getElementById('renameConfirm');
            const inputEl = document.getElementById('renameInput');
            
            console.log('🎯 [更名] DOM元素检查:', {
                overlay: !!overlay,
                currentEl: !!currentEl,
                costEl: !!costEl,
                costTextEl: !!costTextEl,
                confirmBtn: !!confirmBtn,
                inputEl: !!inputEl
            });
            
            // 设置当前道号
            currentEl.textContent = '当前道号：' + renameInfo.current_name;
            console.log('🎯 [更名] 设置当前道号:', renameInfo.current_name);
            
            // 设置费用信息
            if (renameInfo.is_free_rename) {
                console.log('🎯 [更名] 首次免费更名');
                costEl.className = 'rename-cost free';
                costTextEl.textContent = '首次更名免费';
                confirmBtn.disabled = false;
            } else {
                console.log('🎯 [更名] 付费更名，灵石:', renameInfo.current_spirit_stones, '需要:', renameInfo.rename_price);
                costEl.className = 'rename-cost paid';
                if (renameInfo.can_afford) {
                    console.log('✅ [更名] 灵石足够');
                    costTextEl.textContent = '更名费用：' + renameInfo.rename_price + '灵石（当前：' + renameInfo.current_spirit_stones + '灵石）';
                    confirmBtn.disabled = false;
                } else {
                    console.log('❌ [更名] 灵石不足');
                    costTextEl.textContent = '灵石不足！需要：' + renameInfo.rename_price + '灵石（当前：' + renameInfo.current_spirit_stones + '灵石）';
                    confirmBtn.disabled = true;
                }
            }
            
            // 清空输入框
            inputEl.value = '';
            console.log('🎯 [更名] 清空输入框');
            
            // 显示弹窗
            overlay.style.display = 'flex';
            console.log('🎯 [更名] 显示弹窗');
            
            // 聚焦输入框
            setTimeout(() => {
                inputEl.focus();
                console.log('🎯 [更名] 聚焦输入框');
            }, 100);
        }
        
        // 关闭更名弹窗
        function closeRenameModal() {
            document.getElementById('renameOverlay').style.display = 'none';
        }
        
        // 确认更名
        function confirmRename() {
            console.log('🎯 [更名] 开始确认更名');
            
            const inputEl = document.getElementById('renameInput');
            const confirmBtn = document.getElementById('renameConfirm');
            const newName = inputEl.value.trim();
            
            console.log('🎯 [更名] 输入的新名称:', newName);
            console.log('🎯 [更名] 当前名称:', renameInfo.current_name);
            
            if (!newName) {
                console.log('❌ [更名] 新名称为空');
                showMessage('请输入新的道号', 'error');
                inputEl.focus();
                return;
            }
            
            if (newName.length < 2) {
                console.log('❌ [更名] 新名称太短:', newName.length);
                showMessage('道号至少需要2个字符', 'error');
                inputEl.focus();
                return;
            }
            
            if (newName.length > 8) {
                console.log('❌ [更名] 新名称太长:', newName.length);
                showMessage('道号不能超过8个字符', 'error');
                inputEl.focus();
                return;
            }
            
            if (newName === renameInfo.current_name) {
                console.log('❌ [更名] 新名称与当前名称相同');
                showMessage('新道号与当前道号相同', 'error');
                inputEl.focus();
                return;
            }
            
            // 检查字符是否合法
            if (!/^[\u4e00-\u9fa5a-zA-Z0-9_]+$/.test(newName)) {
                console.log('❌ [更名] 新名称包含非法字符:', newName);
                showMessage('道号只能包含中文、英文、数字和下划线', 'error');
                inputEl.focus();
                return;
            }
            
            console.log('✅ [更名] 名称验证通过');
            
            // 确认更名
            const confirmMessage = renameInfo.is_free_rename 
                ? '确定要将道号更改为"' + newName + '"吗？（首次更名免费）'
                : '确定要将道号更改为"' + newName + '"吗？\n将消耗' + renameInfo.rename_price + '灵石';
                
            console.log('🎯 [更名] 显示确认对话框:', confirmMessage);
            if (!confirm(confirmMessage)) {
                console.log('🎯 [更名] 用户取消更名');
                return;
            }
            
            console.log('🎯 [更名] 用户确认更名，开始发送请求');
            
            // 禁用按钮，显示加载状态
            confirmBtn.disabled = true;
            confirmBtn.textContent = '更名中...';
            confirmBtn.className = 'rename-button rename-confirm rename-loading';
            console.log('🎯 [更名] 按钮状态设置为加载中');
            
            // 构建请求数据
            const requestData = {
                action: 'rename_character',
                new_name: newName
            };
            console.log('🎯 [更名] 请求数据:', requestData);
            
            // 发送更名请求
            fetch(window.GameConfig ? window.GameConfig.getApiUrl('rename_character.php') : '../src/api/rename_character.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            })
            .then(response => {
                console.log('🎯 [更名] 更名API响应状态:', response.status);
                console.log('🎯 [更名] 更名API响应类型:', response.headers.get('content-type'));
                
                // 先获取原始文本，然后解析
                return response.text();
            })
            .then(responseText => {
                console.log('🎯 [更名] API原始响应文本:', responseText);
                console.log('🎯 [更名] 响应文本长度:', responseText.length);
                console.log('🎯 [更名] 响应文本类型:', typeof responseText);
                
                // 尝试解析JSON
                let data;
                try {
                    data = JSON.parse(responseText);
                    console.log('🎯 [更名] JSON解析成功:', data);
                } catch (parseError) {
                    console.error('❌ [更名] JSON解析失败:', parseError);
                    console.error('❌ [更名] 无法解析的文本:', responseText);
                    throw new Error('API返回的不是有效的JSON格式');
                }
                
                return data;
            })
            .then(data => {
                console.log('🎯 [更名] 更名API返回数据:', data);
                console.log('🎯 [更名] API返回的完整数据结构:', JSON.stringify(data, null, 2));
                console.log('🎯 [更名] data.data内容:', data.data);
                console.log('🎯 [更名] new_name字段:', data.data ? data.data.new_name : 'data.data不存在');
                
                if (data.success) {
                    console.log('✅ [更名] 更名成功!');
                    console.log('🎯 [更名] 响应消息:', data.message);
                    
                    showMessage(data.message, 'success');
                    
                    // 更新显示的用户名
                    const usernameElement = document.getElementById('username');
                    console.log('🎯 [更名] 更新前的用户名元素:', usernameElement);
                    console.log('🎯 [更名] 更新前的显示名称:', usernameElement ? usernameElement.textContent : 'null');
                    
                    // 安全获取新名称
                    let newDisplayName = null;
                    if (data.data && data.data.new_name) {
                        newDisplayName = data.data.new_name;
                    } else if (data.new_name) {
                        newDisplayName = data.new_name;
                    } else {
                        console.error('❌ [更名] 无法从API响应中获取新名称');
                        showMessage('更名响应数据错误', 'error');
                        return;
                    }
                    
                    console.log('🎯 [更名] 解析出的新名称:', newDisplayName);
                    
                    if (usernameElement && newDisplayName) {
                        usernameElement.textContent = newDisplayName;
                        console.log('✅ [更名] 用户名显示已更新为:', newDisplayName);
                        console.log('🎯 [更名] 更新后的显示名称:', usernameElement.textContent);
                    } else {
                        console.error('❌ [更名] 找不到用户名元素或新名称为空');
                        console.error('❌ [更名] usernameElement:', usernameElement);
                        console.error('❌ [更名] newDisplayName:', newDisplayName);
                    }
                    
                    // 更新用户数据 - 修复：确保用户数据完整性
                    if (window.currentUserData && newDisplayName) {
                        console.log('🎯 [更名] 更新前的用户数据:', window.currentUserData);
                        window.currentUserData.character_name = newDisplayName;
                        console.log('✅ [更名] 用户数据已更新:', window.currentUserData);
                    } else {
                        console.error('❌ [更名] window.currentUserData 不存在，重新创建');
                        if (newDisplayName) {
                            // 如果用户数据不存在，重新创建一个基本的用户数据对象
                            window.currentUserData = {
                                character_name: newDisplayName,
                                username: newDisplayName, // 临时使用新名称
                                character_avatar: 'ck.png',
                                avatar_frame: 'base1.png',
                                level: 1,
                                gold: 130560,
                                spirit_stones: 0
                            };
                            console.log('✅ [更名] 重新创建用户数据:', window.currentUserData);
                        }
                    }
                    
                    // 强制保存更名结果到localStorage，防止页面刷新丢失
                    if (newDisplayName) {
                        try {
                            const renameResult = {
                                new_name: newDisplayName,
                                timestamp: Date.now()
                            };
                            console.log('🎯 [更名] 准备保存到localStorage的数据:', renameResult);
                            
                            localStorage.setItem('lastRenameResult', JSON.stringify(renameResult));
                            console.log('✅ [更名] 更名结果已保存到localStorage');
                            
                            // 验证保存是否成功
                            const saved = localStorage.getItem('lastRenameResult');
                            const parsed = JSON.parse(saved);
                            console.log('🎯 [更名] 验证保存的数据:', parsed);
                            console.log('🎯 [更名] 保存的新名称:', parsed.new_name);
                        } catch (e) {
                            console.error('❌ [更名] 无法保存到localStorage:', e);
                        }
                    } else {
                        console.error('❌ [更名] 新名称为空，无法保存到localStorage');
                    }
                    
                    // 关闭弹窗
                    console.log('🎯 [更名] 关闭更名弹窗');
                    closeRenameModal();
                } else {
                    console.error('❌ [更名] 更名失败:', data.message);
                    if (data.message === '请先登录') {
                        showMessage('登录状态已过期，请重新登录', 'error');
                        closeRenameModal();
                        setTimeout(() => {
                            window.location.href = 'login.html';
                        }, 2000);
                    } else {
                        showMessage('更名失败：' + data.message, 'error');
                    }
                }
            })
            .catch(error => {
                console.error('❌ [更名] 更名请求网络错误:', error);
                showMessage('更名失败，请稍后重试', 'error');
            })
            .finally(() => {
                console.log('🎯 [更名] 恢复按钮状态');
                // 恢复按钮状态
                confirmBtn.disabled = renameInfo && !renameInfo.can_afford;
                confirmBtn.textContent = '确认更名';
                confirmBtn.className = 'rename-button rename-confirm';
            });
        }
        
        // 点击弹窗外部关闭
        document.addEventListener('click', function(event) {
            const overlay = document.getElementById('renameOverlay');
            const modal = document.querySelector('.rename-modal');
            
            if (overlay && overlay.style.display === 'flex') {
                if (event.target === overlay) {
                    closeRenameModal();
                }
            }
        });
        
        // ESC键关闭弹窗
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const overlay = document.getElementById('renameOverlay');
                if (overlay && overlay.style.display === 'flex') {
                    closeRenameModal();
                }
            }
        });
        
        // 回车键确认更名
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Enter') {
                const overlay = document.getElementById('renameOverlay');
                if (overlay && overlay.style.display === 'flex') {
                    confirmRename();
                }
            }
        });

        // 禁用页面下拉刷新（移动端）
        document.addEventListener('touchmove', function(e) {
            e.preventDefault();
        }, { passive: false });

        // 禁用双指缩放
        document.addEventListener('gesturestart', function(e) {
            e.preventDefault();
        }, { passive: false });

        document.addEventListener('gesturechange', function(e) {
            e.preventDefault();
        }, { passive: false });

        document.addEventListener('gestureend', function(e) {
            e.preventDefault();
        }, { passive: false });

        // 显示消息提示
        function showMessage(text, type = 'info') {
            // 移除之前的消息
            const existingMessage = document.querySelector('.message');
            if (existingMessage) {
                existingMessage.remove();
            }
            
            // 创建新消息
            const message = document.createElement('div');
            message.className = `message ${type}`;
            message.textContent = text;
            document.body.appendChild(message);
            
            // 3秒后自动移除
            setTimeout(() => {
                if (message.parentNode) {
                    message.parentNode.removeChild(message);
                }
            }, 3000);
        }

        // 手动清理localStorage的函数（调试用）
        function clearRenameCache() {
            localStorage.removeItem('lastRenameResult');
            console.log('🧹 [清理] 手动清除更名缓存');
            showMessage('更名缓存已清除', 'info');
        }
        
        // 🌟 修炼进度环系统
        let cultivationProgressSystem = {
            currentExp: 0,
            maxExp: 100,
            progressInterval: null,
            isAutoUpdating: false,
            soulStatus: null, // 🆕 添加魂力状态
            
            // 初始化进度环系统
            init: async function() {
                console.log('🌟 [修炼进度] 初始化进度环系统');
                await this.loadCultivationProgress();
                this.updateProgressDisplay();
                this.startAutoUpdate();
            },
            
            // 从API加载修炼进度
            loadCultivationProgress: async function() {
                try {
                    console.log('🌟 [修炼进度] 获取修炼进度数据...');
                    
                    // 🆕 同时获取修炼进度和魂力状态
                    const [cultivationResponse, soulResponse] = await Promise.all([
                        fetch(window.GameConfig ? window.GameConfig.getApiUrl('cultivation.php?action=get_cultivation_info') : '../src/api/cultivation.php?action=get_cultivation_info'),
                        fetch(window.GameConfig ? window.GameConfig.getApiUrl('cultivation.php?action=get_soul_status') : '../src/api/cultivation.php?action=get_soul_status')
                    ]);
                    
                    const cultivationData = await cultivationResponse.json();
                    const soulData = await soulResponse.json();
                    
                    console.log('🌟 [修炼进度] 修炼API返回完整数据:', cultivationData);
                    console.log('🔮 [魂力状态] 魂力API返回数据:', soulData);
                    
                    // 处理修炼进度数据
                    if (cultivationData.success && cultivationData.cultivation && cultivationData.cultivation.current_realm) {
                        const currentRealm = cultivationData.cultivation.current_realm;
                        this.currentExp = parseInt(currentRealm.current_qi) || 0;
                        this.maxExp = parseInt(currentRealm.exp_required) || 100;
                        
                        console.log('✅ [修炼进度] 真实进度加载成功:', {
                            current: this.currentExp,
                            max: this.maxExp,
                            percentage: ((this.currentExp / this.maxExp) * 100).toFixed(1) + '%',
                            realm: currentRealm.realm_name || '未知'
                        });
                    } else {
                        console.log('❌ [修炼进度] 获取进度失败，API返回:', cultivationData);
                        console.log('❌ [修炼进度] 使用测试数据');
                        // 使用测试数据
                        this.currentExp = 75;
                        this.maxExp = 100;
                    }
                    
                    // 🆕 处理魂力状态数据
                    if (soulData.success && soulData.soul_status) {
                        this.soulStatus = soulData.soul_status;
                        console.log('✅ [魂力状态] 魂力状态加载成功:', {
                            power: this.soulStatus.current_power,
                            damaged: this.soulStatus.is_damaged,
                            timeToFull: this.soulStatus.time_to_full
                        });
                    } else {
                        console.log('❌ [魂力状态] 获取魂力状态失败:', soulData.message);
                        // 使用默认正常状态
                        this.soulStatus = {
                            current_power: 100,
                            is_damaged: false,
                            time_to_full: 0
                        };
                    }
                    
                } catch (error) {
                    console.error('❌ [修炼进度] 网络错误:', error);
                    // 网络错误时使用测试数据
                    this.currentExp = 50;
                    this.maxExp = 100;
                    this.soulStatus = {
                        current_power: 100,
                        is_damaged: false,
                        time_to_full: 0
                    };
                }
            },
            
            // 更新进度显示
            updateProgressDisplay: function() {
                const progressCircle = document.getElementById('cultivationProgressCircle');
                const progressText = document.getElementById('cultivationProgressText');
                const platform = document.querySelector('.cultivation-platform');
                
                if (!progressCircle || !progressText) {
                    console.error('❌ [修炼进度] 找不到进度显示元素');
                    return;
                }
                
                let percentage, displayText;
                
                // 🆕 根据魂力状态决定显示内容
                if (this.soulStatus && this.soulStatus.is_damaged) {
                    // 魂力受损：显示魂力恢复进度
                    percentage = Math.min(this.soulStatus.current_power || 0, 100);
                    displayText = `${Math.floor(percentage)}%`;
                    
                    console.log('🩸 [魂力进度] 显示魂力恢复进度:', {
                        soulPower: this.soulStatus.current_power,
                        percentage: percentage.toFixed(1) + '%',
                        timeToFull: this.soulStatus.time_to_full
                    });
                } else {
                    // 魂力正常：显示修炼进度
                    percentage = Math.min((this.currentExp / this.maxExp) * 100, 100);
                    displayText = Math.floor(percentage) + '%';
                    
                    console.log('🌟 [修炼进度] 显示修炼进度:', {
                        current: this.currentExp,
                        max: this.maxExp,
                        percentage: percentage.toFixed(1) + '%'
                    });
                }
                
                // 计算SVG路径长度 (2πr，这里r=45)
                const circumference = 2 * Math.PI * 45; // 约283
                const dashOffset = circumference - (circumference * percentage / 100);
                
                // 更新进度环
                progressCircle.style.strokeDasharray = circumference;
                progressCircle.style.strokeDashoffset = dashOffset;
                
                // 更新进度文字
                progressText.textContent = displayText;
                
                // 🆕 根据魂力状态改变光圈颜色和效果
                if (this.soulStatus && this.soulStatus.is_damaged) {
                    // 魂力受损：红色光圈
                    progressCircle.classList.remove('full');
                    progressCircle.classList.add('soul-damaged');
                    platform.classList.remove('full');
                    platform.classList.add('soul-damaged');
                    
                    console.log('🩸 [魂力状态] 魂力受损，显示红色光圈，魂力恢复进度:', percentage.toFixed(1) + '%');
                } else {
                    // 魂力正常：根据修炼进度显示金色光圈
                    progressCircle.classList.remove('soul-damaged');
                    platform.classList.remove('soul-damaged');
                    
                    if (percentage >= 100) {
                        progressCircle.classList.add('full');
                        platform.classList.add('full');
                        console.log('🌟 [修炼进度] 修炼值已满，开启发光效果');
                    } else {
                        progressCircle.classList.remove('full');
                        platform.classList.remove('full');
                    }
                }
                
                console.log('🌟 [进度更新] 最终显示:', {
                    type: this.soulStatus && this.soulStatus.is_damaged ? '魂力恢复' : '修炼进度',
                    percentage: percentage.toFixed(1) + '%',
                    displayText: displayText,
                    dashOffset: dashOffset.toFixed(1),
                    soulDamaged: this.soulStatus ? this.soulStatus.is_damaged : false
                });
            },
            
            // 开始自动更新（每30秒刷新真实数据）
            startAutoUpdate: function() {
                if (this.isAutoUpdating) {
                    console.log('🌟 [修炼进度] 自动更新已启动，跳过');
                    return;
                }
                
                console.log('🌟 [修炼进度] 开始自动更新（每30秒刷新真实数据）');
                this.isAutoUpdating = true;
                
                this.progressInterval = setInterval(async () => {
                    // 重新获取真实的修炼进度数据
                    await this.loadCultivationProgress();
                    this.updateProgressDisplay();
                    console.log('🌟 [修炼进度] 自动刷新修炼数据');
                }, 30000); // 30秒
            },
            
            // 停止自动更新
            stopAutoUpdate: function() {
                if (this.progressInterval) {
                    clearInterval(this.progressInterval);
                    this.progressInterval = null;
                    this.isAutoUpdating = false;
                    console.log('🌟 [修炼进度] 自动更新已停止');
                }
            },
            
            // 手动增加经验值（用于测试）
            addExp: function(amount = 1) {
                this.currentExp = Math.min(this.currentExp + amount, this.maxExp);
                this.updateProgressDisplay();
                console.log('🌟 [修炼进度] 手动增加经验值:', amount);
            },
            
            // 重置进度（用于测试）
            resetProgress: function() {
                this.currentExp = 0;
                this.updateProgressDisplay();
                console.log('🌟 [修炼进度] 进度已重置');
            }
        };

        // 头像外框自定义相关变量
        let selectedFrame = null;
        let currentFrame = 'base1.png';
        let availableFrames = [];
        
        // 打开头像外框选择弹窗
        function openAvatarCustomization() {
            console.log('🎯 [头像] 打开头像外框选择弹窗');
            
            if (!window.currentUserData) {
                showMessage('请先登录', 'error');
                return;
            }
            
            // 初始化当前选择
            currentFrame = window.currentUserData.avatar_frame || 'base1.png';
            selectedFrame = currentFrame;
            
            // 加载可用选项
            loadFrameOptions();
            
            // 显示弹窗
            document.getElementById('avatarCustomizationOverlay').style.display = 'flex';
        }
        
        // 关闭头像外框选择弹窗
        function closeAvatarCustomization() {
            document.getElementById('avatarCustomizationOverlay').style.display = 'none';
            // 重置选择
            selectedFrame = currentFrame;
        }
        
        // 加载头像外框选项
        async function loadFrameOptions() {
            try {
                console.log('🎯 [头像框] 开始加载头像外框选项...');
                
                // 调用API获取可用头像外框
                const response = await fetch(window.GameConfig ? window.GameConfig.getApiUrl('equipment_integrated.php?action=get_available_frames') : '../src/api/equipment_integrated.php?action=get_available_frames');
                const data = await response.json();
                
                console.log('🎯 [头像框] API返回数据:', data);
                
                if (data.success) {
                    availableFrames = data.frames;
                    console.log('✅ [头像框] 头像外框列表加载成功:', availableFrames.length, '个');
                } else {
                    console.error('❌ [头像框] 获取头像外框列表失败:', data.message);
                    // 使用默认数据作为后备
                    availableFrames = [
                        { filename: 'base1.png', name: '基础外框', unlocked: true },
                        { filename: 'base2.png', name: '青铜外框', unlocked: true },
                        { filename: 'base3.png', name: '白银外框', unlocked: true },
                        { filename: 'base4.png', name: '黄金外框', unlocked: true },
                        { filename: 'base5.png', name: '钻石外框', unlocked: true },
                        { filename: 'base6.png', name: '传说外框', unlocked: true },
                        { filename: 'wlmz1.png', name: '武林盟主外框', unlocked: false },
                        { filename: 'wlmz2.png', name: '至尊盟主外框', unlocked: false }
                    ];
                }
                
                renderFrameOptions();
                updateCurrentFrameDisplay();
                
            } catch (error) {
                console.error('❌ [头像框] 加载头像外框选项失败:', error);
                showMessage('加载头像外框选项失败', 'error');
                
                // 使用默认数据作为后备
                availableFrames = [
                    { filename: 'base1.png', name: '基础外框', unlocked: true },
                    { filename: 'base2.png', name: '青铜外框', unlocked: true },
                    { filename: 'base3.png', name: '白银外框', unlocked: true },
                    { filename: 'base4.png', name: '黄金外框', unlocked: true },
                    { filename: 'base5.png', name: '钻石外框', unlocked: true },
                    { filename: 'base6.png', name: '传说外框', unlocked: true },
                    { filename: 'wlmz1.png', name: '武林盟主外框', unlocked: false },
                    { filename: 'wlmz2.png', name: '至尊盟主外框', unlocked: false }
                ];
                renderFrameOptions();
                updateCurrentFrameDisplay();
            }
        }
        
        // 渲染头像外框选项
        function renderFrameOptions() {
            const frameGrid = document.getElementById('frameGrid');
            frameGrid.innerHTML = '';
            
            availableFrames.forEach(frame => {
                const option = document.createElement('div');
                option.className = 'avatar-option';
                option.style.backgroundImage = `url('assets/images/head/${frame.filename}')`;
                option.dataset.frame = frame.filename;
                
                if (frame.filename === selectedFrame) {
                    option.classList.add('selected');
                }
                
                if (!frame.unlocked) {
                    option.classList.add('locked');
                    option.title = `需要：${frame.requirement}`;
                    option.onclick = () => showMessage(`需要${frame.requirement}才能解锁`, 'info');
                } else {
                    option.title = frame.name;
                    option.onclick = () => selectFrame(frame.filename, option);
                }
                
                frameGrid.appendChild(option);
            });
        }
        
        // 选择头像外框
        function selectFrame(filename, element) {
            // 移除其他选中状态
            document.querySelectorAll('#frameGrid .avatar-option.selected').forEach(el => {
                el.classList.remove('selected');
            });
            
            // 设置选中状态
            element.classList.add('selected');
            selectedFrame = filename;
            updateCurrentFrameDisplay();
        }
        
        // 更新当前外框显示
        function updateCurrentFrameDisplay() {
            const frameName = availableFrames.find(f => f.filename === selectedFrame)?.name || '默认';
            document.getElementById('frameCurrent').textContent = `当前外框：${frameName}`;
        }
        
        // 确认头像外框自定义
        async function confirmAvatarCustomization() {
            // 检查是否有变更
            if (selectedFrame === currentFrame) {
                console.log('🎯 [头像框] 没有变更，直接关闭');
                closeAvatarCustomization();
                return;
            }
            
            try {
                console.log('🎯 [头像框] 开始保存头像外框设置');
                console.log('🎯 [头像框] 当前外框:', currentFrame);
                console.log('🎯 [头像框] 选择外框:', selectedFrame);
                
                // 调用API保存用户的头像外框设置
                const formData = new FormData();
                formData.append('action', 'save_avatar_frame');
                formData.append('avatar_frame', selectedFrame);
                
                console.log('🎯 [头像框] 发送API请求...');
                const response = await fetch(window.GameConfig ? window.GameConfig.getApiUrl('equipment_integrated.php') : '../src/api/equipment_integrated.php', {
                    method: 'POST',
                    body: formData
                });
                
                console.log('🎯 [头像框] API响应状态:', response.status);
                const data = await response.json();
                console.log('🎯 [头像框] API返回数据:', data);
                
                if (data.success) {
                    console.log('✅ [头像框] 头像外框设置保存成功');
                    
                    // 更新用户数据
                    if (window.currentUserData) {
                        window.currentUserData.avatar_frame = selectedFrame;
                        console.log('🎯 [头像框] 更新用户数据:', window.currentUserData);
                    }
                    
                    // 立即更新CSS变量
                    const avatarElement = document.getElementById('userAvatar');
                    if (avatarElement) {
                        console.log('🎯 [头像框] 更新头像元素CSS变量');
                        avatarElement.style.setProperty('--avatar-frame', `url('../images/head/${selectedFrame}')`);
                    }
                    
                    // 更新界面显示
                    updateUserDisplay(window.currentUserData);
                    
                    // 更新当前值
                    currentFrame = selectedFrame;
                    
                    closeAvatarCustomization();
                    showMessage('头像外框设置保存成功！', 'success');
                } else {
                    console.error('❌ [头像框] 保存失败:', data.message);
                    showMessage(data.message || '保存头像外框设置失败', 'error');
                }
                
            } catch (error) {
                console.error('❌ [头像框] 保存头像外框设置失败:', error);
                showMessage('保存头像外框设置失败', 'error');
            }
        }
        
        // 点击弹窗外部关闭
        document.addEventListener('click', function(event) {
            const overlay = document.getElementById('avatarCustomizationOverlay');
            
            if (overlay && overlay.style.display === 'flex') {
                if (event.target === overlay) {
                    closeAvatarCustomization();
                }
            }
        });

        function openSpiritRootSystem() {
            window.location.href = 'spirit_root.html';
        }

        function openAlchemySystem() {
            console.log('🧪 [炼丹] 打开炼丹系统界面');
            showMessage('正在进入炼丹界面...', 'info');
            // 跳转到炼丹界面
            setTimeout(() => {
                window.location.href = 'alchemy.html';
            }, 500);
        }

        function openBeastSystem() {
            showMessage('异兽系统开发中...', 'info');
            // TODO: 跳转到异兽系统界面
        }

        // 🌟 修炼进度环调试功能（开发测试用）
        window.testAddExp = function(amount = 10) {
            cultivationProgressSystem.addExp(amount);
            showMessage(`增加了 ${amount} 点修炼值`, 'success');
        };

        window.testResetProgress = function() {
            cultivationProgressSystem.resetProgress();
            showMessage('修炼进度已重置', 'info');
        };

        window.testFillProgress = function() {
            cultivationProgressSystem.currentExp = cultivationProgressSystem.maxExp;
            cultivationProgressSystem.updateProgressDisplay();
            showMessage('修炼进度已满', 'success');
        };

        window.testRefreshProgress = function() {
            cultivationProgressSystem.loadCultivationProgress().then(() => {
                cultivationProgressSystem.updateProgressDisplay();
                showMessage('修炼进度已刷新', 'success');
            });
        };

        // 🆕 测试魂力受损状态
        window.testSoulDamaged = function(soulPower = 30) {
            // 模拟魂力受损状态，可以指定魂力值
            const power = Math.max(0, Math.min(100, soulPower)); // 确保在0-100范围内
            cultivationProgressSystem.soulStatus = {
                current_power: power,
                is_damaged: power < 100,
                time_to_full: power < 100 ? Math.ceil((100 - power) * 3) : 0 // 模拟恢复时间
            };
            cultivationProgressSystem.updateProgressDisplay();
            showMessage(`测试魂力受损状态 - 魂力${power}%（红色光圈）`, 'error');
        };

        // 🆕 测试魂力恢复状态
        window.testSoulHealed = function() {
            // 模拟魂力完全恢复状态
            cultivationProgressSystem.soulStatus = {
                current_power: 100,
                is_damaged: false,
                time_to_full: 0
            };
            cultivationProgressSystem.updateProgressDisplay();
            showMessage('测试魂力完全恢复状态（金色光圈）', 'success');
        };

        // 🆕 模拟魂力恢复过程
        window.testSoulRecovery = function() {
            let currentPower = 0;
            const recoveryInterval = setInterval(() => {
                currentPower += 10;
                if (currentPower > 100) {
                    currentPower = 100;
                    clearInterval(recoveryInterval);
                }
                
                cultivationProgressSystem.soulStatus = {
                    current_power: currentPower,
                    is_damaged: currentPower < 100,
                    time_to_full: currentPower < 100 ? Math.ceil((100 - currentPower) * 3) : 0
                };
                cultivationProgressSystem.updateProgressDisplay();
                
                if (currentPower >= 100) {
                    showMessage('魂力完全恢复！光圈变为金色', 'success');
                } else if (currentPower === 10) {
                    showMessage('开始模拟魂力恢复过程...', 'info');
                }
            }, 800); // 每0.8秒增加10%魂力
        };

        // 🌟 全局暴露修炼进度系统（用于调试）
        window.cultivationProgressSystem = cultivationProgressSystem;

        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', function() {
            if (cultivationProgressSystem) {
                cultivationProgressSystem.stopAutoUpdate();
            }
        });

        // 页面隐藏时暂停，显示时恢复（省电优化）
        document.addEventListener('visibilitychange', function() {
            if (cultivationProgressSystem) {
                if (document.hidden) {
                    console.log('🌟 [修炼进度] 页面隐藏，暂停自动更新');
                    cultivationProgressSystem.stopAutoUpdate();
                } else {
                    console.log('🌟 [修炼进度] 页面显示，恢复自动更新');
                    if (!cultivationProgressSystem.isAutoUpdating) {
                        cultivationProgressSystem.startAutoUpdate();
                    }
                }
            }
        });
    </script>
</body>
</html> 