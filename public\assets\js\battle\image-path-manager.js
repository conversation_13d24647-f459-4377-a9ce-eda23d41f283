/**
 * 战斗系统图片路径管理器
 * 统一处理所有图片路径问题
 */
class ImagePathManager {
    constructor() {
        // 🔧 修正：从battle.html的位置看，正确的基础路径
        this.basePath = 'assets/images/';
        
        // 🔧 新增：图片路径缓存，避免重复处理
        this.imagePathCache = new Map();
        
        console.log('🖼️ 图片路径管理器已初始化，基础路径:', this.basePath);
    }
    
    /**
     * 获取角色头像路径
     */
    getCharacterImage(imagePath) {
        // 🔧 优化：使用缓存避免重复处理相同路径
        if (this.imagePathCache.has(imagePath)) {
            return this.imagePathCache.get(imagePath);
        }
        
        console.log('🧑 获取角色图片:', imagePath);
        
        let finalPath;
        
        if (!imagePath) {
            finalPath = this.basePath + 'char/ck.png';
            console.log('📄 使用默认角色图片:', finalPath);
        }
        // 如果已经是完整路径，直接返回
        else if (imagePath.startsWith('assets/') || imagePath.startsWith('/') || imagePath.startsWith('http')) {
            finalPath = imagePath;
            console.log('🔗 完整路径，直接使用:', imagePath);
        }
        // 如果是相对路径（如../../images/char/xxx.png），转换为assets路径
        else if (imagePath.includes('../../images/')) {
            finalPath = imagePath.replace('../../images/', this.basePath);
            console.log('🔄 相对路径转换:', imagePath, '->', finalPath);
        }
        // 如果包含目录分隔符，在前面添加basePath
        else if (imagePath.includes('/')) {
            finalPath = this.basePath + imagePath;
            console.log('📁 目录路径处理:', imagePath, '->', finalPath);
        }
        else {
            // 纯文件名，添加char目录
            const cleanFileName = this.extractFileName(imagePath);
            finalPath = this.basePath + 'char/' + cleanFileName;
            
            // 如果文件名没有扩展名，添加.png
            if (!finalPath.includes('.')) {
                finalPath += '.png';
            }
            
            console.log('📄 文件名转换:', imagePath, '->', finalPath, '(添加目录: char/)');
        }
        
        // 🔧 缓存处理结果
        this.imagePathCache.set(imagePath, finalPath);
        
        // 🔧 自动限制缓存大小
        this.limitCacheSize(50);
        
        return finalPath;
    }
    
    /**
     * 获取敌人头像路径
     */
    getEnemyImage(imagePath) {
        // 🔧 优化：使用缓存避免重复处理相同路径
        if (this.imagePathCache.has(imagePath)) {
            return this.imagePathCache.get(imagePath);
        }
        
        console.log('👹 获取敌人图片:', imagePath);
        
        let finalPath;
        
        if (!imagePath) {
            finalPath = this.basePath + 'enemy/yelang.png';
            console.log('📄 使用默认敌人图片:', finalPath);
        }
        // 如果已经是完整路径，直接返回
        else if (imagePath.startsWith('assets/') || imagePath.startsWith('/') || imagePath.startsWith('http')) {
            finalPath = imagePath;
            console.log('🔗 完整路径，直接使用:', imagePath);
        }
        // 如果是相对路径（如../../images/enemy/xxx.png），转换为assets路径
        else if (imagePath.includes('../../images/')) {
            finalPath = imagePath.replace('../../images/', this.basePath);
            console.log('🔄 相对路径转换:', imagePath, '->', finalPath);
        }
        // 如果包含目录分隔符，在前面添加basePath
        else if (imagePath.includes('/')) {
            finalPath = this.basePath + imagePath;
            console.log('📁 目录路径处理:', imagePath, '->', finalPath);
        }
        else {
            // 纯文件名，需要进一步判断
            const cleanFileName = this.extractFileName(imagePath);
            console.log('📂 提取的敌人文件名:', cleanFileName);
            
            // 直接使用文件名生成路径
            finalPath = this.basePath + 'enemy/' + cleanFileName;
            
            // 如果文件名没有扩展名，添加.png
            if (!finalPath.includes('.')) {
                finalPath += '.png';
            }
            
            console.log('✅ 直接使用文件名生成路径:', finalPath);
        }
        
        // 🔧 缓存处理结果
        this.imagePathCache.set(imagePath, finalPath);
        
        // 🔧 自动限制缓存大小
        this.limitCacheSize(50);
        
        return finalPath;
    }
    
    /**
     * 获取武器图片路径
     */
    getWeaponImage(imagePath) {
        // 🔧 优化：使用缓存避免重复处理相同路径
        if (this.imagePathCache.has(imagePath)) {
            return this.imagePathCache.get(imagePath);
        }
        
        // 只在首次处理时输出日志
        console.log('⚔️ 获取武器图片:', imagePath);
        
        let finalPath;
        
        if (!imagePath) {
            finalPath = this.basePath + 'battle_sword.png';
            console.log('📄 使用默认武器图片:', finalPath);
        }
        // 🔧 修复：特殊处理equi/和weapon/路径前缀（数据库中的武器图片字段）
        else if (imagePath.startsWith('equi/')) {
            finalPath = this.basePath + imagePath; // assets/images/equi/xxx.png
            console.log('📂 equi/路径处理:', imagePath, '->', finalPath);
        }
        else if (imagePath.startsWith('weapon/')) {
            finalPath = this.basePath + imagePath; // assets/images/weapon/xxx.png
            console.log('📂 weapon/路径处理:', imagePath, '->', finalPath);
        }
        // 如果已经是完整路径，直接返回
        else if (imagePath.startsWith('assets/') || imagePath.startsWith('/') || imagePath.startsWith('http')) {
            finalPath = imagePath;
            console.log('🔗 完整路径，直接使用:', imagePath);
        }
        // 如果是相对路径（如../../images/）
        else if (imagePath.includes('../../images/')) {
            finalPath = imagePath.replace('../../images/', this.basePath);
            console.log('🔄 相对路径转换:', imagePath, '->', finalPath);
        }
        // 如果包含目录分隔符，在前面添加basePath
        else if (imagePath.includes('/')) {
            finalPath = this.basePath + imagePath;
            console.log('📁 目录路径处理:', imagePath, '->', finalPath);
        }
        else {
            // 纯文件名，需要进一步判断类型
            const cleanFileName = this.extractFileName(imagePath);
            
            // 根据文件名前缀判断目录
            if (cleanFileName.startsWith('bw_')) {
                // 武器图片，放在equi目录
                finalPath = this.basePath + 'equi/' + cleanFileName + '.png';
                console.log('⚔️ 武器文件名:', imagePath, '->', finalPath);
            } else if (cleanFileName.startsWith('weapon_') || cleanFileName.startsWith('battle_')) {
                // 特殊武器图片，放在根目录
                finalPath = this.basePath + cleanFileName + '.png';
                console.log('🗡️ 特殊武器文件名:', imagePath, '->', finalPath);
            } else {
                // 无法确定类型，返回默认武器图片
                console.warn('⚠️ 无法确定武器图片类型，使用默认:', imagePath);
                finalPath = this.basePath + 'battle_sword.png';
            }
        }
        
        // 🔧 缓存处理结果
        this.imagePathCache.set(imagePath, finalPath);
        
        // 🔧 自动限制缓存大小
        this.limitCacheSize(50);
        
        return finalPath;
    }
    
    /**
     * 从各种格式的路径中提取文件名
     */
    extractFileName(imagePath) {
        if (!imagePath) return '';
        
        // 先处理完整路径
        let cleanPath = imagePath;
        
        // 移除assets/images/前缀
        if (cleanPath.startsWith('assets/images/')) {
            cleanPath = cleanPath.substring(14);
        } else if (cleanPath.startsWith('assets/')) {
            cleanPath = cleanPath.substring(7);
        }
        
        // 🔧 新增：处理数据库中存储的images/相对路径
        if (cleanPath.startsWith('images/')) {
            cleanPath = cleanPath.substring(7); // 去掉'images/'前缀
        }
        
        // 移除目录前缀（char/, enemy/, general/等）
        cleanPath = cleanPath.replace(/^(char|enemy|general)\//, '');
        
        // 移除../../images/前缀
        cleanPath = cleanPath.replace(/^\.\.\/\.\.\/images\/[^\/]+\//, '');
        
        // 如果路径中还包含/，取最后一部分
        if (cleanPath.includes('/')) {
            cleanPath = cleanPath.substring(cleanPath.lastIndexOf('/') + 1);
        }
        
        return cleanPath;
    }
    
    /**
     * 通用图片路径处理
     */
    processImagePath(imagePath, type = 'char') {
        switch (type) {
            case 'char':
            case 'character':
                return this.getCharacterImage(imagePath);
            case 'enemy':
            case 'monster':
                return this.getEnemyImage(imagePath);
            case 'weapon':
            case 'equi':
                return this.getWeaponImage(imagePath);
            default:
                console.warn('未知图片类型:', type);
                return this.getCharacterImage(imagePath);
        }
    }
    
    /**
     * 检查图片是否存在（模拟）
     */
    async checkImageExists(imagePath) {
        return new Promise((resolve) => {
            const img = new Image();
            img.onload = () => resolve(true);
            img.onerror = () => resolve(false);
            img.src = imagePath;
        });
    }
    
    /**
     * 预加载关键图片
     */
    async preloadImages() {
        const imagesToPreload = [
            this.basePath + 'char/ck.png',
            this.basePath + 'enemy/yelang.png',
            this.basePath + 'enemy/monster_default.png',
            this.basePath + 'enemy/boss_default.png',
            this.basePath + 'battle_sword.png',
            this.basePath + 'equi/bw_11001.png',
            this.basePath + 'battle-bg.jpg'
        ];
        
        console.log('🔄 开始预加载图片，基础路径:', this.basePath);
        console.log('📋 预加载列表:', imagesToPreload);
        
        const promises = imagesToPreload.map(src => {
            return new Promise((resolve) => {
                const img = new Image();
                img.onload = () => {
                    console.log('✅ 预加载成功:', src);
                    resolve(true);
                };
                img.onerror = () => {
                    console.warn('❌ 预加载失败:', src);
                    resolve(false);
                };
                img.src = src;
            });
        });
        
        const results = await Promise.all(promises);
        const successCount = results.filter(r => r).length;
        console.log(`📸 图片预加载完成: ${successCount}/${imagesToPreload.length} 成功`);
    }
    
    /**
     * 🔧 修正：处理数据库路径的专用方法
     */
    processDatabasePath(imagePath, type) {
        if (!imagePath) return this.getDefaultImage(type);
        
        console.log('🔍 处理数据库路径:', imagePath, '类型:', type);
        
        // 🔧 修复：检查图片路径格式，确保是正确的assets/images/格式
        if (imagePath.startsWith('assets/images/')) {
            const finalPath = this.basePath + imagePath.substring(7); // 去掉'images/'，加上'assets/images/'
            console.log('📁 完整路径转换:', imagePath, '->', finalPath);
            return finalPath;
        }
        
        // 如果路径包含目录分隔符，可能是完整的相对路径
        if (imagePath.includes('/')) {
            // 检查是否是char/, enemy/, general/开头的路径
            if (imagePath.startsWith('char/') || imagePath.startsWith('enemy/') || imagePath.startsWith('general/')) {
                const finalPath = this.basePath + imagePath;
                console.log('📁 目录路径转换:', imagePath, '->', finalPath);
                return finalPath;
            }
        }
        
        // 🔧 修复：如果是纯文件名，直接拼接对应目录
        if (!imagePath.includes('/')) {
            let fileName = imagePath;
            // 确保有.png扩展名
            if (!fileName.endsWith('.png')) {
                fileName = fileName + '.png';
            }
            
            const finalPath = this.basePath + type + '/' + fileName;
            console.log('📄 文件名转换:', imagePath, '->', finalPath, '(添加目录:', type + '/)');
            return finalPath;
        }
        
        // 备用方案
        console.warn('⚠️ 无法处理的数据库路径格式:', imagePath);
        return this.getDefaultImage(type);
    }
    
    /**
     * 获取默认图片路径
     */
    getDefaultImage(type) {
        const defaults = {
            'char': this.basePath + 'char/ck.png',
            'enemy': this.basePath + 'enemy/yelang.png',
            'weapon': this.basePath + 'battle_sword.png'
        };
        return defaults[type] || this.basePath + 'char/ck.png';
    }
    
    /**
     * 🔧 新增：清理图片路径缓存
     */
    clearImageCache() {
        const cacheSize = this.imagePathCache.size;
        this.imagePathCache.clear();
        console.log(`🧹 图片路径缓存已清理，释放了 ${cacheSize} 个缓存项`);
    }
    
    /**
     * 🔧 新增：获取缓存统计信息
     */
    getCacheStats() {
        return {
            size: this.imagePathCache.size,
            entries: Array.from(this.imagePathCache.entries())
        };
    }
    
    /**
     * 🔧 新增：限制缓存大小，防止内存泄漏
     */
    limitCacheSize(maxSize = 100) {
        if (this.imagePathCache.size > maxSize) {
            const entries = Array.from(this.imagePathCache.entries());
            const toDelete = entries.slice(0, entries.length - maxSize);
            
            toDelete.forEach(([key]) => {
                this.imagePathCache.delete(key);
            });
            
            console.log(`🔧 缓存大小限制：删除了 ${toDelete.length} 个旧缓存项，当前缓存大小：${this.imagePathCache.size}`);
        }
    }
}

// 创建全局实例
window.ImagePathManager = new ImagePathManager(); 