<?php
/**
 * 一念修仙 - 灵根系统API
 * 提供灵根数据查询和基本操作
 */

// 错误处理设置
ini_set('display_errors', 0);
error_reporting(E_ALL);

// 启动会话（必须在任何输出之前）
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// 设置JSON响应头
header('Content-Type: application/json; charset=utf-8');

try {
    // 引入配置文件
    require_once __DIR__ . '/../../setting.php';

    // 检查维护模式
    if (function_exists('isMaintenanceMode') && isMaintenanceMode()) {
        echo json_encode([
            'success' => false,
            'message' => '游戏正在维护中，请稍后再试',
            'maintenance' => true
        ]);
        exit;
    }

    // 记录API调用（如果开启了调试）
    if (defined('DEBUG_LOG_API_CALLS') && DEBUG_LOG_API_CALLS && function_exists('writeLog')) {
        writeLog("API调用: spirit_root.php", 'DEBUG', 'api.log');
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => '系统初始化失败: ' . $e->getMessage()
    ]);
    exit;
}

// 设置调试日志（根据配置）
if (isDebugMode()) {
    ini_set('display_errors', 1);
    ini_set('log_errors', 1);
    error_log("灵根系统API请求开始处理，时间：" . date('Y-m-d H:i:s'));
}

// 获取数据库连接
try {
    $pdo = getDatabase();
    if (isDebugMode()) {
        error_log("灵根系统API - 数据库连接成功");
    }
} catch (Exception $e) {
    writeLog("灵根系统API数据库连接失败: " . $e->getMessage(), 'ERROR', 'database.log');
    echo json_encode(['success' => false, 'message' => '数据库连接失败']);
    exit;
}

// 获取会话中的用户信息
// 🔧 修复：避免重复启动session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
$userId = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;
$characterId = isset($_SESSION['character_id']) ? $_SESSION['character_id'] : null;

// 如果没有登录，尝试从GET参数获取ID用于测试
if (!$userId || !$characterId) {
    $userId = isset($_GET['test_user_id']) ? $_GET['test_user_id'] : null;
    $characterId = isset($_GET['test_character_id']) ? $_GET['test_character_id'] : null;
    
    // 如果仍然没有ID，返回错误
    if (!$userId || !$characterId) {
        echo json_encode(['success' => false, 'message' => '请先登录']);
        exit;
    }
}

// 获取请求动作
$action = isset($_GET['action']) ? $_GET['action'] : 'get_spirit_roots';

/**
 * 根据灵根值确定品质
 */
function getSpiritQuality($value) {
    if ($value >= 25) {
        return '极品灵根';
    } elseif ($value >= 20) {
        return '上品灵根';
    } elseif ($value >= 15) {
        return '中品灵根';
    } elseif ($value >= 10) {
        return '下品灵根';
    } else {
        return '废灵根';
    }
}

// 根据请求动作处理不同的API请求
switch ($action) {
    case 'get_spirit_roots':
        // 获取角色的灵根信息
        getSpiritRoots($pdo, $characterId);
        break;
    
    case 'get_spiritual_materials':
        // 兼容天材地宝接口，返回灵根数据
        getSpiritRootsForMaterials($pdo, $characterId);
        break;
    
    case 'get_spirit_root_info':
        // 获取灵根详细信息
        getSpiritRootInfo($pdo, $characterId);
        break;
    
    default:
        echo json_encode(['success' => false, 'message' => '无效的请求']);
}

/**
 * 获取角色的灵根信息
 */
function getSpiritRoots($pdo, $characterId) {
    // 获取角色的灵根数据
    $sql = "SELECT metal_affinity, wood_affinity, water_affinity, fire_affinity, earth_affinity, 
                   spiritual_root_usage
            FROM characters WHERE id = ?";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$characterId]);
    $character = $stmt->fetch();
    
    if (!$character) {
        echo json_encode(['success' => false, 'message' => '角色不存在']);
        return;
    }
    
    // 解析灵根使用记录
    $usageData = [];
    if (!empty($character['spiritual_root_usage'])) {
        $usageData = json_decode($character['spiritual_root_usage'], true);
        if (!$usageData) {
            $usageData = [];
        }
    }
    
    // 组装灵根数据
    $spiritRoots = [
        'metal' => [
            'value' => $character['metal_affinity'],
            'quality' => getSpiritQuality($character['metal_affinity']),
            'usage_count' => isset($usageData['metal']) ? $usageData['metal'] : 0
        ],
        'wood' => [
            'value' => $character['wood_affinity'],
            'quality' => getSpiritQuality($character['wood_affinity']),
            'usage_count' => isset($usageData['wood']) ? $usageData['wood'] : 0
        ],
        'water' => [
            'value' => $character['water_affinity'],
            'quality' => getSpiritQuality($character['water_affinity']),
            'usage_count' => isset($usageData['water']) ? $usageData['water'] : 0
        ],
        'fire' => [
            'value' => $character['fire_affinity'],
            'quality' => getSpiritQuality($character['fire_affinity']),
            'usage_count' => isset($usageData['fire']) ? $usageData['fire'] : 0
        ],
        'earth' => [
            'value' => $character['earth_affinity'],
            'quality' => getSpiritQuality($character['earth_affinity']),
            'usage_count' => isset($usageData['earth']) ? $usageData['earth'] : 0
        ]
    ];
    
    echo json_encode([
        'success' => true,
        'spirit_roots' => $spiritRoots,
        'usage_data' => $usageData
    ]);
}

/**
 * 获取灵根数据（兼容天材地宝接口格式）
 */
function getSpiritRootsForMaterials($pdo, $characterId) {
    // 获取角色的灵根数据
    $sql = "SELECT metal_affinity, wood_affinity, water_affinity, fire_affinity, earth_affinity, 
                   spiritual_root_usage
            FROM characters WHERE id = ?";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$characterId]);
    $character = $stmt->fetch();
    
    if (!$character) {
        echo json_encode(['success' => false, 'message' => '角色不存在']);
        return;
    }
    
    // 计算总值
    $totalValue = $character['metal_affinity'] + $character['wood_affinity'] + 
                  $character['water_affinity'] + $character['fire_affinity'] + $character['earth_affinity'];
    
    // 确定品质
    $quality = '凡品';
    if ($totalValue >= 501) {
        $quality = '极品';
    } elseif ($totalValue >= 301) {
        $quality = '上品';
    } elseif ($totalValue >= 151) {
        $quality = '中品';
    } elseif ($totalValue >= 101) {
        $quality = '下品';
    }
    
    // 组装前端期望的数据结构
    $spiritRootData = [
        'total_value' => $totalValue,
        'quality' => $quality,
        'roots' => [
            'metal' => $character['metal_affinity'],
            'wood' => $character['wood_affinity'],
            'water' => $character['water_affinity'],
            'fire' => $character['fire_affinity'],
            'earth' => $character['earth_affinity']
        ]
    ];
    
    echo json_encode([
        'success' => true,
        'message' => '获取灵根数据成功',
        'data' => $spiritRootData
    ]);
}

/**
 * 获取灵根详细信息
 */
function getSpiritRootInfo($pdo, $characterId) {
    // 获取角色的完整信息
    $sql = "SELECT * FROM characters WHERE id = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$characterId]);
    $character = $stmt->fetch();
    
    if (!$character) {
        echo json_encode(['success' => false, 'message' => '角色不存在']);
        return;
    }
    
    // 计算灵根总值和平均值
    $totalValue = $character['metal_affinity'] + $character['wood_affinity'] + 
                  $character['water_affinity'] + $character['fire_affinity'] + $character['earth_affinity'];
    $averageValue = $totalValue / 5;
    
    // 确定主要灵根
    $roots = [
        'metal' => $character['metal_affinity'],
        'wood' => $character['wood_affinity'],
        'water' => $character['water_affinity'],
        'fire' => $character['fire_affinity'],
        'earth' => $character['earth_affinity']
    ];
    
    $primaryRoot = array_keys($roots, max($roots))[0];
    
    // 灵根品质映射
    $qualityMap = [
        '废灵根' => 1,
        '下品灵根' => 2,
        '中品灵根' => 3,
        '上品灵根' => 4,
        '极品灵根' => 5
    ];
    
    // 计算平均品质
    $qualities = [
        getSpiritQuality($character['metal_affinity']),
        getSpiritQuality($character['wood_affinity']),
        getSpiritQuality($character['water_affinity']),
        getSpiritQuality($character['fire_affinity']),
        getSpiritQuality($character['earth_affinity'])
    ];
    
    $totalQuality = 0;
    foreach ($qualities as $quality) {
        $totalQuality += isset($qualityMap[$quality]) ? $qualityMap[$quality] : 1;
    }
    $averageQuality = $totalQuality / 5;
    
    // 解析使用记录
    $usageData = [];
    if (!empty($character['spiritual_root_usage'])) {
        $usageData = json_decode($character['spiritual_root_usage'], true);
        if (!$usageData) {
            $usageData = [];
        }
    }
    
    echo json_encode([
        'success' => true,
        'character_info' => [
            'id' => $character['id'],
            'character_name' => $character['character_name'],
            'level' => $character['level'],
            'realm_level' => $character['realm_level']
        ],
        'spirit_root_summary' => [
            'total_value' => $totalValue,
            'average_value' => round($averageValue, 2),
            'primary_root' => $primaryRoot,
            'average_quality' => round($averageQuality, 2)
        ],
        'spirit_roots' => [
            'metal' => [
                'value' => $character['metal_affinity'],
                'quality' => getSpiritQuality($character['metal_affinity']),
                'usage_count' => isset($usageData['metal']) ? $usageData['metal'] : 0
            ],
            'wood' => [
                'value' => $character['wood_affinity'],
                'quality' => getSpiritQuality($character['wood_affinity']),
                'usage_count' => isset($usageData['wood']) ? $usageData['wood'] : 0
            ],
            'water' => [
                'value' => $character['water_affinity'],
                'quality' => getSpiritQuality($character['water_affinity']),
                'usage_count' => isset($usageData['water']) ? $usageData['water'] : 0
            ],
            'fire' => [
                'value' => $character['fire_affinity'],
                'quality' => getSpiritQuality($character['fire_affinity']),
                'usage_count' => isset($usageData['fire']) ? $usageData['fire'] : 0
            ],
            'earth' => [
                'value' => $character['earth_affinity'],
                'quality' => getSpiritQuality($character['earth_affinity']),
                'usage_count' => isset($usageData['earth']) ? $usageData['earth'] : 0
            ]
        ],
        'usage_data' => $usageData
    ]);
}
?> 