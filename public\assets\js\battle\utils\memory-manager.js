/**
 * 🔧 战斗系统内存管理器
 * 统一管理战斗系统的内存使用，防止内存泄漏
 */
class BattleMemoryManager {
    constructor() {
        this.timers = new Set(); // 活跃的定时器
        this.intervals = new Set(); // 活跃的间隔器
        this.eventListeners = new Map(); // 事件监听器
        this.observers = new Set(); // MutationObserver实例
        this.skillLoader = null; // 技能加载器引用
        this.imageManager = null; // 图片管理器引用
        this.dataManager = null; // 数据管理器引用
        
        // 性能监控
        this.memoryStats = {
            startTime: Date.now(),
            peakMemoryUsage: 0,
            currentCacheSize: 0
        };
        
        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('memory-manager', '🧠 战斗系统内存管理器已初始化');
        }
    }

    /**
     * 设置管理器引用
     */
    setManagers(skillLoader, imageManager, dataManager) {
        this.skillLoader = skillLoader;
        this.imageManager = imageManager;
        this.dataManager = dataManager;
    }

    /**
     * 注册定时器
     * @param {number} timerId 定时器ID
     */
    registerTimer(timerId) {
        this.timers.add(timerId);
        return timerId;
    }

    /**
     * 注册间隔器
     * @param {number} intervalId 间隔器ID
     */
    registerInterval(intervalId) {
        this.intervals.add(intervalId);
        return intervalId;
    }

    /**
     * 安全的setTimeout
     * @param {Function} callback 回调函数
     * @param {number} delay 延迟时间
     * @returns {number} 定时器ID
     */
    setTimeout(callback, delay) {
        const timerId = setTimeout(() => {
            try {
                callback();
            } catch (error) {
                console.error('定时器回调执行失败:', error);
            } finally {
                this.timers.delete(timerId);
            }
        }, delay);
        
        return this.registerTimer(timerId);
    }

    /**
     * 安全的setInterval
     * @param {Function} callback 回调函数
     * @param {number} delay 间隔时间
     * @returns {number} 间隔器ID
     */
    setInterval(callback, delay) {
        const intervalId = setInterval(() => {
            try {
                callback();
            } catch (error) {
                console.error('间隔器回调执行失败:', error);
                this.clearInterval(intervalId);
            }
        }, delay);
        
        return this.registerInterval(intervalId);
    }

    /**
     * 清理指定定时器
     * @param {number} timerId 定时器ID
     */
    clearTimeout(timerId) {
        if (this.timers.has(timerId)) {
            clearTimeout(timerId);
            this.timers.delete(timerId);
        }
    }

    /**
     * 清理指定间隔器
     * @param {number} intervalId 间隔器ID
     */
    clearInterval(intervalId) {
        if (this.intervals.has(intervalId)) {
            clearInterval(intervalId);
            this.intervals.delete(intervalId);
        }
    }

    /**
     * 注册事件监听器
     * @param {Element} element DOM元素
     * @param {string} event 事件名称
     * @param {Function} handler 事件处理函数
     * @param {Object} options 事件选项
     */
    addEventListener(element, event, handler, options = {}) {
        const key = `${element}_${event}_${Date.now()}`;
        
        element.addEventListener(event, handler, options);
        
        this.eventListeners.set(key, {
            element,
            event,
            handler,
            options
        });
        
        return key;
    }

    /**
     * 移除事件监听器
     * @param {string} key 事件监听器键
     */
    removeEventListener(key) {
        const listener = this.eventListeners.get(key);
        if (listener) {
            listener.element.removeEventListener(listener.event, listener.handler, listener.options);
            this.eventListeners.delete(key);
        }
    }

    /**
     * 注册MutationObserver
     * @param {MutationObserver} observer 观察器实例
     */
    registerObserver(observer) {
        this.observers.add(observer);
        return observer;
    }

    /**
     * 断开MutationObserver
     * @param {MutationObserver} observer 观察器实例
     */
    disconnectObserver(observer) {
        if (this.observers.has(observer)) {
            observer.disconnect();
            this.observers.delete(observer);
        }
    }

    /**
     * 获取内存使用统计
     * @returns {Object} 内存统计信息
     */
    getMemoryStats() {
        const currentMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
        if (currentMemory > this.memoryStats.peakMemoryUsage) {
            this.memoryStats.peakMemoryUsage = currentMemory;
        }

        return {
            // 基础统计
            activeTimers: this.timers.size,
            activeIntervals: this.intervals.size,
            activeEventListeners: this.eventListeners.size,
            activeObservers: this.observers.size,
            
            // 缓存统计
            skillInstances: this.skillLoader ? this.skillLoader.getCacheStats().cachedInstances : 0,
            imageCache: this.imageManager ? this.imageManager.getCacheStats().size : 0,
            
            // 内存统计（如果可用）
            jsHeapSize: performance.memory ? Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) : 'N/A',
            peakMemoryUsage: Math.round(this.memoryStats.peakMemoryUsage / 1024 / 1024),
            runningTime: Math.round((Date.now() - this.memoryStats.startTime) / 1000)
        };
    }

    /**
     * 清理所有资源
     */
    cleanupAll() {
        console.log('🧹 开始清理战斗系统内存资源...');
        
        // 清理定时器
        this.timers.forEach(timerId => clearTimeout(timerId));
        this.timers.clear();
        
        // 清理间隔器
        this.intervals.forEach(intervalId => clearInterval(intervalId));
        this.intervals.clear();
        
        // 清理事件监听器
        this.eventListeners.forEach((listener, key) => {
            this.removeEventListener(key);
        });
        
        // 断开观察器
        this.observers.forEach(observer => {
            observer.disconnect();
        });
        this.observers.clear();
        
        // 清理管理器缓存
        if (this.skillLoader) {
            this.skillLoader.clearSkillCache();
        }
        
        if (this.imageManager) {
            this.imageManager.clearImageCache();
        }
        
        if (this.dataManager && typeof this.dataManager.clearCache === 'function') {
            this.dataManager.clearCache();
        }
        
        console.log('✅ 战斗系统内存资源清理完成');
    }

    /**
     * 部分清理（保留重要缓存）
     */
    partialCleanup() {
        console.log('🧽 执行部分内存清理...');
        
        // 只清理过期的定时器和事件监听器
        const now = Date.now();
        
        // 清理超时的技能实例缓存
        if (this.skillLoader) {
            // 清理不常用的技能实例
            const stats = this.skillLoader.getCacheStats();
            if (stats.cachedInstances > 10) {
                console.log('🔧 技能实例缓存过多，执行部分清理');
                // 可以实现LRU清理策略
            }
        }
        
        // 限制图片缓存大小
        if (this.imageManager) {
            this.imageManager.limitCacheSize(50);
        }
        
        console.log('✅ 部分内存清理完成');
    }

    /**
     * 监控内存使用情况
     */
    startMemoryMonitoring() {
        this.monitoringInterval = this.setInterval(() => {
            const stats = this.getMemoryStats();
            
            // 内存使用过高时自动清理
            if (stats.jsHeapSize !== 'N/A' && stats.jsHeapSize > 100) {
                console.warn('⚠️ 内存使用过高，执行自动清理', stats);
                this.partialCleanup();
            }
            
            // 定期输出统计信息（调试模式）
            if (window.DEBUG_MODE) {
                console.log('📊 内存监控统计:', stats);
            }
        }, 30000); // 每30秒检查一次
    }

    /**
     * 停止内存监控
     */
    stopMemoryMonitoring() {
        if (this.monitoringInterval) {
            this.clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }
    }
}

// 创建全局实例
window.BattleMemoryManager = new BattleMemoryManager();

// 页面卸载时自动清理
window.addEventListener('beforeunload', () => {
    if (window.BattleMemoryManager) {
        window.BattleMemoryManager.cleanupAll();
    }
});

// 导出类
window.BattleMemoryManagerClass = BattleMemoryManager; 