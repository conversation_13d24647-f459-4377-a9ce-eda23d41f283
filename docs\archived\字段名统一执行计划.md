# 🎯 战斗属性字段名统一执行计划

## 📋 字段名统一标准（最终版）

### 🔧 需要统一的字段映射表

| 中文名称 | ❌ 错误的驼峰命名 | ✅ 正确的数据库字段名 | 分类 |
|---------|------------------|---------------------|------|
| 生命值 | `maxHp` | `hp_bonus` | 基础属性 |
| 法力值 | `maxMp` | `mp_bonus` | 基础属性 |
| 速度 | `speed` | `speed_bonus` | 基础属性 |
| 物理攻击力 | `physicalAttack` | `physical_attack` | 攻防属性 |
| 物理防御力 | `physicalDefense` | `physical_defense` | 攻防属性 |
| 法术攻击力 | `immortalAttack` | `immortal_attack` | 攻防属性 |
| 法术防御力 | `immortalDefense` | `immortal_defense` | 攻防属性 |
| 命中率 | `hitRate` | `accuracy_bonus` | 战斗属性 |
| 闪避率 | `dodgeRate` | `dodge_bonus` | 战斗属性 |
| 暴击率 | `criticalRate` | `critical_bonus` | 战斗属性 |
| 暴击伤害 | `criticalDamage` | `critical_damage` | 战斗属性 |
| 免暴率 | `criticalResistance` | `critical_resistance` | 战斗属性 |
| 格挡率 | `blockRate` | `block_bonus` | 战斗属性 |

## 📂 执行计划

### 🎯 第一阶段：后端 PHP API 文件修复

#### ✅ 步骤1：src/api/user_info.php
- [ ] 修复 `immortalAttack` → `immortal_attack`
- [ ] 修复 `immortalDefense` → `immortal_defense`
- [ ] 验证修复结果

#### ✅ 步骤2：src/api/battle_unified.php
- [ ] 修复 `maxHp` → `hp_bonus`
- [ ] 修复 `maxMp` → `mp_bonus`
- [ ] 修复 `physicalAttack` → `physical_attack`
- [ ] 修复 `physicalDefense` → `physical_defense`
- [ ] 修复 `immortalAttack` → `immortal_attack`
- [ ] 修复 `immortalDefense` → `immortal_defense`
- [ ] 验证修复结果

#### ✅ 步骤3：src/api/monster_ai_system.php
- [ ] 修复 `maxHp` → `hp_bonus`
- [ ] 修复 `maxMp` → `mp_bonus`
- [ ] 验证修复结果

#### ✅ 步骤4：src/api/battle_stage_info.php
- [ ] 修复 `maxHp` → `hp_bonus`
- [ ] 验证修复结果

### 🎯 第二阶段：前端 JavaScript 文件修复

#### ✅ 步骤5：public/assets/js/battle/battle-manager.js
- [ ] 修复 `maxHp` → `hp_bonus`
- [ ] 修复 `maxMp` → `mp_bonus`
- [ ] 修复 `physicalAttack` → `physical_attack`
- [ ] 修复 `physicalDefense` → `physical_defense`
- [ ] 修复 `immortalAttack` → `immortal_attack`
- [ ] 修复 `immortalDefense` → `immortal_defense`
- [ ] 验证修复结果

#### ✅ 步骤6：public/assets/js/battle/battle-combat-calculator.js
- [ ] 修复 `hitRate` → `accuracy_bonus`
- [ ] 修复 `criticalRate` → `critical_bonus`
- [ ] 修复 `immortalAttack` → `immortal_attack`
- [ ] 修复 `physicalAttack` → `physical_attack`
- [ ] 修复 `immortalDefense` → `immortal_defense`
- [ ] 修复 `physicalDefense` → `physical_defense`
- [ ] 验证修复结果

#### ✅ 步骤7：public/assets/js/battle/managers/battle-flow-manager.js
- [ ] 修复 `maxHp` → `hp_bonus`
- [ ] 修复 `maxMp` → `mp_bonus`
- [ ] 修复 `physicalAttack` → `physical_attack`
- [ ] 修复 `physicalDefense` → `physical_defense`
- [ ] 验证修复结果

#### ✅ 步骤8：public/assets/js/battle/script.js
- [ ] 修复 `maxMp` → `mp_bonus`
- [ ] 验证修复结果

#### ✅ 步骤9：public/assets/js/battle/utils/debug-panel.js
- [ ] 修复 `maxMp` → `mp_bonus`
- [ ] 验证修复结果

#### ✅ 步骤10：public/assets/js/battle/utils/error-handler.js
- [ ] 修复 `maxHp` → `hp_bonus`
- [ ] 修复 `physicalAttack` → `physical_attack`
- [ ] 修复 `physicalDefense` → `physical_defense`
- [ ] 验证修复结果

#### ✅ 步骤11：public/assets/js/item-detail-popup.js
- [ ] 修复 `criticalRate` → `critical_bonus`
- [ ] 修复 `criticalResistance` → `critical_resistance`
- [ ] 验证修复结果

### 🎯 第三阶段：其他相关文件检查

#### ✅ 步骤12：全项目搜索遗漏
- [ ] 搜索所有 `.php` 文件中的驼峰命名
- [ ] 搜索所有 `.js` 文件中的驼峰命名
- [ ] 搜索所有 `.html` 文件中的驼峰命名
- [ ] 修复发现的遗漏

### 🎯 第四阶段：测试验证

#### ✅ 步骤13：功能测试
- [ ] 测试战斗系统属性显示
- [ ] 测试装备属性计算
- [ ] 测试属性变化弹窗
- [ ] 测试角色属性面板

## 📝 执行记录

### ✅ 已完成项目
- [x] 步骤1：src/api/user_info.php ✅ 已完成
- [x] 步骤2：src/api/battle_unified.php ✅ 已完成
- [x] 步骤3：src/api/monster_ai_system.php ✅ 已完成
- [x] 步骤4：src/api/battle_stage_info.php ✅ 已完成
- [x] 步骤5：public/assets/js/battle/battle-manager.js ✅ 已完成
- [x] 步骤6：public/assets/js/battle/battle-combat-calculator.js ✅ 已完成
- [x] 步骤7：public/assets/js/battle/managers/battle-flow-manager.js ✅ 已完成
- [x] 步骤8：public/assets/js/battle/script.js ✅ 已完成
- [x] 步骤9：public/assets/js/battle/utils/debug-panel.js ✅ 已完成
- [x] 步骤10：public/assets/js/battle/utils/error-handler.js ✅ 已完成
- [x] 步骤11：public/assets/js/item-detail-popup.js ✅ 已完成
- [x] 步骤12：全项目搜索遗漏 ✅ 已完成，无遗漏字段
- [ ] 步骤13：功能测试

## 🚨 重要注意事项

1. **严格按照数据库字段名** - 不能自创字段名
2. **前后端完全一致** - API和JavaScript都用同样的字段名
3. **逐步验证** - 每修复一个文件就测试一次
4. **保持备份** - 重要修改前先备份
5. **统一格式** - 所有数值都是整数格式

---
*创建时间：2024年12月19日*
*执行状态：✅ 已完成所有步骤！* 