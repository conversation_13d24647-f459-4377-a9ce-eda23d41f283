/**
 * 水系技能动画样式 - 水龙卷
 * 对应 animation_model = 'shuilongjuan'
 */

/* 动画容器 */
.shuilongjuan-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1000;
}

/* === 蓄力阶段动画 === */

/* 水元素魔法阵 */
.shuilongjuan-magic-circle {
    position: absolute;
    width: 120px;
    height: 120px;
    transform: translate(-50%, -50%);
    border: 3px solid rgba(64, 164, 223, 0.8);
    border-radius: 50%;
    background: radial-gradient(circle, rgba(64, 164, 223, 0.3) 0%, rgba(135, 206, 235, 0.1) 50%, transparent 100%);
    animation: shuilongjuan-magic-circle 1s ease-out;
    box-shadow: 0 0 30px rgba(64, 164, 223, 0.6), inset 0 0 20px rgba(135, 206, 235, 0.4);
}

@keyframes shuilongjuan-magic-circle {
    0% {
        transform: translate(-50%, -50%) scale(0) rotate(0deg);
        opacity: 0;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.2) rotate(180deg);
        opacity: 0.8;
    }
    100% {
        transform: translate(-50%, -50%) scale(1) rotate(360deg);
        opacity: 1;
    }
}

/* 内圈水纹 */
.shuilongjuan-inner-ripples {
    position: absolute;
    width: 80px;
    height: 80px;
    transform: translate(-50%, -50%);
    border: 2px solid rgba(135, 206, 235, 0.7);
    border-radius: 50%;
    animation: shuilongjuan-inner-ripples 1s ease-out infinite;
}

@keyframes shuilongjuan-inner-ripples {
    0%, 100% {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 0.7;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.1);
        opacity: 0.4;
    }
}

/* 外圈水纹 */
.shuilongjuan-outer-ripples {
    position: absolute;
    width: 140px;
    height: 140px;
    transform: translate(-50%, -50%);
    border: 1px solid rgba(176, 224, 230, 0.5);
    border-radius: 50%;
    animation: shuilongjuan-outer-ripples 1.5s ease-out infinite;
}

@keyframes shuilongjuan-outer-ripples {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.5;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.3);
        opacity: 0.2;
    }
}

/* 武器图片旋转 */
.shuilongjuan-weapon-sprite {
    position: absolute;
    width: 40px;
    height: 40px;
    transform: translate(-50%, -50%);
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    animation: shuilongjuan-weapon-rotate 1s linear infinite;
    -webkit-filter: drop-shadow(0 0 8px rgba(64, 164, 223, 0.8));
    filter: drop-shadow(0 0 8px rgba(64, 164, 223, 0.8));
}

@keyframes shuilongjuan-weapon-rotate {
    0% { transform: translate(-50%, -50%) rotate(0deg) scale(1); }
    50% { transform: translate(-50%, -50%) rotate(180deg) scale(1.1); }
    100% { transform: translate(-50%, -50%) rotate(360deg) scale(1); }
}

/* 蓄力能量核心 */
.shuilongjuan-energy-core {
    position: absolute;
    width: 30px;
    height: 30px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, rgba(64, 164, 223, 0.9) 0%, rgba(135, 206, 235, 0.6) 50%, transparent 100%);
    border-radius: 50%;
    animation: shuilongjuan-energy-pulse 0.8s ease-in-out infinite alternate;
    box-shadow: 0 0 20px rgba(64, 164, 223, 0.8);
    }

@keyframes shuilongjuan-energy-pulse {
    0% {
        transform: translate(-50%, -50%) scale(0.8);
        box-shadow: 0 0 15px rgba(64, 164, 223, 0.6);
    }
    100% {
        transform: translate(-50%, -50%) scale(1.2);
        box-shadow: 0 0 25px rgba(64, 164, 223, 1);
    }
}

/* 水滴汇聚粒子 */
.shuilongjuan-charge-droplet {
    position: absolute;
    width: 4px;
    height: 6px;
    transform: translate(-50%, -50%);
    background: linear-gradient(to bottom, rgba(64, 164, 223, 0.9), rgba(135, 206, 235, 0.7));
    border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
    animation: shuilongjuan-droplet-gather 1.2s ease-out forwards;
    box-shadow: 0 0 4px rgba(64, 164, 223, 0.6);
}

@keyframes shuilongjuan-droplet-gather {
    0% {
        transform: translate(calc(-50% + var(--chargeX)), calc(-50% + var(--chargeY))) scale(0);
        opacity: 0;
    }
    30% {
        transform: translate(calc(-50% + var(--chargeX) * 0.7), calc(-50% + var(--chargeY) * 0.7)) scale(1);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(0.5);
        opacity: 0;
    }
}

/* 环绕水流螺旋 */
.shuilongjuan-charge-spiral {
    position: absolute;
    width: 8px;
    height: 8px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, rgba(135, 206, 235, 0.8), transparent);
    border-radius: 50%;
    animation: shuilongjuan-spiral-move 1s ease-out forwards;
}

@keyframes shuilongjuan-spiral-move {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0.8;
    }
    100% {
        transform: translate(-50%, -50%) scale(0.3);
        opacity: 0;
    }
}

/* 水元素波纹 */
.shuilongjuan-energy-ripple {
    position: absolute;
    width: 20px;
    height: 20px;
    transform: translate(-50%, -50%);
    border: 2px solid rgba(64, 164, 223, 0.6);
    border-radius: 50%;
    animation: shuilongjuan-ripple-expand 0.8s ease-out forwards;
}

@keyframes shuilongjuan-ripple-expand {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0.8;
    }
    100% {
        transform: translate(-50%, -50%) scale(8);
        opacity: 0;
    }
}

/* 蒸汽效果 */
.shuilongjuan-steam {
    position: absolute;
    width: 6px;
    height: 6px;
    transform: translate(-50%, -50%);
    background: rgba(176, 224, 230, 0.6);
    border-radius: 50%;
    animation: shuilongjuan-steam-rise 1.5s ease-out forwards;
}

@keyframes shuilongjuan-steam-rise {
    0% {
        transform: translate(-50%, -50%) scale(0.5);
        opacity: 0.6;
    }
    100% {
        transform: translate(-50%, calc(-50% - 40px)) scale(2);
        opacity: 0;
    }
}

/* === 发射阶段动画 === */

/* 预发射水流震动 */
.shuilongjuan-water-shake {
    position: absolute;
    width: 60px;
    height: 60px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, rgba(64, 164, 223, 0.4), transparent);
    border-radius: 50%;
    animation: shuilongjuan-water-shake 0.2s ease-in-out 3;
}

@keyframes shuilongjuan-water-shake {
    0%, 100% { transform: translate(-50%, -50%) scale(1); }
    50% { transform: translate(-50%, -50%) scale(1.3); }
}

/* 主要水龙卷 - 真正的龙卷风形状 */
.shuilongjuan-main-tornado {
    position: absolute;
    width: 25px;
    height: 80px;
    transform: translate(-50%, -50%);
    background: conic-gradient(from 0deg,
        rgba(64, 164, 223, 0.9) 0%,
        rgba(135, 206, 235, 0.7) 25%,
        rgba(176, 224, 230, 0.5) 50%,
        rgba(135, 206, 235, 0.7) 75%,
        rgba(64, 164, 223, 0.9) 100%);
    -webkit-clip-path: polygon(40% 0%, 60% 0%, 80% 100%, 20% 100%);
    clip-path: polygon(40% 0%, 60% 0%, 80% 100%, 20% 100%);
    animation: shuilongjuan-tornado-fly var(--flyTime) linear forwards;
    box-shadow: 0 0 15px rgba(64, 164, 223, 0.6);
}

@keyframes shuilongjuan-tornado-fly {
    0% {
        transform: translate(-50%, -50%) rotate(0deg) scale(0.8);
        opacity: 0.8;
    }
    50% {
        transform: translate(calc(-50% + var(--targetX) * 0.5), calc(-50% + var(--targetY) * 0.5)) rotate(720deg) scale(1.2);
        opacity: 1;
    }
    100% {
        transform: translate(calc(-50% + var(--targetX)), calc(-50% + var(--targetY))) rotate(1440deg) scale(1);
        opacity: 0.9;
    }
}

/* 螺旋水流层 - 龙卷风形状 */
.shuilongjuan-spiral-layer-1 {
    position: absolute;
    width: 22px;
    height: 70px;
    transform: translate(-50%, -50%);
    background: conic-gradient(from 45deg, rgba(135, 206, 235, 0.6), rgba(176, 224, 230, 0.4));
    -webkit-clip-path: polygon(42% 0%, 58% 0%, 75% 100%, 25% 100%);
    clip-path: polygon(42% 0%, 58% 0%, 75% 100%, 25% 100%);
    animation: shuilongjuan-spiral-fly var(--flyTime) linear forwards;
}

.shuilongjuan-spiral-layer-2 {
    position: absolute;
    width: 20px;
    height: 60px;
    transform: translate(-50%, -50%);
    background: conic-gradient(from 90deg, rgba(176, 224, 230, 0.5), rgba(135, 206, 235, 0.3));
    -webkit-clip-path: polygon(44% 0%, 56% 0%, 70% 100%, 30% 100%);
    clip-path: polygon(44% 0%, 56% 0%, 70% 100%, 30% 100%);
    animation: shuilongjuan-spiral-fly var(--flyTime) linear forwards;
}

.shuilongjuan-spiral-layer-3 {
    position: absolute;
    width: 18px;
    height: 50px;
    transform: translate(-50%, -50%);
    background: conic-gradient(from 135deg, rgba(135, 206, 235, 0.4), rgba(176, 224, 230, 0.2));
    -webkit-clip-path: polygon(46% 0%, 54% 0%, 65% 100%, 35% 100%);
    clip-path: polygon(46% 0%, 54% 0%, 65% 100%, 35% 100%);
    animation: shuilongjuan-spiral-fly var(--flyTime) linear forwards;
}

@keyframes shuilongjuan-spiral-fly {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
        opacity: 0.6;
    }
    100% {
        transform: translate(calc(-50% + var(--targetX)), calc(-50% + var(--targetY))) rotate(1080deg);
        opacity: 0.3;
    }
}

/* 增强水流拖尾 */
.shuilongjuan-enhanced-trail {
    position: absolute;
    width: calc(30px - var(--trailIndex) * 2px);
    height: calc(60px - var(--trailIndex) * 4px);
    transform: translate(-50%, -50%);
    background: linear-gradient(45deg, 
        rgba(135, 206, 235, calc(0.5 - var(--trailIndex) * 0.05)), 
        rgba(176, 224, 230, calc(0.3 - var(--trailIndex) * 0.03)));
    border-radius: 50% 50% 30% 30%;
    animation: shuilongjuan-trail-fly var(--flyTime) linear forwards;
}

@keyframes shuilongjuan-trail-fly {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
        opacity: 0.4;
    }
    100% {
        transform: translate(calc(-50% + var(--targetX)), calc(-50% + var(--targetY))) rotate(900deg);
        opacity: 0;
    }
}

/* 飞行水花飞溅 */
.shuilongjuan-flight-splash {
    position: absolute;
    width: 3px;
    height: 3px;
    transform: translate(-50%, -50%);
    background: rgba(64, 164, 223, 0.7);
    border-radius: 50%;
    animation: shuilongjuan-flight-splash 0.8s ease-out forwards;
}

@keyframes shuilongjuan-flight-splash {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0.7;
    }
    50% {
        transform: translate(-50%, -50%) scale(2);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(0.5);
        opacity: 0;
    }
}

/* === 击中阶段动画 === */

/* 瞬间冲击波 */
.shuilongjuan-impact-flash {
    position: absolute;
    width: 60px;
    height: 60px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, rgba(64, 164, 223, 0.9), rgba(135, 206, 235, 0.6), transparent);
    border-radius: 50%;
    animation: shuilongjuan-impact-flash 0.3s ease-out forwards;
}

@keyframes shuilongjuan-impact-flash {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0.8;
    }
    100% {
        transform: translate(-50%, -50%) scale(3);
        opacity: 0;
    }
}

/* 螺旋爆发核心 */
.shuilongjuan-spiral-core {
    position: absolute;
    width: 60px;
    height: 60px;
    transform: translate(-50%, -50%);
    background: conic-gradient(from 0deg, 
        rgba(64, 164, 223, 0.8), 
        rgba(135, 206, 235, 0.6), 
        rgba(176, 224, 230, 0.4), 
        rgba(135, 206, 235, 0.6), 
        rgba(64, 164, 223, 0.8));
    border-radius: 50%;
    animation: shuilongjuan-spiral-core 1.2s ease-out forwards;
}

@keyframes shuilongjuan-spiral-core {
    0% {
        transform: translate(-50%, -50%) rotate(0deg) scale(0);
        opacity: 0.8;
    }
    50% {
        transform: translate(-50%, -50%) rotate(360deg) scale(1.2);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) rotate(720deg) scale(0.3);
        opacity: 0;
    }
}

/* 冲击波 */
.shuilongjuan-impact-shockwave {
    position: absolute;
    width: 30px;
    height: 30px;
    transform: translate(-50%, -50%);
    border: 3px solid rgba(64, 164, 223, 0.6);
    border-radius: 50%;
    animation: shuilongjuan-shockwave-expand 0.8s ease-out forwards;
}

@keyframes shuilongjuan-shockwave-expand {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0.8;
        border-width: 3px;
    }
    100% {
        transform: translate(-50%, -50%) scale(6);
        opacity: 0;
        border-width: 1px;
    }
}

/* 螺旋水花爆发 */
.shuilongjuan-spiral-splash {
    position: absolute;
    width: 4px;
    height: 8px;
    transform: translate(-50%, -50%);
    background: linear-gradient(to bottom, rgba(64, 164, 223, 0.8), rgba(135, 206, 235, 0.6));
    border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
    animation: shuilongjuan-spiral-splash 1s ease-out forwards;
}

@keyframes shuilongjuan-spiral-splash {
    0% {
        transform: translate(-50%, -50%) rotate(var(--splashAngle)) scale(0);
        opacity: 0.8;
    }
    50% {
        transform: translate(-50%, -50%) 
                   rotate(var(--splashAngle)) 
                   translateX(calc(var(--splashDistance) * 0.5 + var(--spiralOffset))) 
                   scale(1.5);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) 
                   rotate(var(--splashAngle)) 
                   translateX(calc(var(--splashDistance) + var(--spiralOffset))) 
                   scale(0.5);
        opacity: 0;
    }
}

/* 水雾弥漫 */
.shuilongjuan-impact-mist {
    position: absolute;
    width: 12px;
    height: 12px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, rgba(176, 224, 230, 0.5), transparent);
    border-radius: 50%;
    animation: shuilongjuan-mist-spread 2s ease-out forwards;
}

@keyframes shuilongjuan-mist-spread {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0.5;
    }
    50% {
        transform: translate(-50%, -50%) scale(3);
        opacity: 0.3;
    }
    100% {
        transform: translate(-50%, -50%) scale(6);
        opacity: 0;
    }
}

/* 水滴回落 */
.shuilongjuan-falling-droplet {
    position: absolute;
    width: 3px;
    height: 5px;
    transform: translate(-50%, -50%);
    background: linear-gradient(to bottom, rgba(64, 164, 223, 0.7), rgba(135, 206, 235, 0.5));
    border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
    animation: shuilongjuan-droplet-fall 1.5s ease-in forwards;
}

@keyframes shuilongjuan-droplet-fall {
    0% {
        transform: translate(-50%, -50%);
        opacity: 0.7;
    }
    100% {
        transform: translate(-50%, calc(-50% + 80px));
        opacity: 0;
    }
}

/* 敌人受击效果 */
@keyframes water-hit {
    0% { -webkit-filter: hue-rotate(0deg) saturate(1); }
    0% { filter: hue-rotate(0deg) saturate(1); }
    25% { -webkit-filter: hue-rotate(180deg) saturate(1.5) brightness(1.2); }
    25% { filter: hue-rotate(180deg) saturate(1.5) brightness(1.2); }
    50% { -webkit-filter: hue-rotate(0deg) saturate(1) brightness(0.8); }
    50% { filter: hue-rotate(0deg) saturate(1) brightness(0.8); }
    75% { -webkit-filter: hue-rotate(180deg) saturate(1.2) brightness(1.1); }
    75% { filter: hue-rotate(180deg) saturate(1.2) brightness(1.1); }
    100% { -webkit-filter: hue-rotate(0deg) saturate(1); }
    100% { filter: hue-rotate(0deg) saturate(1); }
}

@keyframes water-shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-3px); }
    75% { transform: translateX(3px); }
}

/* 移动端适配 */
@media (max-width: 768px) {
    .shuilongjuan-container {
        transform: scale(0.8);
    }
    
    .shuilongjuan-magic-circle {
        width: 100px;
        height: 100px;
    }
    
    .shuilongjuan-main-tornado {
        width: 35px;
        height: 70px;
    }
    
    .shuilongjuan-impact-flash {
        width: 60px;
        height: 60px;
    }
} 