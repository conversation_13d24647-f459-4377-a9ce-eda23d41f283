<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>检查套装数据</title>
</head>
<body>
    <h1>检查套装数据</h1>
    <button onclick="checkSets()">检查套装数据</button>
    <div id="result"></div>

    <script src="assets/js/config.js"></script>
    <script>
        async function checkSets() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '检查中...';
            
            try {
                // 检查套装系统API
                const response = await fetch(window.GameConfig.getApiUrl('equipment_set_system.php'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=get_all_sets'
                });
                
                console.log('响应状态:', response.status);
                
                if (response.ok) {
                    const data = await response.json();
                    console.log('套装数据:', data);
                    
                    if (data.success) {
                        let html = '<h3>套装数据检查结果</h3>';
                        html += `<p><strong>总套装数:</strong> ${data.total_count}</p>`;
                        
                        if (data.sets && data.sets.length > 0) {
                            html += '<h4>套装列表:</h4>';
                            data.sets.forEach(set => {
                                html += `<div style="border: 1px solid #ccc; margin: 10px; padding: 10px;">`;
                                html += `<h5>${set.set_name} (${set.rarity})</h5>`;
                                html += `<p>描述: ${set.description || '无描述'}</p>`;
                                html += `<p>件数: ${set.items_count}</p>`;
                                html += `<p>境界要求: ${set.realm_requirement}</p>`;
                                if (set.effects) {
                                    html += `<p>效果: <pre>${JSON.stringify(set.effects, null, 2)}</pre></p>`;
                                }
                                html += `</div>`;
                            });
                        } else {
                            html += '<p style="color: orange;">没有找到套装数据</p>';
                        }
                        
                        resultDiv.innerHTML = html;
                    } else {
                        resultDiv.innerHTML = `<p style="color: red;">错误: ${data.message}</p>`;
                    }
                } else {
                    const text = await response.text();
                    resultDiv.innerHTML = `<p style="color: red;">HTTP错误 ${response.status}: ${text}</p>`;
                }
            } catch (error) {
                console.error('检查失败:', error);
                resultDiv.innerHTML = `<p style="color: red;">检查失败: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
