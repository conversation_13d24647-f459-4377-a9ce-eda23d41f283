<?php
require_once __DIR__ . '/../includes/functions.php';

// 检查维护模式
if (isMaintenanceMode()) {
    echo json_encode([
        'success' => false,
        'message' => '游戏正在维护中，请稍后再试',
        'maintenance' => true
    ]);
    exit;
}

// 记录API调用（如果开启了调试）
if (DEBUG_LOG_API_CALLS) {
    writeLog("API调用: register.php", 'DEBUG', 'api.log');
}

setJsonResponse();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '请求方法不允许']);
    exit;
}

// 获取POST数据
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    $input = $_POST;
}

$username = trim(isset($input['username']) ? $input['username'] : '');
$email = trim(isset($input['email']) ? $input['email'] : '');
$password = isset($input['password']) ? $input['password'] : '';
$confirmPassword = isset($input['confirmPassword']) ? $input['confirmPassword'] : '';
$csrfToken = isset($input['csrf_token']) ? $input['csrf_token'] : '';

// 验证CSRF令牌
if (!validateCSRFToken($csrfToken)) {
    echo json_encode(['success' => false, 'message' => 'CSRF令牌验证失败']);
    exit;
}

// 验证输入数据
$errors = [];

if (empty($username)) {
    $errors[] = '用户名不能为空';
} elseif (!validateUsername($username)) {
    $errors[] = '用户名格式不正确（3-20个字符，支持中英文、数字、下划线）';
} elseif (usernameExists($username)) {
    $errors[] = '用户名已存在';
}

if (empty($email)) {
    $errors[] = '邮箱不能为空';
} elseif (!validateEmail($email)) {
    $errors[] = '邮箱格式不正确';
} elseif (emailExists($email)) {
    $errors[] = '邮箱已被注册';
}

if (empty($password)) {
    $errors[] = '密码不能为空';
} elseif (!validatePassword($password)) {
    $errors[] = '密码至少需要6位字符';
}

if ($password !== $confirmPassword) {
    $errors[] = '两次输入的密码不一致';
}

if (!empty($errors)) {
    echo json_encode(['success' => false, 'message' => implode('，', $errors)]);
    exit;
}

// 注册用户
$userId = registerUser($username, $email, $password);

if ($userId) {
    echo json_encode([
        'success' => true, 
        'message' => '注册成功！',
        'user_id' => $userId
    ]);
} else {
    echo json_encode(['success' => false, 'message' => '注册失败，请稍后重试']);
}
?> 