/**
 * 物品处理工具类
 * 提供物品相关的处理和显示方法
 */
class BattleItemUtils {
    
    /**
     * 保存单个物品到背包
     * @param {Object} itemData 物品数据
     */
    static async saveItem(itemData) {
        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('item-utils', '保存单个物品:', itemData);
        }
        
        // 这里可以调用API保存单个物品
        // 暂时只显示提示
        alert(`物品 "${itemData.name}" 已保存到背包！`);
        
        // 关闭物品详情弹窗
        if (typeof ItemDetailPopup !== 'undefined') {
            ItemDetailPopup.close();
        }
    }

    /**
     * 显示更多物品信息
     * @param {Object} itemData 物品数据
     */
    static showItemDetail(itemData) {
        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('item-utils', '显示更多物品信息:', itemData);
        }
        
        // 🔧 修复：使用ItemDetailPopup组件显示详情
        if (typeof ItemDetailPopup !== 'undefined') {
            ItemDetailPopup.show(itemData, {
                isBattleContext: true, // 明确标识为战斗界面
                showEquip: false, // 战斗界面不允许装备
                showUnequip: false, // 战斗界面不允许卸下
                showUse: false, // 战斗界面不允许使用
                showRepair: false, // 战斗界面不允许修复
                showRecycle: false // 战斗界面不允许回收
            });
        } else {
            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.error('item-utils', 'ItemDetailPopup组件未加载');
            }
            // 如果ItemDetailPopup组件未加载，使用简单弹窗
            console.error('ItemDetailPopup组件未加载');
            alert(`查看 "${itemData.name}" 的更多信息...`);
        }
    }

    /**
     * 根据品质获取颜色类名
     * @param {string} rarity 品质等级
     * @returns {string} CSS类名
     */
    static getRarityClass(rarity) {
        const rarityClasses = {
            'common': 'quality-common',
            'uncommon': 'quality-uncommon', 
            'rare': 'quality-rare',
            'epic': 'quality-epic',
            'legendary': 'quality-legendary'
        };
        
        // 支持中文品质
        const chineseMapping = {
            '普通': 'common',
            '稀有': 'uncommon',
            '史诗': 'rare',
            '传说': 'epic',
            '神话': 'legendary'
        };
        
        const englishRarity = chineseMapping[rarity] || rarity;
        return rarityClasses[englishRarity] || 'quality-common';
    }

    /**
     * 根据品质获取颜色代码
     * @param {string} rarity 品质等级
     * @returns {string} 颜色代码
     */
    static getRarityColor(rarity) {
        const rarityColors = {
            'common': '#ffffff',    // 白色
            'uncommon': '#1eff00',  // 绿色
            'rare': '#0070dd',      // 蓝色
            'epic': '#a335ee',      // 紫色
            'legendary': '#ff8000'  // 橙色
        };
        
        // 支持中文品质
        const chineseMapping = {
            '普通': 'common',
            '稀有': 'uncommon',
            '史诗': 'rare',
            '传说': 'epic',
            '神话': 'legendary'
        };
        
        const englishRarity = chineseMapping[rarity] || rarity;
        return rarityColors[englishRarity] || '#ffffff';
    }

    /**
     * 构造物品详情HTML
     * @param {Object} itemData 物品数据
     * @returns {string} HTML字符串
     */
    static constructItemDetailHtml(itemData) {
        const rarityClass = this.getRarityClass(itemData.rarity || itemData.quality);
        const rarityColor = this.getRarityColor(itemData.rarity || itemData.quality);
        
        return `
            <div class="item-detail ${rarityClass}">
                <div class="item-header" style="border-color: ${rarityColor};">
                    <div class="item-name" style="color: ${rarityColor};">${itemData.name}</div>
                    <div class="item-type">${itemData.type || '未知类型'}</div>
                </div>
                <div class="item-stats">
                    ${itemData.quantity ? `<div class="item-quantity">数量: ${itemData.quantity}</div>` : ''}
                    ${itemData.sell_price ? `<div class="item-price">价值: ${itemData.sell_price} 金币</div>` : ''}
                </div>
                <div class="item-description">
                    ${itemData.description || '暂无描述'}
                </div>
            </div>
        `;
    }

    /**
     * 格式化物品信息
     * @param {Object} itemData 物品数据
     * @returns {Object} 格式化后的物品数据
     */
    static formatItemData(itemData) {
        return {
            name: itemData.name || '未知物品',
            type: itemData.type || 'material',
            rarity: itemData.rarity || itemData.quality || 'common',
            quantity: parseInt(itemData.quantity) || 1,
            sell_price: parseInt(itemData.sell_price) || 0,
            description: itemData.description || '暂无描述',
            item_data: itemData.item_data || {}
        };
    }

    /**
     * 检查物品是否可以使用
     * @param {Object} itemData 物品数据
     * @param {string} context 使用上下文（battle, inventory等）
     * @returns {boolean} 是否可以使用
     */
    static canUseItem(itemData, context = 'battle') {
        if (context === 'battle') {
            // 战斗中只能使用消耗品
            return itemData.type === 'consumable' || itemData.type === 'potion';
        }
        
        return true;
    }

    /**
     * 获取物品图标路径
     * @param {Object} itemData 物品数据
     * @returns {string} 图标路径
     */
    static getItemIconPath(itemData) {
        if (itemData.icon_image) {
            return itemData.icon_image;
        }
        
        // 根据物品类型返回默认图标
        const defaultIcons = {
            'weapon': 'assets/images/icons/weapon_default.png',
            'armor': 'assets/images/icons/armor_default.png',
            'consumable': 'assets/images/icons/potion_default.png',
            'material': 'assets/images/icons/material_default.png'
        };
        
        return defaultIcons[itemData.type] || 'assets/images/icons/item_default.png';
    }
}

// 全局导出工具类
window.BattleItemUtils = BattleItemUtils;

if (window.BattleDebugConfig) {
    window.BattleDebugConfig.log('item-utils', '🎒 物品处理工具模块已加载');
} else {
    console.log('�� 物品处理工具模块已加载');
} 