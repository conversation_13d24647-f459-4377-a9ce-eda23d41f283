/**
 * 一念修仙 - 全局音乐管理器 v4.2.2
 * 真正的全局音乐系统，支持完整的状态持久化，优化性能减少资源消耗
 * 新增：iOS Safari兼容性修复，无需用户交互的音频播放
 * 修复：新账号真正默认关闭音乐，构造函数默认值改为false
 */

(function() {
    'use strict';
    
    // 防止重复加载
    if (window.globalMusicManager) {
        console.log('🎵 全局音乐管理器已存在，跳过重复初始化');
        return;
    }
    
    class GlobalMusicManager {
        constructor() {
            this.backgroundAudio = null;
            this.battleAudio = null;
            this.currentMusicType = 'background'; // 'background' or 'battle'
            this.isMusicEnabled = false; // 新账号默认关闭音乐
            this.bgVolume = 0.3;
            this.battleVolume = 0.4;
            this.isInitialized = false;
            
            // iOS兼容性相关
            this.isIOS = this.detectIOS();
            this.audioContext = null;
            this.audioContextUnlocked = false;
            this.pendingPlay = null; // 待播放的音频
            
            // 音乐文件配置
            this.musicFiles = {
                background: [
                    'assets/sound/game_music_bg_1.mp3',
                    'assets/sound/game_music_bg_2.mp3'
                ],
                battle: [
                    'assets/sound/battle_music_bg_1.mp3',
                    'assets/sound/battle_music_bg_2.mp3'
                ]
            };
            
            // 当前播放索引
            this.currentBgIndex = 0;
            this.currentBattleIndex = 0;
            
            // 状态保存优化
            this.saveStateTimer = null;
            this.stateUpdateInterval = null;
            this.lastSavedState = null; // 缓存上次保存的状态
            this.debounceDelay = 2000; // 防抖延迟2秒
            
            // 日志控制
            this.debugMode = false; // 调试模式开关
            this.logLevel = 'error'; // 日志级别：'debug', 'info', 'warn', 'error', 'silent'
            
            this.init();
        }
        
        // 新增：检测iOS设备
        detectIOS() {
            const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
            const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
            const isWebkit = navigator.userAgent.includes('WebKit');
            
            // 更广泛的iOS检测，包括iPadOS
            const isIOSDevice = isIOS || (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
            
            return isIOSDevice || (isSafari && isWebkit);
        }
        
        // 新增：初始化AudioContext（iOS必需）
        initAudioContext() {
            if (!this.isIOS || this.audioContext) return;
            
            try {
                // 创建AudioContext
                window.AudioContext = window.AudioContext || window.webkitAudioContext;
                this.audioContext = new AudioContext();
                
                this.log('info', 'AudioContext已创建', { state: this.audioContext.state });
                
                // 尝试解锁AudioContext
                this.unlockAudioContext();
                
            } catch (error) {
                this.log('warn', 'AudioContext创建失败', error);
            }
        }
        
        // 新增：解锁AudioContext
        unlockAudioContext() {
            if (!this.audioContext || this.audioContextUnlocked) return;
            
            const unlockAudio = () => {
                if (this.audioContext.state === 'suspended') {
                    this.audioContext.resume().then(() => {
                        this.audioContextUnlocked = true;
                        this.log('info', 'AudioContext已解锁');
                        
                        // 如果有待播放的音频，现在播放
                        if (this.pendingPlay) {
                            this.playPendingAudio();
                        }
                        
                        // 移除事件监听器
                        document.removeEventListener('touchstart', unlockAudio);
                        document.removeEventListener('touchend', unlockAudio);
                        document.removeEventListener('click', unlockAudio);
                        document.removeEventListener('scroll', unlockAudio);
                    }).catch(error => {
                        this.log('warn', 'AudioContext解锁失败', error);
                    });
                } else {
                    this.audioContextUnlocked = true;
                    this.log('info', 'AudioContext状态正常');
                    
                    if (this.pendingPlay) {
                        this.playPendingAudio();
                    }
                }
            };
            
            // 监听多种用户交互事件
            document.addEventListener('touchstart', unlockAudio, { once: true, passive: true });
            document.addEventListener('touchend', unlockAudio, { once: true, passive: true });
            document.addEventListener('click', unlockAudio, { once: true, passive: true });
            document.addEventListener('scroll', unlockAudio, { once: true, passive: true });
            
            // 立即尝试解锁（某些情况下可能直接成功）
            unlockAudio();
        }
        
        // 新增：播放待播放的音频
        playPendingAudio() {
            if (!this.pendingPlay) return;
            
            const { type, audio } = this.pendingPlay;
            this.pendingPlay = null;
            
            this.log('info', `播放待播放的${type}音乐`);
            
            if (type === 'background') {
                this.playBackgroundMusic();
            } else if (type === 'battle') {
                this.playBattleMusic();
            }
        }
        
        // 新增：iOS兼容的音频播放
        playAudioWithIOSCompat(audio, type) {
            if (!this.isIOS) {
                // 非iOS设备直接播放
                return audio.play();
            }
            
            // iOS设备需要特殊处理
            if (!this.audioContextUnlocked) {
                this.log('info', `iOS音频上下文未解锁，暂存${type}音乐`);
                this.pendingPlay = { type, audio };
                this.unlockAudioContext();
                return Promise.resolve();
            }
            
            // 设置音频属性以提高iOS兼容性
            audio.load(); // 强制重新加载
            audio.muted = false;
            audio.autoplay = false;
            
            // iOS特殊的播放尝试
            return new Promise((resolve, reject) => {
                const playAttempt = () => {
                    const playPromise = audio.play();
                    
                    if (playPromise !== undefined) {
                        playPromise
                            .then(() => {
                                this.log('info', `iOS ${type}音乐播放成功`);
                                resolve();
                            })
                            .catch(error => {
                                this.log('warn', `iOS ${type}音乐播放失败，重试中`, error);
                                // 稍后重试
                                setTimeout(() => {
                                    const retryPromise = audio.play();
                                    if (retryPromise !== undefined) {
                                        retryPromise.then(resolve).catch(reject);
                                    } else {
                                        resolve();
                                    }
                                }, 100);
                            });
                    } else {
                        resolve();
                    }
                };
                
                // 确保音频已加载
                if (audio.readyState >= 2) {
                    playAttempt();
                } else {
                    audio.addEventListener('canplay', playAttempt, { once: true });
                }
            });
        }
        
        // 新增：日志输出控制
        log(level, message, data = null) {
            if (this.logLevel === 'silent') return;
            
            const levels = { debug: 0, info: 1, warn: 2, error: 3, silent: 4 };
            const currentLevel = levels[this.logLevel] || 1;
            const messageLevel = levels[level] || 1;
            
            if (messageLevel >= currentLevel) {
                const prefix = {
                    debug: '🔧',
                    info: '🎵',
                    warn: '⚠️',
                    error: '❌'
                }[level] || '🎵';
                
                if (data) {
                    console[level === 'debug' ? 'log' : level](`${prefix} ${message}`, data);
                } else {
                    console[level === 'debug' ? 'log' : level](`${prefix} ${message}`);
                }
            }
        }
        
        init() {
            this.log('info', `全局音乐管理器 v4.2.2 初始化中... (iOS设备: ${this.isIOS})`);
            
            // iOS设备初始化AudioContext
            if (this.isIOS) {
                this.initAudioContext();
            }
            
            // 从localStorage加载设置
            this.loadSettings();
            
            // 恢复音乐状态
            this.restoreMusicState();
            
            // 监听设置变化
            window.addEventListener('storage', (e) => {
                if (e.key === 'soundEnabled') {
                    this.isMusicEnabled = e.newValue === 'true';
                    if (!this.isMusicEnabled) {
                        this.stopAll();
                    } else {
                        this.startMusic();
                    }
                }
            });
            
            // 监听页面可见性变化
            document.addEventListener('visibilitychange', () => {
                if (document.hidden) {
                    this.saveCurrentStateDebounced(); // 使用防抖保存
                    this.pauseCurrentMusic();
                } else if (this.isMusicEnabled) {
                    this.resumeCurrentMusic();
                }
            });
            
            // 监听页面卸载，保存状态
            window.addEventListener('beforeunload', () => {
                this.saveCurrentStateImmediate(); // 立即保存，不防抖
            });
            
            // 监听页面加载完成
            window.addEventListener('load', () => {
                if (this.isMusicEnabled) {
                    this.restoreAndPlay();
                }
            });
            
            // iOS设备页面交互监听
            if (this.isIOS) {
                this.setupIOSInteractionListeners();
            }
            
            // 预加载音频文件
            this.preloadAudio();
            
            this.isInitialized = true;
            this.log('info', '全局音乐管理器初始化完成');
            
            // 启动优化的状态监控
            this.startOptimizedStateMonitoring();
            
            // 延迟自动开始播放
            if (this.isMusicEnabled) {
                setTimeout(() => {
                    this.restoreAndPlay();
                }, this.isIOS ? 1000 : 500); // iOS设备延迟更长
            }
        }
        
        // 新增：设置iOS交互监听器
        setupIOSInteractionListeners() {
            // 监听多种交互事件，自动尝试播放音乐
            const interactionEvents = ['touchstart', 'touchend', 'click', 'scroll', 'keydown'];
            
            const autoPlayHandler = () => {
                if (this.isMusicEnabled && !this.audioContextUnlocked) {
                    this.unlockAudioContext();
                }
                
                // 如果音乐应该播放但没有播放，尝试播放
                if (this.isMusicEnabled && !this.isPlaying()) {
                    setTimeout(() => {
                        this.startMusic();
                    }, 100);
                }
            };
            
            interactionEvents.forEach(event => {
                document.addEventListener(event, autoPlayHandler, { 
                    once: false, 
                    passive: true 
                });
            });
            
            // 额外的iOS特殊事件监听
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(() => {
                    if (this.isMusicEnabled && !this.isPlaying()) {
                        this.startMusic();
                    }
                }, 1000);
            });
            
            // 监听页面获得焦点
            window.addEventListener('focus', () => {
                if (this.isMusicEnabled && !this.isPlaying()) {
                    setTimeout(() => {
                        this.startMusic();
                    }, 500);
                }
            });
            
            // 监听页面变为可见
            document.addEventListener('visibilitychange', () => {
                if (!document.hidden && this.isMusicEnabled && !this.isPlaying()) {
                    setTimeout(() => {
                        this.startMusic();
                    }, 500);
                }
            });
            
            this.log('info', 'iOS交互监听器已设置');
        }
        
        loadSettings() {
            const soundEnabled = localStorage.getItem('soundEnabled');
            // 新账号默认关闭音乐，只有明确设置为'true'时才开启
            this.isMusicEnabled = soundEnabled === 'true';
            this.log('info', `音效状态: ${this.isMusicEnabled ? '开启' : '关闭'}`);
        }
        
        preloadAudio() {
            // 预加载背景音乐
            this.backgroundAudio = new Audio(this.musicFiles.background[this.currentBgIndex]);
            this.backgroundAudio.loop = false;
            this.backgroundAudio.volume = this.bgVolume;
            this.backgroundAudio.preload = this.isIOS ? 'none' : 'auto'; // iOS使用none避免预加载问题
            
            // iOS特殊设置
            if (this.isIOS) {
                this.backgroundAudio.playsInline = true;
                this.backgroundAudio.muted = false;
                this.backgroundAudio.autoplay = false;
            }
            
            // 预加载战斗音乐
            this.battleAudio = new Audio(this.musicFiles.battle[this.currentBattleIndex]);
            this.battleAudio.loop = false;
            this.battleAudio.volume = this.battleVolume;
            this.battleAudio.preload = this.isIOS ? 'none' : 'auto';
            
            // iOS特殊设置
            if (this.isIOS) {
                this.battleAudio.playsInline = true;
                this.battleAudio.muted = false;
                this.battleAudio.autoplay = false;
            }
            
            // 设置音乐结束事件
            this.backgroundAudio.addEventListener('ended', () => {
                if (this.currentMusicType === 'background') {
                    this.playNextBackground();
                }
            });
            
            this.battleAudio.addEventListener('ended', () => {
                if (this.currentMusicType === 'battle') {
                    this.playNextBattle();
                }
            });
            
            // 优化：减少timeupdate事件的处理频率
            let lastUpdateTime = 0;
            this.backgroundAudio.addEventListener('timeupdate', () => {
                if (this.currentMusicType === 'background') {
                    const now = Date.now();
                    // 限制为每5秒更新一次
                    if (now - lastUpdateTime > 5000) {
                        this.updatePlaybackStateOptimized();
                        lastUpdateTime = now;
                    }
                }
            });
            
            this.battleAudio.addEventListener('timeupdate', () => {
                if (this.currentMusicType === 'battle') {
                    const now = Date.now();
                    // 限制为每5秒更新一次
                    if (now - lastUpdateTime > 5000) {
                        this.updatePlaybackStateOptimized();
                        lastUpdateTime = now;
                    }
                }
            });
            
            // 添加错误处理
            this.backgroundAudio.addEventListener('error', (e) => {
                this.log('warn', '背景音乐加载失败', e);
            });
            
            this.battleAudio.addEventListener('error', (e) => {
                this.log('warn', '战斗音乐加载失败', e);
            });
            
            // iOS特殊的加载监听
            if (this.isIOS) {
                this.backgroundAudio.addEventListener('loadstart', () => {
                    this.log('debug', '背景音乐开始加载');
                });
                
                this.backgroundAudio.addEventListener('canplay', () => {
                    this.log('debug', '背景音乐可以播放');
                });
                
                this.battleAudio.addEventListener('loadstart', () => {
                    this.log('debug', '战斗音乐开始加载');
                });
                
                this.battleAudio.addEventListener('canplay', () => {
                    this.log('debug', '战斗音乐可以播放');
                });
            }
            
            this.log('debug', '音频文件预加载完成');
        }
        
        // 优化：启动优化的状态监控
        startOptimizedStateMonitoring() {
            // 每30秒保存一次状态（降低频率）
            this.stateUpdateInterval = setInterval(() => {
                if (this.isPlaying()) {
                    this.saveCurrentStateDebounced();
                }
            }, 30000);
        }
        
        // 优化：减少频繁的状态更新
        updatePlaybackStateOptimized() {
            // 只在真正需要时更新状态，不输出日志
            if (this.isPlaying()) {
                this.saveCurrentStateDebounced();
            }
        }
        
        // 新增：防抖状态保存
        saveCurrentStateDebounced() {
            // 清除之前的定时器
            if (this.saveStateTimer) {
                clearTimeout(this.saveStateTimer);
            }
            
            // 设置新的防抖定时器
            this.saveStateTimer = setTimeout(() => {
                this.saveCurrentStateImmediate();
            }, this.debounceDelay);
        }
        
        // 优化：立即保存状态（带状态对比）
        saveCurrentStateImmediate() {
            try {
                const currentAudio = this.currentMusicType === 'background' ? this.backgroundAudio : this.battleAudio;
                const currentIndex = this.currentMusicType === 'background' ? this.currentBgIndex : this.currentBattleIndex;
                
                const state = {
                    musicType: this.currentMusicType,
                    currentIndex: currentIndex,
                    currentTime: currentAudio ? Math.floor(currentAudio.currentTime) : 0, // 只保存到秒
                    isPlaying: currentAudio ? !currentAudio.paused : false,
                    volume: currentAudio ? currentAudio.volume : (this.currentMusicType === 'background' ? this.bgVolume : this.battleVolume),
                    timestamp: Date.now(),
                    musicEnabled: this.isMusicEnabled
                };
                
                // 只有状态真正变化时才保存和记录日志
                if (!this.lastSavedState || this.isStateChanged(state, this.lastSavedState)) {
                    localStorage.setItem('globalMusicState', JSON.stringify(state));
                    this.lastSavedState = { ...state };
                    this.log('debug', '音乐状态已保存', { time: state.currentTime, type: state.musicType });
                }
            } catch (error) {
                this.log('warn', '保存音乐状态失败', error);
            }
        }
        
        // 新增：检查状态是否真正变化
        isStateChanged(newState, oldState) {
            return (
                newState.musicType !== oldState.musicType ||
                newState.currentIndex !== oldState.currentIndex ||
                Math.abs(newState.currentTime - oldState.currentTime) > 5 || // 时间差超过5秒
                newState.isPlaying !== oldState.isPlaying ||
                newState.musicEnabled !== oldState.musicEnabled
            );
        }
        
        // 兼容旧方法名
        saveCurrentState() {
            this.saveCurrentStateDebounced();
        }
        
        // 新增：恢复音乐状态
        restoreMusicState() {
            try {
                const savedState = localStorage.getItem('globalMusicState');
                if (!savedState) {
                    this.log('debug', '无保存状态，使用默认设置');
                    return;
                }
                
                const state = JSON.parse(savedState);
                const now = Date.now();
                
                // 检查状态是否过期（30分钟）
                if (now - state.timestamp > 30 * 60 * 1000) {
                    this.log('debug', '保存状态已过期，清除');
                    localStorage.removeItem('globalMusicState');
                    return;
                }
                
                // 恢复基本状态
                this.currentMusicType = state.musicType || 'background';
                this.isMusicEnabled = state.musicEnabled !== false;
                
                if (state.musicType === 'background') {
                    this.currentBgIndex = state.currentIndex || 0;
                } else {
                    this.currentBattleIndex = state.currentIndex || 0;
                }
                
                this.log('info', '音乐状态已恢复', { time: state.currentTime, type: state.musicType });
                this.lastSavedState = { ...state };
                
            } catch (error) {
                this.log('warn', '恢复音乐状态失败', error);
                localStorage.removeItem('globalMusicState');
            }
        }
        
        // 新增：恢复并播放音乐
        restoreAndPlay() {
            if (!this.isMusicEnabled || !this.isInitialized) return;
            
            try {
                const savedState = localStorage.getItem('globalMusicState');
                if (!savedState) {
                    this.startMusic();
                    return;
                }
                
                const state = JSON.parse(savedState);
                const now = Date.now();
                
                // 检查状态是否过期
                if (now - state.timestamp > 30 * 60 * 1000) {
                    this.startMusic();
                    return;
                }
                
                // 根据当前页面决定音乐类型
                const isBattlePage = window.location.pathname.includes('battle.html');
                const targetMusicType = isBattlePage ? 'battle' : 'background';
                
                // 如果页面类型和保存的音乐类型一致，恢复播放位置
                if (state.musicType === targetMusicType && state.isPlaying) {
                    this.log('info', `恢复音乐播放位置: ${state.currentTime}秒`);
                    
                    if (targetMusicType === 'background') {
                        this.currentMusicType = 'background';
                        this.currentBgIndex = state.currentIndex || 0;
                        this.backgroundAudio.src = this.musicFiles.background[this.currentBgIndex];
                        this.backgroundAudio.volume = state.volume || this.bgVolume;
                        
                        // iOS设备需要延迟设置currentTime
                        if (this.isIOS) {
                            this.backgroundAudio.addEventListener('loadeddata', () => {
                                this.backgroundAudio.currentTime = state.currentTime || 0;
                            }, { once: true });
                            this.backgroundAudio.load();
                        } else {
                            this.backgroundAudio.currentTime = state.currentTime || 0;
                        }
                        
                        this.playBackgroundMusic();
                    } else {
                        this.currentMusicType = 'battle';
                        this.currentBattleIndex = state.currentIndex || 0;
                        this.battleAudio.src = this.musicFiles.battle[this.currentBattleIndex];
                        this.battleAudio.volume = state.volume || this.battleVolume;
                        
                        // iOS设备需要延迟设置currentTime
                        if (this.isIOS) {
                            this.battleAudio.addEventListener('loadeddata', () => {
                                this.battleAudio.currentTime = state.currentTime || 0;
                            }, { once: true });
                            this.battleAudio.load();
                        } else {
                            this.battleAudio.currentTime = state.currentTime || 0;
                        }
                        
                        this.playBattleMusic();
                    }
                } else {
                    // 页面类型不同，切换音乐类型
                    this.startMusic();
                }
                
            } catch (error) {
                this.log('warn', '恢复播放失败', error);
                this.startMusic();
            }
        }
        
        // 新增：检查是否正在播放
        isPlaying() {
            const currentAudio = this.currentMusicType === 'background' ? this.backgroundAudio : this.battleAudio;
            return currentAudio && !currentAudio.paused;
        }
        
        startMusic() {
            if (!this.isMusicEnabled || !this.isInitialized) return;
            
            // 根据当前页面判断音乐类型
            const isBattlePage = window.location.pathname.includes('battle.html');
            
            if (isBattlePage) {
                this.switchToBattleMusic();
            } else {
                this.switchToBackgroundMusic();
            }
        }
        
        switchToBackgroundMusic() {
            if (this.currentMusicType === 'background' && !this.backgroundAudio.paused) {
                this.log('debug', '背景音乐已在播放');
                return;
            }
            
            this.log('info', '切换到背景音乐');
            this.currentMusicType = 'background';
            
            // 淡出战斗音乐
            if (!this.battleAudio.paused) {
                this.fadeOut(this.battleAudio);
            }
            
            // 播放背景音乐
            this.playBackgroundMusic();
        }
        
        switchToBattleMusic() {
            if (this.currentMusicType === 'battle' && !this.battleAudio.paused) {
                this.log('debug', '战斗音乐已在播放');
                return;
            }
            
            this.log('info', '切换到战斗音乐');
            this.currentMusicType = 'battle';
            
            // 淡出背景音乐
            if (!this.backgroundAudio.paused) {
                this.fadeOut(this.backgroundAudio);
            }
            
            // 播放战斗音乐
            this.playBattleMusic();
        }
        
        playBackgroundMusic() {
            if (!this.isMusicEnabled) return;
            
            // 使用iOS兼容的播放方法
            this.playAudioWithIOSCompat(this.backgroundAudio, 'background').then(() => {
                this.log('info', `播放背景音乐: ${this.musicFiles.background[this.currentBgIndex]}`);
                this.fadeIn(this.backgroundAudio, this.bgVolume);
                this.saveCurrentStateDebounced(); // 使用防抖保存
            }).catch(error => {
                this.log('warn', '背景音乐播放失败', error);
            });
        }
        
        playBattleMusic() {
            if (!this.isMusicEnabled) return;
            
            // 使用iOS兼容的播放方法
            this.playAudioWithIOSCompat(this.battleAudio, 'battle').then(() => {
                this.log('info', `播放战斗音乐: ${this.musicFiles.battle[this.currentBattleIndex]}`);
                this.fadeIn(this.battleAudio, this.battleVolume);
                this.saveCurrentStateDebounced(); // 使用防抖保存
            }).catch(error => {
                this.log('warn', '战斗音乐播放失败', error);
            });
        }
        
        playNextBackground() {
            this.currentBgIndex = (this.currentBgIndex + 1) % this.musicFiles.background.length;
            
            // 加载下一首背景音乐
            this.backgroundAudio.src = this.musicFiles.background[this.currentBgIndex];
            
            // iOS需要特殊处理
            if (this.isIOS) {
                this.backgroundAudio.load();
                this.backgroundAudio.addEventListener('canplay', () => {
                    if (this.currentMusicType === 'background') {
                        this.playBackgroundMusic();
                    }
                }, { once: true });
            } else {
                this.backgroundAudio.load();
                // 播放
                if (this.currentMusicType === 'background') {
                    setTimeout(() => {
                        this.playBackgroundMusic();
                    }, 500);
                }
            }
        }
        
        playNextBattle() {
            this.currentBattleIndex = (this.currentBattleIndex + 1) % this.musicFiles.battle.length;
            
            // 加载下一首战斗音乐
            this.battleAudio.src = this.musicFiles.battle[this.currentBattleIndex];
            
            // iOS需要特殊处理
            if (this.isIOS) {
                this.battleAudio.load();
                this.battleAudio.addEventListener('canplay', () => {
                    if (this.currentMusicType === 'battle') {
                        this.playBattleMusic();
                    }
                }, { once: true });
            } else {
                this.battleAudio.load();
                // 播放
                if (this.currentMusicType === 'battle') {
                    setTimeout(() => {
                        this.playBattleMusic();
                    }, 500);
                }
            }
        }
        
        pauseCurrentMusic() {
            if (!this.backgroundAudio.paused) {
                this.backgroundAudio.pause();
            }
            if (!this.battleAudio.paused) {
                this.battleAudio.pause();
            }
        }
        
        resumeCurrentMusic() {
            if (this.currentMusicType === 'background' && this.backgroundAudio.paused) {
                this.playAudioWithIOSCompat(this.backgroundAudio, 'background').catch(e => this.log('warn', '恢复背景音乐失败', e));
            } else if (this.currentMusicType === 'battle' && this.battleAudio.paused) {
                this.playAudioWithIOSCompat(this.battleAudio, 'battle').catch(e => this.log('warn', '恢复战斗音乐失败', e));
            }
        }
        
        stopAll() {
            this.log('info', '停止所有音乐');
            
            if (this.backgroundAudio) {
                this.backgroundAudio.pause();
                this.backgroundAudio.currentTime = 0;
            }
            if (this.battleAudio) {
                this.battleAudio.pause();
                this.battleAudio.currentTime = 0;
            }
            
            // 清除保存的状态
            localStorage.removeItem('globalMusicState');
            this.lastSavedState = null;
        }
        
        fadeIn(audio, targetVolume) {
            if (!audio) return;
            
            audio.volume = 0;
            const fadeAudio = setInterval(() => {
                if (audio.volume < targetVolume - 0.05) {
                    audio.volume += 0.05;
                } else {
                    audio.volume = targetVolume;
                    clearInterval(fadeAudio);
                }
            }, 100);
        }
        
        fadeOut(audio) {
            if (!audio) return;
            
            const fadeAudio = setInterval(() => {
                if (audio.volume > 0.05) {
                    audio.volume -= 0.05;
                } else {
                    audio.volume = 0;
                    audio.pause();
                    clearInterval(fadeAudio);
                }
            }, 100);
        }
        
        setMusicEnabled(enabled) {
            this.isMusicEnabled = enabled;
            
            if (enabled) {
                this.startMusic();
            } else {
                this.stopAll();
            }
            
            this.log('info', `音乐状态更新: ${enabled ? '开启' : '关闭'}`);
        }
        
        // 新增：设置日志级别
        setLogLevel(level) {
            this.logLevel = level;
            this.log('info', `日志级别设置为: ${level}`);
        }
        
        // 新增：开启/关闭调试模式
        setDebugMode(enabled) {
            this.debugMode = enabled;
            this.logLevel = enabled ? 'debug' : 'error';
            this.log('info', `调试模式: ${enabled ? '开启' : '关闭'}`);
        }
        
        // 新增：清理资源
        destroy() {
            if (this.stateUpdateInterval) {
                clearInterval(this.stateUpdateInterval);
            }
            if (this.saveStateTimer) {
                clearTimeout(this.saveStateTimer);
            }
            this.saveCurrentStateImmediate();
        }
        
        // 新增：强制启动音乐（用于调试和手动触发）
        forceStartMusic() {
            this.log('info', '强制启动音乐...');
            
            if (this.isIOS && !this.audioContextUnlocked) {
                this.unlockAudioContext();
            }
            
            // 强制重新加载音频
            if (this.backgroundAudio) {
                this.backgroundAudio.load();
            }
            if (this.battleAudio) {
                this.battleAudio.load();
            }
            
            // 启动音乐
            setTimeout(() => {
                this.startMusic();
            }, 200);
        }
        
        // 公共API方法，供战斗系统调用
        playBackgroundMode() {
            this.switchToBackgroundMusic();
        }
        
        playBattleMode() {
            this.switchToBattleMusic();
        }
        
        getCurrentMusicInfo() {
            return {
                musicEnabled: this.isMusicEnabled,
                currentMusicType: this.currentMusicType,
                backgroundPlaying: this.backgroundAudio && !this.backgroundAudio.paused,
                battlePlaying: this.battleAudio && !this.battleAudio.paused,
                backgroundIndex: this.currentBgIndex,
                battleIndex: this.currentBattleIndex,
                currentTime: this.currentMusicType === 'background' ? 
                    (this.backgroundAudio ? this.backgroundAudio.currentTime : 0) :
                    (this.battleAudio ? this.battleAudio.currentTime : 0),
                duration: this.currentMusicType === 'background' ? 
                    (this.backgroundAudio ? this.backgroundAudio.duration : 0) :
                    (this.battleAudio ? this.battleAudio.duration : 0),
                logLevel: this.logLevel,
                debugMode: this.debugMode,
                // iOS兼容性信息
                isIOS: this.isIOS,
                audioContextState: this.audioContext ? this.audioContext.state : 'none',
                audioContextUnlocked: this.audioContextUnlocked,
                pendingPlay: this.pendingPlay ? this.pendingPlay.type : null,
                backgroundReadyState: this.backgroundAudio ? this.backgroundAudio.readyState : 0,
                battleReadyState: this.battleAudio ? this.battleAudio.readyState : 0
            };
        }
    }
    
    // 创建全局实例
    window.globalMusicManager = new GlobalMusicManager();
    
    // 兼容旧的接口
    window.musicManager = {
        playBackgroundMusic: () => window.globalMusicManager.playBackgroundMode(),
        playBattleMusic: () => window.globalMusicManager.playBattleMode(),
        stopBattleMusic: () => window.globalMusicManager.playBackgroundMode(),
        setMusicEnabled: (enabled) => window.globalMusicManager.setMusicEnabled(enabled),
        getCurrentMusicInfo: () => window.globalMusicManager.getCurrentMusicInfo(),
        // 新增：调试控制接口
        setLogLevel: (level) => window.globalMusicManager.setLogLevel(level),
        setDebugMode: (enabled) => window.globalMusicManager.setDebugMode(enabled),
        // 新增：iOS兼容性接口
        forceStartMusic: () => window.globalMusicManager.forceStartMusic()
    };
    
    // 页面卸载时清理
    window.addEventListener('beforeunload', () => {
        if (window.globalMusicManager) {
            window.globalMusicManager.destroy();
        }
    });
    
    console.log('🎵 全局音乐管理器 v4.2.2 加载完成 (新账号默认关闭音乐)');
})();

