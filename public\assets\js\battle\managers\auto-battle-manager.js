/**
 * 挂机系统管理器
 * 负责处理自动战斗的状态管理和流程控制
 */
class BattleAutoBattleManager {
    constructor(battleSystem) {
        this.battleSystem = battleSystem;
        this.dataManager = null; // 将在后续设置

        // 🏆 检查是否为竞技场模式
        const urlParams = new URLSearchParams(window.location.search);
        this.isArenaMode = urlParams.get('arena') === '1';

        // 从localStorage恢复挂机状态（竞技场模式下跳过）
        this.isAutoBattleMode =
            !this.isArenaMode && localStorage.getItem('autoBattleMode') === 'true';
        this.isAutoBattlePaused = false; // 🆕 挂机暂停状态（失败后暂停）
        this.autoBattleCountdown = null;

        // 🆕 新增：挂机失败标识管理（竞技场模式下跳过）
        this.autoBattleFailedFlag =
            !this.isArenaMode && localStorage.getItem('autoBattleFailedFlag') === 'true';

        console.log('🤖 挂机系统管理器初始化完成');
        if (!this.isArenaMode) {
            console.log('🤖 挂机状态恢复:', this.isAutoBattleMode ? '开启' : '关闭');
            console.log('🤖 挂机失败标识:', this.autoBattleFailedFlag ? '存在' : '无');
        } else {
            console.log('🏆 竞技场模式：跳过挂机状态初始化');
        }
    }

    /**
     * 设置数据管理器引用
     */
    setDataManager(dataManager) {
        this.dataManager = dataManager;
        console.log('🔗 挂机管理器已关联数据管理器');
    }

    /**
     * 处理挂机延续逻辑
     * @param {boolean} isVictory 是否胜利
     */
    async handleAutoBattleContinuation(isVictory) {
        console.log(`🤖 挂机模式：${isVictory ? '胜利' : '失败'}后继续在本层挂机`);

        // 等待1秒让用户看到战斗结果
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 🔧 简化逻辑：无论胜利还是失败，都在本层继续挂机
        console.log('🤖 挂机模式：继续在当前层循环挂机，不推进进度');
        this.battleSystem.victoryPanelManager.startAutoBattleMode();
    }

    /**
     * 🆕 处理战斗失败的逻辑（简化版）
     */
    async handleBattleFailure() {
        try {
            console.log('🔻 战斗失败，继续在本层挂机');

            // 🔧 Safari兼容性：强制垃圾回收和内存清理
            if (window.gc && typeof window.gc === 'function') {
                window.gc();
            }
        } catch (error) {
            console.error('❌ 处理战斗失败时出错:', error);
        }
    }

    /**
     * 🆕 同步数据库进度到当前前端显示的层数
     */
    async syncDatabaseProgress() {
        try {
            const currentStage = this.battleSystem.dataManager.currentStage;
            const currentArea = this.battleSystem.dataManager.getCurrentArea();

            if (!currentArea || !currentArea.areaId) {
                console.warn('⚠️ 无法获取当前地图信息，跳过数据库同步');
                return;
            }

            console.log(`🔄 同步数据库进度：地图 ${currentArea.areaId}, 层数 ${currentStage}`);

            // 调用后端API同步数据库进度
            const formData = new FormData();
            formData.append('action', 'set_stage_progress');
            formData.append('map_code', currentArea.areaId);
            formData.append('target_stage', currentStage);

            // 🔧 Safari兼容性：添加超时控制和重试机制
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('请求超时')), 10000); // 10秒超时
            });

            const apiUrl = window.GameConfig
                ? window.GameConfig.getApiUrl('battle_drops_unified.php')
                : '../../../src/api/battle_drops_unified.php';
            const fetchPromise = fetch(apiUrl, {
                method: 'POST',
                body: formData,
                // 🔧 Safari兼容性：添加请求头
                headers: {
                    'Cache-Control': 'no-cache',
                },
            });

            const response = await Promise.race([fetchPromise, timeoutPromise]);

            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    console.log(`✅ 数据库进度同步成功：${currentArea.areaId} 第${currentStage}层`);
                } else {
                    console.warn('⚠️ 数据库进度同步失败:', result.message);
                }
            } else {
                console.warn('⚠️ 数据库进度同步请求失败:', response.status);
            }
        } catch (error) {
            console.error('❌ 同步数据库进度时出错:', error);
            // 🔧 Safari兼容性：不因同步失败而中断挂机
            if (error.message === '请求超时') {
                console.warn('⚠️ Safari网络超时，继续挂机流程');
            }
        }
    }

    /**
     * 🆕 设置挂机失败标识
     */
    setAutoBattleFailedFlag() {
        this.autoBattleFailedFlag = true;
        localStorage.setItem('autoBattleFailedFlag', 'true');
        console.log('🚩 挂机失败标识已设置');
    }

    /**
     * 🆕 清除挂机失败标识
     */
    clearAutoBattleFailedFlag() {
        this.autoBattleFailedFlag = false;
        localStorage.removeItem('autoBattleFailedFlag');
        console.log('🗑️ 挂机失败标识已清除');

        // 🔧 修复：清除标识后更新按钮文字
        this.updateAutoBattleButtonText();
    }

    /**
     * 🆕 检查是否有挂机失败标识
     */
    hasAutoBattleFailedFlag() {
        return this.autoBattleFailedFlag;
    }

    /**
     * 🆕 通用的按钮文字和提示更新方法（简化版）
     * 可以在任何时候调用，更新挂机按钮的显示文字
     */
    updateAutoBattleButtonText() {
        const autoBattleText = document.querySelector('[data-auto-battle-text]');
        const autoBattleButton = document.querySelector('[data-auto-battle-button]');
        const modeHint = document.querySelector('[data-auto-battle-mode-hint]');

        if (!autoBattleText) {
            console.warn('⚠️ 找不到挂机按钮文字元素');
            return;
        }

        const isAutoBattle = this.isAutoBattleMode;

        console.log('🔄 更新挂机按钮显示:', {
            isAutoBattle: isAutoBattle,
        });

        if (isAutoBattle) {
            // 挂机模式中：显示"停止挂机"
            autoBattleText.textContent = '停止挂机';
            if (autoBattleButton) {
                autoBattleButton.classList.add('stop-mode');
                autoBattleButton.classList.remove('paused-mode');
            }
            // 隐藏循环模式提示
            if (modeHint) {
                modeHint.style.display = 'none';
            }
            console.log('🔄 按钮文字更新为：停止挂机');
        } else {
            // 非挂机模式：显示"开始挂机"
            autoBattleText.textContent = '开始挂机';
            if (autoBattleButton) {
                autoBattleButton.classList.remove('stop-mode');
                autoBattleButton.classList.remove('paused-mode');
            }
            console.log('🔄 按钮文字更新为：开始挂机');
        }
    }

    /**
     * 🆕 刷新失败标识状态（从localStorage重新读取）
     */
    refreshFailedFlagFromStorage() {
        const oldFlag = this.autoBattleFailedFlag;
        this.autoBattleFailedFlag = localStorage.getItem('autoBattleFailedFlag') === 'true';

        if (oldFlag !== this.autoBattleFailedFlag) {
            console.log('🔄 失败标识状态已更新:', oldFlag, '→', this.autoBattleFailedFlag);
            this.updateAutoBattleButtonText();
        }

        return this.autoBattleFailedFlag;
    }

    /**
     * 获取挂机相关的DOM元素
     * @returns {Object} 挂机元素对象
     */
    getAutoBattleElements() {
        const autoBattleButton = document.querySelector('[data-auto-battle-button]');
        return {
            button: autoBattleButton,
            countdownDiv: autoBattleButton?.querySelector('[data-auto-battle-countdown]'),
            text: document.querySelector('[data-auto-battle-text]'),
            span: autoBattleButton?.querySelector('span'),
        };
    }

    /**
     * 停止挂机模式
     */
    stopAutoBattle() {
        console.log('🛑 停止挂机模式');
        this.isAutoBattleMode = false;

        // 🎯 新增：清除localStorage中的挂机状态
        localStorage.removeItem('autoBattleMode');
        console.log('🗑️ 挂机状态已从localStorage清除');

        // 🔧 修复：停止挂机时清除失败标识和暂停状态
        this.isAutoBattlePaused = false;
        this.clearAutoBattleFailedFlag(); // 🆕 清除失败标识

        // 清除倒计时
        if (this.autoBattleCountdown) {
            clearInterval(this.autoBattleCountdown);
            this.autoBattleCountdown = null;
        }

        // 更新按钮状态
        const autoBattleButton = document.querySelector('[data-auto-battle-button]');
        const countdownDiv = document.querySelector('[data-auto-battle-countdown]');
        const autoBattleSpan = autoBattleButton ? autoBattleButton.querySelector('span') : null;

        if (autoBattleButton) {
            autoBattleButton.classList.remove('stop-mode');
            autoBattleButton.classList.remove('paused-mode');
        }

        // 🔧 修复：clearAutoBattleFailedFlag()已经调用了updateAutoBattleButtonText()
        // 不需要在这里重复设置按钮文字和循环模式提示

        if (countdownDiv) {
            countdownDiv.innerHTML = '';
        }

        // 恢复按钮图标显示
        if (autoBattleSpan) {
            autoBattleSpan.style.removeProperty('--hide-icon');
        }

        // 🔧 修复：清除所有可能的setTimeout
        const allTimeouts = window.setTimeout(() => {}, 0);
        for (let i = 0; i < allTimeouts; i++) {
            window.clearTimeout(i);
        }

        // 🔧 修复：阻止页面重载
        const overlayToRemove = document.querySelector('.game-overlay');
        if (overlayToRemove) {
            // 不要移除胜利面板，让用户可以继续操作
            console.log('🛑 保持胜利面板显示，停止自动战斗');
        }
    }

    // 🗑️ 已删除：不必要的代理方法，直接使用battleFlowManager.autoBattle()

    // 🗑️ 已删除：不必要的代理方法，直接使用victoryPanelManager的方法

    /**
     * 检查挂机状态并恢复
     * 在gameOver方法中调用，确保挂机状态正确
     */
    checkAndRestoreAutoBattleState() {
        // 🎯 修复：确保挂机状态正确恢复和处理
        // 重新从localStorage读取挂机状态，确保状态同步
        const savedAutoBattleMode = localStorage.getItem('autoBattleMode') === 'true';
        this.isAutoBattleMode = savedAutoBattleMode;

        // 🆕 同时恢复失败标识状态
        this.autoBattleFailedFlag = localStorage.getItem('autoBattleFailedFlag') === 'true';

        console.log('🤖 挂机状态检查:', {
            'this.isAutoBattleMode': this.isAutoBattleMode,
            localStorage值: localStorage.getItem('autoBattleMode'),
            最终判断: savedAutoBattleMode,
            失败标识: this.autoBattleFailedFlag,
        });

        // 🔧 修复：简化挂机处理逻辑
        if (this.isAutoBattleMode) {
            console.log('🤖 检测到挂机模式，胜利面板将处理后续挂机逻辑');
        } else {
            console.log('🤖 非挂机模式，等待用户操作');
        }

        return this.isAutoBattleMode;
    }

    /**
     * 获取当前挂机状态
     * @returns {boolean} 是否处于挂机模式
     */
    isInAutoBattleMode() {
        return this.isAutoBattleMode;
    }

    /**
     * 设置挂机状态
     * @param {boolean} enabled 是否启用挂机
     */
    setAutoBattleMode(enabled) {
        this.isAutoBattleMode = enabled;
        if (enabled) {
            localStorage.setItem('autoBattleMode', 'true');
            this.isAutoBattlePaused = false; // 启用挂机时清除暂停状态
        } else {
            localStorage.removeItem('autoBattleMode');
            this.isAutoBattlePaused = false; // 停止挂机时清除暂停状态
            // 🔧 修复：停止挂机时不自动清除失败标识，保留用户的选择
            // 失败标识应该只在用户明确操作时清除（如点击下一关、退出战斗）
        }
        console.log(`🤖 挂机状态设置为: ${enabled ? '开启' : '关闭'}`);

        // 🔧 修复：状态改变后更新按钮文字
        this.updateAutoBattleButtonText();
    }

    /**
     * 🆕 检查是否处于挂机暂停状态
     * @returns {boolean} 是否暂停
     */
    isAutoBattlePausedState() {
        return this.isAutoBattlePaused;
    }

    /**
     * 🆕 恢复挂机（从暂停状态）
     */
    resumeAutoBattle() {
        console.log('🔄 恢复挂机模式');
        this.isAutoBattlePaused = false;
        // 🔧 修复：清除失败标识，但不影响挂机状态
        const oldFlag = this.autoBattleFailedFlag;
        this.autoBattleFailedFlag = false;
        localStorage.removeItem('autoBattleFailedFlag');
        console.log('🗑️ 挂机失败标识已清除（恢复挂机）');

        // 🔧 修复：只有标识状态改变时才更新按钮文字
        if (oldFlag !== this.autoBattleFailedFlag) {
            this.updateAutoBattleButtonText();
        }
    }

    /**
     * 🆕 重置当前关卡并继续挂机
     */
    async resetCurrentStageAndContinue() {
        console.log('🔄 重置当前关卡并继续挂机');
        this.isAutoBattlePaused = false;
        // 保持失败标识，继续循环当前关
        // 不改变进度，直接重新开始当前关卡的战斗

        // 🚀 使用无刷新方式重新开始战斗
        if (
            this.battleSystem.victoryPanelManager &&
            this.battleSystem.victoryPanelManager.restartBattleWithoutRefresh
        ) {
            await this.battleSystem.victoryPanelManager.restartBattleWithoutRefresh();
        } else {
            // 备用方案：传统页面刷新
            console.log('🔄 备用方案：使用页面刷新');
            window.location.reload();
        }
    }
}

// 导出管理器类
window.BattleAutoBattleManager = BattleAutoBattleManager;
