/**
 * 横斩技能模块 - 独立文件
 * 剑类/火法混合技能：巨剑旋转蓄力 + 直接击中 + 火焰爆炸
 */

/**
 * 横斩技能类 - 剑类/火法混合技能
 * 巨剑术的旋转蓄力 + 直接击中 + 火球术的爆炸效果
 */
class HengZhanSkill extends BaseSkill {
    constructor(battleSystem) {
        super(battleSystem);
        this.skillName = '横斩';
        this.elementType = 'sword-fire';
        
        // v2.0新增：技能实例管理
        this.activeTimers = new Set();
        this.activeElements = new Set();
        
        console.log(`🔥⚔️ 横斩技能实例创建完成`);
    }

    async execute(skillData, weaponImage) {
        try {
            console.log(`🔥⚔️ 横斩技能开始执行`);
            
            // 必须调用技能喊话
            // 🔧 修复：使用真实技能名称而不是动画名称
        const skillName = skillData?.skillName || skillData?.displayName || '横斩';
        await this.showSkillShout(skillName);
            
            // 调用具体的技能动画方法
            await this.createHengZhan(weaponImage);
            
        } catch (error) {
            console.error(`❌ 横斩技能执行失败:`, error);
            this.cleanup();
        }
    }

    async createHengZhan(weaponImage) {
        // 🔧 动态判断位置映射
        let casterPos, targetPos;
        
        if (this.isEnemySkill) {
            // 敌方技能：敌人是施法者，玩家是目标
            casterPos = this.getCharacterPosition(false); // 敌人位置
            targetPos = this.getCharacterPosition(true);  // 玩家位置
        } else {
            // 我方技能：玩家是施法者，敌人是目标
            casterPos = this.getCharacterPosition(true);  // 玩家位置
            targetPos = this.getCharacterPosition(false); // 敌人位置
        }
        
        console.log(`🔥⚔️ 横斩位置计算:`, {
            起点: {x: casterPos.x, y: casterPos.y},
            终点: {x: targetPos.x, y: targetPos.y}
        });

        // 创建技能动画容器
        const container = this.createElement('hengzhan-container', {
            style: {
                position: 'absolute',
                top: '0',
                left: '0',
                width: '100%',
                height: '100%',
                pointerEvents: 'none',
                zIndex: 1000
            }
        });

        this.effectsContainer.appendChild(container);
        this.activeElements.add(container);

        try {
            // 第一阶段：巨剑旋转蓄力，逐渐放大到能砍中敌人（2.0秒）
            await this.createSwordSpinCharge(container, casterPos, targetPos, weaponImage);
            
            // 第二阶段：直接击中敌人，无飞行过程（0.5秒）
            await this.createDirectStrike(container, casterPos, targetPos, weaponImage);
            
            // 第三阶段：火球术爆炸效果（1.5秒）
            await this.createFireExplosion(container, targetPos);
            
        } finally {
            // 清理动画容器（必须执行）
            setTimeout(() => {
                this.safeRemoveElement(container);
            }, 100);
        }
    }
    
    // 第一阶段：巨剑旋转蓄力，逐渐放大到能砍中敌人
    async createSwordSpinCharge(container, casterPos, targetPos, weaponImage) {
        console.log(`⚔️ 横斩第一阶段：巨剑旋转蓄力`);
        
        // 计算玩家到敌人的距离，确定最终剑的尺寸
        const deltaX = targetPos.x - casterPos.x;
        const deltaY = targetPos.y - casterPos.y;
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
        const finalSwordLength = Math.max(distance * 0.6, 80); // 减少一半大小，确保能够覆盖到敌人
        
        console.log(`⚔️ 横斩尺寸计算:`, {
            玩家到敌人距离: distance,
            最终剑长: finalSwordLength
        });
        
        // === 创建主旋转剑 ===
        const mainSword = this.createElement('hengzhan-main-sword', {
            style: {
                position: 'absolute',
                left: casterPos.x + 'px',
                top: casterPos.y + 'px',
                transform: 'translate(-50%, -50%)',
                zIndex: '1001'
            }
        });
        
        // 添加武器图片
        if (weaponImage) {
            this.addWeaponImage(mainSword, weaponImage);
            // 🗡️ 动态调整武器图片角度
            const weaponImg = mainSword.querySelector('.weapon-image');
            if (weaponImg) {
                weaponImg.style.transform = `rotate(${this.calculateInitialSwordRotation()}deg)`;
            }
            console.log(`⚔️ 为主剑添加武器图片: ${weaponImage}`);
        }
        
        // 设置CSS变量控制最终尺寸
        mainSword.style.setProperty('--finalLength', `${finalSwordLength}px`);
        
        container.appendChild(mainSword);
        
        // === 创建虚影跟随效果 ===
        const shadowCount = 6; // 6个虚影
        for (let i = 0; i < shadowCount; i++) {
            const shadow = this.createElement('hengzhan-sword-shadow', {
                style: {
                    position: 'absolute',
                    left: casterPos.x + 'px',
                    top: casterPos.y + 'px',
                    transform: 'translate(-50%, -50%)',
                    animationDelay: `${i * 0.1}s`,
                    opacity: `${0.6 - i * 0.08}`, // 逐渐透明
                    zIndex: '1000'
                }
            });
            
            // 为虚影也添加武器图片
            if (weaponImage) {
                this.addWeaponImage(shadow, weaponImage);
                // 🗡️ 动态调整虚影武器图片角度
                const weaponImg = shadow.querySelector('.weapon-image');
                if (weaponImg) {
                    weaponImg.style.transform = `rotate(${this.calculateInitialSwordRotation()}deg)`;
                }
            }
            
            shadow.style.setProperty('--finalLength', `${finalSwordLength}px`);
            shadow.style.setProperty('--shadowDelay', `${i * 50}ms`);
            
            container.appendChild(shadow);
        }
        
        // === 创建旋转能量场 ===
        const energyField = this.createElement('hengzhan-energy-field', {
            style: {
                position: 'absolute',
                left: casterPos.x + 'px',
                top: casterPos.y + 'px',
                transform: 'translate(-50%, -50%)'
            }
        });
        container.appendChild(energyField);
        
        // === 创建火焰粒子环绕 ===
        for (let i = 0; i < 15; i++) {
            const particle = this.createElement('hengzhan-charge-particle', {
                style: {
                    position: 'absolute',
                    left: casterPos.x + 'px',
                    top: casterPos.y + 'px',
                    animationDelay: `${Math.random() * 1.5}s`
                }
            });
            
            const angle = (i / 15) * Math.PI * 2;
            const radius = 40 + Math.random() * 20;
            particle.style.setProperty('--orbitAngle', `${angle * 180 / Math.PI}deg`);
            particle.style.setProperty('--orbitRadius', `${radius}px`);
            
            container.appendChild(particle);
        }
        
        // === 创建蓄力冲击波 ===
        for (let i = 0; i < 4; i++) {
            const shockwave = this.createElement('hengzhan-charge-shockwave', {
                style: {
                    position: 'absolute',
                    left: casterPos.x + 'px',
                    top: casterPos.y + 'px',
                    transform: 'translate(-50%, -50%)',
                    animationDelay: `${i * 0.4}s`
                }
            });
            container.appendChild(shockwave);
        }
        
        // 等待蓄力完成（2.0秒）
        console.log(`⚔️ 等待1000毫秒蓄力完成...`);
        await this.wait(1000);
        
        console.log(`✅ 横斩第一阶段完成`);
    }
    
    // 第二阶段：巨剑保持原位，播放击中效果
    async createDirectStrike(container, casterPos, targetPos, weaponImage) {
        console.log(`💥 横斩第二阶段：巨剑保持原位，播放击中效果`);
        
        // === 清理蓄力阶段的元素，保留主剑 ===
        container.querySelectorAll('.hengzhan-sword-shadow, .hengzhan-energy-field, .hengzhan-charge-particle, .hengzhan-charge-shockwave').forEach(el => {
            this.safeRemoveElement(el);
        });
        
        // === 创建火焰斩切路线 ===
        await this.createFlameSlashTrail(container, casterPos, targetPos);
        
        // === 主剑保持在原位不移动 ===
        const mainSword = container.querySelector('.hengzhan-main-sword');
        console.log(`⚔️ 巨剑保持在玩家位置，剑尖朝上`);
        
        // === 创建击中冲击效果（在敌人位置） ===
        const strikeImpact = this.createElement('hengzhan-strike-impact', {
            style: {
                position: 'absolute',
                left: targetPos.x + 'px',
                top: targetPos.y + 'px',
                transform: 'translate(-50%, -50%)'
            }
        });
        container.appendChild(strikeImpact);
        
        // === 创建击中火花（在敌人位置） ===
        for (let i = 0; i < 12; i++) {
            const spark = this.createElement('hengzhan-strike-spark', {
                style: {
                    position: 'absolute',
                    left: targetPos.x + 'px',
                    top: targetPos.y + 'px',
                    transform: 'translate(-50%, -50%)',
                    animationDelay: `${i * 0.02}s`
                }
            });
            
            const angle = (i / 12) * Math.PI * 2;
            const distance = 25 + Math.random() * 40;
            spark.style.setProperty('--sparkAngle', `${angle * 180 / Math.PI}deg`);
            spark.style.setProperty('--sparkDistance', `${distance}px`);
            
            container.appendChild(spark);
        }
        
        // 立即触发击中效果
        this.createHitEffect(targetPos.x, targetPos.y, true);
        console.log(`💥 在敌人位置播放击中效果`);
        
        // 等待击中动画完成
        console.log(`💥 等待500毫秒击中完成...`);
        await this.wait(500);
        
        // 清理击中特效
        this.safeRemoveElement(strikeImpact);
        container.querySelectorAll('.hengzhan-strike-spark').forEach(spark => {
            this.safeRemoveElement(spark);
        });
        
        console.log(`✅ 横斩第二阶段完成`);
    }
    
    // 创建火焰斩切路线
    async createFlameSlashTrail(container, startPos, endPos) {
        console.log(`🔥⚔️ 创建横向火焰斩切路线`);
        
        // 计算屏幕宽度和敌人Y位置
        const screenWidth = window.innerWidth || 800;
        const enemyY = endPos.y; // 敌人的Y位置
        const slashStartX = 0; // 从屏幕左边开始
        const slashEndX = screenWidth; // 到屏幕右边结束
        const slashLength = screenWidth; // 横切线长度等于屏幕宽度
        
        console.log(`🔥 横向斩切路线计算:`, {
            敌人位置: {x: endPos.x, y: endPos.y},
            斩切Y位置: enemyY,
            屏幕宽度: screenWidth,
            斩切长度: slashLength
        });
        
        // === 主火焰横切线 ===
        const flameSlash = this.createElement('hengzhan-flame-slash', {
            style: {
                position: 'absolute',
                left: '0px', // 从屏幕左边开始
                top: enemyY + 'px', // 在敌人的Y位置
                transform: 'translateY(-50%)', // 只垂直居中
                zIndex: '1002'
            }
        });
        
        // 设置横切线参数
        flameSlash.style.setProperty('--slashLength', `${slashLength}px`);
        flameSlash.style.setProperty('--slashAngle', '0deg'); // 水平切线
        
        container.appendChild(flameSlash);
        
        // === 火焰轨迹粒子（沿横线分布） ===
        const particleCount = Math.floor(slashLength / 20); // 每20px一个粒子
        for (let i = 0; i < particleCount; i++) {
            const progress = i / particleCount;
            const particleX = slashStartX + slashLength * progress;
            const particleY = enemyY;
            
            const flameParticle = this.createElement('hengzhan-flame-particle', {
                style: {
                    position: 'absolute',
                    left: particleX + 'px',
                    top: particleY + 'px',
                    transform: 'translate(-50%, -50%)',
                    animationDelay: `${i * 0.005}s`, // 更快的连续出现
                    zIndex: '1001'
                }
            });
            
            container.appendChild(flameParticle);
        }
        
        // === 火焰斩切波（横向扩散） ===
        for (let i = 0; i < 3; i++) {
            const slashWave = this.createElement('hengzhan-slash-wave', {
                style: {
                    position: 'absolute',
                    left: '0px',
                    top: (enemyY + (i - 1) * 15) + 'px', // 上下错开分布
                    transform: 'translateY(-50%)',
                    animationDelay: `${i * 0.03}s`,
                    zIndex: '1000'
                }
            });
            
            slashWave.style.setProperty('--waveLength', `${slashLength}px`);
            slashWave.style.setProperty('--waveAngle', '0deg'); // 水平波纹
            slashWave.style.setProperty('--waveWidth', `${6 + i * 3}px`);
            
            container.appendChild(slashWave);
        }
        
        // === 额外的斩切冲击效果（在敌人位置） ===
        const slashImpact = this.createElement('hengzhan-slash-impact', {
            style: {
                position: 'absolute',
                left: endPos.x + 'px',
                top: endPos.y + 'px',
                transform: 'translate(-50%, -50%)',
                zIndex: '1003'
            }
        });
        
        container.appendChild(slashImpact);
        
        // 等待斩切路线动画完成
        await this.wait(100); // 斩切路线动画时长
        
        console.log(`✅ 横向火焰斩切路线创建完成`);
    }
    
    // 第三阶段：火球术爆炸效果
    async createFireExplosion(container, targetPos) {
        console.log(`🔥 横斩第三阶段：火焰爆炸`);
        
        // 清理剑的图片，爆炸阶段不需要显示剑
        const mainSword = container.querySelector('.hengzhan-main-sword');
        if (mainSword) {
            this.safeRemoveElement(mainSword);
        }
        
        // 调用击中特效（必须调用）
        this.createHitEffect(targetPos.x, targetPos.y, true);
        
        // 创建火焰爆炸效果（复用火球术的爆炸特效）
        await this.createRealisticExplosion(targetPos.x, targetPos.y, container);
        
        // 创建敌人受击动画
        this.createFireEnemyHit();
        
        console.log(`🔥 等待500毫秒爆炸完成...`);
        await this.wait(500);
        
        console.log(`✅ 横斩第三阶段完成`);
    }
    
    // 创建真实的火焰爆炸效果（复用火球术）
    async createRealisticExplosion(centerX, centerY, container) {
        console.log(`🔥 创建火焰爆炸特效于位置: (${centerX}, ${centerY})`);
        
        // 第一阶段：爆炸闪光
        const explosionFlash = this.createElement('explosion-flash', {
            style: {
                position: 'absolute',
                left: centerX + 'px',
                top: centerY + 'px',
                transform: 'translate(-50%, -50%)'
            }
        });
        container.appendChild(explosionFlash);
        
        // 第二阶段：爆炸核心
        setTimeout(() => {
            const explosionCore = this.createElement('explosion-core', {
                style: {
                    position: 'absolute',
                    left: centerX + 'px',
                    top: centerY + 'px',
                    transform: 'translate(-50%, -50%)'
                }
            });
            container.appendChild(explosionCore);
        }, 50);
        
        // 第三阶段：冲击波
        setTimeout(() => {
            for (let i = 0; i < 3; i++) {
                const shockwave = this.createElement('explosion-shockwave', {
                    style: {
                        position: 'absolute',
                        left: centerX + 'px',
                        top: centerY + 'px',
                        transform: 'translate(-50%, -50%)',
                        animationDelay: `${i * 0.1}s`
                    }
                });
                container.appendChild(shockwave);
            }
        }, 100);
        
        // 第四阶段：火焰环
        setTimeout(() => {
            const fireRing = this.createElement('explosion-fire-ring', {
                style: {
                    position: 'absolute',
                    left: centerX + 'px',
                    top: centerY + 'px',
                    transform: 'translate(-50%, -50%)'
                }
            });
            container.appendChild(fireRing);
        }, 150);
        
        // 第五阶段：爆炸烟雾
        setTimeout(() => {
            for (let i = 0; i < 6; i++) {
                const smoke = this.createElement('explosion-smoke', {
                    style: {
                        position: 'absolute',
                        left: (centerX + (Math.random() - 0.5) * 60) + 'px',
                        top: (centerY + (Math.random() - 0.5) * 60) + 'px',
                        transform: 'translate(-50%, -50%)',
                        animationDelay: `${i * 0.1}s`
                    }
                });
                container.appendChild(smoke);
            }
        }, 200);
        
        // 第六阶段：火花粒子
        setTimeout(() => {
            for (let i = 0; i < 30; i++) {
                const particle = this.createElement('fire-particle', {
                    style: {
                        position: 'absolute',
                        left: centerX + 'px',
                        top: centerY + 'px',
                        transform: 'translate(-50%, -50%)',
                        animationDelay: `${i * 0.02}s`
                    }
                });
                
                const angle = Math.random() * Math.PI * 2;
                const distance = 30 + Math.random() * 80;
                const moveX = Math.cos(angle) * distance;
                const moveY = Math.sin(angle) * distance;
                
                particle.style.setProperty('--moveX', `${moveX}px`);
                particle.style.setProperty('--moveY', `${moveY}px`);
                particle.style.animation = 'fire-particle 1s ease-out forwards';
                
                container.appendChild(particle);
            }
        }, 250);
        
        // 第七阶段：热浪扭曲效果
        setTimeout(() => {
            const heatDistortion = this.createElement('explosion-heat-distortion', {
                style: {
                    position: 'absolute',
                    left: centerX + 'px',
                    top: centerY + 'px',
                    transform: 'translate(-50%, -50%)'
                }
            });
            container.appendChild(heatDistortion);
        }, 300);
        
        console.log(`🔥 火焰爆炸特效创建完成`);
    }
    
    // 创建火焰敌人受击动画
    createFireEnemyHit() {
        // 🔧 修复：为被攻击者添加受击动画
        const targetSelector = !this.isEnemySkill ? '.enemy .character-sprite' : '.player .character-sprite';
        const targetSprite = document.querySelector(targetSelector);
        if (targetSprite) {
            targetSprite.style.animation = 'fire-hit 2s ease-out, fire-shake 0.5s ease-in-out 4';
            
            // v2.0新增：添加定时器管理
            const timer = setTimeout(() => {
                if (targetSprite) {
                    targetSprite.style.animation = '';
                }
            }, 2000);
            this.activeTimers.add(timer);
        }
    }

    // v2.0新增：安全移除元素方法
    safeRemoveElement(element) {
        if (element && element.parentNode) {
            try {
                element.parentNode.removeChild(element);
                this.activeElements.delete(element);
            } catch (error) {
                console.warn(`⚠️ 移除元素失败:`, error);
            }
        }
    }

    // v2.0新增：清理方法
    cleanup() {
        console.log(`🧹 横斩技能清理中...`);
        
        // 清理所有活跃的定时器
        this.activeTimers.forEach(timer => {
            clearTimeout(timer);
        });
        this.activeTimers.clear();
        
        // 清理所有活跃的元素
        this.activeElements.forEach(element => {
            this.safeRemoveElement(element);
        });
        this.activeElements.clear();
        
        console.log(`✅ 横斩技能清理完成`);
    }

    // 🗡️ 动态计算剑的初始旋转角度
    calculateInitialSwordRotation() {
        // 敌方技能：剑尖向下（指向玩家），我方技能：剑尖向上（指向敌人）
        return this.isEnemySkill ? 0 : 180;
    }
}

// 导出技能类（必须按此格式）
window.SwordSkills = window.SwordSkills || {};
window.SwordSkills.HengZhanSkill = HengZhanSkill;