# 🎯 技能开发清单 (v3.0更新)

## 📋 技能开发准备阶段

### 🎨 设计阶段
- [ ] 技能名称确定（数据库中的skill_name）
- [ ] 技能类型分类（剑类/火法/雷法等）
- [ ] 动画效果设计（蓄力/发射/击中/清理）
- [ ] 时长控制规划（总时长2-5秒）

### 🔧 技术准备
- [ ] **英文类名设计**（如：HuoQiuShuSkill）❌不能用中文
- [ ] animation_model值确定（如：huoqiushu）
- [ ] 文件命名规划（如：huoqiu-skill.js）
- [ ] CSS样式命名（如：huoqiu-animations.css）

## 🛠️ 开发实施阶段

### ✅ JavaScript文件创建
- [ ] 创建独立技能文件（推荐）或添加到模块文件
- [ ] **使用英文类名**（继承BaseSkill）
- [ ] 实现execute方法（调用showSkillShout）
- [ ] 实现技能动画方法
- [ ] 添加try-finally清理机制
- [ ] **正确导出类**（window.{ModuleName}Skills = { SkillClass }）

### ✅ CSS动画样式
- [ ] 创建对应的CSS文件
- [ ] 定义动画容器样式
- [ ] 实现各阶段动画关键帧
- [ ] 添加移动端响应式支持
- [ ] 使用CSS变量控制动态参数

### ✅ skill-config.js 统一配置 (🌟 v3.0核心)
- [ ] 在animationModelMapping中添加映射
- [ ] 在skillImplementationMapping中添加配置
- [ ] 确保配置信息完整准确
- [ ] 验证映射关系正确

### ✅ 数据库配置
- [ ] 确保item_skills表中有对应记录
- [ ] 设置正确的animation_model值
- [ ] 技能名称与代码中一致

## 🔍 测试验证阶段

### ✅ 功能测试
- [ ] 控制台无错误信息
- [ ] 技能名称喊话正常显示
- [ ] 动画时序控制准确
- [ ] 击中特效正确触发
- [ ] 动画容器正确清理

### ✅ 兼容性测试
- [ ] 玩家使用技能正常
- [ ] 怪物使用技能正常（isEnemySkill支持）
- [ ] 不同武器装备兼容
- [ ] 移动端显示正常

### ✅ 性能测试
- [ ] 内存没有泄漏
- [ ] 动画帧率流畅
- [ ] 多次使用无累积问题
- [ ] 与其他技能无冲突

## 🚫 v3.0已废弃的步骤

- ❌ ~~别名映射配置~~
- ❌ ~~skill-loader.js本地映射~~
- ❌ ~~复杂的技能名称判断~~
- ❌ ~~中文类名~~

## 📊 v3.0核心检查点

### 🔑 核心要求
1. **类名英文化**：SkillClass（不能用中文）
2. **统一配置**：skill-config.js中完整配置
3. **数据库驱动**：animation_model控制动画选择
4. **降级保护**：配置失败时有本地备用方案

### 🎯 质量标准
- 代码注释用中文
- 动画时长2-5秒
- 内存安全清理
- 错误处理完善
- 移动端兼容

### 🔧 调试验证
```javascript
// 检查配置是否正确
console.log(window.SkillConfig.getSkillConfig('火球术'));
console.log(window.SkillConfig.getSkillNameByAnimationModel('huoqiushu'));
```

## 🎮 最终确认清单

- [ ] 类名使用英文
- [ ] 在skill-config.js中正确配置
- [ ] 创建独立的CSS文件
- [ ] 正确导出技能类
- [ ] 数据库animation_model配置正确
- [ ] 通过实际战斗测试
- [ ] 性能和内存检查通过
- [ ] 移动端兼容性验证

---
*清单更新日期: 2024年12月*  
*基于: v3.0统一配置系统* 