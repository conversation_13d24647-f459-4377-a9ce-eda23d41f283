<?php
/**
 * 装备属性统一管理器
 * 统一处理所有装备属性计算逻辑，避免代码重复
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2024-12-19
 */

class EquipmentStatsManager {
    
    /**
     * 获取角色的装备属性加成（不包含武器）
     * 
     * @param PDO $pdo 数据库连接
     * @param int $characterId 角色ID
     * @return array 装备属性加成数组
     */
    public static function getEquipmentStats($pdo, $characterId) {
        $stats = [
            'physical_attack' => 0,
            'immortal_attack' => 0,
            'physical_defense' => 0,
            'immortal_defense' => 0,
            'hp_bonus' => 0,
            'mp_bonus' => 0,
            'speed_bonus' => 0,
            'critical_bonus' => 0,
            'critical_damage' => 0,
            'accuracy_bonus' => 0,  // 命中加成
            'dodge_bonus' => 0,     // 闪避加成
            'critical_resistance' => 0  // 免暴加成（新增）
        ];
        
        try {
            // 🔧 统一查询：从character_equipment表关联user_inventories表获取真实装备属性
            $stmt = $pdo->prepare("
                SELECT
                    gi.item_name,
                    gi.hp_bonus, gi.mp_bonus, gi.speed_bonus,
                    gi.physical_attack, gi.immortal_attack, gi.physical_defense, gi.immortal_defense,
                    gi.critical_bonus, gi.critical_damage, gi.accuracy_bonus, gi.dodge_bonus, 
                    ui.custom_attributes as inventory_custom_attributes
                FROM character_equipment ce
                JOIN game_items gi ON ce.item_id = gi.id
                LEFT JOIN user_inventories ui ON ce.inventory_item_id = ui.id
                WHERE ce.character_id = ? AND gi.item_type != 'weapon' AND ce.item_id > 0
            ");
            $stmt->execute([$characterId]);
            $equipment = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            error_log("🔧 [装备管理器] 找到装备数量: " . count($equipment));
            
            foreach ($equipment as $item) {
                $actualAttributes = self::getActualItemAttributes($item);
                self::addAttributesToStats($stats, $actualAttributes);
            }
            
            error_log("🔧 [装备管理器] 装备属性统计: " . json_encode($stats));
            
        } catch (Exception $e) {
            error_log("获取装备属性失败: " . $e->getMessage());
        }
        
        return $stats;
    }
    
    /**
     * 获取角色的武器属性加成
     * 
     * @param PDO $pdo 数据库连接
     * @param int $characterId 角色ID
     * @return array 武器属性加成数组
     */
    public static function getWeaponStats($pdo, $characterId) {
        $stats = [
            'physical_attack' => 0,
            'immortal_attack' => 0,
            'physical_defense' => 0,
            'immortal_defense' => 0,
            'hp_bonus' => 0,
            'mp_bonus' => 0,
            'speed_bonus' => 0,
            'critical_bonus' => 0,
            'critical_damage' => 0,
            'accuracy_bonus' => 0,  // 命中加成
            'dodge_bonus' => 0,     // 闪避加成
            'critical_resistance' => 0  // 免暴加成（新增）
        ];
        
        try {
            // 🔧 统一查询：从character_equipment表关联user_inventories表获取真实武器属性
            $stmt = $pdo->prepare("
                SELECT
                    gi.item_name,
                    gi.hp_bonus, gi.mp_bonus, gi.speed_bonus,
                    gi.physical_attack, gi.immortal_attack, gi.physical_defense, gi.immortal_defense,
                    gi.critical_bonus, gi.critical_damage, gi.accuracy_bonus, gi.dodge_bonus,
                    ui.custom_attributes as inventory_custom_attributes
                FROM character_equipment ce
                JOIN game_items gi ON ce.item_id = gi.id
                LEFT JOIN user_inventories ui ON ce.inventory_item_id = ui.id
                WHERE ce.character_id = ? AND gi.item_type = 'weapon' AND ce.item_id > 0
            ");
            $stmt->execute([$characterId]);
            $weapons = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            error_log("🔧 [装备管理器] 找到武器数量: " . count($weapons));
            
            foreach ($weapons as $weapon) {
                $actualAttributes = self::getActualItemAttributes($weapon);
                self::addAttributesToStats($stats, $actualAttributes);
            }
            
            error_log("🔧 [装备管理器] 武器属性统计: " . json_encode($stats));
            
        } catch (Exception $e) {
            error_log("获取武器属性失败: " . $e->getMessage());
        }
        
        return $stats;
    }
    
    /**
     * 获取角色的所有装备属性加成（装备+武器）
     * 
     * @param PDO $pdo 数据库连接
     * @param int $characterId 角色ID
     * @return array 所有装备属性加成数组
     */
    public static function getAllEquipmentStats($pdo, $characterId) {
        $equipmentStats = self::getEquipmentStats($pdo, $characterId);
        $weaponStats = self::getWeaponStats($pdo, $characterId);
        
        // 合并装备和武器属性
        $allStats = [];
        foreach ($equipmentStats as $key => $value) {
            $allStats[$key] = $value + (isset($weaponStats[$key]) ? $weaponStats[$key] : 0);
        }
        
        error_log("🔧 [装备管理器] 总装备属性: " . json_encode($allStats));
        
        return $allStats;
    }
    
    /**
     * 获取物品的真实属性（优先使用calculated_attributes，向后兼容基础属性）
     * 
     * @param array $item 物品数据（包含game_items和user_inventories信息）
     * @return array 物品的真实属性数组
     */
    private static function getActualItemAttributes($item) {
        $actualAttributes = [];
        
        // 🔧 优先使用user_inventories表中的calculated_attributes
        if (!empty($item['inventory_custom_attributes'])) {
            $customAttributes = json_decode($item['inventory_custom_attributes'], true);
            if (isset($customAttributes['calculated_attributes'])) {
                $actualAttributes = $customAttributes['calculated_attributes'];
                error_log("物品 {$item['item_name']} 使用calculated_attributes: " . json_encode($actualAttributes));
                return $actualAttributes;
            }
        }
        
        // 如果没有calculated_attributes，才使用game_items表的基础属性（向后兼容）
        $actualAttributes = [
            'physical_attack' => intval($item['physical_attack']),
            'immortal_attack' => intval($item['immortal_attack']),
            'physical_defense' => intval($item['physical_defense']),
            'immortal_defense' => intval($item['immortal_defense']),
            'hp_bonus' => intval($item['hp_bonus']),
            'mp_bonus' => intval($item['mp_bonus']),
            'speed_bonus' => intval($item['speed_bonus']),
            'critical_bonus' => floatval($item['critical_bonus']),
            'critical_damage' => floatval($item['critical_damage']),
            'accuracy_bonus' => floatval($item['accuracy_bonus']),
            'dodge_bonus' => floatval($item['dodge_bonus']),
            'critical_resistance' => 0  // 基础属性中没有免暴，默认为0
        ];
        
        error_log("物品 {$item['item_name']} 使用基础属性: " . json_encode($actualAttributes));
        
        return $actualAttributes;
    }
    
    /**
     * 将物品属性累加到统计数组中
     * 
     * @param array &$stats 属性统计数组（引用传递）
     * @param array $attributes 要累加的属性数组
     */
    private static function addAttributesToStats(&$stats, $attributes) {
        // 🔧 修复：统一使用标准字段名
        $attributeMapping = [
            // 标准字段名（直接映射）
            'physical_attack' => 'physical_attack',
            'immortal_attack' => 'immortal_attack', 
            'physical_defense' => 'physical_defense',
            'immortal_defense' => 'immortal_defense',
            'hp_bonus' => 'hp_bonus',
            'mp_bonus' => 'mp_bonus',
            'speed_bonus' => 'speed_bonus',
            'critical_bonus' => 'critical_bonus',
            'critical_damage' => 'critical_damage',
            'accuracy_bonus' => 'accuracy_bonus',
            'dodge_bonus' => 'dodge_bonus',
            'critical_resistance' => 'critical_resistance',
            
            // 🔧 删除：废弃字段映射已清理，仅保留核心字段映射
        ];
        
        foreach ($attributes as $key => $value) {
            if (!is_numeric($value) || $value == 0) {
                continue;
            }
            
            // 🔧 使用映射表确定目标字段名
            $targetField = isset($attributeMapping[$key]) ? $attributeMapping[$key] : $key;
        
            // 🔧 删除：废弃字段兼容逻辑已清理
            
            // 累加属性值
            if (!isset($stats[$targetField])) {
                $stats[$targetField] = 0;
            }
            $stats[$targetField] += floatval($value);
            
            error_log("累加属性: {$targetField} += {$value} (来源字段: {$key})");
        }
    }
    
    /**
     * 获取兼容格式的装备加成（用于旧版API兼容）
     * 统一处理所有装备（包括武器），不再区分装备和武器
     * 
     * @param PDO $pdo 数据库连接
     * @param int $characterId 角色ID
     * @return array 兼容格式的装备加成数组
     */
    public static function getEquipmentBonusCompatible($pdo, $characterId) {
        // 🔧 简化：直接获取所有装备的统计，不区分装备和武器
        $allStats = self::getAllEquipmentStats($pdo, $characterId);
        
        // 转换为旧版API兼容格式
        return [
            'physical_attack' => $allStats['physical_attack'],
            'immortal_attack' => $allStats['immortal_attack'],
            'physical_defense' => $allStats['physical_defense'],
            'immortal_defense' => $allStats['immortal_defense'],
            'hp_bonus' => $allStats['hp_bonus'],
            'mp_bonus' => $allStats['mp_bonus'],
            'speed_bonus' => $allStats['speed_bonus'],
            'critical_bonus' => $allStats['critical_bonus'],
            'critical_damage' => $allStats['critical_damage'],
            'critical_resistance' => $allStats['critical_resistance']  // 免暴率
        ];
    }
    
    /**
     * 获取用户统计格式的装备属性（用于前端显示）
     * 
     * @param PDO $pdo 数据库连接
     * @param int $characterId 角色ID
     * @return array 用户统计格式的装备属性数组
     */
    public static function getUserStatsFormat($pdo, $characterId) {
        $equipmentStats = self::getEquipmentStats($pdo, $characterId);
        $weaponStats = self::getWeaponStats($pdo, $characterId);
        
        return [
            'equipment_stats' => $equipmentStats,
            'weapon_stats' => $weaponStats
        ];
    }
}

/**
 * 全局函数：获取装备加成（向后兼容）
 * 
 * @param PDO $pdo 数据库连接
 * @param int $characterId 角色ID
 * @return array 装备加成数组
 */
function getEquipmentBonusUnified($pdo, $characterId) {
    return EquipmentStatsManager::getEquipmentBonusCompatible($pdo, $characterId);
}

/**
 * 全局函数：计算装备属性统计（向后兼容）
 * 
 * @param int $characterId 角色ID
 * @param PDO $pdo 数据库连接
 * @return array 装备属性统计数组
 */
function calculateEquipmentStatsUnified($characterId, $pdo) {
    return EquipmentStatsManager::getEquipmentStats($pdo, $characterId);
}

/**
 * 全局函数：计算武器属性统计（向后兼容）
 * 
 * @param int $characterId 角色ID
 * @param PDO $pdo 数据库连接
 * @return array 武器属性统计数组
 */
function calculateWeaponStatsUnified($characterId, $pdo) {
    return EquipmentStatsManager::getWeaponStats($pdo, $characterId);
}

?> 