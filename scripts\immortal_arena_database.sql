-- ==========================================
-- 一念修仙 - 升仙大会竞技系统数据库脚本
-- 创建时间：2025年6月17日
-- 版本：v1.0
-- ==========================================

USE `yn_game`;

-- ==========================================
-- 1. 创建竞技场记录表
-- ==========================================

CREATE TABLE IF NOT EXISTS `immortal_arena_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `character_id` int(11) NOT NULL COMMENT '角色ID',
  `opponent_character_id` int(11) DEFAULT NULL COMMENT '对手角色ID（真实玩家）',
  `opponent_name` varchar(50) NOT NULL COMMENT '对手名称',
  `opponent_dao_power` int(11) NOT NULL COMMENT '对手道行值',
  `is_ai_puppet` tinyint(1) DEFAULT 0 COMMENT '是否为灵智傀儡',
  `ai_template_id` int(11) DEFAULT NULL COMMENT 'AI傀儡模板角色ID',
  `battle_result` enum('win','lose','draw') NOT NULL COMMENT '战斗结果',
  `dao_power_before` int(11) NOT NULL COMMENT '战斗前道行值',
  `dao_power_after` int(11) NOT NULL COMMENT '战斗后道行值',
  `dao_power_change` int(11) NOT NULL DEFAULT 0 COMMENT '道行值变化',
  `spirit_stone_reward` int(11) NOT NULL DEFAULT 0 COMMENT '灵石奖励',
  `battle_duration` int(11) DEFAULT NULL COMMENT '战斗时长(秒)',
  `battle_rounds` int(11) DEFAULT NULL COMMENT '战斗回合数',
  `battle_snapshot` text DEFAULT NULL COMMENT '战斗快照数据(JSON)',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_character_id` (`character_id`),
  KEY `idx_battle_result` (`battle_result`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_opponent` (`opponent_character_id`),
  KEY `idx_dao_power` (`dao_power_before`),
  FOREIGN KEY (`character_id`) REFERENCES `characters` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`opponent_character_id`) REFERENCES `characters` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='升仙大会论道竞技记录';

-- ==========================================
-- 2. 扩展characters表 - 添加竞技相关字段
-- ==========================================

-- 检查并添加竞技场道行值字段
ALTER TABLE `characters` 
ADD COLUMN IF NOT EXISTS `arena_dao_power` int(11) DEFAULT 0 COMMENT '竞技场道行值' AFTER `spiritual_root_usage`;

-- 检查并添加每日论道次数字段
ALTER TABLE `characters` 
ADD COLUMN IF NOT EXISTS `arena_daily_attempts` int(11) DEFAULT 0 COMMENT '今日论道次数' AFTER `arena_dao_power`;

-- 检查并添加购买论道次数字段
ALTER TABLE `characters` 
ADD COLUMN IF NOT EXISTS `arena_purchased_attempts` int(11) DEFAULT 0 COMMENT '今日购买的论道次数' AFTER `arena_daily_attempts`;

-- 检查并添加最后重置日期字段
ALTER TABLE `characters` 
ADD COLUMN IF NOT EXISTS `arena_last_reset` date DEFAULT NULL COMMENT '最后重置日期' AFTER `arena_purchased_attempts`;

-- 检查并添加竞技等级字段
ALTER TABLE `characters` 
ADD COLUMN IF NOT EXISTS `arena_rank_level` int(11) DEFAULT 1 COMMENT '竞技等级' AFTER `arena_last_reset`;

-- 检查并添加总胜利次数字段
ALTER TABLE `characters` 
ADD COLUMN IF NOT EXISTS `arena_total_wins` int(11) DEFAULT 0 COMMENT '总胜利次数' AFTER `arena_rank_level`;

-- 检查并添加总论道次数字段
ALTER TABLE `characters` 
ADD COLUMN IF NOT EXISTS `arena_total_battles` int(11) DEFAULT 0 COMMENT '总论道次数' AFTER `arena_total_wins`;

-- 检查并添加连胜次数字段
ALTER TABLE `characters` 
ADD COLUMN IF NOT EXISTS `arena_win_streak` int(11) DEFAULT 0 COMMENT '连胜次数' AFTER `arena_total_battles`;

-- 检查并添加最佳连胜纪录字段
ALTER TABLE `characters` 
ADD COLUMN IF NOT EXISTS `arena_best_streak` int(11) DEFAULT 0 COMMENT '最佳连胜纪录' AFTER `arena_win_streak`;

-- 检查并添加竞技技能释放序列字段
ALTER TABLE `characters` 
ADD COLUMN IF NOT EXISTS `arena_skill_sequence` varchar(50) DEFAULT '0,1,2,3,4,5' COMMENT '竞技技能释放序列' AFTER `arena_best_streak`;

-- ==========================================
-- 3. 创建索引优化查询性能
-- ==========================================

-- 为characters表新增字段创建索引
CREATE INDEX `idx_arena_dao_power` ON `characters` (`arena_dao_power`);
CREATE INDEX `idx_arena_total_wins` ON `characters` (`arena_total_wins`);
CREATE INDEX `idx_arena_rank_level` ON `characters` (`arena_rank_level`);
CREATE INDEX `idx_arena_last_reset` ON `characters` (`arena_last_reset`);

-- ==========================================
-- 4. 初始化现有角色的竞技数据
-- ==========================================

-- 为现有角色初始化基础竞技数据
UPDATE `characters` 
SET 
    `arena_dao_power` = 0,
    `arena_daily_attempts` = 0,
    `arena_purchased_attempts` = 0,
    `arena_last_reset` = CURDATE(),
    `arena_rank_level` = 1,
    `arena_total_wins` = 0,
    `arena_total_battles` = 0,
    `arena_win_streak` = 0,
    `arena_best_streak` = 0,
    `arena_skill_sequence` = '0,1,2,3,4,5'
WHERE `arena_dao_power` IS NULL OR `arena_dao_power` = 0;

-- ==========================================
-- 5. 创建匹配池临时表（用于实时匹配）
-- ==========================================

CREATE TABLE IF NOT EXISTS `immortal_arena_match_pool` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '匹配ID',
  `character_id` int(11) NOT NULL COMMENT '角色ID',
  `dao_power` int(11) NOT NULL COMMENT '道行值',
  `realm_level` int(11) NOT NULL COMMENT '境界等级',
  `character_snapshot` text NOT NULL COMMENT '角色快照数据(JSON)',
  `match_timeout` timestamp NOT NULL COMMENT '匹配超时时间',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_character_id` (`character_id`),
  KEY `idx_dao_power` (`dao_power`),
  KEY `idx_realm_level` (`realm_level`),
  KEY `idx_timeout` (`match_timeout`),
  FOREIGN KEY (`character_id`) REFERENCES `characters` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='升仙大会实时匹配池';

-- ==========================================
-- 6. 创建段位等级配置表
-- ==========================================

CREATE TABLE IF NOT EXISTS `immortal_arena_ranks` (
  `rank_level` int(11) NOT NULL COMMENT '段位等级',
  `rank_name` varchar(20) NOT NULL COMMENT '段位名称',
  `min_dao_power` int(11) NOT NULL COMMENT '最低道行值要求',
  `max_dao_power` int(11) DEFAULT NULL COMMENT '最高道行值（下个段位门槛）',
  `rank_color` varchar(7) DEFAULT '#FFFFFF' COMMENT '段位颜色',
  `reward_multiplier` decimal(3,2) DEFAULT 1.00 COMMENT '奖励倍率',
  PRIMARY KEY (`rank_level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='升仙大会段位等级配置';

-- 插入基础段位配置
INSERT IGNORE INTO `immortal_arena_ranks` VALUES
(1, '练气期', 0, 1999, '#FFFFFF', 1.00),
(2, '筑基期', 2000, 3999, '#C0C0C0', 1.10),
(3, '结丹期', 4000, 5999, '#FFB366', 1.20),
(4, '元婴期', 6000, 7999, '#66B3FF', 1.30),
(5, '化神期', 8000, 9999, '#B366FF', 1.40),
(6, '合体期', 10000, 12999, '#FF6B6B', 1.50),
(7, '大乘期', 13000, 15999, '#FFD700', 1.75),
(8, '渡劫期', 16000, 19999, '#FF4500', 2.00),
(9, '仙人境', 20000, 29999, '#00FFFF', 2.50),
(10, '仙君境', 30000, NULL, '#FF1493', 3.00);

-- ==========================================
-- 7. 数据库完整性检查
-- ==========================================

-- 检查表是否创建成功
SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    TABLE_ROWS
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = 'yn_game' 
AND TABLE_NAME IN ('immortal_arena_records', 'immortal_arena_match_pool', 'immortal_arena_ranks');

-- 检查characters表新增字段
SELECT 
    COLUMN_NAME,
    COLUMN_TYPE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'yn_game' 
AND TABLE_NAME = 'characters' 
AND COLUMN_NAME LIKE 'arena_%';

-- ==========================================
-- 数据库脚本执行完成
-- ==========================================

SELECT '🚀 升仙大会竞技系统数据库初始化完成！' AS message; 