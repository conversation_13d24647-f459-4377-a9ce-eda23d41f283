<?php
/**
 * VSCode配置检查脚本
 * 检查开发环境配置是否正确
 */

echo "=== 一念修仙项目 VSCode 配置检查 ===\n\n";

// 检查PHP环境
echo "1. PHP环境检查:\n";
echo "   PHP版本: " . PHP_VERSION . "\n";
echo "   PHP路径: " . PHP_BINARY . "\n";

// 检查扩展
$required_extensions = ['pdo', 'pdo_mysql', 'json', 'session', 'mbstring'];
echo "\n2. PHP扩展检查:\n";
foreach ($required_extensions as $ext) {
    $status = extension_loaded($ext) ? "✓" : "✗";
    echo "   {$status} {$ext}\n";
}

// 检查数据库连接
echo "\n3. 数据库连接检查:\n";
try {
    require_once __DIR__ . '/src/config/database.php';
    $pdo = getDatabase();
    if ($pdo) {
        echo "   ✓ 数据库连接成功\n";
        echo "   数据库: " . DB_NAME . "\n";
        echo "   用户: " . DB_USER . "\n";
        
        // 检查表数量
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "   表数量: " . count($tables) . "\n";
    } else {
        echo "   ✗ 数据库连接失败\n";
    }
} catch (Exception $e) {
    echo "   ✗ 数据库连接错误: " . $e->getMessage() . "\n";
}

// 检查项目目录结构
echo "\n4. 项目目录检查:\n";
$required_dirs = [
    'src/api' => 'API目录',
    'src/config' => '配置目录',
    'src/includes' => '公共库目录',
    'public' => '公共资源目录',
    '.vscode' => 'VSCode配置目录'
];

foreach ($required_dirs as $dir => $desc) {
    $status = is_dir($dir) ? "✓" : "✗";
    echo "   {$status} {$desc}: {$dir}\n";
}

// 检查VSCode配置文件
echo "\n5. VSCode配置文件检查:\n";
$vscode_files = [
    '.vscode/settings.json' => 'VSCode设置',
    '.vscode/launch.json' => '调试配置',
    '.vscode/tasks.json' => '任务配置',
    '.vscode/extensions.json' => '扩展推荐',
    '.vscode/snippets/php.json' => 'PHP代码片段',
    '.vscode/api_test.http' => 'API测试文件'
];

foreach ($vscode_files as $file => $desc) {
    $status = file_exists($file) ? "✓" : "✗";
    echo "   {$status} {$desc}: {$file}\n";
}

// 检查权限
echo "\n6. 目录权限检查:\n";
$writable_dirs = ['logs', 'data/backups'];
foreach ($writable_dirs as $dir) {
    if (is_dir($dir)) {
        $status = is_writable($dir) ? "✓" : "✗";
        echo "   {$status} {$dir} 可写权限\n";
    } else {
        echo "   - {$dir} 目录不存在\n";
    }
}

// 检查Web服务器配置
echo "\n7. Web服务器检查:\n";
if (isset($_SERVER['SERVER_SOFTWARE'])) {
    echo "   服务器: " . $_SERVER['SERVER_SOFTWARE'] . "\n";
    echo "   文档根目录: " . $_SERVER['DOCUMENT_ROOT'] . "\n";
} else {
    echo "   运行在命令行模式\n";
}

// 生成配置报告
echo "\n8. 配置建议:\n";

// 检查setting.php
if (file_exists('setting.php')) {
    echo "   ✓ 主配置文件存在\n";
    require_once 'setting.php';
    if (defined('GAME_DEBUG') && GAME_DEBUG) {
        echo "   ⚠ 调试模式已开启，生产环境请关闭\n";
    }
} else {
    echo "   ✗ 主配置文件不存在\n";
}

// 检查config.js
if (file_exists('public/assets/js/config.js')) {
    echo "   ✓ 前端配置文件存在\n";
} else {
    echo "   ⚠ 前端配置文件不存在\n";
}

echo "\n=== 检查完成 ===\n";
echo "如果有 ✗ 标记的项目，请检查相关配置。\n";
echo "如果有 ⚠ 标记的项目，请注意相关建议。\n";
?>
