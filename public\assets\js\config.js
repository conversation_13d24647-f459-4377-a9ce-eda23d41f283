/**
 * 前端配置文件 - 一念修仙项目
 * 统一管理所有前端配置，包括API路径、资源路径等
 */

// 🌐 项目基础配置
window.GameConfig = {
    // 项目名称（用于构建完整路径）
    PROJECT_NAME: 'yinian',

    // API基础路径 - 针对phpstudy_pro环境优化
    API_BASE_URL: '/yinian/src/api/',

    // 静态资源基础路径
    ASSETS_BASE_URL: '/yinian/public/assets/',
    
    // 具体资源路径
    PATHS: {
        CSS: '/yinian/public/assets/css/',
        JS: '/yinian/public/assets/js/',
        IMAGES: '/yinian/public/assets/images/',
        AUDIO: '/yinian/public/assets/audio/'
    },
    
    // 🔧 API路径构建器
    getApiUrl: function(endpoint) {
        // 确保endpoint不以/开头，避免重复
        const cleanEndpoint = endpoint.startsWith('/') ? endpoint.substring(1) : endpoint;
        return this.API_BASE_URL + cleanEndpoint;
    },
    
    // 🖼️ 图片路径构建器
    getImageUrl: function(imagePath) {
        // 如果已经是完整URL，直接返回
        if (imagePath.startsWith('http') || imagePath.startsWith('/yinian/')) {
            return imagePath;
        }
        
        // 如果以assets/images/开头，替换为完整路径
        if (imagePath.startsWith('assets/images/')) {
            return '/' + this.PROJECT_NAME + '/public/' + imagePath;
        }
        
        // 否则添加完整的images路径前缀
        return this.PATHS.IMAGES + imagePath;
    },
    
    // 🎵 音频路径构建器
    getAudioUrl: function(audioPath) {
        if (audioPath.startsWith('http') || audioPath.startsWith('/yinian/')) {
            return audioPath;
        }
        return this.PATHS.AUDIO + audioPath;
    },
    
    // 📄 CSS路径构建器
    getCssUrl: function(cssPath) {
        if (cssPath.startsWith('http') || cssPath.startsWith('/yinian/')) {
            return cssPath;
        }
        return this.PATHS.CSS + cssPath;
    },
    
    // 📜 JS路径构建器
    getJsUrl: function(jsPath) {
        if (jsPath.startsWith('http') || jsPath.startsWith('/yinian/')) {
            return jsPath;
        }
        return this.PATHS.JS + jsPath;
    }
};

// 🔧 向后兼容性支持
window.API_BASE_URL = window.GameConfig.API_BASE_URL;
window.ASSETS_BASE_URL = window.GameConfig.ASSETS_BASE_URL;

// 🚀 初始化日志
console.log('🎮 游戏配置已加载:', {
    projectName: window.GameConfig.PROJECT_NAME,
    apiBaseUrl: window.GameConfig.API_BASE_URL,
    assetsBaseUrl: window.GameConfig.ASSETS_BASE_URL
});

// 🔧 全局辅助函数（向后兼容）
window.getApiUrl = function(endpoint) {
    return window.GameConfig.getApiUrl(endpoint);
};

window.getImageUrl = function(imagePath) {
    return window.GameConfig.getImageUrl(imagePath);
};
