# Setting.php 使用指南

## 概述

`setting.php` 是一念修仙项目的全局配置文件，提供了统一的配置管理和便捷的路径访问函数。

## 文件位置

```
yinian/
├── setting.php                 # 全局配置文件（项目根目录）
├── test_setting.php           # 配置测试脚本
└── public/
    └── test_setting_integration.html  # 前端集成测试页面
```

## 主要功能

### 1. 游戏基本信息配置

```php
// 游戏基本信息
define('GAME_NAME', '一念修仙');
define('GAME_VERSION', '1.0.0');
define('GAME_AUTHOR', '一念修仙开发团队');
define('GAME_DESCRIPTION', '一款基于Web的修仙类RPG游戏');

// 环境设置
define('GAME_ENV', 'development');
define('GAME_DEBUG', true);
define('GAME_MAINTENANCE', false);
```

### 2. 目录路径定义

```php
// 核心目录
define('PROJECT_ROOT', dirname(__FILE__));
define('SRC_DIR', PROJECT_ROOT . DIRECTORY_SEPARATOR . 'src');
define('API_DIR', SRC_DIR . DIRECTORY_SEPARATOR . 'api');
define('CONFIG_DIR', SRC_DIR . DIRECTORY_SEPARATOR . 'config');
define('INCLUDES_DIR', SRC_DIR . DIRECTORY_SEPARATOR . 'includes');

// 前端资源目录
define('ASSETS_DIR', PUBLIC_ROOT . DIRECTORY_SEPARATOR . 'assets');
define('CSS_DIR', ASSETS_DIR . DIRECTORY_SEPARATOR . 'css');
define('JS_DIR', ASSETS_DIR . DIRECTORY_SEPARATOR . 'js');
define('IMAGES_DIR', ASSETS_DIR . DIRECTORY_SEPARATOR . 'images');

// Web访问路径
define('API_PATH', '/src/api/');
define('ASSETS_PATH', '/public/assets/');
```

### 3. 数据库配置

```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'yn_game');
define('DB_USER', 'ynxx');
define('DB_PASS', 'mjlxz159');
define('DB_CHARSET', 'utf8mb4');
define('DB_PORT', 3306);
```

### 4. 游戏设置

```php
// 角色设置
define('MAX_CHARACTERS_PER_USER', 3);
define('CHARACTER_NAME_MIN_LENGTH', 2);
define('CHARACTER_NAME_MAX_LENGTH', 12);

// 战斗设置
define('BATTLE_TIMEOUT', 30);
define('BATTLE_MAX_ROUNDS', 100);

// 装备设置
define('EQUIPMENT_MAX_LEVEL', 100);
define('EQUIPMENT_QUALITIES', ['普通', '稀有', '史诗', '传说', '神话']);
```

## 实用函数

### 路径获取函数

```php
// 获取项目根目录
$root = getProjectRoot();

// 获取API文件路径
$apiPath = getApiPath('user_info.php');
$apiUrl = getApiUrl('user_info.php');

// 获取配置文件路径
$configPath = getConfigPath('database.php');

// 获取包含文件路径
$includePath = getIncludePath('functions.php');

// 获取静态资源URL
$cssUrl = getAssetUrl('css', 'style.css');
$jsUrl = getAssetUrl('js', 'script.js');
$imageUrl = getAssetUrl('images', 'logo.png');

// 获取日志文件路径
$logPath = getLogPath('game.log');
```

### 数据库函数

```php
// 获取数据库连接
$pdo = getDatabaseConnection();
if ($pdo) {
    // 使用数据库连接
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$userId]);
}
```

### 日志函数

```php
// 写入日志
writeLog("用户登录成功", 'INFO', 'auth.log');
writeLog("数据库连接失败", 'ERROR', 'database.log');
writeLog("调试信息", 'DEBUG', 'debug.log');
```

### 状态检查函数

```php
// 检查调试模式
if (isDebugMode()) {
    // 调试模式下的操作
}

// 检查维护模式
if (isMaintenanceMode()) {
    // 维护模式处理
}

// 获取游戏配置
$config = getGameConfig();
```

### 配置验证函数

```php
// 验证配置
$validation = validateConfig();
if (!$validation['valid']) {
    // 处理配置错误
    foreach ($validation['errors'] as $error) {
        echo "错误: " . $error . "\n";
    }
}
```

## 在API文件中使用

### 方法1：通过functions.php引入（推荐）

```php
<?php
// 引入functions.php（已经包含setting.php）
require_once __DIR__ . '/../includes/functions.php';

// 检查维护模式
if (isMaintenanceMode()) {
    echo json_encode(['success' => false, 'message' => '维护中']);
    exit;
}

// 记录API调用
if (DEBUG_LOG_API_CALLS) {
    writeLog("API调用: " . basename(__FILE__), 'DEBUG', 'api.log');
}

// 使用配置
$maxCharacters = MAX_CHARACTERS_PER_USER;
$pdo = getDatabaseConnection();
```

### 方法2：直接引入setting.php

```php
<?php
// 直接引入setting.php
require_once __DIR__ . '/../../setting.php';

// 使用配置和函数
if (isMaintenanceMode()) {
    header('Content-Type: application/json');
    echo json_encode(['maintenance' => true]);
    exit;
}
```

## 在前端页面中使用

### HTML页面中引用资源

```html
<!-- 使用配置中定义的路径 -->
<link rel="stylesheet" href="/public/assets/css/style.css">
<script src="/public/assets/js/script.js"></script>
<img src="/public/assets/images/logo.png" alt="Logo">
```

### JavaScript中调用API

```javascript
// 使用配置中定义的API路径
fetch('/src/api/user_info.php')
    .then(response => response.json())
    .then(data => {
        console.log(data);
    });
```

## 配置修改指南

### 开发环境 vs 生产环境

```php
// 开发环境设置
define('GAME_ENV', 'development');
define('GAME_DEBUG', true);
define('GAME_MAINTENANCE', false);

// 生产环境设置
define('GAME_ENV', 'production');
define('GAME_DEBUG', false);
define('GAME_MAINTENANCE', false);
```

### 数据库配置修改

```php
// 修改数据库连接信息
define('DB_HOST', 'your_host');
define('DB_NAME', 'your_database');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

### 游戏参数调整

```php
// 调整游戏参数
define('MAX_CHARACTERS_PER_USER', 5);        // 增加最大角色数
define('BATTLE_TIMEOUT', 60);                // 增加战斗超时时间
define('INVENTORY_DEFAULT_SIZE', 100);       // 增加默认背包大小
```

## 测试和验证

### 1. 运行配置测试

访问：`http://localhost/yinian/test_setting.php`

### 2. 运行简单测试

访问：`http://localhost/yinian/test_setting_simple.php`

### 3. 运行集成测试

访问：`http://localhost/yinian/public/test_setting_integration.html`

### 4. API测试

访问：`http://localhost/yinian/src/api/example_using_setting.php?action=config`

## 最佳实践

### 1. 统一使用配置函数

```php
// ✅ 推荐
$apiPath = getApiPath('user_info.php');
$logPath = getLogPath('game.log');

// ❌ 不推荐
$apiPath = __DIR__ . '/../api/user_info.php';
$logPath = __DIR__ . '/../../logs/game.log';
```

### 2. 使用常量而不是硬编码

```php
// ✅ 推荐
if (strlen($name) < CHARACTER_NAME_MIN_LENGTH) {
    return false;
}

// ❌ 不推荐
if (strlen($name) < 2) {
    return false;
}
```

### 3. 利用调试开关

```php
// ✅ 推荐
if (DEBUG_LOG_API_CALLS) {
    writeLog("API调用详情", 'DEBUG');
}

// ❌ 不推荐
writeLog("API调用详情", 'DEBUG'); // 总是记录
```

### 4. 检查维护模式

```php
// ✅ 推荐：在API开始处检查
if (isMaintenanceMode()) {
    echo json_encode(['maintenance' => true]);
    exit;
}
```

## 常见问题排查

### "No input file specified." 错误

这个错误通常表示：

1. **Web服务器配置问题**
   - PHPStudy可能需要重启
   - 虚拟主机配置可能有问题

2. **PHP文件路径问题**
   - 确认访问的URL路径正确
   - 确认文件确实存在

3. **权限问题**
   - 检查文件和目录权限

### 解决步骤

1. 首先测试简单的PHP文件：`test_setting_simple.php`
2. 如果简单文件可以运行，再测试完整的`test_setting.php`
3. 检查PHPStudy是否正常启动
4. 确认访问的域名和端口是否正确

## 更新日志

- **v1.0.0** (2025-06-27)
  - 初始版本发布
  - 包含完整的配置管理功能
  - 提供路径管理和实用函数
  - 集成测试页面和文档
