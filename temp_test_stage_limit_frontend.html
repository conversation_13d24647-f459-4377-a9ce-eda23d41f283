<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关卡限制系统前端测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .test-button:hover {
            transform: scale(1.05);
        }
        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🎮 关卡限制系统前端测试</h1>
    
    <div class="test-section">
        <h2>📋 测试说明</h2>
        <p>此页面用于测试关卡限制系统的前端行为。请按以下步骤进行测试：</p>
        <ol>
            <li><strong>设置测试数据</strong>：点击下方按钮设置角色到碧水寒潭第90层（最大层）</li>
            <li><strong>进入战斗</strong>：访问碧水寒潭地图进行战斗</li>
            <li><strong>验证按钮行为</strong>：胜利后检查"下一关"按钮是否显示为"已通关"</li>
            <li><strong>测试点击行为</strong>：点击"已通关"按钮应该重新挑战第90层，而不是尝试进入第91层</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>🔧 测试工具</h2>
        
        <button class="test-button" onclick="setTestData()">
            🎯 设置测试数据 (碧水寒潭第90层)
        </button>
        
        <button class="test-button" onclick="checkCurrentProgress()">
            📊 检查当前进度
        </button>
        
        <button class="test-button" onclick="testMapData()">
            🗺️ 测试地图数据获取
        </button>
        
        <button class="test-button" onclick="openBattlePage()">
            ⚔️ 打开战斗页面
        </button>
        
        <div id="testResults"></div>
    </div>

    <div class="test-section">
        <h2>📝 预期结果</h2>
        <div class="info status">
            <strong>正确的行为应该是：</strong>
            <ul>
                <li>✅ 在第90层胜利后，按钮显示"已通关"而不是"下一关"</li>
                <li>✅ 点击"已通关"按钮重新开始第90层战斗</li>
                <li>✅ 不会出现"已达到地图最大关卡数"的错误</li>
                <li>✅ 控制台显示"🏆 已达到最大层数：按钮设置为'已通关'"</li>
                <li>✅ 点击时显示"🏆 已通关：重新挑战当前最大层"</li>
            </ul>
        </div>
    </div>

    <script>
        function showResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const resultElement = document.createElement('div');
            resultElement.className = `status ${type}`;
            resultElement.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            resultsDiv.appendChild(resultElement);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        async function setTestData() {
            try {
                showResult('🔄 正在设置测试数据...', 'info');
                
                const response = await fetch('../src/api/update_map_progress.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=set_stage&map_id=2&stage=90'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showResult(`✅ 测试数据设置成功: ${result.message}`, 'success');
                    showResult(`📊 当前层数: ${result.new_stage}, 最高层数: ${result.max_stage_reached}`, 'info');
                } else {
                    showResult(`❌ 设置失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showResult(`❌ 请求失败: ${error.message}`, 'error');
            }
        }

        async function checkCurrentProgress() {
            try {
                showResult('🔄 正在检查当前进度...', 'info');
                
                const response = await fetch('../src/api/battle_unified.php?action=init_battle&map_id=2&map_code=bishuihantan&stage_number=90');
                const result = await response.json();
                
                if (result.success && result.data) {
                    const mapInfo = result.data.map_info;
                    showResult(`✅ 地图信息获取成功`, 'success');
                    showResult(`🗺️ 地图: ${mapInfo.map_name} (ID: ${mapInfo.id})`, 'info');
                    showResult(`📊 最大关卡: ${mapInfo.max_stages}`, 'info');
                    
                    // 显示详细信息
                    const details = document.createElement('pre');
                    details.textContent = JSON.stringify(mapInfo, null, 2);
                    document.getElementById('testResults').appendChild(details);
                } else {
                    showResult(`❌ 获取进度失败: ${result.message || '未知错误'}`, 'error');
                }
            } catch (error) {
                showResult(`❌ 请求失败: ${error.message}`, 'error');
            }
        }

        async function testMapData() {
            try {
                showResult('🔄 正在测试地图数据...', 'info');
                
                // 测试所有地图的最大关卡数
                const maps = [
                    {id: 1, name: '太乙峰', expected: 60},
                    {id: 2, name: '碧水寒潭', expected: 90},
                    {id: 3, name: '赤焰谷', expected: 120},
                    {id: 4, name: '幽冥鬼域', expected: 140},
                    {id: 5, name: '青云仙山', expected: 150},
                    {id: 6, name: '星辰古战场', expected: 140},
                    {id: 7, name: '混元虚空', expected: 140},
                    {id: 8, name: '洪荒秘境', expected: 175}
                ];
                
                for (const map of maps) {
                    const response = await fetch(`../src/api/battle_unified.php?action=init_battle&map_id=${map.id}&stage_number=1`);
                    const result = await response.json();
                    
                    if (result.success && result.data && result.data.map_info) {
                        const actual = result.data.map_info.max_stages;
                        const status = actual == map.expected ? '✅' : '❌';
                        showResult(`${status} ${map.name}: ${actual}层 (期望${map.expected}层)`, actual == map.expected ? 'success' : 'error');
                    } else {
                        showResult(`❌ ${map.name}: 数据获取失败`, 'error');
                    }
                }
            } catch (error) {
                showResult(`❌ 测试失败: ${error.message}`, 'error');
            }
        }

        function openBattlePage() {
            showResult('🚀 正在打开碧水寒潭战斗页面...', 'info');
            window.open('battle.html?map_code=bishuihantan&stage=90', '_blank');
        }

        // 页面加载时显示说明
        window.onload = function() {
            showResult('📋 测试页面已加载，请按照说明进行测试', 'info');
        };
    </script>
</body>
</html>
