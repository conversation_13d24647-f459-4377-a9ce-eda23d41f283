.main-container {
    padding: 10px 15px 80px 15px;
    height: 100vh;
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
}

/* 页面标题 */
.page-title {
    text-align: center;
    margin-bottom: 15px;
}

.page-title h1 {
    font-size: 22px;
    color: #d4af37;
    text-shadow: 0 0 10px rgba(212, 175, 55, 0.5);
    margin-bottom: 3px;
}

.page-subtitle {
    font-size: 12px;
    color: #bdc3c7;
    margin-bottom: 10px;
}

/* ✨ 整合的五行相生相克图布局 */
.wuxing-diagram {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
    border-radius: 15px;
    padding: 20px;
    border: 2px solid rgba(212, 175, 55, 0.4);
    backdrop-filter: blur(15px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 10px;
}

/* 紧凑的灵根总览 */
.spirit-overview-compact {
    display: flex;
    gap: 25px;
    margin-bottom: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.overview-item {
    display: flex;
    align-items: center;
    gap: 5px;
    background: rgba(0, 0, 0, 0.3);
    padding: 8px 12px;
    border-radius: 8px;
    border: 1px solid rgba(212, 175, 55, 0.3);
}

.overview-label {
    font-size: 12px;
    color: #d4af37;
    font-weight: bold;
}

.overview-value {
    font-size: 14px;
    color: #fff;
    font-weight: bold;
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
}

.wuxing-container {
    position: relative;
    width: 300px;
    height: 300px;
    margin: 10px auto;
}

/* 相生线条（外圈金色） */
.shengxian {
    stroke: #ffd700;
    stroke-width: 2;
    fill: none;
    opacity: 0.7;
    stroke-dasharray: 5,5;
    animation: flow 3s linear infinite;
}

/* 相克线条（内五角星红色） */
.kexian {
    stroke: #ff4500;
    stroke-width: 3;
    fill: none;
    opacity: 0.8;
}

@keyframes flow {
    0% { stroke-dashoffset: 0; }
    100% { stroke-dashoffset: 10; }
}

/* 中心标题 */
.wuxing-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 10;
}

.center-title {
    font-size: 14px;
    color: #d4af37;
    font-weight: bold;
    text-shadow: 0 0 8px rgba(212, 175, 55, 0.5);
    background: rgba(0, 0, 0, 0.8);
    padding: 6px 12px;
    border-radius: 8px;
    border: 1px solid rgba(212, 175, 55, 0.4);
}

/* 五行元素基础样式 */
.wuxing-element {
    position: absolute;
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
    z-index: 5;
}

.wuxing-element:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.5);
    z-index: 6;
}

/* 五行元素按五角星位置定位 */

/* 火元素（正上方） */
.wuxing-element.fire {
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    background: radial-gradient(circle, rgba(255, 69, 0, 0.9), rgba(255, 99, 71, 0.7));
    border-color: #ff4500;
    box-shadow: 0 0 20px rgba(255, 69, 0, 0.6);
}

/* 土元素（右上） */
.wuxing-element.earth {
    top: 80px;
    right: 0px;
    background: radial-gradient(circle, rgba(139, 69, 19, 0.9), rgba(210, 180, 140, 0.7));
    border-color: #8b4513;
    box-shadow: 0 0 20px rgba(139, 69, 19, 0.6);
}

/* 金元素（右下） */
.wuxing-element.metal {
    bottom: 10px;
    right: 45px;
    background: radial-gradient(circle, rgba(192, 192, 192, 0.9), rgba(255, 215, 0, 0.7));
    border-color: #ffd700;
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
}

/* 水元素（左下） */
.wuxing-element.water {
    bottom: 10px;
    left: 45px;
    background: radial-gradient(circle, rgba(30, 144, 255, 0.9), rgba(135, 206, 235, 0.7));
    border-color: #1e90ff;
    box-shadow: 0 0 20px rgba(30, 144, 255, 0.6);
}

/* 木元素（左上） */
.wuxing-element.wood {
    top: 80px;
    left: 0px;
    background: radial-gradient(circle, rgba(34, 139, 34, 0.9), rgba(50, 205, 50, 0.7));
    border-color: #32cd32;
    box-shadow: 0 0 20px rgba(50, 205, 50, 0.6);
}

.element-icon {
    font-size: 24px;
    margin-bottom: 3px;
}

.element-name {
    font-size: 12px;
    font-weight: bold;
    color: #fff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
    margin-bottom: 2px;
}

.element-value {
    font-size: 10px;
    color: #fff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
    font-weight: bold;
}

/* 灵根操作面板 */
.spirit-actions-panel {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.action-button {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 8px 15px;
    border: none;
    border-radius: 10px;
    font-size: 12px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #fff;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
    border: 2px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    min-width: 80px;
    justify-content: center;
}

.enhance-all-btn {
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.9), rgba(56, 142, 60, 0.8));
    box-shadow: 0 3px 10px rgba(76, 175, 80, 0.3);
}

.enhance-all-btn:hover {
    background: linear-gradient(135deg, rgba(76, 175, 80, 1), rgba(56, 142, 60, 0.9));
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4);
}

.balance-btn {
    background: linear-gradient(135deg, rgba(156, 39, 176, 0.9), rgba(123, 31, 162, 0.8));
    box-shadow: 0 3px 10px rgba(156, 39, 176, 0.3);
}

.balance-btn:hover {
    background: linear-gradient(135deg, rgba(156, 39, 176, 1), rgba(123, 31, 162, 0.9));
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(156, 39, 176, 0.4);
}

.info-btn {
    background: linear-gradient(135deg, rgba(33, 150, 243, 0.9), rgba(30, 136, 229, 0.8));
    box-shadow: 0 3px 10px rgba(33, 150, 243, 0.3);
}

.info-btn:hover {
    background: linear-gradient(135deg, rgba(33, 150, 243, 1), rgba(30, 136, 229, 0.9));
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(33, 150, 243, 0.4);
}

.button-icon {
    font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .wuxing-container {
        width: 280px;
        height: 280px;
    }
    
    .wuxing-element {
        width: 60px;
        height: 60px;
    }
    
    .element-icon {
        font-size: 20px;
    }
    
    .element-name {
        font-size: 10px;
    }
    
    .element-value {
        font-size: 9px;
    }
    
    .center-title {
        font-size: 12px;
        padding: 5px 8px;
    }
    
    .spirit-actions-panel {
        gap: 8px;
    }
    
    .action-button {
        padding: 6px 10px;
        font-size: 11px;
        min-width: 70px;
    }
    
    .overview-item {
        padding: 6px 10px;
    }
    
    .overview-label {
        font-size: 11px;
    }
    
    .overview-value {
        font-size: 12px;
    }
}

@media (max-width: 375px) {
    .page-title h1 {
        font-size: 20px;
    }
    
    .wuxing-diagram {
        padding: 15px;
    }
    
    .wuxing-container {
        width: 260px;
        height: 260px;
    }
    
    .wuxing-element {
        width: 55px;
        height: 55px;
    }
    
    .element-icon {
        font-size: 18px;
    }
    
    .spirit-overview-compact {
        gap: 15px;
        margin-bottom: 10px;
    }
    
    .wuxing-legend {
        gap: 15px;
        margin: 10px 0;
    }
    
    .spirit-actions-panel {
        margin-top: 10px;
    }
}

.loading {
    text-align: center;
    padding: 20px;
    color: #d4af37;
    font-size: 16px;
}

.loading::after {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #d4af37;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.message {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 15px 25px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: bold;
    z-index: 1000;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    white-space: pre-line;
    text-align: center;
    max-width: 300px;
}

.message.success {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    border: 1px solid #4CAF50;
}

.message.error {
    background: linear-gradient(135deg, #f44336, #da190b);
    color: white;
    border: 1px solid #f44336;
}

.message.info {
    background: linear-gradient(135deg, #2196F3, #1976D2);
    color: white;
    border: 1px solid #2196F3;
}

/* 新增按钮样式 */
.reset-btn {
    background: linear-gradient(135deg, rgba(156, 39, 176, 0.9), rgba(123, 31, 162, 0.8));
    box-shadow: 0 3px 10px rgba(156, 39, 176, 0.3);
}

.reset-btn:hover {
    background: linear-gradient(135deg, rgba(156, 39, 176, 1), rgba(123, 31, 162, 0.9));
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(156, 39, 176, 0.4);
}

.enhance-btn {
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.9), rgba(56, 142, 60, 0.8));
    box-shadow: 0 3px 10px rgba(76, 175, 80, 0.3);
}

.enhance-btn:hover {
    background: linear-gradient(135deg, rgba(76, 175, 80, 1), rgba(56, 142, 60, 0.9));
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4);
}

/* 弹窗样式 */
.spirit-modal, .confirm-modal, .enhance-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
    padding: 20px 10px 100px 10px; /* 上20px 左右10px 下100px(预留导航栏) */
    box-sizing: border-box;
}

.modal-content, .confirm-content, .enhance-content {
    background: linear-gradient(135deg, rgba(30, 30, 50, 0.95), rgba(20, 20, 40, 0.9));
    border-radius: 15px;
    border: 2px solid rgba(212, 175, 55, 0.4);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    max-width: 90%;
    max-height: 100%;
    overflow-y: auto;
    backdrop-filter: blur(10px);
    margin: auto;
}

.modal-header, .confirm-header, .enhance-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid rgba(212, 175, 55, 0.3);
}

.modal-header h3, .confirm-header h3, .enhance-header h3 {
    color: #d4af37;
    margin: 0;
    font-size: 18px;
}

.modal-close {
    background: none;
    border: none;
    color: #bdc3c7;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
}

.modal-body, .confirm-body, .enhance-body {
    padding: 20px;
    color: #fff;
}

/* 元素详情样式 */
.element-detail {
    text-align: left;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
}

.detail-label {
    color: #bdc3c7;
    font-size: 14px;
}

.detail-value {
    color: #d4af37;
    font-size: 16px;
    font-weight: bold;
}

.detail-desc {
    margin-bottom: 20px;
}

.detail-desc p {
    color: #bdc3c7;
    line-height: 1.6;
    margin: 0;
}

.element-enhance h4 {
    color: #d4af37;
    margin-bottom: 10px;
    font-size: 16px;
}

.element-enhance p {
    color: #bdc3c7;
    margin-bottom: 15px;
    font-size: 14px;
}

.enhance-controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.enhance-point-btn {
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.9), rgba(56, 142, 60, 0.8));
    border: none;
    border-radius: 8px;
    color: #fff;
    padding: 8px 12px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(76, 175, 80, 0.5);
}

.enhance-point-btn:hover {
    background: linear-gradient(135deg, rgba(76, 175, 80, 1), rgba(56, 142, 60, 0.9));
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(76, 175, 80, 0.4);
}

/* 灵根介绍样式 */
.spirit-intro h4 {
    color: #d4af37;
    margin: 20px 0 10px 0;
    font-size: 16px;
}

.spirit-intro h4:first-child {
    margin-top: 0;
}

.spirit-intro p {
    color: #bdc3c7;
    line-height: 1.6;
    margin-bottom: 15px;
}

.spirit-intro ul {
    color: #bdc3c7;
    margin-left: 20px;
    line-height: 1.6;
}

.spirit-intro li {
    margin-bottom: 8px;
}

.spirit-intro strong {
    color: #d4af37;
}

/* 确认按钮样式 */
.confirm-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 20px;
}

.confirm-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 80px;
}

.confirm-btn.cancel {
    background: linear-gradient(135deg, rgba(108, 117, 125, 0.9), rgba(73, 80, 87, 0.8));
    color: #fff;
}

.confirm-btn.cancel:hover {
    background: linear-gradient(135deg, rgba(108, 117, 125, 1), rgba(73, 80, 87, 0.9));
    transform: translateY(-1px);
}

.confirm-btn.confirm {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.9), rgba(200, 35, 51, 0.8));
    color: #fff;
}

.confirm-btn.confirm:hover {
    background: linear-gradient(135deg, rgba(220, 53, 69, 1), rgba(200, 35, 51, 0.9));
    transform: translateY(-1px);
}

/* 洗灵根弹窗专用样式 */
.wash-quality-section, .lock-section {
    margin: 20px 0;
    padding: 15px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    border: 1px solid rgba(212, 175, 55, 0.3);
}

.wash-quality-section h4, .lock-section h4 {
    color: #d4af37;
    margin: 0 0 15px 0;
    font-size: 16px;
}

.lock-tip {
    color: #bdc3c7;
    font-size: 12px;
    font-weight: normal;
}

.quality-options, .lock-options {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.quality-option, .lock-option {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 10px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.quality-option:hover, .lock-option:hover {
    background: rgba(212, 175, 55, 0.1);
}

.quality-option input, .lock-option input {
    margin: 0;
    cursor: pointer;
}

.quality-option span, .lock-option span {
    color: #fff;
    font-size: 14px;
    cursor: pointer;
}

.element-label {
    font-weight: bold;
}

.fire-label { color: #ff6347; }
.earth-label { color: #d2b48c; }
.metal-label { color: #ffd700; }
.water-label { color: #87ceeb; }
.wood-label { color: #32cd32; }

.cost-summary {
    margin: 15px 0;
    padding: 10px;
    background: rgba(212, 175, 55, 0.1);
    border-radius: 6px;
    border: 1px solid rgba(212, 175, 55, 0.3);
    text-align: center;
}

.cost-summary p {
    color: #d4af37;
    font-weight: bold;
    margin: 0;
    font-size: 14px;
}

#costCount {
    color: #ff6347;
    font-size: 16px;
}

/* 天材地宝列表样式 */
.treasure-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.treasure-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(212, 175, 55, 0.3);
    border-radius: 8px;
    padding: 12px 15px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.treasure-item:hover {
    background: rgba(212, 175, 55, 0.1);
    border-color: rgba(212, 175, 55, 0.6);
    transform: translateX(5px);
}

.treasure-name {
    color: #fff;
    font-weight: bold;
    font-size: 14px;
}

.treasure-effect {
    color: #d4af37;
    font-size: 12px;
    font-weight: bold;
}

/* 响应式弹窗 */
@media (max-width: 480px) {
    .spirit-modal, .confirm-modal, .enhance-modal {
        padding: 15px 5px 90px 5px; /* 移动端减少间距，底部预留90px */
    }
    
    .modal-content, .confirm-content, .enhance-content {
        max-width: 95%;
        margin: 10px;
    }
    
    .modal-header, .confirm-header, .enhance-header {
        padding: 12px 15px;
    }
    
    .modal-header h3, .confirm-header h3, .enhance-header h3 {
        font-size: 16px;
    }
    
    .modal-body, .confirm-body, .enhance-body {
        padding: 15px;
    }
    
    .enhance-controls {
        flex-direction: column;
    }
    
    .enhance-point-btn {
        width: 100%;
        text-align: center;
    }
    
    .confirm-buttons {
        flex-direction: column;
    }
    
    .confirm-btn {
        width: 100%;
    }
    
    /* 洗灵根弹窗移动端优化 */
    .wash-quality-section, .lock-section {
        margin: 15px 0;
        padding: 12px;
    }
    
    .quality-options, .lock-options {
        gap: 6px;
    }
    
    .quality-option, .lock-option {
        padding: 6px 8px;
        font-size: 13px;
    }
    
    .quality-option span, .lock-option span {
        font-size: 13px;
    }
}