/**
 * 雷法技能模块
 * 包含掌心雷等所有雷法技能动画
 */

/**
 * 掌心雷技能
 */
class ZhangXinLeiSkill extends BaseSkill {
    async execute(skillData, weaponImage) {
        // 使用数据库中的真实技能名称，而不是硬编码
        const skillName = skillData?.skillName || skillData?.displayName || '掌心雷'; // 提供默认值作为后备
        await this.showSkillShout(skillName);
        await this.createLightning();
    }

    async createLightning() {
        // 🔧 修复：动态判断位置映射
        let casterPos, targetPos;
        
        if (this.isEnemySkill) {
            // 敌方技能：敌人是施法者，玩家是目标
            casterPos = this.getCharacterPosition(false); // 敌人位置
            targetPos = this.getCharacterPosition(true);  // 玩家位置
        } else {
            // 我方技能：玩家是施法者，敌人是目标
            casterPos = this.getCharacterPosition(true);  // 玩家位置
            targetPos = this.getCharacterPosition(false); // 敌人位置
        }

        // 创建雷电容器
        const lightningContainer = document.createElement('div');
        lightningContainer.className = 'lightning-container';
        this.effectsContainer.appendChild(lightningContainer);

        // 🔧 修复：使用动态位置
        const startX = casterPos.x;
        const startY = casterPos.y;
        const endX = targetPos.x;
        const endY = targetPos.y;

        // 蓄力阶段 - 在玩家周围生成小闪电 - 完全对标原版
        const chargeContainer = document.createElement('div');
        chargeContainer.className = 'lightning-charge-container';
        chargeContainer.style.left = `${startX}px`;
        chargeContainer.style.top = `${startY}px`;
        lightningContainer.appendChild(chargeContainer);

        // 创建蓄力核心
        const chargeCore = this.createElement('leijian-charge-core');
        chargeContainer.appendChild(chargeCore);
        console.log(`⚡ 蓄力核心已创建`);
        
        // 创建环绕的小闪电
        for (let i = 0; i < 12; i++) {
            const angle = (i / 12) * Math.PI * 2;
            const lightning = this.createElement('leijian-charge-lightning', {
                style: {
                    '--angle': `${angle}rad`,
                    '--index': i
                }
            });
            chargeContainer.appendChild(lightning);
        }
        console.log(`⚡ 12个环绕闪电已创建`);
        
        // 创建电弧粒子
        for (let i = 0; i < 30; i++) {
            const particle = this.createElement('leijian-charge-particle', {
                style: {
                    '--random-delay': `${Math.random() * 1.2}s`,
                    '--random-angle': `${Math.random() * 360}deg`,
                    '--random-distance': `${30 + Math.random() * 40}px`
                }
            });
            chargeContainer.appendChild(particle);
        }

        // 创建电弧环效果 - 完全对标原版
        for (let i = 0; i < 3; i++) {
            const arcRing = document.createElement('div');
            arcRing.className = 'lightning-charge-arc-ring';
            arcRing.style.animationDelay = `${i * 0.3}s`;
            chargeContainer.appendChild(arcRing);
        }

        // 等待蓄力完成 (从800ms调整到1000ms)
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 移除蓄力效果
        chargeContainer.remove();

        // 创建预闪效果
        const preFlash = document.createElement('div');
        preFlash.className = 'lightning-preflash';
        preFlash.style.left = `${startX - 30}px`;
        preFlash.style.top = `${startY - 30}px`;
        lightningContainer.appendChild(preFlash);

        // 等待预闪完成 (从100ms调整到500ms)
        await new Promise(resolve => setTimeout(resolve, 500));

        // 创建主闪电路径（锯齿状） - 完全对标原版
        const mainLightning = this.createZigzagLightning(startX, startY, endX, endY, 'main');
        lightningContainer.appendChild(mainLightning);

        // 创建主闪电的内核（更亮的核心）
        const mainCore = this.createZigzagLightning(startX, startY, endX, endY, 'core');
        lightningContainer.appendChild(mainCore);

        // 创建多个分支闪电 - 完全对标原版
        const numBranches = 4 + Math.floor(Math.random() * 3); // 4-6个分支
        for (let i = 0; i < numBranches; i++) {
            // 在主路径上随机选择分支点
            const branchPoint = 0.2 + Math.random() * 0.6; // 20%-80%的位置
            const branchStartX = startX + (endX - startX) * branchPoint;
            const branchStartY = startY + (endY - startY) * branchPoint;
            
            // 生成分支终点（更自然的分布）
            const branchAngle = (Math.random() - 0.5) * Math.PI * 0.8; // 更窄的角度范围
            const branchLength = 30 + Math.random() * 70;
            const branchEndX = branchStartX + Math.cos(branchAngle) * branchLength;
            const branchEndY = branchStartY + Math.sin(branchAngle) * branchLength;
            
            const branchLightning = this.createZigzagLightning(branchStartX, branchStartY, branchEndX, branchEndY, 'branch');
            lightningContainer.appendChild(branchLightning);

            // 为一些分支添加二级分支
            if (Math.random() < 0.4) {
                const subBranchPoint = 0.5 + Math.random() * 0.3;
                const subStartX = branchStartX + (branchEndX - branchStartX) * subBranchPoint;
                const subStartY = branchStartY + (branchEndY - branchStartY) * subBranchPoint;
                const subAngle = branchAngle + (Math.random() - 0.5) * Math.PI * 0.5;
                const subLength = 20 + Math.random() * 40;
                const subEndX = subStartX + Math.cos(subAngle) * subLength;
                const subEndY = subStartY + Math.sin(subAngle) * subLength;
                
                const subBranch = this.createZigzagLightning(subStartX, subStartY, subEndX, subEndY, 'subbranch');
                lightningContainer.appendChild(subBranch);
            }
        }

        // 创建电磁场效果 - 完全对标原版
        const electricField = document.createElement('div');
        electricField.className = 'electric-field';
        electricField.style.left = `${Math.min(startX, endX) - 50}px`;
        electricField.style.top = `${Math.min(startY, endY) - 50}px`;
        electricField.style.width = `${Math.abs(endX - startX) + 100}px`;
        electricField.style.height = `${Math.abs(endY - startY) + 100}px`;
        lightningContainer.appendChild(electricField);

        // 等待闪电动画完成 (从250ms调整到500ms)
        await new Promise(resolve => setTimeout(resolve, 500));

        // 创建强烈的闪光效果
        const flash = document.createElement('div');
        flash.className = 'lightning-flash-screen';
        lightningContainer.appendChild(flash);

        // 创建电弧效果容器
        const arcContainer = document.createElement('div');
        arcContainer.className = 'lightning-arc';
        this.effectsContainer.appendChild(arcContainer);

        // 创建更多电弧粒子 - 完全对标原版
        for (let i = 0; i < 35; i++) {
            const particle = document.createElement('div');
            particle.className = 'lightning-particle';
            
            // 在闪电路径上随机分布粒子
            const pathProgress = Math.random();
            const particleX = startX + (endX - startX) * pathProgress;
            const particleY = startY + (endY - startY) * pathProgress;
            
            particle.style.left = `${particleX}px`;
            particle.style.top = `${particleY}px`;
            
            const particleAngle = (Math.random() * Math.PI * 2);
            const particleDistance = 15 + Math.random() * 50;
            const moveX = Math.cos(particleAngle) * particleDistance;
            const moveY = Math.sin(particleAngle) * particleDistance;
            
            particle.style.setProperty('--moveX', `${moveX}px`);
            particle.style.setProperty('--moveY', `${moveY}px`);
            
            const delay = Math.random() * 0.4;
            particle.style.animation = `lightning-particle 0.6s ease-out ${delay}s forwards`;
            
            arcContainer.appendChild(particle);
        }

        // 创建冲击波效果
        const impact = document.createElement('div');
        impact.className = 'lightning-impact';
        impact.style.left = `${endX - 40}px`;
        impact.style.top = `${endY - 40}px`;
        impact.style.animation = 'lightning-impact 1s ease-out forwards';

        lightningContainer.appendChild(impact);

        // 🔧 修复：为被攻击者添加雷电受击效果
        const targetSelector = !this.isEnemySkill ? '.enemy .character-sprite' : '.player .character-sprite';
        const targetSprite = document.querySelector(targetSelector);
        if (targetSprite) {
            targetSprite.style.animation = 'lightning-hit 1s ease-out, lightning-shake 0.08s ease-in-out 12';
        }

        // 等待动画完成后移除效果 (保持1000ms，因为击中阶段包含多个效果)
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 移除所有效果
        lightningContainer.remove();
        arcContainer.remove();
    }

    // 创建锯齿状闪电的辅助方法 - 完全对标原版
    createZigzagLightning(startX, startY, endX, endY, type) {
        const lightning = document.createElement('div');
        lightning.className = `zigzag-lightning lightning-${type}`;
        
        // 计算基本参数
        const totalDistance = Math.sqrt(Math.pow(endX - startX, 2) + Math.pow(endY - startY, 2));
        const segments = Math.floor(totalDistance / 15) + 3; // 根据距离调整段数
        
        // 生成锯齿路径
        let pathPoints = [];
        for (let i = 0; i <= segments; i++) {
            const progress = i / segments;
            const baseX = startX + (endX - startX) * progress;
            const baseY = startY + (endY - startY) * progress;
            
            // 添加随机偏移（首尾点不偏移）
            let offsetX = 0, offsetY = 0;
            if (i > 0 && i < segments) {
                const maxOffset = type === 'main' ? 25 : (type === 'core' ? 15 : 20);
                offsetX = (Math.random() - 0.5) * maxOffset;
                offsetY = (Math.random() - 0.5) * maxOffset;
            }
            
            pathPoints.push({
                x: baseX + offsetX,
                y: baseY + offsetY
            });
        }
        
        // 创建SVG路径
        const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        svg.style.position = 'absolute';
        svg.style.top = '0';
        svg.style.left = '0';
        svg.style.width = '100%';
        svg.style.height = '100%';
        svg.style.pointerEvents = 'none';
        svg.style.zIndex = '200';

        let pathData = `M ${pathPoints[0].x} ${pathPoints[0].y}`;
        for (let i = 1; i < pathPoints.length; i++) {
            pathData += ` L ${pathPoints[i].x} ${pathPoints[i].y}`;
        }

        const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        path.setAttribute('d', pathData);
        path.setAttribute('fill', 'none');
        
        // 根据类型设置不同的样式 - 完全对标原版
        if (type === 'main') {
            path.setAttribute('stroke', '#ffffff');
            path.setAttribute('stroke-width', '3');
            path.style.filter = 'drop-shadow(0 0 15px rgba(150, 220, 255, 0.9)) drop-shadow(0 0 25px rgba(255, 255, 255, 0.6))';
        } else if (type === 'core') {
            path.setAttribute('stroke', '#ffffff');
            path.setAttribute('stroke-width', '1');
            path.style.filter = 'drop-shadow(0 0 8px rgba(255, 255, 255, 1)) drop-shadow(0 0 15px rgba(255, 255, 255, 0.8))';
        } else if (type === 'branch') {
            path.setAttribute('stroke', '#a0d8ff');
            path.setAttribute('stroke-width', '2');
            path.style.filter = 'drop-shadow(0 0 8px rgba(150, 220, 255, 0.7))';
        } else if (type === 'subbranch') {
            path.setAttribute('stroke', '#c0e8ff');
            path.setAttribute('stroke-width', '1');
            path.style.filter = 'drop-shadow(0 0 5px rgba(150, 220, 255, 0.5))';
        }
        
        path.style.opacity = '0';
        path.style.animation = `lightning-draw 0.1s ease-out forwards, lightning-intense-flicker 0.3s ease-out 0.1s forwards`;

        svg.appendChild(path);
        lightning.appendChild(svg);
        
        return lightning;
    }

    // 🔧 对标原版的createChargeLightning方法
    createChargeLightning(angle, radius, index) {
        const chargeLightning = document.createElement('div');
        chargeLightning.className = 'charge-lightning';
        
        const x = Math.cos(angle) * radius;
        const y = Math.sin(angle) * radius;
        
        chargeLightning.style.left = `${x}px`;
        chargeLightning.style.top = `${y}px`;
        chargeLightning.style.setProperty('--angle', `${angle}rad`);
        chargeLightning.style.animationDelay = `${index * 0.1}s`;
        
        return chargeLightning;
    }
}

/**
 * 雷剑技能
 * 结合飞剑术的剑刃攻击和掌心雷的雷电效果
 */
class LeiJianSkill extends BaseSkill {
    constructor(battleSystem) {
        super(battleSystem);
        this.skillName = '雷剑';
        this.elementType = 'lightning';
        
        // v2.0新增：技能实例管理
        this.animationContainers = new Set();
        this.activeTimers = new Set();
    }

    async execute(skillData, weaponImage) {
        try {

            
            console.log(`🌩️ 雷剑技能开始执行，技能数据:`, skillData, `武器图片:`, weaponImage);
            
            // 显示技能喊话
            const skillName = skillData?.skillName || skillData?.displayName || this.skillName;
            console.log(`🌩️ 显示技能喊话: ${skillName}`);
            await this.showSkillShout(skillName);
            console.log(`🌩️ 技能喊话显示完成`);
            
            // 执行雷剑动画
            console.log(`🌩️ 开始执行雷剑动画`);
            await this.createLeiJian(weaponImage);
            console.log(`🌩️ 雷剑动画执行完成`);
            
        } catch (error) {
            console.error(`❌ ${this.skillName} 执行失败:`, error);
            this.handleError(error, 'execute');
        }
    }
    
    async createLeiJian(weaponImage) {
        console.log(`🌩️ createLeiJian 开始，获取角色位置，isEnemySkill: ${this.isEnemySkill}`);
        // 🔧 修复：动态判断位置映射
        // 根据技能使用者动态确定施法者和目标位置
        let casterPos, targetPos;
        
        if (this.isEnemySkill) {
            // 敌方技能：敌人是施法者，玩家是目标
            casterPos = this.getCharacterPosition(false); // 敌人位置
            targetPos = this.getCharacterPosition(true);  // 玩家位置
        } else {
            // 我方技能：玩家是施法者，敌人是目标
            casterPos = this.getCharacterPosition(true);  // 玩家位置
            targetPos = this.getCharacterPosition(false); // 敌人位置
        }
        
        console.log(`🌩️ 雷剑位置计算 - 敌方技能:${this.isEnemySkill}, 施法者:`, casterPos, '目标:', targetPos);
        
        // 创建技能动画容器
        console.log(`🌩️ 创建动画容器`);
        const container = this.createElement('leijian-container', {
            style: {
                position: 'absolute',
                top: '0',
                left: '0',
                width: '100%',
                height: '100%',
                pointerEvents: 'none',
                zIndex: 1000
            }
        });
        
        this.animationContainers.add(container);
        this.effectsContainer.appendChild(container);
        console.log(`🌩️ 动画容器已添加到DOM，容器:`, container, `父元素:`, this.effectsContainer);
        
        try {
            // 第一阶段：雁形阵雷剑陆续生成 (1.2秒)
            console.log(`🌩️ 开始第一阶段：雁形阵雷剑陆续生成`);
            const formationSwords = await this.createLightningCharge(container, casterPos, weaponImage);
            console.log(`🌩️ 第一阶段完成`);
            
            // 第二阶段：雷剑飞行攻击，使用前导剑作为主剑
            console.log(`🌩️ 开始第二阶段：雷剑飞行攻击`);
            
            // 立即清除静态的雁形阵连接电弧
            const staticArcs = container.querySelector('.leijian-formation-arc');
            if (staticArcs) {
                this.safeRemoveElement(staticArcs);
                console.log(`⚡ 已清除静态雁形阵连接电弧`);
            }
            
            await this.launchFormationAttack(container, formationSwords, casterPos, targetPos);
            console.log(`🌩️ 第二阶段完成`);
            
            // 第三阶段：技能完成
            console.log(`🌩️ 雷剑穿透完成，技能结束`);
            
        } finally {
            // 安全清理
            console.log(`🌩️ 开始清理动画容器`);
            this.safeCleanupContainer(container);
            console.log(`🌩️ 动画容器清理完成`);
        }
    }
    
    // 第一阶段：雁形阵雷剑陆续生成
    async createLightningCharge(container, casterPos, weaponImage) {
        console.log(`⚡ 雁形阵雷剑陆续生成开始，施法者位置:`, casterPos, `武器图片:`, weaponImage);
        
        // 6把剑的雁形阵位置（以玩家前方为中心）
        const formationPositions = [
            { x: 0, y: -40 },      // 最前方（领头雁）
            { x: -30, y: -20 },    // 左翼前排
            { x: 30, y: -20 },     // 右翼前排  
            { x: -60, y: 0 },      // 左翼后排
            { x: 60, y: 0 },       // 右翼后排
            { x: 0, y: 20 }        // 最后方
        ];
        
        const formationSwords = [];
        
        // 创建6把雷剑
        for (let i = 0; i < 6; i++) {
            const pos = formationPositions[i];
            const swordX = casterPos.x + pos.x;
            const swordY = casterPos.y + pos.y;
            
            const formationSword = this.createElement('leijian-formation-sword', {
                style: {
                    position: 'absolute',
                    left: swordX + 'px',
                    top: swordY + 'px',
                    transform: `translate(-50%, -50%) rotate(${this.calculateInitialSwordRotation()}deg)`, // 动态调整剑朝向
                    width: '30px',
                    height: '60px',
                    '--centerX': casterPos.x + 'px',
                    '--centerY': casterPos.y + 'px',
                    '--index': i,
                    '--delay': `${i * 0.1}s`
                }
            });
            
            // 添加武器图片
            if (weaponImage) {
                const weaponImg = document.createElement('img');
                weaponImg.src = weaponImage;
                weaponImg.className = 'leijian-formation-weapon-image';
                weaponImg.style.width = '100%';
                weaponImg.style.height = '100%';
                weaponImg.style.objectFit = 'contain';
                weaponImg.style.filter = 'brightness(1.3) saturate(1.2)';
                weaponImg.style.transform = `rotate(${this.calculateInitialSwordRotation()}deg)`; // 动态调整武器图片角度
                
                weaponImg.onerror = () => {
                    console.warn('⚠️ 雁形阵武器图片加载失败，使用默认图片');
                    if (window.ImagePathManager) {
                        weaponImg.src = window.ImagePathManager.getWeaponImage('battle_sword.png');
                    } else {
                        weaponImg.src = 'assets/images/battle_sword.png';
                    }
                };
                
                formationSword.appendChild(weaponImg);
            } else {
                // 使用默认背景
                formationSword.style.backgroundImage = `url('assets/images/battle_sword.png')`;
                formationSword.style.backgroundSize = 'contain';
                formationSword.style.backgroundRepeat = 'no-repeat';
                formationSword.style.backgroundPosition = 'center';
            }
            
            // 添加雷电光晕
            formationSword.style.filter = 'drop-shadow(0 0 8px #4fc3f7) drop-shadow(0 0 16px #2196f3)';
            
            container.appendChild(formationSword);
            formationSwords.push(formationSword);
            
            console.log(`⚡ 第${i+1}把雁形阵雷剑已创建，位置: (${swordX}, ${swordY})`);
        }
        
        // 立即创建雁形阵电弧连接特效
        console.log(`⚡ 立即创建雁形阵电弧连接特效`);
        this.createFormationArcs(container, formationSwords);
        
        // 创建雷电蓄力特效
        const chargeCore = this.createElement('leijian-charge-core', {
            style: {
                position: 'absolute',
                left: casterPos.x + 'px',
                top: casterPos.y + 'px',
                transform: 'translate(-50%, -50%)'
            }
        });
        container.appendChild(chargeCore);
        
        console.log(`⚡ 等待1200毫秒雁形阵生成动画完成...`);
        await this.wait(1200);
        
        // 清理蓄力特效，但保留雁形阵小剑用于跟随飞行
        this.safeRemoveElement(chargeCore);
        console.log(`⚡ 雁形阵雷剑生成完成，返回${formationSwords.length}把小剑`);
        
        return formationSwords;
    }
    
    
    // 创建雷剑专用的闪电轨迹
    createLeiJianLightningTrail(container, startPos, endX, endY) {
        const deltaX = endX - startPos.x;
        const deltaY = endY - startPos.y;
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
        
        // 创建主闪电路径
        const mainLightning = this.createElement('leijian-trail-lightning', {
                style: {
                position: 'absolute',
                left: startPos.x + 'px',
                top: startPos.y + 'px',
                width: '4px',
                height: distance + 'px',
                transform: `translate(-50%, 0) rotate(${Math.atan2(deltaY, deltaX) * 180 / Math.PI + 90}deg)`,
                transformOrigin: 'top center'
                }
            });
        
        // 创建锯齿状路径
        const pathPoints = [];
        const segments = Math.floor(distance / 20); // 每20px一个转折点
        
        for (let i = 0; i <= segments; i++) {
            const progress = i / segments;
            const x = startPos.x + deltaX * progress;
            const y = startPos.y + deltaY * progress;
            
            // 添加随机偏移，除了起点和终点
            let offsetX = 0;
            let offsetY = 0;
            if (i > 0 && i < segments) {
                offsetX = (Math.random() - 0.5) * 20;
                offsetY = (Math.random() - 0.5) * 20;
            }
            
            pathPoints.push({
                x: x + offsetX,
                y: y + offsetY
            });
        }
        
        // 创建SVG路径
        const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        svg.style.position = 'absolute';
        svg.style.left = '0';
        svg.style.top = '0';
        svg.style.width = '100%';
        svg.style.height = '100%';
        svg.style.pointerEvents = 'none';
        svg.style.zIndex = '190';
        
        // 主闪电路径
        const mainPath = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        let pathData = `M ${pathPoints[0].x} ${pathPoints[0].y}`;
        for (let i = 1; i < pathPoints.length; i++) {
            pathData += ` L ${pathPoints[i].x} ${pathPoints[i].y}`;
        }
        
        mainPath.setAttribute('d', pathData);
        mainPath.setAttribute('stroke', '#ffffff');
        mainPath.setAttribute('stroke-width', '3');
        mainPath.setAttribute('fill', 'none');
        mainPath.setAttribute('filter', 'drop-shadow(0 0 8px #4fc3f7)');
        mainPath.style.animation = 'leijian-trail-flicker 0.2s ease-out forwards';
        
        // 内核闪电（更亮）
        const corePath = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        corePath.setAttribute('d', pathData);
        corePath.setAttribute('stroke', '#4fc3f7');
        corePath.setAttribute('stroke-width', '1');
        corePath.setAttribute('fill', 'none');
        corePath.style.animation = 'leijian-trail-core-flicker 0.15s ease-out forwards';
        
        svg.appendChild(mainPath);
        svg.appendChild(corePath);
        
        // 创建分支闪电
        for (let i = 1; i < pathPoints.length - 1; i++) {
            if (Math.random() < 0.4) { // 40%概率创建分支
                const branchPoint = pathPoints[i];
                const branchAngle = Math.random() * Math.PI * 2;
                const branchLength = 20 + Math.random() * 30;
                const branchEndX = branchPoint.x + Math.cos(branchAngle) * branchLength;
                const branchEndY = branchPoint.y + Math.sin(branchAngle) * branchLength;
                
                const branchPath = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                branchPath.setAttribute('d', `M ${branchPoint.x} ${branchPoint.y} L ${branchEndX} ${branchEndY}`);
                branchPath.setAttribute('stroke', '#2196f3');
                branchPath.setAttribute('stroke-width', '2');
                branchPath.setAttribute('fill', 'none');
                branchPath.setAttribute('opacity', '0.7');
                branchPath.style.animation = `leijian-trail-branch-flicker 0.1s ease-out forwards ${i * 0.01}s`;
                
                svg.appendChild(branchPath);
            }
        }
        
        container.appendChild(svg);
        console.log(`⚡ 雷剑闪电轨迹已创建，包含${pathPoints.length}个路径点`);
        
        return svg;
    }
    
    // 创建雷剑专用的敌人受击效果
    // 🗡️ 动态计算剑的初始旋转角度
    calculateInitialSwordRotation() {
        // 敌方技能：剑尖向下（指向玩家），我方技能：剑尖向上（指向敌人）
        return this.isEnemySkill ? 0 : 180;
    }

    createLeiJianEnemyHitEffect() {
        console.log(`⚡ 开始创建雷剑受击效果，isEnemySkill: ${this.isEnemySkill}`);
        
        // 🔧 修复：动态判断被攻击的目标
        const targetSelectors = [];
        if (!this.isEnemySkill) {
            // 我方技能攻击敌人
            targetSelectors.push('.enemy .character-sprite', '.enemy img', '.enemy .sprite', '.enemy .monster-sprite', '.enemy');
        } else {
            // 敌方技能攻击玩家  
            targetSelectors.push('.player .character-sprite', '.player img', '.player .sprite', '.player');
        }
        
        let targetSprite = null;
        for (const selector of targetSelectors) {
            targetSprite = document.querySelector(selector);
            if (targetSprite) {
                console.log(`⚡ 找到目标元素: ${selector}`);
                break;
            }
        }
        
        if (!targetSprite) {
            console.warn('⚠️ 未找到目标精灵元素，无法播放受击动画');
            return;
        }
        
        // 清除之前的动画
        targetSprite.style.animation = '';
        
        // 强制重排以确保动画重置
        targetSprite.offsetHeight;
        
        // 应用雷剑受击动画：雷电发光效果 + 快速震动
        targetSprite.style.animation = 'leijian-enemy-hit 0.8s ease-out, leijian-enemy-shake 0.06s ease-in-out 10';
        
        console.log(`⚡ 雷剑受击动画已应用到${!this.isEnemySkill ? '敌人' : '玩家'}`);
        
        // 1秒后清除动画
        setTimeout(() => {
            if (targetSprite) {
                targetSprite.style.animation = '';
                console.log(`⚡ 雷剑受击动画已清除`);
            }
        }, 1000);
    }
    
    // 雷剑专用：创建锯齿状闪电的方法（独立实现，不复用其他技能代码）
    createZigzagLightning(startX, startY, endX, endY, type) {
        const lightning = document.createElement('div');
        lightning.className = `zigzag-lightning lightning-${type}`;
        
        // 计算基本参数
        const totalDistance = Math.sqrt(Math.pow(endX - startX, 2) + Math.pow(endY - startY, 2));
        const segments = Math.floor(totalDistance / 15) + 3; // 根据距离调整段数
        
        // 生成锯齿路径
        let pathPoints = [];
        for (let i = 0; i <= segments; i++) {
            const progress = i / segments;
            const baseX = startX + (endX - startX) * progress;
            const baseY = startY + (endY - startY) * progress;
            
            // 添加随机偏移（首尾点不偏移）
            let offsetX = 0, offsetY = 0;
            if (i > 0 && i < segments) {
                const maxOffset = type === 'main' ? 25 : (type === 'core' ? 15 : 20);
                offsetX = (Math.random() - 0.5) * maxOffset;
                offsetY = (Math.random() - 0.5) * maxOffset;
            }
            
            pathPoints.push({
                x: baseX + offsetX,
                y: baseY + offsetY
            });
        }
        
        // 创建SVG路径
        const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        svg.style.position = 'absolute';
        svg.style.top = '0';
        svg.style.left = '0';
        svg.style.width = '100%';
        svg.style.height = '100%';
        svg.style.pointerEvents = 'none';
        svg.style.zIndex = '200';

        let pathData = `M ${pathPoints[0].x} ${pathPoints[0].y}`;
        for (let i = 1; i < pathPoints.length; i++) {
            pathData += ` L ${pathPoints[i].x} ${pathPoints[i].y}`;
        }

        const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        path.setAttribute('d', pathData);
        path.setAttribute('fill', 'none');
        
        // 根据类型设置不同的样式
        if (type === 'main') {
            path.setAttribute('stroke', '#ffffff');
            path.setAttribute('stroke-width', '3');
            path.style.filter = 'drop-shadow(0 0 15px rgba(150, 220, 255, 0.9)) drop-shadow(0 0 25px rgba(255, 255, 255, 0.6))';
        } else if (type === 'core') {
            path.setAttribute('stroke', '#ffffff');
            path.setAttribute('stroke-width', '1');
            path.style.filter = 'drop-shadow(0 0 8px rgba(255, 255, 255, 1)) drop-shadow(0 0 15px rgba(255, 255, 255, 0.8))';
        } else if (type === 'branch') {
            path.setAttribute('stroke', '#a0d8ff');
            path.setAttribute('stroke-width', '2');
            path.style.filter = 'drop-shadow(0 0 8px rgba(150, 220, 255, 0.7))';
        } else if (type === 'subbranch') {
            path.setAttribute('stroke', '#c0e8ff');
            path.setAttribute('stroke-width', '1');
            path.style.filter = 'drop-shadow(0 0 5px rgba(150, 220, 255, 0.5))';
        }
        
        path.style.opacity = '0';
        path.style.animation = `lightning-draw 0.1s ease-out forwards, lightning-intense-flicker 0.3s ease-out 0.1s forwards`;

        svg.appendChild(path);
        lightning.appendChild(svg);
        
        return lightning;
    }
    
    // 创建雁形阵电弧连接特效
    createFormationArcs(container, formationSwords) {
        if (!formationSwords || formationSwords.length === 0) {
            return null;
        }
        
        console.log(`⚡ 创建雁形阵闪电电弧连接特效，${formationSwords.length}把小剑`);
        
        const arcContainer = this.createElement('leijian-formation-arc', {
            style: {
                position: 'absolute',
                top: '0',
                left: '0',
                width: '100%',
                height: '100%',
                pointerEvents: 'none',
                zIndex: 198
            }
        });
        
        // 在相邻的小剑之间创建闪电电弧
        for (let i = 0; i < formationSwords.length; i++) {
            for (let j = i + 1; j < formationSwords.length; j++) {
                // 只在距离较近的剑之间创建电弧
                if (j - i <= 2) {
                    const sword1 = formationSwords[i];
                    const sword2 = formationSwords[j];
                    
                    // 获取剑的位置
                    const rect1 = sword1.getBoundingClientRect();
                    const rect2 = sword2.getBoundingClientRect();
                    const containerRect = container.getBoundingClientRect();
                    
                    const x1 = rect1.left + rect1.width / 2 - containerRect.left;
                    const y1 = rect1.top + rect1.height / 2 - containerRect.top;
                    const x2 = rect2.left + rect2.width / 2 - containerRect.left;
                    const y2 = rect2.top + rect2.height / 2 - containerRect.top;
                    
                    // 使用雷剑专用的锯齿闪电方法创建电弧
                    const lightning = this.createZigzagLightning(x1, y1, x2, y2, 'branch');
                    lightning.style.zIndex = '198';
                    lightning.style.animationDelay = `${(i + j) * 0.1}s`;
                    
                    arcContainer.appendChild(lightning);
                    
                    console.log(`⚡ 创建第${i+1}剑到第${j+1}剑的闪电电弧: (${x1},${y1}) -> (${x2},${y2})`);
                }
            }
        }
        
        container.appendChild(arcContainer);
        console.log(`⚡ 雁形阵闪电电弧特效已创建`);
        return arcContainer;
    }
    
    // 雁形阵集体攻击
    async launchFormationAttack(container, formationSwords, casterPos, targetPos) {
        if (!formationSwords || formationSwords.length === 0) {
            console.warn('⚠️ 没有雁形阵小剑，无法进行攻击');
            return;
        }
        
        console.log(`⚡ 雁形阵集体攻击开始，${formationSwords.length}把小剑`);
        
        // 计算穿透飞行的参数
        const deltaX = targetPos.x - casterPos.x;
        const deltaY = targetPos.y - casterPos.y;
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
        const penetrateDistance = 150;
        const totalDistance = distance + penetrateDistance;
        const penetrateX = casterPos.x + (deltaX / distance) * totalDistance;
        const penetrateY = casterPos.y + (deltaY / distance) * totalDistance;
        
        console.log(`⚡ 雁形阵穿透飞行计算:`, {
            起点: {x: casterPos.x, y: casterPos.y},
            击中点: {x: targetPos.x, y: targetPos.y},
            穿透终点: {x: penetrateX, y: penetrateY},
            距离: distance
        });
        
        // 创建主剑的闪电轨迹（使用第一把剑作为主剑）
        const leadSword = formationSwords[0];
        const lightningTrail = this.createLeiJianLightningTrail(container, casterPos, penetrateX, penetrateY);
        
        // 创建动态电弧连接系统，在飞行过程中保持连接
        const dynamicArcs = this.createDynamicFormationArcs(container, formationSwords);
        
        // 计算统一的飞行角度 - 动态调整剑朝向
        const angle = Math.atan2(deltaY, deltaX);
        let angleDeg = (angle * 180 / Math.PI) - 90; // 基础角度让剑尖指向目标
        
        // 🔧 动态判断：根据攻击方向调整剑尖朝向
        // 敌方技能时不需要额外调整，我方技能时也不需要额外调整
        // 角度计算已经能够正确处理双向攻击
        console.log(`⚡ 雷剑技能角度计算: ${angleDeg}度 (isEnemySkill: ${this.isEnemySkill})`);
        console.log(`⚡ 攻击向量: (${deltaX}, ${deltaY}), 距离: ${Math.sqrt(deltaX*deltaX + deltaY*deltaY).toFixed(2)}`);
        
        // 让所有小剑开始飞行，第一把剑作为主剑
        formationSwords.forEach((sword, index) => {
            // 为每把小剑设置跟随飞行属性
            sword.style.setProperty('--startX', sword.style.left);
            sword.style.setProperty('--startY', sword.style.top);
            sword.style.setProperty('--targetX', `${targetPos.x}px`);
            sword.style.setProperty('--targetY', `${targetPos.y}px`);
            sword.style.setProperty('--penetrateX', `${penetrateX}px`);
            sword.style.setProperty('--penetrateY', `${penetrateY}px`);
            sword.style.setProperty('--follow-delay', `${index * 0.05}s`); // 小剑之间有轻微延迟
            sword.style.setProperty('--angle', `${angleDeg}deg`); // 所有剑都设置相同角度，保持剑尖朝上
            
            // 添加跟随飞行动画类
            sword.classList.add('following');
            
            console.log(`⚡ 第${index+1}把小剑开始跟随飞行，角度${angleDeg}度，延迟${index * 0.05}s`);
        });
        
        console.log(`⚡ 雁形阵开始穿透飞行，等待600毫秒...`);
        
        // 在击中点创建击中效果和敌人受击动画
        setTimeout(() => {
            this.createHitEffect(targetPos.x, targetPos.y, true);
            this.createLeiJianEnemyHitEffect();
            console.log(`⚡ 雁形阵击中目标: (${targetPos.x}, ${targetPos.y})`);
        }, 240); // 飞行到目标点的时间（40%进度）
        
        await this.wait(600);
        
        // 清理闪电尾迹和雁形阵
        this.safeRemoveElement(lightningTrail);
        if (dynamicArcs) {
            this.safeRemoveElement(dynamicArcs);
        }
        formationSwords.forEach(sword => this.safeRemoveElement(sword));
        console.log(`⚡ 雁形阵穿透完成，所有元素已清理`);
    }
    
    // 创建动态电弧连接系统
    createDynamicFormationArcs(container, formationSwords) {
        if (!formationSwords || formationSwords.length === 0) {
            return null;
        }
        
        console.log(`⚡ 创建动态电弧连接系统，${formationSwords.length}把小剑`);
        
        const dynamicArcContainer = this.createElement('leijian-dynamic-arc', {
            style: {
                position: 'absolute',
                top: '0',
                left: '0',
                width: '100%',
                height: '100%',
                pointerEvents: 'none',
                zIndex: 199
            }
        });
        
        container.appendChild(dynamicArcContainer);
        
        // 创建定时器，定期更新电弧位置
        let frameCount = 0;
        const updateInterval = setInterval(() => {
            // 清除之前的电弧
            dynamicArcContainer.innerHTML = '';
        
            // 重新创建电弧连接
            for (let i = 0; i < formationSwords.length; i++) {
                for (let j = i + 1; j < formationSwords.length; j++) {
                    // 只在相邻的剑之间创建电弧
                    if (j - i <= 2) {
                        const sword1 = formationSwords[i];
                        const sword2 = formationSwords[j];
                        
                        try {
                            // 获取当前剑的位置
                            const rect1 = sword1.getBoundingClientRect();
                            const rect2 = sword2.getBoundingClientRect();
                            const containerRect = container.getBoundingClientRect();
                            
                            const x1 = rect1.left + rect1.width / 2 - containerRect.left;
                            const y1 = rect1.top + rect1.height / 2 - containerRect.top;
                            const x2 = rect2.left + rect2.width / 2 - containerRect.left;
                            const y2 = rect2.top + rect2.height / 2 - containerRect.top;
                            
                            // 创建连接电弧
                            const lightning = this.createZigzagLightning(x1, y1, x2, y2, 'subbranch');
                            lightning.style.zIndex = '199';
                            lightning.style.opacity = '0.8';
                            
                            dynamicArcContainer.appendChild(lightning);
                        } catch (error) {
                            // 忽略位置获取错误，剑可能已经飞出视野
                        }
                    }
                }
            }
            
            frameCount++;
            // 飞行600毫秒后停止更新
            if (frameCount >= 37) { // 约600ms的更新(按60fps计算)
                clearInterval(updateInterval);
            }
        }, 16); // 约60fps更新频率
        
        // 保存定时器以便清理
        this.activeTimers.add(updateInterval);
        
        console.log(`⚡ 动态电弧连接系统已创建`);
        return dynamicArcContainer;
    }

    // v2.0新增：安全清理方法
    safeRemoveElement(element) {
        if (element && element.parentNode) {
            try {
                element.parentNode.removeChild(element);
            } catch (error) {
                console.warn(`⚠️ 元素移除失败:`, error);
            }
        }
    }

    safeCleanupContainer(container) {
        if (container) {
            this.animationContainers.delete(container);
            
            const timer = setTimeout(() => {
                this.safeRemoveElement(container);
                this.activeTimers.delete(timer);
            }, 100);
            
            this.activeTimers.add(timer);
        }
    }

    // v2.0新增：技能实例清理方法
    cleanup() {
        this.animationContainers.forEach(container => {
            this.safeRemoveElement(container);
        });
        this.animationContainers.clear();

        this.activeTimers.forEach(timer => {
            // 清理定时器，可能是setTimeout或setInterval
            clearTimeout(timer);
            clearInterval(timer);
        });
        this.activeTimers.clear();

        console.log(`✅ ${this.skillName} 实例已清理`);
    }

    // v2.0新增：错误处理
    handleError(error, context) {
        const errorInfo = {
            skill: this.skillName,
            context: context,
            error: error.message,
            timestamp: Date.now()
        };

        if (window.BattleDebugPanel) {
            window.BattleDebugPanel.addLog('error', `${this.skillName} ${context} 失败`, errorInfo);
        }
    }
}

// 导出技能类（必须按此格式）
window.LightningSkills = window.LightningSkills || {};
window.LightningSkills.ZhangXinLeiSkill = ZhangXinLeiSkill;
window.LightningSkills.LeiJianSkill = LeiJianSkill; 