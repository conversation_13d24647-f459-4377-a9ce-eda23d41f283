<?php
/**
 * 用户认证相关函数
 */

/**
 * 检查用户是否已登录
 * @return int|false 返回用户ID或false
 */
function check_auth() {
    // 🔧 修复：使用统一的会话管理函数
    if (session_status() === PHP_SESSION_NONE) {
        session_set_cookie_params([
            'lifetime' => 0,
            'path' => '/yinian/',
            'domain' => '',
            'secure' => false,
            'httponly' => true,
            'samesite' => 'Lax'
        ]);
        session_start();
    }

    if (!isset($_SESSION['user_id']) || !isset($_SESSION['character_id'])) {
        return false;
    }
    
    // 验证会话是否有效
    $user_id = $_SESSION['user_id'];
    $character_id = $_SESSION['character_id'];
    
    if (!is_numeric($user_id) || !is_numeric($character_id)) {
        return false;
    }
    
    return (int)$user_id;
}

/**
 * 获取当前角色ID
 * @return int|false 返回角色ID或false
 */
function get_character_id() {
    // 🔧 修复：使用统一的会话管理函数
    if (session_status() === PHP_SESSION_NONE) {
        session_set_cookie_params([
            'lifetime' => 0,
            'path' => '/yinian/',
            'domain' => '',
            'secure' => false,
            'httponly' => true,
            'samesite' => 'Lax'
        ]);
        session_start();
    }

    if (!isset($_SESSION['character_id'])) {
        return false;
    }
    
    return (int)$_SESSION['character_id'];
}

/**
 * 设置用户会话
 * @param int $user_id 用户ID
 * @param int $character_id 角色ID
 */
function set_user_session($user_id, $character_id) {
    // 🔧 修复：避免重复启动session
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    $_SESSION['user_id'] = $user_id;
    $_SESSION['character_id'] = $character_id;
}

/**
 * 清除用户会话
 */
function clear_user_session() {
    // 🔧 修复：避免重复启动session
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    session_unset();
    session_destroy();
}
?> 