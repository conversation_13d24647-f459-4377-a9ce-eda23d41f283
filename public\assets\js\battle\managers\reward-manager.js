/**
 * 🎁 奖励管理器
 * 负责管理战斗奖励、掉落物品处理、胜利结果保存等
 * 从script.js分拆出奖励相关的方法
 */

class BattleRewardManager {
    constructor(battleSystem) {
        this.battleSystem = battleSystem;
        console.log('🎁 奖励管理器已创建');
    }

    /**
     * 设置数据管理器引用
     */
    setDataManager(dataManager) {
        this.dataManager = dataManager;
        console.log('🔗 奖励管理器已关联数据管理器');
    }

    /**
     * 设置UI管理器引用
     */
    setUIManager(uiManager) {
        this.uiManager = uiManager;
        console.log('🔗 奖励管理器已关联UI管理器');
    }

    /**
     * 保存战斗胜利结果
     */
    async saveVictoryResult(droppedItems) {
        // 🔧 防重复提交
        if (this.isSaving) {
            console.log('⚠️ 正在保存中，忽略重复调用');
            return { success: false, message: '正在保存中，请稍候' };
        }
        
        this.isSaving = true;
        
        try {
            const requestId = 'frontend_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            console.log(`=== 请求ID: ${requestId} - 开始保存战斗结果 ===`);
            console.log(`请求ID: ${requestId} - 掉落物品数据:`, droppedItems);
            console.log(`请求ID: ${requestId} - 调用堆栈:`, new Error().stack);
            
            // 🆕 检查是否为竞技场战斗
            const urlParams = new URLSearchParams(window.location.search);
            const isArenaBattle = urlParams.get('arena') === '1';
            
            if (isArenaBattle) {
                // 🏆 竞技场专用结算
                return await this.saveArenaResult();
            }
            
            // 🔧 新增：计算战斗时间
            this.battleSystem.battleEndTime = Date.now();
            const battleDuration = this.battleSystem.battleStartTime ? 
                Math.round((this.battleSystem.battleEndTime - this.battleSystem.battleStartTime) / 1000) : 30; // 默认30秒
            
            console.log('战斗用时:', battleDuration, '秒');
            
            const currentArea = this.dataManager.getCurrentArea();
            const enemyData = this.dataManager.enemyData;
            
            console.log('当前区域:', currentArea);
            console.log('敌人数据:', enemyData);
            
            // 🔧 修复：确保droppedItems格式正确
            const itemsData = droppedItems.map(item => {
                const itemData = {
                    name: item.name || '未知物品',
                    type: item.type || 'material',
                    rarity: item.rarity || item.quality || 'common', // 🔧 修复：优先使用rarity字段
                    quantity: parseInt(item.quantity) || 1,
                    sell_price: parseInt(item.sell_price) || 0,
                    description: item.description || '暂无描述', // 🔧 修复：添加缺失的描述字段
                    item_data: item.item_data || {}
                };
                console.log('处理物品数据:', itemData);
                return itemData;
            });
            
            // 🔐 安全修复：奖励值仅用于前端显示，不发送到后端
            // 后端会从数据库重新计算真实奖励值，防止前端篡改
            const spiritStoneGained = enemyData.spiritStoneReward || 10;
            const goldGained = enemyData.goldReward || 5;
            
            console.log('🔐 前端显示奖励（仅用于UI展示）: 灵石=', spiritStoneGained, '金币=', goldGained);
            console.log('🔐 注意：后端会从数据库重新计算真实奖励，不信任前端传值');
            
            // 🔧 修复：使用FormData确保参数正确传递
            const postData = new FormData();
            postData.append('action', 'save_victory_result');
            postData.append('frontend_request_id', requestId); // 🔧 新增：前端请求ID
            postData.append('map_code', currentArea.areaId);
            
            // 🔧 修复：只有失败标识时才不更新进度，正常挂机要推进进度
            // 🆕 检查是否有挂机失败标识
            const hasFailedFlag = this.battleSystem.autoBattleManager && 
                                   this.battleSystem.autoBattleManager.hasAutoBattleFailedFlag();
            
            // 🔧 简化逻辑：挂机模式始终在当前层循环，不推进进度
            if (this.battleSystem.isAutoBattleMode) {
                console.log('🤖 挂机模式：循环当前关，不推进进度');
                postData.append('auto_battle_mode', 'true');
                postData.append('current_stage', this.dataManager.currentStage);
            } else {
                // 🆕 修改：正常战斗胜利也不自动更新进度，等待用户点击下一层
                console.log('🎮 正常胜利模式：保存奖励但不更新地图进度');
                postData.append('normal_victory_mode', 'true');
                postData.append('current_stage', this.dataManager.currentStage);
            }
            
            postData.append('dropped_items', JSON.stringify(itemsData));
            
            // 🔐 安全修复：不再发送奖励值到后端，后端会从数据库重新计算
            // postData.append('exp_gained', expGained);  // 🚫 已移除
            // postData.append('gold_gained', goldGained); // 🚫 已移除
            
            // 🔧 新增：发送战斗时间用于验证
            postData.append('battle_duration', battleDuration);
            
            // 🔥 新增：发送战斗回合数用于验证
            const actualRounds = Math.floor((this.battleSystem.attackCount || 0) / 2) + 1;
            postData.append('battle_rounds', actualRounds);
            console.log(`🔍 历练模式计算回合数: attackCount=${this.battleSystem.attackCount}, rounds=${actualRounds}`);
            
            console.log('发送到API的数据:');
            console.log('- action:', 'save_victory_result');
            console.log('- map_code:', currentArea.areaId);
            
            if (hasFailedFlag) {
                console.log('- auto_battle_mode:', 'true');
                console.log('- failed_hangup_mode:', 'true');
                console.log('- current_stage:', this.dataManager.currentStage, '(失败标识存在，循环当前关，不更新进度)');
            } else {
                console.log('- completed_stage:', this.dataManager.currentStage, '(完成的关卡，会更新进度)');
                if (this.battleSystem.isAutoBattleMode) {
                    console.log('- normal_hangup_mode:', 'true', '(正常挂机，会推进进度)');
                }
            }
            
            console.log('- dropped_items:', JSON.stringify(itemsData));
            console.log('🔐 奖励值不再发送到后端，由后端从数据库重新计算');
            console.log('- battle_duration:', battleDuration, '秒');
            
            // 🔧 修复：使用相对于battle.html的正确路径
            const apiUrl = window.GameConfig ? window.GameConfig.getApiUrl('battle_drops_unified.php') : '../../../src/api/battle_drops_unified.php';
            console.log('🌐 API调用URL:', apiUrl);

            const response = await fetch(apiUrl, {
                method: 'POST',
                body: postData
            });
            
            console.log('📡 HTTP响应状态:', response.status, response.statusText);
            console.log('📡 响应URL:', response.url);
            console.log('📡 响应头:', Object.fromEntries(response.headers.entries()));
            
            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
            }
            
            // 🔧 新增：先获取原始文本，再尝试解析JSON
            const responseText = await response.text();
            console.log('📄 原始响应文本:', responseText);
            console.log('📄 响应文本长度:', responseText.length);
            console.log('📄 响应文本前100字符:', responseText.substring(0, 100));
            
            // 🔧 新增：检查响应是否为HTML错误页面
            if (responseText.trim().startsWith('<')) {
                console.error('❌ API返回HTML页面而不是JSON:', responseText);
                throw new Error('API返回HTML错误页面，可能是PHP错误或服务器配置问题');
            }
            
            let data;
            try {
                data = JSON.parse(responseText);
                console.log('✅ JSON解析成功:', data);
            } catch (jsonError) {
                // 🔧 修复：屏蔽JSON解析错误的控制台输出，避免用户看到错误信息
                if (window.BattleDebugConfig && window.BattleDebugConfig.log) {
                    window.BattleDebugConfig.log('reward-manager', '❌ JSON解析失败: ' + jsonError.message);
                    window.BattleDebugConfig.log('reward-manager', '❌ 原始响应: ' + responseText);
                }
                
                // 🔧 临时修复：尝试处理双重JSON的情况
                if (responseText.includes('} {')) {
                    if (window.BattleDebugConfig && window.BattleDebugConfig.log) {
                        window.BattleDebugConfig.log('reward-manager', '🔧 检测到双重JSON，尝试提取第二个JSON');
                    }
                    const jsonParts = responseText.split('} {');
                    if (jsonParts.length >= 2) {
                        const secondJson = '{' + jsonParts[1];
                        try {
                            data = JSON.parse(secondJson);
                            if (window.BattleDebugConfig && window.BattleDebugConfig.log) {
                                window.BattleDebugConfig.log('reward-manager', '✅ 成功解析第二个JSON: ' + JSON.stringify(data));
                            }
                        } catch (secondJsonError) {
                            if (window.BattleDebugConfig && window.BattleDebugConfig.log) {
                                window.BattleDebugConfig.log('reward-manager', '❌ 第二个JSON也解析失败: ' + secondJsonError.message);
                            }
                            throw new Error(`JSON解析失败: ${jsonError.message}. 原始响应: ${responseText.substring(0, 200)}...`);
                        }
                    } else {
                        throw new Error(`JSON解析失败: ${jsonError.message}. 原始响应: ${responseText.substring(0, 200)}...`);
                    }
                } else {
                    throw new Error(`JSON解析失败: ${jsonError.message}. 原始响应: ${responseText.substring(0, 200)}...`);
                }
            }
            
            if (data.success) {
                console.log('✅ 保存战斗结果成功');
            } else {
                console.error('❌ API返回失败:', data.message);
                
                // 返回失败状态
                return {
                    success: false,
                    message: data.message || '保存战斗结果失败'
                };
            }
            
            return data;
            
        } catch (error) {
            console.error('💥 保存战斗结果时出错:', error);
            console.error('错误堆栈:', error.stack);
            return { success: false, message: '网络错误: ' + error.message };
        } finally {
            // 🔧 重置保存状态
            this.isSaving = false;
        }
    }

    /**
     * 🔧 新增：创建奖励区域（备用方案）
     */
    createRewardsSection(gameOverlay, spiritStoneGained, goldGained) {
        try {
            console.log('🎁 手动创建奖励区域:', { spiritStoneGained, goldGained });
            console.log('🔍 lastSaveResult完整内容:', this.battleSystem.lastSaveResult);
            
            // 🔧 新增：确保回收警告动画CSS存在
            if (!document.querySelector('#recycle-warning-styles')) {
                const style = document.createElement('style');
                style.id = 'recycle-warning-styles';
                style.textContent = `
                    @keyframes warningPulse {
                        0%, 100% {
                            opacity: 1;
                            transform: scale(1);
                        }
                        50% {
                            opacity: 0.8;
                            transform: scale(1.02);
                        }
                    }
                `;
                document.head.appendChild(style);
                console.log('✅ 回收警告动画CSS已添加');
            }
            
            // 🔧 强制调试：无论如何都要输出回收信息检查过程
            console.log('🔍🔍🔍 createRewardsSection 强制回收调试 🔍🔍🔍');
            console.log('this.battleSystem.lastSaveResult存在吗?', !!this.battleSystem.lastSaveResult);
            if (this.battleSystem.lastSaveResult) {
                console.log('lastSaveResult.has_recycled_items:', this.battleSystem.lastSaveResult.has_recycled_items);
                console.log('lastSaveResult.recycled_items:', this.battleSystem.lastSaveResult.recycled_items);
                console.log('lastSaveResult.recycle_gold:', this.battleSystem.lastSaveResult.recycle_gold);
                console.log('lastSaveResult所有字段:', Object.keys(this.battleSystem.lastSaveResult));
            }
            console.log('🔍🔍🔍 createRewardsSection 强制调试结束 🔍🔍🔍');
            
            // 检查是否有回收信息
            const hasRecycledItems = this.battleSystem.lastSaveResult && this.battleSystem.lastSaveResult.has_recycled_items;
            const recycleGold = this.battleSystem.lastSaveResult ? (this.battleSystem.lastSaveResult.recycle_gold || 0) : 0;
            const recycledItems = this.battleSystem.lastSaveResult ? (this.battleSystem.lastSaveResult.recycled_items || []) : [];
            const totalGoldGained = this.battleSystem.lastSaveResult ? (this.battleSystem.lastSaveResult.total_gold_gained || this.battleSystem.lastSaveResult.gold_gained || goldGained) : goldGained;
            
            console.log('🔍 回收信息详细检查:', {
                'this.battleSystem.lastSaveResult存在': !!this.battleSystem.lastSaveResult,
                'has_recycled_items原始值': this.battleSystem.lastSaveResult ? this.battleSystem.lastSaveResult.has_recycled_items : 'N/A',
                'has_recycled_items类型': this.battleSystem.lastSaveResult ? typeof this.battleSystem.lastSaveResult.has_recycled_items : 'N/A',
                'hasRecycledItems最终值': hasRecycledItems,
                'recycleGold': recycleGold,
                'recycledItems': recycledItems,
                'recycledItemsCount': recycledItems.length,
                'totalGoldGained': totalGoldGained,
                'originalGoldGained': goldGained
            });
            
            const rewardsSection = document.createElement('div');
            rewardsSection.className = 'rewards-section';
            rewardsSection.innerHTML = `
                <div class="rewards-title" style="
                    font-weight: bold;
                    font-size: 14px;
                    color: #ffd700;
                    margin-bottom: 10px;
                    text-shadow: 0 0 8px rgba(255, 215, 0, 0.6);
                    text-align: center;
                ">🎁 战斗奖励</div>
                
                ${hasRecycledItems && recycleGold > 0 ? `
                <div class="recycle-warning" style="
                    display: block;
                    background: rgba(220, 20, 60, 0.2);
                    border: 1px solid rgba(220, 20, 60, 0.5);
                    border-radius: 6px;
                    padding: 8px;
                    margin-bottom: 10px;
                    text-align: center;
                    color: #ff6b6b;
                    font-size: 12px;
                    font-weight: bold;
                    text-shadow: 0 0 4px rgba(220, 20, 60, 0.8);
                    animation: warningPulse 2s ease-in-out infinite;
                ">
                    ⚠️ 背包空间不足，已自动回收 ${recycledItems.length} 件装备获得 ${recycleGold} 金币
                </div>
                ` : ''}
                
                <div class="rewards-grid" style="
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 8px;
                ">
                    <div class="reward-item" style="
                        border-radius: 8px;
                        padding: 10px;
                        border: 2px solid rgba(138, 43, 226, 0.6);
                        background: linear-gradient(135deg, rgba(138, 43, 226, 0.3), rgba(138, 43, 226, 0.1));
                        text-align: center;
                        transition: all 0.3s ease;
                    ">
                        <div class="reward-label" style="
                            color: #d4af37;
                            font-size: 11px;
                            margin-bottom: 4px;
                            font-weight: bold;
                        ">🔹 灵石</div>
                        <div class="reward-value" style="
                            color: #ffd700;
                            font-size: 18px;
                            font-weight: bold;
                            text-shadow: 0 0 8px rgba(255, 215, 0, 0.8);
                        ">+${spiritStoneGained}</div>
                    </div>
                    <div class="reward-item" style="
                        border-radius: 8px;
                        padding: 10px;
                        border: 2px solid rgba(255, 215, 0, 0.6);
                        background: linear-gradient(135deg, rgba(255, 215, 0, 0.3), rgba(255, 215, 0, 0.1));
                        text-align: center;
                        transition: all 0.3s ease;
                    ">
                        <div class="reward-label" style="
                            color: #d4af37;
                            font-size: 11px;
                            margin-bottom: 4px;
                            font-weight: bold;
                        ">💰 金币</div>
                        <div class="reward-value" style="
                            color: #ffd700;
                            font-size: 18px;
                            font-weight: bold;
                            text-shadow: 0 0 8px rgba(255, 215, 0, 0.8);
                        ">+${totalGoldGained}${hasRecycledItems && recycleGold > 0 ? ` (含回收 ${recycleGold})` : ''}</div>
                    </div>
                </div>
            `;
            
            // 插入到胜利面板中
            const victoryContent = gameOverlay.querySelector('.victory-content');
            if (victoryContent) {
                victoryContent.appendChild(rewardsSection);
                console.log('✅ 手动创建的奖励区域已添加', hasRecycledItems ? '(包含回收提示)' : '');
            }
            
            // 🔧 修复：显示回收警告（如果有回收物品）
            if (hasRecycledItems && recycleGold > 0) {
                console.log('🚨 检测到回收物品，显示红色警告提示');
                const recycleWarning = document.createElement('div');
                recycleWarning.className = 'recycle-warning';
                recycleWarning.style.cssText = `
                    color: #ff4444;
                    background: rgba(255, 68, 68, 0.1);
                    border: 1px solid #ff4444;
                    border-radius: 4px;
                    padding: 8px;
                    margin: 10px 0;
                    text-align: center;
                    font-size: 14px;
                    text-shadow: 0 0 4px rgba(255, 68, 68, 0.5);
                `;
                recycleWarning.innerHTML = `⚠️ 背包空间不足，已自动回收 ${recycledItems.length} 件装备获得 ${recycleGold} 金币`;
                
                // 插入到奖励区域的开头
                const rewardsContainer = gameOverlay.querySelector('[data-rewards-section]') || gameOverlay.querySelector('.rewards-section');
                if (rewardsContainer) {
                    rewardsContainer.insertBefore(recycleWarning, rewardsContainer.firstChild);
                    console.log('✅ 回收警告已插入到奖励区域');
                } else {
                    console.error('❌ 找不到奖励区域容器，无法插入回收警告');
                }
            } else {
                console.log('ℹ️ 没有回收物品，不显示回收警告');
                console.log('  - hasRecycledItems:', hasRecycledItems);
                console.log('  - recycleGold:', recycleGold);
            }
        } catch (error) {
            console.error('创建奖励区域失败:', error);
        }
    }

    /**
     * 🔧 修复：从掉落数据构造物品详情，优先从API获取真实数据
     */
    async constructItemDetailFromDrop(dropData) {
        try {
            console.log('🔧 构造掉落物品详情:', dropData);
            
            // 🔧 安全验证：确保dropData存在且是对象
            if (!dropData || typeof dropData !== 'object') {
                console.error('❌ 无效的掉落数据:', dropData);
                throw new Error('掉落数据格式错误');
            }
            
            // 🔧 尝试从API获取真实的物品数据
            let realItemData = null;
            
            if (dropData.id && dropData.id > 0) {
                try {
                    console.log('🌐 尝试从API获取物品真实数据, ID:', dropData.id);
                    
                    const apiUrl = window.GameConfig ? window.GameConfig.getApiUrl(`equipment_integrated.php?action=get_item_detail&item_id=${dropData.id}`) : `../../../src/api/equipment_integrated.php?action=get_item_detail&item_id=${dropData.id}`;
                    const response = await fetch(apiUrl);
                    if (response.ok) {
                        const result = await response.json();
                        if (result.success && result.item) {
                            realItemData = result.item;
                            console.log('✅ 从API获取到真实物品数据:', realItemData);
                        } else {
                            console.warn('⚠️ API返回失败或无数据:', result.message);
                        }
                    } else {
                        console.warn('⚠️ API请求失败:', response.status, response.statusText);
                    }
                } catch (apiError) {
                    console.warn('⚠️ API调用出错:', apiError.message);
                }
            }
            
            // 🔧 如果API获取失败，尝试通过物品名称查询
            if (!realItemData && dropData.name) {
                try {
                    console.log('🌐 尝试通过物品名称查询, 名称:', dropData.name);
                    
                    const apiUrl = window.GameConfig ? window.GameConfig.getApiUrl(`equipment_integrated.php?action=get_item_detail_by_name&item_name=${encodeURIComponent(dropData.name)}`) : `../../../src/api/equipment_integrated.php?action=get_item_detail_by_name&item_name=${encodeURIComponent(dropData.name)}`;
                    const response = await fetch(apiUrl);
                    if (response.ok) {
                        const result = await response.json();
                        if (result.success && result.item) {
                            realItemData = result.item;
                            console.log('✅ 通过名称获取到真实物品数据:', realItemData);
                        } else {
                            console.warn('⚠️ 通过名称查询失败:', result.message);
                        }
                    }
                } catch (nameApiError) {
                    console.warn('⚠️ 通过名称查询API出错:', nameApiError.message);
                }
            }
            
            // 🔧 根据是否获取到真实数据选择处理方式
            let baseItemData;
            if (realItemData) {
                console.log('🎯 使用API获取的真实物品数据作为基础');
                baseItemData = realItemData;
                
                // 🔧 用掉落数据覆盖一些字段
                baseItemData.quantity = this.safeParseInt(dropData.quantity) || 1;
                baseItemData.rarity = dropData.rarity || dropData.quality || baseItemData.rarity;
                baseItemData.description = dropData.description || baseItemData.description;
                
                // 🔧 如果掉落数据有特殊说明，添加到描述中
                if (dropData.recycled) {
                    baseItemData.description += ' (背包空间不足，已自动回收)';
                }
                
            } else {
                console.log('🔧 API获取失败，使用掉落数据和智能推断');
                baseItemData = dropData;
            }
            
            // 🔧 应用品质倍率到属性（如果没有从API获取到数据）
            if (!realItemData) {
                const rarityMultiplier = this.getRarityMultiplier(baseItemData.rarity || 'common');
                console.log('🎨 应用品质倍率:', rarityMultiplier, '到品质:', baseItemData.rarity);
                
                // 🔧 智能推断基础属性（如果缺失）
                this.applyIntelligentAttributes(baseItemData, rarityMultiplier);
            }
            
            // 🔧 构造最终的物品详情数据
            const itemDetail = {
                id: this.safeParseInt(baseItemData.id) || 0,
                name: baseItemData.name || '未知物品',
                description: baseItemData.description || '暂无描述',
                rarity: baseItemData.rarity || 'common',
                level_requirement: this.safeParseInt(baseItemData.level_requirement) || this.safeParseInt(baseItemData.realm_requirement) || 1,
                realm_requirement: this.safeParseInt(baseItemData.realm_requirement) || this.safeParseInt(baseItemData.level_requirement) || 1,
                slot_type: baseItemData.slot_type || baseItemData.type || 'material',
                item_type: baseItemData.item_type || baseItemData.type || 'material',
                
                // 🔧 属性数据（优先使用API数据）
                physical_attack: this.safeParseInt(baseItemData.physical_attack) || 0,
                immortal_attack: this.safeParseInt(baseItemData.immortal_attack) || 0,
                physical_defense: this.safeParseInt(baseItemData.physical_defense) || 0,
                immortal_defense: this.safeParseInt(baseItemData.immortal_defense) || 0,

                hp_bonus: this.safeParseInt(baseItemData.hp_bonus) || this.safeParseInt(baseItemData.hp) || 0,
                mp_bonus: this.safeParseInt(baseItemData.mp_bonus) || this.safeParseInt(baseItemData.mp) || 0,
                speed_bonus: this.safeParseInt(baseItemData.speed_bonus) || 0,
                critical_bonus: this.safeParseFloat(baseItemData.critical_bonus) || 0, // 🔧 修复：统一使用critical_bonus
                critical_damage: this.safeParseFloat(baseItemData.critical_damage) || 0,
                accuracy_bonus: this.safeParseFloat(baseItemData.accuracy_bonus) || this.safeParseFloat(baseItemData.accuracy) || 0,
                dodge_bonus: this.safeParseFloat(baseItemData.dodge_bonus) || this.safeParseFloat(baseItemData.dodge) || 0,
                block_bonus: this.safeParseFloat(baseItemData.block_bonus) || this.safeParseFloat(baseItemData.block) || 0,
                
                // 🔧 保留掉落时的重要信息
                quantity: this.safeParseInt(dropData.quantity) || 1,
                sell_price: this.safeParseInt(baseItemData.sell_price) || this.safeParseInt(dropData.sell_price) || 1,
                icon_image: baseItemData.icon || baseItemData.icon_image || dropData.icon || null,
                special_effects: baseItemData.special_effects || dropData.special_effects || null,
                
                // 标记信息
                bind_status: 0,
                is_equipped: false,
                
                // 🔧 新增：标记数据来源
                dataSource: realItemData ? 'api' : 'constructed',
                originalDropData: dropData // 保留原始掉落数据
            };
            
            console.log('🎯 最终构造的物品详情:', itemDetail);
            console.log('📊 数据来源:', itemDetail.dataSource);
            
            return itemDetail;
            
        } catch (error) {
            console.error('❌ 构造物品详情失败:', error);
            console.error('❌ 出错时的dropData:', dropData);
            
            // 🔧 返回一个最基础的物品详情，确保不会完全失败
            return {
                id: 0,
                name: (dropData && dropData.name) || '未知物品',
                description: '数据格式错误，无法解析物品属性',
                rarity: 'common',
                level_requirement: 1,
                realm_requirement: 1,
                slot_type: 'material',
                item_type: 'material',
                physical_attack: 0,
                immortal_attack: 0,
                physical_defense: 0,
                immortal_defense: 0,
                hp_bonus: 0,
                mp_bonus: 0,
                speed_bonus: 0,
                critical_bonus: 0, // 🔧 修复：统一使用critical_bonus
                critical_damage: 0,
                accuracy_bonus: 0,
                dodge_bonus: 0,
                block_bonus: 0,
                quantity: 1,
                sell_price: 1,
                icon_image: null,
                special_effects: '物品数据解析失败',
                bind_status: 0,
                is_equipped: false,
                dataSource: 'fallback',
                originalDropData: dropData
            };
        }
    }
    
    /**
     * 🔧 新增：智能推断物品属性
     */
    applyIntelligentAttributes(itemData, rarityMultiplier) {
        try {
            const weaponName = itemData.name || '';
            const itemType = itemData.type || itemData.slot_type || itemData.item_type;
            
            // 🔧 如果是武器且缺少属性数据
            if ((itemType === 'weapon' || weaponName.includes('剑') || weaponName.includes('扇')) && 
                !itemData.physical_attack && !itemData.immortal_attack) {
                
                console.log('🔧 武器缺少属性数据，使用智能推断');
                
                if (weaponName.includes('剑') || weaponName.includes('刀') || weaponName.includes('枪') || weaponName.includes('斧')) {
                    // 物理攻击武器
                    itemData.physical_attack = Math.floor((20 + Math.random() * 30) * rarityMultiplier);

                    console.log('🗡️ 推断为物理武器，物理攻击:', itemData.physical_attack);
                } else if (weaponName.includes('法杖') || weaponName.includes('扇') || weaponName.includes('书') || weaponName.includes('珠')) {
                    // 法术攻击武器
                    itemData.immortal_attack = Math.floor((20 + Math.random() * 30) * rarityMultiplier);

                    console.log('🔮 推断为法术武器，法术攻击:', itemData.immortal_attack);
                } else {
                    // 混合攻击武器
                    itemData.physical_attack = Math.floor((15 + Math.random() * 20) * rarityMultiplier);
                    itemData.immortal_attack = Math.floor((15 + Math.random() * 20) * rarityMultiplier);

                    console.log('⚔️ 推断为混合武器，物理攻击:', itemData.physical_attack, '法术攻击:', itemData.immortal_attack);
                }
            }
            
            // 🔧 如果是防具且缺少属性数据
            if ((itemType === 'armor' || itemType === 'chest' || itemType === 'legs' || itemType === 'feet') &&
                !itemData.physical_defense && !itemData.immortal_defense) {
                
                console.log('🔧 防具缺少属性数据，使用智能推断');
                itemData.physical_defense = Math.floor((10 + Math.random() * 20) * rarityMultiplier);
                itemData.immortal_defense = Math.floor((10 + Math.random() * 20) * rarityMultiplier);

                console.log('🛡️ 推断防具属性 - 物理防御:', itemData.physical_defense, '法术防御:', itemData.immortal_defense);
            }
            
            // 🔧 如果是饰品且缺少属性数据
            if ((itemType === 'accessory' || itemType === 'ring' || itemType === 'necklace' || itemType === 'bracelet') &&
                !itemData.hp_bonus && !itemData.mp_bonus) {
                
                console.log('🔧 饰品缺少属性数据，使用智能推断');
                itemData.hp_bonus = Math.floor((5 + Math.random() * 15) * rarityMultiplier);
                itemData.mp_bonus = Math.floor((5 + Math.random() * 15) * rarityMultiplier);
                console.log('💎 推断饰品属性 - HP加成:', itemData.hp_bonus, 'MP加成:', itemData.mp_bonus);
            }
            
        } catch (error) {
            console.error('❌ 智能推断属性时出错:', error);
        }
    }

    /**
     * 🔧 新增：安全的整数解析方法
     */
    safeParseInt(value) {
        if (value === null || value === undefined || value === '') {
            return 0;
        }
        const parsed = parseInt(value);
        return isNaN(parsed) ? 0 : parsed;
    }
    
    /**
     * 🔧 新增：安全的浮点数解析方法
     */
    safeParseFloat(value) {
        if (value === null || value === undefined || value === '') {
            return 0;
        }
        const parsed = parseFloat(value);
        return isNaN(parsed) ? 0 : parsed;
    }
    
    /**
     * 🔧 新增：根据品质获取属性倍率
     */
    getRarityMultiplier(rarity) {
        const multipliers = {
            'common': 1.0,      // 普通 100%
            'uncommon': 1.3,    // 稀有 130%
            'rare': 1.6,        // 史诗 160%
            'epic': 2.0,        // 传说 200%
            'legendary': 2.5    // 神话 250%
        };
        
        // 支持中文品质
        const chineseMapping = {
            '普通': 'common',
            '稀有': 'uncommon',
            '史诗': 'rare',
            '传说': 'epic',
            '神话': 'legendary'
        };
        
        const englishRarity = chineseMapping[rarity] || rarity;
        return multipliers[englishRarity] || 1.0;
    }

    /**
     * 🔧 新增：为已回收物品添加遮罩层
     */
    addRecycledOverlay() {
        try {
            const itemPopup = document.getElementById('item-detail-popup');
            if (!itemPopup) {
                console.error('❌ 未找到物品详情弹窗，无法添加回收遮罩');
                return;
            }
            
            // 检查是否已经有遮罩层
            if (itemPopup.querySelector('.recycled-overlay')) {
                console.log('ℹ️ 回收遮罩已存在，跳过添加');
                return;
            }
            
            // 创建遮罩层
            const overlay = document.createElement('div');
            overlay.className = 'recycled-overlay';
            overlay.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.7);
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                z-index: 1000;
                border-radius: 8px;
            `;
            
            // 创建垃圾桶图标
            const trashIcon = document.createElement('div');
            trashIcon.innerHTML = '🗑️';
            trashIcon.style.cssText = `
                font-size: 48px;
                margin-bottom: 10px;
                animation: rotate 2s linear infinite;
            `;
            
            // 创建提示文字
            const message = document.createElement('div');
            message.textContent = '物品已被回收';
            message.style.cssText = `
                color: #ff6b6b;
                font-size: 16px;
                font-weight: bold;
                text-shadow: 0 0 8px rgba(255, 107, 107, 0.8);
                text-align: center;
            `;
            
            // 添加旋转动画
            if (!document.querySelector('#recycled-overlay-styles')) {
                const style = document.createElement('style');
                style.id = 'recycled-overlay-styles';
                style.textContent = `
                    @keyframes rotate {
                        from { transform: rotate(0deg); }
                        to { transform: rotate(360deg); }
                    }
                `;
                document.head.appendChild(style);
            }
            
            overlay.appendChild(trashIcon);
            overlay.appendChild(message);
            
            // 添加到弹窗中
            itemPopup.appendChild(overlay);
            
            // 模糊底层内容
            const itemContent = itemPopup.querySelector('.item-detail-content') || 
                              itemPopup.querySelector('.popup-content') ||
                              itemPopup.querySelector('.modal-content');
            if (itemContent) {
                itemContent.style.filter = 'blur(3px)';
                itemContent.style.pointerEvents = 'none';
                console.log('✅ 已模糊底层内容并禁用交互');
            }
            
            console.log('✅ 回收遮罩添加成功');
            
        } catch (error) {
            console.error('❌ 添加回收遮罩失败:', error);
        }
    }
    
    /**
     * 🏆 竞技场专用结算
     */
    async saveArenaResult() {
        try {
            console.log('🏆 开始竞技场专用结算');
            
            // 获取战斗参数
            const urlParams = new URLSearchParams(window.location.search);
            const opponentId = urlParams.get('opponent_id');
            const isAiPuppet = urlParams.get('is_ai') === 'true';
            
            // 计算战斗时间和回合数
            const battleEndTime = Date.now();
            const battleStartTime = this.battleSystem.battleStartTime || battleEndTime - 30000;
            const battleDuration = Math.round((battleEndTime - battleStartTime) / 1000);
            
            // 🔥 新增：计算实际的战斗回合数（每2次攻击为1回合）
            const actualRounds = Math.floor((this.battleSystem.attackCount || 0) / 2) + 1;
            console.log(`🔍 计算回合数: attackCount=${this.battleSystem.attackCount}, rounds=${actualRounds}`);
            
            // 🔧 修复：正确获取战斗结果，使用战斗系统的最终状态
            const battleSystem = this.battleSystem || window.battleSystem;
            const dataManager = this.dataManager || battleSystem?.dataManager;
            
            let isVictory = true; // 默认胜利
            let battleData = {
                duration: battleDuration,
                rounds: actualRounds, // 🔥 使用实际回合数
                player_final_hp: 100,
                enemy_final_hp: 0
            };
            
            if (!dataManager) {
                console.error('❌ 数据管理器未找到，检查battleSystem状态');
                
                // 🔧 尝试从battleSystem获取战斗结果
                if (battleSystem && battleSystem.battleEnded && battleSystem.isPlayerVictory !== undefined) {
                    isVictory = battleSystem.isPlayerVictory;
                    console.log('🔍 从battleSystem获取战斗结果:', isVictory ? '胜利' : '失败');
            } else {
                    console.error('❌ 无法获取战斗结果，使用默认胜利');
                }
            } else {
                // 🔧 修复：首先尝试从battleSystem获取战斗结果
                if (battleSystem && battleSystem.isPlayerVictory !== undefined) {
                    isVictory = battleSystem.isPlayerVictory;
                    console.log('🔍 ✅ 从battleSystem获取准确的战斗结果:', isVictory ? '胜利' : '失败');
                } else {
                    // 🔧 备用方案：从dataManager获取HP数据判断
                    const player = dataManager.player || {};
                    const enemy = dataManager.enemy || {};
                
                    console.log('🔍 完整玩家数据:', player);
                    console.log('🔍 完整敌人数据:', enemy);
                    
                    // 🔧 尝试从character对象获取实时HP
                    let playerHp = 100;
                    let enemyHp = 0;
                
                    // 从DOM或battleSystem获取实时HP
                    const playerCharacter = battleSystem?.playerCharacter;
                    const enemyCharacter = battleSystem?.enemyCharacter;
                    
                    if (playerCharacter && playerCharacter.currentHp !== undefined) {
                        playerHp = playerCharacter.currentHp;
                        console.log('🔍 从playerCharacter获取玩家HP:', playerHp);
                    } else if (player.currentHp !== undefined) {
                        playerHp = player.currentHp;
                    } else if (player.hp_bonus !== undefined) {
                        playerHp = player.hp_bonus;
                    } else {
                        playerHp = 100; // 默认认为玩家存活
                    }
                    
                    if (enemyCharacter && enemyCharacter.currentHp !== undefined) {
                        enemyHp = enemyCharacter.currentHp;
                        console.log('🔍 从enemyCharacter获取敌人HP:', enemyHp);
                    } else if (enemy.currentHp !== undefined) {
                        enemyHp = enemy.currentHp;
                    } else if (enemy.hp !== undefined) {
                        enemyHp = enemy.hp;
                    } else {
                        enemyHp = 0; // 默认认为敌人死亡
                    }
                    
                    // 胜负判断逻辑
                isVictory = playerHp > 0 && enemyHp <= 0;
                    
                    console.log('🔍 HP详细获取过程:');
                    console.log(`  玩家HP: ${playerHp} (来源: ${playerCharacter ? 'playerCharacter' : 'dataManager'})`);
                    console.log(`  敌人HP: ${enemyHp} (来源: ${enemyCharacter ? 'enemyCharacter' : 'dataManager'})`);
                    console.log(`  胜负判定: 玩家HP(${playerHp}) > 0 && 敌人HP(${enemyHp}) <= 0 = ${isVictory}`);
                }
                
                // 构建战斗数据
                battleData = {
                    duration: battleDuration,
                    rounds: actualRounds, // 🔥 使用实际回合数
                    player_final_hp: 100,
                    enemy_final_hp: 0
                };
                
                console.log('🔍 最终战斗数据:', battleData);
                console.log('🔍 最终战斗结果:', isVictory ? '胜利' : '失败');
            }
            
            // battleData 已在上面定义
            
            // 🔧 修复：获取对手名字
            const opponentName = urlParams.get('opponent_name') || '';

            // 调用竞技场专用API
            const formData = new FormData();
            formData.append('action', 'submit_arena_result');
            formData.append('battle_result', isVictory ? 'win' : 'lose');
            formData.append('opponent_id', opponentId || '');
            formData.append('opponent_name', opponentName); // 🔧 修复：传递对手名字
            formData.append('is_ai_puppet', (isAiPuppet || opponentId.includes('ai_')) ? 'true' : 'false');
            formData.append('battle_data', JSON.stringify(battleData));
            
            console.log('🏆 发送竞技场结算数据:', {
                battle_result: isVictory ? 'win' : 'lose',
                opponent_id: opponentId,
                is_ai_puppet: isAiPuppet,
                battle_data: battleData
            });
            
            const apiUrl = window.GameConfig ? window.GameConfig.getApiUrl('immortal_arena.php') : '../../../src/api/immortal_arena.php';
            const response = await fetch(apiUrl, {
                method: 'POST',
                body: formData,
                credentials: 'same-origin' // 🔧 修复：确保发送Cookie和会话信息
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const result = await response.json();
            console.log('🏆 竞技场结算结果:', result);
            
            if (result.success) {
                // 保存结果给胜利面板使用
                this.battleSystem.lastSaveResult = result;
                
                // 设置竞技场专用的返回格式
                return {
                    success: true,
                    is_arena_battle: true,
                    battle_result: result.battle_result,
                    rewards: result.rewards,
                    arena_stats: result.arena_stats,
                    message: result.message,
                    // 兼容现有系统的字段
                    exp_gained: 0, // 竞技场不给经验
                    gold_gained: 0, // 竞技场不给金币
                    spiritual_power_gained: 0, // 竞技场不给修为
                    spirit_stones_gained: result.rewards.spirit_stones || 0
                };
            } else {
                throw new Error(result.message || '竞技场结算失败');
            }
            
        } catch (error) {
            console.error('❌ 竞技场结算失败:', error);
            return {
                success: false,
                message: '竞技场结算失败: ' + error.message,
                is_arena_battle: true,
                // 提供默认奖励避免界面错误
                rewards: {
                    spirit_stones: 0,
                    rank_points: 0,
                    is_ai_opponent: false
                }
            };
        }
    }
} 