/**
 * 🎮 战斗流程管理器
 * 负责管理战斗的主要流程逻辑
 * 从script.js分拆出战斗流程相关的方法
 */

class BattleFlowManager {
    constructor(battleSystem) {
        this.battleSystem = battleSystem;
        console.log('🔄 战斗流程管理器已创建');
    }

    /**
     * 设置数据管理器引用
     */
    setDataManager(dataManager) {
        this.dataManager = dataManager;
        console.log('🔗 战斗流程管理器已关联数据管理器');
    }

    /**
     * 设置UI管理器引用
     */
    setUIManager(uiManager) {
        this.uiManager = uiManager;
        console.log('🔗 战斗流程管理器已关联UI管理器');
    }

    /**
     * 自动战斗主逻辑
     */
    async autoBattle() {
        if (this.battleSystem.isGameOver) return;

        // 🔥 新增：检查回合数限制（最多30回合）
        const maxRounds = 30;
        const currentRound = Math.floor(this.battleSystem.attackCount / 2) + 1; // 每2次攻击为1回合（玩家+敌人各1次）

        if (currentRound > maxRounds) {
            console.log(`⏰ 战斗已达到最大回合数限制：${maxRounds}回合，挑战者失败`);

            // 🏆 根据不同模式显示不同的失败消息
            const urlParams = new URLSearchParams(window.location.search);
            const isArenaMode = urlParams.get('arena') === '1';

            if (isArenaMode) {
                await this.battleSystem.gameOver('论道失败！战斗超时！');
            } else {
                await this.battleSystem.gameOver('挑战失败！战斗超时！');
            }
            return;
        }

        // 显示当前回合信息
        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log(
                'battle-flow',
                `⏰ 当前回合: ${currentRound}/${maxRounds}`
            );
        }

        // 🔥 新增：更新界面上的回合数显示
        if (this.uiManager && this.uiManager.updateRoundDisplay) {
            this.uiManager.updateRoundDisplay(currentRound, maxRounds);
        }

        // 🔥 新增：20回合暴走机制检查
        if (currentRound >= 20 && !this.battleSystem.enemyBerserkMode) {
            this.battleSystem.enemyBerserkMode = true;
            console.log('🔥 怪物进入暴走状态！20回合到达，每击必暴击必命中！');

            // 显示暴走状态提示
            window.battleReportManager?.showReport('敌方陷入暴走状态！', 'status', 0);

            // 🔥 在敌人身上添加暴走特效
            this.showBerserkEffect();
        }

        // 按6个槽位循环
        const currentSlotIndex = this.battleSystem.attackCount % 6;
        const currentSkillData = this.battleSystem.skillSequence[currentSlotIndex];

        // === 新增：统一损坏武器判定，所有表现都用finalSkillData ===
        const weaponStatus = this.checkWeaponStatus(currentSkillData);
        const finalSkillData = this.adjustSkillDataByWeaponStatus(currentSkillData, weaponStatus);

        console.log('=== 技能调用调试 ===');
        console.log('当前轮次:', this.battleSystem.attackCount);
        console.log('当前槽位索引:', currentSlotIndex);
        console.log('当前技能数据:', finalSkillData);
        console.log('技能名称:', finalSkillData.skillName);
        console.log('是否有武器:', finalSkillData.hasWeapon);
        console.log('武器名称:', finalSkillData.weaponName);

        // 更新当前技能显示
        this.uiManager.updateCurrentSkill(finalSkillData.skillName);
        // 高亮当前使用的武器槽位
        this.uiManager.highlightCurrentWeapon(currentSlotIndex);
        // 显示技能信息
        let skillInfo = finalSkillData.skillName || '未知技能';
        if (finalSkillData.hasWeapon) {
            skillInfo += ` (${finalSkillData.weaponName})`;
        }
        console.log('准备执行技能:', finalSkillData.skillName);
        console.log('动画类型:', finalSkillData.animationType);

        // 🔧 修复：用于动画执行的技能名称（基于animation_model）
        const animationType = finalSkillData.animationType || finalSkillData.skillName || 'feijian';
        console.log('🔍 技能映射调试:');
        console.log('  - finalSkillData.animationType:', finalSkillData.animationType);
        console.log('  - finalSkillData.skillName:', finalSkillData.skillName);
        console.log('  - 使用的animationType:', animationType);

        // 获取武器图片
        const weaponImage = this.getCurrentWeaponImage(finalSkillData);

        // 🔧 技能动画名称：用于动画系统识别，不用于喊话
        let animationSkillName = '剑气外放！'; // 默认技能
        if (window.SkillConfig) {
            animationSkillName = window.SkillConfig.getSkillNameByAnimationModel(animationType);
        } else {
            console.warn('⚠️ 统一配置不可用，使用默认技能');
            animationSkillName = '剑气外放！';
        }
        console.log('  - 动画技能名称:', animationSkillName);
        // 🔥 新增：MP消耗检查和扣除（在所有技能执行前统一处理）
        const canUse = this.dataManager.canUseSkill(finalSkillData, this.battleSystem.playerStats);
        if (!canUse.canUse) {
            this.uiManager.showBattleMessage(canUse.message || '技能使用失败', 'error');
            console.log(
                '技能执行完成，攻击计数:',
                this.battleSystem.attackCount,
                '->',
                this.battleSystem.attackCount + 1
            );
            this.battleSystem.attackCount++;
            if (!this.battleSystem.isGameOver) {
                this.battleSystem.battleTimeout = setTimeout(() => this.autoBattle(), 800);
            }
            return;
        }
        // 扣除MP
        const mpResult = this.dataManager.consumeSkillMp(
            finalSkillData,
            this.battleSystem.playerStats,
            this.battleSystem.player
        );
        if (mpResult.success) {
            console.log(`🔋 ${finalSkillData.skillName}消耗MP: ${finalSkillData.mp_cost || 0}点`);
            if (finalSkillData.mp_cost > 0) {
                window.battleReportManager?.showSkillReport(
                    finalSkillData.skillName,
                    finalSkillData.mp_cost,
                    'player'
                );
            }
        }
        // 记录技能使用时间（用于冷却）
        this.dataManager.recordSkillUsage(finalSkillData);
        // 🔥 新增：使用技能加载器执行技能动画
        if (window.skillLoader) {
            try {
                console.log(
                    `🎯 使用技能加载器执行动画: ${animationSkillName} (技能: ${finalSkillData.skillName})`
                );

                // 🔧 修复：确保传递正确的技能名称给动画系统
                const enhancedSkillData = {
                    ...finalSkillData,
                    displayName: finalSkillData.skillName, // 确保使用真实技能名称
                };

                await window.skillLoader.executeSkill(
                    animationSkillName,
                    enhancedSkillData,
                    weaponImage
                );
                // 统一的战斗计算和伤害处理
                const isDead = await this.performSkillDamageCalculation(finalSkillData);
                if (isDead) {
                    return;
                }
            } catch (error) {
                console.error(`❌ 技能执行失败: ${finalSkillData.skillName}`, error);
                this.uiManager.showBattleMessage(
                    `技能${finalSkillData.skillName}执行失败，跳过本回合`,
                    'error'
                );
                const isDead = await this.performSkillDamageCalculation(finalSkillData);
                if (isDead) {
                    return;
                }
            }
        } else {
            console.warn('⚠️ 技能加载器不可用');
            this.uiManager.showBattleMessage('技能系统未就绪，使用基础攻击', 'warning');
            const isDead = await this.performSkillDamageCalculation(finalSkillData);
            if (isDead) {
                return;
            }
        }
        if (!this.battleSystem.isGameOver) {
            await this.enemyCounter();
        }
        if (!this.battleSystem.isGameOver) {
            await this.performAutoRegeneration();
        }
        console.log(
            '技能执行完成，攻击计数:',
            this.battleSystem.attackCount,
            '->',
            this.battleSystem.attackCount + 1
        );
        this.battleSystem.attackCount++;
        if (!this.battleSystem.isGameOver) {
            this.battleSystem.battleTimeout = setTimeout(() => this.autoBattle(), 800);
        }
    }

    /**
     * 敌人反击逻辑 - 竞技场优化版本
     */
    async enemyCounter() {
        await new Promise(resolve => setTimeout(resolve, 800));

        // 🏆 检查是否为竞技场模式
        const urlParams = new URLSearchParams(window.location.search);
        const isArenaMode = urlParams.get('arena') === '1';

        if (isArenaMode) {
            // 🏆 竞技场模式：直接使用玩家技能序列，不调用AI
            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.log(
                    'battle-flow',
                    '🏆 竞技场模式：敌人直接使用玩家技能序列'
                );
            }

            await this.executeArenaPlayerAttack();
            return;
        }

        // 🐺 历练模式：使用AI决策系统
        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('battle-flow', '=== 怪物AI决策开始 ===');
        }

        // 调用AI决策API
        let aiDecision = null;
        try {
            const battleState = {
                monster: {
                    hp: this.battleSystem.enemy.currentHp || this.battleSystem.enemy.hp,
                    max_hp: this.battleSystem.enemy.max_hp || this.battleSystem.enemy.hp,
                    mp: this.battleSystem.enemy.currentMp || this.battleSystem.enemy.mp,
                    max_mp: this.battleSystem.enemy.max_mp || this.battleSystem.enemy.mp,
                    attack: this.battleSystem.enemy.attack,
                    defense: this.battleSystem.enemy.defense,
                    skills: this.dataManager.enemyData?.skills || ['普通攻击'],
                    type: this.dataManager.enemyData?.type || 'normal',
                },
                player: {
                    hp: this.battleSystem.player.currentHp,
                    hp_bonus: this.battleSystem.player.hp_bonus,
                    mp:
                        this.battleSystem.playerStats.currentMp ||
                        this.battleSystem.playerStats.mp_bonus,
                    mp_bonus: this.battleSystem.playerStats.mp_bonus,
                    attack: this.battleSystem.playerStats.physical_attack,
                    defense: this.battleSystem.playerStats.physical_defense,
                },
            };

            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.log(
                    'battle-flow',
                    '发送AI决策请求，战斗状态:',
                    battleState
                );
            }

            const apiUrl = window.GameConfig
                ? window.GameConfig.getApiUrl('monster_ai_decision_balanced.php')
                : '../../../src/api/monster_ai_decision_balanced.php';
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json; charset=utf-8',
                },
                body: JSON.stringify({
                    ai_pattern:
                        this.dataManager.enemyData?.ai_pattern ||
                        this.dataManager.enemyData?.aiPattern ||
                        'balanced',
                }),
            });

            if (response.ok) {
                const result = await response.json();
                if (window.BattleDebugConfig) {
                    window.BattleDebugConfig.log('battle-flow', 'AI决策结果:', result);
                }

                if (result.success) {
                    aiDecision = result.decision;
                    if (window.BattleDebugConfig) {
                        window.BattleDebugConfig.log('battle-flow', '✅ AI决策成功:', aiDecision);
                    }
                }
            } else {
                if (window.BattleDebugConfig) {
                    window.BattleDebugConfig.log(
                        'battle-flow',
                        '❌ AI决策API请求失败:',
                        response.status
                    );
                }
            }
        } catch (error) {
            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.log('battle-flow', '❌ AI决策API调用异常:', error);
            }
        }

        // 如果AI决策失败，使用备用逻辑
        if (!aiDecision) {
            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.log('battle-flow', '🔄 使用备用AI决策逻辑');
            }
            aiDecision = this.getBackupAIDecision();
        }

        // 根据AI决策执行相应行为
        await this.executeAIAction(aiDecision);
    }

    /**
     * 🏆 竞技场玩家攻击（不使用AI决策）
     */
    async executeArenaPlayerAttack() {
        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('battle-flow', '🏆 执行竞技场玩家攻击');
        }

        // 获取对手下一个技能
        const skillData = this.selectPlayerSkillInSequence();
        let skillName = '';

        if (skillData && typeof skillData === 'object') {
            skillName = skillData.skill_name;
            console.log('🏆 [竞技场] 对手使用完整技能数据:', skillData);
        } else {
            skillName = skillData; // 兼容旧版本返回的字符串
            console.log('🏆 [竞技场] 对手使用技能名称:', skillName);
        }

        // 强制修正敌人数据为玩家类型
        if (this.battleSystem.enemy) {
            this.battleSystem.enemy.isPlayer = true;
            this.battleSystem.enemy.type = 'arena_player';
        }
        if (this.battleSystem.dataManager?.enemyData) {
            this.battleSystem.dataManager.enemyData.isPlayer = true;
            this.battleSystem.dataManager.enemyData.type = 'arena_player';
        }

        // 不再显示攻击开始的无关消息

        // 执行对手技能动画
        await this.executeEnemySkillAnimation(skillName);

        // 计算攻击伤害
        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('battle-flow', '=== 竞技场对手攻击战斗计算 ===');
        }

        const attackerData = {
            ...this.battleSystem.enemy,
            accuracy_bonus: parseFloat(this.battleSystem.enemy.accuracy_bonus) || 85,
            critical_bonus: parseFloat(this.battleSystem.enemy.critical_bonus) || 5,
            isEnemy: true, // 🔥 新增：标识这是敌人
        };

        const defenderData = {
            ...this.battleSystem.playerStats,
            dodge_bonus: parseFloat(this.battleSystem.playerStats.dodge_bonus) || 5,
            critical_resistance: parseFloat(this.battleSystem.playerStats.critical_resistance) || 0,
        };

        const baseAttack = this.battleSystem.enemy.attack || 15;
        const enemyAttackPower = Math.round(baseAttack * 1.0); // 竞技场固定1.0倍数

        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log(
                'battle-flow',
                `对手攻击力: ${baseAttack} × 1.0 = ${enemyAttackPower}`
            );
        }

        const battleResult = this.dataManager.calculateFinalDamage(
            enemyAttackPower,
            this.battleSystem.player.defense,
            attackerData,
            defenderData,
            {
                weaponType: 'sword',
                skillType: 'physical',
                skillData: {
                    hasWeapon: true,
                    weaponType: 'sword',
                    skillName: skillName,
                    damageMultiplier: 1.0,
                },
            }
        );

        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('battle-flow', '对手攻击战斗结果:', battleResult);
        }

        // 🔧 新增：应用敌人的攻击套装特殊效果
        let finalDamage = battleResult;
        if (this.battleSystem.applyEnemySetAttackEffects) {
            const enemyAttackEffectResult =
                this.battleSystem.applyEnemySetAttackEffects(finalDamage);
            finalDamage = enemyAttackEffectResult.damage;

            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.log(
                    'battle-flow',
                    '敌人套装攻击效果:',
                    enemyAttackEffectResult
                );
            }
        }

        // 🔧 新增：应用玩家的防御套装特殊效果
        if (this.battleSystem.applySetDefenseEffects) {
            const defenseEffectResult = this.battleSystem.applySetDefenseEffects(finalDamage);
            finalDamage = defenseEffectResult.damage;

            // 处理反击效果
            if (defenseEffectResult.counterDamage > 0) {
                this.battleSystem.enemy.takeDamage(defenseEffectResult.counterDamage);
                this.battleSystem.showSetEffectMessage(
                    `反击造成${defenseEffectResult.counterDamage}点伤害！`
                );
            }

            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.log(
                    'battle-flow',
                    '玩家套装防御效果:',
                    defenseEffectResult
                );
            }
        }

        // 对玩家造成伤害
        const isDead = await this.battleSystem.player.takeDamage(finalDamage);
        if (isDead) {
            await this.battleSystem.gameOver('战斗失败！论道落败！');
            return;
        }
    }

    /**
     * 备用AI决策逻辑
     */
    getBackupAIDecision() {
        // 简单的备用AI逻辑
        const rand = Math.random();

        if (rand < 0.8) {
            // 80%普通攻击
            return {
                action: 'attack',
                skill: '普通攻击',
                damage_modifier: 1.0,
                animation: 'attack',
                message: '发动攻击',
            };
        } else {
            // 20%防御
            return {
                action: 'defend',
                skill: null,
                damage_modifier: 0.5,
                animation: 'defend',
                message: '进入防御姿态',
            };
        }
    }

    /**
     * 执行AI行为
     */
    async executeAIAction(aiDecision) {
        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('battle-flow', '=== 执行AI行为 ===');
            window.BattleDebugConfig.log('battle-flow', 'AI决策:', aiDecision);
        }

        switch (aiDecision.action) {
            case 'attack':
            case 'skill':
                await this.executeAIAttack(aiDecision);
                break;
            case 'defend':
                await this.executeAIDefend(aiDecision);
                break;
            case 'combo':
                await this.executeAICombo(aiDecision);
                break;
            default:
                if (window.BattleDebugConfig) {
                    window.BattleDebugConfig.log('battle-flow', '未知的AI行为:', aiDecision.action);
                }
                await this.executeAIAttack(aiDecision);
                break;
        }
    }

    /**
     * 执行AI攻击行为
     */
    async executeAIAttack(aiDecision) {
        let skillName = aiDecision.skill || '普通攻击';

        // 🏆 检查是否为竞技场模式
        const urlParams = new URLSearchParams(window.location.search);
        const isArenaMode = urlParams.get('arena') === '1';

        if (isArenaMode) {
            // 🏆 竞技场模式：强制使用玩家技能序列，不管isPlayer标识
            const skillData = this.selectPlayerSkillInSequence();
            if (skillData && typeof skillData === 'object') {
                skillName = skillData.skill_name;
                console.log('🏆 [竞技场] 对手使用完整技能数据:', skillData);
            } else {
                skillName = skillData; // 兼容旧版本返回的字符串
                console.log('🏆 [竞技场] 对手使用技能名称:', skillName);
            }
            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.log(
                    'battle-flow',
                    `🏆 竞技场模式：强制使用玩家技能序列: ${skillName}`
                );
            }

            // 🔧 强制修正敌人数据为玩家类型，确保技能动画系统识别
            if (this.battleSystem.enemy) {
                this.battleSystem.enemy.isPlayer = true;
                this.battleSystem.enemy.type = 'arena_player';
            }
            if (this.battleSystem.dataManager?.enemyData) {
                this.battleSystem.dataManager.enemyData.isPlayer = true;
                this.battleSystem.dataManager.enemyData.type = 'arena_player';
            }
        } else {
            // 🐺 历练模式：使用怪物技能
            console.log('🐺 [历练] 怪物使用默认技能:', skillName);
        }

        // 不再显示攻击开始的无关消息

        // 🔧 执行怪物技能动画
        await this.executeEnemySkillAnimation(skillName);

        // 🔧 计算攻击伤害
        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('battle-flow', '=== 敌人攻击战斗计算 ===');
        }

        const attackerData = {
            ...this.battleSystem.enemy,
            accuracy_bonus: parseFloat(this.battleSystem.enemy.accuracy_bonus) || 85, // 🔧 修复：使用数据库字段名
            critical_bonus: parseFloat(this.battleSystem.enemy.critical_bonus) || 5, // 🔧 修复：统一使用critical_bonus
            isEnemy: true, // 🔥 新增：标识这是敌人
        };

        const defenderData = {
            ...this.battleSystem.playerStats,
            dodge_bonus: parseFloat(this.battleSystem.playerStats.dodge_bonus) || 5, // 🔧 修复：使用数据库字段名
            critical_resistance: parseFloat(this.battleSystem.playerStats.critical_resistance) || 0, // 🔧 修复：使用数据库字段名
        };

        const baseAttack = this.battleSystem.enemy.attack || 15;
        const enemyAttackPower = Math.round(baseAttack * (aiDecision.damage_modifier || 1.0));
        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log(
                'battle-flow',
                `敌人攻击力: ${baseAttack} × ${aiDecision.damage_modifier} = ${enemyAttackPower}`
            );
        }

        const battleResult = this.dataManager.calculateFinalDamage(
            enemyAttackPower,
            this.battleSystem.player.defense,
            attackerData,
            defenderData,
            {
                weaponType: 'sword',
                skillType: 'physical',
                skillData: {
                    hasWeapon: true,
                    weaponType: 'sword',
                    skillName: skillName,
                    damageMultiplier: aiDecision.damage_modifier || 1.0,
                },
            }
        );

        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('battle-flow', '敌人攻击战斗结果:', battleResult);
        }

        // 🔧 检查玩家是否处于防御状态（如果有的话）
        let finalDamage = battleResult.damage;
        if (this.battleSystem && this.battleSystem.player && this.battleSystem.player.isDefending) {
            const defenseModifier = this.battleSystem.player.defenseModifier || 0.8;
            finalDamage = Math.round(finalDamage * defenseModifier);
            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.log(
                    'battle-flow',
                    `🛡️ 玩家防御状态，伤害: ${battleResult.damage} -> ${finalDamage}`
                );
            }

            // 清除防御状态
            this.battleSystem.player.isDefending = false;
            this.battleSystem.player.defenseModifier = 1.0;
        }

        // 使用修正后的伤害
        const modifiedBattleResult = { ...battleResult, damage: finalDamage };

        const isDead = await this.battleSystem.player.takeDamage(modifiedBattleResult);
        if (isDead) {
            // 🏆 根据模式显示不同的失败消息
            const urlParams = new URLSearchParams(window.location.search);
            const isArenaMode = urlParams.get('arena') === '1';

            if (isArenaMode) {
                await this.battleSystem.gameOver('战斗失败！论道落败！');
            } else {
                await this.battleSystem.gameOver('战斗失败！退回上一层！');
            }
            return;
        }
    }

    /**
     * 🏆 竞技场玩家技能序列选择
     */
    selectPlayerSkillInSequence() {
        if (!window.enemySkillIndex) {
            window.enemySkillIndex = 0;
        }

        // 🔧 修复：优先从dataManager获取敌人数据，然后才是battleSystem
        let enemyData = null;
        if (this.battleSystem?.dataManager?.enemyData) {
            enemyData = this.battleSystem.dataManager.enemyData;
        } else if (this.battleSystem?.enemy) {
            enemyData = this.battleSystem.enemy;
        } else if (this.dataManager?.enemyData) {
            enemyData = this.dataManager.enemyData;
        }

        const skillSequence = enemyData?.skill_sequence || [0, 1, 2, 3, 4, 5];
        const weaponSkills = enemyData?.weapon_skills || enemyData?.skills || ['普通攻击'];

        // 🎯 关键修复：获取完整的武器技能数据（包含animation_model）
        const weaponSkillsData = enemyData?.weapon_skills_data || [];

        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('battle-flow', '🎯 技能序列数据:', {
                skillSequence,
                weaponSkills,
                weaponSkillsData,
                currentIndex: window.enemySkillIndex,
                敌人完整数据: enemyData,
            });
        }

        // 按序列获取技能索引
        const sequenceIndex = window.enemySkillIndex % skillSequence.length;
        const skillIndex = skillSequence[sequenceIndex];

        // 🎯 关键修复：优先获取完整技能数据，兜底使用技能名称
        let selectedSkillData = null;
        if (
            weaponSkillsData &&
            weaponSkillsData.length > skillIndex &&
            weaponSkillsData[skillIndex]
        ) {
            selectedSkillData = weaponSkillsData[skillIndex];

            // 🔧 修复：如果是feijian技能，强制设置正确的显示名称
            if (
                selectedSkillData.skill_name === 'feijian' ||
                selectedSkillData.animation_model === 'feijian'
            ) {
                selectedSkillData = {
                    ...selectedSkillData,
                    skill_name: '剑气外放！',
                    display_name: '剑气外放！',
                    animation_model: 'feijian',
                };
            }
        } else {
            // 兜底方案：使用技能名称
            const skillName = weaponSkills[skillIndex] || weaponSkills[0] || '普通攻击';
            selectedSkillData = {
                skill_name: skillName === 'feijian' ? '剑气外放！' : skillName,
                display_name: skillName === 'feijian' ? '剑气外放！' : skillName,
                animation_model: skillName === 'feijian' ? 'feijian' : skillName,
                weapon_id: null,
                weapon_name: '空槽位',
                model_image: 'battle_sword.png',
            };
        }

        // 递增索引，准备下次释放
        window.enemySkillIndex++;

        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log(
                'battle-flow',
                `🎯 竞技场技能选择: 序列${sequenceIndex} 武器${skillIndex}:`,
                selectedSkillData
            );
        }

        console.log(
            `🎯 [技能选择结果] 序列索引:${sequenceIndex}, 武器索引:${skillIndex}, 选中技能数据:`,
            selectedSkillData
        );

        return selectedSkillData;
    }

    /**
     * 🎯 获取当前竞技场技能的完整数据（用于动画执行）
     */
    getCurrentArenaSkillData() {
        // 获取当前应该使用的技能索引（不增加计数器）
        if (!window.enemySkillIndex) {
            window.enemySkillIndex = 0;
        }

        // 获取敌人数据
        let enemyData = null;
        if (this.battleSystem?.dataManager?.enemyData) {
            enemyData = this.battleSystem.dataManager.enemyData;
        } else if (this.battleSystem?.enemy) {
            enemyData = this.battleSystem.enemy;
        } else if (this.dataManager?.enemyData) {
            enemyData = this.dataManager.enemyData;
        }

        if (!enemyData) {
            console.log('⚠️ [竞技场] 无法获取敌人数据');
            return null;
        }

        const skillSequence = enemyData.skill_sequence || [0, 1, 2, 3, 4, 5];
        const weaponSkillsData = enemyData.weapon_skills_data || [];

        console.log('🏆 [竞技场] 技能数据调试:', {
            敌人类型: enemyData.isRealPlayer ? '真实玩家' : enemyData.isPlayer ? 'AI玩家' : '怪物',
            武器技能数组: enemyData.weapon_skills,
            完整技能数据长度: weaponSkillsData.length,
            完整技能数据: weaponSkillsData,
            技能序列: skillSequence,
            当前enemySkillIndex: window.enemySkillIndex,
        });

        // 🔧 修复：如果 weapon_skills_data 为空但有 weapon_skills，则构建基础数据
        if (
            weaponSkillsData.length === 0 &&
            enemyData.weapon_skills &&
            enemyData.weapon_skills.length > 0
        ) {
            console.log('⚠️ [竞技场] weapon_skills_data为空，尝试从weapon_skills构建基础数据');
            for (let i = 0; i < enemyData.weapon_skills.length; i++) {
                weaponSkillsData.push({
                    skill_name: enemyData.weapon_skills[i],
                    animation_model: 'feijian', // 🔧 临时：使用默认动画，应该从API获取正确数据
                    mp_cost: 10,
                    min_attack: 100,
                    max_attack: 200,
                    model_image: 'battle_sword.png', // 🔧 修复：添加默认武器图片
                });
            }
            console.log(
                '🔧 [竞技场] 构建的基础技能数据（应该从API获取正确数据）:',
                weaponSkillsData
            );
        }

        // 计算当前技能索引（但不修改全局计数器）
        const currentIndex = window.enemySkillIndex - 1 >= 0 ? window.enemySkillIndex - 1 : 0;
        const sequenceIndex = currentIndex % skillSequence.length;
        const skillIndex = skillSequence[sequenceIndex];

        console.log(
            '🎯 [技能选择] currentIndex:',
            currentIndex,
            'sequenceIndex:',
            sequenceIndex,
            'skillIndex:',
            skillIndex
        );

        if (
            weaponSkillsData &&
            weaponSkillsData.length > skillIndex &&
            weaponSkillsData[skillIndex]
        ) {
            const skillData = weaponSkillsData[skillIndex];
            console.log('🎯 [当前技能数据] 索引:', skillIndex, '数据:', skillData);
            return skillData;
        }

        console.log(
            '⚠️ [竞技场] 无法获取技能数据，索引:',
            skillIndex,
            '数据长度:',
            weaponSkillsData.length,
            '完整数据:',
            weaponSkillsData
        );
        return null;
    }

    /**
     * 执行AI防御行为（简化版）
     */
    async executeAIDefend(aiDecision) {
        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('battle-flow', '🛡️ 怪物进入防御状态');
        }

        // 🔥 新增：显示AI防御战报
        window.battleReportManager?.showAIReport('defend', '敌方防御准备');

        // 显示防御喊话和行为描述
        const message = aiDecision.message || '防御';
        const behaviorText = aiDecision.behavior_text || '防御';
        await this.showSkillShout(`${message}`, true);

        // 显示简单的防御效果
        this.showSimpleDefenseEffect();

        // 🔧 设置防御状态（影响下次受到的伤害）
        if (this.battleSystem && this.battleSystem.enemy) {
            this.battleSystem.enemy.isDefending = true;
            this.battleSystem.enemy.defenseModifier = aiDecision.damage_modifier || 0.8;
            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.log(
                    'battle-flow',
                    `🛡️ 防御状态设置，伤害减免: ${(1 - (aiDecision.damage_modifier || 0.8)) * 100}%`
                );
            }
        }

        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('battle-flow', '🛡️ 防御行为完成');
        }
    }

    /**
     * 显示简单的防御效果
     */
    showSimpleDefenseEffect() {
        const enemyElement = document.querySelector('.enemy');
        if (!enemyElement) return;

        // 创建防御光环效果
        const defenseAura = document.createElement('div');
        defenseAura.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80px;
            height: 80px;
            border: 3px solid #4CAF50;
            border-radius: 50%;
            animation: defenseAura 1s ease-out;
            pointer-events: none;
            z-index: 100;
        `;

        // 添加防御动画CSS
        if (!document.querySelector('#defense-style')) {
            const style = document.createElement('style');
            style.id = 'defense-style';
            style.textContent = `
                @keyframes defenseAura {
                    0% { opacity: 0; transform: translate(-50%, -50%) scale(0.5); }
                    50% { opacity: 1; transform: translate(-50%, -50%) scale(1.2); }
                    100% { opacity: 0; transform: translate(-50%, -50%) scale(1.5); }
                }
            `;
            document.head.appendChild(style);
        }

        enemyElement.appendChild(defenseAura);

        // 1秒后移除效果
        setTimeout(() => {
            if (defenseAura && defenseAura.parentNode) {
                defenseAura.parentNode.removeChild(defenseAura);
            }
        }, 1000);
    }

    /**
     * 执行AI连击行为
     */
    async executeAICombo(aiDecision) {
        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('battle-flow', '⚡ 怪物执行连击');
        }

        // 🔥 新增：显示AI连击战报
        window.battleReportManager?.showAIReport('combo', '敌方连击准备');

        const hitCount = aiDecision.hit_count || 2;
        const skillName = aiDecision.skill || '连击';

        for (let i = 0; i < hitCount; i++) {
            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.log('battle-flow', `🔥 连击第${i + 1}击`);
            }

            // 每次连击都执行完整的攻击流程
            const singleHitDecision = {
                ...aiDecision,
                action: 'attack',
                damage_modifier: (aiDecision.damage_modifier || 1.5) / hitCount, // 总伤害分摊到每击
                skill: `${skillName}第${i + 1}击`,
            };

            await this.executeAIAttack(singleHitDecision);

            // 连击间隔
            if (i < hitCount - 1) {
                await new Promise(resolve => setTimeout(resolve, 300));
            }
        }

        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('battle-flow', '⚡ 连击完成');
        }
    }

    /**
     * 🔋 自动恢复系统 - 每回合为玩家和敌人恢复HP/MP
     */
    async performAutoRegeneration() {
        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('battle-flow', '=== 自动恢复系统 ===');
        }

        // 玩家自动恢复 (5%最大值)
        const playerHpRegen = Math.max(1, Math.floor(this.battleSystem.player.hp_bonus * 0.05));
        const playerMpRegen = Math.max(
            1,
            Math.floor(this.battleSystem.playerStats.mp_bonus * 0.05)
        );

        // 执行玩家恢复
        if (this.battleSystem.player.currentHp < this.battleSystem.player.hp_bonus) {
            this.battleSystem.player.currentHp = Math.min(
                this.battleSystem.player.hp_bonus,
                this.battleSystem.player.currentHp + playerHpRegen
            );
            this.battleSystem.player.updateUI();
            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.log(
                    'battle-flow',
                    `🔋 玩家HP恢复: +${playerHpRegen} (当前: ${this.battleSystem.player.currentHp}/${this.battleSystem.player.hp_bonus})`
                );
            }
        }

        if (this.battleSystem.playerStats.currentMp < this.battleSystem.playerStats.mp_bonus) {
            this.battleSystem.playerStats.currentMp = Math.min(
                this.battleSystem.playerStats.mp_bonus,
                this.battleSystem.playerStats.currentMp + playerMpRegen
            );
            this.battleSystem.updateMpBar();
            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.log(
                    'battle-flow',
                    `🔋 玩家MP恢复: +${playerMpRegen} (当前: ${this.battleSystem.playerStats.currentMp}/${this.battleSystem.playerStats.mp_bonus})`
                );
            }
        }

        // 敌人自动恢复 (5%最大值)
        const enemyHpRegen = Math.max(1, Math.floor(this.battleSystem.enemy.max_hp * 0.05)); // 🔧 修复：使用max_hp
        const enemyMpRegen = Math.max(
            1,
            Math.floor((this.battleSystem.enemy.max_mp || this.battleSystem.enemy.mp || 100) * 0.05)
        ); // 🔧 修复：使用max_mp

        // 执行敌人恢复
        if (this.battleSystem.enemy.currentHp < this.battleSystem.enemy.max_hp) {
            // 🔧 修复：使用max_hp
            this.battleSystem.enemy.currentHp = Math.min(
                this.battleSystem.enemy.max_hp,
                this.battleSystem.enemy.currentHp + enemyHpRegen
            ); // 🔧 修复：使用max_hp
            this.battleSystem.enemy.updateUI();
            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.log(
                    'battle-flow',
                    `🔋 敌人HP恢复: +${enemyHpRegen} (当前: ${this.battleSystem.enemy.currentHp}/${this.battleSystem.enemy.max_hp})`
                ); // 🔧 修复：使用max_hp
            }
        }

        if (
            (this.battleSystem.enemy.currentMp || 0) <
            (this.battleSystem.enemy.max_mp || this.battleSystem.enemy.mp || 100)
        ) {
            // 🔧 修复：使用max_mp
            this.battleSystem.enemy.currentMp = Math.min(
                this.battleSystem.enemy.max_mp || this.battleSystem.enemy.mp || 100,
                (this.battleSystem.enemy.currentMp || 0) + enemyMpRegen
            ); // 🔧 修复：使用max_mp
            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.log(
                    'battle-flow',
                    `🔋 敌人MP恢复: +${enemyMpRegen} (当前: ${this.battleSystem.enemy.currentMp}/${
                        this.battleSystem.enemy.max_mp || this.battleSystem.enemy.mp || 100
                    })`
                ); // 🔧 修复：使用max_mp
            }
        }

        // 🔥 新增：显示具体的恢复战报
        if (playerHpRegen > 0 || playerMpRegen > 0) {
            window.battleReportManager?.showHealReport(playerHpRegen, playerMpRegen, 'player');
        }

        if (enemyHpRegen > 0 || enemyMpRegen > 0) {
            window.battleReportManager?.showHealReport(enemyHpRegen, enemyMpRegen, 'enemy');
        }

        // 短暂等待，让玩家看到恢复效果
        await new Promise(resolve => setTimeout(resolve, 500));
    }

    /**
     * 显示AI行为描述（在战斗界面顶部）
     */
    showAIBehaviorDescription(description) {
        console.log('🤖 显示AI行为描述:', description);

        // 查找战斗界面顶部的合适位置
        const battleContainer =
            document.querySelector('.battle-container') ||
            document.querySelector('.battle-area') ||
            document.querySelector('#battleContainer') ||
            document.querySelector('.container');

        if (!battleContainer) {
            console.warn('❌ 未找到战斗容器，无法显示AI行为描述');
            return;
        }

        // 移除之前的AI描述（如果存在）
        const existingDescription = battleContainer.querySelector('.ai-behavior-description');
        if (existingDescription) {
            existingDescription.remove();
        }

        // 创建AI行为描述元素
        const descriptionElement = document.createElement('div');
        descriptionElement.className = 'ai-behavior-description';
        descriptionElement.textContent = `🤖 ${description}`;
        descriptionElement.style.cssText = `
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #fff;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 13px;
            text-align: center;
            margin: 10px auto 15px auto;
            max-width: 300px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
            animation: aiDescriptionShow 0.5s ease-out;
            position: relative;
            z-index: 100;
        `;

        // 添加CSS动画
        if (!document.querySelector('#ai-description-style')) {
            const style = document.createElement('style');
            style.id = 'ai-description-style';
            style.textContent = `
                @keyframes aiDescriptionShow {
                    0% { opacity: 0; transform: translateY(-10px) scale(0.9); }
                    100% { opacity: 1; transform: translateY(0) scale(1); }
                }
            `;
            document.head.appendChild(style);
        }

        // 插入到战斗容器顶部
        battleContainer.insertBefore(descriptionElement, battleContainer.firstChild);

        console.log('✅ AI行为描述已显示在战斗界面顶部:', description);

        // 5秒后淡出移除
        setTimeout(() => {
            if (descriptionElement && descriptionElement.parentNode) {
                descriptionElement.style.transition = 'opacity 1s ease-out';
                descriptionElement.style.opacity = '0';
                setTimeout(() => {
                    if (descriptionElement && descriptionElement.parentNode) {
                        descriptionElement.parentNode.removeChild(descriptionElement);
                    }
                }, 1000);
            }
        }, 5000);
    }

    /**
     * 🔥 新增：显示敌人暴走特效
     */
    showBerserkEffect() {
        const enemyCharacter = document.querySelector('.character.enemy');
        if (!enemyCharacter) return;

        // 添加暴走状态CSS类
        enemyCharacter.classList.add('berserk-mode');

        // 创建暴走特效元素
        const berserkEffect = document.createElement('div');
        berserkEffect.className = 'berserk-aura';
        berserkEffect.innerHTML = '🔥';
        berserkEffect.style.cssText = `
            position: absolute;
            top: -10px;
            right: -10px;
            font-size: 20px;
            color: #ff4444;
            text-shadow: 0 0 10px #ff4444;
            animation: berserkPulse 1s ease-in-out infinite;
            z-index: 1000;
            pointer-events: none;
        `;

        enemyCharacter.appendChild(berserkEffect);

        // 添加CSS动画
        if (!document.querySelector('#berserk-style')) {
            const style = document.createElement('style');
            style.id = 'berserk-style';
            style.textContent = `
                .character.enemy.berserk-mode {
                    filter: saturate(1.5) hue-rotate(15deg);
                    animation: berserkShake 0.3s ease-in-out infinite;
                }
                
                @keyframes berserkPulse {
                    0%, 100% { transform: scale(1); opacity: 1; }
                    50% { transform: scale(1.2); opacity: 0.7; }
                }
                
                @keyframes berserkShake {
                    0%, 100% { transform: translateX(0); }
                    25% { transform: translateX(-1px); }
                    75% { transform: translateX(1px); }
                }
            `;
            document.head.appendChild(style);
        }

        console.log('✅ 暴走特效已添加到敌人身上');
    }

    /**
     * 🔧 怪物技能动画执行（重写版本）
     * @param {string} enemySkillName 技能名称
     */
    async executeEnemySkillAnimation(enemySkillName) {
        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log(
                'battle-flow',
                `🎯 开始执行敌人技能动画: ${enemySkillName}`
            );
        }

        try {
            // 🔧 步骤1：使用技能名称直接执行，不需要复杂映射
            let skillToExecute = enemySkillName;
            let shoutName = enemySkillName;
            let currentSkillData = null;

            // 🔧 步骤2：检查竞技场模式，使用玩家技能动画
            const urlParams = new URLSearchParams(window.location.search);
            const isArenaMode = urlParams.get('arena') === '1';

            if (isArenaMode) {
                // 🏆 竞技场模式：获取正确的animation_model和技能名称
                currentSkillData = this.getCurrentArenaSkillData();
                if (currentSkillData) {
                    if (currentSkillData.animation_model) {
                        skillToExecute = currentSkillData.animation_model;
                        console.log(
                            '🏆 [竞技场] 使用animation_model:',
                            skillToExecute,
                            '来自技能:',
                            currentSkillData.skill_name
                        );
                    }

                    // 🔧 修复：使用正确的技能名称进行喊话
                    if (
                        currentSkillData.animation_model === 'feijian' ||
                        currentSkillData.skill_name === 'feijian'
                    ) {
                        shoutName = '剑气外放！';
                    } else {
                        shoutName = currentSkillData.skill_name || enemySkillName;
                    }
                } else {
                    console.log(
                        '🏆 [竞技场] 无法获取animation_model，使用技能名称:',
                        enemySkillName
                    );
                }

                if (window.BattleDebugConfig) {
                    window.BattleDebugConfig.log(
                        'battle-flow',
                        `🏆 竞技场模式：执行对手技能 ${enemySkillName} -> ${skillToExecute} (喊话: ${shoutName})`
                    );
                }
            }

            // 🔧 步骤3：显示技能喊话
            await this.showSkillShout(shoutName, true);

            // 🔧 步骤4：执行技能动画
            if (window.skillLoader) {
                // 🎯 关键修复：获取敌方正确的武器图片
                let weaponImage = 'assets/images/battle_sword.png'; // 默认武器图片

                if (currentSkillData && currentSkillData.model_image) {
                    weaponImage = window.ImagePathManager
                        ? window.ImagePathManager.getWeaponImage(currentSkillData.model_image)
                        : `assets/images/equi/${currentSkillData.model_image}`;
                    console.log(
                        '🏆 [敌方武器] 使用技能武器图片:',
                        weaponImage,
                        '来源model_image:',
                        currentSkillData.model_image
                    );
                } else {
                    console.log(
                        '🏆 [敌方武器] 使用默认武器图片:',
                        weaponImage,
                        '技能数据缺失model_image'
                    );
                }

                const finalSkillData = currentSkillData || {
                    isEnemySkill: true,
                    skillName: skillToExecute,
                    animation_model: skillToExecute,
                    displayName: shoutName, // 添加displayName字段
                };

                console.log('🏆 [敌方技能] 执行动画:', skillToExecute, '技能数据:', finalSkillData);

                // 🔧 修复：使用正确的参数调用，确保包含正确的技能名称
                const enhancedSkillData = {
                    ...finalSkillData,
                    isEnemySkill: true,
                    isEnemy: true,
                    isMonsterSkill: true,
                    displayName: shoutName, // 使用处理后的喊话文本
                    skillName: shoutName, // 同步更新skillName
                };

                await window.skillLoader.executeSkill(
                    skillToExecute,
                    enhancedSkillData,
                    weaponImage,
                    this.battleSystem?.effectsContainer,
                    this.battleSystem,
                    true
                );

                if (window.BattleDebugConfig) {
                    window.BattleDebugConfig.log(
                        'battle-flow',
                        `✅ 敌人技能动画执行完成: ${skillToExecute}`
                    );
                }
            } else {
                console.log('⚠️ [技能加载器] skillLoader未找到，使用备用动画');
                await new Promise(resolve => setTimeout(resolve, 1500));
            }
        } catch (error) {
            console.error('❌ 敌人技能动画执行失败:', error);
            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.log(
                    'battle-flow',
                    `❌ 敌人技能动画执行失败: ${enemySkillName}`,
                    error
                );
            }
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    }

    /**
     * 显示技能喊话
     */
    async showSkillShout(skillName, isEnemy = false) {
        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('battle-flow', '=== 显示技能喊话 ===');
            window.BattleDebugConfig.log('battle-flow', '技能名称:', skillName);
            window.BattleDebugConfig.log('battle-flow', '是否敌人:', isEnemy);
        }

        // 检查必要的DOM元素
        if (!this.battleSystem?.effectsContainer || !this.battleSystem?.battleContainer) {
            return;
        }

        // 清理之前的喊话文字，防止叠加
        const existingShouts = this.battleSystem.effectsContainer.querySelectorAll('.skill-shout');
        existingShouts.forEach(shout => shout.remove());

        const characterElement = document.querySelector(isEnemy ? '.enemy' : '.player');
        if (!characterElement) return;

        const shout = document.createElement('div');
        shout.className = 'skill-shout';

        // 技能喊话文本处理
        let shoutText;
        if (skillName === 'feijian' || skillName === '剑气外放！') {
            shoutText = '剑气外放！';
        } else {
            shoutText = skillName + (skillName.endsWith('！') ? '' : '！');
        }
        shout.textContent = shoutText;

        // 获取位置信息
        const characterRect = characterElement.getBoundingClientRect();
        const containerRect = this.battleSystem.battleContainer.getBoundingClientRect();

        const leftPos = characterRect.left - containerRect.left + characterRect.width / 2 - 50;
        const topPos = characterRect.top - containerRect.top - 20;

        shout.style.left = `${leftPos}px`;
        shout.style.top = `${topPos}px`;

        // 敌人喊话使用不同颜色
        if (isEnemy) {
            shout.style.color = '#ff6b6b';
            shout.style.textShadow = '2px 2px 4px rgba(255, 0, 0, 0.5)';
        }

        // 添加到效果容器
        this.battleSystem.effectsContainer.appendChild(shout);

        // 缩短喊话持续时间
        setTimeout(() => {
            if (shout && shout.parentNode) {
                shout.remove();
            }
        }, 800);
    }

    /**
     * 获取当前武器图片的辅助方法
     */
    getCurrentWeaponImage(skillData) {
        try {
            // 优先使用技能数据中的model_image（来自数据库）
            if (skillData && skillData.model_image) {
                if (window.ImagePathManager) {
                    return window.ImagePathManager.getWeaponImage(skillData.model_image);
                } else {
                    return 'assets/images/battle_sword.png';
                }
            }

            // 次选：使用weaponImage字段
            if (skillData && skillData.weaponImage) {
                if (window.ImagePathManager) {
                    return window.ImagePathManager.getWeaponImage(skillData.weaponImage);
                } else {
                    return 'assets/images/battle_sword.png';
                }
            }

            // 默认：使用默认武器图片
            if (window.ImagePathManager) {
                return window.ImagePathManager.getWeaponImage('battle_sword');
            } else {
                return 'assets/images/battle_sword.png';
            }
        } catch (error) {
            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.log('battle-flow', '获取武器图片失败:', error);
            }
            return 'assets/images/battle_sword.png';
        }
    }

    /**
     * 🔥 统一的技能伤害计算和处理 - 主要的耐久度检查入口
     * @param {Object} currentSkillData 当前技能数据
     */
    async performSkillDamageCalculation(currentSkillData) {
        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('battle-flow', '=== 技能伤害计算开始 ===');
            window.BattleDebugConfig.log('battle-flow', '原始技能数据:', currentSkillData);
        }

        // 🔧 核心：检查武器耐久度状态
        const weaponStatus = this.checkWeaponStatus(currentSkillData);
        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('battle-flow', '武器状态检查结果:', weaponStatus);
        }

        // 🔧 根据武器状态调整技能数据
        const finalSkillData = this.adjustSkillDataByWeaponStatus(currentSkillData, weaponStatus);
        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('battle-flow', '调整后的技能数据:', finalSkillData);
        }

        // 准备战斗数据
        const attackerData = this.prepareAttackerData();
        const defenderData = this.prepareDefenderData();

        // 🔧 计算技能威力
        let attackPower;
        if (weaponStatus.isUsable) {
            // 正常武器：计算完整技能威力
            attackPower = this.dataManager.getSkillDamage(
                finalSkillData,
                this.battleSystem.playerStats
            );
            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.log('battle-flow', '✅ 正常武器技能威力:', attackPower);
            }
        } else {
            // 损坏/无效武器：使用基础攻击力
            attackPower = this.battleSystem.playerStats.physical_attack || 10;
            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.log(
                    'battle-flow',
                    '⚠️ 损坏武器，使用基础攻击力:',
                    attackPower
                );
            }
        }

        // 进行战斗计算
        const battleResult = this.dataManager.calculateFinalDamage(
            attackPower,
            this.battleSystem.enemy.defense,
            attackerData,
            defenderData,
            {
                weaponType: finalSkillData.weaponType || 'sword',
                skillType: finalSkillData.weaponType === 'fan' ? 'magic' : 'physical',
                skillData: finalSkillData,
            }
        );

        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('battle-flow', '战斗计算结果:', battleResult);
        }

        // 🔧 检查怪物是否处于防御状态
        let finalDamage = battleResult.damage;
        if (this.battleSystem.enemy && this.battleSystem.enemy.isDefending) {
            const defenseReduction = this.battleSystem.enemy.defenseModifier || 0.8;
            finalDamage = Math.round(finalDamage * defenseReduction);
            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.log(
                    'battle-flow',
                    `🛡️ 怪物处于防御状态，伤害减免: ${finalDamage} (原伤害: ${battleResult.damage})`
                );
            }

            // 🔥 新增：显示防御减伤战报
            const damageReduced = battleResult.damage - finalDamage;
            window.battleReportManager?.showDefenseReport(damageReduced, 'enemy');

            // 清除防御状态（防御只持续一回合）
            this.battleSystem.enemy.isDefending = false;
            this.battleSystem.enemy.defenseModifier = null;
        }

        // 🔧 新增：应用玩家的攻击套装特殊效果
        if (this.battleSystem.applySetAttackEffects) {
            const playerAttackEffectResult = this.battleSystem.applySetAttackEffects(finalDamage);
            finalDamage = playerAttackEffectResult.damage;

            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.log(
                    'battle-flow',
                    '玩家套装攻击效果:',
                    playerAttackEffectResult
                );
            }
        }

        // 🔧 新增：应用敌人的防御套装特殊效果
        if (this.battleSystem.applyEnemySetDefenseEffects) {
            const enemyDefenseEffectResult =
                this.battleSystem.applyEnemySetDefenseEffects(finalDamage);
            finalDamage = enemyDefenseEffectResult.damage;

            // 处理敌人的反击效果
            if (enemyDefenseEffectResult.counterDamage > 0) {
                this.battleSystem.player.takeDamage(enemyDefenseEffectResult.counterDamage);
                this.battleSystem.showSetEffectMessage(
                    `敌人反击造成${enemyDefenseEffectResult.counterDamage}点伤害！`
                );
            }

            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.log(
                    'battle-flow',
                    '敌人套装防御效果:',
                    enemyDefenseEffectResult
                );
            }
        }

        // 使用修正后的伤害
        const modifiedBattleResult = { ...battleResult, damage: finalDamage };

        // 对敌人造成伤害
        const isDead = await this.battleSystem.enemy.takeDamage(modifiedBattleResult);
        if (isDead) {
            console.log('🏆 BattleFlowManager: 敌人死亡，调用handleBattleEnd');
            await this.battleSystem.handleBattleEnd(true, []);
            return true;
        }

        return false;
    }

    /**
     * 🔧 检查武器状态 - 集中的耐久度检查逻辑
     */
    checkWeaponStatus(skillData) {
        const weaponId = skillData.weaponId;
        const weaponName = skillData.weaponName || '未知武器';
        const currentDurability = parseInt(skillData.durability) || 0;
        const maxDurability = parseInt(skillData.maxDurability) || 100;
        const inventoryItemId = skillData.inventory_item_id;

        // 检查是否有武器
        if (!skillData.hasWeapon || !weaponId) {
            return {
                isUsable: false,
                reason: 'no_weapon',
                message: '无武器装备',
            };
        }

        // 检查inventory_item_id有效性
        const hasValidInventoryId =
            inventoryItemId &&
            inventoryItemId !== 'null' &&
            inventoryItemId !== null &&
            parseInt(inventoryItemId) > 0;

        if (!hasValidInventoryId) {
            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.log(
                    'battle-flow',
                    `⚠️ 武器${weaponName}的inventory_item_id无效:`,
                    inventoryItemId
                );
            }
            return {
                isUsable: false,
                reason: 'invalid_id',
                message: '武器ID无效',
                details: { weaponName, inventoryItemId },
            };
        }

        // 检查耐久度
        if (
            currentDurability === null ||
            currentDurability === undefined ||
            currentDurability <= 0
        ) {
            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.log('battle-flow', `⚠️ 武器${weaponName}耐久度为0或无效`);
            }
            return {
                isUsable: false,
                reason: 'zero_durability',
                message: '武器已损坏',
                details: { weaponName, currentDurability, maxDurability },
            };
        }

        // 武器正常可用
        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log(
                'battle-flow',
                `✅ 武器${weaponName}状态正常 (${currentDurability}/${maxDurability})`
            );
        }
        return {
            isUsable: true,
            reason: 'normal',
            message: '武器正常',
            details: { weaponName, currentDurability, maxDurability },
        };
    }

    /**
     * 🔧 根据武器状态调整技能数据
     */
    adjustSkillDataByWeaponStatus(originalSkillData, weaponStatus) {
        if (weaponStatus.isUsable) {
            return originalSkillData;
        }
        // 武器不可用，强制切换为"剑气外放！"所有关键字段
        return {
            hasWeapon: false,
            weaponId: null,
            weaponName: '空槽位',
            skillName: '剑气外放！',
            animationType: 'feijian',
            weaponAttack: 0,
            weaponType: 'sword',
            durability: 0,
            maxDurability: 0,
            damageMultiplier: 1.0,
            elementType: 'neutral',
            element_type: 'neutral',
            mp_cost: 0,
            mpCost: 0,
            cooldown_time: 0,
            cooldown: 0,
            model_image: null,
            icon_image: null,
            weaponImage: null,
            inventory_item_id: null,
            // 保留原始信息用于显示
            originalWeaponName: originalSkillData.weaponName,
            weaponStatusReason: weaponStatus.reason,
        };
    }

    /**
     * 🔧 准备攻击者数据
     */
    prepareAttackerData() {
        return {
            ...this.battleSystem.playerStats,
            accuracy_bonus: parseFloat(this.battleSystem.playerStats.accuracy_bonus) || 85, // 🔧 修复：使用数据库字段名
            critical_bonus: parseFloat(this.battleSystem.playerStats.critical_bonus) || 5, // 🔧 修复：统一使用critical_bonus
        };
    }

    /**
     * 🔧 准备防御者数据
     */
    prepareDefenderData() {
        return {
            ...this.battleSystem.enemy,
            dodge_bonus: parseFloat(this.battleSystem.enemy.dodge_bonus) || 5, // 🔧 修复：使用数据库字段名
            critical_resistance: parseFloat(this.battleSystem.enemy.critical_resistance) || 0, // 🔧 修复：使用数据库字段名
        };
    }
}

// 导出到全局
window.BattleFlowManager = BattleFlowManager;
