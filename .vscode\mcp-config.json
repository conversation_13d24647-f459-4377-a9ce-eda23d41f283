{"mcpServers": {"filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/path/to/allowed/files"], "env": {"NODE_ENV": "development"}}, "database": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sqlite", "yn_game.db"], "env": {"DATABASE_URL": "mysql://ynxx:mjlxz159@localhost:3306/yn_game"}}, "web-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "your-api-key-here"}}, "git": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-git", "--repository", "."], "env": {"GIT_AUTHOR_NAME": "一念修仙开发者", "GIT_AUTHOR_EMAIL": "<EMAIL>"}}}, "globalShortcuts": {"mcp.filesystem.readFile": "Ctrl+Shift+R", "mcp.database.query": "Ctrl+Shift+Q", "mcp.git.status": "Ctrl+Shift+G"}, "autoConnect": true, "logLevel": "info"}