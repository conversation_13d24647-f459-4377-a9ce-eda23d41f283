<?php

/**
 * 一念修仙项目数据备份系统
 * 自动化数据库备份和恢复机制
 */

require_once __DIR__ . '/setting.php';

class BackupSystem
{
    private $pdo;
    private $backupDir;
    private $maxBackups;

    public function __construct()
    {
        $this->pdo = getDatabaseConnection();
        $this->backupDir = BACKUP_DIR;
        $this->maxBackups = 30; // 保留30个备份文件

        // 确保备份目录存在
        if (!is_dir($this->backupDir)) {
            mkdir($this->backupDir, 0755, true);
        }
    }

    /**
     * 执行完整数据库备份
     */
    public function createFullBackup()
    {
        try {
            $timestamp = date('Y-m-d_H-i-s');
            $backupFile = $this->backupDir . "/full_backup_{$timestamp}.sql";

            echo "开始创建完整备份...\n";
            echo "备份文件: $backupFile\n";

            // 构建mysqldump命令 - 使用PHPStudy Pro的mysqldump
            $mysqldumpPath = 'E:/phpstudy_pro/Extensions/MySQL5.7.26/bin/mysqldump.exe';
            $command = sprintf(
                '"%s" -h%s -P%s -u%s -p%s --single-transaction --routines --triggers %s > %s',
                $mysqldumpPath,
                DB_HOST,
                DB_PORT,
                DB_USER,
                DB_PASS,
                DB_NAME,
                escapeshellarg($backupFile)
            );

            // 执行备份
            $output = [];
            $returnCode = 0;
            exec($command, $output, $returnCode);

            if ($returnCode === 0 && file_exists($backupFile)) {
                $fileSize = filesize($backupFile);
                echo "✅ 备份成功创建\n";
                echo "   文件大小: " . $this->formatBytes($fileSize) . "\n";

                // 记录备份信息
                $this->logBackup('full', $backupFile, $fileSize);

                // 清理旧备份
                $this->cleanOldBackups();

                return [
                    'success' => true,
                    'file' => $backupFile,
                    'size' => $fileSize
                ];
            } else {
                throw new Exception("备份命令执行失败，返回码: $returnCode");
            }
        } catch (Exception $e) {
            echo "❌ 备份失败: " . $e->getMessage() . "\n";
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 创建增量备份（仅备份用户数据）
     */
    public function createIncrementalBackup()
    {
        try {
            $timestamp = date('Y-m-d_H-i-s');
            $backupFile = $this->backupDir . "/incremental_backup_{$timestamp}.sql";

            echo "开始创建增量备份...\n";

            // 只备份用户相关的表
            $userTables = [
                'users',
                'characters',
                'character_equipment',
                'user_inventories',
                'character_skills',
                'battle_records',
                'immortal_arena_ranks',
                'immortal_arena_records',
                'cultivation_records',
                'transaction_logs'
            ];

            $tablesStr = implode(' ', $userTables);

            $mysqldumpPath = 'E:/phpstudy_pro/Extensions/MySQL5.7.26/bin/mysqldump.exe';
            $command = sprintf(
                '"%s" -h%s -P%s -u%s -p%s --single-transaction %s %s > %s',
                $mysqldumpPath,
                DB_HOST,
                DB_PORT,
                DB_USER,
                DB_PASS,
                DB_NAME,
                $tablesStr,
                escapeshellarg($backupFile)
            );

            $output = [];
            $returnCode = 0;
            exec($command, $output, $returnCode);

            if ($returnCode === 0 && file_exists($backupFile)) {
                $fileSize = filesize($backupFile);
                echo "✅ 增量备份成功\n";
                echo "   文件大小: " . $this->formatBytes($fileSize) . "\n";

                $this->logBackup('incremental', $backupFile, $fileSize);

                return [
                    'success' => true,
                    'file' => $backupFile,
                    'size' => $fileSize
                ];
            } else {
                throw new Exception("增量备份失败，返回码: $returnCode");
            }
        } catch (Exception $e) {
            echo "❌ 增量备份失败: " . $e->getMessage() . "\n";
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 恢复数据库
     */
    public function restoreBackup($backupFile)
    {
        try {
            if (!file_exists($backupFile)) {
                throw new Exception("备份文件不存在: $backupFile");
            }

            echo "开始恢复数据库...\n";
            echo "备份文件: $backupFile\n";

            // 确认操作
            echo "⚠️  警告: 此操作将覆盖当前数据库！\n";
            echo "请输入 'YES' 确认继续: ";
            $confirmation = trim(fgets(STDIN));

            if ($confirmation !== 'YES') {
                echo "操作已取消\n";
                return ['success' => false, 'error' => '用户取消操作'];
            }

            // 执行恢复
            $mysqlPath = 'E:/phpstudy_pro/Extensions/MySQL5.7.26/bin/mysql.exe';
            $command = sprintf(
                '"%s" -h%s -P%s -u%s -p%s %s < %s',
                $mysqlPath,
                DB_HOST,
                DB_PORT,
                DB_USER,
                DB_PASS,
                DB_NAME,
                escapeshellarg($backupFile)
            );

            $output = [];
            $returnCode = 0;
            exec($command, $output, $returnCode);

            if ($returnCode === 0) {
                echo "✅ 数据库恢复成功\n";
                $this->logRestore($backupFile);

                return ['success' => true];
            } else {
                throw new Exception("恢复命令执行失败，返回码: $returnCode");
            }
        } catch (Exception $e) {
            echo "❌ 恢复失败: " . $e->getMessage() . "\n";
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 列出所有备份文件
     */
    public function listBackups()
    {
        $backups = [];
        $files = glob($this->backupDir . '/*.sql');

        foreach ($files as $file) {
            $filename = basename($file);
            $size = filesize($file);
            $mtime = filemtime($file);

            // 解析备份类型
            $type = 'unknown';
            if (strpos($filename, 'full_backup_') === 0) {
                $type = 'full';
            } elseif (strpos($filename, 'incremental_backup_') === 0) {
                $type = 'incremental';
            }

            $backups[] = [
                'file' => $file,
                'filename' => $filename,
                'type' => $type,
                'size' => $size,
                'size_formatted' => $this->formatBytes($size),
                'date' => date('Y-m-d H:i:s', $mtime),
                'timestamp' => $mtime
            ];
        }

        // 按时间倒序排列
        usort($backups, function ($a, $b) {
            return $b['timestamp'] - $a['timestamp'];
        });

        return $backups;
    }

    /**
     * 清理旧备份文件
     */
    private function cleanOldBackups()
    {
        $backups = $this->listBackups();

        if (count($backups) > $this->maxBackups) {
            $toDelete = array_slice($backups, $this->maxBackups);

            foreach ($toDelete as $backup) {
                if (unlink($backup['file'])) {
                    echo "🗑️  删除旧备份: " . $backup['filename'] . "\n";
                }
            }
        }
    }

    /**
     * 记录备份操作
     */
    private function logBackup($type, $file, $size)
    {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO backup_logs (backup_type, backup_file, file_size, created_at)
                VALUES (?, ?, ?, NOW())
            ");
            $stmt->execute([$type, basename($file), $size]);
        } catch (Exception $e) {
            // 如果backup_logs表不存在，创建它
            $this->createBackupLogsTable();
            $stmt = $this->pdo->prepare("
                INSERT INTO backup_logs (backup_type, backup_file, file_size, created_at)
                VALUES (?, ?, ?, NOW())
            ");
            $stmt->execute([$type, basename($file), $size]);
        }
    }

    /**
     * 记录恢复操作
     */
    private function logRestore($file)
    {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO backup_logs (backup_type, backup_file, file_size, created_at, operation)
                VALUES ('restore', ?, 0, NOW(), 'restore')
            ");
            $stmt->execute([basename($file)]);
        } catch (Exception $e) {
            // 忽略日志错误
        }
    }

    /**
     * 创建备份日志表
     */
    private function createBackupLogsTable()
    {
        $sql = "
            CREATE TABLE IF NOT EXISTS backup_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                backup_type VARCHAR(20) NOT NULL,
                backup_file VARCHAR(255) NOT NULL,
                file_size BIGINT DEFAULT 0,
                operation VARCHAR(20) DEFAULT 'backup',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ";
        $this->pdo->exec($sql);
    }

    /**
     * 格式化文件大小
     */
    private function formatBytes($size)
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $unit = 0;

        while ($size >= 1024 && $unit < count($units) - 1) {
            $size /= 1024;
            $unit++;
        }

        return round($size, 2) . ' ' . $units[$unit];
    }
}

// 命令行接口
if (php_sapi_name() === 'cli') {
    echo "=== 一念修仙数据备份系统 ===\n\n";

    $backup = new BackupSystem();

    $action = $argv[1] ?? 'help';

    switch ($action) {
        case 'full':
            $result = $backup->createFullBackup();
            break;

        case 'incremental':
            $result = $backup->createIncrementalBackup();
            break;

        case 'list':
            $backups = $backup->listBackups();
            echo "备份文件列表:\n";
            echo "----------------------------------------\n";
            foreach ($backups as $b) {
                echo sprintf(
                    "%-20s %-12s %-10s %s\n",
                    $b['filename'],
                    $b['type'],
                    $b['size_formatted'],
                    $b['date']
                );
            }
            break;

        case 'restore':
            $file = $argv[2] ?? '';
            if (empty($file)) {
                echo "请指定要恢复的备份文件\n";
                echo "用法: php backup_system.php restore <备份文件路径>\n";
            } else {
                $backup->restoreBackup($file);
            }
            break;

        default:
            echo "用法:\n";
            echo "  php backup_system.php full        - 创建完整备份\n";
            echo "  php backup_system.php incremental - 创建增量备份\n";
            echo "  php backup_system.php list        - 列出所有备份\n";
            echo "  php backup_system.php restore <file> - 恢复备份\n";
            break;
    }
}
