# 🔥 一念修仙战力评分系统设计文档

*设计时间: 2025年6月17日 17:58*  
*版本: v2.0 (简化版)*  
*适用范围: 人物、怪物、武器、装备全方位战力评估*

## 🎯 战力评分系统概述

### 📋 设计目标
- **简单实用**: 基于数值直接计算，避免过度复杂化
- **直观显示**: 直接显示"战力值：xxx"，一目了然
- **动态计算**: 实时计算，无需数据库缓存
- **轻量级**: 单一文件实现，便于维护

### 🌟 核心功能
1. **人物战力评分**: 综合计算玩家角色的总体战斗力
2. **怪物威胁评级**: 评估怪物对玩家的威胁程度
3. **装备战力显示**: 装备详情中显示战力值
4. **背包智能提示**: 比当前装备更好的显示绿色上箭头

## 📊 简化战力评分体系

### 🔬 核心评分公式

```javascript
总战力 = (基础属性战力 + 装备战力) × 境界倍率

其中：
基础属性战力 = 攻击力最高值 + 防御力平均值 + 生命值 × 0.1 + 速度值
装备战力 = Σ(单件装备战力评分)
境界倍率 = 1 + 境界等级 × 0.1
```

### 🎯 简化设计原则

1. **无职业区分**: 项目中无明确剑修/法修职业，直接取最高攻击力
2. **武器决定伤害**: 带什么武器就算什么伤害，不需要复杂的功法判断
3. **直接数值显示**: 所有地方都显示"战力值：xxx"，简单直观
4. **动态实时计算**: 属性变化时立即重新计算，不存储数据库
5. **单一绿箭头**: 只在背包中比当前装备好的显示绿色上箭头

### ⚖️ 简化权重系统

去掉复杂的动态权重，使用固定权重：

```javascript
基础权重配置 = {
    攻击力: max(物理攻击, 法术攻击),  // 取最高攻击力
    防御力: (物理防御 + 法术防御) / 2, // 防御平均值
    生命值: 生命值 × 0.1,              // 生命值权重
    速度: 速度值                       // 速度直接计算
}
```

## 🧮 详细计算方法

### 🔋 基础战力计算

```javascript
function calculateBasePower(characterStats) {
    // 攻击力取最高值（无论物理还是法术）
    const attackPower = Math.max(
        characterStats.physical_attack || 0,
        characterStats.immortal_attack || 0
    );
    
    // 防御力取平均值
    const defensePower = (
        (characterStats.physical_defense || 0) + 
        (characterStats.immortal_defense || 0)
    ) / 2;
    
    // 生命值按0.1权重
    const hpPower = (characterStats.hp || 0) * 0.1;
    
    // 速度直接计算
    const speedPower = characterStats.speed_bonus || 0;
    
    return attackPower + defensePower + hpPower + speedPower;
}
```

### 🎲 境界倍率计算

```javascript
function calculateRealmMultiplier(realmLevel) {
    // 境界名称对照表（正确版本）- 280个境界等级
    const realmGroups = [
        '开光期',    // 1-10 (10个小境界)
        '灵虚期',    // 11-20
        '辟谷期',    // 21-30
        '心动期',    // 31-40
        '元化期',    // 41-50
        '元婴期',    // 51-60
        '离合期',    // 61-70
        '空冥期',    // 71-80
        '寂灭期',    // 81-90
        '大乘期',    // 91-100
        '渡劫期',    // 101-110
        '凡仙期',    // 111-120
        '地仙期',    // 121-130
        '天仙期',    // 131-140
        '真仙期',    // 141-150
        '太乙真仙期', // 151-160
        '太乙金仙期', // 161-170
        '太乙玄仙期', // 171-180
        '大罗真仙期', // 181-190
        '大罗金仙期', // 191-200
        '大罗玄仙期', // 201-210
        '准圣期',    // 211-220
        '教主期',    // 221-230
        '混元期',    // 231-240
        '混元金仙期', // 241-250
        '混元至仙期', // 251-260
        '天道期',    // 261-270
        '鸿蒙至元期'  // 271-280
    ];
    
    // 简单的境界倍率：1 + 境界等级 × 0.1
    return 1 + (realmLevel || 1) * 0.1;
}
```

### 🛡️ 装备战力评估

```javascript
function calculateEquipmentPower(equipment) {
    let equipmentPower = 0;
    
    // 基础属性直接相加（不分物理法术，项目中无明确职业区分）
    equipmentPower += equipment.physical_attack || 0;
    equipmentPower += equipment.immortal_attack || 0;
    equipmentPower += equipment.physical_defense || 0;
    equipmentPower += equipment.immortal_defense || 0;
    equipmentPower += (equipment.hp_bonus || 0) * 0.1;
    equipmentPower += equipment.speed_bonus || 0;
    
    // 特殊属性简单加成（避免复杂计算）
    equipmentPower += (equipment.critical_bonus || 0) * 2;
    equipmentPower += (equipment.critical_damage || 0) * 100;
    equipmentPower += equipment.accuracy_bonus || 0;
    equipmentPower += equipment.dodge_bonus || 0;
    equipmentPower += equipment.block_bonus || 0;
    
    return Math.round(equipmentPower);
}
```

## 🎯 威胁等级评估

### 👹 简化威胁评级

```javascript
function calculateThreatLevel(monsterPower, playerPower) {
    const powerRatio = playerPower > 0 ? monsterPower / playerPower : 999;
    
    if (powerRatio < 0.7) return { text: '轻松', color: '#2ecc71' };
    if (powerRatio < 1.0) return { text: '普通', color: '#f39c12' };
    if (powerRatio < 1.5) return { text: '困难', color: '#e67e22' };
    if (powerRatio < 2.0) return { text: '危险', color: '#e74c3c' };
    return { text: '致命', color: '#8e44ad' };
}
```

## 🛠️ 简化实施指南

### 📁 文件创建清单

**只需一个后端文件**:
```
src/api/power_rating.php                  # 统一战力系统API
```

**前端文件**:
```
public/assets/js/power-rating.js          # 前端战力显示
public/assets/css/power-rating.css        # 简单样式
```

### 🗄️ 无需数据库字段

**完全动态计算，不添加任何数据库字段**

### 🔧 核心后端实现

**统一API文件** `src/api/power_rating.php`:
```php
<?php
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/auth.php';

header('Content-Type: application/json; charset=utf-8');

class PowerRating {
    
    /**
     * 计算角色总战力
     */
    public static function calculateCharacterPower($characterData, $equipment = null) {
        $basePower = self::calculateBasePower($characterData);
        $equipmentPower = $equipment ? self::calculateTotalEquipmentPower($equipment) : 0;
        $realmMultiplier = self::calculateRealmMultiplier($characterData['realm_level'] ?? 1);
        
        $totalPower = ($basePower + $equipmentPower) * $realmMultiplier;
        
        return round($totalPower);
    }
    
    /**
     * 计算基础战力
     */
    private static function calculateBasePower($stats) {
        // 攻击力取最高值
        $attackPower = max(
            $stats['physical_attack'] ?? 0,
            $stats['immortal_attack'] ?? 0
        );
        
        // 防御力取平均值
        $defensePower = (
            ($stats['physical_defense'] ?? 0) + 
            ($stats['immortal_defense'] ?? 0)
        ) / 2;
        
        // 生命值按0.1权重
        $hpPower = ($stats['hp'] ?? 0) * 0.1;
        
        // 速度直接计算
        $speedPower = $stats['speed_bonus'] ?? 0;
        
        return $attackPower + $defensePower + $hpPower + $speedPower;
    }
    
    /**
     * 境界倍率计算
     */
    private static function calculateRealmMultiplier($realmLevel) {
        return 1 + ($realmLevel * 0.1);
    }
    
    /**
     * 装备总战力计算
     */
    private static function calculateTotalEquipmentPower($equipment) {
        $totalPower = 0;
        
        foreach ($equipment as $item) {
            if (is_array($item) && isset($item['item_type']) && $item['item_type'] === 'equipment') {
                $totalPower += self::calculateSingleEquipmentPower($item);
            }
        }
        
        return $totalPower;
    }
    
    /**
     * 单件装备战力计算
     */
    public static function calculateSingleEquipmentPower($equipment) {
        $power = 0;
        
        // 基础属性直接相加
        $power += $equipment['physical_attack'] ?? 0;
        $power += $equipment['immortal_attack'] ?? 0;
        $power += $equipment['physical_defense'] ?? 0;
        $power += $equipment['immortal_defense'] ?? 0;
        $power += ($equipment['hp_bonus'] ?? 0) * 0.1;
        $power += $equipment['speed_bonus'] ?? 0;
        
        // 特殊属性简单加成
        $power += ($equipment['critical_bonus'] ?? 0) * 2;
        $power += ($equipment['critical_damage'] ?? 0) * 100;
        $power += $equipment['accuracy_bonus'] ?? 0;
        $power += $equipment['dodge_bonus'] ?? 0;
        $power += $equipment['block_bonus'] ?? 0;
        
        return round($power);
    }
    
    /**
     * 威胁等级评估
     */
    public static function calculateThreatLevel($monsterPower, $playerPower) {
        $powerRatio = $playerPower > 0 ? $monsterPower / $playerPower : 999;
        
        if ($powerRatio < 0.7) return ['text' => '轻松', 'color' => '#2ecc71'];
        if ($powerRatio < 1.0) return ['text' => '普通', 'color' => '#f39c12'];
        if ($powerRatio < 1.5) return ['text' => '困难', 'color' => '#e67e22'];
        if ($powerRatio < 2.0) return ['text' => '危险', 'color' => '#e74c3c'];
        return ['text' => '致命', 'color' => '#8e44ad'];
    }
    
    /**
     * 装备对比
     */
    public static function compareEquipment($currentEquipment, $newEquipment) {
        $currentPower = $currentEquipment ? self::calculateSingleEquipmentPower($currentEquipment) : 0;
        $newPower = self::calculateSingleEquipmentPower($newEquipment);
        
        return [
            'current_power' => $currentPower,
            'new_power' => $newPower,
            'is_better' => $newPower > $currentPower,
            'power_difference' => $newPower - $currentPower
        ];
    }
}

// API处理
if (!check_auth()) {
    echo json_encode(['success' => false, 'message' => '用户未登录']);
    exit;
}

$action = $_POST['action'] ?? $_GET['action'] ?? '';

try {
    $pdo = getDatabase();
    $character_id = get_character_id();
    
    switch ($action) {
        case 'get_character_power':
            // 获取角色数据
            $stmt = $pdo->prepare("
                SELECT c.*, cr.* 
                FROM characters c 
                LEFT JOIN character_resources cr ON c.character_id = cr.character_id 
                WHERE c.character_id = ?
            ");
            $stmt->execute([$character_id]);
            $characterData = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // 获取装备数据
            $equipment = get_character_equipment($character_id);
            
            // 计算战力
            $totalPower = PowerRating::calculateCharacterPower($characterData, $equipment);
            
            echo json_encode([
                'success' => true,
                'power_rating' => $totalPower
            ]);
            break;
            
        case 'get_equipment_power':
            $equipmentId = $_POST['equipment_id'] ?? 0;
            
            // 获取装备数据
            $stmt = $pdo->prepare("
                SELECT gi.*, ia.* 
                FROM game_items gi 
                LEFT JOIN item_attributes ia ON gi.item_id = ia.item_id 
                WHERE gi.item_id = ?
            ");
            $stmt->execute([$equipmentId]);
            $equipment = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$equipment) {
                throw new Exception('装备不存在');
            }
            
            $power = PowerRating::calculateSingleEquipmentPower($equipment);
            
            echo json_encode([
                'success' => true,
                'power_rating' => $power
            ]);
            break;
            
        case 'check_better_equipment':
            $slot = $_POST['slot'] ?? '';
            
            // 获取当前装备
            $currentEquipment = get_equipped_item($character_id, $slot);
            
            // 获取背包中该槽位的所有装备
            $stmt = $pdo->prepare("
                SELECT ui.item_id, gi.*, ia.*
                FROM user_inventories ui
                JOIN game_items gi ON ui.item_id = gi.item_id
                LEFT JOIN item_attributes ia ON gi.item_id = ia.item_id
                WHERE ui.character_id = ? AND gi.equipment_slot = ?
            ");
            $stmt->execute([$character_id, $slot]);
            $inventoryItems = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $betterItems = [];
            $currentPower = $currentEquipment ? PowerRating::calculateSingleEquipmentPower($currentEquipment) : 0;
            
            foreach ($inventoryItems as $item) {
                $itemPower = PowerRating::calculateSingleEquipmentPower($item);
                if ($itemPower > $currentPower) {
                    $betterItems[] = $item['item_id'];
                }
            }
            
            echo json_encode([
                'success' => true,
                'better_items' => $betterItems
            ]);
            break;
            
        case 'assess_monster_threat':
            $monsterId = $_POST['monster_id'] ?? 0;
            
            // 获取角色战力
            $stmt = $pdo->prepare("
                SELECT c.*, cr.* 
                FROM characters c 
                LEFT JOIN character_resources cr ON c.character_id = cr.character_id 
                WHERE c.character_id = ?
            ");
            $stmt->execute([$character_id]);
            $characterData = $stmt->fetch(PDO::FETCH_ASSOC);
            $equipment = get_character_equipment($character_id);
            $playerPower = PowerRating::calculateCharacterPower($characterData, $equipment);
            
            // 获取怪物数据
            $stmt = $pdo->prepare("SELECT * FROM monsters WHERE monster_id = ?");
            $stmt->execute([$monsterId]);
            $monster = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$monster) {
                throw new Exception('怪物不存在');
            }
            
            $monsterPower = PowerRating::calculateBasePower($monster);
            $threat = PowerRating::calculateThreatLevel($monsterPower, $playerPower);
            
            echo json_encode([
                'success' => true,
                'player_power' => $playerPower,
                'monster_power' => round($monsterPower),
                'threat' => $threat
            ]);
            break;
            
        default:
            throw new Exception('未知的操作');
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
?>
```

### 🎨 简化前端实现

**前端JavaScript** `public/assets/js/power-rating.js`:
```javascript
class PowerRating {
    
    // 显示角色战力
    static async displayCharacterPower(containerId) {
        try {
            const response = await fetch('src/api/power_rating.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: 'action=get_character_power'
            });
            
            const data = await response.json();
            if (data.success) {
                const container = document.getElementById(containerId);
                if (container) {
                    container.innerHTML = `战力值：${data.power_rating}`;
                }
            }
        } catch (error) {
            console.error('获取战力失败:', error);
        }
    }
    
    // 显示装备战力
    static async displayEquipmentPower(equipmentId, containerId) {
        try {
            const response = await fetch('src/api/power_rating.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `action=get_equipment_power&equipment_id=${equipmentId}`
            });
            
            const data = await response.json();
            if (data.success) {
                const container = document.getElementById(containerId);
                if (container) {
                    container.innerHTML = `战力值：${data.power_rating}`;
                }
            }
        } catch (error) {
            console.error('获取装备战力失败:', error);
        }
    }
    
    // 标记背包中更好的装备
    static async markBetterEquipment(slot) {
        try {
            const response = await fetch('src/api/power_rating.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `action=check_better_equipment&slot=${slot}`
            });
            
            const data = await response.json();
            if (data.success) {
                // 清除所有箭头
                document.querySelectorAll('.power-upgrade-arrow').forEach(el => el.remove());
                
                // 为更好的装备添加绿色上箭头
                data.better_items.forEach(itemId => {
                    const itemElement = document.querySelector(`[data-item-id="${itemId}"]`);
                    if (itemElement) {
                        const arrow = document.createElement('div');
                        arrow.className = 'power-upgrade-arrow';
                        arrow.innerHTML = '↗';
                        itemElement.style.position = 'relative';
                        itemElement.appendChild(arrow);
                    }
                });
            }
        } catch (error) {
            console.error('检查装备失败:', error);
        }
    }
    
    // 显示怪物威胁等级
    static async displayMonsterThreat(monsterId, containerId) {
        try {
            const response = await fetch('src/api/power_rating.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `action=assess_monster_threat&monster_id=${monsterId}`
            });
            
            const data = await response.json();
            if (data.success) {
                const container = document.getElementById(containerId);
                if (container) {
                    container.innerHTML = `
                        <span style="color: ${data.threat.color}; font-weight: bold;">
                            ${data.threat.text}
                        </span>
                        <small>(敌方战力: ${data.monster_power})</small>
                    `;
                }
            }
        } catch (error) {
            console.error('威胁评估失败:', error);
        }
    }
}

// 全局可用
window.PowerRating = PowerRating;
```

**简化CSS样式** `public/assets/css/power-rating.css`:
```css
/* 战力值显示 */
.power-rating-display {
    color: #e67e22;
    font-weight: bold;
    font-size: 14px;
    margin: 5px 0;
}

/* 装备升级箭头 */
.power-upgrade-arrow {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #27ae60;
    color: white;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    z-index: 10;
    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

/* 威胁等级显示 */
.threat-level-display {
    font-size: 14px;
    margin: 5px 0;
}
```

### 🔗 页面集成示例

**在属性页面显示战力**:
```html
<div class="power-rating-display" id="character-power">战力值：计算中...</div>
<script>
    PowerRating.displayCharacterPower('character-power');
</script>
```

**在装备详情显示战力**:
```html
<div class="power-rating-display" id="equipment-power">战力值：计算中...</div>
<script>
    PowerRating.displayEquipmentPower(equipmentId, 'equipment-power');
</script>
```

**在背包标记更好装备**:
```javascript
// 打开背包时调用
PowerRating.markBetterEquipment('weapon'); // 武器槽位
PowerRating.markBetterEquipment('chest');  // 胸甲槽位
// 其他槽位...
```

**在战斗显示威胁等级**:
```html
<div class="threat-level-display" id="monster-threat">威胁等级：评估中...</div>
<script>
    PowerRating.displayMonsterThreat(monsterId, 'monster-threat');
</script>
```

### 📋 简化实施检查清单

**后端开发**:
- [ ] 创建 `power_rating.php` 统一API文件
- [ ] 测试各个API接口功能

**前端开发**:
- [ ] 创建 `power-rating.js` 前端脚本
- [ ] 创建 `power-rating.css` 简单样式
- [ ] 在属性页面集成战力显示
- [ ] 在装备详情集成战力显示
- [ ] 在背包集成升级箭头提示
- [ ] 在战斗页面集成威胁显示

**测试验证**:
- [ ] 测试战力计算合理性
- [ ] 测试装备对比准确性
- [ ] 测试威胁等级显示
- [ ] 测试性能（响应速度）

### 🎯 简化版特点

1. **计算简单**: 去掉复杂权重，直接数值相加
2. **显示直观**: 直接显示"战力值：xxx"
3. **提示简洁**: 只在更好装备显示绿色上箭头
4. **动态计算**: 完全实时计算，无数据库缓存
5. **单文件维护**: 后端逻辑集中在一个文件
6. **轻量级**: 前端代码简单，样式最小化

---

*本文档基于项目实际情况重新设计，追求简单实用的战力评分系统。*

## 🔥 基于实际数值的科学化战力权重系统

### 📊 权重设计原理

基于项目实际代码分析，重新设计科学化的战力权重体系：

#### 🎯 实际数值范围分析
- **暴击率/命中率/闪避率**：数值型，可超过100点
  - 基础命中率：85点 + 悟性×0.5
  - 基础闪避率：5点 + 身法×0.3  
  - 基础暴击率：5点 + 身法×0.2
  - 装备随机加成：5-15点范围

- **暴击伤害**：小数格式(0.15表示15%)，战斗中转为倍率(1+值)

- **战斗公式实际应用**：
  - 命中计算：`命中率 - 闪避率×0.8` (限制5%-95%)
  - 暴击计算：`暴击率 - 免暴率` (限制0%-50%)  
  - 暴伤计算：`基础伤害 × (1 + 暴击伤害值)`

### ⚖️ 科学化权重配置

```javascript
// 🔥 新权重系统 - 基于实际战斗影响设计
战力权重系统 = {
    // === 核心战斗属性 (权重1.0基准) ===
    攻击力: 1.0,     // 基准属性，直接影响伤害输出
    防御力: 0.8,     // 防御重要但略低于攻击
    
    // === 暴击系统 (基于实际数值范围) ===
    暴击率: 15,      // 装备5-15点 → 75-225战力 (合理贡献)
    暴击伤害: 800,   // 小数0.1-0.3 → 80-240战力 (高价值属性)
    免暴率: 12,      // 防御性暴击属性
    
    // === 命中闪避系统 (基于公式影响) ===
    命中率: 8,       // 基础85点基础上，装备加成影响有限
    闪避率: 12,      // 公式×0.8但防御价值较高
    格挡率: 10,      // 特殊防御属性
    
    // === 生存机动性 (基于数值规模) ===
    生命值: 0.08,    // 数值大(50-200)，权重需要小
    法力值: 0.05,    // 重要性略低于生命值  
    速度: 2.0,       // 影响行动顺序，战斗价值高
    
    // === 特殊效果 (动态评估) ===
    特殊效果: 8-50   // 基础8分，根据效果类型最多50分
}
```

### 🧮 权重设计依据

#### 🎯 攻击力权重 (1.0基准)
- **直接影响**：每点攻击力直接转化为伤害
- **设计依据**：作为基准属性，权重1.0
- **实际价值**：20点攻击力 = 20战力

#### ⚔️ 暴击率权重 (15)
- **实际范围**：装备加成5-15点
- **战斗影响**：10点暴击率 ≈ 10%暴击概率 ≈ 约5%伤害提升
- **设计依据**：10点×15权重=150战力，相当于150点攻击力的价值
- **合理性验证**：5%伤害提升确实值得这个战力投入

#### 💥 暴击伤害权重 (800)
- **实际范围**：小数0.1-0.3 (代表10%-30%)
- **战斗影响**：0.2暴击伤害 = 20%额外伤害
- **设计依据**：0.2×800权重=160战力
- **收益分析**：配合暴击率，20%额外伤害具有极高价值

#### 🎯 命中率权重 (8)
- **基础值高**：角色基础85点命中率
- **装备加成**：5-15点增量影响相对有限
- **实际影响**：在高基础值上+10点≈10%命中率提升
- **设计依据**：影响有限，权重适中

#### 💨 闪避率权重 (12)
- **战斗公式**：`命中率 - 闪避率×0.8`
- **实际影响**：10点闪避≈8%命中率降低
- **防御价值**：闪避比命中更有防御价值
- **设计依据**：防御性质，权重高于命中率

### 🎯 战力计算实例

#### 示例装备A：传说级剑类武器
```
物理攻击: 45点  → 45 × 1.0 = 45战力
暴击率: 12点    → 12 × 15 = 180战力  
暴击伤害: 0.25  → 0.25 × 800 = 200战力
命中率: 10点    → 10 × 8 = 80战力
速度: 8点       → 8 × 2.0 = 16战力
总战力: 521点
```

#### 示例装备B：史诗级防具
```
物理防御: 35点  → 35 × 0.8 = 28战力
生命值: 150点   → 150 × 0.08 = 12战力
闪避率: 8点     → 8 × 12 = 96战力
免暴率: 6点     → 6 × 12 = 72战力
总战力: 208点
```

### 📈 权重合理性验证

#### ✅ 符合游戏平衡性
- 攻击型装备战力高于防御型装备
- 稀有属性(暴击伤害)权重体现其价值
- 基础属性(攻防)依然是战力主要构成

#### ✅ 反映实际战斗影响  
- 暴击系统权重体现其战斗重要性
- 命中闪避权重符合实际公式影响
- 生存属性权重匹配数值规模

#### ✅ 避免极端情况
- 没有单一属性主导战力
- 特殊效果有上限，防止过高
- 各属性权重相互平衡