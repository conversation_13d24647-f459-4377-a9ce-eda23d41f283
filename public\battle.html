<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta charset="UTF-8" />
        <meta
            name="viewport"
            content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover"
        />

        <!-- 🔧 PWA模式底部白边强力修复 - 必须最早执行 -->
        <script src="assets/js/pwa-fix.js"></script>

        <!-- 🔧 项目配置文件 - 必须早期加载 -->
        <script src="assets/js/config.js"></script>

        <!-- 新增HBuilder X优化meta标签 -->
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
        <meta name="format-detection" content="telephone=no" />
        <!-- PWA支持 -->
        <link rel="manifest" href="manifest.json" />
        <meta name="theme-color" content="#d4af37" />

        <title>一念修仙 - 战斗系统</title>

        <!-- 技能动画CSS -->
        <link rel="stylesheet" href="assets/css/battle/skills/base-animations.css" />

        <!-- 其他技能CSS通过skill-loader动态加载 -->
        <!-- 移动端CSS已删除 -->
        <link rel="stylesheet" href="assets/css/global.css" />
        <link rel="stylesheet" href="assets/css/battle/battle-ui.css" />
        <link rel="stylesheet" href="assets/css/battle/battle.css" />
        <link rel="stylesheet" href="assets/css/item-detail-popup.css" />

        <!-- 🔧 视口高度修复脚本 -->
        <script src="assets/js/viewport-height-fix.js"></script>
        <!-- 🔑 全局登录检查系统 -->
        <script src="assets/js/auth-check.js"></script>
        <!-- 🎵 全局音乐管理器 -->
        <script src="assets/js/global-music-manager.js"></script>
    </head>
    <body>
        <div class="battle-ui-container">
            <!-- 顶部信息栏 -->
            <div class="battle-header">
                <div class="round-display" id="round-display" style="display: none">回合 1/30</div>
                <div class="area-info">
                    <span id="area-name">长安</span>
                    <span id="area-progress">0/300关</span>
                </div>
                <div class="return-button" onclick="returnToMap()" style="display: none">返回</div>
                <div class="battle-status">准备战斗...</div>
            </div>

            <!-- 战斗场景 -->
            <div class="battle-container">
                <div class="effects-container"></div>

                <div class="character enemy">
                    <div class="character-info">
                        <div class="character-name" id="enemy-name">敌人</div>
                        <div class="character-level" id="enemy-level">境界</div>
                    </div>
                    <div class="character-sprite"></div>
                    <div class="hp-bar">
                        <div class="hp-fill"></div>
                        <div class="hp-text">
                            <span id="enemy-hp">400</span>/<span id="enemy-max-hp">400</span>
                            <span id="enemy-shield" class="shield-display"></span>
                        </div>
                    </div>
                    <div class="mp-bar">
                        <div class="mp-fill"></div>
                        <div class="mp-text"><span id="enemy-mp">50</span>/<span id="enemy-max-mp">50</span></div>
                    </div>
                </div>

                <div class="character player">
                    <div class="character-info">
                        <div class="character-name" id="player-name">修仙者</div>
                        <div class="character-level" id="player-level">境界</div>
                    </div>
                    <div class="character-sprite"></div>
                    <div class="hp-bar">
                        <div class="hp-fill"></div>
                        <div class="hp-text">
                            <span id="player-hp">300</span>/<span id="player-max-hp">300</span>
                            <span id="player-shield" class="shield-display"></span>
                        </div>
                    </div>
                    <div class="mp-bar">
                        <div class="mp-fill"></div>
                        <div class="mp-text"><span id="player-mp">100</span>/<span id="player-max-mp">100</span></div>
                    </div>
                </div>
            </div>
            <!-- 战斗状态 -->
            <div class="battle-status-container">
                <div class="skill-info" id="current-skill">当前技能：准备中</div>
                <div
                    class="adventure-info"
                    id="adventure-info"
                    style="
                        color: #f39c12;
                        font-size: 12px;
                        text-align: center;
                        margin: 5px 0;
                        text-shadow: 0 0 3px rgba(243, 156, 18, 0.5);
                    "
                >
                    🎲 奇遇值：加载中...
                </div>

                <!-- 武器信息 -->
                <div class="weapon-display">
                    <div class="weapon-list" id="weapon-list">
                        <div class="weapon-item empty">空</div>
                        <div class="weapon-item empty">空</div>
                        <div class="weapon-item empty">空</div>
                        <div class="weapon-item empty">空</div>
                        <div class="weapon-item empty">空</div>
                        <div class="weapon-item empty">空</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 🔧 新增：胜利面板模板 -->
        <template id="victory-panel-template">
            <div class="game-overlay">
                <div class="victory-panel">
                    <div class="victory-title" data-victory-title>🎉 胜利！</div>
                    <div class="victory-subtitle" data-victory-subtitle>战斗胜利！</div>

                    <!-- 奖励显示区域 -->
                    <div class="rewards-section" data-rewards-section>
                        <div class="rewards-title">🎉 战斗奖励</div>

                        <div class="rewards-grid">
                            <div class="reward-item">
                                <div class="reward-info">
                                    <div class="reward-label">🔹 灵石</div>
                                    <div class="reward-value" data-exp-reward>+0</div>
                                </div>
                            </div>
                            <div class="reward-item">
                                <div class="reward-info">
                                    <div class="reward-label">💰 金币</div>
                                    <div class="reward-value" data-gold-reward>+0</div>
                                </div>
                            </div>
                            <div class="reward-item">
                                <div class="reward-info">
                                    <div class="reward-label">🎲 奇遇值</div>
                                    <div class="reward-value" data-adventure-reward>+0</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 掉落物品区域 -->
                    <div class="drops-section" data-drops-section>
                        <div class="drops-title" data-drops-title>💎 获得战利品</div>
                        <div class="drops-list" data-drops-list>
                            <!-- 掉落物品将在这里动态生成 -->
                        </div>
                    </div>

                    <!-- 🔧 新增：回收警告区域 -->
                    <div
                        class="recycle-warning"
                        data-recycle-warning
                        style="
                            display: none;
                            background: rgba(220, 20, 60, 0.2);
                            border: 1px solid rgba(220, 20, 60, 0.5);
                            border-radius: 6px;
                            padding: 8px;
                            margin-bottom: 10px;
                            text-align: center;
                            color: #ff6b6b;
                            font-size: 12px;
                            font-weight: bold;
                            text-shadow: 0 0 4px rgba(220, 20, 60, 0.8);
                            animation: warningPulse 2s ease-in-out infinite;
                        "
                    >
                        ⚠️ 背包空间不足，已自动回收装备获得金币
                    </div>

                    <!-- 按钮区域 -->
                    <div class="victory-buttons">
                        <button class="victory-button exit-battle-button" data-exit-battle-button>
                            <span>退出战斗</span>
                        </button>
                        <button class="victory-button auto-battle-button" data-auto-battle-button>
                            <span data-auto-battle-text>开始挂机</span>
                            <div class="auto-battle-countdown" data-auto-battle-countdown></div>
                        </button>
                        <button class="victory-button next-stage-button" data-next-stage-button>
                            <span>下一层</span>
                        </button>
                    </div>

                    <!-- 🆕 循环挂机模式提示 - 独立一行 -->
                    <div
                        class="auto-battle-mode-hint"
                        data-auto-battle-mode-hint
                        style="
                            display: none;
                            font-size: 11px;
                            color: #ffa500;
                            text-align: center;
                            margin-top: 8px;
                            text-shadow: 0 0 4px rgba(255, 165, 0, 0.6);
                            animation: hintGlow 2s ease-in-out infinite alternate;
                        "
                    >
                        （循环挂机模式开启）
                    </div>
                </div>
            </div>
        </template>

        <!-- 掉落物品模板 -->
        <template id="drop-item-template">
            <div class="drop-item" data-drop-item>
                <div class="drop-icon" data-drop-icon></div>
                <div class="drop-name" data-drop-name>物品名称</div>
                <div class="drop-quantity" data-drop-quantity>x1</div>
            </div>
        </template>

        <!-- 引入战斗数据管理器 -->
        <script src="assets/js/realm-system.js"></script>
        <script src="assets/js/item-detail-popup.js"></script>
        <!-- 🔧 新增：引入通用属性变化浮窗组件 -->
        <script src="assets/js/attribute-changes-popup.js"></script>

        <!-- 🎛️ 全局调试开关（必须最先加载） -->
        <script src="assets/js/global-debug-switch.js"></script>

        <!-- 🔧 调试配置（在全局开关之后加载） -->
        <script src="assets/js/battle/utils/debug-config.js"></script>

        <!-- 🔥 基础工具和核心模块（注意顺序） -->
        <script src="assets/js/battle/utils/error-handler.js"></script>
        <script src="assets/js/battle/utils/battle-utils.js"></script>

        <!-- 🔧 工具类模块 (任务7新增) -->
        <script src="assets/js/battle/utils/data-utils.js"></script>
        <script src="assets/js/battle/utils/item-utils.js"></script>
        <script src="assets/js/battle/utils/effect-utils.js"></script>
        <script src="assets/js/battle/utils/weapon-utils.js"></script>

        <!-- 🧠 内存管理器 (性能优化新增) -->
        <script src="assets/js/battle/utils/memory-manager.js"></script>

        <!-- 🔧 调试面板 (功能增强新增) -->
        <script src="assets/js/battle/utils/debug-panel.js"></script>

        <!-- 🏗️ 管理器注册中心 (架构重构新增) -->
        <script src="assets/js/battle/core/manager-registry.js"></script>

        <!-- 🔥 核心系统组件（注意顺序） -->
        <script src="assets/js/battle/core/battle-system.js"></script>
        <script src="assets/js/battle/core/battle-state-machine.js"></script>
        <script src="assets/js/battle/core/character.js"></script>

        <!-- 🚀 AJAX管理器（优先加载） -->
        <script src="assets/js/ajax-manager.js"></script>

        <!-- 🔥 管理器和计算器（注意顺序） -->
        <script src="assets/js/battle/image-path-manager.js"></script>
        <script src="assets/js/battle/battle-combat-calculator.js"></script>
        <script src="assets/js/battle/battle-manager.js"></script>
        <script src="assets/js/battle/managers/ui-manager.js"></script>
        <script src="assets/js/battle/managers/battle-flow-manager.js"></script>
        <script src="assets/js/battle/managers/reward-manager.js"></script>
        <script src="assets/js/battle/managers/victory-panel-manager.js"></script>
        <script src="assets/js/battle/managers/auto-battle-manager.js"></script>

        <!-- 🔥 技能系统模块 -->
        <script src="assets/js/battle/skills/base-skill.js"></script>
        <script src="assets/js/battle/skills/skill-config.js"></script>
        <script src="assets/js/battle/skills/skill-loader.js"></script>

        <!-- 其他技能模块通过skill-loader动态加载，避免重复声明错误 -->

        <!-- 🔥 主战斗系统（最后加载） -->
        <script src="assets/js/battle/script.js"></script>

        <!-- 初始化战斗系统 -->
        <script>
            // 🔧 全局错误处理，防止异步监听器错误
            window.addEventListener('error', function (event) {
                if (event.message && event.message.includes('message channel closed')) {
                    console.warn('⚠️ 检测到浏览器扩展相关错误，已忽略:', event.message);
                    event.preventDefault();
                    return false;
                }
            });

            window.addEventListener('unhandledrejection', function (event) {
                if (event.reason && event.reason.message && event.reason.message.includes('message channel closed')) {
                    console.warn('⚠️ 检测到未处理的Promise拒绝（浏览器扩展相关），已忽略:', event.reason.message);
                    event.preventDefault();
                    return false;
                }
            });

            // 🔧 移除DOMContentLoaded中的实例创建，统一在load事件中处理
            document.addEventListener('DOMContentLoaded', function () {
                // 确保所有依赖都已加载
                if (typeof BattleSystem === 'undefined') {
                    console.error('BattleSystem未加载！');
                    return;
                }
                if (typeof BattleStateMachine === 'undefined') {
                    console.error('BattleStateMachine未加载！');
                    return;
                }

                if (window.BattleDebugConfig) {
                    window.BattleDebugConfig.log('battle-manager', '✅ DOM已加载，战斗系统依赖检查通过');
                }
            });
        </script>

        <script>
            // 全局变量初始化
            let currentStage = 1;
            let battleDrops = [];
            let spiritStoneGained = 0;
            let goldGained = 0;
            let currentMonster = null;
            let nextStage = 0;

            // 等待所有资源加载完成
            window.addEventListener('load', async function () {
                if (window.BattleDebugConfig) {
                    window.BattleDebugConfig.log('battle-manager', '页面完全加载，开始初始化...');
                }

                // 🏆 检查竞技场模式并处理界面
                const urlParams = new URLSearchParams(window.location.search);
                const isArenaMode = urlParams.get('arena') === '1';

                if (isArenaMode) {
                    console.log('🏆 竞技场模式检测');

                    // 🔧 新增：竞技场次数验证，防止F5刷新绕过检查
                    try {
                        const arenaResponse = await fetch(
                            window.GameConfig
                                ? window.GameConfig.getApiUrl('immortal_arena.php?action=verify_arena_access')
                                : '../src/api/immortal_arena.php?action=verify_arena_access',
                            {
                                method: 'GET',
                                credentials: 'same-origin',
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                            }
                        );
                        const arenaData = await arenaResponse.json();

                        if (!arenaData.success || arenaData.remaining_attempts <= 0) {
                            console.log('❌ 竞技场次数验证失败，强制返回竞技场页面');
                            alert('挑战次数不足，无法进行竞技场战斗！');
                            window.location.href = 'immortal_arena.html';
                            return;
                        }

                        console.log('✅ 竞技场次数验证通过，剩余次数:', arenaData.remaining_attempts);
                    } catch (error) {
                        console.error('❌ 竞技场次数验证失败:', error);
                        alert('无法验证竞技场权限，请返回重新匹配！');
                        window.location.href = 'immortal_arena.html';
                        return;
                    }

                    // 设置地图信息为竞技场
                    const areaNameElement = document.getElementById('area-name');
                    const areaProgressElement = document.getElementById('area-progress');
                    if (areaNameElement) areaNameElement.textContent = '升仙大会';
                    if (areaProgressElement) areaProgressElement.textContent = '';

                    // 🔧 竞技场模式下隐藏返回按钮
                    const returnButton = document.querySelector('.return-button');
                    if (returnButton) {
                        returnButton.style.display = 'none';
                        console.log('🔧 竞技场模式：已隐藏返回按钮');
                    }

                    const adventureInfo = document.getElementById('adventure-info');
                    if (adventureInfo) {
                        adventureInfo.style.display = 'none';
                        console.log('🔧 竞技场模式：已隐藏奇遇值显示');
                    }

                    // 🔧 竞技场模式下也要显示回合数，所以不隐藏回合显示
                }

                // 🚀 第一步：初始化AJAX管理器
                if (typeof AjaxManager !== 'undefined') {
                    window.ajaxManager = new AjaxManager();
                    console.log('✅ AJAX管理器初始化完成');
                } else {
                    console.error('❌ AJAX管理器未加载！');
                }

                // 🆕 第一步：检查是否从地图界面进入，只有从地图进入才清除数据
                const fromMap = sessionStorage.getItem('fromMap') === 'true';
                if (fromMap) {
                    console.log('🗑️ 从地图界面进入战斗，清除所有战斗相关数据...');
                    localStorage.removeItem('autoBattleMode');
                    localStorage.removeItem('autoBattleFailedFlag');
                    localStorage.removeItem('battleStartTime');
                    localStorage.removeItem('lastBattleResult');
                    localStorage.removeItem('battleTimeout');
                    // 清除标识，避免重复清除
                    sessionStorage.removeItem('fromMap');
                    console.log('✅ 战斗相关数据已清除');
                } else {
                    console.log('🔄 战斗界面内刷新，保持现有挂机状态');
                }

                // 🔧 新增：创建战斗系统实例
                try {
                    window.battleSystem = new BattleSystem();
                    console.log('✅ 战斗系统实例创建成功');

                    // 🆕 检查挂机失败标识并更新按钮文字
                    const hasFailedFlag = localStorage.getItem('autoBattleFailedFlag') === 'true';
                    console.log('🚩 页面加载时检查失败标识:', hasFailedFlag);

                    // 等待DOM元素和战斗系统完全加载后更新按钮文字
                    setTimeout(() => {
                        if (window.battleSystem && window.battleSystem.autoBattleManager) {
                            // 刷新失败标识状态并更新按钮文字
                            window.battleSystem.autoBattleManager.refreshFailedFlagFromStorage();
                            console.log('✅ 使用挂机管理器更新按钮文字');
                        } else {
                            // 备用方案：直接更新按钮文字和提示
                            const autoBattleText = document.querySelector('[data-auto-battle-text]');
                            const modeHint = document.querySelector('[data-auto-battle-mode-hint]');

                            if (autoBattleText) {
                                autoBattleText.textContent = '开始挂机'; // 固定显示
                            }

                            if (modeHint && hasFailedFlag) {
                                modeHint.style.display = 'block';
                                console.log('✅ 备用方案：显示循环挂机模式提示');
                            } else if (modeHint) {
                                modeHint.style.display = 'none';
                            }
                        }
                    }, 200);
                } catch (error) {
                    console.error('❌ 战斗系统实例创建失败:', error);
                    return;
                }

                // 🔧 新增：ItemDetailPopup组件初始化
                if (typeof ItemDetailPopup !== 'undefined') {
                    // ItemDetailPopup会在实例化时自动创建HTML模板
                    const itemDetailPopup = ItemDetailPopup.getInstance();
                    console.log('✅ ItemDetailPopup组件初始化完成');
                } else {
                    console.error('❌ ItemDetailPopup组件未加载！');
                }

                // 🔧 新增：确保图片路径管理器可用
                if (window.ImagePathManager) {
                    console.log('🖼️ 开始预加载关键图片...');
                    await window.ImagePathManager.preloadImages();
                } else {
                    console.error('❌ 图片路径管理器未加载！');
                }

                // 🔥 新增：初始化技能系统
                if (window.SkillLoader) {
                    console.log('🎯 初始化技能加载器...');
                    window.skillLoader = new SkillLoader();
                    await window.skillLoader.preloadBaseModules();
                    console.log('✅ 技能系统预加载完成');
                } else {
                    console.error('❌ 技能加载器类未加载！');
                }

                // 🧠 新增：初始化内存管理器
                if (window.BattleMemoryManager) {
                    console.log('🧠 初始化内存管理器...');
                    window.battleMemoryManager = new BattleMemoryManager();
                    window.battleMemoryManager.setManagers(
                        window.skillLoader,
                        window.ImagePathManager,
                        window.battleSystem?.dataManager
                    );
                    console.log('✅ 内存管理器初始化完成');
                } else {
                    console.error('❌ 内存管理器类未加载！');
                }

                // 🏗️ 新增：注册管理器到注册中心
                if (window.ManagerRegistry && window.battleSystem) {
                    console.log('🏗️ 注册战斗系统管理器...');

                    // 注册数据管理器（无依赖）
                    window.ManagerRegistry.register('dataManager', window.battleSystem.dataManager, []);

                    // 注册UI管理器（依赖数据管理器）
                    window.ManagerRegistry.register('uiManager', window.battleSystem.uiManager, ['dataManager']);

                    // 注册战斗流程管理器（依赖数据管理器和UI管理器）
                    window.ManagerRegistry.register('battleFlowManager', window.battleSystem.battleFlowManager, [
                        'dataManager',
                        'uiManager',
                    ]);

                    // 注册奖励管理器（依赖数据管理器和UI管理器）
                    window.ManagerRegistry.register('rewardManager', window.battleSystem.rewardManager, [
                        'dataManager',
                        'uiManager',
                    ]);

                    // 注册胜利面板管理器（依赖数据管理器、UI管理器和奖励管理器）
                    window.ManagerRegistry.register('victoryPanelManager', window.battleSystem.victoryPanelManager, [
                        'dataManager',
                        'uiManager',
                        'rewardManager',
                    ]);

                    // 注册挂机管理器（依赖数据管理器）
                    window.ManagerRegistry.register('autoBattleManager', window.battleSystem.autoBattleManager, [
                        'dataManager',
                    ]);

                    // 注册工具管理器
                    if (window.skillLoader) {
                        window.ManagerRegistry.register('skillLoader', window.skillLoader, []);
                    }
                    if (window.ImagePathManager) {
                        window.ManagerRegistry.register('imagePathManager', window.ImagePathManager, []);
                    }
                    if (window.battleMemoryManager) {
                        window.ManagerRegistry.register('memoryManager', window.battleMemoryManager, [
                            'skillLoader',
                            'imagePathManager',
                            'dataManager',
                        ]);
                    }

                    // 初始化所有管理器
                    await window.ManagerRegistry.initializeAll();
                    console.log('✅ 管理器注册中心初始化完成');
                }

                // 🔧 修复：使用已创建的战斗系统实例
                if (window.battleSystem) {
                    // 🔥 新增：将战斗系统与技能加载器关联
                    if (window.skillLoader) {
                        window.skillLoader.setBattleSystem(window.battleSystem);
                        console.log('🔗 技能系统与战斗系统关联完成');
                    }

                    // 🔧 新增：为控制台调试提供全局访问
                    window.SkillLoaderInstance = window.skillLoader;

                    // 🔧 新增：详细调试信息
                    console.log('🔍 战斗系统初始化前状态检查:');
                    console.log('  - battleSystem存在:', !!window.battleSystem);
                    console.log('  - 数据管理器存在:', !!window.battleSystem.dataManager);
                    console.log('  - UI管理器存在:', !!window.battleSystem.uiManager);
                    console.log('  - 状态机存在:', !!window.battleSystem.stateMachine);

                    await window.battleSystem.initialize();
                    console.log('✅ 战斗系统初始化完成');

                    // 🔧 新增：初始化后状态检查
                    console.log('🔍 战斗系统初始化后状态检查:');
                    console.log('  - 数据管理器存在:', !!window.battleSystem.dataManager);
                    console.log('  - 玩家属性存在:', !!window.battleSystem.playerStats);
                    console.log('  - 敌人数据存在:', !!window.battleSystem.dataManager?.enemyData);
                    console.log('  - 技能序列存在:', !!window.battleSystem.skillSequence);
                    console.log('  - 技能序列长度:', window.battleSystem.skillSequence?.length);
                } else {
                    console.error('❌ 战斗系统实例未找到！');
                }

                // 🆕 创建全局测试函数，方便控制台调试
                window.testAutoBattleFlag = {
                    set: () => {
                        localStorage.setItem('autoBattleFailedFlag', 'true');
                        if (window.battleSystem && window.battleSystem.autoBattleManager) {
                            window.battleSystem.autoBattleManager.refreshFailedFlagFromStorage();
                        }
                        console.log('✅ 设置挂机失败标识并更新按钮');
                    },
                    clear: () => {
                        localStorage.removeItem('autoBattleFailedFlag');
                        if (window.battleSystem && window.battleSystem.autoBattleManager) {
                            window.battleSystem.autoBattleManager.refreshFailedFlagFromStorage();
                        }
                        console.log('✅ 清除挂机失败标识并更新按钮');
                    },
                    check: () => {
                        const flag = localStorage.getItem('autoBattleFailedFlag') === 'true';
                        const modeHint = document.querySelector('[data-auto-battle-mode-hint]');
                        const isHintVisible = modeHint ? modeHint.style.display !== 'none' : false;
                        console.log('🔍 当前失败标识状态:', flag);
                        console.log('🔍 循环模式提示显示:', isHintVisible);
                        if (window.battleSystem && window.battleSystem.autoBattleManager) {
                            console.log(
                                '🔍 管理器中的状态:',
                                window.battleSystem.autoBattleManager.hasAutoBattleFailedFlag()
                            );
                        }
                        return { flag, isHintVisible };
                    },
                    update: () => {
                        if (window.battleSystem && window.battleSystem.autoBattleManager) {
                            window.battleSystem.autoBattleManager.updateAutoBattleButtonText();
                            console.log('✅ 手动更新按钮显示');
                        } else {
                            console.error('❌ 战斗系统未初始化');
                        }
                    },
                };

                // 🎵 启动战斗音乐
                setTimeout(() => {
                    if (window.musicManager) {
                        window.musicManager.playBattleMusic();
                    }
                }, 500);

                console.log('🎮 战斗系统初始化完成，准备开始战斗！');
                console.log(
                    '🧪 测试函数已创建：testAutoBattleFlag.set(), testAutoBattleFlag.clear(), testAutoBattleFlag.check(), testAutoBattleFlag.update()'
                );
            });

            // 🔧 新增：页面可见性变化时刷新图片
            document.addEventListener('visibilitychange', function () {
                if (!document.hidden) {
                    console.log('页面重新可见，检查图片状态...');
                    // 检查所有图片是否正常加载
                    const images = document.querySelectorAll('img');
                    images.forEach(img => {
                        if (img.naturalWidth === 0) {
                            console.log('重新加载图片:', img.src);
                            img.src = img.src + '?t=' + Date.now();
                        }
                    });
                }
            });

            // 从地图代码获取地图名称
            function getMapNameFromCode(mapCode) {
                const mapNames = {
                    taiyifeng: '太乙峰',
                    bishuihantan: '碧水寒潭',
                    chiyanggu: '赤焰谷',
                    youmingguiyu: '幽冥鬼域',
                    qingyunxianshan: '青云仙山',
                    guzhanchang: '古战场',
                    hunyuanxukong: '混元虚空',
                    honghuangmijing: '洪荒秘境',
                };

                return mapNames[mapCode] || '未知地图';
            }

            // 从地图ID获取当前进度
            function getProgressFromMapId(mapId) {
                // 尝试从localStorage获取
                const storedProgress = localStorage.getItem('map_progress');
                if (storedProgress) {
                    try {
                        const progressData = JSON.parse(storedProgress);
                        if (progressData && progressData.map_id == mapId) {
                            return progressData.current_stage || 1;
                        }
                    } catch (error) {
                        console.error('解析进度数据失败:', error);
                    }
                }

                return 1; // 默认从第1关开始
            }

            // 从地图ID获取最大关卡数
            function getMaxStagesFromMapId(mapId) {
                // 尝试从localStorage获取
                const storedMap = localStorage.getItem('current_map');
                if (storedMap) {
                    try {
                        const mapData = JSON.parse(storedMap);
                        if (mapData && mapData.id == mapId) {
                            return mapData.max_stages || 100;
                        }
                    } catch (error) {
                        console.error('解析地图数据失败:', error);
                    }
                }

                // 🔧 更新：基于数据库实际关卡数的地图关卡配置
                const mapStages = {
                    1: 60, // 太乙峰
                    2: 90, // 碧水寒潭
                    3: 120, // 赤焰谷
                    4: 140, // 幽冥鬼域
                    5: 150, // 青云仙山
                    6: 140, // 星辰古战场
                    7: 140, // 混元虚空
                    8: 175, // 洪荒秘境
                };

                return mapStages[mapId] || 100;
            }

            // 返回地图
            function returnToMap() {
                // 🏆 检查是否为竞技场模式
                const urlParams = new URLSearchParams(window.location.search);
                const isArena = urlParams.get('arena') === '1';

                if (isArena) {
                    // 🏆 竞技场模式：返回竞技场
                    if (confirm('确定要退出竞技场吗？')) {
                        // 🎵 切换回背景音乐
                        if (window.musicManager) {
                            window.musicManager.playBackgroundMusic();
                        }
                        window.location.href = 'immortal_arena.html';
                    }
                } else {
                    // 🗺️ 普通模式：返回地图
                    if (confirm('确定要返回地图吗？当前战斗进度将会丢失。')) {
                        // 🎵 切换回背景音乐
                        if (window.musicManager) {
                            window.musicManager.playBackgroundMusic();
                        }
                        window.location.href = 'adventure.html';
                    }
                }
            }

            // 禁用双击缩放
            document.addEventListener('dblclick', function (e) {
                e.preventDefault();
            });

            // 禁用页面滚动
            document.body.addEventListener(
                'touchmove',
                function (e) {
                    e.preventDefault();
                },
                { passive: false }
            );

            // 禁用长按菜单
            document.body.addEventListener('contextmenu', function (e) {
                e.preventDefault();
            });

            // 显示加载状态
            function showLoader() {
                const loaderDiv = document.createElement('div');
                loaderDiv.className = 'loader-overlay';
                loaderDiv.innerHTML = '<div class="loader"></div><div class="loader-text">加载中...</div>';
                document.body.appendChild(loaderDiv);
            }

            // 隐藏加载状态
            function hideLoader() {
                const loader = document.querySelector('.loader-overlay');
                if (loader) {
                    loader.remove();
                }
            }

            // 显示消息
            function showMessage(message, type = 'info') {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message-box ${type}`;
                messageDiv.textContent = message;
                document.body.appendChild(messageDiv);

                setTimeout(() => {
                    messageDiv.style.opacity = '0';
                    setTimeout(() => {
                        messageDiv.remove();
                    }, 500);
                }, 3000);
            }
        </script>
    </body>
</html>
