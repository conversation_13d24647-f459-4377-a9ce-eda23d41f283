<?php

/**
 * 功法碎片合成系统API
 * 处理功法碎片的合成、查看可合成功法、获取碎片列表等功能
 * 作者：一念修仙装备系统开发团队
 * 日期：2024年12月19日
 */

// 错误处理设置
ini_set('display_errors', 0);
error_reporting(E_ALL);

require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/auth.php';

// 检查维护模式
if (isMaintenanceMode()) {
    echo json_encode([
        'success' => false,
        'message' => '游戏正在维护中，请稍后再试',
        'maintenance' => true
    ]);
    exit;
}

// 记录API调用（如果开启了调试）
if (DEBUG_LOG_API_CALLS) {
    writeLog("API调用: technique_fragment_synthesis.php", 'DEBUG', 'api.log');
}

// 设置JSON响应头
header('Content-Type: application/json; charset=utf-8');

try {
    // 检查用户是否登录
    if (!isLoggedIn()) {
        echo json_encode([
            'success' => false,
            'message' => '请先登录'
        ]);
        exit;
    }

    $characterId = get_character_id();
    if (!$characterId) {
        echo json_encode([
            'success' => false,
            'message' => '未找到角色信息，请先创建角色'
        ]);
        exit;
    }

    $action = isset($_GET['action']) ? $_GET['action'] : (isset($_POST['action']) ? $_POST['action'] : '');

    // 获取数据库连接
    $pdo = getDatabase();
    if (!$pdo) {
        echo json_encode(['success' => false, 'message' => '数据库连接失败']);
        exit;
    }

    switch ($action) {
        case 'get_fragments':
            getTechniqueFragments($pdo, $characterId);
            break;

        case 'get_synthesis_recipes':
            getSynthesisRecipes($pdo, $characterId);
            break;

        case 'synthesize_technique':
            synthesizeTechnique($pdo, $characterId, $_POST);
            break;

        default:
            echo json_encode([
                'success' => false,
                'message' => '无效的操作'
            ]);
            break;
    }
} catch (Exception $e) {
    error_log("功法碎片合成API错误: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => '系统错误: ' . $e->getMessage()
    ]);
}

/**
 * 获取角色背包中的功法碎片
 */
function getTechniqueFragments($pdo, $characterId)
{
    try {
        $stmt = $pdo->prepare("
            SELECT 
                ui.id as inventory_id,
                ui.item_id,
                ui.quantity,
                gi.item_name,
                gi.item_code,
                gi.description,
                gi.rarity
            FROM user_inventories ui
            JOIN game_items gi ON ui.item_id = gi.id
            WHERE ui.character_id = ? 
            AND gi.item_subtype = 'technique_fragment'
            AND ui.quantity > 0
            ORDER BY gi.item_name
        ");
        $stmt->execute([$characterId]);
        $fragments = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo json_encode([
            'success' => true,
            'fragments' => $fragments
        ]);
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => '获取功法碎片失败: ' . $e->getMessage()
        ]);
    }
}

/**
 * 获取可合成的功法配方
 */
function getSynthesisRecipes($pdo, $characterId)
{
    try {
        // 从crafting_recipes表获取功法合成配方
        $stmt = $pdo->prepare("
            SELECT 
                cr.id as recipe_id,
                cr.recipe_name,
                cr.result_item_id,
                cr.result_quantity,
                cr.materials,
                cr.description,
                gi.item_name as result_item_name,
                gi.item_code as result_item_code,
                gi.rarity as result_rarity
            FROM crafting_recipes cr
            JOIN game_items gi ON cr.result_item_id = gi.id
            WHERE cr.recipe_name LIKE '%秘籍合成%'
            AND cr.is_active = 1
            ORDER BY cr.id
        ");
        $stmt->execute();
        $recipes = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // 获取用户背包中的功法碎片数量
        $stmt = $pdo->prepare("
            SELECT 
                ui.item_id,
                ui.quantity,
                gi.item_name
            FROM user_inventories ui
            JOIN game_items gi ON ui.item_id = gi.id
            WHERE ui.character_id = ? 
            AND gi.item_subtype = 'technique_fragment'
            AND ui.quantity > 0
        ");
        $stmt->execute([$characterId]);
        $userFragments = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // 创建用户碎片数量映射
        $fragmentQuantities = [];
        foreach ($userFragments as $fragment) {
            $fragmentQuantities[$fragment['item_id']] = $fragment['quantity'];
        }

        // 处理配方数据，添加用户拥有的材料数量和可合成状态
        foreach ($recipes as &$recipe) {
            $materials = json_decode($recipe['materials'], true);
            $canSynthesize = true;

            foreach ($materials as &$material) {
                $material['owned_quantity'] = isset($fragmentQuantities[$material['item_id']])
                    ? $fragmentQuantities[$material['item_id']] : 0;

                // 获取材料名称
                $materialStmt = $pdo->prepare("SELECT item_name FROM game_items WHERE id = ?");
                $materialStmt->execute([$material['item_id']]);
                $materialInfo = $materialStmt->fetch(PDO::FETCH_ASSOC);
                $material['item_name'] = $materialInfo ? $materialInfo['item_name'] : '未知物品';

                if ($material['owned_quantity'] < $material['quantity']) {
                    $canSynthesize = false;
                }
            }

            $recipe['materials'] = $materials;
            $recipe['can_synthesize'] = $canSynthesize;
        }

        echo json_encode([
            'success' => true,
            'recipes' => $recipes,
            'user_fragments' => $userFragments
        ]);
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => '获取合成配方失败: ' . $e->getMessage()
        ]);
    }
}

/**
 * 合成功法
 */
function synthesizeTechnique($pdo, $characterId, $postData)
{
    try {
        $recipeId = isset($postData['recipe_id']) ? intval($postData['recipe_id']) : 0;

        if (!$recipeId) {
            echo json_encode([
                'success' => false,
                'message' => '请选择要合成的配方'
            ]);
            return;
        }

        // 获取配方信息
        $stmt = $pdo->prepare("
            SELECT 
                cr.*,
                gi.item_name as result_item_name
            FROM crafting_recipes cr
            JOIN game_items gi ON cr.result_item_id = gi.id
            WHERE cr.id = ? AND cr.is_active = 1
        ");
        $stmt->execute([$recipeId]);
        $recipe = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$recipe) {
            echo json_encode([
                'success' => false,
                'message' => '配方不存在或已禁用'
            ]);
            return;
        }

        $materials = json_decode($recipe['materials'], true);

        // 开始事务
        $pdo->beginTransaction();

        // 检查材料是否充足并扣除
        foreach ($materials as $material) {
            $stmt = $pdo->prepare("
                SELECT quantity FROM user_inventories 
                WHERE character_id = ? AND item_id = ?
            ");
            $stmt->execute([$characterId, $material['item_id']]);
            $userItem = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$userItem || $userItem['quantity'] < $material['quantity']) {
                $pdo->rollBack();
                echo json_encode([
                    'success' => false,
                    'message' => '材料不足，无法合成'
                ]);
                return;
            }

            // 扣除材料
            $stmt = $pdo->prepare("
                UPDATE user_inventories 
                SET quantity = quantity - ? 
                WHERE character_id = ? AND item_id = ?
            ");
            $stmt->execute([$material['quantity'], $characterId, $material['item_id']]);
        }

        // 获取当前最高的sort_weight值，让新合成的物品排在最前面
        $stmt = $pdo->prepare("SELECT MAX(sort_weight) as max_weight FROM user_inventories WHERE character_id = ?");
        $stmt->execute([$characterId]);
        $maxWeight = $stmt->fetchColumn();
        $newSortWeight = ($maxWeight ? $maxWeight : 0) + 1000; // 确保新物品排在最前面

        // 检查是否已拥有该功法秘籍
        $stmt = $pdo->prepare("
            SELECT quantity FROM user_inventories 
            WHERE character_id = ? AND item_id = ?
        ");
        $stmt->execute([$characterId, $recipe['result_item_id']]);
        $existingItem = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($existingItem) {
            // 增加数量并更新排序权重
            $stmt = $pdo->prepare("
                UPDATE user_inventories 
                SET quantity = quantity + ?, sort_weight = ? 
                WHERE character_id = ? AND item_id = ?
            ");
            $stmt->execute([$recipe['result_quantity'], $newSortWeight, $characterId, $recipe['result_item_id']]);
        } else {
            // 获取物品类型信息
            $stmt = $pdo->prepare("SELECT item_type FROM game_items WHERE id = ?");
            $stmt->execute([$recipe['result_item_id']]);
            $itemInfo = $stmt->fetch(PDO::FETCH_ASSOC);
            $itemType = $itemInfo ? $itemInfo['item_type'] : 'consumable';

            // 添加新物品
            $stmt = $pdo->prepare("
                INSERT INTO user_inventories (character_id, item_id, item_type, quantity, sort_weight, obtained_time) 
                VALUES (?, ?, ?, ?, ?, NOW())
            ");
            $stmt->execute([$characterId, $recipe['result_item_id'], $itemType, $recipe['result_quantity'], $newSortWeight]);
        }

        // 计算消耗的碎片总数
        $totalFragmentsUsed = 0;
        foreach ($materials as $material) {
            $totalFragmentsUsed += $material['quantity'];
        }

        // 提交事务
        $pdo->commit();

        echo json_encode([
            'success' => true,
            'message' => "成功合成 {$recipe['result_item_name']} x{$recipe['result_quantity']}",
            'technique_name' => $recipe['result_item_name'],
            'fragments_used' => $totalFragmentsUsed,
            'result_quantity' => $recipe['result_quantity']
        ]);
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        echo json_encode([
            'success' => false,
            'message' => '合成失败: ' . $e->getMessage()
        ]);
    }
}
