<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>兑换码管理 - 一念修仙</title>

    <!-- 🔧 项目配置文件 - 必须早期加载 -->
    <script src="../assets/js/config.js"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            min-height: 100vh;
            color: white;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #ffd700;
            font-size: 28px;
            margin-bottom: 10px;
        }

        .section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 215, 0, 0.3);
        }

        .section h2 {
            color: #ffd700;
            margin-bottom: 15px;
            font-size: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #bdc3c7;
            font-weight: bold;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid rgba(255, 215, 0, 0.3);
            border-radius: 5px;
            background: rgba(0, 0, 0, 0.3);
            color: white;
            font-size: 14px;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #ffd700;
            box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .btn {
            background: linear-gradient(135deg, #27ae60, #219a52);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: linear-gradient(135deg, #219a52, #1e8449);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #3498db, #2980b9);
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, #2980b9, #21618c);
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.4);
        }

        .codes-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .codes-table th,
        .codes-table td {
            border: 1px solid rgba(255, 215, 0, 0.2);
            padding: 8px;
            text-align: left;
            font-size: 12px;
        }

        .codes-table th {
            background: rgba(255, 215, 0, 0.1);
            color: #ffd700;
            font-weight: bold;
        }

        .codes-table td {
            background: rgba(255, 255, 255, 0.05);
        }

        .status-active {
            color: #27ae60;
        }

        .status-inactive {
            color: #e74c3c;
        }

        .message {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }

        .message.success {
            background: linear-gradient(135deg, #27ae60, #219a52);
            border: 1px solid #27ae60;
        }

        .message.error {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            border: 1px solid #e74c3c;
        }

        .test-codes {
            background: rgba(255, 215, 0, 0.1);
            border: 1px solid rgba(255, 215, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
        }

        .test-codes h3 {
            color: #ffd700;
            margin-bottom: 10px;
        }

        .code-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 0;
            border-bottom: 1px solid rgba(255, 215, 0, 0.2);
        }

        .code-item:last-child {
            border-bottom: none;
        }

        .code-text {
            font-family: 'Courier New', monospace;
            background: rgba(0, 0, 0, 0.3);
            padding: 2px 6px;
            border-radius: 3px;
            color: #ffd700;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>兑换码管理系统</h1>
            <p>创建和管理游戏兑换码</p>
        </div>

        <!-- 创建兑换码 -->
        <div class="section">
            <h2>创建新兑换码</h2>
            <form id="createCodeForm">
                <div class="form-row">
                    <div class="form-group">
                        <label>兑换码</label>
                        <input type="text" id="code" placeholder="输入兑换码" required>
                    </div>
                    <div class="form-group">
                        <label>兑换码类型</label>
                        <select id="codeType">
                            <option value="general">通用</option>
                            <option value="vip">VIP专属</option>
                            <option value="event">活动</option>
                            <option value="test">测试</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>奖励类型</label>
                        <select id="rewardType" onchange="toggleRewardFields()">
                            <option value="mixed">混合奖励</option>
                            <option value="package">礼包</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>最大使用次数</label>
                        <input type="number" id="maxUses" value="1" min="0" placeholder="0表示不限总次数">
                    </div>
                </div>

                <div id="simpleReward" style="display: none;">
                    <div class="form-group">
                        <label>奖励数量</label>
                        <input type="number" id="rewardAmount" value="100" min="1">
                    </div>
                </div>

                <div id="mixedReward">
                    <div class="form-row">
                        <div class="form-group">
                            <label>灵石数量</label>
                            <input type="number" id="mixedGems" value="0" min="0">
                        </div>
                        <div class="form-group">
                            <label>金币数量</label>
                            <input type="number" id="mixedGold" value="0" min="0">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>物品ID（多个用逗号分隔）</label>
                            <input type="text" id="itemIds" placeholder="例如: 1,2,3" value="">
                        </div>
                        <div class="form-group">
                            <label>对应物品数量（多个用逗号分隔）</label>
                            <input type="text" id="itemQuantities" placeholder="例如: 1,2,1" value="">
                        </div>
                    </div>
                    <div class="form-group">
                        <label>灵气数量</label>
                        <input type="number" id="mixedExp" value="0" min="0">
                    </div>
                </div>

                <div class="form-group">
                    <label>过期时间（可选）</label>
                    <input type="datetime-local" id="expireTime">
                </div>

                <button type="submit" class="btn">创建兑换码</button>
            </form>
        </div>

        <!-- 现有兑换码列表 -->
        <div class="section">
            <h2>兑换码列表</h2>
            <button type="button" class="btn btn-secondary" onclick="loadExistingCodes()">刷新列表</button>
            <div id="codesContainer">
                <p style="color: #bdc3c7;">点击"刷新列表"查看现有兑换码</p>
            </div>
        </div>

        <!-- 测试兑换码 -->
        <div class="test-codes">
            <h3>测试兑换码</h3>
            <p style="color: #bdc3c7; margin-bottom: 15px;">以下是可以在游戏中测试的兑换码：</p>
            
            <div style="background: rgba(255, 215, 0, 0.1); padding: 10px; border-radius: 5px; margin-bottom: 15px; font-size: 12px;">
                <strong>说明：</strong><br>
                • <strong>通用</strong>：普通玩家可使用的兑换码<br>
                • <strong>VIP专属</strong>：只有VIP玩家才能使用<br>
                • <strong>活动</strong>：特定活动期间的兑换码<br>
                • <strong>测试</strong>：测试用途的兑换码<br>
                • <strong>最大使用次数</strong>：0表示不限总次数，可被无限多个用户使用
            </div>
            
            <div class="code-item">
                <span>新手礼包（混合奖励）:</span>
                <span class="code-text">WELCOME2024</span>
            </div>
            <div class="code-item">
                <span>新年灵石:</span>
                <span class="code-text">NEWYEAR100</span>
            </div>
            <div class="code-item">
                <span>春节礼包（单次）:</span>
                <span class="code-text">SPRING2024</span>
            </div>
            <div class="code-item">
                <span>金币奖励（不限总次数）:</span>
                <span class="code-text">GOLD500</span>
            </div>
            <div class="code-item">
                <span>装备礼包（包含物品）:</span>
                <span class="code-text">EQUIPMENT2024</span>
            </div>
            <div class="code-item">
                <span>豪华礼包（灵石+物品）:</span>
                <span class="code-text">LUXURY2024</span>
            </div>
        </div>
    </div>

    <script>
        // 切换奖励字段显示
        function toggleRewardFields() {
            const rewardType = document.getElementById('rewardType').value;
            const simpleReward = document.getElementById('simpleReward');
            const mixedReward = document.getElementById('mixedReward');
            
            // 现在只有混合奖励，始终显示混合奖励字段
                simpleReward.style.display = 'none';
                mixedReward.style.display = 'block';
        }

        // 创建兑换码
        document.getElementById('createCodeForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = {
                action: 'create',
                code: document.getElementById('code').value.trim(),
                code_type: document.getElementById('codeType').value,
                title: document.getElementById('code').value.trim(),
                description: '通过管理面板创建',
                max_uses: parseInt(document.getElementById('maxUses').value),
                valid_until: document.getElementById('expireTime').value || null
            };

            // 构建奖励数据
                formData.reward_data = {
                spirit_stones: parseInt(document.getElementById('mixedGems').value) || 0,
                    gold: parseInt(document.getElementById('mixedGold').value) || 0,
                description: document.getElementById('rewardType').value === 'package' ? '自定义礼包' : '混合奖励'
            };
            
            // 处理物品奖励
            const itemIds = document.getElementById('itemIds').value.trim();
            const itemQuantities = document.getElementById('itemQuantities').value.trim();
            
            if (itemIds) {
                const itemIdArray = itemIds.split(',').map(id => parseInt(id.trim())).filter(id => id > 0);
                const quantityArray = itemQuantities ? itemQuantities.split(',').map(q => parseInt(q.trim())).filter(q => q > 0) : [];
                
                if (itemIdArray.length > 0) {
                    formData.reward_data.item_id = itemIdArray;
                    
                    // 构建物品数量映射
                    const itemQuantityMap = {};
                    itemIdArray.forEach((itemId, index) => {
                        itemQuantityMap[itemId] = quantityArray[index] || 1; // 默认数量为1
                    });
                    formData.reward_data.item_quantity = itemQuantityMap;
                }
            }

            fetch(window.GameConfig ? window.GameConfig.getApiUrl('redeem_code.php') : '../../src/api/redeem_code.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                showMessage(data.message, data.success ? 'success' : 'error');
                if (data.success) {
                    document.getElementById('createCodeForm').reset();
                    loadExistingCodes();
                }
            })
            .catch(error => {
                console.error('创建兑换码失败:', error);
                showMessage('网络错误，请稍后重试', 'error');
            });
        });

        // 加载现有兑换码
        function loadExistingCodes() {
            fetch(window.GameConfig ? window.GameConfig.getApiUrl('redeem_code.php?action=list') : '../../src/api/redeem_code.php?action=list')
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('codesContainer');
                    
                    if (data.success && data.codes) {
                        let html = '<table class="codes-table"><thead><tr>';
                        html += '<th>兑换码</th><th>类型</th><th>奖励</th><th>使用情况</th><th>过期时间</th><th>状态</th>';
                        html += '</tr></thead><tbody>';
                        
                        data.codes.forEach(code => {
                            const isActive = code.status === 'active';
                            const isExpired = code.valid_until && new Date(code.valid_until) < new Date();
                            const status = isActive && !isExpired ? '正常' : '失效';
                            const statusClass = isActive && !isExpired ? 'status-active' : 'status-inactive';
                            
                            // 解析奖励数据
                            let rewardDisplay = code.code_type;
                            try {
                                const rewardData = JSON.parse(code.rewards);
                                let rewards = [];
                                
                                if (rewardData.spirit_stones && rewardData.spirit_stones > 0) {
                                    rewards.push(`${rewardData.spirit_stones}灵石`);
                                }
                                if (rewardData.gold && rewardData.gold > 0) {
                                    rewards.push(`${rewardData.gold}金币`);
                                }
                                if (rewardData.spiritual_power && rewardData.spiritual_power > 0) {
                                    rewards.push(`${rewardData.spiritual_power}灵气`);
                                }
                                if (rewardData.silver && rewardData.silver > 0) {
                                    rewards.push(`${rewardData.silver}银两`);
                                }
                                
                                // 显示物品奖励
                                if (rewardData.item_id && Array.isArray(rewardData.item_id)) {
                                    rewardData.item_id.forEach(itemId => {
                                        const quantity = rewardData.item_quantity && rewardData.item_quantity[itemId] ? rewardData.item_quantity[itemId] : 1;
                                        rewards.push(`${quantity}x物品[${itemId}]`);
                                    });
                                }
                                
                                if (rewards.length > 0) {
                                    rewardDisplay = `${code.code_type} (${rewards.join('、')})`;
                                } else if (rewardData.description) {
                                    rewardDisplay = `${code.code_type} (${rewardData.description})`;
                                } else {
                                    rewardDisplay = `${code.code_type} (未定义)`;
                                }
                            } catch (e) {
                                rewardDisplay = `${code.code_type} (解析失败)`;
                            }
                            
                            // 使用状况显示
                            const usedCount = code.used_count || 0;
                            const maxUses = code.max_uses || 0;
                            const usageDisplay = maxUses > 0 ? `${usedCount}/${maxUses}` : `${usedCount}/∞`;
                            
                            html += '<tr>';
                            html += `<td><code>${code.code}</code></td>`;
                            html += `<td>${code.code_type}</td>`;
                            html += `<td>${rewardDisplay}</td>`;
                            html += `<td>${usageDisplay}</td>`;
                            html += `<td>${code.valid_until || '永久'}</td>`;
                            html += `<td class="${statusClass}">${status}</td>`;
                            html += '</tr>';
                        });
                        
                        html += '</tbody></table>';
                        container.innerHTML = html;
                    } else {
                        container.innerHTML = '<p style="color: #e74c3c;">加载失败或没有兑换码</p>';
                    }
                })
                .catch(error => {
                    console.error('加载兑换码列表失败:', error);
                    document.getElementById('codesContainer').innerHTML = '<p style="color: #e74c3c;">加载失败，请稍后重试</p>';
                });
        }

        // 显示消息
        function showMessage(text, type) {
            const existingMessage = document.querySelector('.message');
            if (existingMessage) {
                existingMessage.remove();
            }
            
            const message = document.createElement('div');
            message.className = `message ${type}`;
            message.textContent = text;
            document.querySelector('.container').insertBefore(message, document.querySelector('.section'));
            
            setTimeout(() => {
                if (message.parentNode) {
                    message.parentNode.removeChild(message);
                }
            }, 5000);
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadExistingCodes();
        });
    </script>
</body>
</html> 