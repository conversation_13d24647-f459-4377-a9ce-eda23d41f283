# 一念修仙 - 游戏开发文档 (GAME_DEVELOPMENT_DOCS.md)

- **版本**: 2.0.0
- **更新日期**: 2025-06-21

## 1. 项目概述

本文档是"一念修仙"项目的核心开发指南，旨在为开发人员提供一个清晰、全面的技术参考和工作流程指引。项目是一款基于网页的修仙主题游戏，采用 **PHP + MySQL + JavaScript** 技术栈。

**核心原则**: 在进行任何开发前，请优先查阅本文档和 `DATABASE_SCHEMA.md`。如有不明确之处，请参考 `.cursorrules` 中的行为准则。

---

## 2. 核心目录结构详解

项目的代码和资源被组织在以下核心目录中，请在开发时遵循此结构：

- **`/` (根目录)**
    - **用途**: 存放项目级配置文件和核心文档。
    - **关键文件**:
        - `.cursorrules`: AI Agent 的核心行为准则，最高优先级。
        - `DATABASE_SCHEMA.md`: 数据库结构的权威参考。
        - `GAME_DEVELOPMENT_DOCS.md`: 本文档。
        - `project_status_tracker.md`: 项目宏观进度跟踪。

- **`/public/` (前端资源与入口)**
    - **用途**: 所有对用户可见的 `.html` 页面、CSS样式、JavaScript脚本和图片、音效等静态资源。**这是所有Web请求的入口点**。
    - **关键子目录**:
        - `assets/css/`: 全局及各模块的CSS样式文件。
        - `assets/js/`: **前端核心逻辑目录**。
            - `ajax-manager.js`: **全局AJAX管理器**，新功能开发应优先使用。
            - `item-detail-popup.js`: 物品详情弹窗逻辑。
            - `equipment-integrated.js`: 装备系统的集成脚本。
            - `battle/`: **战斗系统专属脚本目录**。
        - `assets/images/`: 游戏图片资源。

- **`/src/` (后端核心代码)**
    - **用途**: 所有PHP后端逻辑、API接口和共享服务。**此目录不应直接通过Web访问**。
    - **关键子目录**:
        - `api/`: **API接口层**。每个文件对应一组相关功能接口，是前后端数据交互的唯一通道。
        - `includes/`: **公共库与函数**。存放被多处引用的核心函数和类。
        - `config/`: **配置文件**。存放数据库连接等环境配置。

- **`/scripts/` (工具脚本)**
    - **用途**: 存放用于开发、部署或维护的辅助脚本，如数据库迁移、数据导出等。这些脚本不应在生产环境中由用户触发。

- **`/docs/` (项目文档)**
    - **用途**: 存放各类详细的设计文档、系统分析报告、开发指南和会议记录。

---

## 3. 关键文件剖析

- **`src/includes/functions.php`**: **项目最核心的函数库**。包含了大量被多个API引用的公共函数，如用户认证、属性计算、物品操作等。任何通用逻辑都应考虑添加到此文件中。

- **`src/config/database.php`**: **数据库连接配置文件**。定义了数据库的连接信息和 `getDatabase()` 函数。所有需要数据库操作的PHP文件都必须引用它。

- **`public/assets/js/ajax-manager.js`**: **标准化的前端数据请求管理器**。封装了 `fetch` 请求，提供了统一的加载提示、错误处理、超时和重试机制。**所有新的前后端交互都应优先使用 `ajaxManager`**。

- **`public/assets/js/battle/battle-manager.js`**: **战斗流程管理器**。负责协调整个战斗的启动、进程、回合切换、技能释放和结束。是战斗系统的核心调度器。

- **`src/api/battle_drops_unified.php`**: **统一战斗与掉落处理接口**。这是游戏中执行战斗计算、判断胜负、生成和分配战利品的核心API。

- **`src/api/equipment_integrated.php`**: **集成装备操作接口**。负责处理所有与装备相关的操作，如穿戴、卸下、强化、回收等。

---

## 4. 核心工作流程

### 如何添加一个新的API接口？

1.  **创建PHP文件**: 在 `src/api/` 目录下创建一个新的PHP文件，例如 `new_feature.php`。
2.  **引入核心依赖**: 在文件顶部，必须引入 `database.php` 和 `functions.php`。
    ```php
    require_once __DIR__ . '/../config/database.php';
    require_once __DIR__ . '/../includes/functions.php';
    ```
3.  **安全验证**: 使用 `check_auth()` 检查用户登录状态，并获取角色ID。
4.  **处理输入**: 从 `$_POST` 或 `$_GET` 获取参数，并进行严格的验证和清理。
5.  **实现业务逻辑**: 调用数据库和公共函数，完成核心功能。
6.  **返回JSON**: 使用 `echo json_encode([...]);` 返回统一格式的JSON数据给前端。**严禁 `include` 或 `require` 其他API文件**，以防双重JSON输出。

### 如何创建一个新的前端页面？

1.  **创建HTML文件**: 在 `public/` 目录下创建 `new_page.html`。
2.  **引入公共资源**: 在 `<head>` 和 `<body>` 底部，引入必要的CSS和JS文件，如 `global.css`, `ajax-manager.js`, `auth-check.js` 等。
3.  **创建专属JS**: 在 `public/assets/js/` 目录下创建 `new-page.js`，用于处理该页面的特定逻辑。
4.  **发起API请求**: 在 `new-page.js` 中，使用 `ajaxManager.post()` 或 `ajaxManager.get()` 与后端API进行交互。
5.  **更新DOM**: 根据API返回的数据，动态更新页面元素。**注意**：涉及API路径时，要根据HTML文件的位置正确使用相对路径，例如 `../src/api/new_feature.php`。

---
本文档应与项目的实际发展保持同步更新。 