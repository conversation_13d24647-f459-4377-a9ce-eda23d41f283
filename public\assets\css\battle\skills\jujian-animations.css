/**
 * 巨剑术技能动画样式
 * 对应 animation_model = 'jujian'
 */

/* 🗡️ 巨剑术动画样式 */
.giant-sword-container {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 200;
}

/* 旋转小剑样式 */
.rotating-mini-sword {
    position: absolute;
    width: min(40px, 8vw);
    height: min(80px, 16vw);
    opacity: 0;
    transform: translate(-50%, -100%) scaleY(-1); /* 底部轴心 + 垂直翻转 */
    transform-origin: center bottom; /* 底部轴心 */
    pointer-events: none;
    z-index: 200;
    
    /* 支持背景图片显示 */
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    
    /* 添加光晕效果 */
    -webkit-filter: drop-shadow(0 0 8px rgba(200, 220, 255, 0.8));
    filter: drop-shadow(0 0 8px rgba(200, 220, 255, 0.8)) 
            drop-shadow(0 0 16px rgba(150, 180, 255, 0.6))
            drop-shadow(0 0 24px rgba(100, 140, 255, 0.4));
}

/* 旋转小剑武器图片样式 */
.rotating-mini-sword .weapon-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    transform: scaleY(-1); /* 垂直翻转图片 */
    
    /* 为武器图片也添加光晕 */
    -webkit-filter: drop-shadow(0 0 6px rgba(200, 220, 255, 0.7));
    filter: drop-shadow(0 0 6px rgba(200, 220, 255, 0.7)) 
            drop-shadow(0 0 12px rgba(150, 180, 255, 0.5));
}

/* 主剑旋转动画 - 1.5圈并逐渐变大 */
@keyframes main-sword-rotate-1-5 {
    0% { transform: translate(-50%, -100%) scaleY(-1) rotate(0deg) scale(1.0); opacity: 1; }
    5% { transform: translate(-50%, -100%) scaleY(-1) rotate(27deg) scale(1.075); opacity: 1; }
    10% { transform: translate(-50%, -100%) scaleY(-1) rotate(54deg) scale(1.15); opacity: 1; }
    15% { transform: translate(-50%, -100%) scaleY(-1) rotate(81deg) scale(1.225); opacity: 1; }
    20% { transform: translate(-50%, -100%) scaleY(-1) rotate(108deg) scale(1.3); opacity: 1; }
    25% { transform: translate(-50%, -100%) scaleY(-1) rotate(135deg) scale(1.375); opacity: 1; }
    30% { transform: translate(-50%, -100%) scaleY(-1) rotate(162deg) scale(1.45); opacity: 1; }
    35% { transform: translate(-50%, -100%) scaleY(-1) rotate(189deg) scale(1.525); opacity: 1; }
    40% { transform: translate(-50%, -100%) scaleY(-1) rotate(216deg) scale(1.6); opacity: 1; }
    45% { transform: translate(-50%, -100%) scaleY(-1) rotate(243deg) scale(1.675); opacity: 1; }
    50% { transform: translate(-50%, -100%) scaleY(-1) rotate(270deg) scale(1.75); opacity: 1; }
    55% { transform: translate(-50%, -100%) scaleY(-1) rotate(297deg) scale(1.825); opacity: 1; }
    60% { transform: translate(-50%, -100%) scaleY(-1) rotate(324deg) scale(1.9); opacity: 1; }
    65% { transform: translate(-50%, -100%) scaleY(-1) rotate(351deg) scale(1.975); opacity: 1; }
    70% { transform: translate(-50%, -100%) scaleY(-1) rotate(378deg) scale(2.05); opacity: 1; }
    75% { transform: translate(-50%, -100%) scaleY(-1) rotate(405deg) scale(2.125); opacity: 1; }
    80% { transform: translate(-50%, -100%) scaleY(-1) rotate(432deg) scale(2.2); opacity: 1; }
    85% { transform: translate(-50%, -100%) scaleY(-1) rotate(459deg) scale(2.275); opacity: 1; }
    90% { transform: translate(-50%, -100%) scaleY(-1) rotate(486deg) scale(2.35); opacity: 1; }
    95% { transform: translate(-50%, -100%) scaleY(-1) rotate(513deg) scale(2.425); opacity: 1; }
    100% { transform: translate(-50%, -100%) scaleY(-1) rotate(540deg) scale(2.5); opacity: 1; }
}

/* 残影剑动画 - 1.5圈并跟随变大 */
@keyframes shadow-sword-follow-1-5 {
    0% { transform: translate(-50%, -100%) scaleY(-1) rotate(var(--startAngle)) scale(1.0); opacity: 0.7; }
    5% { transform: translate(-50%, -100%) scaleY(-1) rotate(calc(var(--startAngle) + 27deg)) scale(1.075); opacity: 0.68; }
    10% { transform: translate(-50%, -100%) scaleY(-1) rotate(calc(var(--startAngle) + 54deg)) scale(1.15); opacity: 0.66; }
    15% { transform: translate(-50%, -100%) scaleY(-1) rotate(calc(var(--startAngle) + 81deg)) scale(1.225); opacity: 0.64; }
    20% { transform: translate(-50%, -100%) scaleY(-1) rotate(calc(var(--startAngle) + 108deg)) scale(1.3); opacity: 0.62; }
    25% { transform: translate(-50%, -100%) scaleY(-1) rotate(calc(var(--startAngle) + 135deg)) scale(1.375); opacity: 0.6; }
    30% { transform: translate(-50%, -100%) scaleY(-1) rotate(calc(var(--startAngle) + 162deg)) scale(1.45); opacity: 0.58; }
    35% { transform: translate(-50%, -100%) scaleY(-1) rotate(calc(var(--startAngle) + 189deg)) scale(1.525); opacity: 0.56; }
    40% { transform: translate(-50%, -100%) scaleY(-1) rotate(calc(var(--startAngle) + 216deg)) scale(1.6); opacity: 0.54; }
    45% { transform: translate(-50%, -100%) scaleY(-1) rotate(calc(var(--startAngle) + 243deg)) scale(1.675); opacity: 0.52; }
    50% { transform: translate(-50%, -100%) scaleY(-1) rotate(calc(var(--startAngle) + 270deg)) scale(1.75); opacity: 0.5; }
    55% { transform: translate(-50%, -100%) scaleY(-1) rotate(calc(var(--startAngle) + 297deg)) scale(1.825); opacity: 0.48; }
    60% { transform: translate(-50%, -100%) scaleY(-1) rotate(calc(var(--startAngle) + 324deg)) scale(1.9); opacity: 0.46; }
    65% { transform: translate(-50%, -100%) scaleY(-1) rotate(calc(var(--startAngle) + 351deg)) scale(1.975); opacity: 0.44; }
    70% { transform: translate(-50%, -100%) scaleY(-1) rotate(calc(var(--startAngle) + 378deg)) scale(2.05); opacity: 0.42; }
    75% { transform: translate(-50%, -100%) scaleY(-1) rotate(calc(var(--startAngle) + 405deg)) scale(2.125); opacity: 0.4; }
    80% { transform: translate(-50%, -100%) scaleY(-1) rotate(calc(var(--startAngle) + 432deg)) scale(2.2); opacity: 0.38; }
    85% { transform: translate(-50%, -100%) scaleY(-1) rotate(calc(var(--startAngle) + 459deg)) scale(2.275); opacity: 0.36; }
    90% { transform: translate(-50%, -100%) scaleY(-1) rotate(calc(var(--startAngle) + 486deg)) scale(2.35); opacity: 0.34; }
    95% { transform: translate(-50%, -100%) scaleY(-1) rotate(calc(var(--startAngle) + 513deg)) scale(2.425); opacity: 0.32; }
    100% { transform: translate(-50%, -100%) scaleY(-1) rotate(calc(var(--startAngle) + 540deg)) scale(2.5); opacity: 0.3; }
}

/* 主剑旋转动画 - 完整一圈 */
@keyframes main-sword-rotate {
    0% {
        transform: translate(-50%, -100%) scaleY(-1) rotate(0deg); /* 保持垂直翻转 + 旋转 */
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -100%) scaleY(-1) rotate(360deg); /* 保持垂直翻转 + 旋转 */
        opacity: 1;
    }
}

/* 残影剑动画 - 跟随主剑但逐渐消失 */
@keyframes shadow-sword-follow {
    0% {
        transform: translate(-50%, -100%) scaleY(-1) rotate(var(--startAngle)); /* 保持垂直翻转 + 旋转 */
        opacity: 0.7;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        transform: translate(-50%, -100%) scaleY(-1) rotate(calc(var(--startAngle) + 360deg)); /* 保持垂直翻转 + 旋转 */
        opacity: 0.1;
    }
}

/* 巨剑样式 */
.giant-sword-final {
    position: absolute;
    width: min(60px, 12vw);
    height: min(120px, 24vw);
    transform: translate(-50%, -50%); /* 移除垂直翻转，保持正常方向 */
    transform-origin: center;
    opacity: 0;
    pointer-events: none;
    z-index: 201;
    
    /* 支持背景图片显示 */
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

/* 巨剑武器图片样式 */
.giant-sword-final .weapon-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    /* 移除垂直翻转 */
}

/* 巨剑放大动画 */
@keyframes giant-sword-scale-up {
    0% {
        transform: translate(-50%, -50%) scale(0.7) rotate(0deg);
        opacity: 0;
    }
    100% {
        transform: translate(-50%, -50%) scale(1.5) rotate(0deg);
        opacity: 1;
    }
}

/* 巨剑中心旋转飞行动画 - 玩家攻击 */
@keyframes giant-sword-center-rotate-fly-player {
    0% {
        left: var(--startX);
        top: var(--startY);
        transform: translate(-50%, -100%) scaleY(-1) rotate(540deg) scale(2.5); /* 从旋转结束位置开始 */
        transform-origin: center center; /* 设置旋转轴心为中心 */
        opacity: 1;
        -webkit-filter: drop-shadow(0 0 8px rgba(200, 220, 255, 0.8));
        filter: drop-shadow(0 0 8px rgba(200, 220, 255, 0.8)) 
                drop-shadow(0 0 16px rgba(150, 180, 255, 0.6))
                drop-shadow(0 0 24px rgba(100, 140, 255, 0.4));
    }
    10% {
        left: var(--startX);
        top: var(--startY);
        transform: translate(-50%, -50%) scaleY(-1) rotate(540deg) scale(2.5); /* 调整为中心定位 */
        transform-origin: center center; /* 保持中心旋转轴心 */
        opacity: 1;
        -webkit-filter: drop-shadow(0 0 12px rgba(200, 220, 255, 0.9)); 
        filter: drop-shadow(0 0 12px rgba(200, 220, 255, 0.9)) 
                drop-shadow(0 0 24px rgba(150, 180, 255, 0.7))
                drop-shadow(0 0 36px rgba(100, 140, 255, 0.5));
    }
    100% {
        left: var(--targetX);
        top: var(--targetY);
        transform: translate(-50%, -50%) scaleY(-1) rotate(27540deg) scale(2.5); /* 50倍旋转速度，中心旋转 */
        transform-origin: center center; /* 保持中心旋转轴心 */
        opacity: 1;
        -webkit-filter: drop-shadow(0 0 20px rgba(255, 255, 255, 1.0));
        filter: drop-shadow(0 0 20px rgba(255, 255, 255, 1.0)) 
                drop-shadow(0 0 40px rgba(200, 220, 255, 0.9))
                drop-shadow(0 0 60px rgba(150, 180, 255, 0.7))
                drop-shadow(0 0 80px rgba(100, 140, 255, 0.5));
    }
}

/* 巨剑中心旋转飞行动画 - 敌人攻击 */
@keyframes giant-sword-center-rotate-fly-enemy {
    0% {
        left: var(--startX);
        top: var(--startY);
        transform: translate(-50%, -100%) scaleY(-1) rotate(468deg) scale(2.0); /* 从小剑旋转结束位置开始 */
        transform-origin: center bottom; /* 先保持底部轴心 */
        opacity: 1;
        -webkit-filter: drop-shadow(0 0 8px rgba(200, 220, 255, 0.8));
        filter: drop-shadow(0 0 8px rgba(200, 220, 255, 0.8)) 
                drop-shadow(0 0 16px rgba(150, 180, 255, 0.6))
                drop-shadow(0 0 24px rgba(100, 140, 255, 0.4));
    }
    10% {
        left: var(--startX);
        top: var(--startY);
        transform: translate(-50%, -50%) scaleY(-1) rotate(558deg) scale(2.0); /* 继续顺时针旋转，切换到中心定位 */
        transform-origin: center center; /* 切换到中心旋转轴心 */
        opacity: 1;
        -webkit-filter: drop-shadow(0 0 12px rgba(200, 220, 255, 0.9));
        filter: drop-shadow(0 0 12px rgba(200, 220, 255, 0.9)) 
                drop-shadow(0 0 24px rgba(150, 180, 255, 0.7))
                drop-shadow(0 0 36px rgba(100, 140, 255, 0.5));
    }
    100% {
        left: var(--targetX);
        top: var(--targetY);
        transform: translate(-50%, -50%) scaleY(-1) rotate(18558deg) scale(2.0); /* 继续顺时针50倍速旋转 */
        transform-origin: center center; /* 保持中心旋转轴心 */
        opacity: 1;
        -webkit-filter: drop-shadow(0 0 20px rgba(255, 255, 255, 1.0));
        filter: drop-shadow(0 0 20px rgba(255, 255, 255, 1.0)) 
                drop-shadow(0 0 40px rgba(200, 220, 255, 0.9))
                drop-shadow(0 0 60px rgba(150, 180, 255, 0.7))
                drop-shadow(0 0 80px rgba(100, 140, 255, 0.5));
    }
}

/* 巨剑穿透动画 */
@keyframes giant-sword-penetrate-player {
    0% {
        left: var(--targetX);
        top: var(--targetY);
        transform: translate(-50%, -50%) scale(1.5) rotate(0deg);
        opacity: 1;
    }
    100% {
        left: var(--targetX);
        top: var(--finalY);
        transform: translate(-50%, -50%) scale(1.8) rotate(0deg);
        opacity: 0;
    }
}

@keyframes giant-sword-penetrate-enemy {
    0% {
        left: var(--targetX);
        top: var(--targetY);
        transform: translate(-50%, -50%) scale(1.5) rotate(180deg);
        opacity: 1;
    }
    100% {
        left: var(--targetX);
        top: var(--finalY);
        transform: translate(-50%, -50%) scale(1.8) rotate(180deg);
        opacity: 0;
    }
}

/* 移动端适配 */
@media (max-width: 480px) {
    .rotating-mini-sword {
        width: min(32px, 6vw);
        height: min(64px, 12vw);
    }
    
    .giant-sword-final {
        width: min(48px, 10vw);
        height: min(96px, 20vw);
    }
}

/* 🎯 击中特效样式 */
.sword-hit-flash {
    position: absolute;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: radial-gradient(circle,
        rgba(255, 255, 255, 1) 0%,
        rgba(200, 220, 255, 0.8) 30%,
        rgba(150, 180, 255, 0.4) 60%,
        transparent 100%
    );
    transform: translate(-50%, -50%);
    animation: sword-hit-flash-expand 0.3s ease-out forwards;
    -webkit-filter: blur(1px);
    filter: blur(1px);
    pointer-events: none;
    z-index: 300;
}

@keyframes sword-hit-flash-expand {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
    }
    50% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.8;
    }
    100% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0;
    }
}

.sword-hit-particle {
    position: absolute;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255, 255, 255, 1) 0%, rgba(200, 220, 255, 0.8) 100%);
    animation: sword-hit-particle-scatter 0.6s ease-out forwards;
    -webkit-filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.8));
    filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.8));
    pointer-events: none;
    z-index: 295;
}

@keyframes sword-hit-particle-scatter {
    0% {
        transform: translate(0, 0) scale(1);
        opacity: 1;
    }
    100% {
        transform: translate(var(--particleX), var(--particleY)) scale(0);
        opacity: 0;
    }
}

.sword-hit-shockwave {
    position: absolute;
    width: 80px;
    height: 80px;
    border: 2px solid rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: sword-hit-shockwave-expand 0.5s ease-out forwards;
    -webkit-filter: drop-shadow(0 0 10px rgba(200, 220, 255, 0.6));
    filter: drop-shadow(0 0 10px rgba(200, 220, 255, 0.6));
    pointer-events: none;
    z-index: 290;
}

@keyframes sword-hit-shockwave-expand {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
        border-color: rgba(255, 255, 255, 0.9);
    }
    50% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0.6;
        border-color: rgba(200, 220, 255, 0.7);
    }
    100% {
        transform: translate(-50%, -50%) scale(3);
        opacity: 0;
        border-color: rgba(150, 180, 255, 0.3);
    }
}

/* 🎭 角色受击动画 */
@keyframes sword-struck {
    0%, 100% {
        transform: translateX(0) translateY(0);
        -webkit-filter: brightness(1);
        filter: brightness(1);
    }
    10% {
        transform: translateX(-5px) translateY(-3px);
        -webkit-filter: brightness(1.8) saturate(1.5);
        filter: brightness(1.8) saturate(1.5);
    }
    20% {
        transform: translateX(4px) translateY(2px);
        -webkit-filter: brightness(1.4) saturate(1.2);
        filter: brightness(1.4) saturate(1.2);
    }
    30% {
        transform: translateX(-4px) translateY(4px);
        -webkit-filter: brightness(2.0) saturate(1.8);
        filter: brightness(2.0) saturate(1.8);
    }
    40% {
        transform: translateX(5px) translateY(-2px);
        -webkit-filter: brightness(1.6) saturate(1.4);
        filter: brightness(1.6) saturate(1.4);
    }
    50% {
        transform: translateX(-3px) translateY(-5px);
        -webkit-filter: brightness(1.9) saturate(1.7);
        filter: brightness(1.9) saturate(1.7);
    }
    60% {
        transform: translateX(3px) translateY(3px);
        -webkit-filter: brightness(1.3) saturate(1.1);
        filter: brightness(1.3) saturate(1.1);
    }
    70% {
        transform: translateX(-2px) translateY(1px);
        -webkit-filter: brightness(1.5) saturate(1.3);
        filter: brightness(1.5) saturate(1.3);
    }
    80% {
        transform: translateX(1px) translateY(-1px);
        -webkit-filter: brightness(1.2) saturate(1.05);
        filter: brightness(1.2) saturate(1.05);
    }
    90% {
        transform: translateX(-1px) translateY(0px);
        -webkit-filter: brightness(1.1) saturate(1.02);
        filter: brightness(1.1) saturate(1.02);
    }
}

@keyframes sword-hit-shake {
    0%, 100% {
        transform: translateX(0) translateY(0);
        -webkit-filter: brightness(1);
        filter: brightness(1);
    }
    10% {
        transform: translateX(-4px) translateY(-3px);
        -webkit-filter: brightness(1.5);
        filter: brightness(1.5);
    }
    20% {
        transform: translateX(3px) translateY(2px);
        -webkit-filter: brightness(1.2);
        filter: brightness(1.2);
    }
    30% {
        transform: translateX(-3px) translateY(4px);
        -webkit-filter: brightness(1.8);
        filter: brightness(1.8);
    }
    40% {
        transform: translateX(4px) translateY(-2px);
        -webkit-filter: brightness(1.3);
        filter: brightness(1.3);
    }
    50% {
        transform: translateX(-2px) translateY(-4px);
        -webkit-filter: brightness(1.6);
        filter: brightness(1.6);
    }
    60% {
        transform: translateX(3px) translateY(3px);
        -webkit-filter: brightness(1.2);
        filter: brightness(1.2);
    }
    70% {
        transform: translateX(-1px) translateY(1px);
        -webkit-filter: brightness(1.4);
        filter: brightness(1.4);
    }
    80% {
        transform: translateX(1px) translateY(-1px);
        -webkit-filter: brightness(1.1);
        filter: brightness(1.1);
    }
    90% {
        transform: translateX(-1px) translateY(0px);
        -webkit-filter: brightness(1.1);
        filter: brightness(1.1);
    }
}

/* 巨剑飞行残影样式 */
.giant-sword-shadow {
    z-index: 199; /* 比主剑稍低 */
    opacity: 0.5; /* 默认半透明 */
}

.giant-sword-shadow .weapon-image {
    opacity: 0.7; /* 残影武器图片更透明 */
}

/* 巨剑残影飞行动画 - 玩家攻击 */
@keyframes giant-sword-shadow-fly-player {
    0% {
        left: var(--startX);
        top: var(--startY);
        transform: translate(-50%, -100%) scaleY(-1) rotate(468deg) scale(1.4); /* 残影起始尺寸较小 */
        transform-origin: center bottom;
        opacity: 0.5;
        -webkit-filter: drop-shadow(0 0 6px rgba(200, 220, 255, 0.6));
        filter: drop-shadow(0 0 6px rgba(200, 220, 255, 0.6)) 
                drop-shadow(0 0 12px rgba(150, 180, 255, 0.4));
    }
    10% {
        left: var(--startX);
        top: var(--startY);
        transform: translate(-50%, -50%) scaleY(-1) rotate(558deg) scale(1.4); /* 保持较小尺寸 */
        transform-origin: center center;
        opacity: 0.4;
        -webkit-filter: drop-shadow(0 0 8px rgba(200, 220, 255, 0.7));
        filter: drop-shadow(0 0 8px rgba(200, 220, 255, 0.7)) 
                drop-shadow(0 0 16px rgba(150, 180, 255, 0.5));
    }
    100% {
        left: var(--targetX);
        top: var(--targetY);
        transform: translate(-50%, -50%) scaleY(-1) rotate(18558deg) scale(1.4); /* 残影结束尺寸仍然较小 */
        transform-origin: center center;
        opacity: 0.2;
        -webkit-filter: drop-shadow(0 0 10px rgba(200, 220, 255, 0.5));
        filter: drop-shadow(0 0 10px rgba(200, 220, 255, 0.5)) 
                drop-shadow(0 0 20px rgba(150, 180, 255, 0.3));
    }
}

/* 巨剑残影飞行动画 - 敌人攻击 */
@keyframes giant-sword-shadow-fly-enemy {
    0% {
        left: var(--startX);
        top: var(--startY);
        transform: translate(-50%, -100%) scaleY(-1) rotate(468deg) scale(1.4); /* 残影起始尺寸较小 */
        transform-origin: center bottom;
        opacity: 0.5;
        -webkit-filter: drop-shadow(0 0 6px rgba(200, 220, 255, 0.6));
        filter: drop-shadow(0 0 6px rgba(200, 220, 255, 0.6)) 
                drop-shadow(0 0 12px rgba(150, 180, 255, 0.4));
    }
    10% {
        left: var(--startX);
        top: var(--startY);
        transform: translate(-50%, -50%) scaleY(-1) rotate(558deg) scale(1.4); /* 保持较小尺寸 */
        transform-origin: center center;
        opacity: 0.4;
        -webkit-filter: drop-shadow(0 0 8px rgba(200, 220, 255, 0.7));
        filter: drop-shadow(0 0 8px rgba(200, 220, 255, 0.7)) 
                drop-shadow(0 0 16px rgba(150, 180, 255, 0.5));
    }
    100% {
        left: var(--targetX);
        top: var(--targetY);
        transform: translate(-50%, -50%) scaleY(-1) rotate(18558deg) scale(1.4); /* 残影结束尺寸仍然较小 */
        transform-origin: center center;
        opacity: 0.2;
        -webkit-filter: drop-shadow(0 0 10px rgba(200, 220, 255, 0.5));
        filter: drop-shadow(0 0 10px rgba(200, 220, 255, 0.5)) 
                drop-shadow(0 0 20px rgba(150, 180, 255, 0.3));
    }
} 