# 数据库结构文档 (DATABASE_SCHEMA.md)

- **版本**: 1.2.0
- **生成日期**: 2025-06-25
- **总表数**: 44

## 简介

本文档详细描述了 "一念修仙" 项目的核心数据库 `yn_game` 的所有表结构。它是数据相关开发的最高权威参考，旨在为开发人员提供清晰、准确的数据结构信息。所有数据库操作和新功能设计都应以此文档为准。

---

## 表索引

1.  [`admin_logs`](#admin_logs) - 管理日志表
2.  [`admin_users`](#admin_users) - 管理员表
3.  [`adventure_events`](#adventure_events) - 奇遇事件配置表
4.  [`adventure_trigger_logs`](#adventure_trigger_logs) - 奇遇触发日志表
5.  [`battle_records`](#battle_records) - 战斗记录表
6.  [`character_equipment`](#character_equipment) - 角色装备表
7.  [`character_growth_logs`](#character_growth_logs) - 角色成长日志表
8.  [`character_name_changes`](#character_name_changes) - 角色名称变更日志表
9.  [`characters`](#characters) - 角色主表
10. [`crafting_recipes`](#crafting_recipes) - 物品制作配方表
11. [`drop_group_items`](#drop_group_items) - 掉落组物品表
12. [`drop_groups`](#drop_groups) - 掉落组配置表
13. [`dungeons`](#dungeons) - 副本信息表
14. [`equipment_durability_logs`](#equipment_durability_logs) - 装备耐久度日志表
15. [`game_item_sets`](#game_item_sets) - 装备套装表
16. [`game_items`](#game_items) - 游戏物品总表
17. [`game_maps`](#game_maps) - 游戏地图配置表
18. [`game_servers`](#game_servers) - 游戏服务器列表
19. [`immortal_arena_match_pool`](#immortal_arena_match_pool) - 竞技场匹配池
20. [`immortal_arena_ranks`](#immortal_arena_ranks) - 竞技场段位配置表
21. [`immortal_arena_records`](#immortal_arena_records) - 竞技场记录表
22. [`item_skills`](#item_skills) - 物品技能关联表
23. [`login_logs`](#login_logs) - 用户登录日志表
24. [`map_drop_configs`](#map_drop_configs) - 地图掉落配置表
25. [`map_stages`](#map_stages) - 地图关卡配置表
26. [`realm_levels`](#realm_levels) - 境界等级配置表
27. [`redeem_codes`](#redeem_codes) - 兑换码表
28. [`redeem_logs`](#redeem_logs) - 兑换日志表
29. [`server_merge_logs`](#server_merge_logs) - 合服日志表
30. [`shop_items`](#shop_items) - 商店物品表
31. [`skill_fragment_collection`](#skill_fragment_collection) - 技能碎片收集表
32. [`spirit_souls`](#spirit_souls) - 精灵(魂灵)配置表
33. [`spiritual_material_usage`](#spiritual_material_usage) - 天材地宝使用记录表
34. [`user_achievements`](#user_achievements) - 用户成就表
35. [`user_adventure_records`](#user_adventure_records) - 用户奇遇记录表
36. [`user_dungeon_progress`](#user_dungeon_progress) - 用户副本进度表
37. [`user_inventories`](#user_inventories) - 用户背包表
38. [`user_learned_recipes`](#user_learned_recipes) - 用户已学习的配方
39. [`user_map_progress`](#user_map_progress) - 用户地图进度表
40. [`user_purchases`](#user_purchases) - 用户购买记录表
41. [`user_resources_log`](#user_resources_log) - 用户资源日志表
42. [`user_server_roles`](#user_server_roles) - 用户在各服务器的角色
43. [`user_spirits`](#user_spirits) - 用户拥有的精灵
44. [`users`](#users) - 用户主表

---

## 表结构详情

<a id="admin_logs"></a>
### `admin_logs`
**描述**: 管理日志表，记录所有管理员在后台的操作。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) |
| `admin_id` | int(11) | 是 | - | 管理员ID |
| `action_type` | varchar(50) | 是 | - | 操作类型 |
| `target_type` | varchar(50) | 否 | NULL | 目标类型 |
| `target_id` | int(11) | 否 | NULL | 目标ID |
| `action_description` | text | 否 | NULL | 操作描述 |
| `request_data` | json | 否 | NULL | 请求数据 |
| `response_data` | json | 否 | NULL | 响应数据 |
| `ip_address` | varchar(45) | 否 | NULL | IP地址 |
| `user_agent` | text | 否 | NULL | 用户代理 |
| `created_at` | timestamp | 否 | NULL | |

<a id="admin_users"></a>
### `admin_users`
**描述**: 管理员表，存储后台管理员的账号信息和权限。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) |
| `username` | varchar(50) | 是 | - | 管理员账号 |
| `password_hash` | varchar(255) | 是 | - | 密码哈希 |
| `real_name` | varchar(50) | 否 | NULL | 真实姓名 |
| `email` | varchar(100) | 否 | NULL | 邮箱 |
| `role` | enum(...) | 否 | 'operator' | 角色权限 |
| `permissions` | json | 否 | NULL | 详细权限 |
| `status` | enum(...) | 否 | 'active' | 状态 |
| `last_login_time` | timestamp | 否 | NULL | 最后登录时间 |
| `created_at` | timestamp | 否 | NULL | |
| `updated_at` | timestamp | 否 | NULL | (ON UPDATE) |

<a id="adventure_events"></a>
### `adventure_events`
**描述**: 奇遇事件配置表，定义游戏中所有可能发生的奇遇事件。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) |
| `event_name` | varchar(100) | 是 | - | 奇遇事件名称 |
| `event_type` | enum(...) | 是 | - | 奇遇事件类型 |
| `probability` | decimal(5,2) | 是 | - | 触发概率(%) |
| `min_map_level` | int(11) | 否 | 1 | 最低地图等级要求 |
| `max_map_level` | int(11) | 否 | 8 | 最高地图等级要求 |
| `reward_config` | json | 是 | - | 奖励配置(JSON格式) |
| `description` | text | 否 | NULL | 事件描述 |
| `is_active` | tinyint(1) | 否 | 1 | 是否启用 |
| `created_at` | timestamp | 是 | CURRENT_TIMESTAMP | |
| `updated_at` | timestamp | 是 | CURRENT_TIMESTAMP | (ON UPDATE) |

<a id="adventure_trigger_logs"></a>
### `adventure_trigger_logs`
**描述**: 奇遇触发日志表。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) |
| `user_id` | int(11) | 否 | 0 | |
| `character_id` | int(11) | 是 | - | 角色ID |
| `event_type` | varchar(50) | 否 | 'treasure_discovery' | |
| `event_id` | int(11) | 是 | - | 奇遇事件ID |
| `map_id` | int(11) | 是 | - | 触发地图ID |
| `adventure_value_before` | int(11) | 是 | - | 触发前奇遇值 |
| `adventure_value_after` | int(11) | 否 | 0 | 触发后奇遇值 |
| `rewards_received` | json | 否 | NULL | 获得的奖励 |
| `trigger_time` | timestamp | 是 | CURRENT_TIMESTAMP | |

<a id="battle_records"></a>
### `battle_records`
**描述**: 战斗记录表。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) |
| `character_id` | bigint(20) | 是 | - | 角色ID |
| `map_id` | int(11) | 否 | NULL | 地图ID |
| `stage_number` | int(11) | 否 | NULL | 关卡号 |
| `monster_id` | int(11) | 否 | NULL | 怪物ID |
| `battle_type` | enum(...) | 否 | 'pve' | 战斗类型 |
| `battle_result` | enum(...) | 是 | - | 战斗结果 |
| `battle_duration` | int(11) | 否 | NULL | 战斗时长(秒) |
| `damage_dealt` | int(11) | 否 | 0 | 造成伤害 |
| `damage_received` | int(11) | 否 | 0 | 受到伤害 |
| `experience_gained` | int(11) | 否 | 0 | 获得经验 |
| `spirit_stones_gained` | int(11) | 否 | 0 | 获得灵石 |
| `items_dropped` | json | 否 | NULL | 掉落物品 |
| `battle_data` | json | 否 | NULL | 战斗详细数据 |
| `created_at` | timestamp | 否 | NULL | |

<a id="character_equipment"></a>
### `character_equipment`
**描述**: 角色装备表。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) |
| `character_id` | bigint(20) | 是 | - | 角色ID |
| `item_id` | int(11) | 是 | - | 物品ID |
| `inventory_item_id` | int(11) | 否 | NULL | 背包物品ID |
| `slot_type` | enum(...) | 是 | - | 装备槽位类型 |
| `slot_index` | int(11) | 否 | 1 | 槽位编号 |
| `attack_order` | int(11) | 否 | NULL | 攻击顺序(仅武器) |
| `is_active` | tinyint(1) | 否 | 1 | 是否激活(仅武器) |
| `enhancement_level` | int(11) | 否 | 0 | 强化等级 |
| `socket_gems` | json | 否 | NULL | 镶嵌宝石 |
| `equipped_at` | timestamp | 是 | CURRENT_TIMESTAMP | 装备时间 |
| `created_at` | timestamp | 是 | CURRENT_TIMESTAMP | |
| `updated_at` | timestamp | 是 | CURRENT_TIMESTAMP | (ON UPDATE) |

<a id="character_growth_logs"></a>
### `character_growth_logs`
**描述**: 角色成长日志表。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) |
| `character_id` | bigint(20) | 是 | - | 角色ID |
| `log_type` | enum(...) | 是 | - | 日志类型 |
| `attribute_type` | enum(...) | 否 | NULL | 属性类型 |
| `change_amount` | int(11) | 否 | NULL | 变动数量 |
| `change_type` | enum(...) | 否 | NULL | 变动类型 |
| `technique_name` | varchar(50) | 否 | NULL | 功法名称 |
| `exp_gained` | int(11) | 否 | NULL | 获得经验 |
| `breakthrough` | tinyint(1) | 否 | NULL | 是否突破 |
| `breakthrough_level` | int(11) | 否 | NULL | 突破等级 |
| `cultivation_time` | int(11) | 否 | NULL | 修炼时长(秒) |
| `description` | text | 否 | NULL | 变动说明 |
| `created_at` | timestamp | 否 | NULL | |

<a id="character_name_changes"></a>
### `character_name_changes`
**描述**: 角色名称变更日志表。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) |
| `character_id` | bigint(20) | 是 | - | 角色ID |
| `old_name` | varchar(50) | 是 | - | 旧角色名 |
| `new_name` | varchar(50) | 是 | - | 新角色名 |
| `change_time` | timestamp | 是 | CURRENT_TIMESTAMP | 变更时间 |
| `created_at` | timestamp | 否 | NULL | |

<a id="characters"></a>
### `characters`
**描述**: 角色主表。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | bigint(20) | 是 | - | 全局唯一ID |
| `user_id` | int(11) | 是 | - | 用户ID |
| `server_id` | int(11) | 否 | 1 | 服务器ID |
| `original_server_id` | int(11) | 否 | 1 | 原始服务器ID |
| `character_name` | varchar(50) | 是 | - | 角色名称 |
| `original_name` | varchar(50) | 否 | NULL | 原始角色名 |
| `name_changed` | tinyint(1) | 否 | 0 | 是否因合区改名 |
| `realm_id` | int(11) | 否 | 1 | 境界ID |
| `realm_progress` | decimal(5,2) | 否 | 0.00 | 境界进度百分比 |
| `current_hp` | int(11) | 否 | 100 | 当前生命值 |
| `current_mp` | int(11) | 否 | 100 | 当前法力值 |
| `cultivation_points`| int(11) | 否 | 0 | 修炼点数 |
| `physique` | int(11) | 否 | 10 | 筋骨 |
| `comprehension` | int(11) | 否 | 10 | 悟性 |
| `constitution` | int(11) | 否 | 10 | 体魄 |
| `spirit` | int(11) | 否 | 10 | 神魂 |
| `agility` | int(11) | 否 | 10 | 身法 |
| `metal_affinity` | int(11) | 否 | 0 | 金灵根 |
| `wood_affinity` | int(11) | 否 | 0 | 木灵根 |
| `water_affinity` | int(11) | 否 | 0 | 水灵根 |
| `fire_affinity` | int(11) | 否 | 0 | 火灵根 |
| `earth_affinity` | int(11) | 否 | 0 | 土灵根 |
| `cultivation_techniques`| text | 否 | NULL | JSON功法数据 |
| `current_technique` | varchar(50) | 否 | NULL | 当前功法ID |
| `attribute_pill_count`| json | 否 | NULL | 丹药使用记录 |
| `spiritual_root_usage`| text | 否 | NULL | 天材地宝使用记录 |
| `arena_dao_power` | int(11) | 否 | 0 | |
| `arena_daily_attempts`| int(11) | 否 | 0 | |
| `arena_purchased_attempts`| int(11) | 否 | 0 | |
| `arena_last_reset` | date | 否 | NULL | |
| `arena_rank_level` | int(11) | 否 | 1 | |
| `arena_rank_points` | int(11) | 否 | 0 | 竞技场段位积分 |
| `arena_total_wins` | int(11) | 否 | 0 | |
| `arena_total_battles`| int(11) | 否 | 0 | |
| `arena_win_streak` | int(11) | 否 | 0 | |
| `arena_best_streak` | int(11) | 否 | 0 | |
| `arena_skill_sequence`| varchar(50) | 否 | '0,1,2,3,4,5'| |
| `tribulation_pill_used`| tinyint(1) | 否 | 0 | 是否使用过渡劫丹 |
| `soul_healing_pill_used`| tinyint(1) | 否 | 0 | 是否使用过养魂丹 |
| `total_battles` | int(11) | 否 | 0 | 总战斗次数 |
| `total_victories` | int(11) | 否 | 0 | 总胜利次数 |
| `total_defeats` | int(11) | 否 | 0 | 总失败次数 |
| `highest_damage` | int(11) | 否 | 0 | 最高伤害记录 |
| `highest_healing` | int(11) | 否 | 0 | 最高治疗记录 |
| `last_cultivation_time`| timestamp | 否 | NULL | 最后修炼时间 |
| `last_login_time` | timestamp | 否 | NULL | 最后登录时间 |
| `last_battle_time`| timestamp | 否 | NULL | 最后战斗时间 |
| `last_tribulation_time`| timestamp | 否 | NULL | 最后渡劫时间 |
| `total_online_time`| int(11) | 否 | 0 | 总在线时间(分钟) |
| `avatar_image` | varchar(200) | 否 | NULL | 角色头像图片 |
| `avatar_frame` | varchar(100) | 否 | 'base (1).png' | 头像外框文件名 |
| `character_model`| varchar(200) | 否 | NULL | 角色立绘/模型图片 |
| `appearance_data`| json | 否 | NULL | 外观自定义数据 |
| `created_at` | timestamp | 否 | NULL | |
| `updated_at` | timestamp | 否 | NULL | (ON UPDATE) |
| `inventory_slots`| int(11) | 否 | 30 | 背包格子数量 |
| `spiritual_roots`| text | 否 | NULL | 五行灵根数据(JSON) |
| `learned_recipes`| json | 否 | NULL | 已学会的丹方记录 |
| `soul_damage_time`| int(11) | 否 | NULL | 魂力受损时间戳 |
| `soul_recovery_per_second`| decimal(10,6) | 否 | NULL | 每秒魂力恢复点数 |
| `current_soul_power`| int(11) | 否 | 100 | 当前魂力值（0-100）|
| `last_breakthrough_time`| timestamp | 否 | NULL | 最后突破时间 |
| `pickup_settings`| text | 否 | NULL | 装备拾取设置JSON |

<a id="crafting_recipes"></a>
### `crafting_recipes`
**描述**: 物品制作配方表。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) |
| `recipe_name` | varchar(255) | 是 | - | 配方名称 |
| `recipe_type` | varchar(50) | 否 | 'alchemy' | 配方类型 |
| `result_item_id` | int(11) | 是 | - | 产物物品ID |
| `result_quantity`| int(11) | 否 | 1 | 产物数量 |
| `materials` | json | 是 | - | 所需材料 (JSON) |
| `required_level` | int(11) | 否 | 1 | 制作等级要求 |
| `required_realm` | int(11) | 否 | NULL | 境界要求 |
| `description` | text | 否 | NULL | 描述 |
| `is_active` | tinyint(1) | 否 | 1 | 是否启用 |
| `created_at` | timestamp | 否 | CURRENT_TIMESTAMP | |
| `updated_at` | timestamp | 否 | CURRENT_TIMESTAMP | (ON UPDATE) |

<a id="drop_group_items"></a>
### `drop_group_items`
**描述**: 掉落组物品表。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) |
| `drop_group_id`| int(11) | 是 | - | 掉落组ID |
| `item_id` | int(11) | 是 | - | 物品ID |
| `min_quantity` | int(11) | 否 | 1 | 最小数量 |
| `max_quantity` | int(11) | 否 | 1 | 最大数量 |
| `drop_chance` | decimal(5,2) | 是 | - | 掉落概率(%) |
| `is_active` | tinyint(1) | 否 | 1 | 是否启用 |
| `created_at` | timestamp | 否 | CURRENT_TIMESTAMP | |
| `updated_at` | timestamp | 否 | CURRENT_TIMESTAMP | (ON UPDATE) |

<a id="drop_groups"></a>
### `drop_groups`
**描述**: 掉落组配置表。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) |
| `group_name` | varchar(255) | 是 | - | 掉落组名称 |
| `description` | text | 否 | NULL | 描述 |
| `is_active` | tinyint(1) | 否 | 1 | 是否启用 |
| `created_at` | timestamp | 否 | CURRENT_TIMESTAMP | |
| `updated_at` | timestamp | 否 | CURRENT_TIMESTAMP | (ON UPDATE) |

<a id="dungeons"></a>
### `dungeons`
**描述**: 副本信息表。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) |
| `dungeon_name` | varchar(100) | 是 | - | 副本名称 |
| `description` | text | 否 | NULL | 描述 |
| `min_level_req`| int(11) | 否 | 1 | 最低等级要求 |
| `min_realm_req`| int(11) | 否 | NULL | 最低境界要求 |
| `entry_cost` | json | 否 | NULL | 进入消耗 |
| `daily_limit` | int(11) | 否 | 3 | 每日进入次数限制 |
| `reset_cycle` | enum(...) | 否 | 'daily'| 重置周期 |
| `reward_preview`| json | 否 | NULL | 奖励预览 |
| `stages_config`| json | 否 | NULL | 关卡配置 |
| `is_active` | tinyint(1) | 否 | 1 | 是否启用 |
| `created_at` | timestamp | 否 | CURRENT_TIMESTAMP | |
| `updated_at` | timestamp | 否 | CURRENT_TIMESTAMP | (ON UPDATE) |

<a id="equipment_durability_logs"></a>
### `equipment_durability_logs`
**描述**: 装备耐久度日志表。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) |
| `character_id` | bigint(20) | 是 | - | 角色ID |
| `equipment_id` | int(11) | 是 | - | 装备ID |
| `inventory_item_id`| int(11) | 是 | - | 背包物品ID |
| `change_type` | enum(...) | 是 | - | 变动类型 |
| `durability_before`| int(11) | 否 | NULL | 变动前耐久 |
| `durability_after`| int(11) | 否 | NULL | 变动后耐久 |
| `change_amount` | int(11) | 否 | NULL | 变动值 |
| `created_at` | timestamp | 否 | NULL | |



<a id="game_items"></a>
### `game_items`
**描述**: 游戏物品总表。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) 物品ID |
| `item_name` | varchar(255) | 是 | - | 物品名称 |
| `item_type` | enum(...) | 是 | - | 物品类型 |
| `sub_type` | varchar(50) | 否 | NULL | 子类型 |
| `description` | text | 否 | NULL | 描述 |
| `icon_path` | varchar(255) | 否 | NULL | 图标路径 |
| `quality` | enum(...) | 否 | 'common'| 品质 |
| `level_req` | int(11) | 否 | 1 | 等级要求 |
| `realm_req` | int(11) | 否 | NULL | 境界要求 |
| `stack_limit` | int(11) | 否 | 999 | 堆叠上限 |
| `is_tradable` | tinyint(1) | 否 | 1 | 是否可交易 |
| `is_sellable` | tinyint(1) | 否 | 1 | 是否可出售 |
| `sell_price` | int(11) | 否 | 0 | 出售价格 |
| `buy_price` | int(11) | 否 | 0 | 购买价格 |
| `use_effect` | json | 否 | NULL | 使用效果(JSON) |
| `attributes` | json | 否 | NULL | 属性(JSON) |
| `durability` | int(11) | 否 | NULL | 耐久度 |
| `animation_model`| varchar(100) | 否 | NULL | 动画模型ID |
| `attack_type` | enum(...) | 否 | 'physical'| 攻击类型 |
| `attack_speed` | decimal(5,2)| 否 | NULL | 攻击速度 |
| `cast_time` | decimal(5,2)| 否 | NULL | 施法时间 |
| `cooldown` | decimal(5,2)| 否 | NULL | 冷却时间 |
| `mana_cost` | int(11) | 否 | NULL | 法力消耗 |
| `binding` | enum(...) | 否 | 'none' | 绑定类型 |
| `expire_time` | int(11) | 否 | NULL | 有效期(秒) |
| `is_quest_item`| tinyint(1) | 否 | 0 | 是否为任务物品 |
| `set_id` | int(11) | 否 | NULL | 套装ID |
| `created_at` | timestamp | 否 | CURRENT_TIMESTAMP | |
| `updated_at` | timestamp | 否 | CURRENT_TIMESTAMP | (ON UPDATE) |

<a id="game_maps"></a>
### `game_maps`
**描述**: 游戏地图配置表。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) |
| `map_name` | varchar(100) | 是 | - | 地图名称 |
| `description` | text | 否 | NULL | 描述 |
| `min_level_req`| int(11) | 否 | 1 | 最低等级要求 |
| `min_realm_req`| int(11) | 否 | NULL | 最低境界要求 |
| `map_type` | enum(...) | 否 | 'wild' | 地图类型 |
| `is_safe_zone` | tinyint(1) | 否 | 0 | 是否为安全区 |
| `background_image`| varchar(255) | 否 | NULL | 背景图片 |
| `stage_count` | int(11) | 否 | 10 | 关卡数量 |
| `boss_id` | int(11) | 否 | NULL | 最终BOSS怪物ID |
| `unlock_map_id`| int(11) | 否 | NULL | 通关后解锁地图ID |
| `drop_group_id`| int(11) | 否 | NULL | 掉落组ID |
| `created_at` | timestamp | 否 | CURRENT_TIMESTAMP | |
| `updated_at` | timestamp | 否 | CURRENT_TIMESTAMP | (ON UPDATE) |

<a id="game_servers"></a>
### `game_servers`
**描述**: 游戏服务器列表。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) |
| `server_name` | varchar(100) | 是 | - | 服务器名称 |
| `description` | varchar(255) | 否 | NULL | 描述 |
| `ip_address` | varchar(45) | 否 | NULL | IP地址 |
| `port` | int(11) | 否 | NULL | 端口 |
| `status` | enum(...) | 否 | 'online'| 服务器状态 |
| `max_players` | int(11) | 否 | 1000 | 最大玩家数 |
| `current_players`| int(11) | 否 | 0 | 当前玩家数 |
| `region` | varchar(50) | 否 | NULL | 区域 |
| `is_recommended`| tinyint(1) | 否 | 0 | 是否推荐 |
| `open_time` | timestamp | 否 | NULL | 开服时间 |
| `merge_to_server_id`| int(11) | 否 | NULL | 合并到的服务器ID |
| `created_at` | timestamp | 否 | NULL | |
| `updated_at` | timestamp | 否 | NULL | (ON UPDATE) |

<a id="immortal_arena_match_pool"></a>
### `immortal_arena_match_pool`
**描述**: 竞技场匹配池。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) |
| `character_id` | bigint(20) | 是 | - | 角色ID |
| `rank_level` | int(11) | 是 | - | 段位等级 |
| `rank_points` | int(11) | 是 | - | 段位积分 |
| `status` | enum(...) | 否 | 'waiting'| 状态 |
| `entered_at` | timestamp | 否 | CURRENT_TIMESTAMP | |

<a id="immortal_arena_ranks"></a>
### `immortal_arena_ranks`
**描述**: 竞技场段位配置表。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) |
| `rank_name` | varchar(50) | 是 | - | 段位名称 |
| `min_points` | int(11) | 是 | - | 最低积分 |
| `max_points` | int(11) | 是 | - | 最高积分 |
| `rank_icon` | varchar(255) | 否 | NULL | 段位图标 |
| `daily_reward` | json | 否 | NULL | 每日奖励 |
| `season_reward`| json | 否 | NULL | 赛季奖励 |

<a id="immortal_arena_records"></a>
### `immortal_arena_records`
**描述**: 竞技场记录表。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) |
| `character_id_1`| bigint(20) | 是 | - | 角色1 ID |
| `character_id_2`| bigint(20) | 是 | - | 角色2 ID |
| `winner_id` | bigint(20) | 否 | NULL | 胜利者ID |
| `points_change_1`| int(11) | 否 | 0 | 角色1积分变化 |
| `points_change_2`| int(11) | 否 | 0 | 角色2积分变化 |
| `battle_replay`| json | 否 | NULL | 战斗回放数据 |
| `battle_time` | timestamp | 否 | CURRENT_TIMESTAMP | |



<a id="item_skills"></a>
### `item_skills`
**描述**: 物品技能关联表。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) |
| `item_id` | int(11) | 是 | - | 物品ID |
| `skill_name` | varchar(50) | 是 | - | 技能名称 |
| `skill_level` | int(11) | 否 | 1 | 技能等级 |
| `trigger_condition`| enum(...) | 否 | 'on_attack'| 触发条件 |
| `trigger_chance`| decimal(5,2)| 否 | NULL | 触发概率 |
| `skill_effect`| json | 是 | - | 技能效果 (JSON) |
| `description` | text | 否 | NULL | 描述 |
| `is_active` | tinyint(1) | 否 | 1 | 是否启用 |
| `created_at` | timestamp | 否 | CURRENT_TIMESTAMP | |
| `updated_at` | timestamp | 否 | CURRENT_TIMESTAMP | (ON UPDATE) |

<a id="login_logs"></a>
### `login_logs`
**描述**: 用户登录日志表。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) |
| `user_id` | int(11) | 是 | - | 用户ID |
| `ip_address` | varchar(45) | 否 | NULL | IP地址 |
| `user_agent` | text | 否 | NULL | 用户代理 |
| `login_time` | timestamp | 否 | CURRENT_TIMESTAMP | |
| `logout_time`| timestamp | 否 | NULL | |
| `status` | enum(...) | 是 | - | 登录状态 |

<a id="map_drop_configs"></a>
### `map_drop_configs`
**描述**: 地图掉落配置表。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) |
| `map_id` | int(11) | 是 | - | 地图ID |
| `monster_type`| enum(...) | 否 | 'all'| 怪物类型 |
| `drop_group_id`| int(11) | 是 | - | 掉落组ID |
| `condition_type`| enum(...) | 否 | 'none'| 条件类型 |
| `condition_value`| varchar(255)| 否 | NULL | 条件值 |
| `description` | text | 否 | NULL | 描述 |
| `is_active` | tinyint(1) | 否 | 1 | 是否启用 |
| `created_at` | timestamp | 否 | CURRENT_TIMESTAMP | |
| `updated_at` | timestamp | 否 | CURRENT_TIMESTAMP | (ON UPDATE) |

<a id="map_stages"></a>
### `map_stages`
**描述**: 地图关卡配置表。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) |
| `map_id` | int(11) | 是 | - | 地图ID |
| `stage_number` | int(11) | 是 | - | 关卡号 |
| `stage_name` | varchar(100) | 否 | NULL | 关卡名称 |
| `monster_ids` | json | 是 | - | 怪物ID列表 (JSON) |
| `monster_count`| int(11) | 否 | 1 | 怪物数量 |
| `boss_id` | int(11) | 否 | NULL | 关底BOSS ID |
| `first_clear_reward`| json | 否 | NULL | 首次通关奖励 |
| `repeat_clear_reward`| json | 否 | NULL | 重复通关奖励 |
| `stamina_cost` | int(11) | 否 | 1 | 体力消耗 |
| `time_limit` | int(11) | 否 | NULL | 时间限制(秒) |
| `description` | text | 否 | NULL | 描述 |
| `is_active` | tinyint(1) | 否 | 1 | 是否启用 |
| `created_at` | timestamp | 否 | CURRENT_TIMESTAMP | |
| `updated_at` | timestamp | 否 | CURRENT_TIMESTAMP | (ON UPDATE) |



<a id="realm_levels"></a>
### `realm_levels`
**描述**: 境界等级配置表。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) |
| `realm_name` | varchar(50) | 是 | - | 境界名称 |
| `realm_level` | int(11) | 是 | - | 境界等级 |
| `exp_needed` | bigint(20) | 是 | - | 升级所需经验 |
| `attribute_bonus`| json | 否 | NULL | 属性加成(JSON) |
| `breakthrough_chance`| decimal(5,2)| 否 | 100.00| 突破成功率 |
| `breakthrough_cost`| json | 否 | NULL | 突破消耗 |
| `breakthrough_reward`| json | 否 | NULL | 突破奖励 |
| `description` | text | 否 | NULL | 描述 |
| `created_at` | timestamp | 否 | CURRENT_TIMESTAMP | |
| `updated_at` | timestamp | 否 | CURRENT_TIMESTAMP | (ON UPDATE) |

<a id="redeem_codes"></a>
### `redeem_codes`
**描述**: 兑换码表。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) |
| `code` | varchar(50) | 是 | - | 兑换码 (唯一) |
| `reward_items`| json | 是 | - | 奖励物品(JSON) |
| `description` | text | 否 | NULL | 描述 |
| `max_uses` | int(11) | 否 | 1 | 最大使用次数 |
| `current_uses`| int(11) | 否 | 0 | 当前使用次数 |
| `expires_at` | timestamp | 否 | NULL | 过期时间 |
| `allowed_servers`| json | 否 | NULL | 允许使用的服务器 |
| `is_active` | tinyint(1) | 否 | 1 | 是否启用 |
| `created_by` | int(11) | 否 | NULL | 创建者(管理员ID)|
| `created_at` | timestamp | 否 | CURRENT_TIMESTAMP | |
| `updated_at` | timestamp | 否 | CURRENT_TIMESTAMP | (ON UPDATE) |

<a id="redeem_logs"></a>
### `redeem_logs`
**描述**: 兑换日志表。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) |
| `user_id` | int(11) | 是 | - | 用户ID |
| `character_id` | bigint(20) | 是 | - | 角色ID |
| `redeem_code_id`| int(11) | 是 | - | 兑换码ID |
| `code_used` | varchar(50) | 是 | - | 使用的兑换码 |
| `rewards_received`| json | 否 | NULL | 获得的奖励 |
| `redeem_time` | timestamp | 否 | CURRENT_TIMESTAMP | |
| `ip_address` | varchar(45) | 否 | NULL | IP地址 |

<a id="server_merge_logs"></a>
### `server_merge_logs`
**描述**: 合服日志表。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) |
| `source_server_id`| int(11) | 是 | - | 源服务器ID |
| `target_server_id`| int(11) | 是 | - | 目标服务器ID |
| `merge_time` | timestamp | 否 | CURRENT_TIMESTAMP | |
| `status` | enum(...) | 否 | 'completed'| 状态 |
| `details` | json | 否 | NULL | 详情(JSON) |
| `admin_id` | int(11) | 否 | NULL | 操作管理员ID |

<a id="shop_items"></a>
### `shop_items`
**描述**: 商店物品表。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) |
| `shop_id` | int(11) | 是 | - | 商店ID |
| `item_id` | int(11) | 是 | - | 物品ID |
| `price` | int(11) | 是 | - | 价格 |
| `currency` | varchar(50) | 否 | 'spirit_stone'| 货币类型 |
| `stock` | int(11) | 否 | -1 | 库存 (-1为无限) |
| `purchase_limit`| int(11) | 否 | -1 | 个人购买限制 |
| `limit_cycle` | enum(...) | 否 | 'none'| 限制周期 |
| `required_vip_level`| int(11) | 否 | 0 | VIP等级要求 |
| `required_realm_id`| int(11) | 否 | 0 | 境界要求 |
| `sort_order` | int(11) | 否 | 0 | 排序 |
| `is_visible` | tinyint(1) | 否 | 1 | 是否可见 |
| `start_time` | timestamp | 否 | NULL | 上架时间 |
| `end_time` | timestamp | 否 | NULL | 下架时间 |
| `created_at` | timestamp | 否 | CURRENT_TIMESTAMP | |
| `updated_at` | timestamp | 否 | CURRENT_TIMESTAMP | (ON UPDATE) |

<a id="skill_fragment_collection"></a>
### `skill_fragment_collection`
**描述**: 技能碎片收集表。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) |
| `character_id` | bigint(20) | 是 | - | 角色ID |
| `item_id` | int(11) | 是 | - | 碎片物品ID |
| `quantity` | int(11) | 是 | - | 拥有数量 |
| `created_at` | timestamp | 否 | CURRENT_TIMESTAMP | |
| `updated_at` | timestamp | 否 | CURRENT_TIMESTAMP | (ON UPDATE) |

<a id="spirit_souls"></a>
### `spirit_souls`
**描述**: 精灵(魂灵)配置表。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) |
| `name` | varchar(100) | 是 | - | 精灵名称 |
| `quality` | enum(...) | 是 | - | 品质 |
| `type` | enum(...) | 否 | 'attack'| 类型 |
| `base_attributes`| json | 是 | - | 基础属性 |
| `growth_rates`| json | 是 | - | 成长率 |
| `skills` | json | 否 | NULL | 技能 |
| `description` | text | 否 | NULL | 描述 |
| `image_path` | varchar(255)| 否 | NULL | 图片路径 |
| `is_active` | tinyint(1) | 否 | 1 | 是否启用 |
| `created_at` | timestamp | 否 | CURRENT_TIMESTAMP | |
| `updated_at` | timestamp | 否 | CURRENT_TIMESTAMP | (ON UPDATE) |

<a id="spiritual_material_usage"></a>
### `spiritual_material_usage`
**描述**: 天材地宝使用记录表。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) |
| `character_id` | bigint(20) | 是 | - | 角色ID |
| `item_id` | int(11) | 是 | - | 物品ID |
| `usage_count` | int(11) | 否 | 1 | 使用次数 |
| `last_used_at`| timestamp | 否 | CURRENT_TIMESTAMP | |

<a id="user_achievements"></a>
### `user_achievements`
**描述**: 用户成就表。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) |
| `character_id` | bigint(20) | 是 | - | 角色ID |
| `achievement_id`| int(11) | 是 | - | 成就ID |
| `progress` | int(11) | 否 | 0 | 成就进度 |
| `is_completed` | tinyint(1) | 否 | 0 | 是否完成 |
| `completed_at` | timestamp | 否 | NULL | 完成时间 |
| `is_claimed` | tinyint(1) | 否 | 0 | 是否领取奖励 |
| `claimed_at` | timestamp | 否 | NULL | 领取时间 |

<a id="user_adventure_records"></a>
### `user_adventure_records`
**描述**: 用户奇遇记录表。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) |
| `character_id` | bigint(20) | 是 | - | 角色ID |
| `event_id` | int(11) | 是 | - | 奇遇事件ID |
| `trigger_count`| int(11) | 否 | 1 | 触发次数 |
| `last_triggered_at`| timestamp | 否 | CURRENT_TIMESTAMP | |

<a id="user_dungeon_progress"></a>
### `user_dungeon_progress`
**描述**: 用户副本进度表。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) |
| `character_id` | bigint(20) | 是 | - | 角色ID |
| `dungeon_id` | int(11) | 是 | - | 副本ID |
| `highest_stage_cleared`| int(11) | 否 | 0 | 通关最高关卡 |
| `daily_entries`| int(11) | 否 | 0 | 今日进入次数 |
| `last_entry_date`| date | 否 | NULL | 最后进入日期 |
| `best_clear_time`| int(11) | 否 | NULL | 最快通关时间(秒) |

<a id="user_inventories"></a>
### `user_inventories`
**描述**: 用户背包表。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) |
| `character_id` | bigint(20) | 是 | - | 角色ID |
| `item_id` | int(11) | 是 | - | 物品ID |
| `quantity` | int(11) | 是 | - | 数量 |
| `slot` | int(11) | 否 | NULL | 背包槽位 |
| `is_equipped` | tinyint(1) | 否 | 0 | 是否已装备 |
| `is_locked` | tinyint(1) | 否 | 0 | 是否已锁定 |
| `attributes` | json | 否 | NULL | 动态属性 |
| `durability` | int(11) | 否 | NULL | 当前耐久 |
| `added_at` | timestamp | 否 | CURRENT_TIMESTAMP | |

<a id="user_learned_recipes"></a>
### `user_learned_recipes`
**描述**: 用户已学习的配方。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) |
| `character_id` | bigint(20) | 是 | - | 角色ID |
| `recipe_id` | int(11) | 是 | - | 配方ID |
| `learned_at` | timestamp | 否 | CURRENT_TIMESTAMP | |

<a id="user_map_progress"></a>
### `user_map_progress`
**描述**: 用户地图进度表。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) |
| `character_id` | bigint(20) | 是 | - | 角色ID |
| `map_id` | int(11) | 是 | - | 地图ID |
| `stage_id` | int(11) | 是 | - | 关卡ID |
| `status` | enum(...) | 否 | 'unlocked'| 状态 |
| `unlocked_at` | timestamp | 否 | CURRENT_TIMESTAMP | |
| `first_cleared_at`| timestamp | 否 | NULL | 首次通关时间 |
| `highest_rating`| int(11) | 否 | 0 | 最高评级 |

<a id="user_purchases"></a>
### `user_purchases`
**描述**: 用户购买记录表。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) |
| `character_id` | bigint(20) | 是 | - | 角色ID |
| `shop_item_id`| int(11) | 是 | - | 商店物品ID |
| `quantity` | int(11) | 是 | - | 购买数量 |
| `total_price` | int(11) | 是 | - | 总价 |
| `currency` | varchar(50) | 是 | - | 货币类型 |
| `purchase_time`| timestamp | 否 | CURRENT_TIMESTAMP | |

<a id="user_resources_log"></a>
### `user_resources_log`
**描述**: 用户资源日志表。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) |
| `character_id` | bigint(20) | 是 | - | 角色ID |
| `resource_type`| varchar(50) | 是 | - | 资源类型 |
| `change_amount`| int(11) | 是 | - | 变动数量 |
| `balance_before`| int(11) | 是 | - | 变动前余额 |
| `balance_after`| int(11) | 是 | - | 变动后余额 |
| `source` | varchar(100) | 否 | NULL | 来源 |
| `created_at` | timestamp | 否 | CURRENT_TIMESTAMP | |

<a id="user_server_roles"></a>
### `user_server_roles`
**描述**: 用户在各服务器的角色。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) |
| `user_id` | int(11) | 是 | - | 用户ID |
| `server_id` | int(11) | 是 | - | 服务器ID |
| `character_id` | bigint(20) | 是 | - | 角色ID |
| `character_name`| varchar(50) | 是 | - | 角色名 |
| `level` | int(11) | 否 | 1 | 等级 |
| `last_login` | timestamp | 否 | NULL | 最后登录时间 |

<a id="user_spirits"></a>
### `user_spirits`
**描述**: 用户拥有的精灵。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) |
| `character_id` | bigint(20) | 是 | - | 角色ID |
| `spirit_id` | int(11) | 是 | - | 精灵ID |
| `level` | int(11) | 否 | 1 | 等级 |
| `experience` | int(11) | 否 | 0 | 经验值 |
| `attributes` | json | 否 | NULL | 当前属性 |
| `is_equipped` | tinyint(1) | 否 | 0 | 是否出战 |
| `acquired_at` | timestamp | 否 | CURRENT_TIMESTAMP | |

<a id="users"></a>
### `users`
**描述**: 用户主表。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) |
| `username` | varchar(50) | 是 | - | 用户名 (唯一) |
| `password_hash`| varchar(255)| 是 | - | 密码哈希 |
| `email` | varchar(100) | 否 | NULL | 邮箱 (唯一) |
| `phone_number` | varchar(20) | 否 | NULL | 手机号 (唯一) |
| `status` | enum(...) | 否 | 'active'| 账号状态 |
| `vip_level` | int(11) | 否 | 0 | VIP等级 |
| `total_recharge`| decimal(10,2)| 否 | 0.00 | 累计充值 |
| `last_ip` | varchar(45) | 否 | NULL | 最后登录IP |
| `last_login` | timestamp | 否 | NULL | 最后登录时间 |
| `created_at` | timestamp | 否 | NULL | |
| `updated_at` | timestamp | 否 | NULL | (ON UPDATE) |

<a id="game_item_sets"></a>
### `game_item_sets`
**描述**: 装备套装表。存储套装基本信息和效果。
| 字段名 | 类型 | 非空 | 默认值 | 注释 |
| --- | --- | --- | --- | --- |
| `id` | int(11) | 是 | - | (主键) 套装ID |
| `set_name` | varchar(100) | 是 | - | 套装名称 |
| `description` | text | 否 | NULL | 套装描述 |
| `min_pieces` | int(11) | 否 | 2 | 最少激活件数 |
| `max_pieces` | int(11) | 否 | 6 | 最多激活件数 |
| `effects` | json | 是 | - | 套装效果(JSON) |
| `rarity` | enum('普通','稀有','史诗','传说','神话') | 是 | '普通' | 套装品质 |
| `realm_requirement` | int(11) | 否 | 1 | 境界要求 |
| `status` | enum('active','inactive') | 是 | 'active' | 状态 |
| `created_at` | timestamp | 是 | CURRENT_TIMESTAMP | 创建时间 |
| `updated_at` | timestamp | 是 | CURRENT_TIMESTAMP | (ON UPDATE) |

**索引**:
- PRIMARY KEY (`id`)

**套装效果JSON格式**:
```json
{
    "attributes": {
        "physical_defense": 100,
        "immortal_defense": 100,
        "hp_bonus": 500,
        "mp_bonus": 200,
        "speed_bonus": 10,
        "critical_bonus": 0.05,
        "critical_damage": 0.10,
        "critical_resistance": 0.05,
        "accuracy_bonus": 0.05,
        "dodge_bonus": 0.05
    },
    "special_effects": [
        {
            "type": "combat_effect",
            "trigger": "on_hit",
            "effect": "damage_reduction",
            "value": 0.15,
            "description": "受到攻击时，伤害降低15%"
        }
    ],
    "description": "套装效果描述文本"
}
```

**说明**:
1. 套装效果分为属性加成和特殊效果两部分
2. 属性加成使用game_items表中的实际属性字段
3. 特殊效果支持战斗触发类型效果
4. 每个套装支持2/4/6件套三个等级的效果
5. 套装部件通过game_items表的set_id字段关联

**索引**:
- PRIMARY KEY (`id`)
- KEY `idx_set_name` (`set_name`) 