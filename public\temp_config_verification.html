<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端配置验证工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .result { margin: 10px 0; }
    </style>
</head>
<body>
    <h1>🔧 前端配置验证工具</h1>
    <p>测试时间: <span id="testTime"></span></p>
    
    <div class="test-section">
        <h2>1. config.js 加载测试</h2>
        <div id="configLoadTest"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 路径构建器测试</h2>
        <div id="pathBuilderTest"></div>
    </div>
    
    <div class="test-section">
        <h2>3. API调用测试</h2>
        <div id="apiCallTest"></div>
    </div>
    
    <div class="test-section">
        <h2>4. 资源路径测试</h2>
        <div id="resourcePathTest"></div>
    </div>
    
    <div class="test-section">
        <h2>5. 总结</h2>
        <div id="summary"></div>
    </div>

    <!-- 引入配置文件 -->
    <script src="assets/js/config.js"></script>
    
    <script>
        // 设置测试时间
        document.getElementById('testTime').textContent = new Date().toLocaleString();
        
        let testResults = [];
        
        function addResult(sectionId, message, type = 'success') {
            const section = document.getElementById(sectionId);
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = message;
            section.appendChild(div);
            
            testResults.push({type, message});
        }
        
        // 测试1: config.js 加载测试
        function testConfigLoad() {
            if (typeof window.GameConfig !== 'undefined') {
                addResult('configLoadTest', '✅ window.GameConfig 对象存在', 'success');
                
                // 检查关键属性
                const requiredProps = ['PROJECT_NAME', 'API_BASE_URL', 'ASSETS_BASE_URL', 'PATHS'];
                requiredProps.forEach(prop => {
                    if (window.GameConfig[prop]) {
                        addResult('configLoadTest', `✅ ${prop}: ${JSON.stringify(window.GameConfig[prop])}`, 'success');
                    } else {
                        addResult('configLoadTest', `❌ ${prop} 属性缺失`, 'error');
                    }
                });
                
                // 检查路径配置
                const paths = window.GameConfig.PATHS;
                if (paths) {
                    Object.keys(paths).forEach(key => {
                        addResult('configLoadTest', `✅ PATHS.${key}: ${paths[key]}`, 'success');
                    });
                }
                
            } else {
                addResult('configLoadTest', '❌ window.GameConfig 对象不存在', 'error');
            }
        }
        
        // 测试2: 路径构建器测试
        function testPathBuilders() {
            if (typeof window.GameConfig !== 'undefined') {
                // 测试API路径构建器
                if (typeof window.GameConfig.getApiUrl === 'function') {
                    const apiUrl = window.GameConfig.getApiUrl('test.php');
                    addResult('pathBuilderTest', `✅ getApiUrl('test.php'): ${apiUrl}`, 'success');
                } else {
                    addResult('pathBuilderTest', '❌ getApiUrl 函数不存在', 'error');
                }
                
                // 测试图片路径构建器
                if (typeof window.GameConfig.getImageUrl === 'function') {
                    const imageUrl = window.GameConfig.getImageUrl('test.png');
                    addResult('pathBuilderTest', `✅ getImageUrl('test.png'): ${imageUrl}`, 'success');
                } else {
                    addResult('pathBuilderTest', '❌ getImageUrl 函数不存在', 'error');
                }
                
                // 测试CSS路径构建器
                if (typeof window.GameConfig.getCssUrl === 'function') {
                    const cssUrl = window.GameConfig.getCssUrl('test.css');
                    addResult('pathBuilderTest', `✅ getCssUrl('test.css'): ${cssUrl}`, 'success');
                } else {
                    addResult('pathBuilderTest', '❌ getCssUrl 函数不存在', 'error');
                }
                
                // 测试全局辅助函数
                if (typeof window.getApiUrl === 'function') {
                    const globalApiUrl = window.getApiUrl('test.php');
                    addResult('pathBuilderTest', `✅ 全局 getApiUrl('test.php'): ${globalApiUrl}`, 'success');
                } else {
                    addResult('pathBuilderTest', '❌ 全局 getApiUrl 函数不存在', 'error');
                }
                
            } else {
                addResult('pathBuilderTest', '❌ 无法测试路径构建器，config.js未加载', 'error');
            }
        }
        
        // 测试3: API调用测试
        async function testApiCall() {
            if (typeof window.GameConfig !== 'undefined') {
                try {
                    // 测试一个简单的API调用
                    const apiUrl = window.GameConfig.getApiUrl('csrf.php');
                    addResult('apiCallTest', `🔄 正在测试API调用: ${apiUrl}`, 'warning');
                    
                    const response = await fetch(apiUrl);
                    if (response.ok) {
                        const data = await response.json();
                        addResult('apiCallTest', `✅ API调用成功，返回数据: ${JSON.stringify(data)}`, 'success');
                    } else {
                        addResult('apiCallTest', `⚠️ API调用返回状态: ${response.status}`, 'warning');
                    }
                } catch (error) {
                    addResult('apiCallTest', `❌ API调用失败: ${error.message}`, 'error');
                }
            } else {
                addResult('apiCallTest', '❌ 无法测试API调用，config.js未加载', 'error');
            }
        }
        
        // 测试4: 资源路径测试
        function testResourcePaths() {
            if (typeof window.GameConfig !== 'undefined') {
                // 测试CSS文件是否可访问
                const cssUrl = window.GameConfig.getCssUrl('style.css');
                const cssLink = document.createElement('link');
                cssLink.rel = 'stylesheet';
                cssLink.href = cssUrl;
                cssLink.onload = () => {
                    addResult('resourcePathTest', `✅ CSS文件可访问: ${cssUrl}`, 'success');
                };
                cssLink.onerror = () => {
                    addResult('resourcePathTest', `⚠️ CSS文件不存在: ${cssUrl}`, 'warning');
                };
                document.head.appendChild(cssLink);
                
                // 测试图片路径
                const imageUrl = window.GameConfig.getImageUrl('logo.png');
                const img = new Image();
                img.onload = () => {
                    addResult('resourcePathTest', `✅ 图片文件可访问: ${imageUrl}`, 'success');
                };
                img.onerror = () => {
                    addResult('resourcePathTest', `⚠️ 图片文件不存在: ${imageUrl}`, 'warning');
                };
                img.src = imageUrl;
                
            } else {
                addResult('resourcePathTest', '❌ 无法测试资源路径，config.js未加载', 'error');
            }
        }
        
        // 生成总结
        function generateSummary() {
            setTimeout(() => {
                const successCount = testResults.filter(r => r.type === 'success').length;
                const errorCount = testResults.filter(r => r.type === 'error').length;
                const warningCount = testResults.filter(r => r.type === 'warning').length;
                
                addResult('summary', `📊 测试完成统计:`, 'success');
                addResult('summary', `✅ 成功: ${successCount} 项`, 'success');
                addResult('summary', `⚠️ 警告: ${warningCount} 项`, 'warning');
                addResult('summary', `❌ 错误: ${errorCount} 项`, 'error');
                
                if (errorCount === 0) {
                    addResult('summary', `🎉 所有关键测试通过！配置系统工作正常`, 'success');
                } else {
                    addResult('summary', `⚠️ 发现 ${errorCount} 个错误，需要修复`, 'error');
                }
                
                addResult('summary', `📝 测试完成后请删除此临时文件`, 'warning');
            }, 2000);
        }
        
        // 执行所有测试
        testConfigLoad();
        testPathBuilders();
        testApiCall();
        testResourcePaths();
        generateSummary();
    </script>
</body>
</html>
