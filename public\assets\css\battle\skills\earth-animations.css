/**
 * 土系技能动画样式 - 岩石突刺
 * 对应 animation_model = 'yanshituci'
 */

/* 动画容器 */
.yanshituci-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1000;
}

/* === 蓄力阶段动画 === */

/* 大地魔法阵 */
.yanshituci-magic-circle {
    position: absolute;
    width: 130px;
    height: 130px;
    transform: translate(-50%, -50%);
    border: 4px solid rgba(139, 69, 19, 0.8);
    border-radius: 50%;
    background: radial-gradient(circle, rgba(160, 82, 45, 0.4) 0%, rgba(139, 69, 19, 0.2) 50%, transparent 100%);
    animation: yanshituci-magic-circle 1.1s ease-out;
    box-shadow: 0 0 35px rgba(139, 69, 19, 0.7), inset 0 0 25px rgba(160, 82, 45, 0.5);
}

@keyframes yanshituci-magic-circle {
    0% {
        transform: translate(-50%, -50%) scale(0) rotate(0deg);
        opacity: 0;
    }
    40% {
        transform: translate(-50%, -50%) scale(1.3) rotate(120deg);
        opacity: 0.9;
    }
    100% {
        transform: translate(-50%, -50%) scale(1) rotate(360deg);
        opacity: 1;
    }
}

/* 内圈岩石符文 */
.yanshituci-inner-runes {
    position: absolute;
    width: 90px;
    height: 90px;
    transform: translate(-50%, -50%);
    border: 3px solid rgba(160, 82, 45, 0.8);
    border-radius: 50%;
    background: conic-gradient(from 0deg, 
        rgba(139, 69, 19, 0.6), 
        rgba(160, 82, 45, 0.4), 
        rgba(205, 133, 63, 0.3), 
        rgba(160, 82, 45, 0.4), 
        rgba(139, 69, 19, 0.6));
    animation: yanshituci-inner-runes 1.2s ease-out infinite;
}

@keyframes yanshituci-inner-runes {
    0%, 100% {
        transform: translate(-50%, -50%) scale(0.9) rotate(0deg);
        opacity: 0.8;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.1) rotate(180deg);
        opacity: 0.5;
    }
}

/* 外圈地脉符文 */
.yanshituci-outer-runes {
    position: absolute;
    width: 150px;
    height: 150px;
    transform: translate(-50%, -50%);
    border: 2px solid rgba(205, 133, 63, 0.6);
    border-radius: 50%;
    background: conic-gradient(from 180deg, 
        rgba(160, 82, 45, 0.3), 
        rgba(205, 133, 63, 0.2), 
        rgba(222, 184, 135, 0.1), 
        rgba(205, 133, 63, 0.2), 
        rgba(160, 82, 45, 0.3));
    animation: yanshituci-outer-runes 1.8s ease-out infinite;
}

@keyframes yanshituci-outer-runes {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1) rotate(0deg);
        opacity: 0.6;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.2) rotate(-180deg);
        opacity: 0.3;
    }
}

/* 武器图片旋转 */
.yanshituci-weapon-sprite {
    position: absolute;
    width: 45px;
    height: 45px;
    transform: translate(-50%, -50%);
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    animation: yanshituci-weapon-rotate 1.1s linear infinite;
    -webkit-filter: drop-shadow(0 0 10px rgba(139, 69, 19, 0.9));
    filter: drop-shadow(0 0 10px rgba(139, 69, 19, 0.9));
}

@keyframes yanshituci-weapon-rotate {
    0% { transform: translate(-50%, -50%) rotate(0deg) scale(1); }
    25% { transform: translate(-50%, -50%) rotate(90deg) scale(1.15); }
    50% { transform: translate(-50%, -50%) rotate(180deg) scale(1.2); }
    75% { transform: translate(-50%, -50%) rotate(270deg) scale(1.15); }
    100% { transform: translate(-50%, -50%) rotate(360deg) scale(1); }
}

/* 蓄力能量核心 */
.yanshituci-energy-core {
    position: absolute;
    width: 35px;
    height: 35px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, rgba(139, 69, 19, 0.9) 0%, rgba(160, 82, 45, 0.7) 50%, transparent 100%);
    border-radius: 50%;
    animation: yanshituci-energy-pulse 0.9s ease-in-out infinite alternate;
    box-shadow: 0 0 25px rgba(139, 69, 19, 0.9);
}

@keyframes yanshituci-energy-pulse {
    0% {
        transform: translate(-50%, -50%) scale(0.7);
        box-shadow: 0 0 20px rgba(139, 69, 19, 0.7);
    }
    100% {
        transform: translate(-50%, -50%) scale(1.3);
        box-shadow: 0 0 30px rgba(139, 69, 19, 1);
    }
}

/* 土石聚集粒子 */
.yanshituci-charge-rock {
    position: absolute;
    width: 5px;
    height: 5px;
    transform: translate(-50%, -50%);
    background: linear-gradient(45deg, rgba(139, 69, 19, 0.9), rgba(160, 82, 45, 0.8));
    border-radius: 30%;
    animation: yanshituci-rock-gather 1.0s ease-out forwards;
    box-shadow: 0 0 5px rgba(139, 69, 19, 0.7);
}

@keyframes yanshituci-rock-gather {
    0% {
        transform: translate(calc(-50% + var(--chargeX)), calc(-50% + var(--chargeY))) scale(0) rotate(0deg);
        opacity: 0;
    }
    40% {
        transform: translate(calc(-50% + var(--chargeX) * 0.6), calc(-50% + var(--chargeY) * 0.6)) scale(1.2) rotate(180deg);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(0.4) rotate(360deg);
        opacity: 0;
    }
}

/* 环绕地脉震动 */
.yanshituci-charge-tremble {
    position: absolute;
    width: 10px;
    height: 10px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, rgba(160, 82, 45, 0.8), rgba(205, 133, 63, 0.4));
    border-radius: 50%;
    animation: yanshituci-tremble-move 1s ease-out forwards;
}

@keyframes yanshituci-tremble-move {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0;
    }
    30% {
        transform: translate(-50%, -50%) scale(1.8);
        opacity: 0.9;
    }
    70% {
        transform: translate(-50%, -50%) scale(1.2);
        opacity: 0.6;
    }
    100% {
        transform: translate(-50%, -50%) scale(0.2);
        opacity: 0;
    }
}

/* 大地能量波纹 */
.yanshituci-energy-ripple {
    position: absolute;
    width: 25px;
    height: 25px;
    transform: translate(-50%, -50%);
    border: 3px solid rgba(139, 69, 19, 0.7);
    border-radius: 50%;
    animation: yanshituci-ripple-expand 0.9s ease-out forwards;
}

@keyframes yanshituci-ripple-expand {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0.9;
        border-width: 3px;
    }
    100% {
        transform: translate(-50%, -50%) scale(9);
        opacity: 0;
        border-width: 1px;
    }
}

/* 地面裂纹预兆 */
.yanshituci-charge-crack {
    position: absolute;
    width: 2px;
    height: 15px;
    transform: translate(-50%, -50%);
    background: linear-gradient(to bottom, rgba(139, 69, 19, 0.8), rgba(101, 67, 33, 0.6));
    border-radius: 1px;
    animation: yanshituci-crack-appear 1.2s ease-out forwards;
}

@keyframes yanshituci-crack-appear {
    0% {
        transform: translate(-50%, -50%) scaleY(0) rotate(0deg);
        opacity: 0;
    }
    50% {
        transform: translate(-50%, -50%) scaleY(1) rotate(15deg);
        opacity: 0.8;
    }
    100% {
        transform: translate(-50%, -50%) scaleY(0.3) rotate(30deg);
        opacity: 0;
    }
}

/* 尘土飞扬效果 */
.yanshituci-dust {
    position: absolute;
    width: 8px;
    height: 8px;
    transform: translate(-50%, -50%);
    background: rgba(205, 133, 63, 0.6);
    border-radius: 50%;
    animation: yanshituci-dust-rise 1.8s ease-out forwards;
}

@keyframes yanshituci-dust-rise {
    0% {
        transform: translate(-50%, -50%) scale(0.3);
        opacity: 0.6;
    }
    50% {
        transform: translate(-50%, calc(-50% - 25px)) scale(1.5);
        opacity: 0.4;
    }
    100% {
        transform: translate(-50%, calc(-50% - 50px)) scale(3);
        opacity: 0;
    }
}

/* === 发射阶段动画 === */

/* 预发射地面震动 */
.yanshituci-ground-quake {
    position: absolute;
    width: 80px;
    height: 80px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, rgba(139, 69, 19, 0.5), transparent);
    border-radius: 50%;
    animation: yanshituci-ground-quake 0.25s ease-in-out 4;
}

@keyframes yanshituci-ground-quake {
    0%, 100% { transform: translate(-50%, -50%) scale(1); }
    50% { transform: translate(-50%, -50%) scale(1.4); }
}

/* 主要地裂 */
.yanshituci-major-crack {
    position: absolute;
    width: 4px;
    height: 60px;
    transform: translate(-50%, -50%);
    background: linear-gradient(to bottom, 
        rgba(139, 69, 19, 0.9) 0%, 
        rgba(101, 67, 33, 0.8) 30%, 
        rgba(160, 82, 45, 0.6) 60%, 
        rgba(205, 133, 63, 0.4) 100%);
    border-radius: 2px;
    animation: yanshituci-crack-spread 0.6s ease-out forwards;
    box-shadow: 0 0 8px rgba(139, 69, 19, 0.7);
}

@keyframes yanshituci-crack-spread {
    0% {
        transform: translate(-50%, -50%) scaleY(0);
        opacity: 0;
    }
    50% {
        transform: translate(-50%, -50%) scaleY(1.2);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scaleY(1);
        opacity: 0.8;
    }
}

/* 地面震动波 */
.yanshituci-shock-ripple {
    position: absolute;
    width: 40px;
    height: 40px;
    transform: translate(-50%, -50%);
    border: 3px solid rgba(160, 82, 45, 0.7);
    border-radius: 50%;
    animation: yanshituci-shock-expand 0.8s ease-out forwards;
}

@keyframes yanshituci-shock-expand {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0.8;
        border-width: 3px;
    }
    100% {
        transform: translate(-50%, -50%) scale(4);
        opacity: 0;
        border-width: 1px;
    }
}

/* 主尖刺 */
.yanshituci-main-spike {
    position: absolute;
    width: 20px;
    height: var(--spikeHeight);
    transform: translate(-50%, -100%);
    background: linear-gradient(to top, 
        rgba(139, 69, 19, 0.9) 0%, 
        rgba(160, 82, 45, 0.8) 40%, 
        rgba(205, 133, 63, 0.6) 80%, 
        rgba(222, 184, 135, 0.4) 100%);
    -webkit-clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    animation: yanshituci-spike-emerge 0.8s ease-out forwards;
    box-shadow: 0 0 10px rgba(139, 69, 19, 0.6);
}

@keyframes yanshituci-spike-emerge {
    0% {
        transform: translate(-50%, -100%) scaleY(0);
        opacity: 0;
    }
    60% {
        transform: translate(-50%, -100%) scaleY(1.1);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -100%) scaleY(1);
        opacity: 0.9;
    }
}

/* 副尖刺 */
.yanshituci-sub-spike {
    position: absolute;
    width: 12px;
    height: var(--spikeHeight);
    transform: translate(-50%, -100%);
    background: linear-gradient(to top, 
        rgba(160, 82, 45, 0.8) 0%, 
        rgba(205, 133, 63, 0.6) 60%, 
        rgba(222, 184, 135, 0.3) 100%);
    -webkit-clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    animation: yanshituci-sub-spike-emerge 0.6s ease-out forwards;
}

@keyframes yanshituci-sub-spike-emerge {
    0% {
        transform: translate(-50%, -100%) scaleY(0);
        opacity: 0;
    }
    100% {
        transform: translate(-50%, -100%) scaleY(1);
        opacity: 0.7;
    }
}

/* 尖刺沉入动画 */
@keyframes yanshituci-spike-sink {
    0% {
        transform: translate(-50%, -100%) scaleY(1);
        opacity: 0.9;
    }
    100% {
        transform: translate(-50%, -100%) scaleY(0);
        opacity: 0;
    }
}

/* 爆发碎石 */
.yanshituci-eruption-debris {
    position: absolute;
    width: 4px;
    height: 4px;
    transform: translate(-50%, -50%);
    background: rgba(139, 69, 19, 0.8);
    border-radius: 30%;
    animation: yanshituci-debris-fly 1.2s ease-out forwards;
}

@keyframes yanshituci-debris-fly {
    0% {
        transform: translate(-50%, -50%) rotate(var(--debrisAngle)) translateX(0) scale(0);
        opacity: 0.8;
    }
    50% {
        transform: translate(-50%, -50%) rotate(var(--debrisAngle)) translateX(calc(var(--debrisDistance) * 0.7)) scale(1.5);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) rotate(var(--debrisAngle)) translateX(var(--debrisDistance)) scale(0.3);
        opacity: 0;
    }
}

/* === 击中阶段动画 === */

/* 瞬间地震冲击 */
.yanshituci-impact-quake {
    position: absolute;
    width: 70px;
    height: 70px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, rgba(139, 69, 19, 0.9), rgba(160, 82, 45, 0.6), transparent);
    border-radius: 50%;
    animation: yanshituci-impact-quake 0.4s ease-out forwards;
}

@keyframes yanshituci-impact-quake {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.8);
        opacity: 0.8;
    }
    100% {
        transform: translate(-50%, -50%) scale(4);
        opacity: 0;
    }
}

/* 巨石爆发核心 */
.yanshituci-rock-core {
    position: absolute;
    width: 80px;
    height: 80px;
    transform: translate(-50%, -50%);
    background: conic-gradient(from 0deg, 
        rgba(139, 69, 19, 0.9), 
        rgba(160, 82, 45, 0.7), 
        rgba(205, 133, 63, 0.5), 
        rgba(160, 82, 45, 0.7), 
        rgba(139, 69, 19, 0.9));
    border-radius: 50%;
    animation: yanshituci-rock-core 1.5s ease-out forwards;
}

@keyframes yanshituci-rock-core {
    0% {
        transform: translate(-50%, -50%) rotate(0deg) scale(0);
        opacity: 0.9;
    }
    40% {
        transform: translate(-50%, -50%) rotate(180deg) scale(1.3);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) rotate(540deg) scale(0.2);
        opacity: 0;
    }
}

/* 震荡波 */
.yanshituci-impact-shockwave {
    position: absolute;
    width: 40px;
    height: 40px;
    transform: translate(-50%, -50%);
    border: 4px solid rgba(139, 69, 19, 0.7);
    border-radius: 50%;
    animation: yanshituci-shockwave-expand 1s ease-out forwards;
}

@keyframes yanshituci-shockwave-expand {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0.9;
        border-width: 4px;
    }
    100% {
        transform: translate(-50%, -50%) scale(7);
        opacity: 0;
        border-width: 1px;
    }
}

/* 放射状碎石爆发 */
.yanshituci-impact-debris {
    position: absolute;
    width: 6px;
    height: 6px;
    transform: translate(-50%, -50%);
    background: linear-gradient(45deg, rgba(139, 69, 19, 0.9), rgba(160, 82, 45, 0.7));
    border-radius: 20%;
    animation: yanshituci-impact-debris 1.5s ease-out forwards;
}

@keyframes yanshituci-impact-debris {
    0% {
        transform: translate(-50%, -50%) rotate(var(--debrisAngle)) scale(0);
        opacity: 0.9;
    }
    30% {
        transform: translate(-50%, -50%) 
                   rotate(var(--debrisAngle)) 
                   translateX(calc(var(--debrisDistance) * 0.3)) 
                   translateY(calc(var(--debrisHeight) * -1)) 
                   scale(1.5);
        opacity: 1;
    }
    70% {
        transform: translate(-50%, -50%) 
                   rotate(var(--debrisAngle)) 
                   translateX(calc(var(--debrisDistance) * 0.8)) 
                   translateY(calc(var(--debrisHeight) * -0.5)) 
                   scale(1);
        opacity: 0.6;
    }
    100% {
        transform: translate(-50%, -50%) 
                   rotate(var(--debrisAngle)) 
                   translateX(var(--debrisDistance)) 
                   translateY(0) 
                   scale(0.3);
        opacity: 0;
    }
}

/* 地面裂纹扩散 */
.yanshituci-impact-crack {
    position: absolute;
    width: 3px;
    height: var(--crackLength);
    transform: translate(-50%, -50%) rotate(var(--crackAngle));
    background: linear-gradient(to bottom, 
        rgba(139, 69, 19, 0.8) 0%, 
        rgba(101, 67, 33, 0.6) 50%, 
        transparent 100%);
    border-radius: 1px;
    animation: yanshituci-crack-spread-impact 1.2s ease-out forwards;
}

@keyframes yanshituci-crack-spread-impact {
    0% {
        transform: translate(-50%, -50%) rotate(var(--crackAngle)) scaleY(0);
        opacity: 0;
    }
    50% {
        transform: translate(-50%, -50%) rotate(var(--crackAngle)) scaleY(1.2);
        opacity: 0.8;
    }
    100% {
        transform: translate(-50%, -50%) rotate(var(--crackAngle)) scaleY(1);
        opacity: 0.3;
    }
}

/* 尘土云雾 */
.yanshituci-impact-dust {
    position: absolute;
    width: 15px;
    height: 15px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, rgba(205, 133, 63, 0.6), transparent);
    border-radius: 50%;
    animation: yanshituci-dust-spread 2.5s ease-out forwards;
}

@keyframes yanshituci-dust-spread {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0.6;
    }
    40% {
        transform: translate(-50%, -50%) scale(4);
        opacity: 0.4;
    }
    100% {
        transform: translate(-50%, -50%) scale(8);
        opacity: 0;
    }
}

/* 余震效果 */
.yanshituci-aftershock {
    position: absolute;
    width: 20px;
    height: 20px;
    transform: translate(-50%, -50%);
    border: 2px solid rgba(160, 82, 45, 0.5);
    border-radius: 50%;
    animation: yanshituci-aftershock 1.8s ease-out forwards;
}

@keyframes yanshituci-aftershock {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0.5;
    }
    50% {
        transform: translate(-50%, -50%) scale(2);
        opacity: 0.3;
    }
    100% {
        transform: translate(-50%, -50%) scale(4);
        opacity: 0;
    }
}

/* 敌人受击效果 */
@keyframes earth-hit {
    0% { -webkit-filter: hue-rotate(0deg) saturate(1); }
    0% { filter: hue-rotate(0deg) saturate(1); }
    25% { -webkit-filter: hue-rotate(30deg) saturate(1.6) brightness(1.3); }
    25% { filter: hue-rotate(30deg) saturate(1.6) brightness(1.3); }
    50% { -webkit-filter: hue-rotate(0deg) saturate(1) brightness(0.7); }
    50% { filter: hue-rotate(0deg) saturate(1) brightness(0.7); }
    75% { -webkit-filter: hue-rotate(30deg) saturate(1.3) brightness(1.2); }
    75% { filter: hue-rotate(30deg) saturate(1.3) brightness(1.2); }
    100% { -webkit-filter: hue-rotate(0deg) saturate(1); }
    100% { filter: hue-rotate(0deg) saturate(1); }
}

@keyframes earth-shake {
    0%, 100% { transform: translate(0, 0); }
    10% { transform: translate(-2px, -1px); }
    20% { transform: translate(2px, 1px); }
    30% { transform: translate(-1px, -2px); }
    40% { transform: translate(1px, 2px); }
    50% { transform: translate(-2px, 0px); }
    60% { transform: translate(2px, -1px); }
    70% { transform: translate(-1px, 1px); }
    80% { transform: translate(1px, -1px); }
    90% { transform: translate(-1px, 0px); }
}

/* 移动端适配 */
@media (max-width: 768px) {
    .yanshituci-container {
        transform: scale(0.8);
    }
    
    .yanshituci-magic-circle {
        width: 110px;
        height: 110px;
    }
    
    .yanshituci-main-spike {
        width: 18px;
    }
    
    .yanshituci-impact-quake {
        width: 60px;
        height: 60px;
    }
} 