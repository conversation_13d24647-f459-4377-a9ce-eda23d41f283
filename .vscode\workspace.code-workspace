{
    "folders": [
        {
            "name": "一念修仙-主项目",
            "path": "."
        },
        {
            "name": "API接口",
            "path": "./src/api"
        },
        {
            "name": "前端资源",
            "path": "./public"
        },
        {
            "name": "配置文件",
            "path": "./src/config"
        },
        {
            "name": "数据库脚本",
            "path": "./database"
        },
        {
            "name": "文档",
            "path": "./docs"
        }
    ],
    "settings": {
        // 工作区专用设置
        "workbench.colorTheme": "Default Dark+",
        "workbench.iconTheme": "vs-seti",
        
        // 文件监控
        "files.watcherExclude": {
            "**/node_modules/**": true,
            "**/vendor/**": true,
            "**/.git/**": true,
            "**/logs/**": true,
            "**/temp_*": true
        },
        
        // 搜索配置
        "search.useGlobalIgnoreFiles": true,
        "search.useParentIgnoreFiles": true,
        
        // 任务配置
        "task.autoDetect": "on",
        "task.showDecorations": true,
        
        // 调试配置
        "debug.allowBreakpointsEverywhere": true,
        "debug.showInStatusBar": "always",
        
        // 扩展配置
        "extensions.autoUpdate": true,
        "extensions.autoCheckUpdates": true,
        
        // 智能提示
        "editor.quickSuggestions": {
            "other": true,
            "comments": false,
            "strings": true
        },
        
        // 项目特定配置
        "php.validate.enable": true,
        "php.validate.run": "onType",
        "html.validate.scripts": true,
        "css.validate": true,
        "javascript.validate.enable": true
    },
    "tasks": {
        "version": "2.0.0",
        "tasks": [
            {
                "label": "启动开发服务器",
                "type": "shell",
                "command": "E:/phpstudy_pro/Extensions/php/php7.4.3nts/php.exe",
                "args": ["-S", "localhost:8000"],
                "group": "build",
                "presentation": {
                    "echo": true,
                    "reveal": "always",
                    "focus": false,
                    "panel": "shared"
                },
                "isBackground": true,
                "problemMatcher": []
            },
            {
                "label": "运行配置检查",
                "type": "shell",
                "command": "E:/phpstudy_pro/Extensions/php/php7.4.3nts/php.exe",
                "args": ["check_vscode_config.php"],
                "group": "test",
                "presentation": {
                    "echo": true,
                    "reveal": "always",
                    "focus": false,
                    "panel": "shared"
                }
            },
            {
                "label": "数据库备份",
                "type": "shell",
                "command": "mysqldump",
                "args": ["-u", "ynxx", "-p", "yn_game", ">", "backup_$(date +%Y%m%d_%H%M%S).sql"],
                "group": "build",
                "presentation": {
                    "echo": true,
                    "reveal": "always",
                    "focus": false,
                    "panel": "shared"
                }
            }
        ]
    },
    "launch": {
        "version": "0.2.0",
        "configurations": [
            {
                "name": "调试当前PHP文件",
                "type": "php",
                "request": "launch",
                "program": "${file}",
                "cwd": "${fileDirname}",
                "port": 0,
                "runtimeArgs": [
                    "-dxdebug.start_with_request=yes"
                ],
                "env": {
                    "XDEBUG_MODE": "debug,develop",
                    "XDEBUG_CONFIG": "client_port=${port}"
                }
            },
            {
                "name": "调试Web应用",
                "type": "php",
                "request": "launch",
                "port": 9003,
                "pathMappings": {
                    "/yinian": "${workspaceFolder}"
                },
                "ignore": [
                    "**/vendor/**/*.php"
                ]
            }
        ]
    },
    "extensions": {
        "recommendations": [
            "bmewburn.vscode-intelephense-client",
            "xdebug.php-debug",
            "mtxr.sqltools",
            "humao.rest-client",
            "github.copilot",
            "ms-vscode.vscode-ai-toolkit"
        ]
    }
}
