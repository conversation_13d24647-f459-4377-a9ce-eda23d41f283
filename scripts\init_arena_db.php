<?php
/**
 * 竞技场系统数据库初始化脚本
 * 执行方式：php scripts/init_arena_db.php
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 引入数据库配置
require_once __DIR__ . '/../src/includes/functions.php';

try {
    echo "🏆 开始初始化竞技场系统数据库...\n";
    
    // 获取数据库连接
    $pdo = getDatabase();
    
    // 读取SQL脚本
    $sqlFile = __DIR__ . '/../database/updates/arena_system_init.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL脚本文件不存在: {$sqlFile}");
    }
    
    $sql = file_get_contents($sqlFile);
    if ($sql === false) {
        throw new Exception("无法读取SQL脚本文件");
    }
    
    echo "📄 SQL脚本读取成功，开始执行...\n";
    
    // 分割SQL语句（按分号分割）
    $statements = explode(';', $sql);
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue; // 跳过空语句和注释
        }
        
        try {
            $pdo->exec($statement);
            $successCount++;
            
            // 显示执行的语句类型
            if (stripos($statement, 'CREATE TABLE') !== false) {
                preg_match('/CREATE TABLE.*?`(\w+)`/i', $statement, $matches);
                $tableName = isset($matches[1]) ? $matches[1] : '未知表';
                echo "✅ 创建表: {$tableName}\n";
            } else if (stripos($statement, 'ALTER TABLE') !== false) {
                preg_match('/ALTER TABLE.*?`(\w+)`/i', $statement, $matches);
                $tableName = isset($matches[1]) ? $matches[1] : '未知表';
                echo "🔧 修改表: {$tableName}\n";
            } else if (stripos($statement, 'INSERT') !== false) {
                echo "📝 插入数据\n";
            } else if (stripos($statement, 'UPDATE') !== false) {
                preg_match('/UPDATE.*?`(\w+)`/i', $statement, $matches);
                $tableName = isset($matches[1]) ? $matches[1] : '未知表';
                echo "🔄 更新表: {$tableName}\n";
            }
            
        } catch (PDOException $e) {
            $errorCount++;
            echo "❌ SQL执行错误: " . $e->getMessage() . "\n";
            echo "   语句: " . substr($statement, 0, 100) . "...\n";
            
            // 检查是否是非关键错误（如字段已存在）
            if (strpos($e->getMessage(), 'Duplicate column name') !== false ||
                strpos($e->getMessage(), 'already exists') !== false) {
                echo "   (这是非关键错误，可以忽略)\n";
                $errorCount--; // 不计入错误数
            }
        }
    }
    
    echo "\n🎯 数据库初始化结果:\n";
    echo "✅ 成功执行: {$successCount} 条语句\n";
    echo "❌ 错误数量: {$errorCount} 条\n";
    
    if ($errorCount === 0) {
        echo "\n🎉 竞技场系统数据库初始化完成！\n";
    } else {
        echo "\n⚠️ 初始化完成，但有一些错误，请检查上面的错误信息。\n";
    }
    
    // 验证关键表是否创建成功
    echo "\n🔍 验证关键表:\n";
    $tables = [
        'immortal_arena_ranks' => '段位表',
        'immortal_arena_records' => '记录表', 
        'immortal_arena_match_pool' => '匹配池表'
    ];
    
    foreach ($tables as $table => $description) {
        try {
            $result = $pdo->query("SHOW TABLES LIKE '{$table}'")->fetch();
            if ($result) {
                echo "✅ {$description} ({$table}) - 存在\n";
            } else {
                echo "❌ {$description} ({$table}) - 不存在\n";
            }
        } catch (Exception $e) {
            echo "❌ {$description} ({$table}) - 检查失败: " . $e->getMessage() . "\n";
        }
    }
    
    // 检查角色表的新字段
    echo "\n🔍 验证角色表新字段:\n";
    try {
        $result = $pdo->query("DESCRIBE characters")->fetchAll(PDO::FETCH_ASSOC);
        $arenaFields = array_filter($result, function($field) {
            return strpos($field['Field'], 'arena_') === 0;
        });
        
        echo "✅ 发现竞技场字段: " . count($arenaFields) . " 个\n";
        foreach ($arenaFields as $field) {
            echo "   - {$field['Field']} ({$field['Type']})\n";
        }
        
    } catch (Exception $e) {
        echo "❌ 检查角色表字段失败: " . $e->getMessage() . "\n";
    }
    
} catch (Exception $e) {
    echo "💥 初始化失败: " . $e->getMessage() . "\n";
    exit(1);
}
?> 