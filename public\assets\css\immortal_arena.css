/* 升仙大会竞技系统样式 */

.arena-page {
    background: url('../images/arena_bg.jpg') no-repeat center center fixed;
    background-size: cover;
    position: relative;
    min-height: 100vh;
    color: #ffffff;
    overflow-x: hidden;
}

body.arena-page::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    z-index: -1;
}

.main-container {
    padding: 8px 8px 80px 8px;
    height: 100vh;
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    max-width: 800px;
    margin: 0 auto;
}

/* === 头部信息区域 === */
.header {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.12), rgba(255, 255, 255, 0.04));
    border-radius: 12px;
    padding: 8px 12px;
    margin-bottom: 8px;
    border: 1px solid rgba(212, 175, 55, 0.4);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 40px;
}

.header-title {
    font-size: 16px;
    font-weight: bold;
    color: #d4af37;
    display: flex;
    align-items: center;
    gap: 6px;
}

.back-btn {
    background: rgba(212, 175, 55, 0.2);
    border: 1px solid rgba(212, 175, 55, 0.5);
    border-radius: 8px;
    color: #d4af37;
    padding: 4px 8px;
    font-size: 12px;
    cursor: pointer;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 3px;
}

/* === 角色信息卡片 === */
.character-info-section {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.03));
    border-radius: 12px;
    padding: 12px;
    margin-bottom: 8px;
    border: 1px solid rgba(212, 175, 55, 0.4);
    backdrop-filter: blur(8px);
    display: flex;
    align-items: center;
    gap: 12px;
}

.character-card {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
}

.character-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(212, 175, 55, 0.3);
}

.character-avatar-content {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-size: cover;
    background-position: center;
}

.character-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.character-name {
    font-size: 16px;
    font-weight: bold;
    color: #d4af37;
}

.character-realm {
    font-size: 12px;
    color: #4CAF50;
}

.character-power {
    font-size: 12px;
    color: #bdc3c7;
}

.rank-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.current-rank {
    display: flex;
    align-items: center;
    gap: 4px;
    background: rgba(212, 175, 55, 0.2);
    padding: 4px 8px;
    border-radius: 6px;
    border: 1px solid rgba(212, 175, 55, 0.5);
}

.rank-icon {
    font-size: 16px;
}

.rank-text {
    font-size: 12px;
    color: #d4af37;
    font-weight: bold;
}

.rank-progress {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
}

.progress-text {
    font-size: 12px;
    color: #bdc3c7;
}

.rank-points {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1px;
}

.points-text {
    font-size: 12px;
    color: #f39c12;
    font-weight: bold;
}

.points-needed {
    font-size: 10px;
    color: #95a5a6;
}

/* === 今日战绩区域 === */
.arena-summary-section {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.03));
    border-radius: 12px;
    padding: 12px;
    margin-bottom: 12px;
    border: 1px solid rgba(182, 160, 160, 0.4);
    backdrop-filter: blur(8px);
}

.daily-stats {
    width: 100%;
}

.stats-title {
    text-align: center;
    font-size: 14px;
    font-weight: bold;
    color: #d4af37;
    margin-bottom: 10px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
}

.stat-item {
    text-align: center;
    background: rgba(182, 160, 160, 0.1);
    border-radius: 8px;
    padding: 8px 4px;
    border: 1px solid rgba(182, 160, 160, 0.3);
}

.stat-value {
    font-size: 16px;
    font-weight: bold;
    color: #d4af37;
    margin-bottom: 2px;
}

.stat-label {
    font-size: 10px;
    color: #bdc3c7;
}

/* 移除购买按钮相关样式 */
.summary-left,
.summary-right,
.attempts-info,
.attempts-item,
.attempts-icon,
.buy-attempts-btn {
    display: none;
}

/* === 主要功能区 === */
.arena-actions {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.03));
    border-radius: 12px;
    padding: 12px;
    margin-top: 8px;
    border: 1px solid rgba(52, 152, 219, 0.4);
    backdrop-filter: blur(8px);
}

.match-section {
    margin-bottom: 12px;
}

.match-btn {
    width: 100%;
    background: linear-gradient(135deg, #3498db, #2980b9);
    border: none;
    border-radius: 12px;
    color: white;
    padding: 12px 20px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.match-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.5);
}

/* 🔧 新增：无挑战次数时的按钮样式 */
.match-btn.no-attempts {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.match-btn.no-attempts:hover {
    background: linear-gradient(135deg, #c0392b, #a93226);
    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.5);
}

.match-btn.no-attempts .match-text::after {
    content: ' (购买次数)';
    font-size: 10px;
    opacity: 0.8;
}

.match-btn-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.match-icon {
    font-size: 18px;
}

.match-text {
    font-weight: bold;
}

.match-status {
    text-align: center;
    margin: 6px 0px 6px 0px;
    font-size: 12px;
    color: #bdc3c7;
}

.function-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
}

.function-btn {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border: 1px solid rgba(212, 175, 55, 0.3);
    border-radius: 8px;
    color: #d4af37;
    padding: 10px 8px;
    cursor: pointer;
    text-align: center;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.function-btn:hover {
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.2), rgba(212, 175, 55, 0.1));
    transform: translateY(-1px);
}

.function-icon {
    font-size: 18px;
    margin-bottom: 4px;
}

.function-text {
    font-size: 12px;
    font-weight: bold;
}

/* 删除重复的今日战绩样式，已在上方定义 */

/* === 弹窗通用样式 === */
.modal-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal-overlay.show {
    display: flex;
}

.modal-content {
    background: linear-gradient(135deg, rgba(26, 26, 46, 0.95), rgba(20, 20, 35, 0.95));
    border-radius: 16px;
    border: 2px solid rgba(212, 175, 55, 0.6);
    backdrop-filter: blur(15px);
    width: 95vw;
    max-width: 450px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.modal-header {
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.3), rgba(212, 175, 55, 0.1));
    padding: 12px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(212, 175, 55, 0.4);
}

.modal-title {
    font-size: 16px;
    font-weight: bold;
    color: #d4af37;
}

.modal-close {
    background: rgba(231, 76, 60, 0.8);
    border: none;
    border-radius: 50%;
    color: white;
    width: 28px;
    height: 28px;
    cursor: pointer;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-body {
    padding: 16px;
    color: #ecf0f1;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 12px 16px;
    border-top: 1px solid rgba(212, 175, 55, 0.4);
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.modal-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.modal-btn.cancel {
    background: rgba(149, 165, 166, 0.8);
    color: white;
}

.modal-btn.confirm {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
}



/* === 战绩记录弹窗 === */
.records-list {
    min-height: 200px;
    max-height: 400px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(212, 175, 55, 0.5) transparent;
}

.records-list::-webkit-scrollbar {
    width: 6px;
}

.records-list::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.records-list::-webkit-scrollbar-thumb {
    background: rgba(212, 175, 55, 0.5);
    border-radius: 3px;
}

.records-list::-webkit-scrollbar-thumb:hover {
    background: rgba(212, 175, 55, 0.7);
}

.record-item {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.02));
    border-radius: 10px;
    padding: 12px 15px;
    margin-bottom: 8px;
    border: 1px solid rgba(212, 175, 55, 0.2);
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.record-item:hover {
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.15), rgba(212, 175, 55, 0.05));
    border-color: rgba(212, 175, 55, 0.4);
    transform: translateY(-1px);
}

.record-opponent {
    font-size: 14px;
    font-weight: bold;
    color: #d4af37;
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.record-details {
    font-size: 10px;
    color: #95a5a6;
    font-weight: normal;
}

.record-result {
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: bold;
    margin: 0 8px;
}

.record-result.win {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
    box-shadow: 0 2px 8px rgba(39, 174, 96, 0.3);
}

.record-result.lose {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
}

.record-reward {
    font-size: 12px;
    color: #f39c12;
    font-weight: bold;
    margin-right: 8px;
}

.record-time {
    font-size: 10px;
    color: #95a5a6;
    white-space: nowrap;
}

/* === 排行榜弹窗 === */
.rankings-list {
    min-height: 200px;
    max-height: 400px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(155, 89, 182, 0.5) transparent;
}

.rankings-list::-webkit-scrollbar {
    width: 6px;
}

.rankings-list::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.rankings-list::-webkit-scrollbar-thumb {
    background: rgba(155, 89, 182, 0.5);
    border-radius: 3px;
}

.rankings-list::-webkit-scrollbar-thumb:hover {
    background: rgba(155, 89, 182, 0.7);
}

.ranking-item {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.02));
    border-radius: 10px;
    padding: 12px 15px;
    margin-bottom: 8px;
    border: 1px solid rgba(155, 89, 182, 0.2);
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.ranking-item:hover {
    background: linear-gradient(135deg, rgba(155, 89, 182, 0.15), rgba(155, 89, 182, 0.05));
    border-color: rgba(155, 89, 182, 0.4);
    transform: translateY(-1px);
}

.ranking-item:nth-child(1) {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.15), rgba(255, 215, 0, 0.05));
    border-color: rgba(255, 215, 0, 0.4);
}

.ranking-item:nth-child(2) {
    background: linear-gradient(135deg, rgba(192, 192, 192, 0.15), rgba(192, 192, 192, 0.05));
    border-color: rgba(192, 192, 192, 0.4);
}

.ranking-item:nth-child(3) {
    background: linear-gradient(135deg, rgba(205, 127, 50, 0.15), rgba(205, 127, 50, 0.05));
    border-color: rgba(205, 127, 50, 0.4);
}

.ranking-position {
    font-size: 16px;
    font-weight: bold;
    color: #d4af37;
    margin-right: 12px;
    min-width: 25px;
    text-align: center;
}

.ranking-item:nth-child(1) .ranking-position {
    color: #FFD700;
}

.ranking-item:nth-child(2) .ranking-position {
    color: #C0C0C0;
}

.ranking-item:nth-child(3) .ranking-position {
    color: #CD7F32;
}

.ranking-name {
    font-size: 14px;
    font-weight: bold;
    color: #ecf0f1;
    flex: 1;
    min-width: 0;
    margin-right: 10px;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.ranking-stats {
    font-size: 10px;
    color: #95a5a6;
    font-weight: normal;
}

.ranking-power {
    font-size: 12px;
    color: #3498db;
    font-weight: bold;
    margin-right: 10px;
    min-width: 60px;
    text-align: right;
}

.ranking-rank {
    font-size: 11px;
    color: #9b59b6;
    font-weight: bold;
    background: rgba(155, 89, 182, 0.2);
    padding: 3px 6px;
    border-radius: 4px;
    border: 1px solid rgba(155, 89, 182, 0.3);
    min-width: 50px;
    text-align: center;
}

.loading {
    text-align: center;
    color: #bdc3c7;
    font-size: 14px;
    padding: 40px 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    border: 1px dashed rgba(189, 195, 199, 0.3);
}

/* === 规则弹窗 === */
.rules-content {
    max-height: 400px;
    overflow-y: auto;
}

.rule-section {
    margin-bottom: 16px;
}

.rule-section h4 {
    color: #d4af37;
    font-size: 14px;
    margin-bottom: 8px;
}

.rule-section ul {
    margin: 0;
    padding-left: 20px;
}

.rule-section li {
    margin-bottom: 4px;
    font-size: 12px;
    line-height: 1.4;
}

/* === 匹配结果弹窗 === */
.opponent-info {
    text-align: center;
    margin-bottom: 20px;
}

.battle-countdown {
    text-align: center;
}

.countdown-text {
    font-size: 14px;
    color: #bdc3c7;
    margin-bottom: 8px;
}

.countdown-number {
    font-size: 32px;
    font-weight: bold;
    color: #e74c3c;
}

/* === 响应式设计 === */
@media (max-width: 768px) {
    .main-container {
        padding: 6px 6px 75px 6px;
    }
    
    .header {
        height: 36px;
        padding: 6px 10px;
    }
    
    .header-title {
        font-size: 14px;
    }
    
    .back-btn {
        font-size: 11px;
        padding: 3px 6px;
    }
    
    .character-info-section {
        padding: 10px;
        gap: 10px;
    }
    
    .character-avatar {
        width: 90px;
        height: 90px;
    }
    
    .character-name {
        font-size: 14px;
    }
    
    .character-realm,
    .character-power {
        font-size: 12px;
    }
    
    .stats-title {
        font-size: 12px;
    }
    
    .stat-value {
        font-size: 14px;
    }
    
    .stat-label {
        font-size: 10px;
    }
    
    .arena-actions {
        padding: 10px;
    }
    
    .match-btn {
        padding: 10px 16px;
        font-size: 14px;
    }
    
    .function-btn {
        padding: 8px 6px;
    }
    
    .function-icon {
        font-size: 16px;
    }
    
    .function-text {
        font-size: 11px;
    }
    
    .arena-summary-section {
        padding: 10px;
    }
    
    .stats-grid {
        gap: 6px;
    }
    
    .stat-item {
        padding: 6px 3px;
    }
    
    .modal-content {
        width: 98vw;
        max-width: 420px;
        max-height: 85vh;
    }
    
    .modal-title {
        font-size: 14px;
    }
    
    .modal-body {
        padding: 12px;
    }
    
    .rule-section h4 {
        font-size: 13px;
    }
    
    .rule-section li {
        font-size: 11px;
    }
    
    /* 战绩记录移动端样式 */
    .record-item {
        padding: 10px 12px;
        flex-direction: column;
        align-items: flex-start;
        gap: 6px;
    }
    
    .record-opponent {
        font-size: 13px;
    }
    
    .record-details {
        font-size: 9px;
    }
    
    .record-result {
        margin: 0;
        align-self: flex-end;
        margin-top: -20px;
    }
    
    .record-reward {
        font-size: 11px;
        margin-right: 0;
    }
    
    .record-time {
        font-size: 9px;
        align-self: flex-end;
    }
    
    /* 排行榜移动端样式 */
    .ranking-item {
        padding: 10px 12px;
    }
    
    .ranking-position {
        font-size: 14px;
        margin-right: 8px;
        min-width: 20px;
    }
    
    .ranking-name {
        font-size: 13px;
        margin-right: 8px;
    }
    
    .ranking-stats {
        font-size: 9px;
    }
    
    .ranking-power {
        font-size: 11px;
        margin-right: 8px;
        min-width: 50px;
    }
    
    .ranking-rank {
        font-size: 10px;
        padding: 2px 4px;
        min-width: 45px;
    }
}

@media (max-width: 375px) {
    .main-container {
        padding: 4px 4px 70px 4px;
    }
    
    .character-info-section {
        padding: 8px;
        gap: 8px;
    }
    
    .character-avatar {
        width: 80px;
        height: 80px;
    }
    
    .character-name {
        font-size: 18px;
    }
    
    .character-realm,
    .character-power {
        font-size: 12px;
    }
    
    .function-grid {
        gap: 6px;
    }
    
    .function-btn {
        padding: 6px 4px;
    }
    
    .function-icon {
        font-size: 14px;
    }
    
    .function-text {
        font-size: 10px;
    }
    
    .stats-grid {
        gap: 4px;
    }
    
    .stat-item {
        padding: 4px 2px;
    }
    
    .stat-value {
        font-size: 12px;
    }
    
    .stat-label {
        font-size: 8px;
    }
    
    /* 战绩记录超小屏幕样式 */
    .record-item {
        padding: 8px 10px;
        gap: 4px;
    }
    
    .record-opponent {
        font-size: 12px;
    }
    
    .record-details {
        font-size: 8px;
    }
    
    .record-result {
        font-size: 11px;
        padding: 3px 6px;
    }
    
    .record-reward {
        font-size: 10px;
    }
    
    .record-time {
        font-size: 8px;
    }
    
    /* 排行榜超小屏幕样式 */
    .ranking-item {
        padding: 8px 10px;
    }
    
    .ranking-position {
        font-size: 12px;
        margin-right: 6px;
        min-width: 18px;
    }
    
    .ranking-name {
        font-size: 12px;
        margin-right: 6px;
    }
    
    .ranking-stats {
        font-size: 8px;
    }
    
    .ranking-power {
        font-size: 10px;
        margin-right: 6px;
        min-width: 40px;
    }
    
    .ranking-rank {
        font-size: 9px;
        padding: 2px 3px;
        min-width: 40px;
    }
}

/* === 隐藏类 === */
.hidden {
    display: none !important;
}

/* === 特殊效果 === */
.glow-effect {
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
}

.pulse-animation {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
} 