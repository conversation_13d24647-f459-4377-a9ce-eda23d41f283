/* ========================================
 * 怪物技能CSS动画模板
 * 基于普通攻击技能修复经验制作
 * 使用说明：复制此文件，替换所有"template"为实际技能名
 * ======================================== */

/* 动画容器 */
.template-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1000;
}

/* ========================================
 * 第一阶段：蓄力准备动画
 * ======================================== */

/* 蓄力核心 */
.template-charge-core {
    width: 30px;
    height: 30px;
    background: radial-gradient(circle, 
        #ffffff 0%, 
        #87CEEB 20%, 
        #4682B4 50%, 
        #1E90FF 70%,
        transparent 90%);
    border-radius: 50%;
    animation: templateChargeCore 1s ease-out;
    box-shadow: 
        0 0 20px #87CEEB,
        0 0 40px rgba(135, 206, 235, 0.6);
}

@keyframes templateChargeCore {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.2) rotate(0deg);
        box-shadow: 0 0 10px #87CEEB;
    }
    50% {
        opacity: 0.8;
        transform: translate(-50%, -50%) scale(1.2) rotate(180deg);
        box-shadow: 
            0 0 25px #4682B4,
            0 0 50px rgba(70, 130, 180, 0.8);
    }
    100% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.5) rotate(360deg);
        box-shadow: 
            0 0 30px #1E90FF,
            0 0 60px rgba(30, 144, 255, 0.6);
    }
}

/* 武器发光效果 */
.template-weapon-sprite {
    width: 40px;
    height: 40px;
    animation: templateWeaponGlow 1s ease-out;
    filter: drop-shadow(0 0 15px #87CEEB);
}

@keyframes templateWeaponGlow {
    0% {
        opacity: 0.5;
        transform: translate(-50%, -50%) scale(1) rotate(0deg);
        filter: drop-shadow(0 0 8px #87CEEB);
    }
    50% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.1) rotate(180deg);
        filter: drop-shadow(0 0 20px #4682B4);
    }
    100% {
        opacity: 0.8;
        transform: translate(-50%, -50%) scale(1) rotate(360deg);
        filter: drop-shadow(0 0 15px #1E90FF);
    }
}

/* 蓄力粒子 */
.template-charge-particle {
    width: 6px;
    height: 6px;
    background: radial-gradient(circle, #ffffff, #87CEEB);
    border-radius: 50%;
    animation: templateChargeParticle 1s ease-out;
    box-shadow: 0 0 8px rgba(135, 206, 235, 0.8);
}

@keyframes templateChargeParticle {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) translate(0, 0) scale(0.3);
    }
    30% {
        opacity: 1;
        transform: translate(-50%, -50%) translate(var(--particleX), var(--particleY)) scale(1);
    }
    70% {
        opacity: 0.8;
        transform: translate(-50%, -50%) translate(calc(var(--particleX) * 0.5), calc(var(--particleY) * 0.5)) scale(0.8);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -50%) translate(0, 0) scale(0.2);
    }
}

/* ========================================
 * 第二阶段：攻击发射动画 - 关键修复点
 * ======================================== */

/* 主攻击弹道 */
.template-projectile {
    width: 60px;
    height: 8px;
    background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(255, 255, 255, 0.8) 10%,
        #87CEEB 25%, 
        #4682B4 50%, 
        #1E90FF 65%,
        #87CEEB 75%, 
        rgba(255, 255, 255, 0.8) 90%,
        transparent 100%);
    border-radius: 8px 4px 4px 8px;
    animation: templateProjectileFly 1s ease-out;
    box-shadow: 
        0 0 15px #87CEEB,
        0 0 30px rgba(135, 206, 235, 0.6),
        0 0 45px rgba(30, 144, 255, 0.3);
    filter: blur(0.3px);
    position: relative;
}

.template-projectile::before {
    content: '';
    position: absolute;
    right: -6px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-left: 12px solid #4682B4;
    filter: drop-shadow(0 0 8px #87CEEB);
}

/* ✅ 正确的飞行动画实现 - 核心修复 */
@keyframes templateProjectileFly {
    0% {
        opacity: 0;
        left: var(--startX);
        top: var(--startY);
        transform: translate(-50%, -50%) rotate(var(--angle)) scale(0.4);
        box-shadow: 0 0 8px #87CEEB;
    }
    15% {
        opacity: 0.8;
        transform: translate(-50%, -50%) rotate(var(--angle)) scale(1.1);
        box-shadow: 
            0 0 18px #4682B4,
            0 0 35px rgba(70, 130, 180, 0.7);
    }
    30% {
        opacity: 1;
        transform: translate(-50%, -50%) rotate(var(--angle)) scale(1);
        box-shadow: 
            0 0 20px #87CEEB,
            0 0 40px rgba(135, 206, 235, 0.8),
            0 0 60px rgba(30, 144, 255, 0.4);
    }
    100% {
        opacity: 0.6;
        left: var(--targetX);
        top: var(--targetY);
        transform: translate(-50%, -50%) rotate(var(--angle)) scale(1.2);
        box-shadow: 
            0 0 25px #1E90FF,
            0 0 50px rgba(30, 144, 255, 0.6);
    }
}

/* 拖尾效果 */
.template-trail {
    width: 40px;
    height: 4px;
    background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(135, 206, 235, 0.6) 20%,
        #87CEEB 50%, 
        rgba(135, 206, 235, 0.6) 80%,
        transparent 100%);
    border-radius: 2px;
    animation: templateTrailFollow 1s ease-out 0.08s;
    opacity: 0.8;
    box-shadow: 0 0 10px rgba(135, 206, 235, 0.5);
}

@keyframes templateTrailFollow {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) rotate(var(--angle)) scale(0.2);
    }
    20% {
        opacity: 0.8;
        transform: translate(-50%, -50%) rotate(var(--angle)) scale(1.1);
        box-shadow: 0 0 12px rgba(135, 206, 235, 0.7);
    }
    40% {
        opacity: 0.9;
        transform: translate(-50%, -50%) rotate(var(--angle)) scale(1);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -50%) rotate(var(--angle)) scale(0.3);
        left: calc(var(--startX) + var(--targetX));
        top: calc(var(--startY) + var(--targetY));
        box-shadow: 0 0 5px rgba(135, 206, 235, 0.3);
    }
}

/* ========================================
 * 第三阶段：击中效果动画
 * ======================================== */

/* 爆炸效果 */
.template-explosion {
    width: 80px;
    height: 80px;
    background: radial-gradient(circle, 
        #ffffff 0%, 
        #87CEEB 15%, 
        #4682B4 40%, 
        #1E90FF 60%,
        transparent 85%);
    border-radius: 50%;
    animation: templateExplosionBurst 0.8s ease-out;
    box-shadow: 
        0 0 30px #87CEEB,
        0 0 60px rgba(135, 206, 235, 0.6);
}

@keyframes templateExplosionBurst {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.3) rotate(0deg);
        box-shadow: 0 0 15px #87CEEB;
    }
    30% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.5) rotate(120deg);
        box-shadow: 
            0 0 40px #4682B4,
            0 0 80px rgba(70, 130, 180, 0.8);
    }
    70% {
        opacity: 0.8;
        transform: translate(-50%, -50%) scale(1.2) rotate(240deg);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(2) rotate(360deg);
        box-shadow: 0 0 20px rgba(135, 206, 235, 0.4);
    }
}

/* 冲击波效果 */
.template-shockwave {
    width: 60px;
    height: 60px;
    border: 3px solid #87CEEB;
    border-radius: 50%;
    animation: templateShockwaveExpand 0.6s ease-out;
    box-shadow: 
        0 0 20px rgba(135, 206, 235, 0.6),
        inset 0 0 15px rgba(135, 206, 235, 0.3);
}

@keyframes templateShockwaveExpand {
    0% {
        opacity: 0.9;
        transform: translate(-50%, -50%) scale(0.2);
        border-width: 4px;
        box-shadow: 
            0 0 15px rgba(135, 206, 235, 0.8),
            inset 0 0 8px rgba(135, 206, 235, 0.5);
    }
    40% {
        opacity: 0.8;
        transform: translate(-50%, -50%) scale(1.5);
        border-width: 3px;
    }
    70% {
        opacity: 0.5;
        transform: translate(-50%, -50%) scale(2.5);
        border-width: 2px;
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(4);
        border-width: 1px;
        box-shadow: 
            0 0 8px rgba(135, 206, 235, 0.3),
            inset 0 0 5px rgba(135, 206, 235, 0.2);
    }
}

/* 散射粒子 */
.template-scatter {
    width: 8px;
    height: 8px;
    background: radial-gradient(circle, #ffffff, #87CEEB, transparent);
    border-radius: 50%;
    animation: templateScatterFly 0.8s ease-out;
    box-shadow: 0 0 12px rgba(135, 206, 235, 0.8);
}

@keyframes templateScatterFly {
    0% {
        opacity: 1;
        transform: translate(-50%, -50%) translate(0, 0) scale(1);
        box-shadow: 0 0 12px rgba(135, 206, 235, 0.8);
    }
    30% {
        opacity: 0.9;
        transform: translate(-50%, -50%) translate(calc(var(--scatterX) * 0.5), calc(var(--scatterY) * 0.5)) scale(1.2);
        box-shadow: 0 0 15px rgba(70, 130, 180, 0.9);
    }
    70% {
        opacity: 0.6;
        transform: translate(-50%, -50%) translate(calc(var(--scatterX) * 0.8), calc(var(--scatterY) * 0.8)) scale(0.8);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -50%) translate(var(--scatterX), var(--scatterY)) scale(0.3);
        box-shadow: 0 0 5px rgba(135, 206, 235, 0.4);
    }
}

/* ========================================
 * 移动端适配
 * ======================================== */

@media (max-width: 768px) {
    .template-container {
        /* 移动端容器调整 */
    }
    
    .template-charge-core {
        width: 25px;
        height: 25px;
    }
    
    .template-projectile {
        width: 45px;
        height: 6px;
    }
    
    .template-trail {
        width: 30px;
        height: 3px;
    }
    
    .template-explosion {
        width: 60px;
        height: 60px;
    }
    
    .template-shockwave {
        width: 45px;
        height: 45px;
    }
    
    .template-scatter {
        width: 6px;
        height: 6px;
    }
    
    .template-weapon-sprite {
        width: 30px;
        height: 30px;
    }
    
    .template-charge-particle {
        width: 4px;
        height: 4px;
    }
}

/* ========================================
 * 使用说明：
 * 1. 复制此文件为新技能名-animations.css
 * 2. 替换所有"template"为实际技能名
 * 3. 修改颜色方案（当前为蓝色系，可改为火红、冰蓝、雷紫等）
 * 4. 调整尺寸、时机、特效强度
 * 5. 保持关键的飞行动画结构（left/top变化）
 * 6. 确保移动端适配完整
 * ======================================== */ 