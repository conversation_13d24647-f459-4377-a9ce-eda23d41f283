/**
 * 万剑诀技能模块
 * 对应 animation_model = 'wanjianjue'
 */

/**
 * 万剑诀技能
 */
class WanJianJueSkill extends BaseSkill {
    async execute(skillData, weaponImage) {
        // 使用数据库中的真实技能名称，而不是硬编码
        const skillName = skillData?.skillName || skillData?.displayName || '万剑诀'; // 提供默认值作为后备
        await this.showSkillShout(skillName);
        // 🔧 修复：根据技能使用者动态确定攻击方向
        await this.createCompleteSkillSwords(skillData, weaponImage);
    }

    async createCompleteSkillSwords(currentSkillData, weaponImage) {
        // 🔧 动态判断位置映射
        let casterPos;
        
        if (this.isEnemySkill) {
            // 敌方技能：敌人是施法者
            casterPos = this.getCharacterPosition(false); // 敌人位置
        } else {
            // 我方技能：玩家是施法者
            casterPos = this.getCharacterPosition(true);  // 玩家位置
        }
        
        // 使用施法者位置作为剑阵中心
        const playerCenterX = casterPos.x;
        const playerCenterY = casterPos.y;

        // 创建剑阵特效容器
        const formationContainer = document.createElement('div');
        formationContainer.className = 'sword-formation-container';
        this.effectsContainer.appendChild(formationContainer);

        // 创建剑阵能量场
        const formationEnergy = document.createElement('div');
        formationEnergy.className = 'sword-formation-energy';
        formationEnergy.style.left = `${playerCenterX}px`;
        formationEnergy.style.top = `${playerCenterY}px`;
        formationContainer.appendChild(formationEnergy);

        // 创建剑阵环形特效
        for (let i = 0; i < 3; i++) {
            const circle = document.createElement('div');
            circle.className = 'sword-formation-circle';
            circle.style.left = `${playerCenterX}px`;
            circle.style.top = `${playerCenterY}px`;
            circle.style.width = `${80 + i * 40}px`;
            circle.style.height = `${80 + i * 40}px`;
            circle.style.animationDelay = `${i * 0.2}s`;
            formationContainer.appendChild(circle);
        }

        // 创建剑阵符文点
        const numRunes = 12; // 减少符文点数量以配合6把剑
        for (let i = 0; i < numRunes; i++) {
            const rune = document.createElement('div');
            rune.className = 'sword-formation-rune';
            
            const angle = (i / numRunes) * Math.PI * 2;
            const radius = 60 + (i % 3) * 15; // 稍微调整符文点半径
            const runeX = playerCenterX + Math.cos(angle) * radius;
            const runeY = playerCenterY + Math.sin(angle) * radius;
            
            rune.style.left = `${runeX}px`;
            rune.style.top = `${runeY}px`;
            rune.style.animationDelay = `${i * 0.05}s`; // 调整延迟以适应更少符文点
            
            formationContainer.appendChild(rune);
        }

        const swords = [];
        const numSwords = 6; // 修改为6把剑
        const radius = 60; // 保持半径
        
        // 计算每把飞剑的位置 - 6把剑均匀分布
        for (let i = 0; i < numSwords; i++) {
            const finalAngle = i * 360 / numSwords; // 每60度一把剑
            const delay = i * 0.08; // 调整延迟时间，给每把剑更多间隔
            
            swords.push(this.createFlyingSword({
                skillSword: true,
                radius,
                finalAngle,
                delay,
                index: i,
                totalSwords: numSwords,
                weaponImage: weaponImage
            }));
        }
        
        // 等待剑阵特效完成后移除 (调整为1.2秒的剑阵生成时间)
        setTimeout(() => {
            formationContainer.remove();
        }, 1000);
        
        await Promise.all(swords);
    }

    // 🔧 完全对标原版的createFlyingSword方法（技能剑部分）
    createFlyingSword(options = {}) {
        const sword = document.createElement('div');
        sword.className = 'flying-sword';
        
        // 🔧 武器图片处理 - 对标原版
        if (options.weaponImage) {
            const weaponImg = document.createElement('img');
            weaponImg.src = options.weaponImage;
            weaponImg.className = 'weapon-image';
            // 🗡️ 动态调整武器图片角度
            weaponImg.style.transform = `rotate(${this.calculateInitialSwordRotation()}deg)`;
            weaponImg.onerror = () => {
                // 图片加载失败时使用默认飞剑图片
                if (window.ImagePathManager) {
                    weaponImg.src = window.ImagePathManager.getWeaponImage('battle_sword.png');
                } else {
                    weaponImg.src = 'assets/images/battle_sword.png';
                }
            };
            sword.appendChild(weaponImg);
        } else {
            // 使用默认背景图片（兼容性）
            if (window.ImagePathManager) {
                sword.style.backgroundImage = `url('${window.ImagePathManager.getWeaponImage('battle_sword.png')}')`;
            } else {
                sword.style.backgroundImage = `url('assets/images/battle_sword.png')`;
            }
        }
        
        // 🔧 动态判断敌方剑样式
        if (this.isEnemySkill) {
            sword.classList.add('enemy-sword');
        }
        
        if (options.skillSword) {
            sword.classList.add('skill-sword');
            sword.style.setProperty('--swordIndex', options.index || 0);
            // 完全对标原版：使用度数而不是弧度
            const angleDegrees = options.index * 360 / (options.totalSwords || 6);
            sword.style.setProperty('--swordAngle', `${angleDegrees}deg`);
            // 传递半径参数
            sword.style.setProperty('--swordRadius', `${options.radius || 60}px`);
        }

        // 🔧 动态判断位置映射
        let casterPos, targetPos;
        
        if (this.isEnemySkill) {
            // 敌方技能：敌人是施法者，玩家是目标
            casterPos = this.getCharacterPosition(false); // 敌人位置
            targetPos = this.getCharacterPosition(true);  // 玩家位置
        } else {
            // 我方技能：玩家是施法者，敌人是目标
            casterPos = this.getCharacterPosition(true);  // 玩家位置
            targetPos = this.getCharacterPosition(false); // 敌人位置
        }

        if (options.skillSword) {
            // 使用动态位置数据
            const centerX = casterPos.x;
            const centerY = casterPos.y + 15; // 万剑诀向下调整15像素
            
            // 目标位置
            const targetCenterX = targetPos.x;
            const targetCenterY = targetPos.y;
            
            // 🔧 动态计算穿透方向
            let direction, penetrateDistance;
            
            if (this.isEnemySkill) {
                // 敌方技能：从敌人向玩家穿透（向下）
                direction = 1;
                penetrateDistance = 120;
            } else {
                // 我方技能：从玩家向敌人穿透（向上）
                direction = -1;
                penetrateDistance = 120;
            }
            
            const finalTargetY = targetCenterY + (direction * penetrateDistance);
            
            // 设置初始和目标位置 - 完全对标原版
            sword.style.setProperty('--initialX', `${centerX}px`);
            sword.style.setProperty('--initialY', `${centerY}px`);
            sword.style.setProperty('--targetX', `${targetCenterX}px`);
            sword.style.setProperty('--attackTargetY', `${targetCenterY}px`);
            sword.style.setProperty('--finalTargetY', `${finalTargetY}px`);
            
            // 设置动画序列 - 三阶段动画 - 完全对标原版
            const appearTime = 1; // 生成和旋转时间 (从0.6秒调整到1.2秒)
            const attackDelay = options.delay || 0;
            const attackTime = 0.4; // 飞向目标时间 (从0.4秒调整到0.8秒)
            const penetrateTime = 0.5; // 穿透时间 (从0.3秒调整到0.5秒)
            
            sword.style.animation = `
                skill-sword-appear ${appearTime}s cubic-bezier(0.3, 0, 0.2, 1) forwards,
                skill-sword-attack ${attackTime}s cubic-bezier(0.3, 0, 0.2, 1) ${appearTime + attackDelay}s forwards,
                skill-sword-penetrate ${penetrateTime}s ease-out ${appearTime + attackDelay + attackTime}s forwards
            `;
            
            this.effectsContainer.appendChild(sword);
            
            // 🔧 修复：在飞剑击中目标时创建受击特效和动画
            setTimeout(() => {
                // 🔧 修复：使用正确的击中特效
                this.createHitEffect(targetCenterX, targetCenterY, true);
                
                // 🔧 修复：为被攻击者添加受击动画
                this.addEnemyHitAnimation();
            }, (appearTime + attackDelay + attackTime) * 1000);
            
            return new Promise(resolve => {
                setTimeout(() => {
                    sword.remove();
                    resolve();
                }, (appearTime + attackDelay + attackTime + penetrateTime) * 1000);
            });
        }
    }
    
    /**
     * 🔧 修复：动态判断受击动画位置
     */
    addEnemyHitAnimation() {
        // 🔧 修复：动态判断被攻击目标
        const targetSelector = !this.isEnemySkill ? '.enemy .character-sprite' : '.player .character-sprite';
        const targetSprite = document.querySelector(targetSelector);
        
        if (targetSprite) {
            // 清除之前的动画
            targetSprite.style.animation = '';
            
            // 强制重绘
            targetSprite.offsetHeight;
            
            // 添加万剑诀受击动画 - 使用强化的剑类受击动画
            targetSprite.style.animation = 'sword-hit-shake 0.5s ease-out';
            
            console.log(`⚔️ 万剑诀：为${!this.isEnemySkill ? '敌人' : '玩家'}添加受击动画`);
            
            // 动画结束后清理
            setTimeout(() => {
                if (targetSprite && targetSprite.style.animation.includes('sword-hit-shake')) {
                    targetSprite.style.animation = '';
                }
            }, 500);
        } else {
            console.warn(`⚠️ 万剑诀：未找到目标元素，无法添加受击动画`);
        }
    }

    // 🗡️ 动态计算剑的初始旋转角度
    calculateInitialSwordRotation() {
        // 敌方技能：剑尖向下（指向玩家），我方技能：剑尖向上（指向敌人）
        return this.isEnemySkill ? 180 : 0;
    }
}

// 导出技能类
window.WanJianJueSkills = { WanJianJueSkill }; 