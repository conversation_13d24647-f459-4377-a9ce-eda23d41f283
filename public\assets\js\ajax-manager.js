/**
 * 统一的AJAX管理系统 - 一念修仙项目
 * 提供加载状态、错误处理、重试机制等功能
 * 让所有表单提交体验更加统一和流畅
 */
class AjaxManager {
    constructor() {
        this.globalConfig = {
            timeout: 30000, // 30秒超时
            retryAttempts: 3, // 重试3次
            retryDelay: 1000, // 重试延迟1秒
            showLoading: true, // 显示加载状态
            autoHideError: true // 自动隐藏错误
        };
        this.requestQueue = new Map();
        this.init();
    }
    
    /**
     * 初始化AJAX管理器
     */
    init() {
        this.createGlobalLoader();
        this.setupNetworkMonitoring();
        console.log('🚀 AJAX管理器初始化完成');
    }
    
    /**
     * 创建全局加载遮罩
     */
    createGlobalLoader() {
        if (document.getElementById('globalLoader')) return;
        
        // 确保DOM已准备好
        if (!document.body) {
            // 如果body还没准备好，延迟创建
            setTimeout(() => this.createGlobalLoader(), 100);
            return;
        }
        
        const loader = document.createElement('div');
        loader.id = 'globalLoader';
        loader.innerHTML = `
            <div class="loader-content">
                <div class="loader-spinner"></div>
                <div class="loader-text">处理中...</div>
            </div>
        `;
        
        // 添加样式
        const styles = `
            #globalLoader {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: none;
                justify-content: center;
                align-items: center;
                z-index: 9999;
                backdrop-filter: blur(2px);
            }
            
            .loader-content {
                background: white;
                padding: 30px;
                border-radius: 8px;
                text-align: center;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
                max-width: 300px;
            }
            
            .loader-spinner {
                width: 40px;
                height: 40px;
                border: 3px solid #f3f3f3;
                border-top: 3px solid #d4af37;
                border-radius: 50%;
                animation: ajaxSpin 1s linear infinite;
                margin: 0 auto 15px;
            }
            
            .loader-text {
                color: #333;
                font-size: 16px;
                font-weight: 500;
            }
            
            @keyframes ajaxSpin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            
            /* 移动端适配 */
            @media (max-width: 768px) {
                .loader-content {
                    padding: 20px;
                    margin: 0 20px;
                }
                .loader-text {
                    font-size: 14px;
                }
            }
        `;
        
        const styleSheet = document.createElement('style');
        styleSheet.textContent = styles;
        document.head.appendChild(styleSheet);
        document.body.appendChild(loader);
    }
    
    /**
     * 显示全局加载状态
     */
    showGlobalLoader(text = '处理中...') {
        const loader = document.getElementById('globalLoader');
        if (loader) {
            const textElement = loader.querySelector('.loader-text');
            if (textElement) {
                textElement.textContent = text;
            }
            loader.style.display = 'flex';
        }
    }
    
    /**
     * 隐藏全局加载状态
     */
    hideGlobalLoader() {
        const loader = document.getElementById('globalLoader');
        if (loader) {
            loader.style.display = 'none';
        }
    }
    
    /**
     * 统一的AJAX请求方法
     * @param {Object} options 请求配置
     * @returns {Promise} 请求Promise
     */
    async request(options) {
        const config = {
            ...this.globalConfig,
            ...options,
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            }
        };
        
        const requestId = this.generateRequestId();
        
        try {
            // 显示加载状态
            if (config.showLoading) {
                this.showGlobalLoader(config.loadingText || '请求处理中...');
            }
            
            // 创建请求控制器
            const controller = new AbortController();
            this.requestQueue.set(requestId, controller);
            
            // 设置超时
            const timeoutId = setTimeout(() => {
                controller.abort();
            }, config.timeout);
            
            // 发送请求
            const response = await fetch(config.url, {
                method: config.method || 'GET',
                headers: config.headers,
                body: config.body ? JSON.stringify(config.body) : undefined,
                signal: controller.signal,
                credentials: 'same-origin'
            });
            
            // 清除超时
            clearTimeout(timeoutId);
            
            // 检查响应状态
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            // 解析响应数据
            const contentType = response.headers.get('content-type');
            let result;
            
            if (contentType && contentType.includes('application/json')) {
                result = await response.json();
            } else {
                result = await response.text();
            }
            
            return result;
            
        } catch (error) {
            return this.handleError(error, config, requestId);
        } finally {
            // 清理
            this.requestQueue.delete(requestId);
            if (config.showLoading) {
                this.hideGlobalLoader();
            }
        }
    }
    
    /**
     * 错误处理
     */
    async handleError(error, config, requestId) {
        console.error('📡 AJAX请求失败:', error);
        
        // 重试机制
        if (config.retryAttempts > 0 && this.shouldRetry(error)) {
            console.log(`🔄 重试请求 (剩余次数: ${config.retryAttempts - 1})`);
            
            // 延迟后重试
            await this.delay(config.retryDelay);
            
            return this.request({
                ...config,
                retryAttempts: config.retryAttempts - 1,
                showLoading: false // 重试时不再显示加载
            });
        }
        
        // 生成友好的错误消息
        const errorMessage = this.getErrorMessage(error);
        
        // 显示错误消息
        if (config.showError !== false) {
            this.showErrorMessage(errorMessage, config.autoHideError);
        }
        
        // 抛出错误
        throw {
            error,
            message: errorMessage,
            config,
            requestId
        };
    }
    
    /**
     * 判断是否应该重试
     */
    shouldRetry(error) {
        // 网络错误、超时或服务器错误才重试
        return error.name === 'TypeError' || 
               error.name === 'AbortError' ||
               (error.message && error.message.includes('HTTP 5'));
    }
    
    /**
     * 获取友好的错误消息
     */
    getErrorMessage(error) {
        if (!navigator.onLine) {
            return '网络连接已断开，请检查网络后重试';
        }
        
        if (error.name === 'AbortError') {
            return '请求超时，请稍后重试';
        }
        
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
            return '无法连接到服务器，请检查网络连接';
        }
        
        if (error.message && error.message.includes('HTTP')) {
            if (error.message.includes('HTTP 404')) {
                return '请求的资源不存在';
            } else if (error.message.includes('HTTP 403')) {
                return '权限不足，请重新登录';
            } else if (error.message.includes('HTTP 500')) {
                return '服务器内部错误，请稍后重试';
            } else {
                return `服务器错误: ${error.message}`;
            }
        }
        
        return '请求失败，请稍后重试';
    }
    
    /**
     * 显示错误消息
     */
    showErrorMessage(message, autoHide = true) {
        // 优先使用项目中现有的消息提示系统
        if (window.showMessage && typeof window.showMessage === 'function') {
            window.showMessage(message, 'error');
        } else {
            // 创建临时错误提示
            this.createTempErrorMessage(message, autoHide);
        }
    }
    
    /**
     * 创建临时错误消息
     */
    createTempErrorMessage(message, autoHide) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'ajax-temp-error';
        errorDiv.textContent = message;
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #ff4d4f;
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(255, 77, 79, 0.3);
            z-index: 10000;
            font-size: 14px;
            max-width: 400px;
            text-align: center;
            animation: slideDown 0.3s ease-out;
        `;
        
        // 添加动画样式
        if (!document.getElementById('tempErrorStyles')) {
            const styles = document.createElement('style');
            styles.id = 'tempErrorStyles';
            styles.textContent = `
                @keyframes slideDown {
                    from {
                        opacity: 0;
                        transform: translateX(-50%) translateY(-20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateX(-50%) translateY(0);
                    }
                }
            `;
            document.head.appendChild(styles);
        }
        
        // 确保DOM已准备好
        if (document.body) {
            document.body.appendChild(errorDiv);
        } else {
            // 如果body还没准备好，延迟创建
            setTimeout(() => {
                if (document.body) {
                    document.body.appendChild(errorDiv);
                }
            }, 100);
        }
        
        // 自动移除
        if (autoHide) {
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.style.animation = 'slideDown 0.3s ease-out reverse';
                    setTimeout(() => errorDiv.remove(), 300);
                }
            }, 3000);
        }
        
        // 点击移除
        errorDiv.addEventListener('click', () => {
            errorDiv.remove();
        });
    }
    
    /**
     * 网络状态监控
     */
    setupNetworkMonitoring() {
        window.addEventListener('online', () => {
            console.log('🌐 网络已连接');
        });
        
        window.addEventListener('offline', () => {
            console.log('🔌 网络已断开');
            this.showErrorMessage('网络连接已断开，部分功能可能受影响');
        });
    }
    
    /**
     * 工具方法
     */
    generateRequestId() {
        return 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    /**
     * 取消所有进行中的请求
     */
    cancelAllRequests() {
        this.requestQueue.forEach((controller) => {
            controller.abort();
        });
        this.requestQueue.clear();
        this.hideGlobalLoader();
        console.log('❌ 已取消所有进行中的请求');
    }
    
    /**
     * 便捷方法 - GET请求
     */
    get(url, options = {}) {
        return this.request({
            url,
            method: 'GET',
            ...options
        });
    }
    
    /**
     * 便捷方法 - POST请求
     */
    post(url, body, options = {}) {
        return this.request({
            url,
            method: 'POST',
            body,
            ...options
        });
    }
    
    /**
     * 便捷方法 - PUT请求
     */
    put(url, body, options = {}) {
        return this.request({
            url,
            method: 'PUT',
            body,
            ...options
        });
    }
    
    /**
     * 便捷方法 - DELETE请求
     */
    delete(url, options = {}) {
        return this.request({
            url,
            method: 'DELETE',
            ...options
        });
    }
    
    /**
     * 表单自动AJAX化
     * 自动将传统表单转换为AJAX提交
     */
    autoAjaxifyForms(selector = 'form[data-ajax="true"]') {
        const forms = document.querySelectorAll(selector);
        
        forms.forEach(form => {
            form.addEventListener('submit', async (e) => {
                e.preventDefault();
                
                const formData = new FormData(form);
                const data = {};
                
                // 转换FormData为对象
                for (let [key, value] of formData.entries()) {
                    data[key] = value;
                }
                
                const url = form.action || form.getAttribute('data-url');
                const method = form.method || 'POST';
                
                try {
                    const result = await this.request({
                        url,
                        method: method.toUpperCase(),
                        body: data,
                        loadingText: form.getAttribute('data-loading') || '提交中...'
                    });
                    
                    // 触发成功事件
                    form.dispatchEvent(new CustomEvent('ajaxSuccess', {
                        detail: { result, form }
                    }));
                    
                } catch (error) {
                    // 触发失败事件
                    form.dispatchEvent(new CustomEvent('ajaxError', {
                        detail: { error, form }
                    }));
                }
            });
        });
        
        console.log(`🔄 已为 ${forms.length} 个表单启用AJAX提交`);
    }
}

// 确保在DOM准备好后创建全局实例
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeAjaxManager);
} else {
    // DOM已经准备好了
    initializeAjaxManager();
}

function initializeAjaxManager() {
    // 创建全局实例
    window.ajaxManager = new AjaxManager();
    
    // 自动为标记的表单启用AJAX
    window.ajaxManager.autoAjaxifyForms();
    
    console.log('✅ AJAX管理器已初始化完成');
}

// 兼容性方法 - 保持现有代码兼容
window.unifiedFetch = function(url, options = {}) {
    return window.ajaxManager.request({
        url,
        ...options
    });
};

 