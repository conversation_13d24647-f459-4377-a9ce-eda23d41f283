-- 创建道具表
CREATE TABLE IF NOT EXISTS game_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL COMMENT '道具名称',
    type ENUM('weapon', 'equipment', 'consumable', 'material') NOT NULL COMMENT '道具类型',
    quality TINYINT NOT NULL DEFAULT 1 COMMENT '品质等级',
    description TEXT COMMENT '道具描述',
    attributes JSON COMMENT '道具属性',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='游戏道具表';

-- 创建地图表
CREATE TABLE IF NOT EXISTS game_maps (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL COMMENT '地图名称',
    type ENUM('normal', 'elite', 'boss') NOT NULL COMMENT '地图类型',
    level INT NOT NULL DEFAULT 1 COMMENT '地图等级',
    description TEXT COMMENT '地图描述',
    unlock_condition JSON COMMENT '解锁条件',
    rewards JSON COMMENT '通关奖励',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='游戏地图表';

-- 创建怪物表
CREATE TABLE IF NOT EXISTS game_monsters (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL COMMENT '怪物名称',
    type ENUM('normal', 'elite', 'boss') NOT NULL COMMENT '怪物类型',
    level INT NOT NULL DEFAULT 1 COMMENT '怪物等级',
    hp INT NOT NULL COMMENT '生命值',
    attack INT NOT NULL COMMENT '攻击力',
    defense INT NOT NULL COMMENT '防御力',
    speed INT NOT NULL COMMENT '速度',
    skills JSON COMMENT '技能列表',
    description TEXT COMMENT '怪物描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='游戏怪物表';

-- 创建掉落表
CREATE TABLE IF NOT EXISTS game_drops (
    id INT AUTO_INCREMENT PRIMARY KEY,
    monster_id INT NOT NULL COMMENT '怪物ID',
    item_id INT NOT NULL COMMENT '道具ID',
    probability DECIMAL(5,2) NOT NULL COMMENT '掉落概率',
    min_quantity INT NOT NULL DEFAULT 1 COMMENT '最小数量',
    max_quantity INT NOT NULL DEFAULT 1 COMMENT '最大数量',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (monster_id) REFERENCES game_monsters(id) ON DELETE CASCADE,
    FOREIGN KEY (item_id) REFERENCES game_items(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='游戏掉落表';

-- 添加一些测试数据
INSERT INTO game_items (name, type, quality, description, attributes) VALUES
('青铜剑', 'weapon', 1, '最基础的武器，适合初学者使用', '{"attack": 10, "durability": 100}'),
('铁甲', 'equipment', 2, '普通的铁甲，提供基础防御', '{"defense": 15, "durability": 150}'),
('回血丹', 'consumable', 1, '恢复少量生命值', '{"hp_recovery": 50}');

INSERT INTO game_maps (name, type, level, description, unlock_condition, rewards) VALUES
('新手村', 'normal', 1, '适合新手历练的地方', '{"player_level": 1}', '{"exp": 100, "gold": 50}'),
('狼人谷', 'elite', 5, '充满狼人的危险之地', '{"player_level": 5}', '{"exp": 500, "gold": 200}');

INSERT INTO game_monsters (name, type, level, hp, attack, defense, speed, skills, description) VALUES
('小狼', 'normal', 1, 100, 10, 5, 10, '["撕咬"]', '常见的野狼'),
('狼王', 'boss', 5, 500, 50, 30, 15, '["撕咬", "嚎叫", "召唤小狼"]', '狼群的首领');

INSERT INTO game_drops (monster_id, item_id, probability, min_quantity, max_quantity) VALUES
(1, 1, 20.00, 1, 1),
(2, 2, 50.00, 1, 1),
(2, 3, 100.00, 1, 3); 