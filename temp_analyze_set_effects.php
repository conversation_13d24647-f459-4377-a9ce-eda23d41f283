<?php
try {
    $pdo = new PDO('mysql:host=127.0.0.1;port=3306;dbname=yn_game;charset=utf8mb4', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== 分析所有套装特殊效果 ===\n\n";
    
    // 查询所有套装的特殊效果
    $stmt = $pdo->query("SELECT id, set_name, effects FROM game_item_sets ORDER BY id");
    $sets = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $problemEffects = [];
    $validEffects = [];
    
    foreach ($sets as $set) {
        echo "【{$set['set_name']}】 (ID: {$set['id']})\n";
        $effects = json_decode($set['effects'], true);
        
        if ($effects) {
            foreach (['two_piece', 'four_piece', 'six_piece'] as $level) {
                if (isset($effects[$level])) {
                    echo "  $level: ";
                    
                    // 检查是否有特殊效果
                    if (isset($effects[$level]['special_effect'])) {
                        $specialEffect = $effects[$level]['special_effect'];
                        echo "特殊效果: $specialEffect\n";
                        
                        // 分析问题效果
                        $hasProblems = false;
                        $problems = [];
                        
                        if (strpos($specialEffect, '秒') !== false) {
                            $problems[] = "包含时间概念(秒)，不符合回合制";
                            $hasProblems = true;
                        }
                        
                        if (strpos($specialEffect, '普通攻击') !== false) {
                            $problems[] = "提到普通攻击，玩家只有技能攻击";
                            $hasProblems = true;
                        }
                        
                        if (strpos($specialEffect, '每3次') !== false || strpos($specialEffect, '每5次') !== false) {
                            $problems[] = "计数机制复杂，不适合简单回合制";
                            $hasProblems = true;
                        }
                        
                        if (strpos($specialEffect, '持续') !== false && strpos($specialEffect, '回合') === false) {
                            $problems[] = "持续效果未明确回合数";
                            $hasProblems = true;
                        }
                        
                        if ($hasProblems) {
                            $problemEffects[] = [
                                'set_name' => $set['set_name'],
                                'level' => $level,
                                'effect' => $specialEffect,
                                'problems' => $problems
                            ];
                        } else {
                            $validEffects[] = [
                                'set_name' => $set['set_name'],
                                'level' => $level,
                                'effect' => $specialEffect
                            ];
                        }
                    } else {
                        echo "仅属性加成\n";
                    }
                }
            }
        } else {
            echo "  无效果配置\n";
        }
        echo "\n";
    }
    
    echo "\n=== 问题效果分析 ===\n";
    foreach ($problemEffects as $problem) {
        echo "❌ {$problem['set_name']} ({$problem['level']}): {$problem['effect']}\n";
        foreach ($problem['problems'] as $issue) {
            echo "   - $issue\n";
        }
        echo "\n";
    }
    
    echo "\n=== 有效效果 ===\n";
    foreach ($validEffects as $valid) {
        echo "✅ {$valid['set_name']} ({$valid['level']}): {$valid['effect']}\n";
    }
    
    echo "\n=== 统计 ===\n";
    echo "问题效果数量: " . count($problemEffects) . "\n";
    echo "有效效果数量: " . count($validEffects) . "\n";
    
} catch (Exception $e) {
    echo '错误: ' . $e->getMessage() . "\n";
}
?>
