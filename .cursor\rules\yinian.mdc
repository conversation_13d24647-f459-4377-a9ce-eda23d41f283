---
description: 
globs: 
alwaysApply: true
---
# 🎮 一念修仙 - AI Agent 核心开发规则

## 📜 1. AI Agent 核心行为准则

*AI Agent 说明：此为最高优先级指令。在执行任何任务前，必须回顾并严格遵守。*

- **语言与沟通**: 始终使用中文简体进行所有交流、代码注释和文档编写。
- **错误排查**: 严格遵循“前端JS控制台 → 后端PHP日志 → 数据库连接与查询 → API接口返回 → 网络请求”的顺序进行全面排查。
- **开发效率**:
    - **禁止创建测试脚本**: 专注于代码实现，不为功能编写一次性测试脚本。
    - **由用户验收**: 完成代码修改后，应告知用户进行前端测试和功能验收。
- **决策制定**:
    - **文档优先**: 解决问题前，必须优先查阅第2节列出的项目文档。
    - **冲突解决**: 若代码实现与文档冲突，以**实际代码和数据库结构**为最终标准。
    - **请求决策**: 当信息不足或存在多种方案时，必须向用户说明情况，请求决策。
- **文件操作**:
    - **禁止使用 Shell**: 绝对禁止使用 shell（尤其是 PowerShell）修改 PHP 文件，以防编码错误。
    - **禁止创建副本**: 严禁创建任何带有 `-fixed`, `-backup`, `-copy`, `-temp` 后缀的重复文件。
- **操作偏好**:
    - **主动执行**: 在明确任务后，应直接执行操作，而不是将具体步骤交由用户完成。
    - **手动替换优先**: 对于大型或复杂文件的修改，倾向于手动逐条替换，而不是批量替换，以避免潜在错误。

## 📚 2. 文档与信息源优先级

*AI Agent 说明：这是获取项目信息的主要来源，请按顺序查阅。*

1.  **数据库结构**: `DATABASE_SCHEMA.md` - **最高权威**，数据相关操作的唯一参考。
2.  **最新实现状态**: `GAME_DEVELOPMENT_DOCS.md` - 了解项目目录结构、核心功能和最新更新。
3.  **项目状态跟踪**: `project_status_tracker.md` - 查看最新的开发进度。
4.  **专项文档**: 根据任务查阅 `docs/` 目录下的具体系统文档（如装备、安全、技能等）。
5.  **原始设计参考**: `一念.md` - **仅供参考**，用于理解最初的设计理念，与实际代码冲突时无效。

## 💻 3. 技术栈与环境约束

*AI Agent 说明：编码时必须遵循的技术限制。*

- **PHP 版本**: `7.43nts`。禁止使用 PHP 8.0+ 的新特性（如 `??` 空合并运算符）。
- **MySQL 版本**: `5.7.2`。
- **数据库引擎**: 所有表必须使用 `InnoDB`。
- **数据库字符集**: 必须使用 `utf8mb4`。

## 🏗️ 4. 核心系统设计与架构规则

*AI Agent 说明：此处为各系统的核心设计原则，详细实现请查阅对应文档。*

### 📁 数据库核心规则
- **表结构权威**: 在使用任何数据库表名、字段名之前，必须通过实际查询 `DATABASE_SCHEMA.md` 或数据库本身来验证其存在性和正确性。
- **单表设计**: 物品系统已合并为单表设计，**严禁使用或查询 `item_attributes` 表**，所有物品信息均在 `game_items` 表中。
- **字段废弃**:
    - `expReward` 字段已废弃，统一使用灵石 (`spirit_stone`) 相关字段。
    - `magic_attack`/`magic_defense` 已统一为 `immortal_attack`/`immortal_defense`。

### 📡 前后端交互
- **API 路径**: 前端JS中的API相对路径必须根据页面实际位置确定 (例如，`public/` 目录下的页面应使用 `../src/api/` 前缀)。
- **AJAX管理器**: 新功能优先使用 `public/assets/js/ajax-manager.js` (`ajaxManager.post()`, `ajaxManager.get()`) 进行异步请求。
- **数据一致性**: 避免直接使用API返回的局部数据更新前端状态，应在操作成功后重新调用主数据加载函数，确保前后端数据结构完全同步。
- **安全计算**: 严禁从前端获取任何奖励数值（如灵石、掉落物品），所有奖励计算必须在后端完成。

### 🎯 技能系统
- **独立文件**: 新技能必须采用独立文件架构：`{技能名}-skill.js` + `{技能名}-animations.css`。
- **独立实现**: 新技能必须独立实现其所有功能，**禁止跨技能类复用或调用内部方法**，以保证模块的独立性和可维护性。
- **动画来源**: 技能动画的触发完全由后端返回的 `animation_model` 字段决定，前端禁止建立技能名到动画的映射逻辑。

## ✍️ 5. 详细编码与开发规范

### 📛 命名规范
- **JS类名**: 所有JavaScript的类名（class）必须使用英文驼峰式命名（如 `HuoQiuShuSkill`），禁止使用中文。
- **字段命名**: 遵循 `_base` (基础值), `_total` (计算后总值), `_equipment` (装备提供值) 的后缀约定来区分不同类型的属性。

### 🎨 代码风格与质量
- **函数复用**: 优先扩展现有函数，避免创建功能重复的新函数（Don't Repeat Yourself）。
- **禁止引用API文件**: 在任何PHP文件中，**严禁 `require` 或 `include` 其他API文件** (如 `src/api/some_api.php`)，以避免双重JSON输出和意外的逻辑执行。

### 💅 前端规范
- **CSS动画**: 元素飞行移动必须使用 `left` / `top` 属性，而非 `transform: translate()`。图片发光效果必须使用 `filter: drop-shadow()`。
- **DOM选择器**: 在编写JS DOM选择器前，必须先检查HTML文件，确认元素的真实ID和class，特别是动态变化的class。
- **格式化**: 所有涉及百分比显示的地方，必须保留2位小数（JS: `.toFixed(2)`）。

### 🐛 调试规范
- **战斗日志**: 战斗系统中的所有 `console.log` 都应替换为 `BattleDebugConfig.log` 条件日志，以实现全局控制。

## 🔄 6. 文档更新规则

*AI Agent 说明：在完成特定类型的修改后，需要履行更新对应文档的职责。*

- **数据库变动** → 更新 `DATABASE_SCHEMA.md`
- **目录或程序结构变动** → 更新 `GAME_DEVELOPMENT_DOCS.md`
- **各子系统功能变动** → 更新其在 `docs/` 目录下的对应文档。

---
*规则文件优化日期: 2024年12月19日*
*此文件是AI agent的核心行为指南，并非项目的完整文档。*