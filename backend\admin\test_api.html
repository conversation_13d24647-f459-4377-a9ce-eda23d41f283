<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>后台API测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #fff;
            min-height: 100vh;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        .test-item {
            margin: 20px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .test-button {
            background: linear-gradient(135deg, #4CAF50, #66bb6a);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            opacity: 0.8;
        }
        .log-area {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            height: 300px;
            overflow-y: auto;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 后台API连接测试</h1>
        
        <div class="test-item">
            <h3>📍 当前位置信息</h3>
            <p id="locationInfo">正在获取...</p>
        </div>

        <div class="test-item">
            <h3>🧪 API测试</h3>
            <button class="test-button" onclick="testCheckAPI()">测试API状态</button>
            <button class="test-button" onclick="testAuthAPI()">测试认证API</button>
            <button class="test-button" onclick="testAdminAPI()">测试管理API</button>
            <button class="test-button" onclick="testDirectPath()">测试直接路径</button>
            <button class="test-button" onclick="clearLog()">清空日志</button>
        </div>

        <div class="log-area" id="logArea"></div>

        <div class="test-item">
            <h3>📋 路径信息</h3>
            <ul>
                <li><strong>认证API:</strong> api/admin_auth.php</li>
                <li><strong>管理API:</strong> api/admin_api.php</li>
                <li><strong>当前目录:</strong> /backend/admin/</li>
                <li><strong>实际路径:</strong> <span id="actualPath"></span></li>
            </ul>
        </div>
    </div>

    <script>
        function log(message) {
            const logArea = document.getElementById('logArea');
            const time = new Date().toLocaleTimeString();
            logArea.innerHTML += `[${time}] ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
        }

        function clearLog() {
            document.getElementById('logArea').innerHTML = '';
        }

        // 页面加载时显示基本信息
        document.addEventListener('DOMContentLoaded', function() {
            const locationInfo = document.getElementById('locationInfo');
            const actualPath = document.getElementById('actualPath');
            
            locationInfo.innerHTML = `
                <strong>URL:</strong> ${window.location.href}<br>
                <strong>协议:</strong> ${window.location.protocol}<br>
                <strong>主机:</strong> ${window.location.host}<br>
                <strong>路径:</strong> ${window.location.pathname}
            `;
            
            actualPath.textContent = window.location.pathname;
            
            log('🚀 后台API测试开始...');
            log(`📍 当前位置: ${window.location.pathname}`);
        });

        // 测试API状态检查
        async function testCheckAPI() {
            log('🔍 测试API状态检查...');
            
            try {
                log('   请求: check_api.php');
                const response = await fetch('check_api.php');
                log(`   状态: ${response.status} ${response.statusText}`);
                log(`   Content-Type: ${response.headers.get('content-type')}`);
                
                if (response.ok) {
                    const data = await response.text();
                    log(`   原始响应: ${data}`);
                    
                    try {
                        const json = JSON.parse(data);
                        log('   ✅ API状态检查成功');
                        log(`   📊 API状态: ${json.status}`);
                        log(`   📝 消息: ${json.message}`);
                        log(`   🗂️ 文件结构:`);
                        log(`     - admin_auth.php: ${json.file_structure.admin_auth_exists ? '✅' : '❌'}`);
                        log(`     - admin_api.php: ${json.file_structure.admin_api_exists ? '✅' : '❌'}`);
                        log(`     - api目录: ${json.file_structure.api_dir_exists ? '✅' : '❌'}`);
                        log(`   🗄️ 数据库: ${json.database.status === 'connected' ? '✅' : '❌'} ${json.database.message}`);
                        if (json.database.admin_table_exists !== undefined) {
                            log(`     - admin_users表: ${json.database.admin_table_exists ? '✅' : '❌'}`);
                        }
                    } catch (e) {
                        log(`   ❌ JSON解析失败: ${e.message}`);
                    }
                } else {
                    log(`   ❌ HTTP错误: ${response.status}`);
                }
            } catch (error) {
                log(`   ❌ 网络错误: ${error.message}`);
            }
        }

        // 测试认证API
        async function testAuthAPI() {
            log('🔐 测试认证API...');
            
            try {
                // 测试登录检查
                log('   请求: api/admin_auth.php');
                const response = await fetch('api/admin_auth.php?action=check_login');
                log(`   状态: ${response.status} ${response.statusText}`);
                log(`   Content-Type: ${response.headers.get('content-type')}`);
                
                if (response.ok) {
                    const data = await response.text();
                    log(`   原始响应: ${data}`);
                    
                    try {
                        const json = JSON.parse(data);
                        log(`   JSON响应: ${JSON.stringify(json)}`);
                        if (json.success) {
                            log('   ✅ 认证API响应正常');
                        } else {
                            log('   ⚠️ 未登录状态');
                        }
                    } catch (e) {
                        log(`   ❌ JSON解析失败: ${e.message}`);
                    }
                } else {
                    log(`   ❌ HTTP错误: ${response.status}`);
                }
            } catch (error) {
                log(`   ❌ 网络错误: ${error.message}`);
            }
        }

        // 测试管理API
        async function testAdminAPI() {
            log('📊 测试管理API...');
            
            try {
                log('   请求: api/admin_api.php?action=stats');
                const response = await fetch('api/admin_api.php?action=stats');
                log(`   状态: ${response.status} ${response.statusText}`);
                log(`   Content-Type: ${response.headers.get('content-type')}`);
                
                if (response.ok) {
                    const data = await response.text();
                    log(`   原始响应: ${data}`);
                    
                    try {
                        const json = JSON.parse(data);
                        log(`   JSON响应: ${JSON.stringify(json)}`);
                        if (json.success) {
                            log('   ✅ 管理API响应正常');
                        } else {
                            log('   ⚠️ 需要登录');
                        }
                    } catch (e) {
                        log(`   ❌ JSON解析失败: ${e.message}`);
                    }
                } else {
                    log(`   ❌ HTTP错误: ${response.status}`);
                }
            } catch (error) {
                log(`   ❌ 网络错误: ${error.message}`);
            }
        }

        // 测试直接路径
        async function testDirectPath() {
            log('🔗 测试直接路径访问...');
            
            const testPaths = [
                './api/admin_auth.php',
                '../admin/api/admin_auth.php',
                './admin_auth.php',
                'admin_auth.php'
            ];
            
            for (const path of testPaths) {
                try {
                    log(`   测试路径: ${path}`);
                    const response = await fetch(path);
                    log(`     状态: ${response.status} ${response.statusText}`);
                    
                    if (response.status === 404) {
                        log('     ❌ 文件不存在');
                    } else if (response.ok) {
                        log('     ✅ 路径可访问');
                    } else {
                        log(`     ⚠️ 其他错误: ${response.status}`);
                    }
                } catch (error) {
                    log(`     ❌ 访问错误: ${error.message}`);
                }
            }
        }
    </script>
</body>
</html> 