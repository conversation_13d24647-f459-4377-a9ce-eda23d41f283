<?php
/**
 * 临时测试adventure_maps.php API
 * 测试完成后请删除此文件
 */

echo "<h1>🧪 Adventure Maps API 测试</h1>";
echo "<p>测试时间: " . date('Y-m-d H:i:s') . "</p>";

// 设置模拟会话
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
$_SESSION['user_id'] = 19;
$_SESSION['character_id'] = 31;

echo "<h2>📊 会话信息</h2>";
echo "- user_id: " . ($_SESSION['user_id'] ?? 'null') . "<br>";
echo "- character_id: " . ($_SESSION['character_id'] ?? 'null') . "<br><br>";

// 测试API调用
$testUrls = [
    'get_maps' => '/yinian/src/api/adventure_maps.php?action=get_maps',
    'get_user_progress' => '/yinian/src/api/adventure_maps.php?action=get_user_progress&map_code=kunlun'
];

foreach ($testUrls as $testName => $url) {
    echo "<h2>🔍 测试 {$testName}</h2>";
    
    // 使用curl测试API
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://127.0.0.1' . $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HEADER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_COOKIE, session_name() . '=' . session_id());
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "HTTP状态码: {$httpCode}<br>";
    
    if ($error) {
        echo "❌ CURL错误: {$error}<br>";
    } else {
        echo "✅ 请求成功<br>";
        
        // 尝试解析JSON
        $data = json_decode($response, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            echo "✅ JSON解析成功<br>";
            echo "响应数据: <pre>" . htmlspecialchars(json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "</pre>";
        } else {
            echo "❌ JSON解析失败: " . json_last_error_msg() . "<br>";
            echo "原始响应: <pre>" . htmlspecialchars($response) . "</pre>";
        }
    }
    
    echo "<br>";
}

echo "<h2>📝 总结</h2>";
echo "<p>✅ 测试完成</p>";
echo "<p>⚠️ 请根据上述结果判断API是否正常工作</p>";
echo "<p>📝 测试完成后请删除此临时文件</p>";

?>
