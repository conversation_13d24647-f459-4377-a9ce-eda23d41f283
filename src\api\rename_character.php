<?php
// 🔧 最优先：完全禁用所有错误输出
error_reporting(0);
ini_set('display_errors', 0);
ini_set('log_errors', 0);

// 🔧 修复：避免重复启动session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// 🔧 临时禁用错误输出，确保JSON响应纯净
error_reporting(0);
ini_set('display_errors', 0);

header('Content-Type: application/json; charset=utf-8');

// 添加调试模式
$debug = true;

// 调试输出函数
function debugLog($message, $data = null) {
    global $debug;
    if ($debug) {
        error_log("RENAME DEBUG: " . $message . ($data ? " | DATA: " . json_encode($data) : ""));
    }
}

// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    debugLog("用户未登录");
    echo json_encode([
        'success' => false,
        'message' => '请先登录'
    ]);
    exit;
}

debugLog("用户ID", $_SESSION['user_id']);

try {
    // 直接建立数据库连接
    debugLog("尝试连接数据库");
    $pdo = new PDO('mysql:host=localhost;dbname=yn_game;charset=utf8mb4', 'ynxx', 'mjlxz159');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    debugLog("数据库连接成功");
    
    $user_id = $_SESSION['user_id'];
    
    // 处理不同的操作
    $action = 'get_rename_info'; // 默认值
    
    if (isset($_GET['action'])) {
        $action = $_GET['action'];
    } elseif (isset($_POST['action'])) {
        $action = $_POST['action'];
    } else {
        $rawInput = file_get_contents('php://input');
        if ($rawInput) {
            $inputData = json_decode($rawInput, true);
            if ($inputData && isset($inputData['action'])) {
                $action = $inputData['action'];
            }
        }
    }
    
    debugLog("处理操作", $action);
    
    switch ($action) {
        case 'get_rename_info':
            getRenameInfo($user_id, $pdo);
            break;
            
        case 'rename_character':
            renameCharacter($user_id, $pdo);
            break;
            
        default:
            debugLog("未知操作", $action);
            echo json_encode([
                'success' => false,
                'message' => '未知的操作: ' . $action
            ]);
            break;
    }
    
} catch (PDOException $e) {
    debugLog("数据库错误", $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => '数据库错误，请稍后重试',
        'debug_error' => $e->getMessage()
    ]);
} catch (Exception $e) {
    debugLog("系统错误", $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => '系统错误，请稍后重试',
        'debug_error' => $e->getMessage()
    ]);
}

// 获取更名信息
function getRenameInfo($userId, $pdo) {
    try {
        debugLog("开始获取更名信息", $userId);
        
        // 先检查表是否存在
        debugLog("检查表结构");
        $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
        $usersExists = $stmt->fetch() ? true : false;
        debugLog("users表存在", $usersExists);
        
        $stmt = $pdo->query("SHOW TABLES LIKE 'characters'");
        $charactersExists = $stmt->fetch() ? true : false;
        debugLog("characters表存在", $charactersExists);
        
        $stmt = $pdo->query("SHOW TABLES LIKE 'character_name_changes'");
        $nameChangesExists = $stmt->fetch() ? true : false;
        debugLog("character_name_changes表存在", $nameChangesExists);
        
        if (!$usersExists || !$charactersExists) {
            debugLog("缺少必要的表");
            echo json_encode([
                'success' => false,
                'message' => '数据库表不存在'
            ]);
            return;
        }
        
        // 检查users表字段
        debugLog("检查users表字段");
        $stmt = $pdo->query("DESCRIBE users");
        $userFields = $stmt->fetchAll(PDO::FETCH_ASSOC);
        debugLog("users表字段", array_column($userFields, 'Field'));
        
        // 检查characters表字段
        debugLog("检查characters表字段");
        $stmt = $pdo->query("DESCRIBE characters");
        $characterFields = $stmt->fetchAll(PDO::FETCH_ASSOC);
        debugLog("characters表字段", array_column($characterFields, 'Field'));
        
        // 查询用户的角色信息和灵石
        debugLog("查询用户角色信息");
        $sql = "
            SELECT 
                c.id as character_id,
                c.character_name,
                u.spirit_stones,
                u.username,
                u.nickname,
                c.created_at as character_created_at
            FROM characters c 
            JOIN users u ON c.user_id = u.id 
            WHERE c.user_id = ?
            ORDER BY c.created_at DESC 
            LIMIT 1
        ";
        debugLog("执行SQL", $sql);
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$userId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        debugLog("查询结果", $result);
        
        if (!$result) {
            debugLog("未找到角色信息");
            echo json_encode([
                'success' => false,
                'message' => '未找到角色信息，请先创建角色'
            ]);
            return;
        }
        
        $characterId = $result['character_id'];
        $currentName = $result['character_name'];
        $currentSpiritStone = (int)$result['spirit_stones'];
        
        debugLog("角色基本信息", [
            'character_id' => $characterId,
            'current_name' => $currentName,
            'spirit_stones' => $currentSpiritStone
        ]);
        
        // 查询更名记录
        if ($nameChangesExists) {
            debugLog("查询更名记录");
            
            // 先检查character_name_changes表的字段结构
            $stmt = $pdo->query("DESCRIBE character_name_changes");
            $nameChangeFields = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $nameChangeFieldNames = array_column($nameChangeFields, 'Field');
            debugLog("character_name_changes表字段", $nameChangeFieldNames);
            
            $hasChangeReason = in_array('change_reason', $nameChangeFieldNames);
            
            if ($hasChangeReason) {
                // 如果有change_reason字段，使用完整查询
                $stmt = $pdo->prepare("
                    SELECT COUNT(*) as rename_count, MAX(change_time) as last_rename_time
                    FROM character_name_changes 
                    WHERE character_id = ? AND change_reason = 'user_request'
                ");
            } else {
                // 如果没有change_reason字段，查询所有记录
                $stmt = $pdo->prepare("
                    SELECT COUNT(*) as rename_count, MAX(change_time) as last_rename_time
                    FROM character_name_changes 
                    WHERE character_id = ?
                ");
            }
            
            $stmt->execute([$characterId]);
            $renameData = $stmt->fetch(PDO::FETCH_ASSOC);
            debugLog("更名记录", $renameData);
        } else {
            debugLog("character_name_changes表不存在，使用默认值");
            $renameData = ['rename_count' => 0, 'last_rename_time' => null];
        }
        
        $renameCount = (int)$renameData['rename_count'];
        $lastRenameTime = $renameData['last_rename_time'];
        $isFreeRename = $renameCount === 0;
        $renamePrice = 100;
        
        $responseData = [
            'success' => true,
            'data' => [
                'character_id' => $characterId,
                'current_name' => $currentName,
                'rename_count' => $renameCount,
                'is_free_rename' => $isFreeRename,
                'rename_price' => $renamePrice,
                'current_spirit_stones' => $currentSpiritStone,
                'can_afford' => $isFreeRename || ($currentSpiritStone >= $renamePrice),
                'last_rename_time' => $lastRenameTime
            ]
        ];
        
        debugLog("最终响应数据", $responseData);
        echo json_encode($responseData);
        
    } catch (Exception $e) {
        debugLog("获取更名信息异常", $e->getMessage());
        echo json_encode([
            'success' => false,
            'message' => '获取更名信息失败',
            'debug_error' => $e->getMessage()
        ]);
    }
}

// 角色更名
function renameCharacter($userId, $pdo) {
    try {
        debugLog("开始角色更名", $userId);
        
        // 获取POST数据
        $rawInput = file_get_contents('php://input');
        $input = json_decode($rawInput, true);
        
        if (!$input) {
            $input = $_POST;
        }
        
        debugLog("更名输入数据", $input);
        
        if (!$input || !isset($input['new_name'])) {
            debugLog("缺少新名称参数");
            echo json_encode([
                'success' => false,
                'message' => '缺少新名称参数'
            ]);
            return;
        }
        
        $newName = trim($input['new_name']);
        debugLog("新名称", $newName);
        
        // 验证新名称
        if (empty($newName)) {
            echo json_encode([
                'success' => false,
                'message' => '请输入新的道号'
            ]);
            return;
        }
        
        if (mb_strlen($newName) < 2) {
            echo json_encode([
                'success' => false,
                'message' => '道号至少需要2个字符'
            ]);
            return;
        }
        
        if (mb_strlen($newName) > 8) {
            echo json_encode([
                'success' => false,
                'message' => '道号不能超过8个字符'
            ]);
            return;
        }
        
        if (!preg_match('/^[\x{4e00}-\x{9fa5}a-zA-Z0-9_]+$/u', $newName)) {
            echo json_encode([
                'success' => false,
                'message' => '道号只能包含中文、英文、数字和下划线'
            ]);
            return;
        }
        
        debugLog("新名称验证通过");
        
        // 获取用户角色信息
        $stmt = $pdo->prepare("
            SELECT 
                c.id as character_id,
                c.character_name,
                u.spirit_stones
            FROM characters c 
            JOIN users u ON c.user_id = u.id 
            WHERE c.user_id = ?
            ORDER BY c.created_at DESC 
            LIMIT 1
        ");
        $stmt->execute([$userId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        debugLog("角色信息查询结果", $result);
        
        if (!$result) {
            echo json_encode([
                'success' => false,
                'message' => '未找到角色信息'
            ]);
            return;
        }
        
        $characterId = $result['character_id'];
        $currentName = $result['character_name'];
        $currentSpiritStone = (int)$result['spirit_stones'];
        
        // 检查新名称是否与当前名称相同
        if ($newName === $currentName) {
            echo json_encode([
                'success' => false,
                'message' => '新道号与当前道号相同'
            ]);
            return;
        }
        
        // 检查新名称是否已被使用
        debugLog("检查名称重复");
        $stmt = $pdo->prepare("SELECT id FROM characters WHERE character_name = ? AND id != ?");
        $stmt->execute([$newName, $characterId]);
        
        if ($stmt->fetch()) {
            echo json_encode([
                'success' => false,
                'message' => '此道号已被使用，请选择其他道号'
            ]);
            return;
        }
        
        // 查询更名次数
        debugLog("查询更名次数");
        
        // 检查character_name_changes表的字段结构
        $stmt = $pdo->query("DESCRIBE character_name_changes");
        $nameChangeFields = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $nameChangeFieldNames = array_column($nameChangeFields, 'Field');
        $hasChangeReason = in_array('change_reason', $nameChangeFieldNames);
        
        if ($hasChangeReason) {
            $stmt = $pdo->prepare("
                SELECT COUNT(*) as rename_count
                FROM character_name_changes 
                WHERE character_id = ? AND change_reason = 'user_request'
            ");
        } else {
            $stmt = $pdo->prepare("
                SELECT COUNT(*) as rename_count
                FROM character_name_changes 
                WHERE character_id = ?
            ");
        }
        
        $stmt->execute([$characterId]);
        $renameData = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $renameCount = (int)$renameData['rename_count'];
        $isFreeRename = $renameCount === 0;
        $renamePrice = 100;
        
        debugLog("更名计费信息", [
            'rename_count' => $renameCount,
            'is_free_rename' => $isFreeRename,
            'current_spirit_stones' => $currentSpiritStone,
            'rename_price' => $renamePrice
        ]);
        
        // 检查灵石是否足够
        if (!$isFreeRename && $currentSpiritStone < $renamePrice) {
            echo json_encode([
                'success' => false,
                'message' => '灵石不足，更名需要' . $renamePrice . '灵石'
            ]);
            return;
        }
        
        // 开始事务
        debugLog("开始更名事务");
        $pdo->beginTransaction();
        
        try {
            // 更新角色名称
            debugLog("更新角色名称");
            $stmt = $pdo->prepare("UPDATE characters SET character_name = ?, updated_at = NOW() WHERE id = ?");
            $updateResult = $stmt->execute([$newName, $characterId]);
            debugLog("角色名称更新结果", $updateResult);
            
            // 记录更名历史
            debugLog("记录更名历史");
            
            if ($hasChangeReason) {
                $stmt = $pdo->prepare("
                    INSERT INTO character_name_changes 
                    (character_id, old_name, new_name, change_reason, change_time) 
                    VALUES (?, ?, ?, 'user_request', NOW())
                ");
                $historyResult = $stmt->execute([$characterId, $currentName, $newName]);
            } else {
                $stmt = $pdo->prepare("
                    INSERT INTO character_name_changes 
                    (character_id, old_name, new_name, change_time) 
                    VALUES (?, ?, ?, NOW())
                ");
                $historyResult = $stmt->execute([$characterId, $currentName, $newName]);
            }
            
            debugLog("更名历史记录结果", $historyResult);
            
            // 如果是付费更名，扣除灵石
            if (!$isFreeRename) {
                debugLog("扣除灵石");
                $newSpiritStone = $currentSpiritStone - $renamePrice;
                $stmt = $pdo->prepare("UPDATE users SET spirit_stones = ?, updated_at = NOW() WHERE id = ?");
                $spiritResult = $stmt->execute([$newSpiritStone, $userId]);
                debugLog("灵石扣除结果", ['result' => $spiritResult, 'new_amount' => $newSpiritStone]);
            }
            
            // 提交事务
            debugLog("提交事务");
            $pdo->commit();
            
            // 更新session
            $_SESSION['character_name'] = $newName;
            debugLog("Session更新完成");
            
            $responseData = [
                'success' => true,
                'message' => $isFreeRename ? '首次更名成功（免费）' : '更名成功，消耗' . $renamePrice . '灵石',
                'data' => [
                    'new_name' => $newName,
                    'rename_count' => $renameCount + 1,
                    'cost' => $isFreeRename ? 0 : $renamePrice,
                    'remaining_spirit_stones' => $isFreeRename ? $currentSpiritStone : $currentSpiritStone - $renamePrice
                ]
            ];
            
            debugLog("更名成功", $responseData);
            echo json_encode($responseData);
            
        } catch (Exception $e) {
            debugLog("事务回滚", $e->getMessage());
            $pdo->rollBack();
            throw $e;
        }
        
    } catch (Exception $e) {
        debugLog("更名异常", $e->getMessage());
        echo json_encode([
            'success' => false,
            'message' => '更名失败，请重试',
            'debug_error' => $e->getMessage()
        ]);
    }
}
?> 