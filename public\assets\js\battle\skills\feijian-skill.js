/**
 * 飞剑术技能模块
 * 对应 animation_model = 'feijian'
 */

/**
 * 飞剑术技能 - 单剑版本
 */
class FeiJianSkill extends BaseSkill {
    async execute(skillData, weaponImage) {
        // 使用数据库中的真实技能名称，而不是硬编码
        const skillName = skillData?.skillName || skillData?.displayName || '剑气外放！'; // 提供默认值作为后备
        await this.showSkillShout(skillName);
        // 🔧 修复：根据技能使用者动态确定攻击方向
        await this.createCompleteFlyingSword(skillData, weaponImage);
    }

    async createCompleteFlyingSword(currentSkillData, weaponImage) {
        // 🔧 动态判断位置映射
        let casterPos, targetPos;
        
        if (this.isEnemySkill) {
            // 敌方技能：敌人是施法者，玩家是目标
            casterPos = this.getCharacterPosition(false); // 敌人位置
            targetPos = this.getCharacterPosition(true);  // 玩家位置
        } else {
            // 我方技能：玩家是施法者，敌人是目标
            casterPos = this.getCharacterPosition(true);  // 玩家位置
            targetPos = this.getCharacterPosition(false); // 敌人位置
        }
        
        // 使用新的位置数据
        const startX = casterPos.x;
        const startY = casterPos.y;
        const targetCenterX = targetPos.x;
        const targetCenterY = targetPos.y;

        // 创建单把飞剑
        const sword = document.createElement('div');
        sword.className = 'flying-sword';
        
        // 🔧 武器图片处理 - 完全对标原版
        if (weaponImage) {
            console.log('🗡️ 飞剑术使用武器图片:', weaponImage);
            const weaponImg = document.createElement('img');
            weaponImg.src = weaponImage;
            weaponImg.className = 'weapon-image';
            weaponImg.style.opacity = '1'; // 强制设置透明度
            weaponImg.style.zIndex = '1'; // 确保在前景
            // 🗡️ 动态调整武器图片角度
            weaponImg.style.transform = `rotate(${this.calculateInitialSwordRotation()}deg)`;
            weaponImg.onload = () => {
                console.log('✅ 飞剑武器图片加载成功:', weaponImage);
            };
            weaponImg.onerror = () => {
                console.warn('⚠️ 飞剑武器图片加载失败，使用默认图片:', weaponImage);
                // 图片加载失败时使用默认飞剑图片
                if (window.ImagePathManager) {
                    weaponImg.src = window.ImagePathManager.getWeaponImage('battle_sword.png');
                } else {
                    weaponImg.src = 'assets/images/battle_sword.png';
                }
            };
            sword.appendChild(weaponImg);
        } else {
            console.log('🗡️ 飞剑术使用默认背景图片');
            // 使用默认背景图片（兼容性）
            if (window.ImagePathManager) {
                sword.style.backgroundImage = `url('${window.ImagePathManager.getWeaponImage('battle_sword.png')}')`;
            } else {
                sword.style.backgroundImage = `url('assets/images/battle_sword.png')`;
            }
            sword.style.backgroundSize = 'contain';
            sword.style.backgroundRepeat = 'no-repeat';
            sword.style.backgroundPosition = 'center';
        }
        
        // 设置初始位置
        sword.style.left = `${startX}px`;
        sword.style.top = `${startY}px`;
        
        // 设置CSS变量用于动画
        sword.style.setProperty('--startX', `${startX}px`);
        sword.style.setProperty('--startY', `${startY}px`);
        sword.style.setProperty('--targetCenterX', `${targetCenterX}px`);
        sword.style.setProperty('--targetCenterY', `${targetCenterY}px`);
        
        // 🔧 动态计算穿透方向
        let direction, penetrateDistance;
        
        if (this.isEnemySkill) {
            // 敌方技能：从敌人向玩家穿透（向下）
            direction = 1;
            penetrateDistance = 200;
            sword.classList.add('enemy-sword');
        } else {
            // 我方技能：从玩家向敌人穿透（向上）
            direction = -1;
            penetrateDistance = 200;
        }
        
        const finalY = targetCenterY + (direction * penetrateDistance);
        sword.style.setProperty('--finalY', `${finalY}px`);
        
        // 🔧 调试信息：确保飞剑容器正确创建
        console.log('🗡️ 飞剑容器创建完成:', {
            className: sword.className,
            hasWeaponImage: !!sword.querySelector('.weapon-image'),
            hasBackgroundImage: !!sword.style.backgroundImage,
            initialOpacity: sword.style.opacity,
            position: { left: sword.style.left, top: sword.style.top }
        });
        
        this.effectsContainer.appendChild(sword);
        
        // 🔧 调试信息：飞剑即将开始动画
        console.log('🗡️ 飞剑动画开始，isEnemySkill:', this.isEnemySkill);
        
        // 🔧 动态选择动画
        let spinAnimation, flyAnimation, penetrateAnimation;
        
        if (this.isEnemySkill) {
            // 敌方技能动画
            spinAnimation = 'sword-spin-enemy';
            flyAnimation = 'fly-sword-to-target-enemy';
            penetrateAnimation = 'fly-sword-penetrate-enemy';
        } else {
            // 我方技能动画
            spinAnimation = 'sword-spin-player';
            flyAnimation = 'fly-sword-to-target-player';
            penetrateAnimation = 'fly-sword-penetrate-player';
        }
        
        console.log('🎬 选择的动画序列:', { spinAnimation, flyAnimation, penetrateAnimation });
        
        // 第一阶段：原地旋转蓄力 (缩短到1秒)
        sword.style.animation = `${spinAnimation} 1s ease-out forwards`;
        console.log('🌀 第一阶段旋转动画已设置:', sword.style.animation);
        
        await this.wait(1000);
        
        // 第二阶段：匀速飞向目标 (0.6秒匀速飞行)
        sword.style.animation = `${flyAnimation} 0.6s linear forwards`;
        console.log('🚀 第二阶段飞行动画已设置:', sword.style.animation);
        
        // 🔧 修复：在飞剑击中目标时创建受击特效和动画
        setTimeout(() => {
            console.log('⚔️ 飞剑击中目标，触发受击特效');
            // 🔧 修复：使用正确的击中特效
            this.createHitEffect(targetCenterX, targetCenterY, true);
            
            // 🔧 修复：为被攻击者添加受击动画
            this.addEnemyHitAnimation();
        }, 600); // 0.6秒后触发击中特效
        
        await this.wait(600);
        
        // 第三阶段：快速穿透 (缩短到0.2秒，增强穿透感)
        sword.style.animation = `${penetrateAnimation} 0.2s ease-in forwards`;
        console.log('💨 第三阶段穿透动画已设置:', sword.style.animation);
        
        await this.wait(200);
        
        // 清理飞剑
        console.log('🧹 飞剑动画完成，开始清理');
        sword.remove();
    }
    
    /**
     * 🔧 修复：动态判断受击动画位置
     */
    addEnemyHitAnimation() {
        // 🔧 修复：动态判断被攻击目标
        const targetSelector = !this.isEnemySkill ? '.enemy .character-sprite' : '.player .character-sprite';
        const targetSprite = document.querySelector(targetSelector);
        
        if (targetSprite) {
            // 清除之前的动画
            targetSprite.style.animation = '';
            
            // 强制重绘
            targetSprite.offsetHeight;
            
            // 添加飞剑受击动画 - 使用通用的剑类受击动画
            targetSprite.style.animation = 'sword-hit-shake 0.4s ease-out';
            
            console.log(`⚔️ 飞剑术：为${!this.isEnemySkill ? '敌人' : '玩家'}添加受击动画`);
            
            // 动画结束后清理
            setTimeout(() => {
                if (targetSprite && targetSprite.style.animation.includes('sword-hit-shake')) {
                    targetSprite.style.animation = '';
                }
            }, 400);
        } else {
            console.warn(`⚠️ 飞剑术：未找到目标元素，无法添加受击动画`);
        }
    }

    // 🗡️ 动态计算剑的初始旋转角度
    calculateInitialSwordRotation() {
        // 敌方技能：剑尖向下（指向玩家），我方技能：剑尖向上（指向敌人）
        return this.isEnemySkill ? 180 : 0;
    }
}

// 导出技能类（必须按此格式）
window.FeiJianSkills = { FeiJianSkill };