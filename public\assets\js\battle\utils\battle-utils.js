/**
 * 战斗系统工具类
 * 提供共享的工具方法
 */
class BattleUtils {
    /**
     * 获取品质对应的样式类名
     */
    static getRarityClass(rarity) {
        const rarityMap = {
            '普通': 'normal',
            '稀有': 'rare',
            '史诗': 'epic',
            '传说': 'legendary',
            '神话': 'mythic'
        };
        return rarityMap[rarity] || 'normal';
    }

    /**
     * 获取品质倍率
     */
    static getRarityMultiplier(rarity) {
        const rarityMultipliers = {
            '普通': 1,
            '稀有': 1.5,
            '史诗': 2,
            '传说': 2.5,
            '神话': 3
        };
        return rarityMultipliers[rarity] || 1;
    }

    /**
     * 安全的数值转换
     */
    static safeParseInt(value) {
        const parsed = parseInt(value);
        return isNaN(parsed) ? 0 : parsed;
    }
    
    static safeParseFloat(value) {
        const parsed = parseFloat(value);
        return isNaN(parsed) ? 0 : parsed;
    }

    /**
     * 构建物品详情HTML
     */
    static constructItemDetailHtml(itemData) {
        let detailHtml = '';
        
        // 添加基础属性
        if (itemData.attack) {
            detailHtml += `<div class="stat-line">攻击力: ${itemData.attack}</div>`;
        }
        if (itemData.defense) {
            detailHtml += `<div class="stat-line">防御力: ${itemData.defense}</div>`;
        }
        if (itemData.hp) {
            detailHtml += `<div class="stat-line">生命值: ${itemData.hp}</div>`;
        }
        if (itemData.mp) {
            detailHtml += `<div class="stat-line">法力值: ${itemData.mp}</div>`;
        }
        
        // 添加特殊属性
        if (itemData.specialAttributes) {
            itemData.specialAttributes.forEach(attr => {
                detailHtml += `<div class="stat-line special">${attr}</div>`;
            });
        }
        
        return detailHtml;
    }

    /**
     * 格式化战斗时间
     */
    static formatBattleTime(milliseconds) {
        const seconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes}分${remainingSeconds}秒`;
    }

    /**
     * 生成唯一ID
     */
    static generateUniqueId(prefix = '') {
        return `${prefix}${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
}

// 导出工具类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BattleUtils;
} else {
    window.BattleUtils = BattleUtils;
} 