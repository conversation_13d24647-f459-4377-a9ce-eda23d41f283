// 🎮 战斗UI管理器
// 负责管理战斗界面的所有UI更新和显示操作

class BattleUIManager {
    constructor(battleSystem) {
        this.battleSystem = battleSystem;
        this.dataManager = null; // 将在后续设置

        // 🔧 修复：获取相关的DOM元素
        this.battleContainer = document.querySelector('.battle-container');
        this.effectsContainer = document.querySelector('.effects-container');

        console.log('🎨 BattleUIManager初始化完成');
    }

    setDataManager(dataManager) {
        this.dataManager = dataManager;
        console.log('🔧 BattleUIManager 数据管理器已设置');
    }

    // 🔧 新增：绑定UI事件
    bindUIEvents() {
        console.log('🎯 开始绑定UI事件...');

        // 绑定自动战斗按钮事件
        const autoBattleBtn = document.querySelector('[data-auto-battle-text]');
        if (autoBattleBtn && !autoBattleBtn.hasAttribute('data-event-bound')) {
            autoBattleBtn.addEventListener('click', () => {
                if (this.battleSystem && this.battleSystem.autoBattleManager) {
                    this.battleSystem.autoBattleManager.toggleAutoBattle();
                }
            });
            autoBattleBtn.setAttribute('data-event-bound', 'true');
            console.log('✅ 自动战斗按钮事件已绑定');
        }

        // 其他UI事件可以在这里添加
        console.log('✅ UI事件绑定完成');
    }

    // 更新区域信息显示
    updateAreaInfo() {
        if (!this.dataManager) return;

        const areaName = document.getElementById('area-name');
        const areaProgress = document.getElementById('area-progress');

        // 🏆 检查是否为竞技场模式
        const urlParams = new URLSearchParams(window.location.search);
        const isArena = urlParams.get('arena') === '1';

        if (isArena) {
            // 🏆 竞技场模式显示
            if (areaName) {
                areaName.textContent = '升仙大会';
            }

            if (areaProgress) {
                const opponentName = urlParams.get('opponent_name') || '未知对手';
                const opponentPower = urlParams.get('opponent_power') || '???';
                const isAi = urlParams.get('is_ai') === '1';

                if (isAi) {
                    // 统一AI对手名称显示格式
                    const displayName = opponentName.includes('灵智傀儡')
                        ? opponentName
                        : `灵智傀儡·${opponentName}`;
                    areaProgress.textContent = `VS ${displayName}`;
                } else {
                    areaProgress.textContent = `VS ${opponentName}`;
                }

                // 可以考虑在状态栏显示道行值
                const battleStatus = document.querySelector('.battle-status');
                if (battleStatus) {
                    battleStatus.textContent = `对手道行: ${opponentPower}`;
                }
            }
        } else {
            // 🗺️ 普通地图模式显示
            if (areaName) {
                // 🔧 修复：优先使用当前区域数据，其次是地图数据
                const currentArea = this.dataManager.getCurrentArea();
                const mapName =
                    currentArea?.areaName || this.dataManager.currentMapData?.name || '未知区域';
                areaName.textContent = mapName;
            }

            if (areaProgress) {
                // 🔧 修复：使用正确的数据源获取当前关卡和总关卡
                const currentArea = this.dataManager.getCurrentArea();
                const currentStage = this.dataManager.currentStage || currentArea?.progress || 1;
                const totalStages =
                    currentArea?.total || this.dataManager.currentMapData?.total_stages || 100;
                areaProgress.textContent = `${currentStage}/${totalStages}关`;

                console.log('🔧 更新关卡显示:', {
                    currentStage,
                    totalStages,
                    'dataManager.currentStage': this.dataManager.currentStage,
                    currentArea: currentArea,
                });
            }
        }
    }

    // 🔧 更新武器显示系统
    updateWeaponDisplay() {
        console.log('🔄 开始更新武器显示...');

        if (!this.dataManager || !this.dataManager.weaponSlots) {
            console.warn('⚠️ 数据管理器或武器数据未准备好');
            return;
        }

        const weaponList = document.getElementById('weapon-list');
        if (!weaponList) {
            console.error('❌ 未找到武器列表容器');
            return;
        }

        // 清空现有武器显示
        weaponList.innerHTML = '';

        // 获取战斗技能序列
        const skillSequence = this.dataManager.getBattleSkillSequence();
        console.log('🔧 获取到技能序列:', skillSequence);

        // 显示6个武器槽位
        for (let i = 0; i < 6; i++) {
            const weaponItem = document.createElement('div');
            weaponItem.className = 'weapon-item';
            weaponItem.dataset.slotIndex = i;

            const skillData = skillSequence[i];
            if (skillData && skillData.hasWeapon) {
                // 有武器的槽位
                weaponItem.classList.add('has-weapon');

                // 🔧 获取武器图片
                let weaponImage = 'assets/images/battle_sword.png'; // 默认图片
                if (skillData.weaponImage && window.ImagePathManager) {
                    weaponImage = window.ImagePathManager.getWeaponImage(skillData.weaponImage);
                }

                // 判断是否损坏
                let isBroken =
                    skillData.durability === null ||
                    skillData.maxDurability === null ||
                    skillData.durability === undefined ||
                    skillData.maxDurability === undefined ||
                    skillData.durability <= 0 ||
                    skillData.maxDurability <= 0;

                // 🔧 创建武器显示内容
                weaponItem.innerHTML = `
                    <div class="weapon-icon" style="background-image: url('${weaponImage}')"></div>
                    <div class="weapon-name">${skillData.weaponName || '未知武器'}</div>
                    <div class="weapon-skill">${skillData.skillName || 'feijian'}</div>
                    <div class="weapon-durability">
                        <div class="durability-bar">
                            <div class="durability-fill" style="width: ${
                                (skillData.durability / skillData.maxDurability) * 100
                            }%; background-color: ${this.getDurabilityColor(
                    skillData.durability / skillData.maxDurability
                )}"></div>
                        </div>
                        <div class="durability-text">${
                            isBroken
                                ? "<span style='color:#f44336'>已损坏</span>"
                                : skillData.durability + '/' + skillData.maxDurability
                        }</div>
                    </div>
                `;

                if (isBroken) {
                    weaponItem.classList.add('broken-weapon');
                } else if (skillData.durability / skillData.maxDurability < 0.2) {
                    weaponItem.classList.add('low-durability');
                }

                // 🔧 添加点击事件显示武器详情
                weaponItem.addEventListener('click', () => {
                    this.showWeaponDetail(skillData, i);
                });
            } else {
                // 空槽位
                weaponItem.classList.add('empty');
                weaponItem.innerHTML = '<div class="empty-text">空</div>';
            }

            weaponList.appendChild(weaponItem);
        }

        console.log('✅ 武器显示更新完成');
    }

    // 获取耐久度颜色
    getDurabilityColor(ratio) {
        if (ratio > 0.6) return '#4CAF50'; // 绿色
        if (ratio > 0.3) return '#FF9800'; // 橙色
        return '#F44336'; // 红色
    }

    // 显示默认武器槽位
    showDefaultWeaponSlots(weaponList) {
        weaponList.innerHTML = '';
        for (let i = 0; i < 6; i++) {
            const weaponItem = document.createElement('div');
            weaponItem.className = 'weapon-item empty';
            weaponItem.dataset.slotIndex = i;
            weaponItem.innerHTML = '<div class="empty-text">空</div>';
            weaponList.appendChild(weaponItem);
        }
    }

    // 显示战斗消息 - 🔧 精简版本，只显示重要状态变化
    showBattleMessage(message, type = 'info') {
        // 🔥 过滤掉无关紧要的消息
        const ignoredMessages = [
            '敌方发起攻击',
            '我方发起攻击',
            '玩家发起攻击',
            '怪物发起攻击',
            '双方恢复',
            '准备攻击',
            '战斗开始',
            '技能准备',
            '开始战斗',
            '攻击准备',
        ];

        // 检查是否为需要忽略的消息
        const shouldIgnore = ignoredMessages.some(ignored => message.includes(ignored));
        if (shouldIgnore) {
            return; // 直接跳过这些消息
        }

        // 🔥 重要状态变化才显示，且不自动消失
        if (message.includes('恢复') && (message.includes('HP') || message.includes('MP'))) {
            // 恢复类消息使用战报系统，但不自动消失
            window.battleReportManager?.showReport(message, 'heal', 0); // 0表示不自动消失
            return;
        }

        if (message.includes('消耗') && message.includes('MP')) {
            // MP消耗消息使用战报系统，但不自动消失
            window.battleReportManager?.showReport(message, 'skill', 0); // 0表示不自动消失
            return;
        }

        if (message.includes('防御') || message.includes('减伤')) {
            // 防御类消息，不自动消失
            window.battleReportManager?.showReport(message, 'status', 0);
            return;
        }

        if (message.includes('伤害') || message.includes('暴击') || message.includes('MISS')) {
            // 伤害相关信息使用战报系统，但不自动消失
            window.battleReportManager?.showReport(message, 'damage', 0); // 0表示不自动消失
            return;
        }

        // 其他重要消息继续使用战斗状态栏显示，不自动消失
        const battleStatus = document.querySelector('.battle-status');
        if (battleStatus) {
            battleStatus.textContent = message;

            // 根据消息类型设置样式
            battleStatus.className = 'battle-status';
            if (type === 'error') {
                battleStatus.classList.add('error');
            } else if (type === 'warning') {
                battleStatus.classList.add('warning');
            } else if (type === 'success') {
                battleStatus.classList.add('success');
            }
        }

        console.log(`📢 [${type.toUpperCase()}] ${message}`);
    }

    // 更新战斗状态
    updateBattleStatus(message) {
        this.showBattleMessage(message, 'info');
    }

    // 🔥 新增：更新回合数显示
    updateRoundDisplay(currentRound, maxRounds = 30) {
        const roundDisplay = document.getElementById('round-display');
        if (roundDisplay) {
            // 显示回合信息
            roundDisplay.textContent = `回合 ${currentRound}/${maxRounds}`;
            roundDisplay.style.display = 'block'; // 显示回合元素

            // 如果回合数接近限制，用警告颜色显示
            const isNearLimit = currentRound > maxRounds * 0.8; // 超过80%显示警告
            if (isNearLimit) {
                roundDisplay.style.color = '#ff6b6b';
                roundDisplay.style.borderColor = 'rgba(255, 107, 107, 0.5)';
                roundDisplay.style.animation = 'warningPulse 1.5s ease-in-out infinite';
            } else {
                roundDisplay.style.color = '#ffd700';
                roundDisplay.style.borderColor = 'rgba(255, 215, 0, 0.3)';
                roundDisplay.style.animation = 'none';
            }
        }
    }

    // 更新当前技能显示
    updateCurrentSkill(skillName) {
        const currentSkillElement = document.getElementById('current-skill');
        if (currentSkillElement) {
            currentSkillElement.textContent = `当前技能：${skillName}`;
        }
    }

    // 高亮当前武器
    highlightCurrentWeapon(slotIndex) {
        // 移除所有武器的高亮效果
        const allWeaponItems = document.querySelectorAll('.weapon-item');
        allWeaponItems.forEach(item => {
            item.classList.remove('weapon-active');
        });

        // 高亮当前武器
        const currentWeapon = document.querySelector(`[data-slot-index="${slotIndex}"]`);
        if (currentWeapon) {
            currentWeapon.classList.add('weapon-active');

            // 添加闪光效果
            const flash = document.createElement('div');
            flash.className = 'weapon-flash';
            currentWeapon.appendChild(flash);

            setTimeout(() => {
                if (flash.parentNode) {
                    flash.remove();
                }
            }, 600);
        }
    }

    // 修复图片路径
    fixImagePath(imagePath) {
        if (!imagePath) return 'assets/images/default.png';

        // 如果已经是完整路径，直接返回
        if (imagePath.startsWith('assets/') || imagePath.startsWith('http')) {
            return imagePath;
        }

        // 如果路径以斜杠开头，去掉它
        if (imagePath.startsWith('/')) {
            imagePath = imagePath.substring(1);
        }

        // 添加assets/images/前缀
        if (!imagePath.startsWith('assets/images/')) {
            return `assets/images/${imagePath}`;
        }

        return imagePath;
    }

    // 🔧 新增：显示武器详情的方法
    async showWeaponDetail(skillData, slotIndex) {
        // 🔧 关键修复：在显示详情前强制刷新武器数据，确保获取最新耐久度
        if (this.dataManager && typeof this.dataManager.reloadWeaponData === 'function') {
            console.log('🔄 强制刷新武器数据以获取最新耐久度...');
            try {
                await this.dataManager.reloadWeaponData();
                console.log('✅ 武器数据刷新完成');

                // 🔧 重新获取战斗技能序列，确保使用最新数据
                if (
                    this.battleSystem &&
                    typeof this.battleSystem.getBattleSkillSequence === 'function'
                ) {
                    this.battleSystem.skillSequence = this.battleSystem.getBattleSkillSequence();
                    const refreshedSkillData = this.battleSystem.skillSequence[slotIndex];
                    if (refreshedSkillData) {
                        skillData = refreshedSkillData;
                        console.log('🔄 使用刷新后的技能数据:', skillData);
                    }
                }
            } catch (error) {
                console.warn('⚠️ 刷新武器数据失败:', error);
                // 继续使用缓存数据，不阻止详情显示
            }
        }

        // 检查ItemDetailPopup组件是否可用
        if (typeof ItemDetailPopup === 'undefined') {
            console.error('ItemDetailPopup组件未加载，请确保已引入 js/item-detail-popup.js');

            // 回退到简单弹窗
            const currentDurability = skillData.durability || 100;
            const maxDurability = skillData.maxDurability || 100;
            alert(
                `${skillData.weaponName}\n这把武器蕴含着强大的力量，熟练掌握${skillData.skillName}技能。\n攻击力: ${skillData.weaponAttack}\n耐久度: ${currentDurability}/${maxDurability}`
            );
            return;
        }

        let fullWeaponData = null;
        let weaponDetailData = null;

        // 尝试从BattleDataManager获取完整武器数据
        if (this.dataManager && this.dataManager.weaponSlots) {
            fullWeaponData = this.dataManager.weaponSlots[slotIndex];
            if (fullWeaponData && fullWeaponData.id === skillData.weaponId) {
                console.log('🔧 从数据管理器获取完整武器数据:', fullWeaponData);

                // 使用完整的武器数据构建详情对象
                weaponDetailData = {
                    id: fullWeaponData.id,
                    name: fullWeaponData.name,
                    description:
                        fullWeaponData.description ||
                        `这把武器蕴含着强大的力量，熟练掌握${skillData.skillName}技能。`,
                    rarity: fullWeaponData.rarity || 'common',
                    level_requirement: fullWeaponData.level_requirement || 1,
                    slot_type: fullWeaponData.slot_type || 'sword',
                    item_type: 'weapon',

                    // 完整的属性数据
                    physical_attack: fullWeaponData.physical_attack || 0,
                    immortal_attack: fullWeaponData.immortal_attack || 0,
                    physical_defense: fullWeaponData.physical_defense || 0,
                    immortal_defense: fullWeaponData.immortal_defense || 0,

                    // 🔧 修复：只显示实际存在的属性，不添加默认值
                    hp_bonus: fullWeaponData.hp_bonus || 0,
                    mp_bonus: fullWeaponData.mp_bonus || 0,
                    speed_bonus: fullWeaponData.speed_bonus || 0,
                    critical_bonus: parseFloat(fullWeaponData.critical_bonus) || 0,
                    critical_damage: parseFloat(fullWeaponData.critical_damage) || 0,
                    accuracy_bonus: fullWeaponData.accuracy_bonus || 0, // 🔧 修复：不使用默认值2.0
                    dodge_bonus: fullWeaponData.dodge_bonus || 0,
                    block_bonus: fullWeaponData.block_bonus || 0,

                    // 武器专用属性
                    current_durability:
                        fullWeaponData.current_durability || fullWeaponData.durability || 0,
                    max_durability:
                        fullWeaponData.max_durability || fullWeaponData.maxDurability || 100,
                    skill_name: fullWeaponData.skill_name || skillData.skillName,
                    damage_multiplier: fullWeaponData.damage_multiplier || 1.0,

                    // 技能相关属性
                    element_type: fullWeaponData.element_type || 'neutral',
                    mp_cost: fullWeaponData.mp_cost || 0,
                    cooldown_time: fullWeaponData.cooldown || 0,
                    trigger_chance: fullWeaponData.trigger_chance || 0,
                    effect_duration: fullWeaponData.effect_duration || 0,
                    animation_model: fullWeaponData.animation_model || 'feijian',
                    skill_description: fullWeaponData.skill_description || null,

                    // 图片和其他信息
                    icon_image: this.fixImagePath(fullWeaponData.icon_image) || 'battle_sword.png',
                    image_url:
                        this.fixImagePath(fullWeaponData.icon_image) ||
                        'assets/images/battle_sword.png',
                    model_image:
                        this.fixImagePath(fullWeaponData.model_image) ||
                        this.fixImagePath(fullWeaponData.icon_image) ||
                        'assets/images/battle_sword.png',
                    detail_image:
                        this.fixImagePath(fullWeaponData.detail_image) ||
                        this.fixImagePath(fullWeaponData.icon_image) ||
                        'assets/images/battle_sword.png',
                    sell_price: fullWeaponData.sell_price || Math.floor(skillData.weaponAttack * 2),
                    special_effects: fullWeaponData.special_effects || null,
                    bind_status: 1, // 装备中的武器默认绑定
                    is_equipped: true, // 标记为已装备状态
                };
            }
        }

        // 如果没有获取到完整数据，使用基础数据构建
        if (!weaponDetailData) {
            console.log('🔧 使用基础技能数据构建武器详情');
            weaponDetailData = {
                id: skillData.weaponId || 0,
                name: skillData.weaponName || '未知武器',
                description: `这把武器蕴含着强大的力量，熟练掌握${skillData.skillName}技能。`,
                rarity: 'common',
                level_requirement: 1,
                slot_type: skillData.weaponType || 'sword',
                item_type: 'weapon',

                // 基础属性
                physical_attack: skillData.weaponAttack || 10,
                immortal_attack: 0,
                current_durability: skillData.durability || 0,
                max_durability: skillData.maxDurability || 100,
                skill_name: skillData.skillName || 'feijian',

                // 默认图片
                icon_image:
                    this.fixImagePath(skillData.weaponImage) || 'assets/images/battle_sword.png',
                image_url:
                    this.fixImagePath(skillData.weaponImage) || 'assets/images/battle_sword.png',
                sell_price: Math.floor((skillData.weaponAttack || 10) * 2),
                is_equipped: true,
            };
        }

        console.log('🔧 最终武器详情数据:', weaponDetailData);

        // 显示详情弹窗 - 明确设置为战斗界面
        ItemDetailPopup.show(weaponDetailData, {
            isBattleContext: true, // 明确标识为战斗界面
            showEquip: false, // 战斗界面不允许装备
            showUnequip: false, // 战斗界面不允许卸下
            showUse: false, // 战斗界面不允许使用
            showRepair: false, // 战斗界面不允许修复
            showRecycle: false, // 战斗界面不允许回收
        });
    }
}

// 🔥 新增：动态战报系统
class BattleReportManager {
    constructor() {
        this.messageQueue = [];
        this.currentMessage = null;
        this.isShowing = false;
        this.reportElement = null;
        this.initReportElement();
    }

    initReportElement() {
        this.reportElement = document.querySelector('.battle-status');
        if (this.reportElement) {
            // 🔧 修复：清空默认内容，但不设置位置相关样式，由CSS控制
            this.reportElement.textContent = '';
            // 只重置颜色和内容相关样式，不影响position、transform等定位样式
            this.reportElement.style.background =
                'linear-gradient(135deg, rgba(0,0,0,0.7), rgba(0,0,0,0.5))';
            this.reportElement.style.color = '#fff';
            this.reportElement.style.borderColor = 'rgba(255,255,255,0.2)';
        }
    }

    // 显示战报消息
    showReport(message, type = 'battle', duration = 2000) {
        if (!this.reportElement) return;

        // 根据类型设置颜色
        let color = '#fff';
        let bgGradient = 'linear-gradient(135deg, rgba(0,0,0,0.7), rgba(0,0,0,0.5))';

        switch (type) {
            case 'damage': // 伤害类
                color = '#ff6b6b';
                bgGradient = 'linear-gradient(135deg, rgba(255,107,107,0.2), rgba(220,20,60,0.1))';
                break;
            case 'heal': // 恢复类
                color = '#4ecdc4';
                bgGradient = 'linear-gradient(135deg, rgba(78,205,196,0.2), rgba(26,188,156,0.1))';
                break;
            case 'skill': // 技能类
                color = '#ffd93d';
                bgGradient = 'linear-gradient(135deg, rgba(255,217,61,0.2), rgba(255,165,0,0.1))';
                break;
            case 'status': // 状态类
                color = '#a29bfe';
                bgGradient =
                    'linear-gradient(135deg, rgba(162,155,254,0.2), rgba(116,185,255,0.1))';
                break;
            case 'battle': // 战斗默认
            default:
                color = '#fff';
                break;
        }

        // 立即显示消息
        this.reportElement.style.background = bgGradient;
        this.reportElement.style.color = color;
        this.reportElement.style.borderColor = `${color}33`;
        this.reportElement.textContent = message;

        // 🔧 修复：动画效果保持CSS的translate(-50%, -50%)居中，只添加scale
        this.reportElement.style.transform = 'translate(-50%, -50%) scale(1.05)';
        setTimeout(() => {
            if (this.reportElement) {
                this.reportElement.style.transform = 'translate(-50%, -50%) scale(1)';
            }
        }, 150);

        // 定时清除 - 如果duration为0则不自动清除，等待新消息覆盖
        if (duration > 0) {
            setTimeout(() => {
                this.clearReport();
            }, duration);
        }
        // duration为0时不清除，信息会保持显示直到被新信息覆盖
    }

    // 清除战报
    clearReport() {
        if (this.reportElement) {
            this.reportElement.style.background =
                'linear-gradient(135deg, rgba(0,0,0,0.7), rgba(0,0,0,0.5))';
            this.reportElement.style.color = '#fff';
            this.reportElement.style.borderColor = 'rgba(255,255,255,0.2)';
            this.reportElement.textContent = '';
        }
    }

    // 伤害战报
    showDamageReport(damage, isCritical = false, target = 'enemy') {
        const targetName = target === 'enemy' ? '敌方' : '我方';
        let message;

        if (isCritical) {
            message = `${targetName}暴击-${damage}！`;
        } else {
            message = `${targetName}受击-${damage}！`;
        }

        this.showReport(message, 'damage', 1800);
    }

    // 未命中战报
    showMissReport(target = 'enemy') {
        const targetName = target === 'enemy' ? '敌方' : '我方';
        this.showReport(`${targetName}闪避成功！`, 'status', 1500);
    }

    // 恢复战报
    showHealReport(hpRegen, mpRegen, target = 'player') {
        const targetName = target === 'player' ? '我方' : '敌方';
        let message = '';

        if (hpRegen > 0 && mpRegen > 0) {
            message = `${targetName}恢复${hpRegen}HP ${mpRegen}MP`;
        } else if (hpRegen > 0) {
            message = `${targetName}恢复${hpRegen}HP`;
        } else if (mpRegen > 0) {
            message = `${targetName}恢复${mpRegen}MP`;
        }

        if (message) {
            this.showReport(message, 'heal', 1500);
        }
    }

    // 技能战报
    showSkillReport(skillName, mpCost, caster = 'player') {
        const casterName = caster === 'player' ? '我方' : '敌方';
        let message;

        if (mpCost > 0) {
            message = `${casterName}${skillName}(-${mpCost}MP)`;
        } else {
            message = `${casterName}使用${skillName}`;
        }

        this.showReport(message, 'skill', 0); // 修改为0，不自动消失
    }

    // AI行为战报 - 只显示重要的状态变化
    showAIReport(actionType, description) {
        // 不再显示攻击开始等无关信息，只保留真正重要的状态变化
        if (actionType === 'defend') {
            this.showReport('敌方进入防御状态', 'status', 0); // 0表示不自动消失
        } else if (actionType === 'combo') {
            this.showReport('敌方蓄力连击', 'status', 0);
        }
        // 攻击类型的消息不再显示，因为伤害结果会显示
    }

    // MP消耗战报
    showMPReport(mpCost, caster = 'player') {
        const casterName = caster === 'player' ? '我方' : '敌方';
        this.showReport(`${casterName}消耗${mpCost}MP`, 'skill', 1000);
    }

    // 防御效果战报
    showDefenseReport(damageReduced, target = 'enemy') {
        const targetName = target === 'enemy' ? '敌方' : '我方';
        this.showReport(`${targetName}防御减伤${damageReduced}`, 'status', 1500);
    }
}

// 创建战报管理器实例
if (!window.battleReportManager) {
    window.battleReportManager = new BattleReportManager();
}

// 导出到全局
window.BattleUIManager = BattleUIManager;
