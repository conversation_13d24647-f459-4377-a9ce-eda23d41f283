// 核心战斗系统
// 注意：此文件依赖于其他模块，需要在HTML中正确引入

class BattleSystem {
    constructor() {
        // 🔧 修复：不在构造函数中立即初始化，避免重复加载
        this.isInitialized = false;
        this.isInitializing = false;

        // 初始化基本属性
        this.skillNames = {
            feijian: '剑气外放！',
            wanjianjue: '万剑诀！',
            zhangxinlei: '掌心雷！',
            huoqiushu: '火球术！',
            jujian: '巨剑术！',
        };

        // 🔧 新增：敌人技能名称
        this.enemySkillNames = [
            '妖爪撕裂！',
            '魔焰冲击！',
            '毒牙突刺！',
            '暗影斩击！',
            '雷霆一击！',
            '寒冰刺骨！',
            '烈火焚身！',
            '狂风呼啸！',
        ];

        // 🔧 修复：安全地查找DOM元素，避免null错误
        this.battleContainer = document.querySelector('.battle-container');
        this.effectsContainer = document.querySelector('.effects-container');
        this.attackCount = 0;
        this.isGameOver = false;

        // 🔧 新增：战斗时间追踪
        this.battleStartTime = null;
        this.battleEndTime = null;

        // 🎯 新增：从localStorage恢复挂机状态
        this.isAutoBattleMode = localStorage.getItem('autoBattleMode') === 'true';
        this.autoBattleCountdown = null;

        console.log('🤖 挂机状态恢复:', this.isAutoBattleMode ? '开启' : '关闭');

        // 🔧 修复：只有在battleContainer存在时才创建effectsContainer
        if (!this.effectsContainer && this.battleContainer) {
            this.effectsContainer = document.createElement('div');
            this.effectsContainer.className = 'effects-container';
            this.battleContainer.appendChild(this.effectsContainer);
        }

        console.log('BattleSystem构造完成，等待手动初始化');
        console.log('DOM元素状态:', {
            battleContainer: !!this.battleContainer,
            effectsContainer: !!this.effectsContainer,
        });
    }

    // 🔧 新增：手动初始化方法，避免重复初始化
    async initialize() {
        if (this.isInitialized) {
            console.log('战斗系统已初始化，跳过重复初始化');
            return true;
        }

        if (this.isInitializing) {
            console.log('战斗系统正在初始化中，等待完成...');
            // 🔧 修复：等待初始化完成而不是直接返回false
            return new Promise(resolve => {
                const checkInterval = setInterval(() => {
                    if (!this.isInitializing) {
                        clearInterval(checkInterval);
                        resolve(this.isInitialized);
                    }
                }, 100);
            });
        }

        this.isInitializing = true;

        try {
            console.log('🔄 开始调用 initializeBattle()');
            const success = await this.initializeBattle();
            console.log('🔄 initializeBattle() 返回结果:', success);

            // 🔧 新增：初始化完成后，使用状态机启动战斗
            if (success && this.stateMachine) {
                console.log('🚀 使用状态机启动战斗...');
                await this.stateMachine.initializeBattle();
                console.log('✅ 状态机战斗启动完成');
            }

            this.isInitialized = success;
            return success;
        } catch (error) {
            console.error('❌ initializeBattle() 执行出错:', error);
            throw error;
        } finally {
            this.isInitializing = false;
        }
    }

    async initializeBattle() {
        console.log('🚀 initializeBattle() 方法开始执行');
        // 显示加载状态
        this.updateBattleStatus('正在加载战斗数据...');

        // 🔧 修复：检查是否已有数据管理器实例，避免重复创建
        if (!this.dataManager) {
            this.dataManager = new BattleDataManager();
        }

        // 🔧 新增：创建所有必要的管理器实例
        if (!this.uiManager) {
            this.uiManager = new BattleUIManager(this);
            console.log('✅ UI管理器已创建');
        }

        if (!this.battleFlowManager) {
            this.battleFlowManager = new BattleFlowManager(this);
            console.log('✅ 战斗流程管理器已创建');
        }

        if (!this.rewardManager) {
            this.rewardManager = new BattleRewardManager(this);
            console.log('✅ 奖励管理器已创建');
        }

        if (!this.victoryPanelManager) {
            this.victoryPanelManager = new BattleVictoryPanelManager(this);
            console.log('✅ 胜利面板管理器已创建');
        }

        if (!this.autoBattleManager) {
            this.autoBattleManager = new BattleAutoBattleManager(this);
            console.log('✅ 挂机管理器已创建');
        }

        if (!this.stateMachine) {
            this.stateMachine = new BattleStateMachine(this);
            console.log('✅ 状态机已创建');
        }

        const success = await this.dataManager.initializeBattleData();
        console.log('🔍 数据管理器初始化结果:', success);

        if (!success) {
            console.error('❌ 数据管理器初始化失败，但继续执行战斗初始化');
            this.updateBattleStatus('加载战斗数据失败，使用默认配置');
            // 🔧 修复：即使数据加载失败，也要继续执行后续初始化
            // return false; // 注释掉这行，让初始化继续进行
        }

        // 🔧 新增：设置管理器之间的关联
        if (this.uiManager) {
            this.uiManager.setDataManager(this.dataManager);
        }

        if (this.battleFlowManager) {
            this.battleFlowManager.setDataManager(this.dataManager);
            this.battleFlowManager.setUIManager(this.uiManager);
        }

        if (this.rewardManager) {
            this.rewardManager.setDataManager(this.dataManager);
            this.rewardManager.setUIManager(this.uiManager);
        }

        if (this.victoryPanelManager) {
            this.victoryPanelManager.setDataManager(this.dataManager);
            this.victoryPanelManager.setUIManager(this.uiManager);
            this.victoryPanelManager.setRewardManager(this.rewardManager);
        }

        if (this.autoBattleManager) {
            this.autoBattleManager.setDataManager(this.dataManager);
        }

        console.log('✅ 所有管理器关联设置完成');

        // 更新区域信息显示
        // 🔧 修复：使用UIManager的区域信息更新方法
        if (this.uiManager && this.uiManager.updateAreaInfo) {
            this.uiManager.updateAreaInfo();
            console.log('✅ 使用UIManager更新区域信息');
        } else {
            console.warn('⚠️ UIManager不可用，使用备用区域信息更新方法');
            this.updateAreaInfo();
        }

        // 🔧 新增：设置战斗背景图片
        this.dataManager.setBattleBackground();

        // 获取最终玩家属性
        console.log('🔍 开始计算最终玩家属性...');
        this.playerStats = this.dataManager.calculateFinalStats();
        console.log('🔍 最终玩家属性计算完成:', !!this.playerStats);
        console.log('🔍 套装效果数据:', this.playerStats?.set_special_effects);

        // 🔥 修复：确保playerStats包含完整的MP数据
        const initialMp = this.playerStats.mp || this.playerStats.max_mp || 100;
        this.playerStats.max_mp = initialMp;
        this.playerStats.currentMp = initialMp;
        console.log('🔋 玩家MP初始化:', {
            max_mp: this.playerStats.max_mp,
            currentMp: this.playerStats.currentMp,
            originalMp: this.playerStats.mp,
        });

        // 先获取技能序列（在更新武器显示之前）
        this.skillSequence = this.dataManager.getBattleSkillSequence();

        console.log('=== 完整玩家数据 ===', this.playerStats);

        // 创建角色，正确传递头像数据
        this.player = new Character(document.querySelector('.player'), {
            name: this.playerStats.name || '修仙者',
            level: this.playerStats.level,
            max_hp: this.playerStats.max_hp,
            max_mp: this.playerStats.max_mp,
            currentMp: this.playerStats.currentMp,
            attack: this.playerStats.physical_attack,
            defense: this.playerStats.physical_defense,
            avatar: this.playerStats.character_avatar || this.playerStats.avatar_image,
        });

        // 🔥 修复：确保玩家角色对象的MP值与playerStats同步
        this.player.max_mp = this.playerStats.max_mp;
        this.player.currentMp = this.playerStats.currentMp;
        this.player.updateUI();

        // 🔧 修复：检查敌人数据是否存在
        if (!this.dataManager.enemyData) {
            console.error('❌ 敌人数据为空，无法初始化战斗');
            throw new Error('敌人数据加载失败');
        }

        // 敌人可能有avatar属性，确保传递
        const enemyData = {
            ...this.dataManager.enemyData,
            // 🔧 修复：正确处理敌人头像路径
            avatar:
                this.dataManager.enemyData.avatarImage ||
                this.dataManager.enemyData.modelImage ||
                this.dataManager.enemyData.avatar ||
                this.dataManager.enemyData.monster_avatar,
            // 🆕 新增：确保type属性正确传递
            type: this.dataManager.enemyData.type || 'normal',
        };

        this.enemy = new Character(document.querySelector('.enemy'), enemyData);

        // 现在更新武器显示（skillSequence和skillNames都已经初始化）
        // 🔧 修复：使用UIManager的武器显示方法而不是本地方法
        if (this.uiManager && this.uiManager.updateWeaponDisplay) {
            this.uiManager.updateWeaponDisplay();
            console.log('✅ 使用UIManager更新武器显示');
        } else {
            console.warn('⚠️ UIManager不可用，使用备用武器显示方法');
            this.updateWeaponDisplay();
        }

        console.log('战斗配置:', {
            playerStats: this.playerStats,
            enemyData: this.dataManager.enemyData,
            weaponSlots: this.dataManager.weaponSlots,
            skillSequence: this.skillSequence,
        });

        // 🔧 新增：详细的敌人技能调试信息
        console.log('=== 敌人技能数据调试 ===');
        console.log('敌人原始数据:', this.dataManager.enemyData);
        console.log('敌人技能列表:', this.dataManager.enemyData?.skills);
        console.log('技能列表类型:', typeof this.dataManager.enemyData?.skills);
        console.log('技能数量:', this.dataManager.enemyData?.skills?.length || 0);
        if (
            this.dataManager.enemyData?.skills &&
            Array.isArray(this.dataManager.enemyData.skills)
        ) {
            this.dataManager.enemyData.skills.forEach((skill, index) => {
                console.log(`  技能${index + 1}: "${skill}" (类型: ${typeof skill})`);
            });
        }

        console.log('=== 详细战斗数据 ===');
        console.log('玩家基础攻击力:', this.playerStats.physical_attack);
        console.log(
            '玩家头像:',
            this.playerStats.character_avatar || this.playerStats.avatar_image
        );
        console.log('敌人生命值:', this.enemy.max_hp);
        console.log('敌人防御力:', this.enemy.defense);
        console.log('敌人头像:', enemyData.avatar);
        console.log('武器槽位详情:');
        this.skillSequence.forEach((skill, index) => {
            console.log(`  槽位${index + 1}:`, {
                hasWeapon: skill.hasWeapon,
                weaponName: skill.weaponName,
                weaponAttack: skill.weaponAttack,
                skillName: skill.skillName,
                damageMultiplier: skill.damageMultiplier,
            });
        });

        // 🔧 新增：套装效果数据检查
        console.log('=== 套装效果数据检查 ===');
        console.log(
            'playerStats.set_special_effects存在:',
            !!this.playerStats?.set_special_effects
        );
        console.log('playerStats.set_special_effects:', this.playerStats?.set_special_effects);
        console.log('玩家最大生命值:', this.player?.maxHp);
        console.log('玩家当前生命值:', this.player?.currentHp);

        // 🔧 新增：应用战斗开始时的套装辅助效果
        console.log('🔥 即将调用 applyBattleStartSetEffects()');
        this.applyBattleStartSetEffects();
        console.log('🔥 applyBattleStartSetEffects() 调用完成');

        // 🔧 新增：初始化完成后绑定UI事件
        if (this.uiManager && typeof this.uiManager.bindUIEvents === 'function') {
            this.uiManager.bindUIEvents();
            console.log('✅ UI事件绑定完成');
        }

        console.log('✅ initializeBattle() 方法执行完成');
        return true;
    }

    // 核心战斗方法
    async performSkillDamageCalculation(currentSkillData) {
        // 🔧 修复：移除重复的武器攻击计算，因为playerStats已包含所有装备加成
        // 根据武器类型选择正确的攻击力
        let baseDamage;
        if (currentSkillData.weaponType === 'fan') {
            baseDamage = this.playerStats.immortal_attack || 15;
        } else {
            baseDamage = this.playerStats.physical_attack || 20;
        }

        // 🔧 新增：应用动态套装效果加成
        const dynamicEffects = this.calculateDynamicSetEffects();
        baseDamage = Math.floor(baseDamage * dynamicEffects.attackBonus);

        // 应用技能倍率
        let skillDamage = baseDamage * (currentSkillData.damageMultiplier || 1);

        // 🔧 新增：应用套装攻击特殊效果
        const attackEffectResult = this.applySetAttackEffects(skillDamage);
        skillDamage = attackEffectResult.damage;

        // 计算最终伤害（考虑防御）
        let finalDamage = Math.max(1, Math.floor(skillDamage * (1 - this.enemy.defense / 1000)));

        // 🔧 新增：应用破甲效果
        if (attackEffectResult.ignoreDefense) {
            finalDamage = Math.max(1, Math.floor(skillDamage)); // 无视防御
        }

        // 🔧 新增：应用敌人的防御套装特殊效果
        const enemyDefenseEffectResult = this.applyEnemySetDefenseEffects(finalDamage);
        finalDamage = enemyDefenseEffectResult.damage;

        // 保存敌人防御效果结果，供后续处理反击使用
        this.lastEnemyDefenseEffectResult = enemyDefenseEffectResult;

        console.log('伤害计算:', {
            baseDamage,
            weaponType: currentSkillData.weaponType,
            damageMultiplier: currentSkillData.damageMultiplier,
            enemyDefense: this.enemy.defense,
            finalDamage,
            setEffectsApplied: this.playerStats.set_special_effects?.length || 0,
            attackEffects: attackEffectResult,
        });

        return finalDamage;
    }

    // 🔧 新增：套装攻击特殊效果处理
    applySetAttackEffects(baseDamage) {
        console.log('🔥 套装效果触发检查 - 攻击时');
        console.log('基础伤害:', baseDamage);
        console.log('set_special_effects长度:', this.playerStats?.set_special_effects?.length || 0);

        if (
            !this.playerStats.set_special_effects ||
            this.playerStats.set_special_effects.length === 0
        ) {
            console.log('❌ 没有套装特殊效果数据，跳过攻击效果处理');
            return { damage: baseDamage, ignoreDefense: false, extraAttacks: 0 };
        }

        let modifiedDamage = baseDamage;
        let ignoreDefense = false;
        let extraAttacks = 0;
        const triggeredEffects = [];

        for (const setEffect of this.playerStats.set_special_effects) {
            const effect = setEffect.effect;

            // 🗡️ 攻击类效果处理

            // 暴击伤害 / 必杀
            if (
                effect.includes('200%伤害') ||
                effect.includes('250%伤害') ||
                effect.includes('300%伤害') ||
                effect.includes('350%伤害') ||
                effect.includes('400%伤害')
            ) {
                const chance = this.extractChance(effect) || 15;
                if (Math.random() * 100 < chance) {
                    const multiplierMatch = effect.match(/(\d+)%伤害/);
                    if (multiplierMatch) {
                        const multiplier = parseInt(multiplierMatch[1]) / 100;
                        modifiedDamage *= multiplier;
                        triggeredEffects.push(
                            `${setEffect.set_name}: 暴击伤害${multiplierMatch[1]}%`
                        );
                        this.showSetEffectMessage(`${setEffect.set_name}套装效果：暴击伤害！`);
                    }
                }
            }

            // 连击
            if (effect.includes('连续攻击') || effect.includes('连击')) {
                const chance = this.extractChance(effect) || 10;
                if (Math.random() * 100 < chance) {
                    extraAttacks = 1; // 额外攻击1次
                    triggeredEffects.push(`${setEffect.set_name}: 连击`);
                    this.showSetEffectMessage(`${setEffect.set_name}套装效果：连击！`);
                }
            }

            // 破甲 / 无视防御
            if (
                effect.includes('无视') &&
                (effect.includes('防御') ||
                    effect.includes('仙术防御') ||
                    effect.includes('物理防御'))
            ) {
                const chance = this.extractChance(effect) || 20;
                if (Math.random() * 100 < chance) {
                    ignoreDefense = true;
                    triggeredEffects.push(`${setEffect.set_name}: 破甲`);
                    this.showSetEffectMessage(`${setEffect.set_name}套装效果：破甲！`);
                }
            }

            // 吸血
            if (effect.includes('转化为生命值') || effect.includes('吸血')) {
                const percentMatch = effect.match(/(\d+)%/);
                if (percentMatch) {
                    const healPercent = parseInt(percentMatch[1]) / 100;
                    const healAmount = Math.floor(modifiedDamage * healPercent);
                    this.player.currentHp = Math.min(
                        this.player.maxHp,
                        this.player.currentHp + healAmount
                    );
                    triggeredEffects.push(`${setEffect.set_name}: 吸血${healAmount}`);
                    this.showSetEffectMessage(`${setEffect.set_name}套装效果：吸血${healAmount}！`);
                    this.updatePlayerHpDisplay();
                }
            }

            // 🔧 新增：降低敌人防御力
            if (effect.includes('降低敌人') && effect.includes('防御力')) {
                const chance = this.extractChance(effect) || 15;
                if (Math.random() * 100 < chance) {
                    const percentMatch = effect.match(/降低敌人(\d+)%防御力/);
                    const durationMatch = effect.match(/持续(\d+)回合/);
                    if (percentMatch) {
                        const reduction = parseInt(percentMatch[1]) / 100;
                        const duration = durationMatch ? parseInt(durationMatch[1]) : 2;

                        // 应用防御力降低效果
                        this.enemy.defenseReduction =
                            (this.enemy.defenseReduction || 0) + reduction;
                        this.enemy.defenseReductionTurns = Math.max(
                            this.enemy.defenseReductionTurns || 0,
                            duration
                        );

                        triggeredEffects.push(
                            `${setEffect.set_name}: 降低敌人防御${percentMatch[1]}%`
                        );
                        this.showSetEffectMessage(`${setEffect.set_name}套装效果：破防！`);
                        console.log(`🔥 降低敌人防御: ${percentMatch[1]}% 持续${duration}回合`);
                    }
                }
            }

            // 🔧 新增：降低敌人攻击力
            if (effect.includes('降低敌人') && effect.includes('攻击力')) {
                const chance = this.extractChance(effect) || 20;
                if (Math.random() * 100 < chance) {
                    const percentMatch = effect.match(/降低敌人(\d+)%攻击力/);
                    const durationMatch = effect.match(/持续(\d+)回合/);
                    if (percentMatch) {
                        const reduction = parseInt(percentMatch[1]) / 100;
                        const duration = durationMatch ? parseInt(durationMatch[1]) : 2;

                        // 应用攻击力降低效果
                        this.enemy.attackReduction = (this.enemy.attackReduction || 0) + reduction;
                        this.enemy.attackReductionTurns = Math.max(
                            this.enemy.attackReductionTurns || 0,
                            duration
                        );

                        triggeredEffects.push(
                            `${setEffect.set_name}: 降低敌人攻击${percentMatch[1]}%`
                        );
                        this.showSetEffectMessage(`${setEffect.set_name}套装效果：削弱！`);
                        console.log(`🔥 降低敌人攻击: ${percentMatch[1]}% 持续${duration}回合`);
                    }
                }
            }

            // 🔧 新增：眩晕效果
            if (effect.includes('使敌人眩晕')) {
                const chance = this.extractChance(effect) || 12;
                if (Math.random() * 100 < chance) {
                    const durationMatch = effect.match(/眩晕(\d+)回合/);
                    const duration = durationMatch ? parseInt(durationMatch[1]) : 1;

                    this.enemy.stunTurns = Math.max(this.enemy.stunTurns || 0, duration);
                    triggeredEffects.push(`${setEffect.set_name}: 眩晕敌人${duration}回合`);
                    this.showSetEffectMessage(`${setEffect.set_name}套装效果：眩晕！`);
                    console.log(`🔥 眩晕敌人: ${duration}回合`);
                }
            }

            // 🔧 新增：禁用敌人特殊技能
            if (effect.includes('无法使用特殊技能')) {
                const chance = this.extractChance(effect) || 10;
                if (Math.random() * 100 < chance) {
                    const durationMatch = effect.match(/(\d+)回合/);
                    const duration = durationMatch ? parseInt(durationMatch[1]) : 1;

                    this.enemy.silenceTurns = Math.max(this.enemy.silenceTurns || 0, duration);
                    triggeredEffects.push(`${setEffect.set_name}: 沉默敌人${duration}回合`);
                    this.showSetEffectMessage(`${setEffect.set_name}套装效果：沉默！`);
                    console.log(`🔥 沉默敌人: ${duration}回合`);
                }
            }
        }

        if (triggeredEffects.length > 0) {
            console.log('🔥 套装攻击效果触发:', triggeredEffects);
        }

        return {
            damage: modifiedDamage,
            ignoreDefense: ignoreDefense,
            extraAttacks: extraAttacks,
            triggeredEffects: triggeredEffects,
        };
    }

    // 🔧 新增：套装防御特殊效果处理
    applySetDefenseEffects(incomingDamage) {
        console.log('🔥 套装效果触发检查 - 防御时');
        console.log('受到伤害:', incomingDamage);
        console.log('set_special_effects长度:', this.playerStats?.set_special_effects?.length || 0);

        if (
            !this.playerStats.set_special_effects ||
            this.playerStats.set_special_effects.length === 0
        ) {
            console.log('❌ 没有套装特殊效果数据，跳过防御效果处理');
            return { damage: incomingDamage, counterDamage: 0 };
        }

        let modifiedDamage = incomingDamage;
        let counterDamage = 0;
        const triggeredEffects = [];

        for (const setEffect of this.playerStats.set_special_effects) {
            const effect = setEffect.effect;

            // 🛡️ 防御类效果处理

            // 格挡 / 减少伤害
            if (effect.includes('减少') && effect.includes('伤害')) {
                const chance = this.extractChance(effect) || 25;
                if (Math.random() * 100 < chance) {
                    const percentMatch = effect.match(/减少(\d+)%伤害/);
                    if (percentMatch) {
                        const reduction = parseInt(percentMatch[1]) / 100;
                        modifiedDamage = Math.floor(modifiedDamage * (1 - reduction));
                        triggeredEffects.push(`${setEffect.set_name}: 格挡减伤${percentMatch[1]}%`);
                        this.showSetEffectMessage(`${setEffect.set_name}套装效果：格挡！`);
                    }
                }
            }

            // 反击 - 🔧 修复：更准确的效果匹配
            if (effect.includes('反弹') && effect.includes('伤害')) {
                const chance = this.extractChance(effect) || 20;
                if (Math.random() * 100 < chance) {
                    // 🔧 修复：支持多种反弹伤害格式
                    const percentMatch =
                        effect.match(/反弹(\d+)%伤害/) || effect.match(/反弹(\d+)%/);
                    if (percentMatch) {
                        const reflectPercent = parseInt(percentMatch[1]) / 100;
                        const reflectDamage = Math.floor(incomingDamage * reflectPercent);
                        counterDamage += reflectDamage;
                        triggeredEffects.push(`${setEffect.set_name}: 反弹${reflectDamage}伤害`);
                        this.showSetEffectMessage(
                            `${setEffect.set_name}套装效果：反弹${reflectDamage}伤害！`
                        );
                        console.log(
                            `🔥 反弹效果触发: ${reflectDamage}点伤害 (${percentMatch[1]}%)`
                        );
                    }
                }
            }

            // 坚韧 / 免疫伤害
            if (effect.includes('免疫本次伤害')) {
                const chance = this.extractChance(effect) || 15;
                if (Math.random() * 100 < chance) {
                    modifiedDamage = 0;
                    triggeredEffects.push(`${setEffect.set_name}: 免疫伤害`);
                    this.showSetEffectMessage(`${setEffect.set_name}套装效果：免疫！`);
                }
            }

            // 🔧 新增：生命值低于阈值时减少伤害
            if (effect.includes('生命值低于') && effect.includes('受到伤害减少')) {
                const thresholdMatch = effect.match(/生命值低于(\d+)%时/);
                const reductionMatch = effect.match(/受到伤害减少(\d+)%/);
                if (thresholdMatch && reductionMatch) {
                    const threshold = parseInt(thresholdMatch[1]) / 100;
                    const reduction = parseInt(reductionMatch[1]) / 100;
                    const currentHpPercent = this.player.currentHp / this.player.maxHp;

                    if (currentHpPercent <= threshold) {
                        modifiedDamage = Math.floor(modifiedDamage * (1 - reduction));
                        triggeredEffects.push(
                            `${setEffect.set_name}: 低血量减伤${reductionMatch[1]}%`
                        );
                        this.showSetEffectMessage(`${setEffect.set_name}套装效果：坚韧！`);
                        console.log(
                            `🔥 低血量减伤: 当前血量${Math.floor(currentHpPercent * 100)}% 减伤${
                                reductionMatch[1]
                            }%`
                        );
                    }
                }
            }

            // 🔧 新增：固定伤害反击
            if (effect.includes('受到攻击时对敌人造成固定') && effect.includes('点伤害')) {
                const damageMatch = effect.match(/造成固定(\d+)点伤害/);
                if (damageMatch) {
                    const fixedDamage = parseInt(damageMatch[1]);
                    counterDamage += fixedDamage;
                    triggeredEffects.push(`${setEffect.set_name}: 固定反击${fixedDamage}`);
                    this.showSetEffectMessage(
                        `${setEffect.set_name}套装效果：荆棘${fixedDamage}！`
                    );
                    console.log(`🔥 固定反击伤害: ${fixedDamage}点`);
                }
            }

            // 荆棘 / 固定反伤
            if (effect.includes('固定') && effect.includes('点伤害')) {
                const damageMatch = effect.match(/固定(\d+)点伤害/);
                if (damageMatch) {
                    counterDamage += parseInt(damageMatch[1]);
                    triggeredEffects.push(`${setEffect.set_name}: 荆棘${damageMatch[1]}`);
                    this.showSetEffectMessage(`${setEffect.set_name}套装效果：荆棘！`);
                }
            }

            // 低血量减伤
            if (effect.includes('生命值低于') && effect.includes('受到伤害减少')) {
                const hpThresholdMatch = effect.match(/生命值低于(\d+)%/);
                const reductionMatch = effect.match(/减少(\d+)%/);
                if (hpThresholdMatch && reductionMatch) {
                    const threshold = parseInt(hpThresholdMatch[1]) / 100;
                    const currentHpPercent = this.player.currentHp / this.player.maxHp;
                    if (currentHpPercent < threshold) {
                        const reduction = parseInt(reductionMatch[1]) / 100;
                        modifiedDamage = Math.floor(modifiedDamage * (1 - reduction));
                        triggeredEffects.push(
                            `${setEffect.set_name}: 低血减伤${reductionMatch[1]}%`
                        );
                        this.showSetEffectMessage(`${setEffect.set_name}套装效果：不屈！`);
                    }
                }
            }
        }

        if (triggeredEffects.length > 0) {
            console.log('🛡️ 套装防御效果触发:', triggeredEffects);
        }

        return {
            damage: Math.max(0, modifiedDamage),
            counterDamage: counterDamage,
            triggeredEffects: triggeredEffects,
        };
    }

    // 🔧 新增：提取效果概率的辅助函数
    extractChance(effectText) {
        const chanceMatch = effectText.match(/有(\d+)%概率/);
        return chanceMatch ? parseInt(chanceMatch[1]) : null;
    }

    // 🔧 新增：显示套装特殊效果消息
    showSetEffectMessage(message) {
        // 在战斗日志中显示特殊效果消息
        const logElement = document.querySelector('.battle-log');
        if (logElement) {
            const effectMessage = document.createElement('div');
            effectMessage.className = 'battle-log-entry set-effect';
            effectMessage.style.color = '#ff6b35';
            effectMessage.style.fontWeight = 'bold';
            effectMessage.textContent = `✨ ${message}`;
            logElement.appendChild(effectMessage);
            logElement.scrollTop = logElement.scrollHeight;
        }

        // 在控制台输出
        console.log(`✨ 套装效果: ${message}`);
    }

    // 🔧 新增：更新玩家血量显示
    updatePlayerHpDisplay() {
        const hpElement = document.querySelector('.player-hp');
        if (hpElement) {
            hpElement.textContent = `${this.player.currentHp}/${this.player.maxHp}`;
        }

        const hpBarElement = document.querySelector('.player-hp-bar');
        if (hpBarElement) {
            const hpPercent = (this.player.currentHp / this.player.maxHp) * 100;
            hpBarElement.style.width = `${hpPercent}%`;
        }
    }

    // 🔧 新增：战斗开始时的套装辅助效果处理
    applyBattleStartSetEffects() {
        console.log('🔥 套装效果触发检查 - 战斗开始时');
        console.log('playerStats存在:', !!this.playerStats);
        console.log('set_special_effects存在:', !!this.playerStats?.set_special_effects);
        console.log('set_special_effects长度:', this.playerStats?.set_special_effects?.length || 0);

        // 🔧 新增：详细调试信息
        if (this.playerStats?.set_special_effects) {
            console.log('🔍 套装效果详细信息:');
            this.playerStats.set_special_effects.forEach((effect, index) => {
                console.log(`  ${index + 1}. ${effect.set_name}: "${effect.effect}"`);
            });
        }

        if (
            !this.playerStats.set_special_effects ||
            this.playerStats.set_special_effects.length === 0
        ) {
            console.log('❌ 没有套装特殊效果数据，跳过处理');
            return;
        }

        console.log('🔥 开始处理套装特殊效果:', this.playerStats.set_special_effects);

        for (const setEffect of this.playerStats.set_special_effects) {
            const effect = setEffect.effect;
            console.log(`🔍 处理套装效果: ${setEffect.set_name} - ${effect}`);

            // ✨ 辅助类效果处理

            // 护盾效果
            console.log(`🔍 检查护盾效果: "${effect}"`);
            console.log(`  - 包含"战斗开始时获得": ${effect.includes('战斗开始时获得')}`);
            console.log(`  - 包含"护盾": ${effect.includes('护盾')}`);

            if (effect.includes('战斗开始时获得') && effect.includes('护盾')) {
                console.log('✅ 护盾效果条件匹配，开始处理...');

                // 🔧 修复：支持多种护盾效果文本格式
                let percentMatch =
                    effect.match(/(\d+)%的护盾/) || effect.match(/最大生命值(\d+)%的护盾/);

                console.log('🔍 正则表达式匹配结果:', percentMatch);
                console.log('🔍 玩家最大生命值(max_hp):', this.player?.max_hp);
                console.log('🔍 玩家最大生命值(maxHp):', this.player?.maxHp);

                if (percentMatch) {
                    const shieldPercent = parseInt(percentMatch[1]) / 100;
                    // 🔧 修复：使用正确的属性名 max_hp 而不是 maxHp
                    const playerMaxHp = this.player.max_hp || this.player.maxHp || 0;
                    const shieldAmount = Math.floor(playerMaxHp * shieldPercent);

                    console.log(`🛡️ 护盾计算: ${playerMaxHp} × ${shieldPercent} = ${shieldAmount}`);

                    // 为玩家添加护盾
                    this.player.shield = (this.player.shield || 0) + shieldAmount;
                    this.showSetEffectMessage(
                        `${setEffect.set_name}套装效果：获得${shieldAmount}点护盾！`
                    );
                    console.log(`🛡️ 护盾效果: +${shieldAmount} (总护盾: ${this.player.shield})`);

                    // 🔧 新增：更新UI显示护盾值
                    this.player.updateUI();
                } else {
                    console.log('❌ 护盾效果正则表达式匹配失败');
                }
            } else {
                console.log('❌ 护盾效果条件不匹配');
            }

            // 🔧 新增：狂暴效果 - 低血量时攻击力提升
            if (effect.includes('生命值低于') && effect.includes('攻击力提升')) {
                // 这个效果在战斗中动态检查，这里只是记录
                console.log(`⚡ 狂暴效果已激活: ${effect}`);
            }

            // 🔧 新增：连续未受伤时攻击力提升
            if (
                effect.includes('连续') &&
                effect.includes('回合未受到伤害') &&
                effect.includes('攻击力提升')
            ) {
                // 初始化连续未受伤回合数
                this.player.consecutiveUnhurtTurns = this.player.consecutiveUnhurtTurns || 0;
                console.log(`⚡ 连击强化效果已激活: ${effect}`);
            }
        }
    }

    // 🔧 新增：每回合结束时的套装辅助效果处理
    applyTurnEndSetEffects() {
        if (
            !this.playerStats.set_special_effects ||
            this.playerStats.set_special_effects.length === 0
        ) {
            return;
        }

        for (const setEffect of this.playerStats.set_special_effects) {
            const effect = setEffect.effect;

            // 回复效果
            if (effect.includes('每回合结束时恢复') && effect.includes('生命值')) {
                const percentMatch = effect.match(/(\d+)%/);
                if (percentMatch) {
                    const healPercent = parseInt(percentMatch[1]) / 100;
                    const healAmount = Math.floor(this.player.maxHp * healPercent);
                    this.player.currentHp = Math.min(
                        this.player.maxHp,
                        this.player.currentHp + healAmount
                    );
                    this.showSetEffectMessage(
                        `${setEffect.set_name}套装效果：回复${healAmount}生命值！`
                    );
                    this.updatePlayerHpDisplay();
                    console.log(`💚 回复效果: +${healAmount}HP`);
                }
            }
        }
    }

    // 🔧 新增：计算动态套装效果加成
    calculateDynamicSetEffects() {
        if (
            !this.playerStats.set_special_effects ||
            this.playerStats.set_special_effects.length === 0
        ) {
            return { attackBonus: 1, defenseBonus: 1 };
        }

        let attackMultiplier = 1;
        let defenseMultiplier = 1;

        for (const setEffect of this.playerStats.set_special_effects) {
            const effect = setEffect.effect;

            // 🔥 低血量攻击力提升
            if (effect.includes('生命值低于') && effect.includes('攻击力提升')) {
                const thresholdMatch = effect.match(/生命值低于(\d+)%时/);
                const bonusMatch = effect.match(/攻击力提升(\d+)%/);
                if (thresholdMatch && bonusMatch) {
                    const threshold = parseInt(thresholdMatch[1]) / 100;
                    const bonus = parseInt(bonusMatch[1]) / 100;
                    const currentHpPercent = this.player.currentHp / this.player.maxHp;

                    if (currentHpPercent <= threshold) {
                        attackMultiplier += bonus;
                        console.log(
                            `🔥 狂暴效果触发: 血量${Math.floor(currentHpPercent * 100)}% 攻击力+${
                                bonusMatch[1]
                            }%`
                        );
                    }
                }
            }

            // 🔥 连续未受伤攻击力提升
            if (
                effect.includes('连续') &&
                effect.includes('回合未受到伤害') &&
                effect.includes('攻击力提升')
            ) {
                const turnsMatch = effect.match(/连续(\d+)回合未受到伤害/);
                const bonusMatch = effect.match(/攻击力提升(\d+)%/);
                if (turnsMatch && bonusMatch) {
                    const requiredTurns = parseInt(turnsMatch[1]);
                    const bonus = parseInt(bonusMatch[1]) / 100;
                    const consecutiveTurns = this.player.consecutiveUnhurtTurns || 0;

                    if (consecutiveTurns >= requiredTurns) {
                        attackMultiplier += bonus;
                        console.log(
                            `🔥 连击强化触发: 连续${consecutiveTurns}回合未受伤 攻击力+${bonusMatch[1]}%`
                        );
                    }
                }
            }
        }

        return {
            attackBonus: attackMultiplier,
            defenseBonus: defenseMultiplier,
        };
    }

    // 🔧 新增：复活效果处理
    handleReviveEffects() {
        if (
            !this.playerStats.set_special_effects ||
            this.playerStats.set_special_effects.length === 0 ||
            this.player.hasRevived // 每场战斗只能复活一次
        ) {
            return false;
        }

        for (const setEffect of this.playerStats.set_special_effects) {
            const effect = setEffect.effect;

            if (effect.includes('死亡时有') && effect.includes('概率') && effect.includes('复活')) {
                const chanceMatch = effect.match(/有(\d+)%概率/);
                const hpMatch = effect.match(/恢复(\d+)%生命值/);

                if (chanceMatch && hpMatch) {
                    const chance = parseInt(chanceMatch[1]);
                    const hpPercent = parseInt(hpMatch[1]) / 100;

                    if (Math.random() * 100 < chance) {
                        const reviveHp = Math.floor(this.player.maxHp * hpPercent);
                        this.player.currentHp = reviveHp;
                        this.player.hasRevived = true;

                        this.showSetEffectMessage(
                            `${setEffect.set_name}套装效果：复活！恢复${reviveHp}生命值！`
                        );
                        this.updatePlayerHpDisplay();
                        console.log(`💀➡️💚 复活效果触发: 恢复${reviveHp}HP`);
                        return true;
                    }
                }
            }
        }

        return false;
    }

    async enemyCounter() {
        if (this.isGameOver) return;

        // 随机选择敌人技能名称
        const randomSkillName =
            this.enemySkillNames[Math.floor(Math.random() * this.enemySkillNames.length)];

        // 显示技能喊话
        await this.showSkillShout(randomSkillName, true);

        // 计算敌人基础伤害
        let enemyDamage = Math.max(
            1,
            Math.floor(this.enemy.attack * (1 - this.playerStats.physical_defense / 1000))
        );

        // 🔧 新增：应用敌人的攻击套装特殊效果
        const enemyAttackEffectResult = this.applyEnemySetAttackEffects(enemyDamage);
        enemyDamage = enemyAttackEffectResult.damage;

        // 🔧 新增：应用玩家的防御套装特殊效果
        const defenseEffectResult = this.applySetDefenseEffects(enemyDamage);
        enemyDamage = defenseEffectResult.damage;

        // 应用伤害
        this.player.takeDamage(enemyDamage);

        // 🔧 新增：更新连续未受伤回合数
        if (enemyDamage > 0) {
            this.player.consecutiveUnhurtTurns = 0; // 受到伤害，重置计数
        } else {
            this.player.consecutiveUnhurtTurns = (this.player.consecutiveUnhurtTurns || 0) + 1;
        }

        // 🔧 新增：处理反击效果
        if (defenseEffectResult.counterDamage > 0) {
            this.enemy.takeDamage(defenseEffectResult.counterDamage);
            this.showSetEffectMessage(`反击造成${defenseEffectResult.counterDamage}点伤害！`);
        }

        // 🔧 新增：处理敌人攻击特殊效果（如连击）
        if (enemyAttackEffectResult.extraAttacks > 0) {
            for (let i = 0; i < enemyAttackEffectResult.extraAttacks; i++) {
                const extraDamage = Math.floor(enemyDamage * 0.5); // 连击伤害减半
                this.player.takeDamage(extraDamage);
                this.showSetEffectMessage(`敌人连击造成${extraDamage}点额外伤害！`);
            }
        }

        // 检查玩家是否死亡
        if (this.player.currentHp <= 0) {
            // 🔧 新增：检查复活效果
            if (this.handleReviveEffects()) {
                console.log('💀➡️💚 玩家复活成功！');
                return; // 复活成功，继续战斗
            }

            await this.gameOver('战斗失败！');
            return;
        }

        // 🔧 新增：回合结束时应用套装辅助效果
        this.applyTurnEndSetEffects();
    }

    async gameOver(message) {
        this.isGameOver = true;
        this.battleEndTime = Date.now();

        // 🏆 新增：设置胜负状态标识，便于竞技场结算获取
        this.isPlayerVictory =
            message.includes('玩家胜利') ||
            message.includes('论道胜利') ||
            message.includes('胜利');
        this.battleEnded = true;

        console.log('🔍 战斗结束状态设置:', {
            message: message,
            isPlayerVictory: this.isPlayerVictory,
            battleEnded: this.battleEnded,
        });

        // 停止自动战斗
        if (this.isAutoBattleMode) {
            this.stopAutoBattle();
        }

        // 显示战斗结果
        this.updateBattleStatus(message);

        // 记录战斗时间
        const battleDuration = (this.battleEndTime - this.battleStartTime) / 1000;
        console.log(`战斗结束，持续时间: ${battleDuration}秒`);

        return battleDuration;
    }

    // 战斗状态更新方法
    updateBattleStatus(message) {
        const statusElement = document.querySelector('.battle-status');
        if (statusElement) {
            statusElement.textContent = message;
        }
    }

    // 安全的数值转换方法
    safeParseInt(value) {
        const parsed = parseInt(value);
        return isNaN(parsed) ? 0 : parsed;
    }

    safeParseFloat(value) {
        const parsed = parseFloat(value);
        return isNaN(parsed) ? 0 : parsed;
    }

    // 品质倍率计算
    getRarityMultiplier(rarity) {
        const rarityMultipliers = {
            普通: 1,
            稀有: 1.5,
            史诗: 2,
            传说: 2.5,
            神话: 3,
        };
        return rarityMultipliers[rarity] || 1;
    }

    // 战斗结果处理
    async handleBattleEnd(isVictory, droppedItems = []) {
        // 停止自动战斗
        if (this.isAutoBattleMode) {
            this.stopAutoBattle();
        }

        // 计算战斗时间
        const battleDuration = await this.gameOver(isVictory ? '战斗胜利！' : '战斗失败！');

        // 处理战斗结果
        if (isVictory) {
            // 处理掉落物品
            if (droppedItems && droppedItems.length > 0) {
                await this.showVictoryPanel('战斗胜利！', droppedItems);
            } else {
                await this.showSimpleVictoryPanel('战斗胜利！', []);
            }
        }

        return {
            isVictory,
            battleDuration,
            droppedItems,
        };
    }

    // 自动战斗相关方法
    async startAutoBattle() {
        if (this.isGameOver) return;

        this.isAutoBattleMode = true;
        localStorage.setItem('autoBattleMode', 'true');

        // 开始自动战斗循环
        await this.autoBattle();
    }

    stopAutoBattle() {
        this.isAutoBattleMode = false;
        localStorage.setItem('autoBattleMode', 'false');

        if (this.autoBattleCountdown) {
            clearInterval(this.autoBattleCountdown);
            this.autoBattleCountdown = null;
        }
    }

    async autoBattle() {
        if (!this.isAutoBattleMode || this.isGameOver) return;

        try {
            // 检查是否有可用的技能
            if (!this.skillSequence || this.skillSequence.length === 0) {
                console.error('没有可用的技能');
                return;
            }

            // 随机选择一个技能
            const randomIndex = Math.floor(Math.random() * this.skillSequence.length);
            const currentSkillData = this.skillSequence[randomIndex];

            // 显示技能名称
            if (currentSkillData.skillName) {
                await this.showSkillShout(
                    this.skillNames[currentSkillData.skillName] || currentSkillData.skillName
                );
            }

            // 计算伤害
            const damage = await this.performSkillDamageCalculation(currentSkillData);

            // 应用伤害
            this.enemy.takeDamage(damage);

            // 🔧 新增：处理敌人的反击效果（从performSkillDamageCalculation获取）
            const enemyDefenseEffectResult = this.lastEnemyDefenseEffectResult;
            if (enemyDefenseEffectResult && enemyDefenseEffectResult.counterDamage > 0) {
                this.player.takeDamage(enemyDefenseEffectResult.counterDamage);
                this.showSetEffectMessage(
                    `敌人反击造成${enemyDefenseEffectResult.counterDamage}点伤害！`
                );
                this.updatePlayerHpDisplay();
            }

            // 检查敌人是否死亡
            if (this.enemy.currentHp <= 0) {
                await this.handleBattleEnd(true);
                return;
            }

            // 检查玩家是否因反击死亡
            if (this.player.currentHp <= 0) {
                await this.gameOver('战斗失败！');
                return;
            }

            // 敌人反击
            await this.enemyCounter();

            // 如果战斗未结束且自动战斗模式仍然开启，继续下一轮
            if (!this.isGameOver && this.isAutoBattleMode) {
                setTimeout(() => this.autoBattle(), 2000);
            }
        } catch (error) {
            console.error('自动战斗出错:', error);
            this.stopAutoBattle();
        }
    }

    // 技能显示相关方法
    async showSkillShout(skillName, isEnemy = false) {
        const shoutElement = document.createElement('div');
        shoutElement.className = `skill-shout ${isEnemy ? 'enemy-shout' : 'player-shout'}`;
        shoutElement.textContent = skillName;

        const container = isEnemy
            ? document.querySelector('.enemy-container')
            : document.querySelector('.player-container');

        if (container) {
            container.appendChild(shoutElement);

            // 动画结束后移除元素
            setTimeout(() => {
                if (shoutElement.parentNode) {
                    shoutElement.parentNode.removeChild(shoutElement);
                }
            }, 1000);

            // 等待动画完成
            await new Promise(resolve => setTimeout(resolve, 500));
        }
    }

    updateCurrentSkill(skillName) {
        const skillDisplay = document.querySelector('.current-skill');
        if (skillDisplay) {
            skillDisplay.textContent = `当前技能: ${skillName}`;
        }
    }

    // 区域信息更新
    updateAreaInfo() {
        const areaNameElement = document.querySelector('.area-name');
        const stageLevelElement = document.querySelector('.stage-level');

        if (areaNameElement && this.dataManager.areaData) {
            areaNameElement.textContent = this.dataManager.areaData.areaName || '未知区域';
        }

        if (stageLevelElement && this.dataManager.areaData) {
            stageLevelElement.textContent = `第${this.dataManager.areaData.stageLevel || 1}关`;
        }
    }

    // 武器显示更新
    updateWeaponDisplay() {
        const weaponContainer = document.querySelector('.weapon-slots');
        if (!weaponContainer) return;
        weaponContainer.innerHTML = '';
        this.skillSequence.forEach((skill, index) => {
            const slot = document.createElement('div');
            slot.className = 'weapon-slot' + (skill.hasWeapon ? ' has-weapon' : '');
            if (skill.hasWeapon) {
                const weaponInfo = document.createElement('div');
                weaponInfo.className = 'weapon-info';
                let durabilityText = '';
                if (
                    skill.durability === null ||
                    skill.maxDurability === null ||
                    skill.durability === undefined ||
                    skill.maxDurability === undefined
                ) {
                    durabilityText = '<span style="color:#f44336">异常</span>';
                } else {
                    durabilityText = `${skill.durability}/${skill.maxDurability}`;
                }
                weaponInfo.innerHTML = `
                    <div class="weapon-name">${skill.weaponName || '未知武器'}</div>
                    <div class="weapon-attack">攻击力: ${skill.weaponAttack || 0}</div>
                    <div class="weapon-durability">耐久: ${durabilityText}</div>
                `;
                slot.appendChild(weaponInfo);
                // === 新增：损坏武器样式 ===
                if (
                    skill.durability === null ||
                    skill.maxDurability === null ||
                    skill.durability === undefined ||
                    skill.maxDurability === undefined ||
                    skill.durability <= 0 ||
                    skill.maxDurability <= 0
                ) {
                    slot.classList.add('broken-weapon');
                }
            }
            weaponContainer.appendChild(slot);
        });
    }

    // 🔧 新增：敌人套装攻击特殊效果处理
    applyEnemySetAttackEffects(baseDamage) {
        if (!this.enemy.set_special_effects || this.enemy.set_special_effects.length === 0) {
            return { damage: baseDamage, ignoreDefense: false, extraAttacks: 0 };
        }

        let modifiedDamage = baseDamage;
        let ignoreDefense = false;
        let extraAttacks = 0;
        const triggeredEffects = [];

        for (const setEffect of this.enemy.set_special_effects) {
            const effect = setEffect.effect;

            // 🗡️ 攻击类效果处理

            // 暴击伤害 / 必杀
            if (effect.includes('暴击伤害') || effect.includes('必杀')) {
                const percentMatch = effect.match(/(\d+)%/);
                if (percentMatch && Math.random() < 0.15) {
                    // 15%触发概率
                    const multiplier = parseInt(percentMatch[1]) / 100;
                    modifiedDamage = Math.floor(modifiedDamage * (1 + multiplier));
                    triggeredEffects.push(`敌人${setEffect.set_name}暴击！`);
                }
            }

            // 连击
            if (effect.includes('连击') && Math.random() < 0.12) {
                // 12%触发概率
                extraAttacks = 1;
                triggeredEffects.push(`敌人${setEffect.set_name}连击！`);
            }

            // 破甲 / 无视防御
            if ((effect.includes('破甲') || effect.includes('无视防御')) && Math.random() < 0.1) {
                // 10%触发概率
                ignoreDefense = true;
                triggeredEffects.push(`敌人${setEffect.set_name}破甲！`);
            }

            // 吸血
            if (effect.includes('吸血') && Math.random() < 0.15) {
                // 15%触发概率
                const percentMatch = effect.match(/(\d+)%/);
                if (percentMatch) {
                    const healPercent = parseInt(percentMatch[1]) / 100;
                    const healAmount = Math.floor(modifiedDamage * healPercent);
                    this.enemy.currentHp = Math.min(
                        this.enemy.max_hp,
                        this.enemy.currentHp + healAmount
                    );
                    triggeredEffects.push(
                        `敌人${setEffect.set_name}吸血回复${healAmount}点生命值！`
                    );
                }
            }
        }

        // 显示触发的效果
        triggeredEffects.forEach(effect => this.showSetEffectMessage(effect));

        // 如果破甲，重新计算伤害（无视防御）
        if (ignoreDefense) {
            modifiedDamage = Math.max(1, Math.floor(this.enemy.attack));
        }

        console.log('🔥 敌人套装攻击效果:', {
            baseDamage,
            modifiedDamage,
            ignoreDefense,
            extraAttacks,
            setEffectsApplied: this.enemy.set_special_effects?.length || 0,
            triggeredEffects,
        });

        return { damage: modifiedDamage, ignoreDefense, extraAttacks };
    }

    // 🔧 新增：敌人套装防御特殊效果处理
    applyEnemySetDefenseEffects(incomingDamage) {
        if (!this.enemy.set_special_effects || this.enemy.set_special_effects.length === 0) {
            return { damage: incomingDamage, counterDamage: 0 };
        }

        let modifiedDamage = incomingDamage;
        let counterDamage = 0;
        const triggeredEffects = [];

        for (const setEffect of this.enemy.set_special_effects) {
            const effect = setEffect.effect;

            // 🛡️ 防御类效果处理

            // 格挡 / 减少伤害
            if (effect.includes('格挡') || (effect.includes('减少') && effect.includes('伤害'))) {
                const percentMatch = effect.match(/(\d+)%/);
                if (percentMatch && Math.random() < 0.2) {
                    // 20%触发概率
                    const reduction = parseInt(percentMatch[1]) / 100;
                    modifiedDamage = Math.floor(modifiedDamage * (1 - reduction));
                    triggeredEffects.push(`敌人${setEffect.set_name}格挡！`);
                }
            }

            // 反击
            if (effect.includes('反击') && Math.random() < 0.15) {
                // 15%触发概率
                const percentMatch = effect.match(/(\d+)%/);
                if (percentMatch) {
                    const counterPercent = parseInt(percentMatch[1]) / 100;
                    counterDamage = Math.floor(incomingDamage * counterPercent);
                } else {
                    counterDamage = Math.floor(this.enemy.attack * 0.5); // 默认50%攻击力反击
                }
                triggeredEffects.push(`敌人${setEffect.set_name}反击！`);
            }

            // 免疫 / 无效化
            if (effect.includes('免疫') && Math.random() < 0.08) {
                // 8%触发概率
                modifiedDamage = 0;
                triggeredEffects.push(`敌人${setEffect.set_name}免疫伤害！`);
            }

            // 荆棘 / 伤害反弹
            if (effect.includes('荆棘') || effect.includes('反弹')) {
                const percentMatch = effect.match(/(\d+)%/);
                if (percentMatch && Math.random() < 0.25) {
                    // 25%触发概率
                    const reflectPercent = parseInt(percentMatch[1]) / 100;
                    const reflectDamage = Math.floor(incomingDamage * reflectPercent);
                    counterDamage += reflectDamage;
                    triggeredEffects.push(
                        `敌人${setEffect.set_name}荆棘反弹${reflectDamage}点伤害！`
                    );
                }
            }
        }

        // 显示触发的效果
        triggeredEffects.forEach(effect => this.showSetEffectMessage(effect));

        console.log('🛡️ 敌人套装防御效果:', {
            incomingDamage,
            modifiedDamage,
            counterDamage,
            setEffectsApplied: this.enemy.set_special_effects?.length || 0,
            triggeredEffects,
        });

        return { damage: modifiedDamage, counterDamage };
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BattleSystem;
}
