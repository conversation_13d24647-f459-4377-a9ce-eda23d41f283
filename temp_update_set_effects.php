<?php
/**
 * 更新套装特殊效果到数据库
 * 将不符合回合制的效果替换为新设计的效果
 */

try {
    $pdo = new PDO('mysql:host=127.0.0.1;port=3306;dbname=yn_game;charset=utf8mb4', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== 开始更新套装特殊效果 ===\n\n";
    
    // 新的套装效果配置
    $newEffectsConfig = [
        '金衣上人' => [
            'four_piece_effect' => '受到攻击时有25%概率减少50%伤害',
            'six_piece_effect' => '生命值低于20%时受到伤害减少40%'
        ],
        '白袍剑君' => [
            'four_piece_effect' => '攻击时有20%概率无视敌人50%防御',
            'six_piece_effect' => '攻击时有15%概率造成200%伤害'
        ],
        '月华法尊' => [
            'four_piece_effect' => '攻击时有15%概率降低敌人30%防御力持续2回合',
            'six_piece_effect' => '攻击造成伤害的30%转化为生命值'
        ],
        '紫霞仙子' => [
            'four_piece_effect' => '每回合结束时恢复最大生命值的5%',
            'six_piece_effect' => '战斗开始时获得最大生命值20%的护盾'
        ],
        '青龙战甲' => [
            'four_piece_effect' => '受到攻击时有20%概率反弹30%伤害',
            'six_piece_effect' => '攻击时有10%概率连续攻击2次'
        ],
        '凤凰羽衣' => [
            'four_piece_effect' => '攻击时有10%概率使敌人眩晕1回合',
            'six_piece_effect' => '死亡时有20%概率恢复50%生命值复活(每场战斗限1次)'
        ]
    ];
    
    // 获取所有套装
    $stmt = $pdo->query("SELECT id, set_name, effects FROM game_item_sets");
    $sets = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $updateCount = 0;
    
    foreach ($sets as $set) {
        $setName = $set['set_name'];
        $currentEffects = json_decode($set['effects'], true);
        
        if (!$currentEffects) {
            echo "⚠️ 跳过 {$setName}: 无效的effects JSON\n";
            continue;
        }
        
        $updated = false;
        
        // 检查是否需要更新特殊效果
        if (isset($newEffectsConfig[$setName])) {
            $newConfig = $newEffectsConfig[$setName];
            
            // 更新4件套特殊效果
            if (isset($currentEffects['four_piece'])) {
                $currentEffects['four_piece']['special_effect'] = $newConfig['four_piece_effect'];
                $updated = true;
                echo "✅ 更新 {$setName} 4件套: {$newConfig['four_piece_effect']}\n";
            }
            
            // 更新6件套特殊效果
            if (isset($currentEffects['six_piece'])) {
                $currentEffects['six_piece']['special_effect'] = $newConfig['six_piece_effect'];
                $updated = true;
                echo "✅ 更新 {$setName} 6件套: {$newConfig['six_piece_effect']}\n";
            }
            
            // 如果有更新，保存到数据库
            if ($updated) {
                $newEffectsJson = json_encode($currentEffects, JSON_UNESCAPED_UNICODE);
                $updateStmt = $pdo->prepare("UPDATE game_item_sets SET effects = ? WHERE id = ?");
                $updateStmt->execute([$newEffectsJson, $set['id']]);
                $updateCount++;
                echo "💾 已保存 {$setName} 的新效果到数据库\n\n";
            }
        } else {
            echo "⚠️ 未找到 {$setName} 的新效果配置\n";
        }
    }
    
    echo "=== 更新完成 ===\n";
    echo "总共更新了 {$updateCount} 个套装的特殊效果\n\n";
    
    // 验证更新结果
    echo "=== 验证更新结果 ===\n";
    $verifyStmt = $pdo->query("SELECT set_name, effects FROM game_item_sets WHERE set_name IN ('" . implode("','", array_keys($newEffectsConfig)) . "')");
    $verifyResults = $verifyStmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($verifyResults as $result) {
        $effects = json_decode($result['effects'], true);
        echo "【{$result['set_name']}】验证:\n";
        
        if (isset($effects['four_piece']['special_effect'])) {
            echo "  4件套: {$effects['four_piece']['special_effect']}\n";
        }
        if (isset($effects['six_piece']['special_effect'])) {
            echo "  6件套: {$effects['six_piece']['special_effect']}\n";
        }
        echo "\n";
    }
    
} catch (Exception $e) {
    echo '错误: ' . $e->getMessage() . "\n";
}
?>
