<?php
$pdo = new PDO('mysql:host=localhost;dbname=yn_game', 'ynxx', 'mjlxz159');
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

echo "=== 数据库表结构检查 ===\n";

// 检查所有表
$tables = $pdo->query('SHOW TABLES')->fetchAll(PDO::FETCH_COLUMN);
echo "数据库中的表: " . implode(', ', $tables) . "\n\n";

// 检查users表结构
echo "=== users表结构 ===\n";
$columns = $pdo->query('DESCRIBE users')->fetchAll();
foreach ($columns as $col) {
    echo $col['Field'] . " - " . $col['Type'] . "\n";
}

echo "\n=== character_equipment表结构 ===\n";
if (in_array('character_equipment', $tables)) {
    $columns = $pdo->query('DESCRIBE character_equipment')->fetchAll();
    foreach ($columns as $col) {
        echo $col['Field'] . " - " . $col['Type'] . "\n";
    }
} else {
    echo "表不存在\n";
}

echo "\n=== game_items表结构 ===\n";
if (in_array('game_items', $tables)) {
    $columns = $pdo->query('DESCRIBE game_items')->fetchAll();
    foreach ($columns as $col) {
        echo $col['Field'] . " - " . $col['Type'] . "\n";
    }
} else {
    echo "表不存在\n";
}

echo "\n=== characters表结构 ===\n";
if (in_array('characters', $tables)) {
    $columns = $pdo->query('DESCRIBE characters')->fetchAll();
    foreach ($columns as $col) {
        echo $col['Field'] . " - " . $col['Type'] . "\n";
    }

    echo "\n=== 检查用户1的角色数据 ===\n";
    $stmt = $pdo->prepare('SELECT * FROM characters WHERE user_id = ?');
    $stmt->execute([1]);
    $character = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($character) {
        echo "角色存在，等级: " . $character['level'] . "\n";
        echo "生命值: " . $character['max_hp'] . "\n";
        echo "物理攻击: " . $character['physical_attack'] . "\n";
    } else {
        echo "角色不存在\n";
    }
} else {
    echo "表不存在\n";
}
