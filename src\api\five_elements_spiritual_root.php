<?php
/**
 * 五行灵根系统 - 核心类
 * 处理角色五行灵根生成、品质计算、属性加成等功能
 * 作者：一念修仙装备系统开发团队
 * 日期：2024年12月19日
 */

class FiveElementsSpiritualRootSystem {
    
    /**
     * 灵根品质配置
     */
    private static $ROOT_QUALITY_CONFIG = [
        '废灵根' => [
            'name' => '废灵根',
            'en_name' => 'defective',
            'color' => '#6c757d',
            'multiplier' => 0.5,
            'probability' => 40,
            'description' => '最低品质的灵根，修炼困难'
        ],
        '下品灵根' => [
            'name' => '下品灵根',
            'en_name' => 'inferior',
            'color' => '#95a5a6',
            'multiplier' => 1.0,
            'probability' => 30,
            'description' => '普通品质的灵根，修炼速度一般'
        ],
        '中品灵根' => [
            'name' => '中品灵根', 
            'en_name' => 'medium',
            'color' => '#27ae60',
            'multiplier' => 1.5,
            'probability' => 20,
            'description' => '中等品质的灵根，修炼天赋较好'
        ],
        '上品灵根' => [
            'name' => '上品灵根',
            'en_name' => 'superior',
            'color' => '#3498db',
            'multiplier' => 2.0,
            'probability' => 8,
            'description' => '优秀品质的灵根，修炼天赋出众'
        ],
        '极品灵根' => [
            'name' => '极品灵根',
            'en_name' => 'perfect',
            'color' => '#9b59b6',
            'multiplier' => 3.0,
            'probability' => 2,
            'description' => '极品品质的灵根，万中无一的修炼奇才'
        ]
    ];
    
    /**
     * 五行属性配置
     */
    private static $FIVE_ELEMENTS_CONFIG = [
        'metal' => [
            'name' => '金',
            'en_name' => 'metal',
            'color' => '#FFD700',
            'affect_attribute' => 'physique',
            'affect_attribute_name' => '筋骨',
            'description' => '金性灵根，影响筋骨属性，增强物理攻击和暴击',
            'element_icon' => '⚔️'
        ],
        'wood' => [
            'name' => '木',
            'en_name' => 'wood',
            'color' => '#228B22',
            'affect_attribute' => 'spirit',
            'affect_attribute_name' => '神魂',
            'description' => '木性灵根，影响神魂属性，增强仙法攻击和闪避',
            'element_icon' => '🌳'
        ],
        'water' => [
            'name' => '水',
            'en_name' => 'water',
            'color' => '#1E90FF',
            'affect_attribute' => 'comprehension',
            'affect_attribute_name' => '悟性',
            'description' => '水性灵根，影响悟性属性，增强法力和仙法防御',
            'element_icon' => '💧'
        ],
        'fire' => [
            'name' => '火',
            'en_name' => 'fire',
            'color' => '#DC143C',
            'affect_attribute' => 'agility',
            'affect_attribute_name' => '身法',
            'description' => '火性灵根，影响身法属性，增强速度和物理防御',
            'element_icon' => '🔥'
        ],
        'earth' => [
            'name' => '土',
            'en_name' => 'earth',
            'color' => '#8B4513',
            'affect_attribute' => 'constitution',
            'affect_attribute_name' => '体魄',
            'description' => '土性灵根，影响体魄属性，增强生命和抗暴击',
            'element_icon' => '🏔️'
        ]
    ];
    
    /**
     * 随机生成角色的五行灵根
     * @param string $generateType 生成类型 ('random', 'balanced', 'focused')
     * @return array 灵根数据
     */
    public static function generateSpiritualRoots($generateType = 'random') {
        $roots = [];
        
        // 🔧 修复：使用固定总数避免刷号问题
        $FIXED_TOTAL = 100; // 固定总数100点，确保公平性
        
        switch ($generateType) {
            case 'balanced':
                // 平衡型：平均分配，略有随机
                $baseValue = intval($FIXED_TOTAL / 5); // 每个20点
                $remainingPoints = $FIXED_TOTAL - ($baseValue * 5);
                
                foreach (self::$FIVE_ELEMENTS_CONFIG as $element => $config) {
                    $value = $baseValue;
                    // 随机分配剩余点数
                    if ($remainingPoints > 0) {
                        $bonus = mt_rand(0, min(5, $remainingPoints));
                        $value += $bonus;
                        $remainingPoints -= $bonus;
                    }
                    // 增加小幅随机变化 ±3
                    $value += mt_rand(-3, 3);
                    $value = max(8, min(35, $value)); // 限制范围8-35
                    
                    $quality = self::determineRootQuality($value);
                    $roots[$element] = [
                        'value' => $value,
                        'quality' => $quality,
                        'multiplier' => self::$ROOT_QUALITY_CONFIG[$quality]['multiplier']
                    ];
                }
                break;
                
            case 'focused':
                // 专精型：1-2个灵根较高，其他较低，但总数固定
                $elements = array_keys(self::$FIVE_ELEMENTS_CONFIG);
                $focusedCount = mt_rand(1, 2);
                $focusedElements = array_rand($elements, $focusedCount);
                
                if (!is_array($focusedElements)) {
                    $focusedElements = [$focusedElements];
                }
                
                // 🔧 修复：使用固定分配确保总数准确
                $tempRoots = [];
                $points = $FIXED_TOTAL;
                $minPerElement = 8;
                $maxPerElement = 35;
                
                // 先给每个元素分配最小值
                foreach ($elements as $element) {
                    $tempRoots[$element] = $minPerElement;
                    $points -= $minPerElement;
                }
                
                // 专精元素额外分配点数
                $focusedBonus = intval($points * 0.7); // 70%给专精元素
                $normalBonus = $points - $focusedBonus;
                
                // 分配专精元素额外点数
                foreach ($focusedElements as $index) {
                    $element = $elements[$index];
                    $bonus = intval($focusedBonus / count($focusedElements));
                    $tempRoots[$element] += $bonus;
                    if ($tempRoots[$element] > $maxPerElement) {
                        $excess = $tempRoots[$element] - $maxPerElement;
                        $tempRoots[$element] = $maxPerElement;
                        $normalBonus += $excess;
                    }
                }
                
                // 分配剩余点数给非专精元素
                $normalElements = [];
                foreach ($elements as $index => $element) {
                    if (!in_array($index, $focusedElements)) {
                        $normalElements[] = $element;
                    }
                }
                
                if (!empty($normalElements) && $normalBonus > 0) {
                    $bonusPerNormal = intval($normalBonus / count($normalElements));
                    $remainder = $normalBonus % count($normalElements);
                    
                    foreach ($normalElements as $i => $element) {
                        $tempRoots[$element] += $bonusPerNormal;
                        if ($i < $remainder) {
                            $tempRoots[$element]++;
                    }
                    }
                }
                
                // 构建最终结果
                foreach ($tempRoots as $element => $value) {
                    $quality = self::determineRootQuality($value);
                    $roots[$element] = [
                        'value' => $value,
                        'quality' => $quality,
                        'multiplier' => self::$ROOT_QUALITY_CONFIG[$quality]['multiplier']
                    ];
                }
                break;
                
            default: // 'random' - 改为固定总数的随机分配
                // 🔧 新算法：固定总数随机分配
                $points = $FIXED_TOTAL;
                $minPerElement = 8; // 每个灵根最少8点
                $maxPerElement = 35; // 每个灵根最多35点
                
                // 先给每个元素分配最小值
                $tempRoots = [];
                foreach (self::$FIVE_ELEMENTS_CONFIG as $element => $config) {
                    $tempRoots[$element] = $minPerElement;
                    $points -= $minPerElement;
                }
                
                // 随机分配剩余点数
                while ($points > 0 && $points > 0) {
                    $elements = array_keys($tempRoots);
                    $randomElement = $elements[mt_rand(0, count($elements) - 1)];
                    
                    if ($tempRoots[$randomElement] < $maxPerElement) {
                        $addPoints = mt_rand(1, min(3, $points, $maxPerElement - $tempRoots[$randomElement]));
                        $tempRoots[$randomElement] += $addPoints;
                        $points -= $addPoints;
                    }
                    
                    // 防止无限循环
                    $allMaxed = true;
                    foreach ($tempRoots as $val) {
                        if ($val < $maxPerElement) {
                            $allMaxed = false;
                            break;
                        }
                    }
                    if ($allMaxed) break;
                }
                
                // 构建最终结果
                foreach ($tempRoots as $element => $value) {
                    $quality = self::determineRootQuality($value);
                    $roots[$element] = [
                        'value' => $value,
                        'quality' => $quality,
                        'multiplier' => self::$ROOT_QUALITY_CONFIG[$quality]['multiplier']
                    ];
                }
        }
        
        // 🔧 确保总数准确（微调）
        $currentTotal = array_sum(array_column($roots, 'value'));
        if ($currentTotal != $FIXED_TOTAL) {
            $diff = $FIXED_TOTAL - $currentTotal;
            $elements = array_keys($roots);
            
            while ($diff != 0) {
                $randomElement = $elements[mt_rand(0, count($elements) - 1)];
                
                if ($diff > 0 && $roots[$randomElement]['value'] < 35) {
                    $roots[$randomElement]['value']++;
                    $diff--;
                } elseif ($diff < 0 && $roots[$randomElement]['value'] > 8) {
                    $roots[$randomElement]['value']--;
                    $diff++;
                } else {
                    // 防止死循环
                    break;
                }
            }
            
            // 重新计算品质
            foreach ($roots as $element => &$rootData) {
                $rootData['quality'] = self::determineRootQuality($rootData['value']);
                $rootData['multiplier'] = self::$ROOT_QUALITY_CONFIG[$rootData['quality']]['multiplier'];
                }
        }
        
        return $roots;
    }
    
    /**
     * 根据灵根数值确定品质
     * @param int $value 灵根数值
     * @return string 品质名称
     */
    private static function determineRootQuality($value) {
        if ($value >= 45) {
            return '极品灵根';
        } elseif ($value >= 35) {
            return '上品灵根';
        } elseif ($value >= 25) {
            return '中品灵根';
        } elseif ($value >= 15) {
            return '下品灵根';
        } else {
            return '废灵根';
        }
    }
    
    /**
     * 计算灵根对基础属性的加成
     * @param array $roots 灵根数据
     * @return array 属性加成数据
     */
    public static function calculateRootAttributeBonus($roots) {
        $attributeBonus = [];
        
        foreach ($roots as $element => $rootData) {
            if (!isset(self::$FIVE_ELEMENTS_CONFIG[$element])) {
                continue;
            }
            
            $elementConfig = self::$FIVE_ELEMENTS_CONFIG[$element];
            $affectedAttribute = $elementConfig['affect_attribute'];
            $rootValue = $rootData['value'];
            $multiplier = $rootData['multiplier'];
            
            // 计算属性加成：灵根值 × 品质倍率 × 基础系数
            $bonus = intval($rootValue * $multiplier * 0.1); // 基础系数0.1
            
            if (!isset($attributeBonus[$affectedAttribute])) {
                $attributeBonus[$affectedAttribute] = 0;
            }
            
            $attributeBonus[$affectedAttribute] += $bonus;
        }
        
        return $attributeBonus;
    }
    
    /**
     * 计算五行属性伤害加成（兼容性函数，不建议使用）
     * 注意：实际战斗系统使用更简单的计算：灵根值 × 1.2
     * @param array $roots 灵根数据
     * @param string $weaponElement 武器五行属性（可选）
     * @return array 五行伤害加成
     * @deprecated 此函数未被实际使用，战斗系统使用 calculateElementalDamage 函数
     */
    public static function calculateElementalDamageBonus($roots, $weaponElement = null) {
        $elementalDamage = [];
        
        foreach ($roots as $element => $rootData) {
            if (!isset(self::$FIVE_ELEMENTS_CONFIG[$element])) {
                continue;
            }
            
            $rootValue = $rootData['value'];
            
            // 🔧 修改为与战斗系统一致的计算方式：直接使用灵根值 × 1.2
            $damage = intval($rootValue * 1.2);
            
            // 如果武器属性匹配，额外加成（这个逻辑在实际战斗中未使用）
            if ($weaponElement === $element) {
                $damage = intval($damage * 1.2); // 20%匹配加成
            }
            
            $elementalDamage[$element] = $damage;
        }
        
        return $elementalDamage;
    }
    
    /**
     * 获取灵根总体评价
     * @param array $roots 灵根数据
     * @return array 总体评价信息
     */
    public static function getRootOverallEvaluation($roots) {
        $totalValue = 0;
        $qualityScores = [];
        $dominantElement = null;
        $maxValue = 0;
        
        foreach ($roots as $element => $rootData) {
            $totalValue += $rootData['value'];
            $qualityScores[] = self::getQualityScore($rootData['quality']);
            
            if ($rootData['value'] > $maxValue) {
                $maxValue = $rootData['value'];
                $dominantElement = $element;
            }
        }
        
        $averageValue = $totalValue / 5;
        $averageQualityScore = array_sum($qualityScores) / 5;
        
        // 确定总体等级
        if ($averageQualityScore >= 4.5) {
            $overallLevel = '仙灵之体';
            $levelColor = '#FFD700';
        } elseif ($averageQualityScore >= 3.5) {
            $overallLevel = '天灵之体';
            $levelColor = '#9b59b6';
        } elseif ($averageQualityScore >= 2.5) {
            $overallLevel = '地灵之体';
            $levelColor = '#3498db';
        } elseif ($averageQualityScore >= 1.5) {
            $overallLevel = '凡灵之体';
            $levelColor = '#27ae60';
        } else {
            $overallLevel = '废柴之体';
            $levelColor = '#6c757d';
        }
        
        return [
            'total_value' => $totalValue,
            'average_value' => round($averageValue, 1),
            'overall_level' => $overallLevel,
            'level_color' => $levelColor,
            'dominant_element' => $dominantElement,
            'dominant_element_name' => self::$FIVE_ELEMENTS_CONFIG[$dominantElement]['name'],
            'cultivation_potential' => self::calculateCultivationPotential($averageQualityScore)
        ];
    }
    
    /**
     * 获取品质分数
     * @param string $quality 品质名称
     * @return int 分数
     */
    private static function getQualityScore($quality) {
        $scoreMap = [
            '废灵根' => 1,
            '下品灵根' => 2,
            '中品灵根' => 3,
            '上品灵根' => 4,
            '极品灵根' => 5
        ];
        
        return isset($scoreMap[$quality]) ? $scoreMap[$quality] : 1;
    }
    
    /**
     * 计算修炼潜力描述
     * @param float $averageScore 平均品质分数
     * @return string 潜力描述
     */
    private static function calculateCultivationPotential($averageScore) {
        if ($averageScore >= 4.5) {
            return '修炼速度+150%，突破成功率+30%，领悟几率+50%';
        } elseif ($averageScore >= 3.5) {
            return '修炼速度+100%，突破成功率+20%，领悟几率+30%';
        } elseif ($averageScore >= 2.5) {
            return '修炼速度+50%，突破成功率+10%，领悟几率+15%';
        } elseif ($averageScore >= 1.5) {
            return '修炼速度正常，突破成功率正常';
        } else {
            return '修炼速度-25%，突破成功率-10%';
        }
    }
    
    /**
     * 保存灵根数据到数据库
     * @param PDO $pdo 数据库连接
     * @param int $characterId 角色ID
     * @param array $roots 灵根数据
     * @return bool 是否成功
     */
    public static function saveRootsToDatabase($pdo, $characterId, $roots) {
        try {
            $stmt = $pdo->prepare("
                UPDATE characters 
                SET metal_affinity = ?, wood_affinity = ?, water_affinity = ?, 
                    fire_affinity = ?, earth_affinity = ?
                WHERE id = ?
            ");
            
            return $stmt->execute([
                $roots['metal']['value'],
                $roots['wood']['value'],
                $roots['water']['value'],
                $roots['fire']['value'],
                $roots['earth']['value'],
                $characterId
            ]);
            
        } catch (Exception $e) {
            error_log("保存灵根数据失败: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 从数据库读取灵根数据
     * @param PDO $pdo 数据库连接
     * @param int $characterId 角色ID
     * @return array|null 灵根数据
     */
    public static function loadRootsFromDatabase($pdo, $characterId) {
        try {
            $stmt = $pdo->prepare("
                SELECT metal_affinity, wood_affinity, water_affinity, 
                       fire_affinity, earth_affinity
                FROM characters 
                WHERE id = ?
            ");
            $stmt->execute([$characterId]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$result) {
                return null;
            }
            
            $roots = [];
            $elements = ['metal', 'wood', 'water', 'fire', 'earth'];
            
            foreach ($elements as $element) {
                $fieldName = $element . '_affinity';
                $value = intval($result[$fieldName]);
                $quality = self::determineRootQuality($value);
                
                $roots[$element] = [
                    'value' => $value,
                    'quality' => $quality,
                    'multiplier' => self::$ROOT_QUALITY_CONFIG[$quality]['multiplier']
                ];
            }
            
            return $roots;
            
        } catch (Exception $e) {
            error_log("读取灵根数据失败: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * 获取灵根配置信息（用于前端显示）
     * @return array 配置数组
     */
    public static function getAllConfigs() {
        return [
            'qualities' => self::$ROOT_QUALITY_CONFIG,
            'elements' => self::$FIVE_ELEMENTS_CONFIG
        ];
    }
}

/**
 * 兼容性函数 - 快速生成角色灵根
 */
function generateCharacterSpiritualRoots($generateType = 'random') {
    return FiveElementsSpiritualRootSystem::generateSpiritualRoots($generateType);
}

/**
 * 兼容性函数 - 计算灵根属性加成
 */
function calculateSpiritualRootBonus($roots) {
    return FiveElementsSpiritualRootSystem::calculateRootAttributeBonus($roots);
}
?> 