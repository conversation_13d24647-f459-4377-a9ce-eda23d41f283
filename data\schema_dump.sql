-- 数据库 `yn_game` 结构转储
-- 生成时间: 2025-06-21 10:03:27
-- 共 45 个表

-- ----------------------------
-- 表结构: `admin_logs`
-- ----------------------------
CREATE TABLE `admin_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `admin_id` int(11) NOT NULL COMMENT '管理员ID',
  `action_type` varchar(50) NOT NULL COMMENT '操作类型',
  `target_type` varchar(50) DEFAULT NULL COMMENT '目标类型',
  `target_id` int(11) DEFAULT NULL COMMENT '目标ID',
  `action_description` text COMMENT '操作描述',
  `request_data` json DEFAULT NULL COMMENT '请求数据',
  `response_data` json DEFAULT NULL COMMENT '响应数据',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理',
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `admin_id` (`admin_id`),
  CONSTRAINT `admin_logs_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `admin_users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理日志表';

-- ----------------------------
-- 表结构: `admin_users`
-- ----------------------------
CREATE TABLE `admin_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '管理员账号',
  `password_hash` varchar(255) NOT NULL COMMENT '密码哈希',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `role` enum('super_admin','admin','operator','viewer') DEFAULT 'operator' COMMENT '角色权限',
  `permissions` json DEFAULT NULL COMMENT '详细权限',
  `status` enum('active','inactive','banned') DEFAULT 'active' COMMENT '状态',
  `last_login_time` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';

-- ----------------------------
-- 表结构: `adventure_events`
-- ----------------------------
CREATE TABLE `adventure_events` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `event_name` varchar(100) NOT NULL COMMENT '奇遇事件名称',
  `event_type` enum('treasure_discovery','equipment_find','skill_fragment','precious_pills','spirit_stone_treasure','recipe_inheritance','secret_realm_discovery') NOT NULL COMMENT '奇遇事件类型',
  `probability` decimal(5,2) NOT NULL COMMENT '触发概率(%)',
  `min_map_level` int(11) DEFAULT '1' COMMENT '最低地图等级要求',
  `max_map_level` int(11) DEFAULT '8' COMMENT '最高地图等级要求',
  `reward_config` json NOT NULL COMMENT '奖励配置(JSON格式)',
  `description` text COMMENT '事件描述',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=64 DEFAULT CHARSET=utf8mb4 COMMENT='奇遇事件配置表';

-- ----------------------------
-- 表结构: `adventure_trigger_logs`
-- ----------------------------
CREATE TABLE `adventure_trigger_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT '0',
  `character_id` int(11) NOT NULL COMMENT '角色ID',
  `event_type` varchar(50) DEFAULT 'treasure_discovery',
  `event_id` int(11) NOT NULL COMMENT '奇遇事件ID',
  `map_id` int(11) NOT NULL COMMENT '触发地图ID',
  `adventure_value_before` int(11) NOT NULL COMMENT '触发前奇遇值',
  `adventure_value_after` int(11) DEFAULT '0' COMMENT '触发后奇遇值',
  `rewards_received` json DEFAULT NULL COMMENT '获得的奖励',
  `trigger_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_character` (`user_id`,`character_id`),
  KEY `idx_event_id` (`event_id`),
  KEY `idx_map_id` (`map_id`),
  KEY `idx_trigger_time` (`trigger_time`),
  CONSTRAINT `adventure_trigger_logs_ibfk_1` FOREIGN KEY (`event_id`) REFERENCES `adventure_events` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=45 DEFAULT CHARSET=utf8mb4 COMMENT='奇遇触发日志表';

-- ----------------------------
-- 表结构: `battle_records`
-- ----------------------------
CREATE TABLE `battle_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `character_id` bigint(20) NOT NULL COMMENT '角色ID',
  `map_id` int(11) DEFAULT NULL COMMENT '地图ID',
  `stage_number` int(11) DEFAULT NULL COMMENT '关卡号',
  `monster_id` int(11) DEFAULT NULL COMMENT '怪物ID',
  `battle_type` enum('pve','pvp','dungeon','special') DEFAULT 'pve' COMMENT '战斗类型',
  `battle_result` enum('victory','defeat','draw') NOT NULL COMMENT '战斗结果',
  `battle_duration` int(11) DEFAULT NULL COMMENT '战斗时长(秒)',
  `damage_dealt` int(11) DEFAULT '0' COMMENT '造成伤害',
  `damage_received` int(11) DEFAULT '0' COMMENT '受到伤害',
  `experience_gained` int(11) DEFAULT '0' COMMENT '获得经验',
  `spirit_stones_gained` int(11) DEFAULT '0' COMMENT '获得灵石',
  `items_dropped` json DEFAULT NULL COMMENT '掉落物品',
  `battle_data` json DEFAULT NULL COMMENT '战斗详细数据',
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `character_id` (`character_id`),
  KEY `map_id` (`map_id`),
  KEY `monster_id` (`monster_id`),
  CONSTRAINT `battle_records_ibfk_1` FOREIGN KEY (`character_id`) REFERENCES `characters` (`id`),
  CONSTRAINT `battle_records_ibfk_2` FOREIGN KEY (`map_id`) REFERENCES `game_maps` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2606 DEFAULT CHARSET=utf8mb4 COMMENT='战斗记录表';

-- ----------------------------
-- 表结构: `character_equipment`
-- ----------------------------
CREATE TABLE `character_equipment` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `character_id` bigint(20) NOT NULL COMMENT '角色ID',
  `item_id` int(11) NOT NULL COMMENT '物品ID',
  `inventory_item_id` int(11) DEFAULT NULL COMMENT '背包物品ID',
  `slot_type` enum('weapon','head','chest','legs','feet','ring','necklace','bracelet') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '装备槽位类型',
  `slot_index` int(11) DEFAULT '1' COMMENT '槽位编号(同类型多个槽位时)',
  `attack_order` int(11) DEFAULT NULL COMMENT '攻击顺序(仅武器有效)',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否激活(仅武器有效)',
  `enhancement_level` int(11) DEFAULT '0' COMMENT '强化等级',
  `socket_gems` json DEFAULT NULL COMMENT '镶嵌宝石',
  `equipped_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '装备时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_character_slot` (`character_id`,`slot_type`,`slot_index`),
  KEY `item_id` (`item_id`)
) ENGINE=MyISAM AUTO_INCREMENT=318 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- 表结构: `character_growth_logs`
-- ----------------------------
CREATE TABLE `character_growth_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `character_id` bigint(20) NOT NULL COMMENT '角色ID',
  `log_type` enum('attribute','cultivation') NOT NULL COMMENT '日志类型',
  `attribute_type` enum('physique','comprehension','constitution','spirit','agility') DEFAULT NULL COMMENT '属性类型',
  `change_amount` int(11) DEFAULT NULL COMMENT '变动数量',
  `change_type` enum('pill','cultivation','tribulation','quest','other') DEFAULT NULL COMMENT '变动类型',
  `technique_name` varchar(50) DEFAULT NULL COMMENT '功法名称',
  `exp_gained` int(11) DEFAULT NULL COMMENT '获得经验',
  `breakthrough` tinyint(1) DEFAULT NULL COMMENT '是否突破',
  `breakthrough_level` int(11) DEFAULT NULL COMMENT '突破等级',
  `cultivation_time` int(11) DEFAULT NULL COMMENT '修炼时长(秒)',
  `description` text COMMENT '变动说明',
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `character_id` (`character_id`),
  CONSTRAINT `character_growth_logs_ibfk_1` FOREIGN KEY (`character_id`) REFERENCES `characters` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=508 DEFAULT CHARSET=utf8mb4 COMMENT='角色成长日志表';

-- ----------------------------
-- 表结构: `character_name_changes`
-- ----------------------------
CREATE TABLE `character_name_changes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `character_id` bigint(20) NOT NULL COMMENT '角色ID',
  `old_name` varchar(50) NOT NULL COMMENT '旧角色名',
  `new_name` varchar(50) NOT NULL COMMENT '新角色名',
  `change_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '变更时间',
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `character_id` (`character_id`),
  CONSTRAINT `character_name_changes_ibfk_1` FOREIGN KEY (`character_id`) REFERENCES `characters` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='角色名称变更日志表';

-- ----------------------------
-- 表结构: `characters`
-- ----------------------------
CREATE TABLE `characters` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '全局唯一ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `server_id` int(11) DEFAULT '1' COMMENT '服务器ID',
  `original_server_id` int(11) DEFAULT '1' COMMENT '原始服务器ID',
  `character_name` varchar(50) NOT NULL COMMENT '角色名称',
  `original_name` varchar(50) DEFAULT NULL COMMENT '原始角色名',
  `name_changed` tinyint(1) DEFAULT '0' COMMENT '是否因合区改名',
  `realm_id` int(11) DEFAULT '1' COMMENT '境界ID',
  `realm_progress` decimal(5,2) DEFAULT '0.00' COMMENT '境界进度百分比',
  `current_hp` int(11) DEFAULT '100' COMMENT '当前生命值',
  `current_mp` int(11) DEFAULT '100' COMMENT '当前法力值',
  `cultivation_points` int(11) DEFAULT '0' COMMENT '修炼点数',
  `physique` int(11) DEFAULT '10' COMMENT '筋骨',
  `comprehension` int(11) DEFAULT '10' COMMENT '悟性',
  `constitution` int(11) DEFAULT '10' COMMENT '体魄',
  `spirit` int(11) DEFAULT '10' COMMENT '神魂',
  `agility` int(11) DEFAULT '10' COMMENT '身法',
  `metal_affinity` int(11) DEFAULT '0' COMMENT '金灵根',
  `wood_affinity` int(11) DEFAULT '0' COMMENT '木灵根',
  `water_affinity` int(11) DEFAULT '0' COMMENT '水灵根',
  `fire_affinity` int(11) DEFAULT '0' COMMENT '火灵根',
  `earth_affinity` int(11) DEFAULT '0' COMMENT '土灵根',
  `cultivation_techniques` text COMMENT 'JSON格式的功法数据集合',
  `current_technique` varchar(50) DEFAULT NULL COMMENT '当前使用的功法ID',
  `attribute_pill_count` json DEFAULT NULL COMMENT '属性丹药使用记录',
  `spiritual_root_usage` text COMMENT 'JSON格式天材地宝使用记录',
  `arena_dao_power` int(11) DEFAULT '0',
  `arena_daily_attempts` int(11) DEFAULT '0',
  `arena_purchased_attempts` int(11) DEFAULT '0',
  `arena_last_reset` date DEFAULT NULL,
  `arena_rank_level` int(11) DEFAULT '1',
  `arena_rank_points` int(11) DEFAULT '0' COMMENT '竞技场段位积分',
  `arena_total_wins` int(11) DEFAULT '0',
  `arena_total_battles` int(11) DEFAULT '0',
  `arena_win_streak` int(11) DEFAULT '0',
  `arena_best_streak` int(11) DEFAULT '0',
  `arena_skill_sequence` varchar(50) DEFAULT '0,1,2,3,4,5',
  `tribulation_pill_used` tinyint(1) DEFAULT '0' COMMENT '是否使用过渡劫丹',
  `soul_healing_pill_used` tinyint(1) DEFAULT '0' COMMENT '是否使用过养魂丹',
  `total_battles` int(11) DEFAULT '0' COMMENT '总战斗次数',
  `total_victories` int(11) DEFAULT '0' COMMENT '总胜利次数',
  `total_defeats` int(11) DEFAULT '0' COMMENT '总失败次数',
  `highest_damage` int(11) DEFAULT '0' COMMENT '最高伤害记录',
  `highest_healing` int(11) DEFAULT '0' COMMENT '最高治疗记录',
  `last_cultivation_time` timestamp NULL DEFAULT NULL COMMENT '最后修炼时间',
  `last_login_time` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `last_battle_time` timestamp NULL DEFAULT NULL COMMENT '最后战斗时间',
  `last_tribulation_time` timestamp NULL DEFAULT NULL COMMENT '最后渡劫时间',
  `total_online_time` int(11) DEFAULT '0' COMMENT '总在线时间(分钟)',
  `avatar_image` varchar(200) DEFAULT NULL COMMENT '角色头像图片',
  `avatar_frame` varchar(100) DEFAULT 'base (1).png' COMMENT '头像外框文件名',
  `character_model` varchar(200) DEFAULT NULL COMMENT '角色立绘/模型图片',
  `appearance_data` json DEFAULT NULL COMMENT '外观自定义数据',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `inventory_slots` int(11) DEFAULT '30' COMMENT '背包格子数量',
  `spiritual_roots` text COMMENT '五行灵根数据(JSON格式)',
  `learned_recipes` json DEFAULT NULL COMMENT '已学会的丹方记录',
  `soul_damage_time` int(11) DEFAULT NULL COMMENT '魂力受损时间戳（渡劫失败时记录）',
  `soul_recovery_per_second` decimal(10,6) DEFAULT NULL COMMENT '每秒魂力恢复点数',
  `current_soul_power` int(11) DEFAULT '100' COMMENT '当前魂力值（0-100）',
  `last_breakthrough_time` timestamp NULL DEFAULT NULL COMMENT '最后突破时间',
  `pickup_settings` text COMMENT '装备拾取设置JSON',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_server` (`user_id`,`server_id`) COMMENT '确保一个账号在一个分区下只有一个角色',
  KEY `server_id` (`server_id`),
  KEY `realm_id` (`realm_id`),
  KEY `idx_original_server` (`original_server_id`),
  KEY `idx_current_technique` (`current_technique`),
  KEY `idx_arena_rank_level` (`arena_rank_level`),
  KEY `idx_arena_total_battles` (`arena_total_battles`),
  KEY `idx_arena_last_reset` (`arena_last_reset`),
  KEY `idx_arena_rank_points` (`arena_rank_points`),
  CONSTRAINT `characters_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `characters_ibfk_2` FOREIGN KEY (`server_id`) REFERENCES `game_servers` (`id`),
  CONSTRAINT `characters_ibfk_3` FOREIGN KEY (`original_server_id`) REFERENCES `game_servers` (`id`),
  CONSTRAINT `characters_ibfk_4` FOREIGN KEY (`realm_id`) REFERENCES `realm_levels` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=38 DEFAULT CHARSET=utf8mb4 COMMENT='角色表';

-- ----------------------------
-- 表结构: `crafting_recipes`
-- ----------------------------
CREATE TABLE `crafting_recipes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `recipe_name` varchar(100) NOT NULL COMMENT '配方名称',
  `result_item_id` int(11) NOT NULL COMMENT '产出物品ID',
  `result_quantity` int(11) DEFAULT '1' COMMENT '产出数量',
  `required_level` int(11) DEFAULT '1' COMMENT '需要等级',
  `required_profession` varchar(50) DEFAULT NULL COMMENT '需要职业',
  `craft_time` int(11) DEFAULT '60' COMMENT '制作时间(秒)',
  `energy_cost` int(11) DEFAULT '10' COMMENT '体力消耗',
  `success_rate` decimal(5,2) DEFAULT '100.00' COMMENT '成功率%',
  `materials` json DEFAULT NULL COMMENT '所需材料(JSON格式)',
  `description` text COMMENT '配方描述',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `result_item_id` (`result_item_id`),
  CONSTRAINT `crafting_recipes_ibfk_1` FOREIGN KEY (`result_item_id`) REFERENCES `game_items` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=102 DEFAULT CHARSET=utf8mb4 COMMENT='配方主表';

-- ----------------------------
-- 表结构: `drop_group_items`
-- ----------------------------
CREATE TABLE `drop_group_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `group_id` int(11) NOT NULL COMMENT '掉落组ID',
  `item_id` int(11) NOT NULL COMMENT '物品ID',
  `drop_weight` int(11) DEFAULT '1' COMMENT '掉落权重',
  `min_quantity` int(11) DEFAULT '1' COMMENT '最少数量',
  `max_quantity` int(11) DEFAULT '1' COMMENT '最多数量',
  `level_min` int(11) DEFAULT '1' COMMENT '最低等级限制',
  `level_max` int(11) DEFAULT '999' COMMENT '最高等级限制',
  `drop_condition` json DEFAULT NULL COMMENT '掉落条件',
  `is_guaranteed` tinyint(1) DEFAULT '0' COMMENT '是否必掉',
  `created_at` timestamp NULL DEFAULT NULL,
  `realm_min` int(11) DEFAULT '1' COMMENT '最小境界要求',
  `realm_max` int(11) DEFAULT '280' COMMENT '最大境界要求',
  PRIMARY KEY (`id`),
  KEY `group_id` (`group_id`),
  KEY `item_id` (`item_id`),
  KEY `idx_realm_range` (`realm_min`,`realm_max`),
  CONSTRAINT `drop_group_items_ibfk_1` FOREIGN KEY (`group_id`) REFERENCES `drop_groups` (`id`) ON DELETE CASCADE,
  CONSTRAINT `drop_group_items_ibfk_2` FOREIGN KEY (`item_id`) REFERENCES `game_items` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4309 DEFAULT CHARSET=utf8mb4 COMMENT='掉落组物品表';

-- ----------------------------
-- 表结构: `drop_groups`
-- ----------------------------
CREATE TABLE `drop_groups` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `group_name` varchar(100) NOT NULL COMMENT '掉落组名称',
  `group_type` enum('装备','材料','丹方','丹药','其他') NOT NULL DEFAULT '装备',
  `total_weight` int(11) DEFAULT '100' COMMENT '总权重',
  `max_drops` int(11) DEFAULT '1' COMMENT '最大掉落数量',
  `min_drops` int(11) DEFAULT '0' COMMENT '最小掉落数量',
  `guarantee_rare` tinyint(1) DEFAULT '0' COMMENT '是否保底稀有',
  `description` text COMMENT '掉落组描述',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=93 DEFAULT CHARSET=utf8mb4 COMMENT='掉落组表';

-- ----------------------------
-- 表结构: `dungeons`
-- ----------------------------
CREATE TABLE `dungeons` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `dungeon_name` varchar(100) NOT NULL COMMENT '副本名称',
  `dungeon_type` enum('normal','heroic','mythic','special') DEFAULT 'normal' COMMENT '副本类型',
  `level_requirement` int(11) DEFAULT '1' COMMENT '等级需求',
  `realm_requirement` int(11) DEFAULT NULL COMMENT '境界需求',
  `team_size_min` int(11) DEFAULT '1' COMMENT '最少队伍人数',
  `team_size_max` int(11) DEFAULT '1' COMMENT '最多队伍人数',
  `duration_limit` int(11) DEFAULT '3600' COMMENT '时间限制(秒)',
  `daily_limit` int(11) DEFAULT '1' COMMENT '每日次数限制',
  `entry_cost` int(11) DEFAULT '0' COMMENT '进入消耗',
  `description` text COMMENT '副本描述',
  `background_image` varchar(200) DEFAULT NULL COMMENT '副本背景',
  `dungeon_icon` varchar(200) DEFAULT NULL COMMENT '副本图标',
  `rewards` json DEFAULT NULL COMMENT '固定奖励',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否开启',
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COMMENT='副本表';

-- ----------------------------
-- 表结构: `equipment_durability_logs`
-- ----------------------------
CREATE TABLE `equipment_durability_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `inventory_item_id` int(11) NOT NULL COMMENT '背包物品ID',
  `durability_before` int(11) NOT NULL COMMENT '操作前耐久',
  `durability_after` int(11) NOT NULL COMMENT '操作后耐久',
  `change_amount` int(11) NOT NULL COMMENT '变化数量',
  `change_reason` enum('battle_win','battle_lose','repair','enhance','decay','special') NOT NULL COMMENT '变化原因',
  `battle_id` int(11) DEFAULT NULL COMMENT '相关战斗ID',
  `repair_cost` int(11) DEFAULT NULL COMMENT '修理费用',
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_item_time` (`inventory_item_id`,`created_at`),
  CONSTRAINT `equipment_durability_logs_ibfk_1` FOREIGN KEY (`inventory_item_id`) REFERENCES `user_inventories` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='装备耐久记录表';

-- ----------------------------
-- 表结构: `equipment_sets`
-- ----------------------------
CREATE TABLE `equipment_sets` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `set_name` varchar(100) NOT NULL COMMENT '套装名称',
  `set_description` text COMMENT '套装描述',
  `set_icon` varchar(200) DEFAULT NULL COMMENT '套装图标',
  `min_pieces` int(11) DEFAULT '2' COMMENT '最少件数',
  `max_pieces` int(11) DEFAULT '8' COMMENT '最多件数',
  `set_bonus_2` json DEFAULT NULL COMMENT '2件套效果',
  `set_bonus_4` json DEFAULT NULL COMMENT '4件套效果',
  `set_bonus_6` json DEFAULT NULL COMMENT '6件套效果',
  `set_bonus_8` json DEFAULT NULL COMMENT '8件套效果',
  `rarity` enum('common','uncommon','rare','epic','legendary','mythic') DEFAULT 'common',
  `level_requirement` int(11) DEFAULT '1',
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COMMENT='装备套装表';

-- ----------------------------
-- 表结构: `game_items`
-- ----------------------------
CREATE TABLE `game_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `item_code` varchar(50) NOT NULL COMMENT '物品唯一代码',
  `item_name` varchar(100) NOT NULL COMMENT '物品名称',
  `item_type` enum('weapon','equipment','armor','accessory','consumable','material','spirit','special') NOT NULL COMMENT '物品类型',
  `item_subtype` varchar(50) DEFAULT NULL COMMENT '物品子类型',
  `slot_type` enum('sword','fan','chest','legs','feet','ring','necklace','bracelet','technique_manual') DEFAULT NULL COMMENT '物品位置',
  `rarity` enum('普通','稀有','史诗','传说','神话') DEFAULT '普通' COMMENT '物品品质',
  `realm_requirement` int(11) DEFAULT NULL COMMENT '境界需求',
  `description` text COMMENT '物品描述',
  `icon_image` varchar(200) DEFAULT NULL COMMENT '图标图片路径',
  `detail_image` varchar(200) DEFAULT NULL COMMENT '详情图片路径',
  `model_image` varchar(200) DEFAULT NULL COMMENT '战斗模型图片',
  `sell_price` int(11) DEFAULT '0' COMMENT '出售价格',
  `buy_price` int(11) DEFAULT '0' COMMENT '购买价格',
  `is_tradeable` tinyint(1) DEFAULT '1' COMMENT '是否可交易',
  `is_stackable` tinyint(1) DEFAULT '1' COMMENT '是否可堆叠',
  `max_stack` int(11) DEFAULT '1' COMMENT '最大堆叠数',
  `max_durability` int(11) DEFAULT '100' COMMENT '最大耐久度',
  `weight` decimal(8,2) DEFAULT '0.00' COMMENT '重量',
  `can_have_rarity` tinyint(1) DEFAULT '0' COMMENT '是否可以有稀有度',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `physical_attack` int(11) DEFAULT '0' COMMENT '物理攻击力',
  `immortal_attack` int(11) DEFAULT '0' COMMENT '仙术攻击力',
  `physical_defense` int(11) DEFAULT '0' COMMENT '物理防御力',
  `immortal_defense` int(11) DEFAULT '0' COMMENT '仙术防御力',
  `hp_bonus` int(11) DEFAULT '0' COMMENT '生命值加成',
  `mp_bonus` int(11) DEFAULT '0' COMMENT '法力值加成',
  `speed_bonus` int(11) DEFAULT '0' COMMENT '速度加成',
  `critical_bonus` decimal(5,2) DEFAULT '0.00' COMMENT '暴击',
  `critical_damage` decimal(5,2) DEFAULT '0.00' COMMENT '暴击伤害',
  `critical_resistance` decimal(5,2) DEFAULT '0.00' COMMENT '免暴率',
  `accuracy_bonus` decimal(5,2) DEFAULT '0.00' COMMENT '命中加成',
  `dodge_bonus` decimal(5,2) DEFAULT '0.00' COMMENT '闪避加成',
  `special_effects` text COMMENT '特殊效果(JSON格式)',
  `attribute_type` varchar(50) DEFAULT 'base' COMMENT '属性类型',
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `item_code` (`item_code`),
  KEY `idx_item_type` (`item_type`),
  KEY `idx_realm_req` (`realm_requirement`),
  KEY `idx_game_items_physical_attack` (`physical_attack`),
  KEY `idx_game_items_immortal_attack` (`immortal_attack`),
  KEY `idx_game_items_physical_defense` (`physical_defense`),
  KEY `idx_game_items_immortal_defense` (`immortal_defense`)
) ENGINE=InnoDB AUTO_INCREMENT=1120 DEFAULT CHARSET=utf8mb4 COMMENT='游戏物品主表';

-- ----------------------------
-- 表结构: `game_maps`
-- ----------------------------
CREATE TABLE `game_maps` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `map_name` varchar(100) NOT NULL COMMENT '地图名称',
  `map_code` varchar(50) NOT NULL COMMENT '地图代码',
  `map_type` enum('normal','dungeon','special','pvp') DEFAULT 'normal' COMMENT '地图类型',
  `level_requirement` int(11) DEFAULT '1' COMMENT '等级需求',
  `realm_requirement` int(11) DEFAULT NULL COMMENT '境界需求',
  `max_stages` int(11) DEFAULT '1' COMMENT '最大关卡数',
  `description` text COMMENT '地图描述',
  `environment_effects` json DEFAULT NULL,
  `background_image` varchar(200) DEFAULT NULL COMMENT '背景图片',
  `map_icon` varchar(200) DEFAULT NULL COMMENT '地图图标',
  `entry_cost` int(11) DEFAULT '0' COMMENT '进入消耗',
  `daily_limit` int(11) DEFAULT '0' COMMENT '每日限制次数(0为无限)',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否开启',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序权重',
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `map_code` (`map_code`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COMMENT='游戏地图表';

-- ----------------------------
-- 表结构: `game_servers`
-- ----------------------------
CREATE TABLE `game_servers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `server_name` varchar(50) NOT NULL COMMENT '分区名称',
  `server_code` varchar(20) NOT NULL COMMENT '分区代码',
  `status` enum('maintenance','normal','crowded','full','merged') DEFAULT 'normal' COMMENT '分区状态',
  `max_players` int(11) DEFAULT '10000' COMMENT '最大玩家数',
  `current_players` int(11) DEFAULT '0' COMMENT '当前玩家数',
  `open_time` timestamp NULL DEFAULT NULL COMMENT '开服时间',
  `maintenance_time` timestamp NULL DEFAULT NULL COMMENT '维护时间',
  `merge_time` timestamp NULL DEFAULT NULL COMMENT '合区时间',
  `merge_to_server_id` int(11) DEFAULT NULL COMMENT '合并到哪个分区',
  `description` text COMMENT '分区描述',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `server_code` (`server_code`),
  KEY `merge_to_server_id` (`merge_to_server_id`),
  CONSTRAINT `game_servers_ibfk_1` FOREIGN KEY (`merge_to_server_id`) REFERENCES `game_servers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='游戏分区表';

-- ----------------------------
-- 表结构: `immortal_arena_match_pool`
-- ----------------------------
CREATE TABLE `immortal_arena_match_pool` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `character_id` int(11) NOT NULL,
  `dao_power` int(11) NOT NULL,
  `realm_id` int(11) DEFAULT NULL,
  `character_snapshot` text NOT NULL,
  `match_timeout` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- 表结构: `immortal_arena_ranks`
-- ----------------------------
CREATE TABLE `immortal_arena_ranks` (
  `rank_level` int(11) NOT NULL,
  `rank_name` varchar(20) NOT NULL,
  `rank_color` varchar(7) DEFAULT '#FFFFFF',
  `reward_multiplier` decimal(3,2) DEFAULT '1.00',
  `required_points` int(11) DEFAULT '0' COMMENT '晋升所需积分',
  PRIMARY KEY (`rank_level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- 表结构: `immortal_arena_records`
-- ----------------------------
CREATE TABLE `immortal_arena_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `character_id` int(11) NOT NULL,
  `opponent_character_id` int(11) DEFAULT NULL,
  `opponent_name` varchar(50) NOT NULL,
  `opponent_dao_power` int(11) NOT NULL,
  `is_ai_puppet` tinyint(1) DEFAULT '0',
  `ai_template_id` int(11) DEFAULT NULL,
  `battle_result` enum('win','lose','draw') NOT NULL,
  `spirit_stone_reward` int(11) NOT NULL DEFAULT '0',
  `rank_points_change` int(11) DEFAULT '0' COMMENT '段位积分变化',
  `battle_duration` int(11) DEFAULT NULL,
  `battle_rounds` int(11) DEFAULT NULL,
  `battle_snapshot` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=79 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- 表结构: `item_set_bindings`
-- ----------------------------
CREATE TABLE `item_set_bindings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `set_id` int(11) NOT NULL COMMENT '套装ID',
  `item_id` int(11) NOT NULL COMMENT '物品ID',
  `slot_type` enum('weapon','head','chest','legs','feet','ring','necklace','bracelet') NOT NULL COMMENT '槽位类型',
  `is_core_piece` tinyint(1) DEFAULT '0' COMMENT '是否核心部件',
  `piece_order` int(11) DEFAULT '1' COMMENT '部件顺序',
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_set_item` (`set_id`,`item_id`),
  KEY `item_id` (`item_id`),
  CONSTRAINT `item_set_bindings_ibfk_1` FOREIGN KEY (`set_id`) REFERENCES `equipment_sets` (`id`) ON DELETE CASCADE,
  CONSTRAINT `item_set_bindings_ibfk_2` FOREIGN KEY (`item_id`) REFERENCES `game_items` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='物品套装绑定表';

-- ----------------------------
-- 表结构: `item_skills`
-- ----------------------------
CREATE TABLE `item_skills` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `item_id` int(11) NOT NULL COMMENT '物品ID',
  `skill_name` varchar(100) NOT NULL COMMENT '技能名称',
  `skill_type` enum('active','passive','trigger') DEFAULT 'active' COMMENT '技能类型',
  `combat_type` enum('sword_skill','magic_spell') DEFAULT 'sword_skill' COMMENT '战斗类型：sword_skill=剑技，magic_spell=法术',
  `element_type` enum('metal','wood','water','fire','earth','neutral') DEFAULT 'neutral' COMMENT '技能五行属性',
  `skill_description` text COMMENT '技能描述',
  `animation_model` enum('default','feijian','wanjianjue','jujian','leijian','xuanbingjian','hengzhan','huichunjian','youlong','zhangxinlei','huoqiushu','tengman','shuilongjuan','yanshituci','jinzhenbayu','bingzhuishu','fengrensu','huoliuxing') DEFAULT 'default' COMMENT '技能动画模型',
  `damage_multiplier` decimal(5,2) DEFAULT '1.00' COMMENT '伤害倍率',
  `mp_cost` int(11) DEFAULT '0' COMMENT '法力消耗',
  `cooldown_time` int(11) DEFAULT '0' COMMENT '冷却时间(秒)',
  `trigger_chance` decimal(5,2) DEFAULT '0.00' COMMENT '触发几率%',
  `effect_duration` int(11) DEFAULT '0' COMMENT '效果持续时间(秒)',
  `effect_data` json DEFAULT NULL COMMENT '技能效果数据',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `item_id` (`item_id`),
  CONSTRAINT `item_skills_ibfk_1` FOREIGN KEY (`item_id`) REFERENCES `game_items` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=194 DEFAULT CHARSET=utf8mb4 COMMENT='物品技能表';

-- ----------------------------
-- 表结构: `login_logs`
-- ----------------------------
CREATE TABLE `login_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
  `username` varchar(50) DEFAULT NULL COMMENT '用户名',
  `login_type` enum('success','failed','logout') NOT NULL COMMENT '登录类型',
  `fail_reason` varchar(100) DEFAULT NULL COMMENT '失败原因',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理',
  `device_info` json DEFAULT NULL COMMENT '设备信息',
  `location_info` json DEFAULT NULL COMMENT '位置信息',
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `login_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=279 DEFAULT CHARSET=utf8mb4 COMMENT='登录日志表';

-- ----------------------------
-- 表结构: `map_drop_configs`
-- ----------------------------
CREATE TABLE `map_drop_configs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `map_id` int(11) NOT NULL COMMENT '地图ID',
  `stage_number` int(11) DEFAULT NULL COMMENT '关卡号(null表示全地图)',
  `drop_group_id` int(11) NOT NULL COMMENT '掉落组ID',
  `drop_chance` decimal(5,2) DEFAULT '100.00' COMMENT '掉落几率%',
  `level_requirement` int(11) DEFAULT '1' COMMENT '等级需求',
  `realm_requirement` int(11) DEFAULT NULL COMMENT '境界需求',
  `special_condition` json DEFAULT NULL COMMENT '特殊条件',
  `is_boss_only` tinyint(1) DEFAULT '0' COMMENT '仅Boss掉落',
  `is_first_kill` tinyint(1) DEFAULT '0' COMMENT '仅首杀掉落',
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `map_id` (`map_id`),
  KEY `drop_group_id` (`drop_group_id`),
  CONSTRAINT `map_drop_configs_ibfk_1` FOREIGN KEY (`map_id`) REFERENCES `game_maps` (`id`) ON DELETE CASCADE,
  CONSTRAINT `map_drop_configs_ibfk_2` FOREIGN KEY (`drop_group_id`) REFERENCES `drop_groups` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='地图掉落配置表';

-- ----------------------------
-- 表结构: `map_stages`
-- ----------------------------
CREATE TABLE `map_stages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `map_id` int(11) NOT NULL,
  `stage_number` int(11) NOT NULL,
  `stage_name` varchar(100) NOT NULL,
  `monster_id` int(11) NOT NULL,
  `monster_level` int(11) NOT NULL,
  `realm_level` int(11) NOT NULL,
  `monster_tier` enum('normal','elite','mini_boss','boss') DEFAULT 'normal',
  `base_hp` int(11) NOT NULL,
  `base_attack` int(11) NOT NULL,
  `base_defense` int(11) NOT NULL,
  `base_speed` int(11) DEFAULT '50',
  `player_realm_requirement` int(11) DEFAULT '1',
  `stage_difficulty` decimal(3,2) DEFAULT '1.00',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `monster_name` varchar(100) NOT NULL DEFAULT '未知怪物',
  `skills` text,
  `avatar_image` varchar(200) DEFAULT NULL,
  `model_image` varchar(200) DEFAULT NULL,
  `description` text,
  `ai_pattern` varchar(50) DEFAULT 'conservative',
  `spirit_stone_reward` int(11) DEFAULT NULL COMMENT '灵石奖励',
  `gold_reward` int(11) DEFAULT NULL COMMENT '金币奖励',
  `hit_rate` int(11) DEFAULT '85' COMMENT '命中率(点数)',
  `dodge_rate` int(11) DEFAULT '5' COMMENT '闪避率(点数)',
  `critical_rate` int(11) DEFAULT '10' COMMENT '暴击率(点数)',
  `critical_resistance` int(11) DEFAULT '0' COMMENT '抗暴率(点数)',
  `base_mp` int(11) DEFAULT '50' COMMENT '基础法力值',
  `hp_regen_rate` decimal(4,3) DEFAULT '0.050' COMMENT 'HP每回合恢复率(最大值的百分比)',
  `mp_regen_rate` decimal(4,3) DEFAULT '0.050' COMMENT 'MP每回合恢复率(最大值的百分比)',
  PRIMARY KEY (`id`),
  KEY `idx_map_stage` (`map_id`,`stage_number`),
  KEY `idx_realm` (`realm_level`),
  KEY `idx_monster` (`monster_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1016 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- 表结构: `realm_levels`
-- ----------------------------
CREATE TABLE `realm_levels` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `realm_name` varchar(50) NOT NULL COMMENT '境界名称',
  `realm_level` int(11) NOT NULL COMMENT '境界等级',
  `min_experience` bigint(20) NOT NULL COMMENT '所需最少经验',
  `max_experience` bigint(20) NOT NULL COMMENT '达到上限经验',
  `hp_multiplier` decimal(5,2) DEFAULT '1.00' COMMENT '生命值倍率',
  `mp_multiplier` decimal(5,2) DEFAULT '1.00' COMMENT '法力值倍率',
  `attack_multiplier` decimal(5,2) DEFAULT '1.00' COMMENT '攻击力倍率',
  `defense_multiplier` decimal(5,2) DEFAULT '1.00' COMMENT '防御力倍率',
  `speed_multiplier` decimal(5,2) DEFAULT '1.00' COMMENT '速度倍率',
  `special_abilities` json DEFAULT NULL COMMENT '特殊能力',
  `breakthrough_items` json DEFAULT NULL COMMENT '突破所需物品',
  `description` text COMMENT '境界描述',
  `color_code` varchar(7) DEFAULT '#FFFFFF' COMMENT '境界颜色代码',
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `realm_level` (`realm_level`)
) ENGINE=InnoDB AUTO_INCREMENT=281 DEFAULT CHARSET=utf8mb4 COMMENT='境界等级表';

-- ----------------------------
-- 表结构: `redeem_codes`
-- ----------------------------
CREATE TABLE `redeem_codes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(50) NOT NULL COMMENT '兑换码',
  `code_type` enum('general','vip','event','test') DEFAULT 'general' COMMENT '兑换码类型',
  `title` varchar(100) NOT NULL COMMENT '兑换码标题',
  `description` text COMMENT '兑换码描述',
  `rewards` json NOT NULL COMMENT '奖励内容',
  `max_uses` int(11) DEFAULT '1' COMMENT '最大使用次数',
  `used_count` int(11) DEFAULT '0' COMMENT '已使用次数',
  `valid_from` timestamp NULL DEFAULT NULL COMMENT '有效开始时间',
  `valid_until` timestamp NULL DEFAULT NULL COMMENT '有效结束时间',
  `level_requirement` int(11) DEFAULT '1' COMMENT '等级需求',
  `realm_requirement` int(11) DEFAULT NULL COMMENT '境界需求',
  `user_limit` int(11) DEFAULT '1' COMMENT '单用户使用限制',
  `status` enum('active','inactive','expired') DEFAULT 'active' COMMENT '状态',
  `created_by` int(11) DEFAULT NULL COMMENT '创建者ID',
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `redeem_codes_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `admin_users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='兑换码表';

-- ----------------------------
-- 表结构: `redeem_logs`
-- ----------------------------
CREATE TABLE `redeem_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `character_id` bigint(20) NOT NULL COMMENT '角色ID',
  `code_id` int(11) NOT NULL COMMENT '兑换码ID',
  `code` varchar(50) NOT NULL COMMENT '兑换码内容',
  `rewards_received` json DEFAULT NULL COMMENT '实际获得奖励',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理',
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `character_id` (`character_id`),
  KEY `code_id` (`code_id`),
  CONSTRAINT `redeem_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `redeem_logs_ibfk_2` FOREIGN KEY (`character_id`) REFERENCES `characters` (`id`),
  CONSTRAINT `redeem_logs_ibfk_3` FOREIGN KEY (`code_id`) REFERENCES `redeem_codes` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='兑换记录表';

-- ----------------------------
-- 表结构: `server_merge_logs`
-- ----------------------------
CREATE TABLE `server_merge_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `merge_from_server_id` int(11) NOT NULL COMMENT '合并前分区ID',
  `merge_to_server_id` int(11) NOT NULL COMMENT '合并后分区ID',
  `merge_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '合并时间',
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `merge_from_server_id` (`merge_from_server_id`),
  KEY `merge_to_server_id` (`merge_to_server_id`),
  CONSTRAINT `server_merge_logs_ibfk_1` FOREIGN KEY (`merge_from_server_id`) REFERENCES `game_servers` (`id`),
  CONSTRAINT `server_merge_logs_ibfk_2` FOREIGN KEY (`merge_to_server_id`) REFERENCES `game_servers` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='服务器合并日志表';

-- ----------------------------
-- 表结构: `shop_items`
-- ----------------------------
CREATE TABLE `shop_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `item_id` int(11) NOT NULL COMMENT '物品ID，对应game_items表的id',
  `shop_key` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '商店内部标识符',
  `shop_type` enum('market','black_market','sect_market') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '商店类型：坊市/黑市',
  `category` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '商品分类',
  `gold_price` int(11) DEFAULT '0' COMMENT '金币价格（坊市使用）',
  `spirit_stone_price` int(11) DEFAULT '0' COMMENT '灵石价格（黑市使用）',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否上架销售',
  `max_purchase` int(11) DEFAULT '0' COMMENT '限购数量，0=无限购买',
  `level_requirement` int(11) DEFAULT NULL COMMENT '购买等级需求',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序权重',
  `special_tags` json DEFAULT NULL COMMENT '特殊标签，如新品、热销等',
  `purchase_note` text COLLATE utf8mb4_unicode_ci COMMENT '购买备注说明',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_shop_key` (`shop_key`),
  KEY `idx_shop_type` (`shop_type`),
  KEY `idx_category` (`category`),
  KEY `idx_active` (`is_active`),
  KEY `idx_sort` (`sort_order`)
) ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商店商品配置表';

-- ----------------------------
-- 表结构: `skill_fragment_collection`
-- ----------------------------
CREATE TABLE `skill_fragment_collection` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `character_id` int(11) NOT NULL COMMENT '角色ID',
  `fragment_type` varchar(50) NOT NULL COMMENT '功法类型',
  `fragment_parts` json NOT NULL COMMENT '已收集的碎片部分',
  `total_fragments_needed` int(11) NOT NULL COMMENT '需要的总碎片数',
  `is_completed` tinyint(1) DEFAULT '0' COMMENT '是否收集完成',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_fragment` (`user_id`,`character_id`,`fragment_type`),
  KEY `idx_character_id` (`character_id`),
  KEY `idx_fragment_type` (`fragment_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='功法碎片收集记录表';

-- ----------------------------
-- 表结构: `spirit_souls`
-- ----------------------------
CREATE TABLE `spirit_souls` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `spirit_name` varchar(100) NOT NULL COMMENT '器灵名称',
  `spirit_type` enum('attack','defense','support','special') DEFAULT 'attack' COMMENT '器灵类型',
  `rarity` enum('common','uncommon','rare','epic','legendary','mythic') DEFAULT 'common',
  `base_level` int(11) DEFAULT '1' COMMENT '基础等级',
  `max_level` int(11) DEFAULT '100' COMMENT '最大等级',
  `growth_rate` decimal(5,2) DEFAULT '1.00' COMMENT '成长率',
  `base_attributes` json DEFAULT NULL COMMENT '基础属性',
  `skills` json DEFAULT NULL COMMENT '器灵技能',
  `evolution_materials` json DEFAULT NULL COMMENT '进化材料',
  `spirit_image` varchar(200) DEFAULT NULL COMMENT '器灵图片',
  `description` text COMMENT '器灵描述',
  `unlock_condition` json DEFAULT NULL COMMENT '解锁条件',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COMMENT='器灵主表';

-- ----------------------------
-- 表结构: `spiritual_material_usage`
-- ----------------------------
CREATE TABLE `spiritual_material_usage` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `character_id` int(11) NOT NULL COMMENT '角色ID',
  `item_id` int(11) NOT NULL COMMENT '天材地宝物品ID',
  `spiritual_root_type` enum('metal','wood','water','fire','earth') NOT NULL COMMENT '灵根类型',
  `usage_count` int(11) DEFAULT '0' COMMENT '使用次数',
  `max_usage_limit` int(11) DEFAULT '10' COMMENT '最大使用限制',
  `first_used_at` timestamp NULL DEFAULT NULL COMMENT '首次使用时间',
  `last_used_at` timestamp NULL DEFAULT NULL COMMENT '最后使用时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_item` (`user_id`,`character_id`,`item_id`),
  KEY `idx_character_id` (`character_id`),
  KEY `idx_item_id` (`item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='天材地宝使用记录表';

-- ----------------------------
-- 表结构: `user_achievements`
-- ----------------------------
CREATE TABLE `user_achievements` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `character_id` bigint(20) NOT NULL COMMENT '角色ID',
  `achievement_type` enum('battle','collection','exploration','social','special') NOT NULL COMMENT '成就类型',
  `achievement_name` varchar(100) NOT NULL COMMENT '成就名称',
  `achievement_description` text COMMENT '成就描述',
  `progress_current` int(11) DEFAULT '0' COMMENT '当前进度',
  `progress_required` int(11) DEFAULT '1' COMMENT '需要进度',
  `is_completed` tinyint(1) DEFAULT '0' COMMENT '是否完成',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `rewards` json DEFAULT NULL COMMENT '奖励内容',
  `is_claimed` tinyint(1) DEFAULT '0' COMMENT '是否已领取',
  `claimed_at` timestamp NULL DEFAULT NULL COMMENT '领取时间',
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `character_id` (`character_id`),
  CONSTRAINT `user_achievements_ibfk_1` FOREIGN KEY (`character_id`) REFERENCES `characters` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户成就表';

-- ----------------------------
-- 表结构: `user_adventure_records`
-- ----------------------------
CREATE TABLE `user_adventure_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT '0',
  `character_id` int(11) NOT NULL COMMENT '角色ID',
  `current_adventure_value` int(11) DEFAULT '0' COMMENT '当前奇遇值',
  `total_adventure_count` int(11) DEFAULT '0' COMMENT '总奇遇次数',
  `last_adventure_time` timestamp NULL DEFAULT NULL COMMENT '最后一次奇遇时间',
  `adventure_history` json DEFAULT NULL COMMENT '奇遇历史记录',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `character_id` (`character_id`),
  KEY `idx_user_character` (`user_id`,`character_id`),
  KEY `idx_character_id` (`character_id`)
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8mb4 COMMENT='用户奇遇记录表';

-- ----------------------------
-- 表结构: `user_dungeon_progress`
-- ----------------------------
CREATE TABLE `user_dungeon_progress` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `character_id` bigint(20) NOT NULL COMMENT '角色ID',
  `dungeon_id` int(11) NOT NULL COMMENT '副本ID',
  `completion_count` int(11) DEFAULT '0' COMMENT '完成次数',
  `best_completion_time` int(11) DEFAULT NULL COMMENT '最佳完成时间',
  `highest_difficulty` enum('normal','heroic','mythic') DEFAULT 'normal' COMMENT '最高难度',
  `daily_entries` int(11) DEFAULT '0' COMMENT '今日进入次数',
  `weekly_entries` int(11) DEFAULT '0' COMMENT '本周进入次数',
  `last_entry_date` date DEFAULT NULL COMMENT '最后进入日期',
  `first_completed_at` timestamp NULL DEFAULT NULL COMMENT '首次完成时间',
  `last_played_at` timestamp NULL DEFAULT NULL COMMENT '最后游玩时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_character_dungeon` (`character_id`,`dungeon_id`),
  KEY `dungeon_id` (`dungeon_id`),
  CONSTRAINT `user_dungeon_progress_ibfk_1` FOREIGN KEY (`character_id`) REFERENCES `characters` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_dungeon_progress_ibfk_2` FOREIGN KEY (`dungeon_id`) REFERENCES `dungeons` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户副本进度表';

-- ----------------------------
-- 表结构: `user_inventories`
-- ----------------------------
CREATE TABLE `user_inventories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `character_id` bigint(20) NOT NULL COMMENT '角色ID',
  `item_id` int(11) NOT NULL COMMENT '物品ID',
  `item_type` enum('weapon','equipment','armor','accessory','consumable','material','spirit','special') NOT NULL COMMENT '物品类型',
  `sort_weight` int(11) NOT NULL DEFAULT '0' COMMENT '背包物品排序权重',
  `quantity` int(11) DEFAULT '1' COMMENT '数量',
  `current_durability` int(11) DEFAULT NULL COMMENT '当前耐久度',
  `max_durability` int(11) DEFAULT NULL COMMENT '最大耐久度',
  `enhancement_level` int(11) DEFAULT '0' COMMENT '强化等级',
  `socket_gems` json DEFAULT NULL COMMENT '镶嵌宝石',
  `custom_attributes` json DEFAULT NULL COMMENT '装备属性',
  `bind_status` enum('unbound','bound','account_bound') DEFAULT 'unbound' COMMENT '绑定状态',
  `obtained_time` timestamp NULL DEFAULT NULL COMMENT '获得时间',
  `obtained_source` varchar(100) DEFAULT NULL COMMENT '获得来源',
  `expires_at` timestamp NULL DEFAULT NULL COMMENT '过期时间',
  `is_locked` tinyint(1) DEFAULT '0' COMMENT '是否锁定',
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `item_id` (`item_id`),
  KEY `idx_character_item` (`character_id`,`item_id`),
  KEY `idx_character_item_type_bind` (`character_id`,`item_id`,`item_type`,`bind_status`),
  CONSTRAINT `user_inventories_ibfk_1` FOREIGN KEY (`character_id`) REFERENCES `characters` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_inventories_ibfk_2` FOREIGN KEY (`item_id`) REFERENCES `game_items` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1517 DEFAULT CHARSET=utf8mb4 COMMENT='用户背包表';

-- ----------------------------
-- 表结构: `user_learned_recipes`
-- ----------------------------
CREATE TABLE `user_learned_recipes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `character_id` bigint(20) NOT NULL COMMENT '角色ID',
  `recipe_id` int(11) NOT NULL COMMENT '丹方ID',
  `learned_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '学会时间',
  `learned_source` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '获得来源',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_character_recipe` (`character_id`,`recipe_id`),
  KEY `recipe_id` (`recipe_id`),
  CONSTRAINT `user_learned_recipes_ibfk_1` FOREIGN KEY (`character_id`) REFERENCES `characters` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_learned_recipes_ibfk_2` FOREIGN KEY (`recipe_id`) REFERENCES `crafting_recipes` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=49 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户已学丹方表';

-- ----------------------------
-- 表结构: `user_map_progress`
-- ----------------------------
CREATE TABLE `user_map_progress` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `character_id` bigint(20) NOT NULL,
  `map_id` int(11) NOT NULL,
  `map_code` varchar(50) DEFAULT NULL,
  `current_stage` int(11) DEFAULT '1',
  `max_stage_reached` int(11) DEFAULT '1',
  `total_battles` int(11) DEFAULT '0',
  `total_victories` int(11) DEFAULT '0',
  `best_clear_time` int(11) DEFAULT '0',
  `daily_entries` int(11) DEFAULT '0',
  `last_entry_date` date DEFAULT NULL,
  `first_completed_at` timestamp NULL DEFAULT NULL,
  `last_played_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_character_map` (`character_id`,`map_id`),
  KEY `idx_character` (`character_id`),
  KEY `idx_map` (`map_id`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- 表结构: `user_purchases`
-- ----------------------------
CREATE TABLE `user_purchases` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `item_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `item_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'shop',
  `purchase_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `item_id` (`item_id`),
  CONSTRAINT `user_purchases_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1892 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户购买记录表';

-- ----------------------------
-- 表结构: `user_resources_log`
-- ----------------------------
CREATE TABLE `user_resources_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `resource_type` enum('spirit_stones','gold') NOT NULL COMMENT '资源类型',
  `amount` int(11) NOT NULL COMMENT '变动数量',
  `balance_after` int(11) NOT NULL COMMENT '变动后余额',
  `change_type` enum('system','battle','trade','task','merge','other') NOT NULL COMMENT '变动类型',
  `description` text COMMENT '变动说明',
  `related_id` int(11) DEFAULT NULL COMMENT '关联ID(战斗ID/交易ID等)',
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `user_resources_log_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='用户资源变动记录表';

-- ----------------------------
-- 表结构: `user_server_roles`
-- ----------------------------
CREATE TABLE `user_server_roles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `server_id` int(11) NOT NULL COMMENT '分区ID',
  `character_id` bigint(20) NOT NULL COMMENT '角色ID',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否激活',
  `last_played_at` timestamp NULL DEFAULT NULL COMMENT '最后游玩时间',
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_server` (`user_id`,`server_id`),
  KEY `server_id` (`server_id`),
  KEY `idx_character` (`character_id`),
  CONSTRAINT `user_server_roles_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_server_roles_ibfk_2` FOREIGN KEY (`server_id`) REFERENCES `game_servers` (`id`),
  CONSTRAINT `user_server_roles_ibfk_3` FOREIGN KEY (`character_id`) REFERENCES `characters` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户分区角色表';

-- ----------------------------
-- 表结构: `user_spirits`
-- ----------------------------
CREATE TABLE `user_spirits` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `character_id` bigint(20) NOT NULL COMMENT '角色ID',
  `spirit_id` int(11) NOT NULL COMMENT '器灵ID',
  `current_level` int(11) DEFAULT '1' COMMENT '当前等级',
  `current_exp` int(11) DEFAULT '0' COMMENT '当前经验',
  `awakening_level` int(11) DEFAULT '0' COMMENT '觉醒等级',
  `skill_points` int(11) DEFAULT '0' COMMENT '技能点数',
  `is_active` tinyint(1) DEFAULT '0' COMMENT '是否激活',
  `bound_item_id` int(11) DEFAULT NULL COMMENT '绑定装备ID',
  `obtained_time` timestamp NULL DEFAULT NULL COMMENT '获得时间',
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `character_id` (`character_id`),
  KEY `spirit_id` (`spirit_id`),
  KEY `bound_item_id` (`bound_item_id`),
  CONSTRAINT `user_spirits_ibfk_1` FOREIGN KEY (`character_id`) REFERENCES `characters` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_spirits_ibfk_2` FOREIGN KEY (`spirit_id`) REFERENCES `spirit_souls` (`id`),
  CONSTRAINT `user_spirits_ibfk_3` FOREIGN KEY (`bound_item_id`) REFERENCES `user_inventories` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户器灵表';

-- ----------------------------
-- 表结构: `users`
-- ----------------------------
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password_hash` varchar(255) NOT NULL COMMENT '密码哈希',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `avatar_frame` varchar(100) DEFAULT NULL COMMENT '头像外框',
  `spirit_stones` bigint(20) DEFAULT '0' COMMENT '灵石',
  `gold` bigint(20) DEFAULT '1000' COMMENT '金币',
  `status` enum('active','banned','pending') DEFAULT 'active' COMMENT '账户状态',
  `last_login_time` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `login_count` int(11) DEFAULT '0' COMMENT '登录次数',
  `registration_ip` varchar(45) DEFAULT NULL COMMENT '注册IP',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `rename_count` int(11) DEFAULT '0' COMMENT '更名次数',
  `last_rename_time` timestamp NULL DEFAULT NULL COMMENT '最后更名时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8mb4 COMMENT='用户主表';

