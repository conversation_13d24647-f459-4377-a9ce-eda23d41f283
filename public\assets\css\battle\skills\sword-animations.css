/**
 * 剑类技能动画样式
 * 包含飞剑术、万剑诀、巨剑术等所有剑类技能的动画效果
 */

/* 🗡️ 飞剑动画样式 */
.flying-sword {
    position: absolute;
    width: min(40px, 8vw);
    height: min(80px, 16vw);
    transform: translate(-50%, -50%) rotate(0deg);
    transform-origin: center;
    opacity: 0;
    -webkit-filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.8));
    filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.8));
    pointer-events: none;
    z-index: 200;
    
    /* 支持背景图片显示 */
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

/* 飞剑武器图片样式 */
.flying-sword .weapon-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    -webkit-filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.8));
    filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.8));
}

/* 🔥 原始完整动画关键帧 */

/* 我方飞剑原地旋转动画 */
@keyframes sword-spin-player {
    0% {
        transform: translate(-50%, -50%) rotate(180deg);
        opacity: 0;
    }
    20% {
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) rotate(540deg);
        opacity: 1;
    }
}

/* 敌方飞剑原地旋转动画 */
@keyframes sword-spin-enemy {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
        opacity: 0;
    }
    20% {
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) rotate(360deg);
        opacity: 1;
    }
}

/* 我方飞剑飞向目标动画 - 加速度效果 */
@keyframes fly-sword-to-target-player {
    0% {
        transform: translate(-50%, -50%) rotate(540deg);
        opacity: 1;
    }
    /* 30%时间飞行20%距离 - 慢速启动 */
    30% {
        transform: translate(
            calc((var(--targetCenterX) - var(--startX)) * 0.2 - 50%),
            calc((var(--targetCenterY) - var(--startY)) * 0.2 - 50%)
        ) rotate(540deg);
        opacity: 1;
    }
    /* 接下来40%时间飞行50%距离 - 中等速度 */
    70% {
        transform: translate(
            calc((var(--targetCenterX) - var(--startX)) * 0.7 - 50%),
            calc((var(--targetCenterY) - var(--startY)) * 0.7 - 50%)
        ) rotate(540deg);
        opacity: 1;
    }
    /* 最后30%时间飞行剩余30%距离 - 高速冲刺 */
    100% {
        transform: translate(
            calc(var(--targetCenterX) - var(--startX) - 50%),
            calc(var(--targetCenterY) - var(--startY) - 50%)
        ) rotate(540deg);
        opacity: 1;
    }
}

/* 敌方飞剑飞向目标动画 - 加速度效果 */
@keyframes fly-sword-to-target-enemy {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
        opacity: 1;
    }
    /* 30%时间飞行20%距离 - 慢速启动 */
    30% {
        transform: translate(
            calc((var(--targetCenterX) - var(--startX)) * 0.2 - 50%),
            calc((var(--targetCenterY) - var(--startY)) * 0.2 - 50%)
        ) rotate(0deg);
        opacity: 1;
    }
    /* 接下来40%时间飞行50%距离 - 中等速度 */
    70% {
        transform: translate(
            calc((var(--targetCenterX) - var(--startX)) * 0.7 - 50%),
            calc((var(--targetCenterY) - var(--startY)) * 0.7 - 50%)
        ) rotate(0deg);
        opacity: 1;
    }
    /* 最后30%时间飞行剩余30%距离 - 高速冲刺 */
    100% {
        transform: translate(
            calc(var(--targetCenterX) - var(--startX) - 50%),
            calc(var(--targetCenterY) - var(--startY) - 50%)
        ) rotate(0deg);
        opacity: 1;
    }
}

/* 我方飞剑穿透动画 */
@keyframes fly-sword-penetrate-player {
    0% {
        transform: translate(
            calc(var(--targetCenterX) - var(--startX) - 50%),
            calc(var(--targetCenterY) - var(--startY) - 50%)
        ) rotate(540deg);
        opacity: 1;
    }
    100% {
        transform: translate(
            calc(var(--targetCenterX) - var(--startX) - 50%),
            calc(var(--finalY) - var(--startY) - 50%)
        ) rotate(540deg);
        opacity: 0;
    }
}

/* 敌方飞剑穿透动画 */
@keyframes fly-sword-penetrate-enemy {
    0% {
        transform: translate(
            calc(var(--targetCenterX) - var(--startX) - 50%),
            calc(var(--targetCenterY) - var(--startY) - 50%)
        ) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translate(
            calc(var(--targetCenterX) - var(--startX) - 50%),
            calc(var(--finalY) - var(--startY) - 50%)
        ) rotate(0deg);
        opacity: 0;
    }
}

/* 🌟 万剑诀动画样式 */
.skill-sword {
    position: absolute;
    width: min(40px, 8vw);
    height: min(80px, 16vw);
    transform: translate(-50%, -50%);
    transform-origin: center;
    opacity: 0;
    -webkit-filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.8));
    filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.8));
    pointer-events: none;
    z-index: 200;
    
    /* 支持背景图片显示 */
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

/* 万剑诀武器图片样式 */
.skill-sword .weapon-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    -webkit-filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.8));
    filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.8));
}

/* 万剑诀剑生成和旋转动画 - 完全对标原版 */
@keyframes skill-sword-appear {
    0% {
        transform-origin: center 25%;
        transform: translate(
            calc(var(--initialX) + cos(var(--swordAngle)) * calc(var(--swordRadius) * 0.2) - 50%),
            calc(var(--initialY) + sin(var(--swordAngle)) * calc(var(--swordRadius) * 0.2) - 50%)
        ) scale(0) rotate(180deg);
        opacity: 0;
    }
    20% {
        transform-origin: center 25%;
        transform: translate(
            calc(var(--initialX) + cos(calc(var(--swordAngle) + 90deg)) * calc(var(--swordRadius) * 0.4) - 50%),
            calc(var(--initialY) + sin(calc(var(--swordAngle) + 90deg)) * calc(var(--swordRadius) * 0.4) - 50%)
        ) scale(0.6) rotate(720deg);
        opacity: 0.4;
    }
    40% {
        transform-origin: center 25%;
        transform: translate(
            calc(var(--initialX) + cos(calc(var(--swordAngle) + 180deg)) * calc(var(--swordRadius) * 0.6) - 50%),
            calc(var(--initialY) + sin(calc(var(--swordAngle) + 180deg)) * calc(var(--swordRadius) * 0.6) - 50%)
        ) scale(0.8) rotate(1260deg);
        opacity: 0.6;
    }
    60% {
        transform-origin: center 25%;
        transform: translate(
            calc(var(--initialX) + cos(calc(var(--swordAngle) + 270deg)) * calc(var(--swordRadius) * 0.8) - 50%),
            calc(var(--initialY) + sin(calc(var(--swordAngle) + 270deg)) * calc(var(--swordRadius) * 0.8) - 50%)
        ) scale(0.9) rotate(1800deg);
        opacity: 0.8;
    }
    80% {
        transform-origin: center 25%;
        transform: translate(
            calc(var(--initialX) + cos(calc(var(--swordAngle) + 330deg)) * calc(var(--swordRadius) * 0.9) - 50%),
            calc(var(--initialY) + sin(calc(var(--swordAngle) + 330deg)) * calc(var(--swordRadius) * 0.9) - 50%)
        ) scale(0.95) rotate(2340deg);
        opacity: 0.9;
    }
    100% {
        transform-origin: center 25%;
        transform: translate(
            calc(var(--initialX) + cos(calc(var(--swordAngle) + 360deg)) * var(--swordRadius) - 50%),
            calc(var(--initialY) + sin(calc(var(--swordAngle) + 360deg)) * var(--swordRadius) - 50%)
        ) scale(1) rotate(1980deg); /* 1980度 = 5.5圈，最终停在朝下的位置 */
        opacity: 1;
    }
}

/* 万剑诀剑攻击动画 - 完全对标原版 */
@keyframes skill-sword-attack {
    0% {
        transform-origin: center 25%;
        transform: translate(
            calc(var(--initialX) + cos(calc(var(--swordAngle) + 360deg)) * var(--swordRadius) - 50%),
            calc(var(--initialY) + sin(calc(var(--swordAngle) + 360deg)) * var(--swordRadius) - 50%)
        ) scale(1) rotate(1980deg);
        opacity: 1;
    }
    /* 35%时间飞行25%距离 - 慢速启动 */
    35% {
        transform-origin: center 25%;
        transform: translate(
            calc(var(--initialX) + cos(calc(var(--swordAngle) + 360deg)) * var(--swordRadius) + (var(--targetX) - var(--initialX) - cos(calc(var(--swordAngle) + 360deg)) * var(--swordRadius)) * 0.25 - 50%),
            calc(var(--initialY) + sin(calc(var(--swordAngle) + 360deg)) * var(--swordRadius) + (var(--attackTargetY) - var(--initialY) - sin(calc(var(--swordAngle) + 360deg)) * var(--swordRadius)) * 0.25 - 50%)
        ) scale(1) rotate(1980deg);
        opacity: 1;
    }
    /* 接下来35%时间飞行45%距离 - 中等速度 */
    70% {
        transform-origin: center 25%;
        transform: translate(
            calc(var(--initialX) + cos(calc(var(--swordAngle) + 360deg)) * var(--swordRadius) + (var(--targetX) - var(--initialX) - cos(calc(var(--swordAngle) + 360deg)) * var(--swordRadius)) * 0.7 - 50%),
            calc(var(--initialY) + sin(calc(var(--swordAngle) + 360deg)) * var(--swordRadius) + (var(--attackTargetY) - var(--initialY) - sin(calc(var(--swordAngle) + 360deg)) * var(--swordRadius)) * 0.7 - 50%)
        ) scale(1) rotate(1980deg);
        opacity: 1;
    }
    /* 最后30%时间飞行剩余30%距离 - 高速冲刺 */
    100% {
        transform-origin: center 25%;
        transform: translate(calc(var(--targetX) - 50%), calc(var(--attackTargetY) - 50%)) rotate(1980deg);
        opacity: 0; /* 对标原版：动画结尾opacity为0 */
    }
}

/* 万剑诀剑穿透动画 - 完全对标原版 */
@keyframes skill-sword-penetrate {
    0% {
        transform-origin: center 25%;
        transform: translate(calc(var(--targetX) - 50%), calc(var(--attackTargetY) - 50%)) rotate(1980deg);
        opacity: 1;
    }
    100% {
        transform-origin: center 25%;
        transform: translate(calc(var(--targetX) - 50%), calc(var(--finalTargetY) - 50%)) rotate(1980deg);
        opacity: 0;
    }
}

/* 🌟 万剑诀特效样式 */
.sword-formation-container {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 190;
}

.sword-formation-energy {
    position: absolute;
    width: 120px;
    height: 120px;
    background: radial-gradient(circle,
        rgba(255, 255, 255, 0.3) 0%,
        rgba(200, 220, 255, 0.2) 30%,
        rgba(150, 180, 255, 0.1) 60%,
        transparent 100%
    );
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: formation-energy 1.2s ease-out forwards;
    -webkit-filter: blur(2px);
    filter: blur(2px);
}

@keyframes formation-energy {
    0% {
        transform: translate(-50%, -50%) scale(0) rotate(0deg);
        opacity: 0;
    }
    30% {
        transform: translate(-50%, -50%) scale(1) rotate(120deg);
        opacity: 0.6;
    }
    60% {
        transform: translate(-50%, -50%) scale(1.5) rotate(240deg);
        opacity: 0.4;
    }
    100% {
        transform: translate(-50%, -50%) scale(2) rotate(360deg);
        opacity: 0;
    }
}

.sword-formation-circle {
    position: absolute;
    border: 2px solid rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: formation-circle 1.2s ease-out forwards;
    -webkit-filter: drop-shadow(0 0 10px rgba(200, 220, 255, 0.8));
    filter: drop-shadow(0 0 10px rgba(200, 220, 255, 0.8));
}

@keyframes formation-circle {
    0% {
        transform: translate(-50%, -50%) scale(0) rotate(0deg);
        opacity: 0;
        border-color: rgba(255, 255, 255, 0.8);
    }
    30% {
        transform: translate(-50%, -50%) scale(1.2) rotate(180deg);
        opacity: 0.8;
        border-color: rgba(200, 220, 255, 0.7);
    }
    60% {
        transform: translate(-50%, -50%) scale(1.5) rotate(360deg);
        opacity: 0.6;
        border-color: rgba(150, 180, 255, 0.5);
    }
    100% {
        transform: translate(-50%, -50%) scale(2) rotate(540deg);
        opacity: 0;
        border-color: rgba(100, 150, 255, 0.2);
    }
}

.sword-formation-rune {
    position: absolute;
    width: 8px;
    height: 8px;
    background: radial-gradient(circle, rgba(255, 255, 255, 1) 0%, rgba(200, 220, 255, 0.8) 100%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: formation-rune 1.2s ease-out forwards;
    -webkit-filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.8));
    filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.8));
}

@keyframes formation-rune {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0;
    }
    20% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0.8;
    }
    40% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
    70% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0;
    }
}

/* 🗡️ 巨剑术动画样式 */
.giant-sword-container {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 200;
}

.giant-mini-sword {
    position: absolute;
    width: min(40px, 8vw);
    height: min(80px, 16vw);
    opacity: 0;
    transform: translate(-50%, -50%);
    transform-origin: center bottom;
    animation: mini-sword-spawn 0.4s ease-out forwards;
    -webkit-filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.8));
    filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.8));
    
    /* 支持背景图片显示 */
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

/* 巨剑小剑武器图片样式 */
.giant-mini-sword .weapon-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    -webkit-filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.8));
    filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.8));
}

/* 小剑生成动画 */
@keyframes mini-sword-spawn {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0) rotate(0deg);
    }
    50% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.2) rotate(180deg);
    }
    100% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1) rotate(360deg);
    }
}

/* 小剑汇聚动画 - 完全对标原版 */
@keyframes sword-merge {
    0% {
        transform: translate(-50%, -50%) rotate(0deg) scale(1);
        opacity: 1;
        -webkit-filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.8));
        filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.8));
    }
    25% {
        transform: translate(-50%, -50%) rotate(0deg) translate(
            calc((var(--centerX) - var(--initialX)) * 0.3 * 1px),
            calc((var(--centerY) - var(--initialY)) * 0.3 * 1px)
        ) scale(0.9);
        opacity: 0.9;
        -webkit-filter: drop-shadow(0 0 12px rgba(255, 255, 255, 1)) drop-shadow(0 0 20px rgba(200, 220, 255, 0.8));
        filter: drop-shadow(0 0 12px rgba(255, 255, 255, 1)) drop-shadow(0 0 20px rgba(200, 220, 255, 0.8));
    }
    50% {
        transform: translate(-50%, -50%) rotate(0deg) translate(
            calc((var(--centerX) - var(--initialX)) * 0.7 * 1px),
            calc((var(--centerY) - var(--initialY)) * 0.7 * 1px)
        ) scale(0.7);
        opacity: 0.8;
        -webkit-filter: drop-shadow(0 0 15px rgba(255, 255, 255, 1)) drop-shadow(0 0 25px rgba(200, 220, 255, 0.9));
        filter: drop-shadow(0 0 15px rgba(255, 255, 255, 1)) drop-shadow(0 0 25px rgba(200, 220, 255, 0.9));
    }
    75% {
        transform: translate(-50%, -50%) rotate(0deg) translate(
            calc((var(--centerX) - var(--initialX)) * 0.9 * 1px),
            calc((var(--centerY) - var(--initialY)) * 0.9 * 1px)
        ) scale(0.4);
        opacity: 0.6;
        -webkit-filter: drop-shadow(0 0 18px rgba(255, 255, 255, 1)) drop-shadow(0 0 30px rgba(200, 220, 255, 1));
        filter: drop-shadow(0 0 18px rgba(255, 255, 255, 1)) drop-shadow(0 0 30px rgba(200, 220, 255, 1));
    }
    100% {
        transform: translate(-50%, -50%) rotate(0deg) translate(
            calc((var(--centerX) - var(--initialX)) * 1px),
            calc((var(--centerY) - var(--initialY)) * 1px)
        ) scale(0);
        opacity: 0;
        -webkit-filter: drop-shadow(0 0 20px rgba(255, 255, 255, 1)) drop-shadow(0 0 35px rgba(200, 220, 255, 1));
        filter: drop-shadow(0 0 20px rgba(255, 255, 255, 1)) drop-shadow(0 0 35px rgba(200, 220, 255, 1));
    }
}

/* 巨剑基础样式 */
.giant-sword-simple {
    position: absolute;
    width: 80px;
    height: 160px;
    transform: translate(-50%, -50%);
    transform-origin: center center;
    -webkit-filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.8));
    filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.8));
    pointer-events: none;
    z-index: 201;
    
    /* 支持武器图片背景 */
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

/* 巨剑内部武器图片样式 */
.giant-sword-simple .weapon-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    -webkit-filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.8));
    filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.8));
}

/* 巨剑剑柄效果 - 隐藏，因为武器图片已包含完整形状 */
.giant-sword-simple::before {
    display: none;
}

/* 巨剑护手效果 - 隐藏，因为武器图片已包含完整形状 */
.giant-sword-simple::after {
    display: none;
}

/* 巨剑形成动画 */
.giant-sword-forming {
    animation: giant-sword-scale-up 1.0s ease-out forwards;
}

@keyframes giant-sword-scale-up {
    0% {
        transform: translate(-50%, -50%) scale(0.05);
        opacity: 0;
        -webkit-filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.3));
        filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.3)) 
                drop-shadow(0 0 6px rgba(200, 220, 255, 0.2));
    }
    20% {
        transform: translate(-50%, -50%) scale(0.3);
        opacity: 0.4;
        -webkit-filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.5)); 
        filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.5)) 
                drop-shadow(0 0 12px rgba(200, 220, 255, 0.3));
    }
    40% {
        transform: translate(-50%, -50%) scale(0.5);
        opacity: 0.7;
        -webkit-filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.6)); 
        filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.6)) 
                drop-shadow(0 0 15px rgba(200, 220, 255, 0.4));
    }
    70% {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 0.9;
        -webkit-filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.8)); 
        filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.8)) 
                drop-shadow(0 0 20px rgba(200, 220, 255, 0.6));
    }
    85% {
        transform: translate(-50%, -50%) scale(0.9);
        opacity: 0.95;
        -webkit-filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.9)); 
        filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.9)) 
                drop-shadow(0 0 25px rgba(200, 220, 255, 0.7));
    }
    100% {
        transform: translate(-50%, -50%) scale(1.0);
        opacity: 1;
        -webkit-filter: drop-shadow(0 0 25px rgba(255, 255, 255, 1)); 
        filter: drop-shadow(0 0 25px rgba(255, 255, 255, 1)) 
                drop-shadow(0 0 30px rgba(200, 220, 255, 0.8));
    }
}

/* 巨剑斩击动画 - 完全对标原版复杂轨迹 */
@keyframes giant-sword-slash {
    0% {
        transform: translate(-50%, -50%) scale(1.0) rotate(0deg);
        opacity: 1;
        -webkit-filter: drop-shadow(0 0 25px rgba(255, 255, 255, 1)) drop-shadow(0 0 45px rgba(200, 220, 255, 0.8));
        filter: drop-shadow(0 0 25px rgba(255, 255, 255, 1)) drop-shadow(0 0 45px rgba(200, 220, 255, 0.8));
    }
    10% {
        transform: translate(
            calc(-50% + var(--curveX1) * 0.5),
            calc(-50% + var(--curveY1) * 0.5)
        ) scale(1.2) rotate(3780deg);
        opacity: 1;
        -webkit-filter: drop-shadow(0 0 28px rgba(255, 255, 255, 1)) drop-shadow(0 0 48px rgba(200, 220, 255, 0.85)) drop-shadow(0 0 70px rgba(255, 255, 255, 0.5));
        filter: drop-shadow(0 0 28px rgba(255, 255, 255, 1)) drop-shadow(0 0 48px rgba(200, 220, 255, 0.85)) drop-shadow(0 0 70px rgba(255, 255, 255, 0.5));
    }
    20% {
        transform: translate(
            calc(-50% + var(--targetX) * 0.2 + var(--curveX1) * 0.8),
            calc(-50% + var(--targetY) * 0.2 + var(--curveY1) * 0.8)
        ) scale(1.2) rotate(6615deg);
        opacity: 1;
        -webkit-filter: drop-shadow(0 0 30px rgba(255, 255, 255, 1)) drop-shadow(0 0 50px rgba(200, 220, 255, 0.9)) drop-shadow(0 0 75px rgba(255, 255, 255, 0.6));
        filter: drop-shadow(0 0 30px rgba(255, 255, 255, 1)) drop-shadow(0 0 50px rgba(200, 220, 255, 0.9)) drop-shadow(0 0 75px rgba(255, 255, 255, 0.6));
    }
    30% {
        transform: translate(
            calc(-50% + var(--targetX) * 0.4 + var(--curveX1) * 1.0),
            calc(-50% + var(--targetY) * 0.4 + var(--curveY1) * 1.0)
        ) scale(1.1) rotate(9450deg);
        opacity: 1;
        -webkit-filter: drop-shadow(0 0 32px rgba(255, 255, 255, 1)) drop-shadow(0 0 52px rgba(200, 220, 255, 0.9)) drop-shadow(0 0 80px rgba(255, 255, 255, 0.6));
        filter: drop-shadow(0 0 32px rgba(255, 255, 255, 1)) drop-shadow(0 0 52px rgba(200, 220, 255, 0.9)) drop-shadow(0 0 80px rgba(255, 255, 255, 0.6));
    }
    40% {
        transform: translate(
            calc(-50% + var(--targetX) * 0.6 + var(--curveX2) * 1.1),
            calc(-50% + var(--targetY) * 0.6 + var(--curveY2) * 1.1)
        ) scale(1.1) rotate(5670deg);
        opacity: 1;
        -webkit-filter: drop-shadow(0 0 34px rgba(255, 255, 255, 1)) drop-shadow(0 0 54px rgba(200, 220, 255, 0.95)) drop-shadow(0 0 85px rgba(255, 255, 255, 0.7));
        filter: drop-shadow(0 0 34px rgba(255, 255, 255, 1)) drop-shadow(0 0 54px rgba(200, 220, 255, 0.95)) drop-shadow(0 0 85px rgba(255, 255, 255, 0.7));
    }
    50% {
        transform: translate(
            calc(-50% + var(--targetX) * 0.75 + var(--curveX2) * 1.0),
            calc(-50% + var(--targetY) * 0.75 + var(--curveY2) * 1.0)
        ) scale(1.0) rotate(7560deg);
        opacity: 1;
        -webkit-filter: drop-shadow(0 0 36px rgba(255, 255, 255, 1)) drop-shadow(0 0 56px rgba(200, 220, 255, 0.95)) drop-shadow(0 0 90px rgba(255, 255, 255, 0.7));
        filter: drop-shadow(0 0 36px rgba(255, 255, 255, 1)) drop-shadow(0 0 56px rgba(200, 220, 255, 0.95)) drop-shadow(0 0 90px rgba(255, 255, 255, 0.7));
    }
    60% {
        transform: translate(
            calc(-50% + var(--targetX) * 0.85 + var(--curveX3) * 0.8),
            calc(-50% + var(--targetY) * 0.85 + var(--curveY3) * 0.8)
        ) scale(1.0) rotate(8505deg);
        opacity: 1;
        -webkit-filter: drop-shadow(0 0 38px rgba(255, 255, 255, 1)) drop-shadow(0 0 58px rgba(200, 220, 255, 0.95)) drop-shadow(0 0 95px rgba(255, 255, 255, 0.8));
        filter: drop-shadow(0 0 38px rgba(255, 255, 255, 1)) drop-shadow(0 0 58px rgba(200, 220, 255, 0.95)) drop-shadow(0 0 95px rgba(255, 255, 255, 0.8));
    }
    70% {
        transform: translate(
            calc(-50% + var(--targetX) * 0.95 + var(--curveX3) * 0.6),
            calc(-50% + var(--targetY) * 0.95 + var(--curveY3) * 0.6)
        ) scale(0.9) rotate(9918deg);
        opacity: 1;
        -webkit-filter: drop-shadow(0 0 40px rgba(255, 255, 255, 1)) drop-shadow(0 0 60px rgba(200, 220, 255, 1)) drop-shadow(0 0 100px rgba(255, 255, 255, 0.8));
        filter: drop-shadow(0 0 40px rgba(255, 255, 255, 1)) drop-shadow(0 0 60px rgba(200, 220, 255, 1)) drop-shadow(0 0 100px rgba(255, 255, 255, 0.8));
    }
    80% {
        transform: translate(
            calc(-50% + var(--targetX) * 1.1),
            calc(-50% + var(--targetY) * 1.1)
        ) scale(0.9) rotate(11340deg);
        opacity: 1;
        -webkit-filter: drop-shadow(0 0 50px rgba(255, 255, 255, 1)) drop-shadow(0 0 80px rgba(200, 220, 255, 1)) drop-shadow(0 0 120px rgba(255, 255, 255, 0.9));
        filter: drop-shadow(0 0 50px rgba(255, 255, 255, 1)) drop-shadow(0 0 80px rgba(200, 220, 255, 1)) drop-shadow(0 0 120px rgba(255, 255, 255, 0.9));
    }
    85% {
        transform: translate(
            calc(-50% + var(--targetX) * 1.15),
            calc(-50% + var(--targetY) * 1.15)
        ) scale(0.8) rotate(12285deg);
        opacity: 0.95;
        -webkit-filter: drop-shadow(0 0 48px rgba(255, 255, 255, 0.95)) drop-shadow(0 0 78px rgba(200, 220, 255, 0.9)) drop-shadow(0 0 115px rgba(255, 255, 255, 0.8));
        filter: drop-shadow(0 0 48px rgba(255, 255, 255, 0.95)) drop-shadow(0 0 78px rgba(200, 220, 255, 0.9)) drop-shadow(0 0 115px rgba(255, 255, 255, 0.8));
    }
    90% {
        transform: translate(
            calc(-50% + var(--targetX) * 1.25),
            calc(-50% + var(--targetY) * 1.25)
        ) scale(0.7) rotate(13230deg);
        opacity: 0.8;
        -webkit-filter: drop-shadow(0 0 45px rgba(255, 255, 255, 0.9)) drop-shadow(0 0 75px rgba(200, 220, 255, 0.8)) drop-shadow(0 0 110px rgba(255, 255, 255, 0.7));
        filter: drop-shadow(0 0 45px rgba(255, 255, 255, 0.9)) drop-shadow(0 0 75px rgba(200, 220, 255, 0.8)) drop-shadow(0 0 110px rgba(255, 255, 255, 0.7));
    }
    95% {
        transform: translate(
            calc(-50% + var(--targetX) * 1.4),
            calc(-50% + var(--targetY) * 1.4)
        ) scale(0.6) rotate(14175deg);
        opacity: 0.5;
        -webkit-filter: drop-shadow(0 0 35px rgba(255, 255, 255, 0.7)) drop-shadow(0 0 60px rgba(200, 220, 255, 0.6)) drop-shadow(0 0 90px rgba(255, 255, 255, 0.5));
        filter: drop-shadow(0 0 35px rgba(255, 255, 255, 0.7)) drop-shadow(0 0 60px rgba(200, 220, 255, 0.6)) drop-shadow(0 0 90px rgba(255, 255, 255, 0.5));
    }
    100% {
        transform: translate(
            calc(-50% + var(--targetX) * 1.6),
            calc(-50% + var(--targetY) * 1.6)
        ) scale(0.5) rotate(15120deg);
        opacity: 0;
        -webkit-filter: drop-shadow(0 0 25px rgba(255, 255, 255, 0.4)) drop-shadow(0 0 45px rgba(200, 220, 255, 0.3)) drop-shadow(0 0 70px rgba(255, 255, 255, 0.2));
        filter: drop-shadow(0 0 25px rgba(255, 255, 255, 0.4)) drop-shadow(0 0 45px rgba(200, 220, 255, 0.3)) drop-shadow(0 0 70px rgba(255, 255, 255, 0.2));
    }
}

/* 击中特效样式 */
.sword-hit-flash {
    position: absolute;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: radial-gradient(circle,
        rgba(255, 255, 255, 1) 0%,
        rgba(200, 220, 255, 0.8) 30%,
        rgba(150, 180, 255, 0.4) 60%,
        transparent 100%
    );
    transform: translate(-50%, -50%);
    animation: sword-hit-flash-expand 0.3s ease-out forwards;
    -webkit-filter: blur(1px);
    filter: blur(1px);
    pointer-events: none;
    z-index: 300;
}

@keyframes sword-hit-flash-expand {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
    }
    50% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.8;
    }
    100% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0;
    }
}

.sword-hit-particle {
    position: absolute;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255, 255, 255, 1) 0%, rgba(200, 220, 255, 0.8) 100%);
    animation: sword-hit-particle-scatter 0.6s ease-out forwards;
    -webkit-filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.8));
    filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.8));
    pointer-events: none;
    z-index: 295;
}

@keyframes sword-hit-particle-scatter {
    0% {
        transform: translate(0, 0) scale(1);
        opacity: 1;
    }
    100% {
        transform: translate(var(--particleX), var(--particleY)) scale(0);
        opacity: 0;
    }
}

.sword-hit-shockwave {
    position: absolute;
    width: 80px;
    height: 80px;
    border: 2px solid rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: sword-hit-shockwave-expand 0.5s ease-out forwards;
    -webkit-filter: drop-shadow(0 0 10px rgba(200, 220, 255, 0.6));
    filter: drop-shadow(0 0 10px rgba(200, 220, 255, 0.6));
    pointer-events: none;
    z-index: 290;
}

@keyframes sword-hit-shockwave-expand {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
        border-color: rgba(255, 255, 255, 0.9);
    }
    50% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0.6;
        border-color: rgba(200, 220, 255, 0.7);
    }
    100% {
        transform: translate(-50%, -50%) scale(3);
        opacity: 0;
        border-color: rgba(150, 180, 255, 0.3);
    }
}

/* 角色受击动画 */
@keyframes sword-struck {
    0%, 100% {
        transform: translateX(0) translateY(0);
        -webkit-filter: brightness(1);
        filter: brightness(1);
    }
    10% {
        transform: translateX(-5px) translateY(-3px);
        -webkit-filter: brightness(1.8) saturate(1.5);
        filter: brightness(1.8) saturate(1.5);
    }
    20% {
        transform: translateX(4px) translateY(2px);
        -webkit-filter: brightness(1.4) saturate(1.2);
        filter: brightness(1.4) saturate(1.2);
    }
    30% {
        transform: translateX(-4px) translateY(4px);
        -webkit-filter: brightness(2.0) saturate(1.8);
        filter: brightness(2.0) saturate(1.8);
    }
    40% {
        transform: translateX(5px) translateY(-2px);
        -webkit-filter: brightness(1.6) saturate(1.4);
        filter: brightness(1.6) saturate(1.4);
    }
    50% {
        transform: translateX(-3px) translateY(-5px);
        -webkit-filter: brightness(1.9) saturate(1.7);
        filter: brightness(1.9) saturate(1.7);
    }
    60% {
        transform: translateX(3px) translateY(3px);
        -webkit-filter: brightness(1.3) saturate(1.1);
        filter: brightness(1.3) saturate(1.1);
    }
    70% {
        transform: translateX(-2px) translateY(1px);
        -webkit-filter: brightness(1.5) saturate(1.3);
        filter: brightness(1.5) saturate(1.3);
    }
    80% {
        transform: translateX(1px) translateY(-1px);
        -webkit-filter: brightness(1.2) saturate(1.05);
        filter: brightness(1.2) saturate(1.05);
    }
    90% {
        transform: translateX(-1px) translateY(0px);
        -webkit-filter: brightness(1.1) saturate(1.02);
        filter: brightness(1.1) saturate(1.02);
    }
}

@keyframes sword-hit-shake {
    0%, 100% {
        transform: translateX(0) translateY(0);
        -webkit-filter: brightness(1);
        filter: brightness(1);
    }
    10% {
        transform: translateX(-4px) translateY(-3px);
        -webkit-filter: brightness(1.5);
        filter: brightness(1.5);
    }
    20% {
        transform: translateX(3px) translateY(2px);
        -webkit-filter: brightness(1.2);
        filter: brightness(1.2);
    }
    30% {
        transform: translateX(-3px) translateY(4px);
        -webkit-filter: brightness(1.8);
        filter: brightness(1.8);
    }
    40% {
        transform: translateX(4px) translateY(-2px);
        -webkit-filter: brightness(1.3);
        filter: brightness(1.3);
    }
    50% {
        transform: translateX(-2px) translateY(-4px);
        -webkit-filter: brightness(1.6);
        filter: brightness(1.6);
    }
    60% {
        transform: translateX(3px) translateY(3px);
        -webkit-filter: brightness(1.2);
        filter: brightness(1.2);
    }
    70% {
        transform: translateX(-1px) translateY(1px);
        -webkit-filter: brightness(1.4);
        filter: brightness(1.4);
    }
    80% {
        transform: translateX(1px) translateY(-1px);
        -webkit-filter: brightness(1.1);
        filter: brightness(1.1);
    }
    90% {
        transform: translateX(-1px) translateY(0px);
        -webkit-filter: brightness(1.1);
        filter: brightness(1.1);
    }
}

/* 移动端适配 */
@media (max-width: 480px) {
    .flying-sword {
        width: min(30px, 10vw);
        height: min(60px, 20vw);
    }
    
    .skill-sword {
        width: min(30px, 10vw);
        height: min(60px, 20vw);
    }
    
    .giant-mini-sword {
        width: min(30px, 10vw);
        height: min(60px, 20vw);
    }
    
    .giant-sword-simple {
        width: 60px;
        height: 120px;
    }
    
    .sword-formation-energy {
        width: 80px;
        height: 80px;
    }
} 