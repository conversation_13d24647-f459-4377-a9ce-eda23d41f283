.main-container {
    padding: 10px 10px 80px 10px;
    height: 100vh;
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
}

.header {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
    border-radius: 18px;
    padding: 15px;
    margin-bottom: 15px;
    border: 2px solid rgba(212, 175, 55, 0.4);
    backdrop-filter: blur(15px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    text-align: center;
}

.header h1 {
    font-size: 24px;
    color: #d4af37;
    text-shadow: 0 0 10px rgba(212, 175, 55, 0.5);
    margin-bottom: 5px;
}

.header p {
    font-size: 14px;
    color: #bdc3c7;
    opacity: 0.8;
}

.settings-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
    flex: 1;
}

.setting-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
    border-radius: 15px;
    padding: 20px;
    border: 2px solid rgba(212, 175, 55, 0.3);
    backdrop-filter: blur(10px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    cursor: pointer;
}

.setting-card:hover {
    border-color: rgba(212, 175, 55, 0.6);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
    transform: translateY(-2px);
}

.setting-card.disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.setting-card.disabled:hover {
    transform: none;
    border-color: rgba(212, 175, 55, 0.3);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.setting-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
}

.setting-title {
    display: flex;
    align-items: center;
    gap: 10px;
}

.setting-icon {
    font-size: 20px;
    color: #d4af37;
}

.setting-name {
    font-size: 18px;
    font-weight: bold;
    color: #fff;
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
}

.setting-arrow {
    font-size: 16px;
    color: #bdc3c7;
    opacity: 0.7;
}

.setting-description {
    font-size: 14px;
    color: #bdc3c7;
    line-height: 1.4;
    margin-left: 30px;
}

/* 开关样式 */
.switch {
    position: relative;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #666;
    transition: .4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #d4af37;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* 弹窗样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal {
    background: linear-gradient(135deg, rgba(26, 26, 46, 0.95), rgba(22, 33, 62, 0.95));
    border-radius: 20px;
    padding: 20px;
    width: 90%;
    max-width: 420px;
    max-height: 85vh;
    border: 2px solid rgba(255, 215, 0, 0.4);
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.3);
    color: white;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(255, 215, 0, 0.3);
}

.modal-title {
    font-size: 18px;
    color: #ffd700;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.modal-close {
    background: none;
    border: none;
    color: #fff;
    font-size: 24px;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #d4af37;
}

.modal-content {
    line-height: 1.6;
}

/* 兑换码输入样式 */
.code-input-group {
    margin: 20px 0;
}

.code-input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid rgba(255, 215, 0, 0.3);
    border-radius: 10px;
    background: rgba(0, 0, 0, 0.3);
    color: white;
    font-size: 16px;
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.code-input:focus {
    outline: none;
    border-color: #ffd700;
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
}

.code-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.code-button {
    width: 100%;
    padding: 12px;
    background: linear-gradient(135deg, #27ae60, #219a52);
    border: none;
    border-radius: 8px;
    color: white;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.code-button:hover {
    background: linear-gradient(135deg, #219a52, #1e8449);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(39, 174, 96, 0.4);
}

.code-button:disabled {
    background: linear-gradient(135deg, #666, #555);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 版本信息样式 */
.version-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
    border-left: 4px solid #d4af37;
}

.version-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.version-number {
    font-size: 16px;
    font-weight: bold;
    color: #d4af37;
}

.version-date {
    font-size: 12px;
    color: #bdc3c7;
}

.version-content {
    font-size: 14px;
    color: #fff;
    line-height: 1.5;
}

.version-content ul {
    padding-left: 20px;
    margin: 10px 0;
}

.version-content li {
    margin: 5px 0;
}

/* 消息提示 */
.message {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 12px 20px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: bold;
    z-index: 1001;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
}

.message.success {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    border: 1px solid #4CAF50;
}

.message.error {
    background: linear-gradient(135deg, #f44336, #da190b);
    color: white;
    border: 1px solid #f44336;
}

.message.info {
    background: linear-gradient(135deg, #2196F3, #1976D2);
    color: white;
    border: 1px solid #2196F3;
}

/* 装备拾取设置样式 */
.pickup-settings-container {
    padding: 5px 0;
}

.quality-filter-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    margin-bottom: 12px;
}

.quality-filter-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    padding: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.quality-filter-item:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
}

.quality-checkbox {
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    user-select: none;
}

.quality-checkbox input[type="checkbox"] {
    display: none;
}

.quality-checkmark {
    width: 14px;
    height: 14px;
    border: 2px solid #95a5a6;
    border-radius: 3px;
    position: relative;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.quality-checkbox input[type="checkbox"]:checked + .quality-checkmark {
    background: currentColor;
    border-color: currentColor;
}

.quality-checkbox input[type="checkbox"]:checked + .quality-checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 10px;
    font-weight: bold;
}

.quality-name {
    font-size: 12px;
    font-weight: bold;
    text-shadow: 0 0 5px currentColor;
}

.realm-filter-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 15px;
}

.realm-filter-option {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    user-select: none;
}

.realm-filter-option input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 16px;
    height: 16px;
    border: 2px solid #d4af37;
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.realm-filter-option input[type="checkbox"]:checked + .checkmark {
    background: #d4af37;
    border-color: #d4af37;
}

.realm-filter-option input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 10px;
    font-weight: bold;
}

.filter-text {
    font-size: 12px;
    color: #fff;
}

.pickup-settings-buttons {
    display: flex;
    gap: 12px;
    justify-content: center;
    margin-top: 20px;
}

.pickup-save-button,
.pickup-reset-button {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 80px;
}

.pickup-save-button {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
    box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

.pickup-save-button:hover {
    background: linear-gradient(135deg, #2ecc71, #27ae60);
    box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4);
    transform: translateY(-2px);
}

.pickup-save-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.pickup-reset-button {
    background: linear-gradient(135deg, #95a5a6, #bdc3c7);
    color: white;
    box-shadow: 0 4px 15px rgba(149, 165, 166, 0.3);
}

.pickup-reset-button:hover {
    background: linear-gradient(135deg, #bdc3c7, #95a5a6);
    box-shadow: 0 6px 20px rgba(149, 165, 166, 0.4);
    transform: translateY(-2px);
}

/* 响应式设计 */
@media (max-width: 480px) {
    .main-container {
        padding: 8px 8px 80px 8px;
    }
    
    .header {
        padding: 12px;
    }
    
    .header h1 {
        font-size: 20px;
    }
    
    .setting-card {
        padding: 15px;
    }
    
    .setting-name {
        font-size: 16px;
    }
    
    .modal {
        padding: 15px; /* 🔧 从20px减少到15px */
        margin: 10px; /* 🔧 从20px减少到10px */
        max-height: 90vh; /* 🔧 从85vh增加到90vh */
    }
    
    .modal-header {
        margin-bottom: 10px; /* 🔧 从15px减少到10px */
        padding-bottom: 8px; /* 🔧 从10px减少到8px */
    }
    
    .modal-title {
        font-size: 16px; /* 🔧 从18px减少到16px */
    }
    
    .pickup-settings-container {
        padding: 0; /* 🔧 从5px减少到0 */
    }
    
    .quality-filter-grid {
        grid-template-columns: repeat(2, 1fr); /* 🔧 保持2列布局 */
        gap: 6px; /* 🔧 从8px减少到6px */
        margin-bottom: 10px; /* 🔧 从12px减少到10px */
    }
    
    .quality-filter-item {
        padding: 6px; /* 🔧 从8px减少到6px */
    }
    
    .quality-name {
        font-size: 11px; /* 🔧 从12px减少到11px */
    }
    
    .realm-filter-section {
        padding: 10px; /* 🔧 从12px减少到10px */
        margin-top: 10px; /* 🔧 从15px减少到10px */
    }
    
    .filter-text {
        font-size: 11px; /* 🔧 从12px减少到11px */
    }
    
    .pickup-settings-buttons {
        margin-top: 15px; /* 🔧 从20px减少到15px */
        gap: 10px; /* 🔧 从12px减少到10px */
    }
    
    .pickup-save-button,
    .pickup-reset-button {
        padding: 6px 12px; /* 🔧 从8px 16px减少到6px 12px */
        font-size: 11px; /* 🔧 从12px减少到11px */
        min-width: 70px; /* 🔧 从80px减少到70px */
    }
}