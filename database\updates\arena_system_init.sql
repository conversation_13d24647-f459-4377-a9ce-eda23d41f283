-- 竞技场系统数据库初始化脚本
-- 创建时间：2024年12月19日

USE yn_game;

-- 1. 创建竞技场段位表
CREATE TABLE IF NOT EXISTS `immortal_arena_ranks` (
  `rank_level` int(11) NOT NULL COMMENT '段位等级',
  `rank_name` varchar(20) NOT NULL COMMENT '段位名称',
  `rank_color` varchar(7) NOT NULL DEFAULT '#FFFFFF' COMMENT '段位颜色',
  `reward_multiplier` decimal(3,2) NOT NULL DEFAULT 1.00 COMMENT '奖励倍数',
  `required_wins` int(11) NOT NULL DEFAULT 0 COMMENT '晋升所需胜场',
  `required_battles` int(11) NOT NULL DEFAULT 0 COMMENT '晋升所需总场次',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`rank_level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='竞技场段位表';

-- 2. 插入基础段位数据
INSERT IGNORE INTO `immortal_arena_ranks` VALUES
(1, '练气期', '#8B4513', 1.00, 0, 0, NOW()),
(2, '筑基期', '#708090', 1.10, 10, 20, NOW()),
(3, '结丹期', '#CD853F', 1.20, 20, 40, NOW()),
(4, '元婴期', '#DAA520', 1.30, 30, 60, NOW()),
(5, '化神期', '#FF8C00', 1.40, 40, 80, NOW()),
(6, '炼虚期', '#FF6347', 1.50, 50, 100, NOW()),
(7, '合体期', '#DC143C', 1.60, 60, 120, NOW()),
(8, '大乘期', '#8A2BE2', 1.70, 70, 140, NOW()),
(9, '渡劫期', '#4B0082', 1.80, 80, 160, NOW()),
(10, '仙君境', '#FFD700', 2.00, 100, 200, NOW());

-- 3. 创建竞技场记录表
CREATE TABLE IF NOT EXISTS `immortal_arena_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `character_id` int(11) NOT NULL COMMENT '角色ID',
  `opponent_id` int(11) DEFAULT NULL COMMENT '对手角色ID（真实玩家）',
  `opponent_name` varchar(50) NOT NULL COMMENT '对手名称',
  `opponent_power` int(11) NOT NULL COMMENT '对手道行值',
  `is_ai_puppet` tinyint(1) DEFAULT 0 COMMENT '是否为灵智傀儡',
  `battle_result` enum('win','lose') NOT NULL COMMENT '战斗结果',
  `spirit_stone_reward` int(11) NOT NULL DEFAULT 0 COMMENT '灵石奖励',
  `battle_data` text COMMENT '战斗数据JSON',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_character_id` (`character_id`),
  KEY `idx_battle_result` (`battle_result`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_opponent` (`opponent_id`),
  FOREIGN KEY (`character_id`) REFERENCES `characters` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`opponent_id`) REFERENCES `characters` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='竞技场战斗记录';

-- 4. 创建匹配池表
CREATE TABLE IF NOT EXISTS `immortal_arena_match_pool` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '匹配ID',
  `character_id` int(11) NOT NULL COMMENT '角色ID',
  `dao_power` int(11) NOT NULL COMMENT '道行值',
  `realm_level` int(11) NOT NULL COMMENT '境界等级',
  `character_snapshot` text NOT NULL COMMENT '角色快照JSON',
  `match_timeout` timestamp NOT NULL DEFAULT (CURRENT_TIMESTAMP + INTERVAL 5 MINUTE) COMMENT '匹配超时时间',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '进入匹配池时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_character_unique` (`character_id`),
  KEY `idx_dao_power` (`dao_power`),
  KEY `idx_realm_level` (`realm_level`),
  KEY `idx_timeout` (`match_timeout`),
  FOREIGN KEY (`character_id`) REFERENCES `characters` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='竞技场匹配池';

-- 5. 扩展角色表，添加竞技场相关字段
ALTER TABLE `characters`
ADD COLUMN IF NOT EXISTS `arena_dao_power` int(11) DEFAULT 0 COMMENT '竞技场道行值' AFTER `spiritual_root_usage`,
ADD COLUMN IF NOT EXISTS `arena_daily_attempts` int(11) DEFAULT 0 COMMENT '今日论道次数' AFTER `arena_dao_power`,
ADD COLUMN IF NOT EXISTS `arena_purchased_attempts` int(11) DEFAULT 0 COMMENT '今日购买的论道次数' AFTER `arena_daily_attempts`,
ADD COLUMN IF NOT EXISTS `arena_last_reset` date DEFAULT NULL COMMENT '最后重置日期' AFTER `arena_purchased_attempts`,
ADD COLUMN IF NOT EXISTS `arena_rank_level` int(11) DEFAULT 1 COMMENT '竞技等级' AFTER `arena_last_reset`,
ADD COLUMN IF NOT EXISTS `arena_total_wins` int(11) DEFAULT 0 COMMENT '总胜利次数' AFTER `arena_rank_level`,
ADD COLUMN IF NOT EXISTS `arena_total_battles` int(11) DEFAULT 0 COMMENT '总论道次数' AFTER `arena_total_wins`,
ADD COLUMN IF NOT EXISTS `arena_win_streak` int(11) DEFAULT 0 COMMENT '连胜次数' AFTER `arena_total_battles`,
ADD COLUMN IF NOT EXISTS `arena_best_streak` int(11) DEFAULT 0 COMMENT '最佳连胜纪录' AFTER `arena_win_streak`,
ADD COLUMN IF NOT EXISTS `arena_skill_sequence` varchar(50) DEFAULT '0,1,2,3,4,5' COMMENT '竞技技能释放序列' AFTER `arena_best_streak`;

-- 6. 添加外键约束（如果不存在）
-- 为 arena_rank_level 添加外键约束
-- 注意：如果数据不一致可能会失败，生产环境中要小心
-- ALTER TABLE `characters` 
-- ADD CONSTRAINT `fk_characters_arena_rank` 
-- FOREIGN KEY (`arena_rank_level`) REFERENCES `immortal_arena_ranks` (`rank_level`);

-- 7. 创建索引优化查询性能
-- MySQL 5.7兼容：使用忽略错误的方式创建索引
CREATE INDEX `idx_arena_dao_power` ON `characters` (`arena_dao_power`);
CREATE INDEX `idx_arena_rank_level` ON `characters` (`arena_rank_level`);
CREATE INDEX `idx_arena_total_battles` ON `characters` (`arena_total_battles`);
CREATE INDEX `idx_arena_last_reset` ON `characters` (`arena_last_reset`);

-- 8. 清理过期的匹配记录（如果有）
DELETE FROM `immortal_arena_match_pool` WHERE `match_timeout` < NOW();

-- 9. 初始化现有角色的竞技场数据
UPDATE `characters` 
SET 
    `arena_dao_power` = GREATEST(1000, `realm_level` * 100 + COALESCE(`physical_attack` + `immortal_attack`, 100)),
    `arena_rank_level` = 1,
    `arena_last_reset` = CURDATE()
WHERE `arena_dao_power` = 0 OR `arena_dao_power` IS NULL;

-- 完成
SELECT '✅ 竞技场系统数据库初始化完成!' as message; 