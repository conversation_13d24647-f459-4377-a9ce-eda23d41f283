<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <!-- 移动端适配核心meta标签 -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover">
    
    <!-- 🔧 PWA模式底部白边强力修复 - 必须最早执行 -->
    <script src="assets/js/pwa-fix.js"></script>
    
    <!-- 🎛️ 全局调试开关 - 必须最早加载 -->
    <script src="assets/js/global-debug-switch.js"></script>

    <!-- 🔧 项目配置文件 - 必须早期加载 -->
    <script src="assets/js/config.js"></script>

    <!-- WebView优化配置 -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="screen-orientation" content="portrait">
    <meta name="x5-orientation" content="portrait">
    <meta name="full-screen" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta name="browsermode" content="application">
    <meta name="x5-page-mode" content="app">
    <!-- 禁用长按菜单 -->
    <meta name="format-detection" content="telephone=no,email=no,address=no">
    <!-- 强制使用最新版本 -->
    <meta http-equiv="Cache-Control" content="no-siteapp">
    <meta http-equiv="Cache-Control" content="no-transform">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <!-- 启用硬件加速 -->
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <!-- iOS图标 -->
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/images/app-icon-180.png">
    <link rel="apple-touch-icon" sizes="167x167" href="/assets/images/app-icon-167.png">
    <link rel="apple-touch-icon" sizes="152x152" href="/assets/images/app-icon-152.png">
    <link rel="apple-touch-icon" sizes="120x120" href="/assets/images/app-icon-120.png">
    
    <title>一念仙魔 - 角色创建</title>
    <link rel="stylesheet" href="assets/css/global.css">
    <link rel="stylesheet" href="assets/css/character_creation.css">
    
    <!-- 🔧 视口高度修复脚本 -->
    <script src="assets/js/viewport-height-fix.js"></script>
    
    <!-- 🔑 全局登录检查系统 -->
    <script src="assets/js/auth-check.js"></script>
    <!-- PWA支持 -->
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#d4af37">
    <!-- 移动端CSS已删除 -->

</head>
<body>
    <div class="story-container">
        <div class="story-panel">
            <div class="story-content">
                <!-- 故事段落 -->
                <div class="story-paragraph" id="story-1">
                    <img src="assets/images/first_login_1.jpg" alt="鸿蒙初判" class="story-image">
                    <div class="story-text">
                        鸿蒙初判之时，盘古斧劈混沌，清气为天，浊气为地。女娲取灵山息壤造人族，伏羲传八卦定三界。
                    </div>
                </div>
                
                <div class="story-paragraph" id="story-2">
                    <img src="assets/images/first_login_2.jpg" alt="神魔大战" class="story-image">
                    <div class="story-text">
                        然九幽魔渊中，蚩尤率十二祖巫欲夺天地造化，引发神魔大战。
                    </div>
                </div>
                
                <div class="story-paragraph" id="story-3">
                    <img src="assets/images/first_login_3.jpg" alt="太虚鼎封印" class="story-image">
                    <div class="story-text">
                        众神以元神为引铸『太虚鼎』，终将魔族封印于归墟之眼...
                    </div>
                </div>
                
                <div class="story-paragraph" id="story-4">
                    <img src="assets/images/first_login_4.jpg" alt="天衍秘录" class="story-image">
                    <div class="story-text">
                        三万年后，太虚鼎结界渐衰，天现《天衍秘录》残卷，预言人界将有破局者现世。
                    </div>
                </div>
                
                <div class="story-paragraph" id="story-5">
                    <img src="assets/images/first_login_7.jpg" alt="破局者觉醒" class="story-image">
                    <div class="story-text">
                        你作为清虚观弟子，在引气入体时引发天地异象，识海中浮现神秘剑纹——这正是上古诸神寻找的破局者印记。
                    </div>
                </div>
                
                <!-- 角色创建界面 -->
                <div class="character-creation" id="character-creation">
                    <div class="creation-title">【角色创建】</div>
                    <div class="story-text">
                        天地异象已现，破局者身份觉醒。
                        <br>
                        <small style="color: rgba(255, 255, 255, 0.6); font-size: 10px;">（进入游戏后可更换其他形象）</small>
                    </div>
                    
                    <form id="characterForm">
                        <!-- 头像预览 -->
                        <div class="input-group">
                            <label class="input-label">形象预览</label>
                            <div class="avatar-preview" id="avatarPreview">
                                <button type="button" class="spirit-button" id="spiritButton" onclick="openSpiritModal()">
                                    灵根
                                </button>
                            </div>
                        </div>

                        <!-- 头像选择 -->
                        <div class="input-group avatar-selection">
                            <label class="input-label">选择角色形象</label>
                            <div class="avatar-grid" id="avatarGrid">
                                <!-- 头像选项将由JavaScript动态生成 -->
                            </div>
                        </div>
                        
                        <div class="input-group">
                            <label class="input-label" for="characterName">道号</label>
                            <input 
                                type="text" 
                                id="characterName" 
                                name="characterName" 
                                class="character-name-input"
                                placeholder="请输入你的道号（2-8个字符）"
                                maxlength="8"
                                required
                            >
                            <div class="error-message" id="nameError"></div>
                        </div>
                        
                        <button type="submit" class="create-button" id="createButton">
                            踏入修仙之路
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <div class="click-prompt" id="clickPrompt">
        （点击显示下一段）
    </div>
    
    <div class="loading" id="loading">
        正在创建角色...
    </div>
    
    <!-- 灵根分配弹窗 -->
    <div class="spirit-allocation-modal" id="spiritModal">
        <div class="spirit-allocation-content">
            <div class="spirit-allocation-header">
                <div class="spirit-allocation-title">💫 灵根资质分配</div>
                <button class="spirit-allocation-close" onclick="closeSpiritModal()">&times;</button>
            </div>
            
            <div class="spirit-allocation">
                <div class="spirit-info">
                    <div class="total-points">
                        可分配点数：<span id="remainingPoints">100</span>
                    </div>
                    <div class="allocation-hint">
                        每个灵根最少8点，最多35点
                    </div>
                </div>
                
                <div class="spirit-controls">
                    <div class="spirit-row" onclick="showSpiritInfo('metal')">
                        <div class="spirit-icon">⚔️</div>
                        <div class="spirit-name">金灵根 ℹ️</div>
                        <div class="spirit-control">
                            <button type="button" class="spirit-btn minus" onclick="adjustSpirit('metal', -1)">-</button>
                            <span class="spirit-value" id="metal-value">20</span>
                            <button type="button" class="spirit-btn plus" onclick="adjustSpirit('metal', 1)">+</button>
                        </div>
                        <div class="spirit-quality" id="metal-quality">下品</div>
                    </div>
                    
                    <div class="spirit-row" onclick="showSpiritInfo('wood')">
                        <div class="spirit-icon">🌲</div>
                        <div class="spirit-name">木灵根 ℹ️</div>
                        <div class="spirit-control">
                            <button type="button" class="spirit-btn minus" onclick="adjustSpirit('wood', -1)">-</button>
                            <span class="spirit-value" id="wood-value">20</span>
                            <button type="button" class="spirit-btn plus" onclick="adjustSpirit('wood', 1)">+</button>
                        </div>
                        <div class="spirit-quality" id="wood-quality">下品</div>
                    </div>
                    
                    <div class="spirit-row" onclick="showSpiritInfo('water')">
                        <div class="spirit-icon">💧</div>
                        <div class="spirit-name">水灵根 ℹ️</div>
                        <div class="spirit-control">
                            <button type="button" class="spirit-btn minus" onclick="adjustSpirit('water', -1)">-</button>
                            <span class="spirit-value" id="water-value">20</span>
                            <button type="button" class="spirit-btn plus" onclick="adjustSpirit('water', 1)">+</button>
                        </div>
                        <div class="spirit-quality" id="water-quality">下品</div>
                    </div>
                    
                    <div class="spirit-row" onclick="showSpiritInfo('fire')">
                        <div class="spirit-icon">🔥</div>
                        <div class="spirit-name">火灵根 ℹ️</div>
                        <div class="spirit-control">
                            <button type="button" class="spirit-btn minus" onclick="adjustSpirit('fire', -1)">-</button>
                            <span class="spirit-value" id="fire-value">20</span>
                            <button type="button" class="spirit-btn plus" onclick="adjustSpirit('fire', 1)">+</button>
                        </div>
                        <div class="spirit-quality" id="fire-quality">下品</div>
                    </div>
                    
                    <div class="spirit-row" onclick="showSpiritInfo('earth')">
                        <div class="spirit-icon">🗻</div>
                        <div class="spirit-name">土灵根 ℹ️</div>
                        <div class="spirit-control">
                            <button type="button" class="spirit-btn minus" onclick="adjustSpirit('earth', -1)">-</button>
                            <span class="spirit-value" id="earth-value">20</span>
                            <button type="button" class="spirit-btn plus" onclick="adjustSpirit('earth', 1)">+</button>
                        </div>
                        <div class="spirit-quality" id="earth-quality">下品</div>
                    </div>
                </div>
                
                <div class="preset-buttons">
                    <button type="button" class="preset-btn" onclick="applyPreset('balanced')">平衡型</button>
                    <button type="button" class="preset-btn" onclick="applyPreset('warrior')">攻击型</button>
                    <button type="button" class="preset-btn" onclick="applyPreset('survivor')">生存型</button>
                    <button type="button" class="preset-btn" onclick="applyPreset('random')">随机分配</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 灵根说明模态框 -->
    <div class="spirit-modal" id="spiritInfoModal">
        <div class="spirit-modal-content">
            <button class="spirit-modal-close" onclick="closeSpiritInfoModal()">&times;</button>
            <div class="spirit-modal-header">
                <div class="spirit-modal-icon" id="modalIcon">⚔️</div>
                <div class="spirit-modal-title" id="modalTitle">金灵根详解</div>
            </div>
            
            <div class="spirit-description" id="modalDescription">
                金灵根主修筋骨之道，金性主攻伐，擅长物理攻击和暴击输出。修炼金系功法可大幅提升物理攻击力和暴击几率，是物理输出的核心属性。
            </div>
            
            <div class="spirit-recommendations" id="modalRecommendations">
                <div class="spirit-recommendations-title">
                    🎯 新手推荐
                </div>
                <div id="recommendationList">
                    <!-- 推荐内容将由JavaScript填充 -->
                </div>
            </div>
            
            <div class="spirit-effects">
                <div class="spirit-effects-title">⚡ 战斗效果</div>
                <div id="modalEffects">
                    <!-- 效果内容将由JavaScript填充 -->
                </div>
            </div>
            
            <div class="spirit-stats">
                <div class="stat-item">
                    <div class="stat-label">当前数值</div>
                    <div class="stat-value" id="modalCurrentValue">20</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">品质等级</div>
                    <div class="stat-value" id="modalQuality">下品</div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 故事变量
        let currentStory = 1; // 🔧 修复：从1开始，因为页面加载时已显示第一个故事
        const totalStories = 5;
        let storyComplete = false;
        let selectedAvatar = 'huaishang.png'; // 默认头像

        // 头像列表（精简到6个指定头像）
        const avatarList = [
            'huaishang.png', 'cy.png', 'dfdyj.png', 'gf.png', 'dgbf.png', 'hr.png'
        ];
        
        // 新增：灵根分配弹窗控制函数
        function openSpiritModal() {
            document.getElementById('spiritModal').style.display = 'block';
        }
        
        function closeSpiritModal() {
            document.getElementById('spiritModal').style.display = 'none';
        }
        
        function closeSpiritInfoModal() {
            document.getElementById('spiritInfoModal').style.display = 'none';
        }
        
        // 修改原有的灵根详细信息显示函数
        function showSpiritInfo(spiritType) {
            const info = spiritInfoData[spiritType];
            if (!info) return;

            // 更新模态框内容
            document.getElementById('modalIcon').textContent = info.icon;
            document.getElementById('modalTitle').textContent = info.name + '详解';
            document.getElementById('modalDescription').textContent = info.description;
            
            // 填充推荐内容
            const recommendationList = document.getElementById('recommendationList');
            recommendationList.innerHTML = '';
            info.recommendations.forEach(rec => {
                const div = document.createElement('div');
                div.className = 'recommendation-item';
                div.textContent = rec;
                recommendationList.appendChild(div);
            });
            
            // 填充效果内容
            const effectsList = document.getElementById('modalEffects');
            effectsList.innerHTML = '';
            info.effects.forEach(effect => {
                const div = document.createElement('div');
                div.className = 'spirit-effect';
                div.textContent = '• ' + effect;
                effectsList.appendChild(div);
            });
            
            // 更新当前数值和品质
            const currentValue = spiritRoots[spiritType];
            const quality = getSpiritQuality(currentValue);
            document.getElementById('modalCurrentValue').textContent = currentValue;
            document.getElementById('modalQuality').textContent = quality.text;
            document.getElementById('modalQuality').style.color = quality.color;
            
            // 显示信息模态框
            document.getElementById('spiritInfoModal').style.display = 'block';
        }
        
        // 将函数暴露到全局作用域
        window.openSpiritModal = openSpiritModal;
        window.closeSpiritModal = closeSpiritModal;
        window.closeSpiritInfoModal = closeSpiritInfoModal;
        window.adjustSpirit = adjustSpirit;
        window.applyPreset = applyPreset;
        window.showSpiritInfo = showSpiritInfo;
        
        // 页面加载完成后开始显示故事
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM加载完成，开始初始化页面...');
            
            // 确保元素存在
            if (!document.getElementById('story-1')) {
                console.error('找不到故事段落元素!');
            } else {
                console.log('故事段落元素已找到');
            }
            
            // 初始化头像选择
            initAvatarSelection();
            
            // 设置默认头像预览
            const preview = document.getElementById('avatarPreview');
            if (preview) {
                preview.style.backgroundImage = 'url(assets/images/char/' + selectedAvatar + ')';
                console.log('已设置默认头像预览');
            } else {
                console.error('找不到头像预览元素!');
            }
            
            // 初始化灵根显示
            updateSpiritDisplay();
            
            // 初始化弹窗事件监听器（确保在DOM完全加载后）
            setTimeout(() => {
                // 灵根分配弹窗背景点击关闭
                const spiritModal = document.getElementById('spiritModal');
                if (spiritModal) {
                    spiritModal.addEventListener('click', function(e) {
                        if (e.target === this) {
                            closeSpiritModal();
                        }
                    });
                }
                
                // 灵根信息弹窗背景点击关闭
                const spiritInfoModal = document.getElementById('spiritInfoModal');
                if (spiritInfoModal) {
                    spiritInfoModal.addEventListener('click', function(e) {
                        if (e.target === this) {
                            closeSpiritInfoModal();
                        }
                    });
                }
                
                console.log('弹窗事件监听器已初始化');
            }, 100);
            
            // 默认显示第一个故事段落，避免页面空白
            showStory(1);
            showClickPrompt();
            console.log('已显示初始故事内容');
            
            // 首先检查登录状态
            checkLoginStatus();
        });

        // 初始化头像选择
        function initAvatarSelection() {
            const avatarGrid = document.getElementById('avatarGrid');
            
            avatarList.forEach(function(avatar, index) {
                const avatarOption = document.createElement('div');
                avatarOption.className = 'avatar-option';
                avatarOption.style.backgroundImage = 'url(assets/images/char/' + avatar + ')';
                avatarOption.dataset.avatar = avatar;
                
                if (index === 0) {
                    avatarOption.classList.add('selected');
                }
                
                avatarOption.addEventListener('click', function() {
                    selectAvatar(avatar, avatarOption);
                });
                
                avatarGrid.appendChild(avatarOption);
            });
        }

        // 选择头像
        function selectAvatar(avatar, element) {
            // 移除所有选中状态
            document.querySelectorAll('.avatar-option').forEach(function(option) {
                option.classList.remove('selected');
            });
            
            // 设置新选中状态
            element.classList.add('selected');
            selectedAvatar = avatar;
            
            // 更新预览
            const preview = document.getElementById('avatarPreview');
            preview.style.backgroundImage = 'url(assets/images/char/' + avatar + ')';
        }
        
        // 检查登录状态
        function checkLoginStatus() {
            console.log('开始检查登录状态...');
            
            // 显示加载中提示
            const loading = document.getElementById('loading');
            if (loading) {
                loading.style.display = 'block';
                console.log('显示加载提示');
            }
            
            // 首先直接获取用户信息
            fetch(window.GameConfig ? window.GameConfig.getApiUrl('user_info.php') : '../src/api/user_info.php')
                .then(response => {
                    console.log('用户信息API响应状态码:', response.status);
                    if (!response.ok) {
                        throw new Error('获取用户信息失败: ' + response.status);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('用户信息响应:', data);
                    
                    // 隐藏加载提示
                    if (loading) {
                        loading.style.display = 'none';
                        console.log('隐藏加载提示');
                    }
                    
                    // 如果未登录，直接跳转登录页
                    if (!data.success || !data.logged_in) {
                        console.error('用户信息API显示未登录，跳转到登录页面');
                        alert('请先登录后再创建角色');
                        window.location.href = 'login.html';
                        return;
                    }
                    
                    // 检查是否已经有角色
                    if (data.user && data.user.character_id) {
                        console.log('用户已有角色，跳转到游戏页面');
                        
                        // 🌙 设置刚登录标记，用于控制离线收益弹窗显示
                        localStorage.setItem('just_logged_in', 'true');
                        localStorage.setItem('login_time', Date.now().toString());
                        
                        alert('角色已存在，正在进入游戏...');
                        window.location.href = 'game.html';
                        return;
                    }
                    
                    // 用户已登录且未创建角色，使用create_character.php检查need_create_character标志
                    console.log('用户已登录且未创建角色，检查need_create_character标志');
                    
                    fetch(window.GameConfig ? window.GameConfig.getApiUrl('create_character.php?check_need_create=1') : '../src/api/create_character.php?check_need_create=1')
                        .then(response => response.json())
                        .then(sessionData => {
                            console.log('会话状态检查结果:', sessionData);
                            
                            // 如果标志未设置，尝试修复
                            if (!sessionData.has_need_create_character) {
                                console.log('need_create_character标志未设置，尝试修复');
                                
                                fetch(window.GameConfig ? window.GameConfig.getApiUrl('create_character.php?fix_need_create=1') : '../src/api/create_character.php?fix_need_create=1')
                                    .then(response => response.json())
                                    .then((result) => {
                                        console.log('已尝试修复need_create_character标志:', result);
                                    })
                                    .catch(err => {
                                        console.error('修复标志失败:', err);
                                    });
                            }
                            
                            // 无论是否需要修复，都继续显示角色创建界面
                            console.log('显示角色创建界面');
                            
                            // 如果故事尚未显示完，显示故事
                            if (!storyComplete) {
                                console.log('显示角色创建故事...');
                                currentStory = 1; // 🔧 修复：从1开始，因为页面已显示第一个故事
                                showStory(1);
                                showClickPrompt();
                            } else {
                                // 如果故事已显示完，直接显示角色创建界面
                                console.log('故事已显示完，直接显示角色创建界面');
                                showCharacterCreation();
                            }
                        })
                        .catch(error => {
                            console.error('检查会话状态失败:', error);
                            
                            // 尽管会话检查失败，但用户已登录且没有角色，仍然显示角色创建界面
                            console.log('尽管会话检查失败，仍然显示角色创建界面');
                            
                            if (!storyComplete) {
                                showStory(1);
                                showClickPrompt();
                            } else {
                                showCharacterCreation();
                            }
                        });
                })
                .catch(error => {
                    // 隐藏加载提示
                    if (loading) {
                        loading.style.display = 'none';
                    }
                    
                    console.error('检查登录状态时发生错误:', error);
                    
                    // 尝试直接使用会话诊断
                    fetch(window.GameConfig ? window.GameConfig.getApiUrl('create_character.php?diagnostic=1') : '../src/api/create_character.php?diagnostic=1')
                        .then(response => response.json())
                        .then(diagnostic => {
                            console.log('会话诊断结果:', diagnostic);
                            
                            if (diagnostic.session_status.has_user_id) {
                                console.log('会话诊断显示用户已登录，继续角色创建');
                                
                                // 用户已登录，可能只是API调用失败，仍然显示角色创建界面
                                if (!storyComplete) {
                                    showStory(1);
                                    showClickPrompt();
                                } else {
                                    showCharacterCreation();
                                }
                            } else {
                                console.error('会话诊断显示用户未登录，跳转到登录页面');
                                alert('请先登录后再创建角色');
                                window.location.href = 'login.html';
                            }
                        })
                        .catch(() => {
                            console.error('无法获取会话诊断，直接跳转到登录页面');
                            alert('系统错误，请重新登录');
                            window.location.href = 'login.html';
                        });
                });
        }
        
        // 显示指定的故事段落
        function showStory(storyIndex) {
            console.log('显示故事段落:', storyIndex);
            
            // 隐藏所有故事段落
            for (let i = 1; i <= totalStories; i++) {
                const story = document.getElementById('story-' + i);
                if (story) {
                    story.classList.remove('active');
                } else {
                    console.error('找不到故事段落元素 #story-' + i);
                }
            }
            
            // 显示当前故事段落
            const currentStoryElement = document.getElementById('story-' + storyIndex);
            if (currentStoryElement) {
                currentStoryElement.classList.add('active');
                console.log('已激活故事段落:', storyIndex);
            } else {
                console.error('找不到当前故事段落元素 #story-' + storyIndex);
            }
        }
        
        // 显示点击提示
        function showClickPrompt() {
            const prompt = document.getElementById('clickPrompt');
            if (!storyComplete) {
                prompt.classList.add('show');
            }
        }
        
        // 隐藏点击提示
        function hideClickPrompt() {
            const prompt = document.getElementById('clickPrompt');
            prompt.classList.remove('show');
        }
        
        // 点击事件处理
        document.addEventListener('click', function(e) {
            // 如果点击的是输入框或按钮，不处理故事切换
            if (e.target.closest('.character-creation') || storyComplete) {
                return;
            }
            
            currentStory++;
            
            if (currentStory <= totalStories) {
                showStory(currentStory);
            } else {
                // 故事播放完毕，显示角色创建界面
                hideClickPrompt();
                showCharacterCreation();
            }
        });
        
        // 显示角色创建界面
        function showCharacterCreation() {
            storyComplete = true;
            
            // 隐藏所有故事段落
            for (let i = 1; i <= totalStories; i++) {
                const story = document.getElementById('story-' + i);
                if (story) {
                    story.classList.remove('active');
                }
            }
            
            // 显示角色创建界面
            const creation = document.getElementById('character-creation');
            creation.classList.add('active');
            
            // 聚焦到输入框
            setTimeout(function() {
                document.getElementById('characterName').focus();
            }, 500);
        }
        
        // 角色名验证
        function validateCharacterName(name) {
            if (!name || name.trim().length === 0) {
                return '请输入道号';
            }
            
            if (name.trim().length < 2) {
                return '道号至少需要2个字符';
            }
            
            if (name.trim().length > 8) {
                return '道号不能超过8个字符';
            }
            
            // 检查是否包含特殊字符
            const regex = /^[\u4e00-\u9fa5a-zA-Z0-9_]+$/;
            if (!regex.test(name.trim())) {
                return '道号只能包含中文、英文、数字和下划线';
            }
            
            return null;
        }
        
        // 禁用页面滚动和缩放（仅在故事播放阶段）
        document.addEventListener('touchmove', function(e) {
            // 如果故事已完成（显示角色创建界面），允许正常滚动
            if (storyComplete) {
                return;
            }
            
            // 如果触摸的是角色创建相关元素，允许滚动
            if (e.target.closest('.character-creation') || 
                e.target.closest('.spirit-modal') ||
                e.target.closest('input') ||
                e.target.closest('button')) {
                return;
            }
            
            // 其他情况下禁用滚动（故事播放阶段）
            e.preventDefault();
        }, { passive: false });
        
        document.addEventListener('gesturestart', function(e) {
            // 如果故事已完成，允许手势
            if (storyComplete) {
                return;
            }
            
            // 如果手势在角色创建相关元素上，允许手势
            if (e.target.closest('.character-creation') || 
                e.target.closest('.spirit-modal')) {
                return;
            }
            
            // 其他情况下禁用手势（故事播放阶段）
            e.preventDefault();
        });

        // 灵根分配系统
        const spiritRoots = {
            metal: 20,
            wood: 20,
            water: 20,
            fire: 20,
            earth: 20
        };

        const TOTAL_POINTS = 100;
        const MIN_VALUE = 8;
        const MAX_VALUE = 35;

        // 灵根品质判断
        function getSpiritQuality(value) {
            if (value >= 35) return { text: '上品', color: '#3498db' };
            if (value >= 25) return { text: '中品', color: '#27ae60' };
            if (value >= 15) return { text: '下品', color: '#95a5a6' };
            return { text: '废根', color: '#6c757d' };
        }

        // 更新灵根显示
        function updateSpiritDisplay() {
            const elements = ['metal', 'wood', 'water', 'fire', 'earth'];
            let totalUsed = 0;

            elements.forEach(element => {
                const value = spiritRoots[element];
                totalUsed += value;
                
                // 更新数值显示
                document.getElementById(element + '-value').textContent = value;
                
                // 更新品质显示
                const quality = getSpiritQuality(value);
                const qualityElement = document.getElementById(element + '-quality');
                qualityElement.textContent = quality.text;
                qualityElement.style.color = quality.color;
                
                // 更新按钮状态
                const minusBtn = document.querySelector(`[onclick="adjustSpirit('${element}', -1)"]`);
                const plusBtn = document.querySelector(`[onclick="adjustSpirit('${element}', 1)"]`);
                
                minusBtn.disabled = (value <= MIN_VALUE);
                plusBtn.disabled = (value >= MAX_VALUE || totalUsed >= TOTAL_POINTS);
            });

            // 更新剩余点数
            document.getElementById('remainingPoints').textContent = TOTAL_POINTS - totalUsed;
        }

        // 调整灵根数值
        function adjustSpirit(element, change) {
            // 阻止事件冒泡到父元素
            if (event) {
                event.stopPropagation();
            }
            
            const currentValue = spiritRoots[element];
            const newValue = currentValue + change;
            const currentTotal = Object.values(spiritRoots).reduce((a, b) => a + b, 0);

            // 检查限制
            if (newValue < MIN_VALUE || newValue > MAX_VALUE) return;
            if (change > 0 && currentTotal >= TOTAL_POINTS) return;

            spiritRoots[element] = newValue;
            updateSpiritDisplay();
        }

        // 应用预设方案
        function applyPreset(type) {
            if (type === 'balanced') {
                // 平衡型：20-20-20-20-20
                spiritRoots.metal = 20;
                spiritRoots.wood = 20;
                spiritRoots.water = 20;
                spiritRoots.fire = 20;
                spiritRoots.earth = 20;
            } else if (type === 'warrior') {
                // 新手：金火双修，攻击输出型
                spiritRoots.metal = 30; // 高金系伤害
                spiritRoots.wood = 15;  // 基础木系伤害
                spiritRoots.water = 10; // 最低水系伤害
                spiritRoots.fire = 25;  // 高火系伤害
                spiritRoots.earth = 20; // 基础土系伤害
            } else if (type === 'survivor') {
                // 生存型：木土双修，防护恢复型
                spiritRoots.metal = 15; // 基础金系伤害
                spiritRoots.wood = 30;  // 高木系伤害(恢复)
                spiritRoots.water = 10; // 最低水系伤害
                spiritRoots.fire = 15;  // 基础火系伤害
                spiritRoots.earth = 30; // 高土系伤害(防护)
            } else if (type === 'random') {
                // 随机型：完全随机分配
                const elements = ['metal', 'wood', 'water', 'fire', 'earth'];
                
                // 先设置所有为最小值
                elements.forEach(element => {
                    spiritRoots[element] = MIN_VALUE;
                });
                
                // 随机分配剩余点数
                let remainingPoints = TOTAL_POINTS - (elements.length * MIN_VALUE);
                
                while (remainingPoints > 0) {
                    const randomElement = elements[Math.floor(Math.random() * elements.length)];
                    if (spiritRoots[randomElement] < MAX_VALUE) {
                        const maxAdd = Math.min(3, remainingPoints, MAX_VALUE - spiritRoots[randomElement]);
                        const addPoints = Math.floor(Math.random() * maxAdd) + 1;
                        spiritRoots[randomElement] += addPoints;
                        remainingPoints -= addPoints;
                    }
                }
            }
            
            updateSpiritDisplay();
        }

        // 灵根详细信息数据
        const spiritInfoData = {
            metal: {
                name: '金灵根',
                icon: '⚔️',
                description: '金灵根决定金系技能的威力，金性主杀伐，擅长锋锐攻击。金灵根值越高，使用金系技能时造成的额外伤害越高。',
                effects: [
                    '🧮 计算公式：金灵根值 × 1.2',
                    '⚔️ 使用金系技能时附加此伤害',
                    '🌟 包括金属性武器技能',
                    '⚡ 与五行相克关系影响最终伤害',
                    '💎 无法直接提升，需通过特殊方法改变'
                ],
                recommendations: [
                    '⚔️ 金系流：推荐金灵根25-35点，配合金系武器和技能',
                    '🗡️ 物理输出：金系技能多为物理类，适合物攻流',
                    '🛡️ 平衡型：金灵根20-25点，保证基础金系伤害',
                    '💡 新手建议：金系技能较多，有一定金灵根很实用'
                ],
                advantages: '金系技能威力高，攻击锋锐，技能丰富',
                disadvantages: '只影响金系技能，需要配合相应武器'
            },
            wood: {
                name: '木灵根',
                icon: '🌲',
                description: '木灵根决定木系技能的威力，木性主生发，擅长恢复和生机。木灵根值越高，使用木系技能时造成的额外伤害越高。',
                effects: [
                    '🧮 计算公式：木灵根值 × 1.2',
                    '⚔️ 使用木系技能时附加此伤害',
                    '🌟 包括木属性武器技能',
                    '⚡ 与五行相克关系影响最终伤害',
                    '💎 无法直接提升，需通过特殊方法改变'
                ],
                recommendations: [
                    '🌿 木系流：推荐木灵根25-35点，配合木系武器和技能',
                    '💚 恢复流：木系技能多有恢复效果，适合持久战',
                    '🍃 平衡发展：木灵根20-25点，保证基础木系伤害',
                    '💡 新手建议：木系技能兼顾攻击和恢复，很实用'
                ],
                advantages: '木系技能威力高，生命力强，攻守兼备',
                disadvantages: '只影响木系技能，需要配合相应武器'
            },
            water: {
                name: '水灵根',
                icon: '💧',
                description: '水灵根决定水系技能的威力，水性主柔韧，擅长防御和控制。水灵根值越高，使用水系技能时造成的额外伤害越高。',
                effects: [
                    '🧮 计算公式：水灵根值 × 1.2',
                    '⚔️ 使用水系技能时附加此伤害',
                    '🌟 包括水属性武器技能',
                    '⚡ 与五行相克关系影响最终伤害',
                    '💎 无法直接提升，需通过特殊方法改变'
                ],
                recommendations: [
                    '🔮 水系流：推荐水灵根25-35点，配合水系武器和技能',
                    '💙 防御流：水系技能多有防御效果，适合生存',
                    '🌊 控制流：水系技能善于控制，战术丰富',
                    '💡 新手建议：水系技能偏防御，适合稳健型玩家'
                ],
                advantages: '水系技能威力高，防御性强，控制丰富',
                disadvantages: '只影响水系技能，需要配合相应武器'
            },
            fire: {
                name: '火灵根',
                icon: '🔥',
                description: '火灵根决定火系技能的威力，火性主爆发，擅长高伤害输出。火灵根值越高，使用火系技能时造成的额外伤害越高。',
                effects: [
                    '🧮 计算公式：火灵根值 × 1.2',
                    '⚔️ 使用火系技能时附加此伤害',
                    '🌟 包括火属性武器技能',
                    '⚡ 与五行相克关系影响最终伤害',
                    '💎 无法直接提升，需通过特殊方法改变'
                ],
                recommendations: [
                    '🔥 火系流：推荐火灵根25-35点，配合火系武器和技能',
                    '⚡ 爆发流：火系技能伤害高，适合秒杀流',
                    '🌪️ 高输出：火系技能威力强，追求极限伤害',
                    '💡 新手建议：火系技能伤害直观，容易上手'
                ],
                advantages: '火系技能威力高，爆发力强，伤害优秀',
                disadvantages: '只影响火系技能，需要配合相应武器'
            },
            earth: {
                name: '土灵根',
                icon: '🗻',
                description: '土灵根决定土系技能的威力，土性主厚重，擅长防护和稳固。土灵根值越高，使用土系技能时造成的额外伤害越高。',
                effects: [
                    '🧮 计算公式：土灵根值 × 1.2',
                    '⚔️ 使用土系技能时附加此伤害',
                    '🌟 包括土属性武器技能',
                    '⚡ 与五行相克关系影响最终伤害',
                    '💎 无法直接提升，需通过特殊方法改变'
                ],
                recommendations: [
                    '🛡️ 土系流：推荐土灵根25-35点，配合土系武器和技能',
                    '⛰️ 防护流：土系技能多有防护效果，适合坦克',
                    '🏔️ 稳固流：土系技能注重稳定，持续输出',
                    '💡 新手建议：土系技能偏防御，适合保守型玩家'
                ],
                advantages: '土系技能威力高，防护性强，稳定可靠',
                disadvantages: '只影响土系技能，需要配合相应武器'
            }
        };

        // 修改表单提交，包含灵根数据
        const originalFormHandler = document.getElementById('characterForm').onsubmit;
        document.getElementById('characterForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // 验证灵根总数
            const totalPoints = Object.values(spiritRoots).reduce((a, b) => a + b, 0);
            if (totalPoints !== TOTAL_POINTS) {
                document.getElementById('nameError').textContent = '灵根总数必须为100点';
                return;
            }

            const startTime = performance.now();
            console.log('🚀 角色创建开始，时间:', new Date().toLocaleTimeString());
            
            const characterName = document.getElementById('characterName').value.trim();
            const errorElement = document.getElementById('nameError');
            const createButton = document.getElementById('createButton');
            const loading = document.getElementById('loading');
            
            // 清除之前的错误信息
            errorElement.textContent = '';
            
            // 验证角色名
            const validationError = validateCharacterName(characterName);
            if (validationError) {
                errorElement.textContent = validationError;
                console.log('❌ 验证失败:', validationError);
                return;
            }
            
            // 禁用按钮并显示加载状态
            createButton.disabled = true;
            createButton.textContent = '创建中...';
            loading.style.display = 'block';
            loading.textContent = '正在创建角色...';
            
            // 包含灵根数据的请求数据
            const requestData = {
                character_name: characterName,
                character_avatar: selectedAvatar,
                spirit_roots: spiritRoots
            };
            
            console.log('📝 发送请求数据:', requestData);
            
            const requestStartTime = performance.now();
            console.log('📡 API请求发送，耗时:', (requestStartTime - startTime).toFixed(2), 'ms');
            
            // 发送角色创建请求
            fetch(window.GameConfig ? window.GameConfig.getApiUrl('create_character.php') : '../src/api/create_character.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            })
            .then(function(response) {
                const responseTime = performance.now();
                console.log('📥 API响应接收，耗时:', (responseTime - requestStartTime).toFixed(2), 'ms');
                console.log('📊 响应状态:', response.status, response.statusText);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                return response.json();
            })
            .then(function(data) {
                const parseTime = performance.now();
                console.log('🔍 数据解析完成，总耗时:', (parseTime - startTime).toFixed(2), 'ms');
                console.log('📋 返回数据:', data);
                
                if (data.success) {
                    loading.textContent = '角色创建成功！';
                    console.log('✅ 角色创建成功');
                    
                    // 🌙 设置刚登录标记，用于控制离线收益弹窗显示
                    localStorage.setItem('just_logged_in', 'true');
                    localStorage.setItem('login_time', Date.now().toString());
                    
                    setTimeout(() => {
                        console.log('🎯 开始跳转到游戏页面');
                        window.location.href = 'game.html';
                    }, 100);
                } else {
                    console.log('❌ 角色创建失败:', data.message);
                    errorElement.textContent = data.message || '角色创建失败，请重试';
                    loading.style.display = 'none';
                }
            })
            .catch(function(error) {
                const errorTime = performance.now();
                console.error('💥 角色创建出错，总耗时:', (errorTime - startTime).toFixed(2), 'ms');
                console.error('🔍 错误详情:', error);
                
                if (!navigator.onLine) {
                    errorElement.textContent = '网络连接已断开，请检查网络后重试';
                } else {
                    if (error.name === 'TypeError' && error.message.includes('fetch')) {
                        errorElement.textContent = '无法连接到服务器，请检查网络连接';
                    } else if (error.message.includes('HTTP')) {
                        errorElement.textContent = `服务器错误: ${error.message}`;
                    } else {
                        errorElement.textContent = '角色创建失败，请重试';
                    }
                }
                
                loading.style.display = 'none';
            })
            .finally(function() {
                const endTime = performance.now();
                console.log('🏁 请求处理完成，总耗时:', (endTime - startTime).toFixed(2), 'ms');
                
                createButton.disabled = false;
                createButton.textContent = '踏入修仙之路';
            });
        });

        // 输入框实时验证
        document.getElementById('characterName').addEventListener('input', function() {
            const errorElement = document.getElementById('nameError');
            const characterName = this.value.trim();
            
            if (characterName.length > 0) {
                const validationError = validateCharacterName(characterName);
                if (validationError) {
                    errorElement.textContent = validationError;
                } else {
                    errorElement.textContent = '';
                }
            } else {
                errorElement.textContent = '';
            }
        });
    </script>
</body>
</html> 