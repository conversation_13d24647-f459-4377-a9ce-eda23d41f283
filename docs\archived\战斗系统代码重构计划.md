# 战斗系统代码重构计划

## 一、目标

将当前臃肿的`script.js`文件进行模块化拆分，提高代码可维护性、可读性和复用性。

## 二、当前状态分析

### 1. 状态机实现状态
- ✅ **基础架构**：状态机已创建并正确加载
- ✅ **核心方法迁移**：6个核心方法已迁移到状态机
- ⚠️ **存在问题**：
  - 方法重复实现：部分方法在script.js中仍有直接实现
  - 循环依赖：BattleSystem和BattleStateMachine互相依赖
  - 状态管理不完整：stopAutoBattle方法未处理所有状态

### 2. 已完成的模块化工作
- ✅ 状态机模块 (`battle-state-machine.js`)：已实现并迁移了核心方法
  - `initialize()`
  - `initializeBattle()`
  - `gameOver()`
  - `handleBattleEnd()`
  - `startAutoBattle()`
  - `stopAutoBattle()`

- ✅ UI控制器 (`ui-controller.js`)：已实现部分UI管理功能
  - `updateBattleStatus()`
  - `getUIState()`

- ✅ 核心战斗系统 (`battle-system.js`)：已创建基础框架

### 3. 重复方法现状
| 方法名 | 当前状态 | 分析 |
|-------|---------|------|
| `startAutoBattle()` | 两处实现：<br>1. script.js第124行（状态机调用）<br>2. script.js第753行（直接实现） | 需要合并实现，保留状态机通知 |
| `stopAutoBattle()` | 两处实现：<br>1. script.js第129行（状态机调用）<br>2. script.js第2960行（直接实现） | 需要完善状态机实现，添加倒计时清理 |
| `initialize()` | 两处实现：<br>1. script.js第67行（状态机调用）<br>2. script.js第181行（直接实现） | 已迁移到状态机，需要删除直接实现 |
| `initializeBattle()` | 两处实现：<br>1. script.js第72行（状态机调用）<br>2. script.js第211行（直接实现） | 已迁移到状态机，需要删除直接实现 |
| `gameOver()` | 两处实现：<br>1. script.js第77行（状态机调用）<br>2. script.js第1056行（直接实现） | 已迁移到状态机，需要删除直接实现 |
| `handleBattleEnd()` | 两处实现：<br>1. script.js第82行（状态机调用）<br>2. script.js第3085行（直接实现） | 已迁移到状态机，需要删除直接实现 |

### 4. 代码依赖关系
- BattleSystem 依赖 BattleStateMachine
- BattleStateMachine 依赖 BattleSystem（循环依赖）
- UI控制器相对独立

## 三、修订后的实施计划

### 第一阶段：完善状态机实现

1. **完善stopAutoBattle方法**
```javascript
// 在BattleStateMachine中
stopAutoBattle() {
    console.log('⚡ 状态机 stopAutoBattle 开始执行');
    // 清理战斗超时
    if (this.battleSystem.battleTimeout) {
        clearTimeout(this.battleSystem.battleTimeout);
        this.battleSystem.battleTimeout = null;
    }
    // 清理倒计时
    if (this.battleSystem.autoBattleCountdown) {
        clearInterval(this.battleSystem.autoBattleCountdown);
        this.battleSystem.autoBattleCountdown = null;
    }
    // 更新状态
    this.battleSystem.isAutoBattleMode = false;
    localStorage.removeItem('autoBattleMode');
    console.log('⚡ 状态机 stopAutoBattle 执行完成');
}
```

2. **统一自动战斗状态管理**
```javascript
// 在BattleStateMachine中
startAutoBattle() {
    console.log('⚡ 状态机 startAutoBattle 开始执行');
    // 记录开始时间
    this.battleSystem.battleStartTime = Date.now();
    // 设置状态
    this.battleSystem.isAutoBattleMode = true;
    localStorage.setItem('autoBattleMode', 'true');
    // 执行自动战斗
    if (!this.battleSystem.isGameOver) {
        this.battleSystem.autoBattle();
    }
    console.log('⚡ 状态机 startAutoBattle 执行完成');
}
```

### 第二阶段：解决循环依赖

1. **创建事件总线**
```javascript
// 新建 battle-event-bus.js
class BattleEventBus {
    constructor() {
        this.listeners = new Map();
    }
    
    on(event, callback) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }
        this.listeners.get(event).push(callback);
    }
    
    emit(event, data) {
        if (this.listeners.has(event)) {
            this.listeners.get(event).forEach(callback => callback(data));
        }
    }
}
```

2. **重构状态机和战斗系统通信**
```javascript
// 在BattleSystem中
constructor() {
    this.eventBus = new BattleEventBus();
    this.stateMachine = new BattleStateMachine(this.eventBus);
}

// 在BattleStateMachine中
constructor(eventBus) {
    this.eventBus = eventBus;
    this.eventBus.on('battleEnd', this.handleBattleEnd.bind(this));
}
```

### 第三阶段：清理重复代码

1. **删除script.js中的重复实现**
2. **保留状态机代理方法**
3. **更新所有调用点**

## 四、风险管理

1. **循环依赖问题**：通过事件总线解耦
2. **状态同步问题**：统一由状态机管理
3. **兼容性问题**：保持API兼容
4. **性能影响**：监控事件总线性能

## 五、实施时间表

| 阶段 | 任务 | 预计工作量 | 风险等级 |
|------|------|------------|----------|
| 第一阶段 | 完善状态机实现 | 1天 | 中 |
| 第二阶段 | 实现事件总线 | 2天 | 高 |
| 第三阶段 | 清理重复代码 | 1天 | 中 |
| 第四阶段 | 测试与验证 | 2天 | 高 |
| **总计** | | **6天** | |

## 六、注意事项

1. 每个修改都要进行充分测试
2. 保持详细的重构日志
3. 确保向后兼容性
4. 优先解决循环依赖问题
5. 完善错误处理机制

## 七、当前进度

- [x] 完成状态机实现状态分析
- [x] 识别具体问题和解决方案
- [ ] 开始完善状态机实现
- [ ] 创建事件总线
- [ ] 清理重复代码

## 八、下一步行动

1. 完善状态机的stopAutoBattle方法
2. 创建事件总线解决循环依赖
3. 清理script.js中的重复代码

*最后更新: 2024-03-21* 