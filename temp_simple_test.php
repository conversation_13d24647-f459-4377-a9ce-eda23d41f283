<?php
echo "开始简单测试...\n";

// 数据库连接测试
$host = 'localhost';
$dbname = 'yn_game';
$username = 'ynxx';
$password = 'mjlxz159';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ 数据库连接成功\n";
    
    // 检查用户表
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "用户数量: " . $result['count'] . "\n";
    
    // 检查角色表
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM characters");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "角色数量: " . $result['count'] . "\n";
    
    // 检查characters表结构
    echo "检查characters表结构:\n";
    $stmt = $pdo->query("DESCRIBE characters");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    foreach ($columns as $col) {
        echo "  - {$col['Field']} ({$col['Type']})\n";
    }

    // 获取第一个角色
    $stmt = $pdo->query("SELECT c.id, u.username FROM characters c JOIN users u ON c.user_id = u.id LIMIT 1");
    $character = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($character) {
        echo "测试角色: ID {$character['id']}, 用户: {$character['username']}\n";
        
        // 检查game_items表结构
        echo "检查game_items表结构:\n";
        $stmt = $pdo->query("DESCRIBE game_items");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        foreach ($columns as $col) {
            echo "  - {$col['Field']} ({$col['Type']})\n";
        }

        // 检查套装表是否存在
        $stmt = $pdo->query("SHOW TABLES LIKE '%set%'");
        $setTables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "套装相关表: " . implode(', ', $setTables) . "\n";

        // 检查该角色的装备
        $stmt = $pdo->prepare("
            SELECT gi.item_name, gi.set_id
            FROM character_equipment ce
            JOIN game_items gi ON ce.item_id = gi.id
            WHERE ce.character_id = ? AND gi.set_id IS NOT NULL AND gi.set_id > 0
        ");
        $stmt->execute([$character['id']]);
        $equipment = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($equipment) {
            echo "✅ 找到 " . count($equipment) . " 件套装装备:\n";
            foreach ($equipment as $item) {
                echo "  - {$item['item_name']} (套装ID: {$item['set_id']})\n";
            }
        } else {
            echo "❌ 该角色没有套装装备\n";
        }
        
    } else {
        echo "❌ 没有找到角色数据\n";
    }
    
} catch (PDOException $e) {
    echo "❌ 数据库错误: " . $e->getMessage() . "\n";
}

echo "简单测试完成\n";
?>
