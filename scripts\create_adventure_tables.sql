-- 奇遇系统数据库表创建脚本
-- 执行时间：2024年12月19日
-- 包括：奇遇事件配置表、用户奇遇记录表、奇遇触发日志表

-- 1. 奇遇事件配置表
CREATE TABLE IF NOT EXISTS `adventure_events` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '奇遇事件ID',
  `event_name` varchar(100) NOT NULL COMMENT '奇遇事件名称',
  `event_type` varchar(50) NOT NULL COMMENT '事件类型',
  `description` text COMMENT '事件描述',
  `rewards` text COMMENT 'JSON格式奖励配置',
  `probability` decimal(5,2) DEFAULT '100.00' COMMENT '触发概率(%)',
  `min_realm_level` int(11) DEFAULT '1' COMMENT '最低境界要求',
  `max_realm_level` int(11) DEFAULT '280' COMMENT '最高境界要求',
  `required_map_ids` varchar(100) DEFAULT NULL COMMENT '限制地图ID(逗号分隔)',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_event_type` (`event_type`),
  KEY `idx_probability` (`probability`),
  KEY `idx_realm_level` (`min_realm_level`, `max_realm_level`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='奇遇事件配置表';

-- 2. 用户奇遇记录表
CREATE TABLE IF NOT EXISTS `user_adventure_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `character_id` int(11) NOT NULL COMMENT '角色ID',
  `adventure_value` int(11) DEFAULT '0' COMMENT '当前奇遇值',
  `total_triggered` int(11) DEFAULT '0' COMMENT '总触发次数',
  `last_triggered_at` timestamp NULL DEFAULT NULL COMMENT '最后触发时间',
  `last_triggered_event_id` int(11) DEFAULT NULL COMMENT '最后触发的事件ID',
  `daily_triggered_count` int(11) DEFAULT '0' COMMENT '今日触发次数',
  `last_daily_reset` date DEFAULT NULL COMMENT '最后日重置时间',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `character_id` (`character_id`),
  KEY `idx_adventure_value` (`adventure_value`),
  KEY `idx_last_triggered` (`last_triggered_at`),
  FOREIGN KEY (`character_id`) REFERENCES `characters` (`character_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户奇遇记录表';

-- 3. 奇遇触发日志表
CREATE TABLE IF NOT EXISTS `adventure_trigger_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `character_id` int(11) NOT NULL COMMENT '角色ID',
  `event_id` int(11) NOT NULL COMMENT '事件ID',
  `event_name` varchar(100) NOT NULL COMMENT '事件名称',
  `event_type` varchar(50) NOT NULL COMMENT '事件类型',
  `rewards_received` text COMMENT 'JSON格式实际获得奖励',
  `adventure_value_before` int(11) DEFAULT '0' COMMENT '触发前奇遇值',
  `adventure_value_after` int(11) DEFAULT '0' COMMENT '触发后奇遇值',
  `character_realm_level` int(11) DEFAULT '1' COMMENT '触发时角色境界',
  `current_map_id` int(11) DEFAULT NULL COMMENT '触发时所在地图',
  `triggered_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '触发时间',
  PRIMARY KEY (`id`),
  KEY `character_id` (`character_id`),
  KEY `event_id` (`event_id`),
  KEY `idx_event_type` (`event_type`),
  KEY `idx_triggered_at` (`triggered_at`),
  KEY `idx_character_realm` (`character_id`, `character_realm_level`),
  FOREIGN KEY (`character_id`) REFERENCES `characters` (`character_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='奇遇触发日志表';

-- 4. 为characters表添加奇遇相关字段（如果不存在）
ALTER TABLE `characters` 
ADD COLUMN IF NOT EXISTS `spiritual_root_usage` text DEFAULT NULL COMMENT 'JSON格式天材地宝使用记录' AFTER `attribute_pill_count`;

-- 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS `idx_characters_spiritual_root` ON `characters` (`character_id`);

-- 插入初始化数据（为现有角色创建奇遇记录）
INSERT IGNORE INTO `user_adventure_records` (`character_id`, `adventure_value`, `total_triggered`, `daily_triggered_count`, `last_daily_reset`)
SELECT `character_id`, 0, 0, 0, CURDATE()
FROM `characters`
WHERE `character_id` NOT IN (SELECT `character_id` FROM `user_adventure_records`); 