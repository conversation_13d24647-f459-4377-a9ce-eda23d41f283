<?php

/**
 * 战斗统一API - 专注于战斗逻辑处理
 * 
 * 🎯 职责分离设计说明：
 * 
 * ✅ 本API负责：
 * - 战斗数据初始化（玩家基础属性、敌人数据、地图信息）
 * - 战斗结果处理（掉落计算、奖励分发）
 * - 战斗相关的业务逻辑（经验计算、进度更新）
 * 
 * ❌ 本API不负责：
 * - 装备/武器数据获取 → 由 equipment_integrated.php 专门处理
 * - 装备属性计算 → 由 equipment_integrated.php 统一管理
 * - 装备耐久度处理 → 由 equipment_integrated.php 专门处理
 * 
 * 🔧 设计优势：
 * 1. 避免代码重复：装备相关逻辑只在一个地方维护
 * 2. 数据一致性：所有装备数据都来自同一个API，属性计算统一
 * 3. 易于维护：修改装备系统时只需要修改一个文件
 * 4. 职责清晰：每个API都有明确的功能边界
 * 
 * 📋 数据流向：
 * 前端 → equipment_integrated.php → 获取完整武器数据（包含随机属性）
 * 前端 → battle_unified.php → 获取战斗基础数据（玩家、敌人、地图）
 * 前端 → 合并数据 → 进行战斗计算
 */

// 引入全局配置和函数库
require_once __DIR__ . '/../includes/functions.php';

// 检查维护模式
if (isMaintenanceMode()) {
    echo json_encode([
        'success' => false,
        'message' => '游戏正在维护中，请稍后再试',
        'maintenance' => true
    ]);
    exit;
}

// 记录API调用（如果开启了调试）
if (DEBUG_LOG_API_CALLS) {
    writeLog("API调用: battle_unified.php", 'DEBUG', 'api.log');
}

// 设置错误报告（根据配置）
if (isDebugMode()) {
    ini_set('display_errors', 1);
    error_reporting(E_ALL);
} else {
    ini_set('display_errors', 0);
    error_reporting(0);
}

setJsonResponse();

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

try {
    $db = getDatabase();

    if (!$db) {
        writeLog("战斗系统: 数据库连接失败", 'ERROR', 'database.log');
        throw new Exception('数据库连接失败');
    }

    $action = isset($_GET['action']) ? $_GET['action'] : (isset($_POST['action']) ? $_POST['action'] : '');

    switch ($action) {
        case 'init_battle':
            // 初始化战斗数据（专注于敌人数据和地图信息）
            echo json_encode(initBattleData($db));
            break;

        case 'get_battle_data':
            // 获取战斗数据（兼容旧版本）
            echo json_encode(getBattleData($db));
            break;

        case 'calculate_drops':
            // 计算战斗掉落
            echo json_encode(calculateBattleDrops($db));
            break;

        case 'save_battle_result':
            // 保存战斗结果
            echo json_encode(saveBattleResult($db));
            break;

        case 'update_weapon_durability':
            // 更新武器耐久度
            echo json_encode(updateWeaponDurability($db));
            break;

        case 'handle_weapon_durability':
            // 处理武器耐久损耗（批量）
            echo json_encode(handleWeaponDurabilityLoss($db));
            break;

        default:
            echo json_encode(['success' => false, 'message' => '无效的操作: ' . $action]);
            break;
    }
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}

/**
 * 初始化战斗数据 - 🔧 优化：专注于敌人数据和地图信息
 * 武器数据由equipment_integrated.php提供，避免重复
 */
function initBattleData($db)
{
    try {
        // 获取参数
        $mapId = isset($_GET['map_id']) ? intval($_GET['map_id']) : 0;
        $mapCode = isset($_GET['map_code']) ? $_GET['map_code'] : '';
        $stageNumber = isset($_GET['stage_number']) ? intval($_GET['stage_number']) : 1;

        // 获取用户信息
        $userId = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;
        $characterId = isset($_SESSION['character_id']) ? $_SESSION['character_id'] : null;

        // 🔧 新增：详细的会话调试信息
        error_log("=== 战斗初始化会话调试 ===");
        error_log("会话状态: " . (session_status() == PHP_SESSION_ACTIVE ? '已启动' : '未启动'));
        error_log("会话ID: " . session_id());
        error_log("原始SESSION数据: " . json_encode($_SESSION));
        error_log("user_id 存在: " . (isset($_SESSION['user_id']) ? 'YES' : 'NO'));
        error_log("character_id 存在: " . (isset($_SESSION['character_id']) ? 'YES' : 'NO'));
        error_log("user_id 值: " . var_export($userId, true));
        error_log("character_id 值: " . var_export($characterId, true));
        error_log("user_id 类型: " . gettype($userId));
        error_log("character_id 类型: " . gettype($characterId));

        // 游客模式处理
        $isGuest = !$userId || !$characterId;

        error_log("游客模式判断: " . ($isGuest ? 'YES (游客模式)' : 'NO (登录用户)'));
        error_log("=====================================");

        // 1. 获取地图信息
        $mapInfo = getMapInfo($db, $mapId, $mapCode);
        if (!$mapInfo) {
            throw new Exception('无法获取地图信息');
        }

        // 2. 获取角色属性
        $playerData = getPlayerAttributes($db, $characterId, $isGuest);

        // 3. 🔧 移除武器数据获取 - 由equipment_integrated.php专门处理
        // $weaponData = getWeaponSlots($db, $characterId, $isGuest); // 已删除

        // 4. 获取怪物数据
        $enemyData = getEnemyData($db, $mapInfo['id'], $stageNumber);

        // 5. 获取用户进度
        $progressData = getUserProgress($db, $characterId, $mapInfo['id'], $isGuest);

        return [
            'success' => true,
            'data' => [
                'map_info' => $mapInfo,
                'player_data' => $playerData,
                // 🔧 移除武器数据返回 - 由equipment_integrated.php专门处理
                // 'weapon_data' => $weaponData, // 已删除
                'enemy_data' => $enemyData,
                'progress_data' => $progressData,
                'is_guest' => $isGuest
            ]
        ];
    } catch (Exception $e) {
        return ['success' => false, 'message' => $e->getMessage()];
    }
}

/**
 * 获取地图信息
 */
function getMapInfo($db, $mapId, $mapCode)
{
    try {
        // 优先使用地图ID查询
        if ($mapId > 0) {
            $stmt = $db->prepare("SELECT * FROM game_maps WHERE id = ?");
            $stmt->execute([$mapId]);
            $map = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($map) {
                return $map;
            }
        }

        // 使用地图代码查询
        if (!empty($mapCode)) {
            $stmt = $db->prepare("SELECT * FROM game_maps WHERE map_code = ?");
            $stmt->execute([$mapCode]);
            $map = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($map) {
                return $map;
            }
        }

        // 地图代码映射（兼容旧版本）
        $mapCodeMapping = [
            'kunlun' => 'map_qingyun',
            'map_qingyun' => 'map_qingyun',
            'donghai' => 'map_donghai',
            'jiuyou' => 'map_jiuyou'
        ];

        $actualMapCode = isset($mapCodeMapping[$mapCode]) ? $mapCodeMapping[$mapCode] : $mapCode;

        if ($actualMapCode !== $mapCode) {
            $stmt = $db->prepare("SELECT * FROM game_maps WHERE map_code = ?");
            $stmt->execute([$actualMapCode]);
            $map = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($map) {
                return $map;
            }
        }

        // 返回默认地图
        return [
            'id' => 1,
            'map_name' => '青云山',
            'map_code' => 'map_qingyun',
            'max_stages' => 100,
            'background_image' => 'assets/images/maps/qingyun.jpg'
        ];
    } catch (Exception $e) {
        error_log("获取地图信息失败: " . $e->getMessage());
        return null;
    }
}

/**
 * 获取角色属性
 */
function getPlayerAttributes($db, $characterId, $isGuest)
{
    if ($isGuest) {
        return getGuestPlayerAttributes();
    }

    try {
        // 🔧 修复：先获取角色信息，然后传递给calculateCharacterAttributes
        $stmt = $db->prepare("
            SELECT c.*, r.realm_name, r.realm_level,
                   c.physique, c.comprehension, c.constitution, c.spirit, c.agility,
                   c.attack_multiplier, c.defense_multiplier, c.hp_multiplier, 
                   c.mp_multiplier, c.speed_multiplier, c.character_name, c.avatar_image
            FROM characters c
            LEFT JOIN realm_levels r ON c.realm_id = r.id
            WHERE c.id = ?
        ");
        $stmt->execute([$characterId]);
        $characterInfo = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$characterInfo) {
            return getGuestPlayerAttributes();
        }

        // 获取装备和套装加成
        require_once dirname(__DIR__) . '/includes/equipment_stats_manager.php';
        $equipmentBonus = EquipmentStatsManager::getAllEquipmentStats($db, $characterId);
        $setBonus = getCharacterSetBonus($db, $characterId);

        // 调用通用函数计算属性
        $attributes = calculateCharacterAttributes($characterInfo, $equipmentBonus, [], [], $setBonus);

        // 🔧 新增：将套装特殊效果添加到角色数据中
        $attributes['set_special_effects'] = $setBonus['special_effects'] ?? [];

        if (!$attributes) {
            return getGuestPlayerAttributes();
        }

        // 格式化属性数据
        return [
            'character_id' => $characterId,
            'name' => isset($characterInfo['character_name']) ? $characterInfo['character_name'] : '修仙者',
            'level' => intval(isset($characterInfo['realm_level']) ? $characterInfo['realm_level'] : 1),
            'realm_name' => isset($characterInfo['realm_name']) ? $characterInfo['realm_name'] : '练气期',
            'hp_bonus' => intval(isset($attributes['hp']) ? $attributes['hp'] : 300),
            'mp_bonus' => intval(isset($attributes['mp']) ? $attributes['mp'] : 100),
            'physical_attack' => intval(isset($attributes['physical_attack']) ? $attributes['physical_attack'] : 20),
            'physical_defense' => intval(isset($attributes['physical_defense']) ? $attributes['physical_defense'] : 10),
            'immortal_attack' => intval(isset($attributes['immortal_attack']) ? $attributes['immortal_attack'] : 15),
            'immortal_defense' => intval(isset($attributes['immortal_defense']) ? $attributes['immortal_defense'] : 8),
            'speed' => intval(isset($attributes['speed']) ? $attributes['speed'] : 50),
            'accuracy_bonus' => intval(isset($attributes['accuracy_bonus']) ? $attributes['accuracy_bonus'] : 85), // 🔧 修复：使用数据库字段名
            'dodge_bonus' => intval(isset($attributes['dodge_bonus']) ? $attributes['dodge_bonus'] : 5), // 🔧 修复：使用数据库字段名
            'critical_bonus' => intval(isset($attributes['critical_bonus']) ? $attributes['critical_bonus'] : 5), // 🔧 修复：统一使用critical_bonus字段名
            'critical_resistance' => intval(isset($attributes['critical_resistance']) ? $attributes['critical_resistance'] : 0), // 🔧 修复：添加抗暴率字段
            'avatar_image' => isset($characterInfo['avatar_image']) ? $characterInfo['avatar_image'] : 'ck.png',
            // 🔧 新增：套装特殊效果数据传递到前端
            'set_special_effects' => $attributes['set_special_effects'] ?? []
        ];
    } catch (Exception $e) {
        error_log("获取角色属性失败: " . $e->getMessage());
        return getGuestPlayerAttributes();
    }
}

/**
 * 获取游客模式角色属性
 */
function getGuestPlayerAttributes()
{
    return [
        'character_id' => 0,
        'name' => '修仙者',
        'level' => 1,
        'realm_name' => '练气期',
        'hp_bonus' => 300,
        'mp_bonus' => 100,
        'physical_attack' => 20,
        'physical_defense' => 10,
        'immortal_attack' => 15,
        'immortal_defense' => 8,
        'speed' => 50,
        'accuracy_bonus' => 80, // 🔧 修复：使用数据库字段名
        'dodge_bonus' => 10, // 🔧 修复：使用数据库字段名
        'critical_bonus' => 10, // 🔧 修复：统一使用critical_bonus字段名
        'critical_resistance' => 0, // 🔧 修复：添加抗暴率字段
        'avatar_image' => 'assets/images/char/ck.png',
        'is_guest' => true
    ];
}

/**
 * 获取敌人数据
 */
function getEnemyData($db, $mapId, $stageNumber)
{
    try {
        error_log("=== 获取敌人数据开始 ===");
        error_log("输入参数: mapId=$mapId, stageNumber=$stageNumber");

        // 🔧 修复1: 首先验证地图ID是否有效
        if (!$mapId || $mapId <= 0) {
            error_log("地图ID无效，尝试从地图代码获取");

            // 尝试从URL参数或其他方式获取地图代码
            $mapCode = isset($_GET['map_code']) ? $_GET['map_code'] : null;
            if (!$mapCode) {
                $mapCode = isset($_POST['map_code']) ? $_POST['map_code'] : null;
            }

            if ($mapCode) {
                error_log("尝试从地图代码获取地图ID: $mapCode");
                $stmt = $db->prepare("SELECT id FROM game_maps WHERE map_code = ?");
                $stmt->execute([$mapCode]);
                $mapData = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($mapData) {
                    $mapId = $mapData['id'];
                    error_log("成功获取地图ID: $mapId");
                } else {
                    error_log("地图代码 $mapCode 未找到，使用默认地图ID=1");
                    $mapId = 1; // 默认使用第一个地图
                }
            } else {
                error_log("没有地图代码，使用默认地图ID=1");
                $mapId = 1;
            }
        }

        // 🔧 修复2: 验证关卡号
        if (!$stageNumber || $stageNumber <= 0) {
            error_log("关卡号无效，使用默认值1");
            $stageNumber = 1;
        }

        error_log("最终使用的参数: mapId=$mapId, stageNumber=$stageNumber");

        // 🔧 修复3: 改进查询，直接使用map_stages表数据
        $stmt = $db->prepare("
            SELECT ms.*,
                   gm.map_name, gm.map_code
            FROM map_stages ms
            LEFT JOIN game_maps gm ON ms.map_id = gm.id
            WHERE ms.map_id = ? AND ms.stage_number = ?
        ");

        $stmt->execute([$mapId, $stageNumber]);
        $stageInfo = $stmt->fetch(PDO::FETCH_ASSOC);

        error_log("查询结果: " . ($stageInfo ? "找到数据" : "未找到数据"));
        if ($stageInfo) {
            error_log("关卡信息: " . json_encode($stageInfo, JSON_UNESCAPED_UNICODE));
        }

        if ($stageInfo && $stageInfo['monster_name']) {
            // 🔧 修复4: 直接使用数据库基础属性，移除等级倍率计算
            $level = isset($stageInfo['monster_level']) && $stageInfo['monster_level'] > 0
                ? intval($stageInfo['monster_level'])
                : $stageNumber;

            // 🔧 修复5: 确保所有数值都是正整数，直接使用map_stages表数据
            $finalHp = max(1, intval(isset($stageInfo['base_hp']) ? $stageInfo['base_hp'] : 100));
            $finalMp = max(1, intval(isset($stageInfo['base_mp']) ? $stageInfo['base_mp'] : 50));
            $finalAttack = max(1, intval(isset($stageInfo['base_attack']) ? $stageInfo['base_attack'] : 10));
            $finalDefense = max(0, intval(isset($stageInfo['base_defense']) ? $stageInfo['base_defense'] : 5));
            $finalSpeed = max(1, intval(isset($stageInfo['base_speed']) ? $stageInfo['base_speed'] : 10));

            // 🔧 使用数据库中的战斗属性字段
            $baseHIT = max(50, intval(isset($stageInfo['accuracy_bonus']) ? $stageInfo['accuracy_bonus'] : 85));
            $baseDodge = max(0, intval(isset($stageInfo['dodge_bonus']) ? $stageInfo['dodge_bonus'] : 5));
            $baseCritical = max(0, intval(isset($stageInfo['critical_bonus']) ? $stageInfo['critical_bonus'] : 10));
            $baseBlock = max(0, intval(isset($stageInfo['critical_resistance']) ? $stageInfo['critical_resistance'] : 0));

            // 🔧 修复6: 处理技能数据
            $skills = ['普通攻击']; // 默认技能
            if (!empty($stageInfo['skills'])) {
                $skillsData = json_decode($stageInfo['skills'], true);
                if (is_array($skillsData)) {
                    if (isset($skillsData['skills']) && is_array($skillsData['skills'])) {
                        $skills = $skillsData['skills'];
                    } else if (is_array($skillsData)) {
                        $skills = $skillsData;
                    }
                }
            }

            // 🔧 修复7: 处理图片路径 - 简化返回，只返回文件名
            $avatarImage = isset($stageInfo['avatar_image']) ? $stageInfo['avatar_image'] : null;
            $modelImage = isset($stageInfo['model_image']) ? $stageInfo['model_image'] : null;

            // 简化图片路径处理 - 只返回文件名，让前端处理
            if (!empty($avatarImage)) {
                // 提取文件名
                $avatarImage = basename($avatarImage);
                // 移除.png扩展名
                $avatarImage = str_replace('.png', '', $avatarImage);
            } else {
                // 使用默认敌人图片
                $avatarImage = $isBoss ? 'boss_default' : 'monster_default';
            }

            if (!empty($modelImage)) {
                $modelImage = basename($modelImage);
                $modelImage = str_replace('.png', '', $modelImage);
            } else {
                $modelImage = $isBoss ? 'boss_default' : 'monster_default';
            }

            // 获取Boss标识
            $isBoss = (intval(isset($stageInfo['is_boss_stage']) ? $stageInfo['is_boss_stage'] : 0) == 1);

            $enemyData = [
                'id' => intval(isset($stageInfo['monster_id']) ? $stageInfo['monster_id'] : 0),
                'name' => isset($stageInfo['monster_name']) ? $stageInfo['monster_name'] : '未知怪物',
                'type' => isset($stageInfo['monster_tier']) ? $stageInfo['monster_tier'] : 'normal',
                'level' => $level,
                'hp' => $finalHp,
                'hp_bonus' => $finalHp,
                'mp' => $finalMp,
                // 🔧 统一使用immortal字段作为法术攻防
                'physical_attack' => $finalAttack,
                'immortal_attack' => $finalAttack,
                'physical_defense' => $finalDefense,
                'immortal_defense' => $finalDefense,
                // 🔧 保持向后兼容
                'attack' => $finalAttack,
                'defense' => $finalDefense,
                'speed' => $finalSpeed,
                // 🔥 修复：使用数据库字段名
                'accuracy_bonus' => intval($baseHIT), // 使用数据库字段名
                'dodge_bonus' => intval($baseDodge), // 使用数据库字段名
                'critical_bonus' => intval($baseCritical), // 🔧 修复：统一使用critical_bonus字段名
                'critical_resistance' => intval($baseBlock), // 使用数据库字段名
                'blockRate' => intval($baseBlock), // 数值型
                'skills' => $skills,
                'avatarImage' => $avatarImage,
                'modelImage' => $modelImage,
                'description' => isset($stageInfo['description']) ? $stageInfo['description'] : '一只神秘的怪物',
                'ai_pattern' => isset($stageInfo['ai_pattern']) ? $stageInfo['ai_pattern'] : 'conservative',
                'isBoss' => $isBoss,
                'spiritStoneReward' => (isset($stageInfo['spirit_stone_reward']) && $stageInfo['spirit_stone_reward'] > 0) ? intval($stageInfo['spirit_stone_reward']) : ($level * 1),
                'goldReward' => (isset($stageInfo['gold_reward']) && $stageInfo['gold_reward'] > 0) ? intval($stageInfo['gold_reward']) : ($level * 5),

                // 🔧 新增: 调试信息
                'debug' => [
                    'map_id' => $mapId,
                    'stage_number' => $stageNumber,
                    'map_name' => isset($stageInfo['map_name']) ? $stageInfo['map_name'] : '未知地图',
                    'map_code' => isset($stageInfo['map_code']) ? $stageInfo['map_code'] : '未知代码',
                    'base_hp' => $finalHp,
                    'final_hp' => $finalHp,
                    'raw_skills' => isset($stageInfo['skills']) ? $stageInfo['skills'] : null,
                    'spirit_stone_reward' => $stageInfo['spirit_stone_reward'],
                    'gold_reward' => $stageInfo['gold_reward'],
                    // 🔥 新增：战斗属性调试信息
                    'accuracy_bonus_raw' => $baseHIT,
                    'dodge_bonus_raw' => $baseDodge,
                    'critical_bonus_raw' => $baseCritical,
                    'block_rate_raw' => $baseBlock
                ]
            ];

            error_log("成功构建敌人数据: " . json_encode($enemyData, JSON_UNESCAPED_UNICODE));
            return $enemyData;
        }

        // 🔧 修复8: 如果没有找到关卡数据，尝试查找相近的关卡
        error_log("未找到指定关卡，尝试查找相近关卡");

        // 查找该地图的最近关卡
        $stmt = $db->prepare("
            SELECT ms.*
            FROM map_stages ms
            WHERE ms.map_id = ? AND ms.stage_number <= ?
            ORDER BY ms.stage_number DESC
            LIMIT 1
        ");

        $stmt->execute([$mapId, $stageNumber]);
        $nearestStage = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($nearestStage && $nearestStage['monster_name']) {
            error_log("找到相近关卡: stage_number=" . $nearestStage['stage_number']);

            // 直接使用相近关卡的数据，不进行等级调整
            $level = $stageNumber;

            $finalHp = max(1, intval(isset($nearestStage['base_hp']) ? $nearestStage['base_hp'] : 100));
            $finalMp = max(1, intval(isset($nearestStage['base_mp']) ? $nearestStage['base_mp'] : 50));
            $finalAttack = max(1, intval(isset($nearestStage['base_attack']) ? $nearestStage['base_attack'] : 10));
            $finalDefense = max(0, intval(isset($nearestStage['base_defense']) ? $nearestStage['base_defense'] : 5));
            $finalSpeed = max(1, intval(isset($nearestStage['base_speed']) ? $nearestStage['base_speed'] : 10));

            $enemyData = [
                'id' => intval(isset($nearestStage['monster_id']) ? $nearestStage['monster_id'] : 0),
                'name' => isset($nearestStage['monster_name']) ? $nearestStage['monster_name'] : '未知怪物',
                'type' => isset($nearestStage['monster_tier']) ? $nearestStage['monster_tier'] : 'normal',
                'level' => $level,
                'hp' => $finalHp,
                'mp' => $finalMp,
                'attack' => $finalAttack,
                'defense' => $finalDefense,
                'speed' => $finalSpeed,
                'accuracy_bonus' => intval(isset($nearestStage['accuracy_bonus']) ? $nearestStage['accuracy_bonus'] : 85),
                'dodge_bonus' => intval(isset($nearestStage['dodge_bonus']) ? $nearestStage['dodge_bonus'] : 5),
                'critical_bonus' => intval(isset($nearestStage['critical_bonus']) ? $nearestStage['critical_bonus'] : 10),
                'critical_resistance' => intval(isset($nearestStage['critical_resistance']) ? $nearestStage['critical_resistance'] : 0),
                'skills' => json_decode(isset($nearestStage['skills']) ? $nearestStage['skills'] : '["普通攻击"]', true),
                'avatarImage' => isset($nearestStage['avatar_image']) ? $nearestStage['avatar_image'] : 'monster_default',
                'modelImage' => isset($nearestStage['model_image']) ? $nearestStage['model_image'] : 'monster_default',
                'description' => isset($nearestStage['description']) ? $nearestStage['description'] : '一只神秘的怪物',
                'ai_pattern' => isset($nearestStage['ai_pattern']) ? $nearestStage['ai_pattern'] : 'conservative',
                'isBoss' => ($stageNumber % 10 == 0),
                'spiritStoneReward' => (isset($nearestStage['spirit_stone_reward']) && $nearestStage['spirit_stone_reward'] > 0) ? intval($nearestStage['spirit_stone_reward']) : ($level * 1),
                'goldReward' => (isset($nearestStage['gold_reward']) && $nearestStage['gold_reward'] > 0) ? intval($nearestStage['gold_reward']) : ($level * 5),
                'debug' => [
                    'source' => 'nearest_stage',
                    'original_stage' => $nearestStage['stage_number'],
                    'target_stage' => $stageNumber
                ]
            ];

            error_log("使用相近关卡生成敌人数据: " . json_encode($enemyData, JSON_UNESCAPED_UNICODE));
            return $enemyData;
        }

        // 🔧 修复9: 最后的备用方案 - 生成默认怪物
        error_log("所有查询都失败，生成默认怪物");
        return generateDefaultEnemy($stageNumber);
    } catch (Exception $e) {
        error_log("获取怪物数据失败: " . $e->getMessage());
        error_log("错误堆栈: " . $e->getTraceAsString());
        return generateDefaultEnemy($stageNumber);
    }
}

/**
 * 生成默认怪物
 */
function generateDefaultEnemy($stageNumber)
{
    $isBoss = ($stageNumber % 10 == 0);

    // 简化的默认属性，不使用等级倍率
    $finalHp = $isBoss ? 200 : 100;
    $finalPhysicalAttack = $isBoss ? 20 : 10;
    $finalImmortalAttack = $isBoss ? 5 : 2;
    $finalPhysicalDefense = $isBoss ? 10 : 5;
    $finalImmortalDefense = $isBoss ? 8 : 4;

    // 默认奖励计算
    $spiritStone = $isBoss ? ($stageNumber * 10) : ($stageNumber * 5);
    $gold = $isBoss ? ($stageNumber * 7) : ($stageNumber * 3);

    return [
        'id' => 0,
        'name' => $isBoss ? '关卡守护者' : '山野妖兽',
        'type' => $isBoss ? 'boss' : 'normal',
        'level' => $stageNumber,
        'hp' => $finalHp,
        'hp_bonus' => $finalHp,
        'mp' => 50,
        // 🔧 更新：统一使用immortal字段作为法术攻防
        'physical_attack' => $finalPhysicalAttack,
        'immortal_attack' => $finalImmortalAttack,
        'physical_defense' => $finalPhysicalDefense,
        'immortal_defense' => $finalImmortalDefense,
        // 🔧 保持向后兼容：提供总攻击力和防御力
        'attack' => $finalPhysicalAttack + $finalImmortalAttack,
        'defense' => $finalPhysicalDefense + $finalImmortalDefense,
        'is_physical_attacker' => true,
        'speed' => 10,
        // 🔥 修复：战斗属性使用正确的小数格式
        'accuracy_bonus' => 85,
        'dodge_bonus' => $isBoss ? 10 : 5,
        'critical_bonus' => $isBoss ? 15 : 5,
        'critical_resistance' => $isBoss ? 10 : 0,
        'skills' => $isBoss ? ['普通攻击', '强力技能'] : ['普通攻击'],
        'avatarImage' => $isBoss ? 'boss_default' : 'monster_default',
        'modelImage' => $isBoss ? 'boss_default' : 'monster_default',
        'description' => $isBoss ? '强大的关卡守护者' : '普通的山野妖兽',
        'isBoss' => $isBoss,
        'spiritStoneReward' => $spiritStone,
        'goldReward' => $gold
    ];
}

/**
 * 获取用户进度
 */
function getUserProgress($db, $characterId, $mapId, $isGuest)
{
    if ($isGuest) {
        return [
            'current_stage' => 1,
            'max_stage_reached' => 1,
            'total_battles' => 0,
            'total_victories' => 0,
            'is_guest' => true
        ];
    }

    try {
        $stmt = $db->prepare("
            SELECT * FROM user_map_progress 
            WHERE character_id = ? AND map_id = ?
        ");

        $stmt->execute([$characterId, $mapId]);
        $progress = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$progress) {
            // 创建新的进度记录
            $stmt = $db->prepare("
                INSERT INTO user_map_progress (character_id, map_id, current_stage, max_stage_reached) 
                VALUES (?, ?, 1, 1)
            ");
            $stmt->execute([$characterId, $mapId]);

            return [
                'current_stage' => 1,
                'max_stage_reached' => 1,
                'total_battles' => 0,
                'total_victories' => 0
            ];
        }

        return [
            'current_stage' => intval($progress['current_stage']),
            'max_stage_reached' => intval($progress['max_stage_reached']),
            'total_battles' => intval($progress['total_battles']),
            'total_victories' => intval($progress['total_victories'])
        ];
    } catch (Exception $e) {
        error_log("获取用户进度失败: " . $e->getMessage());
        return [
            'current_stage' => 1,
            'max_stage_reached' => 1,
            'total_battles' => 0,
            'total_victories' => 0
        ];
    }
}

/**
 * 计算战斗掉落
 */
function calculateBattleDrops($db)
{
    try {
        $mapId = isset($_POST['map_id']) ? intval($_POST['map_id']) : 0;
        $stageNumber = isset($_POST['stage_number']) ? intval($_POST['stage_number']) : 1;
        $isVictory = isset($_POST['is_victory']) ? ($_POST['is_victory'] === 'true') : false;

        if (!$isVictory) {
            return ['success' => true, 'drops' => []];
        }

        // 获取掉落配置
        $stmt = $db->prepare("
            SELECT dg.*, mdc.drop_chance
            FROM map_drop_configs mdc
            JOIN drop_groups dg ON mdc.drop_group_id = dg.id
            WHERE mdc.map_id = ? AND (mdc.stage_number IS NULL OR mdc.stage_number = ?)
            ORDER BY dg.id
            LIMIT 1
        ");

        $stmt->execute([$mapId, $stageNumber]);
        $dropConfig = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$dropConfig) {
            return ['success' => true, 'drops' => []];
        }

        // 检查是否触发掉落
        $dropChance = floatval(isset($dropConfig['drop_chance']) ? $dropConfig['drop_chance'] : 100);
        if (mt_rand(1, 100) > $dropChance) {
            return ['success' => true, 'drops' => []];
        }

        // 获取掉落组中的物品
        $stmt = $db->prepare("
            SELECT dgi.*, gi.item_name, gi.item_type, gi.icon_image
            FROM drop_group_items dgi
            JOIN game_items gi ON dgi.item_id = gi.id
            WHERE dgi.group_id = ?
        ");

        $stmt->execute([$dropConfig['id']]);
        $dropItems = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $drops = [];
        $maxDrops = intval(isset($dropConfig['max_drops']) ? $dropConfig['max_drops'] : 1);
        $minDrops = intval(isset($dropConfig['min_drops']) ? $dropConfig['min_drops'] : 0);

        // 计算实际掉落数量
        $actualDrops = mt_rand($minDrops, $maxDrops);

        for ($i = 0; $i < $actualDrops && count($dropItems) > 0; $i++) {
            $randomIndex = mt_rand(0, count($dropItems) - 1);
            $item = $dropItems[$randomIndex];

            $quantity = mt_rand($item['min_quantity'], $item['max_quantity']);

            // 🔧 修复：处理图片路径，确保前端能正确加载
            $iconPath = $item['icon_image'];
            if ($iconPath) {
                // 🔧 修复：清理路径，移除多余的前缀
                $iconPath = trim($iconPath);

                // 移除可能的路径前缀，但保留原有的文件扩展名
                $iconPath = str_replace(['assets/images/', 'images/', './'], '', $iconPath);

                // 确保使用正确的路径格式，如果已有扩展名就不重复添加
                if (!preg_match('/\.(png|jpg|jpeg|gif|webp)$/i', $iconPath)) {
                    $iconPath = 'assets/images/' . $iconPath . '.png';
                } else {
                    $iconPath = 'assets/images/' . $iconPath;
                }
            } else {
                // 如果没有图片信息，使用默认图片
                $iconPath = 'assets/images/battle_sword.png';
            }

            $drops[] = [
                'item_id' => $item['item_id'],
                'item_name' => $item['item_name'],
                'item_type' => $item['item_type'],
                'quantity' => $quantity,
                'icon_image' => $iconPath
            ];

            // 移除已选择的物品（避免重复）
            array_splice($dropItems, $randomIndex, 1);
        }

        return ['success' => true, 'drops' => $drops];
    } catch (Exception $e) {
        error_log("计算战斗掉落失败: " . $e->getMessage());
        return ['success' => false, 'message' => $e->getMessage()];
    }
}

/**
 * 保存战斗结果
 */
function saveBattleResult($db)
{
    try {
        $characterId = isset($_SESSION['character_id']) ? $_SESSION['character_id'] : null;

        if (!$characterId) {
            return ['success' => false, 'message' => '未登录'];
        }

        $mapId = isset($_POST['map_id']) ? intval($_POST['map_id']) : 0;
        $stageNumber = isset($_POST['stage_number']) ? intval($_POST['stage_number']) : 1;
        $isVictory = isset($_POST['is_victory']) ? ($_POST['is_victory'] === 'true') : false;
        $expGained = isset($_POST['exp_gained']) ? intval($_POST['exp_gained']) : 0;
        $goldGained = isset($_POST['gold_gained']) ? intval($_POST['gold_gained']) : 0;
        $drops = isset($_POST['drops']) ? json_decode($_POST['drops'], true) : [];

        $db->beginTransaction();

        try {
            // 更新用户进度
            if ($isVictory) {
                $stmt = $db->prepare("
                    UPDATE user_map_progress 
                    SET total_battles = total_battles + 1,
                        total_victories = total_victories + 1,
                        max_stage_reached = GREATEST(max_stage_reached, ?),
                        current_stage = LEAST(current_stage + 1, max_stage_reached + 1)
                    WHERE character_id = ? AND map_id = ?
                ");
                $stmt->execute([$stageNumber, $characterId, $mapId]);
            } else {
                $stmt = $db->prepare("
                    UPDATE user_map_progress 
                    SET total_battles = total_battles + 1
                    WHERE character_id = ? AND map_id = ?
                ");
                $stmt->execute([$characterId, $mapId]);
            }

            // 🆕 新增：检查背包空间（如果有掉落物品）
            if (!empty($drops)) {
                // 获取角色背包信息
                $stmt = $db->prepare("SELECT inventory_slots FROM characters WHERE id = ? LIMIT 1");
                $stmt->execute([$characterId]);
                $character = $stmt->fetch(PDO::FETCH_ASSOC);
                $maxSlots = intval($character['inventory_slots'] ?: 30);

                // 检查当前背包使用情况
                $stmt = $db->prepare("SELECT COUNT(*) as used_slots FROM user_inventories WHERE character_id = ?");
                $stmt->execute([$characterId]);
                $usedSlots = intval($stmt->fetch(PDO::FETCH_ASSOC)['used_slots']);
                $availableSlots = $maxSlots - $usedSlots;

                // 计算需要的背包空间（简化版，每个掉落物品占一格）
                $requiredSlots = count($drops);

                error_log("🎒 背包空间检查 - 已用: {$usedSlots}/{$maxSlots}, 可用: {$availableSlots}, 需要: {$requiredSlots}");

                // 如果背包空间不足，返回错误
                if ($requiredSlots > $availableSlots) {
                    $db->rollBack();

                    $shortageSlots = $requiredSlots - $availableSlots;
                    $warningMessage = "背包空间不足！需要 {$requiredSlots} 个空位，当前只有 {$availableSlots} 个空位，缺少 {$shortageSlots} 个空位。";

                    error_log("❌ " . $warningMessage);

                    return [
                        'success' => false,
                        'message' => $warningMessage,
                        'error_type' => 'inventory_full',
                        'required_slots' => $requiredSlots,
                        'available_slots' => $availableSlots,
                        'shortage_slots' => $shortageSlots,
                        'max_slots' => $maxSlots,
                        'used_slots' => $usedSlots
                    ];
                }
            }

            // 添加掉落物品到背包
            require_once __DIR__ . '/../includes/inventory_utils.php';
            foreach ($drops as $drop) {
                $sortWeight = calculateSortWeight($db, $characterId, 'material');
                $stmt = $db->prepare("
                    INSERT INTO user_inventories (character_id, item_id, quantity, obtained_source, sort_weight)
                    VALUES (?, ?, ?, 'battle_drop', ?)
                    ON DUPLICATE KEY UPDATE quantity = quantity + VALUES(quantity)
                ");
                $stmt->execute([$characterId, $drop['item_id'], $drop['quantity'], $sortWeight]);
            }

            // 记录战斗日志
            $stmt = $db->prepare("
                INSERT INTO battle_records (character_id, map_id, stage_number, battle_result, 
                                          experience_gained, spirit_stones_gained, items_dropped)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $characterId,
                $mapId,
                $stageNumber,
                $isVictory ? 'victory' : 'defeat',
                $expGained,
                $goldGained,
                json_encode($drops)
            ]);

            $db->commit();

            return ['success' => true, 'message' => '战斗结果保存成功'];
        } catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }
    } catch (Exception $e) {
        error_log("保存战斗结果失败: " . $e->getMessage());
        return ['success' => false, 'message' => $e->getMessage()];
    }
}

/**
 * 更新武器耐久度
 */
function updateWeaponDurability($db)
{
    try {
        $characterId = isset($_SESSION['character_id']) ? $_SESSION['character_id'] : null;

        if (!$characterId) {
            return ['success' => false, 'message' => '未登录'];
        }

        $weaponId = isset($_POST['weapon_id']) ? intval($_POST['weapon_id']) : 0;
        $durabilityChange = isset($_POST['durability_change']) ? intval($_POST['durability_change']) : 0;
        $changeReason = isset($_POST['change_reason']) ? $_POST['change_reason'] : 'battle';

        if ($weaponId <= 0) {
            return ['success' => false, 'message' => '无效的武器ID'];
        }

        // 更新武器耐久度
        $stmt = $db->prepare("
            UPDATE user_inventories 
            SET current_durability = GREATEST(0, current_durability + ?)
            WHERE id = ? AND character_id = ?
        ");

        $stmt->execute([$durabilityChange, $weaponId, $characterId]);

        if ($stmt->rowCount() > 0) {
            return ['success' => true, 'message' => '武器耐久度更新成功'];
        } else {
            return ['success' => false, 'message' => '武器不存在或更新失败'];
        }
    } catch (Exception $e) {
        error_log("更新武器耐久度失败: " . $e->getMessage());
        return ['success' => false, 'message' => $e->getMessage()];
    }
}

/**
 * 兼容旧版本的getBattleData函数
 */
function getBattleData($db)
{
    // 重定向到initBattleData
    return initBattleData($db);
}

/**
 * 处理武器耐久损耗（批量处理）
 */
function handleWeaponDurabilityLoss($db)
{
    try {
        $characterId = isset($_SESSION['character_id']) ? $_SESSION['character_id'] : null;

        // 🔧 新增：详细的会话调试信息
        error_log("=== 武器耐久处理会话调试 ===");
        error_log("会话状态: " . (session_status() == PHP_SESSION_ACTIVE ? '已启动' : '未启动'));
        error_log("会话ID: " . session_id());
        error_log("原始SESSION数据: " . json_encode($_SESSION));
        error_log("character_id 存在: " . (isset($_SESSION['character_id']) ? 'YES' : 'NO'));
        error_log("character_id 值: " . var_export($characterId, true));
        error_log("character_id 类型: " . gettype($characterId));

        // 🔧 修复：对于游客模式，直接返回成功，不处理耐久损耗
        if (!$characterId) {
            error_log("武器耐久损耗: 游客模式，跳过耐久度处理");
            error_log("==============================");
            return [
                'success' => true,
                'message' => '游客模式，无需处理武器耐久损耗',
                'processed_weapons' => [],
                'is_guest_mode' => true
            ];
        }

        error_log("武器耐久损耗: 登录用户模式，处理耐久度");
        error_log("==============================");

        $usedWeapons = isset($_POST['used_weapons']) ? json_decode($_POST['used_weapons'], true) : [];
        $battleResult = isset($_POST['battle_result']) ? $_POST['battle_result'] : 'defeat';

        if (empty($usedWeapons)) {
            return ['success' => true, 'message' => '没有使用武器，无需处理耐久损耗'];
        }

        $db->beginTransaction();

        try {
            $processedWeapons = [];

            foreach ($usedWeapons as $weaponData) {
                $weaponId = isset($weaponData['id']) ? intval($weaponData['id']) : 0;
                $usageCount = isset($weaponData['usage_count']) ? intval($weaponData['usage_count']) : 1;

                if ($weaponId <= 0) {
                    continue;
                }

                // 计算耐久损耗
                $durabilityLoss = $usageCount; // 每次使用减少1点耐久
                if ($battleResult === 'defeat') {
                    $durabilityLoss *= 2; // 失败时耐久损耗加倍
                }

                // 更新武器耐久度
                $stmt = $db->prepare("
                    UPDATE user_inventories 
                    SET current_durability = GREATEST(0, current_durability - ?)
                    WHERE id = ? AND character_id = ?
                ");

                $stmt->execute([$durabilityLoss, $weaponId, $characterId]);

                if ($stmt->rowCount() > 0) {
                    // 获取更新后的耐久度
                    $stmt = $db->prepare("
                        SELECT current_durability, max_durability 
                        FROM user_inventories 
                        WHERE id = ? AND character_id = ?
                    ");
                    $stmt->execute([$weaponId, $characterId]);
                    $weapon = $stmt->fetch(PDO::FETCH_ASSOC);

                    $processedWeapons[] = [
                        'weapon_id' => $weaponId,
                        'durability_loss' => $durabilityLoss,
                        'current_durability' => intval($weapon['current_durability']),
                        'max_durability' => intval($weapon['max_durability']),
                        'usage_count' => $usageCount
                    ];
                }
            }

            $db->commit();

            return [
                'success' => true,
                'message' => '武器耐久损耗处理完成',
                'processed_weapons' => $processedWeapons,
                'battle_result' => $battleResult,
                'is_guest_mode' => false
            ];
        } catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }
    } catch (Exception $e) {
        error_log("处理武器耐久损耗失败: " . $e->getMessage());
        return ['success' => false, 'message' => $e->getMessage()];
    }
}

/**
 * 计算五行附加伤害
 * @param int $characterId 角色ID
 * @param string $skillElement 技能五行属性
 * @param object $db 数据库连接
 * @return int 五行附加伤害
 */
function calculateElementalDamage($characterId, $skillElement, $db)
{
    try {
        // 如果技能没有五行属性或为中性，返回0
        if (!$skillElement || $skillElement === 'neutral') {
            return 0;
        }

        // 获取角色五行灵根数据
        $stmt = $db->prepare("
            SELECT metal_affinity, wood_affinity, water_affinity, fire_affinity, earth_affinity 
            FROM characters 
            WHERE id = ?
        ");
        $stmt->execute([$characterId]);
        $roots = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$roots) {
            return 0;
        }

        // 五行属性映射
        $elementMapping = [
            'metal' => 'metal_affinity',
            'wood' => 'wood_affinity',
            'water' => 'water_affinity',
            'fire' => 'fire_affinity',
            'earth' => 'earth_affinity'
        ];

        // 获取对应的灵根值
        if (!isset($elementMapping[$skillElement])) {
            return 0;
        }

        $rootField = $elementMapping[$skillElement];
        $rootValue = isset($roots[$rootField]) ? floatval($roots[$rootField]) : 10.0;

        // 计算五行附加伤害：灵根值 × 1.2（提高五行伤害占比）
        $elementalDamage = $rootValue * 1.2;

        return round($elementalDamage);
    } catch (Exception $e) {
        error_log("计算五行伤害失败: " . $e->getMessage());
        return 0;
    }
}

/**
 * 获取技能的五行属性
 * @param int $itemId 物品ID
 * @param object $db 数据库连接
 * @return string 技能五行属性
 */
function getSkillElementType($itemId, $db)
{
    try {
        $stmt = $db->prepare("SELECT element_type FROM item_skills WHERE item_id = ? LIMIT 1");
        $stmt->execute([$itemId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        return $result ? $result['element_type'] : 'neutral';
    } catch (Exception $e) {
        error_log("获取技能五行属性失败: " . $e->getMessage());
        return 'neutral';
    }
}
