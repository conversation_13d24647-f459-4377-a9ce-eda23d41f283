<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover">
    
    <!-- 🔧 PWA模式底部白边强力修复 - 必须最早执行 -->
    <script src="assets/js/pwa-fix.js"></script>
    
    <!-- 🎛️ 全局调试开关 - 必须最早加载 -->
    <script src="assets/js/global-debug-switch.js"></script>

    <!-- 🔧 项目配置文件 - 必须早期加载 -->
    <script src="assets/js/config.js"></script>

    <title>一念修仙 - 修炼系统</title>
    
    <!-- 移动端适配优化标签 -->
    <meta name="format-detection" content="telephone=no, email=no, address=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="一念修仙">
    <meta name="mobile-web-app-capable" content="yes">
    
    <!-- HBuilder X 优化配置 -->
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="force-rendering" content="webkit">
    <meta name="browsermode" content="application">
    
    <!-- PWA 支持 -->
    <meta name="theme-color" content="#1a3a5c">
    <meta name="msapplication-navbutton-color" content="#1a3a5c">
    <link rel="manifest" href="manifest.json">    
    
    <!-- 🔧 新增：引入全局样式文件 -->
    <link rel="stylesheet" href="assets/css/global.css">
    <link rel="stylesheet" href="assets/css/cultivation.css">

    <!-- 引入通用导航样式 -->
    <link rel="stylesheet" href="assets/css/common-navigation.css">    
    
    <!-- 🔧 视口高度修复脚本 -->
    <script src="assets/js/viewport-height-fix.js"></script>

    <!-- 🔑 全局登录检查系统 -->
    <script src="assets/js/auth-check.js"></script>

    <!-- 🔥 属性变化弹窗和战力系统 -->
    <script src="assets/js/attribute-changes-popup.js"></script>
    <script src="assets/js/power-rating.js"></script>

    <!-- 🎵 全局音乐管理器 -->
    <script src="assets/js/global-music-manager.js"></script>

</head>
<body class="cultivation-page">
    <div class="main-container">
        <!-- 顶部标题栏 -->
    <div class="header">
            <div class="header-title">
                <span>⚡</span>
                修炼系统
    </div>
            <a href="game.html" class="back-btn">
                <span>🔙</span>
                返回
            </a>
            </div>
            
        <!-- 修炼界面主体 -->
        <div class="cultivation-container">
            <!-- 境界信息区域 -->
            <div class="realm-section">
                <div class="realm-info">
                    <div class="current-realm" id="currentRealm">开光期</div>
                    
                    <div class="qi-progress">
                        <!-- 🔧 修为进度条（魂力正常时显示） -->
                        <div class="progress-bar" id="progressContainer">
                            <div class="progress-fill" id="progressFill"></div>
                            <div class="progress-text" id="progressText">加载中...</div>
                        </div>
                        
                        <!-- 🔧 魂力状态指示器（魂力受损时显示） -->
                        <div class="soul-status-indicator" id="soulStatusIndicator" style="display: none;">
                            <div class="soul-status-text">魂力修复中...</div>
                            <div class="soul-recovery-bar">
                                <div class="soul-recovery-fill" id="soulRecoveryFill"></div>
                            </div>
                            <div class="soul-recovery-time" id="soulRecoveryTime">剩余时间：计算中...</div>
                        </div>
                    </div>
            
                    <div class="breakthrough-info" id="breakthroughInfo">
                        <span>突破成功率:</span>
                        <span class="success-rate" id="successRate">50%</span>
                    </div>
                </div>
            </div>
            
            <!-- 功能信息区域 -->
            <div class="functions-section">
                <!-- 修炼状态面板 -->
                <div class="function-panel" style="flex: 1;">
                    <div class="panel-title">修炼状态</div>
                    <div class="cultivation-status-grid">
                        <div class="status-item technique-detail-trigger" onclick="showTechniqueDetail()">
                            <div class="status-icon">📖</div>
                            <div class="status-value" id="currentTechnique">凝气决</div>
                            <div class="status-name">当前功法</div>
                        </div>
                        <div class="status-item efficiency-detail-trigger" onclick="showEfficiencyDetail()">
                            <div class="status-icon">⚡</div>
                            <div class="status-value" id="techniqueBonus">+0%</div>
                            <div class="status-name">总效率</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 修炼进度区域 -->
            <div class="cultivation-progress-section">
                <div class="progress-title">周天运行进度</div>
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill" id="cultivationProgressFill"></div>
                        <div class="progress-text" id="cultivationProgressText">0 / 30秒</div>
                    </div>
                </div>
                <div class="progress-info">
                    <span>下次获得修为: <span id="nextQiGain">10</span></span>
                </div>
            </div>

            <!-- 操作按钮区域 -->
            <div class="actions-section">
                <div class="panel-title">修炼操作</div>
                
                <div class="action-buttons-grid">
                    <button class="action-button btn-technique" onclick="openTechniqueModal()">
                        <div class="action-icon">📖</div>
                        <div class="action-text">功法选择</div>
                        <div class="action-description">选择修炼功法</div>
                </button>

                    <button class="action-button btn-breakthrough" onclick="openBreakthroughModal()" disabled>
                        <div class="action-icon">🌟</div>
                        <div class="action-text">境界突破</div>
                        <div class="action-description">尝试突破到下一境界</div>
                </button>

                    <button class="action-button btn-synthesis" onclick="openFragmentSynthesisModal()">
                        <div class="action-icon">🧩</div>
                        <div class="action-text">功法合成</div>
                        <div class="action-description">使用碎片合成功法</div>
                </button>
                </div>
            </div>
            </div>
        </div>

    <!-- 功法选择弹窗 -->
    <div class="technique-modal" id="techniqueModal">
        <div class="technique-modal-content">
            <div class="technique-title">📖 功法选择 📖</div>
            <div class="technique-list" id="techniqueList">
                <!-- 功法列表将通过JavaScript生成 -->
            </div>
            <div class="technique-buttons">
                <button class="technique-button technique-cancel" onclick="closeTechniqueModal()">取消</button>
                <button class="technique-button technique-confirm" onclick="confirmTechniqueChange()" disabled>确认修炼</button>
            </div>
        </div>
    </div>

    <!-- 渡劫确认弹窗 -->
    <div class="breakthrough-modal" id="breakthroughModal">
        <div class="breakthrough-modal-content">
            <div class="breakthrough-title">⚡ 境界突破 ⚡</div>
            
            <!-- 境界转换信息 -->
            <div class="realm-transition">
                <div class="realm-row">
                    <span class="realm-label">当前境界：</span>
                    <span class="realm-value" id="currentRealmText">开光期一阶</span>
                </div>
                <div class="realm-row">
                    <span class="realm-label">目标境界：</span>
                    <span class="realm-value" id="targetRealmText">开光期二阶</span>
            </div>
        </div>

            <!-- 成功率信息 -->
            <div class="success-rate-section">
                <div class="rate-row">
                    <span class="rate-label">基础成功率：</span>
                    <span class="rate-value base-rate" id="baseSuccessRate">50%</span>
                </div>
                <div class="rate-row">
                    <span class="rate-label">最终成功率：</span>
                    <span class="rate-value final-rate" id="finalSuccessRate">50%</span>
                </div>
            </div>
            
            <!-- 风险提示 -->
            <div class="risk-warning">
                <div class="risk-text">
                    ⚠️ 失败将损失30%修为<br>
                    📈 下次成功率+10%<br>
                    ⭐ 成功可获得：全属性+2（筋骨、悟性、体魄、神魂、身法）
            </div>
            </div>
            
            <!-- 丹药选择区域 -->
            <div class="breakthrough-pills">
                <div class="pill-section-title">
                    💊 渡劫丹 💊
                </div>
                <div class="pill-list" id="tribulationPillList">
                    <!-- 渡劫丹列表将通过JavaScript生成 -->
        </div>
    </div>
            
            <!-- 操作按钮 -->
            <div class="breakthrough-buttons">
                <button class="breakthrough-button breakthrough-cancel" onclick="closeBreakthroughModal()">取消</button>
                <button class="breakthrough-button breakthrough-confirm" onclick="confirmBreakthrough()">开始渡劫</button>
            </div>
        </div>
    </div>

    <!-- 🆕 养魂丹选择弹窗 -->
    <div class="soul-pill-modal" id="soulPillModal" style="
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 1001;
        backdrop-filter: blur(5px);
    ">
        <div style="
            background: linear-gradient(135deg, rgba(26, 26, 46, 0.95), rgba(22, 33, 62, 0.95));
            border-radius: 20px;
            padding: 20px;
            width: 90%;
            max-width: 380px;
            max-height: 80vh;
            overflow-y: auto;
            border: 2px solid rgba(231, 76, 60, 0.6);
            box-shadow: 0 0 30px rgba(231, 76, 60, 0.4);
            text-align: center;
            color: white;
        ">
            <div style="font-size: 18px; font-weight: bold; color: #e74c3c; margin-bottom: 20px;">
                🩸 选择养魂丹 🩸
            </div>
            
            <div style="margin-bottom: 15px; font-size: 13px; line-height: 1.6; color: #bdc3c7;">
                魂力受损，需要使用养魂丹来恢复
            </div>
            
            <div class="soul-pill-list" id="soulPillList" style="
                margin: 15px 0;
                display: flex;
                flex-direction: column;
                gap: 8px;
            ">
                <!-- 养魂丹列表将通过JavaScript生成 -->
            </div>
            
            <div style="display: flex; gap: 12px; justify-content: center; margin-top: 20px;">
                <button onclick="closeSoulPillModal()" style="
                    padding: 12px 24px;
                    border: none;
                    border-radius: 10px;
                    font-size: 13px;
                    font-weight: bold;
                    cursor: pointer;
                    background: linear-gradient(135deg, #666, #888);
                    color: white;
                    min-width: 100px;
                ">取消</button>
                <button id="confirmSoulPill" onclick="confirmUseSoulPill()" disabled style="
                    padding: 12px 24px;
                    border: none;
                    border-radius: 10px;
                    font-size: 13px;
                    font-weight: bold;
                    cursor: pointer;
                    background: linear-gradient(135deg, #e74c3c, #c0392b);
                    color: white;
                    min-width: 100px;
                ">确认使用</button>
            </div>
        </div>
    </div>

    <!-- 功法碎片合成弹窗 -->
    <div class="fragment-synthesis-modal" id="fragmentSynthesisModal">
        <div class="fragment-synthesis-modal-content">
            <div class="fragment-synthesis-title">🧩 功法碎片合成 🧩</div>
            
            <div class="fragment-synthesis-content">
                <div class="synthesis-main-content">
                    <!-- 合成配方列表 -->
                    <div class="synthesis-recipes-section">
                        <div class="recipes-title">可合成功法</div>
                        <div class="recipes-list" id="synthesisRecipesList">
                            <!-- 合成配方将通过JavaScript生成 -->
                        </div>
                    </div>
                    
                    <!-- 拥有的功法碎片 (隐藏) -->
                    <div class="owned-fragments-section" style="display: none;">
                        <div class="fragments-title">拥有的功法碎片</div>
                        <div class="fragments-list" id="ownedFragmentsList">
                            <!-- 功法碎片列表将通过JavaScript生成 -->
                        </div>
                    </div>
                </div>
                
                <!-- 操作按钮 -->
                <div class="fragment-synthesis-buttons">
                    <button class="fragment-synthesis-button synthesis-cancel" onclick="closeFragmentSynthesisModal()">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <script src="assets/js/common-navigation.js"></script>

    <script>
        // 全局变量
        let cultivationData = {
            realmInfo: null,
            resources: null,
            currentTechnique: 'ningqi',
            mapProgress: null,
            soulStatus: null // 🆕 魂力状态
        };
        
        // 确保window.cultivationData存在
        window.cultivationData = cultivationData;
        
        let cultivationTimer = null;
        let currentCycleTime = 0;
        let cycleInterval = null;
        let soulCheckInterval = null; // 🆕 魂力状态检查定时器
        let selectedTechnique = null;
        let selectedPill = null;
        
        // 🆕 突破状态控制变量
        let isBreakthroughInProgress = false;
        let lastBreakthroughTime = 0;
        const BREAKTHROUGH_COOLDOWN = 3000; // 3秒冷却时间
        
        // 🔧 修复：渡劫动画状态管理
        let tribulationAnimationState = {
            isActive: false,
            overlayExists: false,
            lightningComplete: false
        };
        
        // 功法数据
        const techniques = {
            'ningqi': {
                id: 'ningqi',
                name: '凝气决',
                description: '基础修炼功法',
                effects: '周天修为增加：10',
                bonus: 0,
                cost: 0,
                unlocked: true
            },
            'xiantian': {
                id: 'xiantian',
                name: '先天功',
                description: '增加吸收灵气效率',
                effects: '周天修为增加：10',
                bonus: 0,
                cost: 0,
                unlocked: true
            },
            'juling': {
                id: 'juling',
                name: '聚灵决',
                description: '坊市功法，提升修炼效率',
                effects: '周天修为增加：10',
                bonus: 0,
                cost: 5000,
                costType: 'spirit_stones',
                unlocked: false
            },
            'lianshen': {
                id: 'lianshen',
                name: '炼神术',
                description: '黑市功法，大幅提升修炼效率',
                effects: '周天修为增加：30，效率+10%',
                bonus: 10,
                cost: 1000,
                costType: 'immortal_jade',
                unlocked: false
            }
        };

        // 🆕 紧急重置渡劫状态函数（调试用）
        window.resetTribulationState = function() {
            console.log('🔧 [调试] 手动重置渡劫状态');
            isBreakthroughInProgress = false;
            window.tribulationLightningComplete = false;
            tribulationAnimationState = {
                isActive: false,
                overlayExists: false,
                lightningComplete: false
            };
            hideTribulationEffect();
            updateButtonStates();
            showMessage('渡劫状态已重置', 'info');
        };

        // 🆕 调试函数：测试API连接
        window.testApiConnection = async function() {
            console.log('🔧 [调试] 开始测试API连接...');

            const tests = [
                { name: '认证状态', url: window.GameConfig ? window.GameConfig.getApiUrl('auth_status.php') : '../src/api/auth_status.php' },
                { name: '修炼API', url: window.GameConfig ? window.GameConfig.getApiUrl('cultivation.php?action=get_cultivation_info&debug=1') : '../src/api/cultivation.php?action=get_cultivation_info&debug=1' },
                { name: '修炼API(带时间戳)', url: window.GameConfig ? window.GameConfig.getApiUrl(`cultivation.php?action=get_cultivation_info&debug=1&t=${Date.now()}`) : `../src/api/cultivation.php?action=get_cultivation_info&debug=1&t=${Date.now()}` }
            ];

            for (const test of tests) {
                try {
                    console.log(`🔧 [调试] 测试 ${test.name}: ${test.url}`);

                    // 添加防缓存头
                    const response = await fetch(test.url, {
                        method: 'GET',
                        headers: {
                            'Cache-Control': 'no-cache, no-store, must-revalidate',
                            'Pragma': 'no-cache',
                            'Expires': '0'
                        }
                    });

                    console.log(`🔧 [调试] ${test.name} 状态:`, response.status, response.statusText);
                    console.log(`🔧 [调试] ${test.name} 响应头:`, [...response.headers.entries()]);

                    if (response.ok) {
                        const text = await response.text();
                        console.log(`🔧 [调试] ${test.name} 原始响应长度:`, text.length);

                        try {
                            const data = JSON.parse(text);
                            console.log(`✅ [调试] ${test.name} 成功:`, data);
                        } catch (e) {
                            console.log(`⚠️ [调试] ${test.name} JSON解析失败:`, e.message);
                            console.log(`⚠️ [调试] ${test.name} 原始响应前200字符:`, text.substring(0, 200));
                        }
                    } else {
                        const text = await response.text();
                        console.log(`❌ [调试] ${test.name} 失败:`, response.status, response.statusText);
                        console.log(`❌ [调试] ${test.name} 错误响应:`, text.substring(0, 200));
                    }
                } catch (error) {
                    console.log(`❌ [调试] ${test.name} 网络错误:`, error.message);
                    console.log(`❌ [调试] ${test.name} 错误堆栈:`, error.stack);
                }
            }

            console.log('🔧 [调试] API连接测试完成');
        };

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎮 [修炼系统] 页面加载完成，开始初始化...');
            console.log('🎮 [修炼系统] 当前URL:', window.location.href);
            console.log('🎮 [修炼系统] 功法数据:', techniques);
            console.log('🎮 [修炼系统] 初始cultivationData:', window.cultivationData);
            console.log('💡 [调试] 如果渡劫卡住，可在控制台输入: resetTribulationState()');
            console.log('💡 [调试] 测试API连接，可在控制台输入: testApiConnection()');
            
            // 检查关键DOM元素是否存在
            const keyElements = [
                'currentRealm', 'progressFill', 'progressText', 'successRate',
                'currentTechnique', 'techniqueBonus',
                'nextQiGain', 'cultivationProgressFill', 'cultivationProgressText'
            ];
            
            console.log('🎮 [DOM检查] 开始检查关键元素...');
            keyElements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    console.log(`✅ [DOM] ${id} 元素存在`);
                } else {
                    console.error(`❌ [DOM] ${id} 元素不存在`);
                }
            });
            
            // 🆕 先处理离线修炼，再加载数据
            handleOfflineCultivationOnLoad().then(() => {
                return loadCultivationData();
            }).then(() => {
                startCultivationCycle();
            }).catch(error => {
                console.error('❌ [初始化] 初始化失败:', error);
                showMessage('初始化失败，请刷新页面', 'error');
                // 即使离线修炼处理失败，也要启动基本功能
                loadCultivationData().catch(() => {
                    useDefaultData();
                }).finally(() => {
                    startCultivationCycle();
                });
            });
        });

        // 加载修炼数据
        async function loadCultivationData() {
            try {
                console.log('📡 [API] 正在获取修炼数据...');

                // 添加时间戳防止缓存
                const timestamp = Date.now();
                const apiUrl = window.GameConfig ? window.GameConfig.getApiUrl(`cultivation.php?action=get_cultivation_info&debug=1&t=${timestamp}`) : `../src/api/cultivation.php?action=get_cultivation_info&debug=1&t=${timestamp}`;
                console.log('📡 [API] 请求URL:', apiUrl);

                // 获取修炼信息
                const cultivationResponse = await fetch(apiUrl, {
                    method: 'GET',
                    headers: {
                        'Cache-Control': 'no-cache',
                        'Pragma': 'no-cache'
                    }
                });
                console.log('📡 [API] 响应状态:', cultivationResponse.status, cultivationResponse.statusText);
                
                if (!cultivationResponse.ok) {
                    throw new Error(`HTTP ${cultivationResponse.status}: ${cultivationResponse.statusText}`);
                }
                
                const cultivationResponseData = await cultivationResponse.json();
                console.log('📊 [数据] 修炼数据响应:', cultivationResponseData);
                
                if (cultivationResponseData.success) {
                    console.log('✅ [数据] 修炼数据获取成功');
                    console.log('📊 [数据] 原始响应数据结构:', cultivationResponseData);
                    console.log('📊 [数据] cultivation字段:', cultivationResponseData.cultivation);
                    console.log('📊 [数据] resources字段:', cultivationResponseData.resources);
                    
                    // 验证并设置境界信息
                    if (cultivationResponseData.cultivation && cultivationResponseData.cultivation.current_realm) {
                        window.cultivationData.realmInfo = cultivationResponseData.cultivation;
                        console.log('✅ [数据] 境界信息设置成功');
                    } else {
                        console.error('❌ [数据] 境界信息结构异常，使用默认数据');
                        console.error('❌ [数据] cultivation:', cultivationResponseData.cultivation);
                        window.cultivationData.realmInfo = {
                            current_realm: {
                                realm_name: '开光期一阶',
                                level: 1,
                                current_qi: 0,
                                exp_required: 1000
                            },
                            breakthrough_rate: 50,
                            can_breakthrough: false
                        };
                    }
                    
                    // 验证并设置资源信息
                    if (cultivationResponseData.resources) {
                        window.cultivationData.resources = cultivationResponseData.resources;
                        console.log('✅ [数据] 资源信息设置成功');
                    } else {
                        console.error('❌ [数据] 资源信息缺失，使用默认数据');
                        window.cultivationData.resources = {
                            spirit_stones: 100,
                            qi_energy: 0,
                            tribulation_pills: 0,
                            soul_pills: 0
                        };
                    }
                    
                    console.log('📊 [数据] 最终境界信息:', window.cultivationData.realmInfo);
                    console.log('📊 [数据] 最终资源信息:', window.cultivationData.resources);
                    
                    // 获取历练地图进度（可选，失败不影响主要功能）
                    try {
                        console.log('📡 [地图] 正在获取地图数据...');
                        const mapResponse = await fetch(window.GameConfig ? window.GameConfig.getApiUrl('adventure_maps.php?action=get_maps') : '../src/api/adventure_maps.php?action=get_maps');
                        console.log('📡 [地图] 地图响应状态:', mapResponse.status);
                        
                        if (mapResponse.ok) {
                            const mapData = await mapResponse.json();
                            console.log('📡 [地图] 地图数据:', mapData);
                            
                            if (mapData.success) {
                                window.cultivationData.mapProgress = mapData;
                                console.log('✅ [地图] 地图数据获取成功');
                            } else {
                                console.warn('⚠️ [地图] 获取地图数据失败:', mapData.message);
                            }
                        } else {
                            console.warn('⚠️ [地图] 地图接口响应失败:', mapResponse.status);
                        }
                    } catch (mapError) {
                        console.warn('⚠️ [地图] 地图数据获取失败（不影响修炼功能）:', mapError.message);
                    }
                    
                    // 🆕 获取魂力状态
                    await checkSoulStatus();
                    
                    // 🆕 启动魂力状态定期检查
                    startSoulStatusCheck();
                    
                    console.log('🔄 [显示] 开始更新显示...');
                    await updateDisplay();
                    updateButtonStates();
                    
                    console.log('✅ [修炼系统] 数据加载成功');
                } else {
                    console.error('❌ [API] 获取修炼数据失败:', cultivationResponseData.message);
                    if (cultivationResponseData.debug) {
                        console.error('❌ [调试] 详细错误信息:', cultivationResponseData.debug);
                    }
                    showMessage('获取修炼数据失败: ' + cultivationResponseData.message, 'error');
                    
                    // 使用默认数据
                    console.log('🔄 [默认] 使用默认数据...');
                    await useDefaultData();
                }
                
            } catch (error) {
                console.error('❌ [网络] 加载修炼数据失败:', error);
                console.error('❌ [网络] 错误详情:', error.message, error.stack);
                showMessage('网络连接失败，使用默认数据', 'error');
                
                // 使用默认数据
                console.log('🔄 [默认] 网络错误，使用默认数据...');
                await useDefaultData();
            }
        }

        // 使用默认数据
        async function useDefaultData() {
            console.log('🔄 [默认] useDefaultData 开始执行');
            
            // 确保cultivationData对象存在
            if (!window.cultivationData) {
                window.cultivationData = {};
            }
            
            window.cultivationData.realmInfo = {
                current_realm: {
                    realm_name: '开光期一阶',
                    level: 1,
                    current_qi: 0,
                    exp_required: 1000
                },
                breakthrough_rate: 50,
                can_breakthrough: false
            };
            
            window.cultivationData.resources = {
                spirit_stones: 100,
                qi_energy: 0,
                tribulation_pills: 0,
                soul_pills: 0
            };
            
            window.cultivationData.mapProgress = { id: 1 };
            
            console.log('🔄 [默认] realmInfo:', window.cultivationData.realmInfo);
            console.log('🔄 [默认] resources:', window.cultivationData.resources);
            console.log('🔄 [默认] mapProgress:', window.cultivationData.mapProgress);
            
            await updateDisplay();
            updateButtonStates();
            
            console.log('✅ [默认] 默认数据初始化完成');
        }

        // 计算基础灵气获得量
        function calculateBaseQi() {
            console.log('🧮 [计算] calculateBaseQi 开始执行');
            
            // 确保mapProgress存在
            if (!window.cultivationData || !window.cultivationData.mapProgress) {
                console.log('🧮 [计算] mapProgress不存在，使用默认值');
                return 10;
            }
            
            console.log('🧮 [计算] mapProgress:', window.cultivationData.mapProgress);
            
            // 基于当前通关的历练地图计算
            const mapLevel = window.cultivationData.mapProgress.id || 1;
            const baseQi = Math.max(10, mapLevel * 2); // 最少10点，地图等级*2
            console.log('🧮 [计算] 基于地图计算 - 地图等级:', mapLevel, '基础灵气:', baseQi);
            return baseQi;
        }

        // 🆕 获取功法基础修为值
        function getTechniqueBaseQi(techniqueName, techniqueLevel = 1) {
            // 功法基础修为值配置
            const baseQiValues = {
                '凝气决': 10,     // 基础功法，基础修为10
                '先天功': 12,     // 基础功法，基础修为12  
                '聚灵决': 15,     // 坊市功法，基础修为15
                '炼神术': 20      // 黑市功法，基础修为20
            };
            
            const baseQi = baseQiValues[techniqueName] || 10;
            
            // 等级加成：基础修为值 × 等级
            const finalBaseQi = baseQi * techniqueLevel;
            
            return finalBaseQi;
        }

        // 🆕 计算总修为获得量（新公式）
        async function calculateTotalQiGain() {
            console.log('🧮 [计算] calculateTotalQiGain 开始执行');
            
            try {
                // 🔧 注释：地图进度不再影响修为计算（已与后端统一）
                
                // 🔧 修改：计算所有已学会功法的基础修为之和
                let techniqueBaseQi = 10; // 默认值（至少有一个基础功法）
                
                // 从API获取所有功法信息
                const response = await fetch(window.GameConfig ? window.GameConfig.getApiUrl('cultivation.php?action=get_attributes') : '../src/api/cultivation.php?action=get_attributes');
                const data = await response.json();
                
                if (data.success && data.attributes && data.attributes.cultivation_techniques) {
                    let techniques = {};
                    if (typeof data.attributes.cultivation_techniques === 'string') {
                        techniques = JSON.parse(data.attributes.cultivation_techniques);
                    } else {
                        techniques = data.attributes.cultivation_techniques;
                    }
                    
                    console.log('📖 [功法] 计算所有功法基础修为:', techniques);
                    
                    // 计算所有已学会功法的基础修为之和
                    let totalTechniqueBaseQi = 0;
                    for (const techniqueId in techniques) {
                        const technique = techniques[techniqueId];
                        if (technique && technique.name && technique.level) {
                            const singleTechniqueBaseQi = getTechniqueBaseQi(technique.name, technique.level);
                            totalTechniqueBaseQi += singleTechniqueBaseQi;
                            console.log(`📖 [功法] ${technique.name} (等级${technique.level}): 基础修为 ${singleTechniqueBaseQi}点`);
                        }
                    }
                    
                    // 如果有功法数据，使用计算出的总和；否则使用默认值
                    if (totalTechniqueBaseQi > 0) {
                        techniqueBaseQi = totalTechniqueBaseQi;
                    }
                    
                    console.log('📖 [功法] 所有功法基础修为总和:', techniqueBaseQi);
                }
                
                // 🔧 修改：基础修为 = 功法基础修为总和 + 地图进度修为
                const mapProgressQi = await calculateMapProgressQi();
                const baseQiGain = techniqueBaseQi + mapProgressQi;
                
                // 🔧 修改：获取所有效率加成
                const efficiencyData = await calculateAllEfficiencyBonus();
                
                // 应用效率加成
                const finalQiGain = Math.floor(baseQiGain * (1 + efficiencyData.total / 100));
                
                console.log('🧮 [计算] 修为计算明细:', {
                    techniqueBaseQi,
                    mapProgressQi,
                    baseQiGain,
                    techniqueEfficiency: efficiencyData.technique,
                    realmEfficiency: efficiencyData.realm,
                    mapEfficiency: efficiencyData.map,
                    totalEfficiency: efficiencyData.total,
                    finalQiGain
                });
                
                return {
                    techniqueBaseQi,
                    mapProgressQi,
                    baseQiGain,
                    techniqueEfficiency: efficiencyData.technique,
                    realmEfficiency: efficiencyData.realm,
                    mapEfficiency: efficiencyData.map,
                    totalEfficiency: efficiencyData.total,
                    finalQiGain
                };
                
            } catch (error) {
                console.error('❌ [计算] 计算总修为获得量失败:', error);
                return {
                    techniqueBaseQi: 10,
                    mapProgressQi: 0,
                    baseQiGain: 10,
                    efficiencyBonus: 0,
                    finalQiGain: 10
                };
            }
        }

        // 🔧 修改：获取功法效率加成（与后端保持一致）
        function getTechniqueEfficiencyBonus(techniqueName, techniqueLevel = 1) {
            // 🔧 与后端getTechniqueEfficiencyBonus函数保持一致 - 所有功法都来源于奇遇
            const baseBonuses = {
                '凝气决': 0,      // 奇遇功法，无加成
                '先天功': 2,      // 奇遇功法，+2%基础效率
                '聚灵决': 4,      // 奇遇功法，+4%基础效率
                '炼神术': 6,      // 奇遇功法，+6%基础效率
                '太极真经': 8,    // 奇遇功法，+8%基础效率
                '九转玄功': 10,   // 奇遇功法，+10%基础效率
                '混沌诀': 12      // 奇遇功法，+12%基础效率
            };
            
            const baseBonus = baseBonuses[techniqueName] || 0;
            
            // 等级加成：每级额外增加1%效率
            const levelBonus = (techniqueLevel - 1) * 1;
            
            return baseBonus + levelBonus;
        }

        // 🔧 新增：获取功法基础修为值（与后端getTechniqueBaseQi函数保持一致）
        function getTechniqueBaseQi(techniqueName, techniqueLevel = 1) {
            // 🔧 与后端getTechniqueBaseQi函数保持一致 - 所有功法都来源于奇遇
            const baseQiValues = {
                '凝气决': 10,     // 奇遇功法，基础修为10
                '先天功': 12,     // 奇遇功法，基础修为12  
                '聚灵决': 15,     // 奇遇功法，基础修为15
                '炼神术': 18,     // 奇遇功法，基础修为18
                '太极真经': 22,   // 奇遇功法，基础修为22
                '九转玄功': 26,   // 奇遇功法，基础修为26
                '混沌诀': 30      // 奇遇功法，基础修为30
            };
            
            const baseQi = baseQiValues[techniqueName] || 10;
            
            // 等级加成：基础修为值 × 等级
            const finalBaseQi = baseQi * techniqueLevel;
            
            return finalBaseQi;
        }

        // 🔧 修改：按照后端一致的逻辑计算所有效率加成
        async function calculateAllEfficiencyBonus() {
            console.log('⚡ [效率] 开始计算所有效率加成（与后端保持一致）');
            
            try {
                // 1. 计算功法效率加成
                const techniqueBonus = await getAllTechniquesEfficiencyBonus();
                console.log(`⚡ [效率] 功法效率加成: +${techniqueBonus}%`);
                
                // 2. 计算境界效率加成
                const realmBonus = calculateRealmEfficiencyBonus();
                console.log(`⚡ [效率] 境界效率加成: +${realmBonus}%`);
                
                // 3. 计算地图效率加成
                const mapBonus = await calculateMapEfficiencyBonus();
                console.log(`⚡ [效率] 地图效率加成: +${mapBonus}%`);
                
                // 4. 计算总效率加成
                const totalBonus = techniqueBonus + realmBonus + mapBonus;
                console.log(`⚡ [效率] 总效率加成: +${totalBonus}%`);
                
                return {
                    technique: techniqueBonus,
                    realm: realmBonus,
                    map: mapBonus,
                    total: totalBonus
                };
                
            } catch (error) {
                console.error('❌ [效率] 计算效率加成失败:', error);
                return {
                    technique: 0,
                    realm: 0,
                    map: 0,
                    total: 0
                };
            }
        }

        // 🆕 计算境界效率加成
        function calculateRealmEfficiencyBonus() {
            try {
                const realmLevel = getCurrentRealmLevel();
                
                // 境界每级+1%效率
                const levelBonus = realmLevel * 1; // 每级+1%
                
                console.log(`🏔️ [境界] 境界等级: ${realmLevel}, 境界效率加成: +${levelBonus}%`);
                
                return levelBonus;
            } catch (error) {
                console.error('❌ [境界] 计算境界效率加成失败:', error);
                return 0;
            }
        }

        // 🆕 计算地图效率加成
        async function calculateMapEfficiencyBonus() {
            try {
                let maxCurrentStage = 1; // 默认关卡数
                
                // 获取所有地图最高通关记录
                try {
                    const mapResponse = await fetch(window.GameConfig ? window.GameConfig.getApiUrl('adventure_maps.php?action=get_maps') : '../src/api/adventure_maps.php?action=get_maps');
                    if (mapResponse.ok) {
                        const mapData = await mapResponse.json();
                        if (mapData.success && mapData.maps) {
                            const allMaps = [
                                ...(mapData.maps.normal_maps || []),
                                ...(mapData.maps.dungeons || []),
                                ...(mapData.maps.special_maps || [])
                            ];
                            
                            // 找到所有地图中最高的current_stage
                            for (let map of allMaps) {
                                if (map.current_stage && map.current_stage > maxCurrentStage) {
                                    maxCurrentStage = map.current_stage;
                                }
                            }
                        }
                    }
                } catch (mapError) {
                    console.warn('⚠️ [地图] 获取地图数据失败，使用默认值:', mapError);
                    maxCurrentStage = 1;
                }
                
                // 每多1关+0.5%效率，保留一位小数
                const mapBonus = Math.round((maxCurrentStage - 1) * 0.5 * 10) / 10;
                console.log(`🗺️ [地图] 最高通关关卡: ${maxCurrentStage}, 地图效率加成: +${mapBonus}%`);
                
                return mapBonus;
            } catch (error) {
                console.error('❌ [地图] 计算地图效率加成失败:', error);
                return 0;
            }
        }

        // 🆕 计算地图进度修为
        async function calculateMapProgressQi() {
            try {
                let maxCurrentStage = 1; // 默认关卡数
                
                // 获取所有地图最高通关记录
                try {
                    const mapResponse = await fetch(window.GameConfig ? window.GameConfig.getApiUrl('adventure_maps.php?action=get_maps') : '../src/api/adventure_maps.php?action=get_maps');
                    if (mapResponse.ok) {
                        const mapData = await mapResponse.json();
                        if (mapData.success && mapData.maps) {
                            const allMaps = [
                                ...(mapData.maps.normal_maps || []),
                                ...(mapData.maps.dungeons || []),
                                ...(mapData.maps.special_maps || [])
                            ];
                            
                            // 找到所有地图中最高的current_stage
                            for (let map of allMaps) {
                                if (map.current_stage && map.current_stage > maxCurrentStage) {
                                    maxCurrentStage = map.current_stage;
                                }
                            }
                        }
                    }
                } catch (mapError) {
                    console.warn('⚠️ [地图] 获取地图数据失败，使用默认值:', mapError);
                    maxCurrentStage = 1;
                }
                
                // 地图进度修为：每通关1关+1点基础修为（第1关不计算）
                const mapProgressQi = Math.max(0, maxCurrentStage - 1);
                console.log(`🗺️ [地图] 最高通关关卡: ${maxCurrentStage}, 地图进度修为: +${mapProgressQi}点`);
                
                return mapProgressQi;
            } catch (error) {
                console.error('❌ [地图] 计算地图进度修为失败:', error);
                return 0;
            }
        }

        // 🔧 修改：计算所有功法的效率加成总和（与后端逻辑保持一致）
        async function getAllTechniquesEfficiencyBonus() {
            console.log('📖 [功法] getAllTechniquesEfficiencyBonus 开始执行');
            
            try {
                // 从API获取最新的功法数据
                const response = await fetch(window.GameConfig ? window.GameConfig.getApiUrl('cultivation.php?action=get_attributes') : '../src/api/cultivation.php?action=get_attributes');
                const data = await response.json();
                
                if (data.success && data.attributes && data.attributes.cultivation_techniques) {
                    let techniques = {};
                    if (typeof data.attributes.cultivation_techniques === 'string') {
                        techniques = JSON.parse(data.attributes.cultivation_techniques);
                    } else {
                        techniques = data.attributes.cultivation_techniques;
                    }
                    
                    console.log('📖 [功法] 解析功法数据:', techniques);
                    
                    // 🔧 计算所有已学会功法的效率加成总和（与后端一致）
                    let totalBonus = 0;
                    
                    for (const techniqueId in techniques) {
                        const technique = techniques[techniqueId];
                        if (technique && technique.name && technique.level) {
                            const techniqueBonus = getTechniqueEfficiencyBonus(technique.name, technique.level);
                            totalBonus += techniqueBonus;
                            console.log(`📖 [功法] ${technique.name} (等级${technique.level}): +${techniqueBonus}%`);
                        }
                    }
                    
                    console.log('📖 [功法] 所有功法总效率加成:', totalBonus);
                    return totalBonus;
                    
                } else {
                    console.log('📖 [功法] API响应无效或数据不完整');
                    return 0;
                }
                
            } catch (error) {
                console.error('📖 [功法] 获取功法效率加成失败:', error);
                return 0;
            }
        }

        // 更新显示
        async function updateDisplay() {
            console.log('🔄 [显示] updateDisplay 开始执行');
            console.log('🔄 [显示] cultivationData:', window.cultivationData);
            
            if (!window.cultivationData || !window.cultivationData.realmInfo) {
                console.warn('⚠️ [显示] 修炼数据未加载');
                console.warn('⚠️ [显示] cultivationData:', window.cultivationData);
                return;
            }
            
            console.log('🔄 [显示] 开始更新境界显示...');
            updateRealmDisplay();
            console.log('🔄 [显示] 开始更新修炼状态...');
            await updateCultivationStatus();
            console.log('✅ [显示] updateDisplay 执行完成');
        }

        // 更新境界显示
        function updateRealmDisplay() {
            console.log('🎯 [境界] updateRealmDisplay 开始执行');
            console.log('🎯 [境界] realmInfo:', window.cultivationData.realmInfo);
            
            if (!window.cultivationData.realmInfo || !window.cultivationData.realmInfo.current_realm) {
                console.warn('⚠️ [显示] 境界数据为空');
                console.warn('⚠️ [显示] realmInfo:', window.cultivationData.realmInfo);
                return;
            }
            
            const realm = window.cultivationData.realmInfo.current_realm;
            console.log('🎯 [境界] 当前境界数据:', realm);
            
            // 更新境界名称
            const currentRealmEl = document.getElementById('currentRealm');
            if (currentRealmEl) {
                const realmName = realm.realm_name || '开光期';
                currentRealmEl.textContent = realmName;
                console.log('🎯 [境界] 更新境界名称:', realmName);
            } else {
                console.error('❌ [境界] 找不到 currentRealm 元素');
            }
            
            // 更新灵气进度（用于突破）
            const currentQi = parseInt(realm.current_qi) || 0;
            const requiredQi = parseInt(realm.exp_required) || 1000;
            const progress = Math.min((currentQi / requiredQi) * 100, 100);
            
            console.log('🎯 [境界] 灵气进度:', { currentQi, requiredQi, progress });
            
            const progressFill = document.getElementById('progressFill');
            if (progressFill) {
                progressFill.style.width = `${progress}%`;
                console.log('🎯 [境界] 更新进度条:', progress + '%');
            } else {
                console.error('❌ [境界] 找不到 progressFill 元素');
            }
            
            const progressText = document.getElementById('progressText');
            if (progressText) {
                const progressTextValue = `${currentQi}/${requiredQi}`;
                progressText.textContent = progressTextValue;
                console.log('🎯 [境界] 更新进度文本:', progressTextValue);
            } else {
                console.error('❌ [境界] 找不到 progressText 元素');
            }
            
            // 更新突破成功率
            const breakthroughRate = window.cultivationData.realmInfo.breakthrough_rate || 50;
            const breakthroughRateEl = document.getElementById('successRate');
            if (breakthroughRateEl) {
                // 🔧 确保显示为整数
                const roundedRate = Math.round(breakthroughRate);
                breakthroughRateEl.textContent = `${roundedRate}%`;
                console.log('🎯 [境界] 更新突破成功率:', roundedRate + '%');
            } else {
                console.error('❌ [境界] 找不到 successRate 元素');
            }
            
            console.log('✅ [境界] 境界显示已更新');
        }

        // 更新修炼状态显示
        async function updateCultivationStatus() {
            console.log('⚡ [状态] updateCultivationStatus 开始执行');
            
            try {
                // 从API获取当前功法信息
                const response = await fetch(window.GameConfig ? window.GameConfig.getApiUrl('cultivation.php?action=get_attributes') : '../src/api/cultivation.php?action=get_attributes');
                const data = await response.json();
                
                if (!data.success || !data.attributes) {
                    console.error('❌ [状态] 获取功法数据失败');
                    return;
                }
                
                let currentTechniqueName = '凝气决';
                let currentTechniqueLevel = 1;
                
                if (data.attributes.cultivation_techniques && data.attributes.current_technique) {
                    try {
                        // 解析功法数据
                        let techniques = {};
                        if (typeof data.attributes.cultivation_techniques === 'string') {
                            techniques = JSON.parse(data.attributes.cultivation_techniques);
                        } else {
                            techniques = data.attributes.cultivation_techniques;
                        }
                        
                        const currentTechniqueId = data.attributes.current_technique;
                        if (techniques[currentTechniqueId]) {
                            currentTechniqueName = techniques[currentTechniqueId].name || '凝气决';
                            currentTechniqueLevel = techniques[currentTechniqueId].level || 1;
                        }
                        
                        console.log('⚡ [状态] 当前功法ID:', currentTechniqueId);
                        console.log('⚡ [状态] 当前功法名称:', currentTechniqueName);
                        console.log('⚡ [状态] 当前功法等级:', currentTechniqueLevel);
                        
                    } catch (parseError) {
                        console.error('❌ [状态] 解析功法数据失败:', parseError);
                    }
                }
                
                // 更新当前功法显示
                const currentTechniqueEl = document.getElementById('currentTechnique');
                if (currentTechniqueEl) {
                    currentTechniqueEl.textContent = currentTechniqueName;
                    console.log('⚡ [状态] 更新当前功法显示:', currentTechniqueName);
                } else {
                    console.error('❌ [状态] 找不到 currentTechnique 元素');
                }
                
                // 🔧 修复：更新总效率（包含功法+境界+地图）
                const techniqueBonusEl = document.getElementById('techniqueBonus');
                if (techniqueBonusEl) {
                    const efficiencyData = await calculateAllEfficiencyBonus();
                    const totalBonus = efficiencyData.total;
                    const bonusText = `+${totalBonus}%`;
                    techniqueBonusEl.textContent = bonusText;
                    console.log('⚡ [状态] 更新总效率:', bonusText);
                    console.log('⚡ [状态] 效率详情:', efficiencyData);
                } else {
                    console.error('❌ [状态] 找不到 techniqueBonus 元素');
                }
                
                // 更新下次获得修为
                const nextQiGainEl = document.getElementById('nextQiGain');
                if (nextQiGainEl) {
                    // 🔧 使用新的修为计算公式
                    const qiCalculation = await calculateTotalQiGain();
                    nextQiGainEl.textContent = qiCalculation.finalQiGain.toString();
                    console.log('⚡ [状态] 更新下次获得修为:', qiCalculation.finalQiGain);
                    console.log('⚡ [状态] 修为计算明细:', qiCalculation);
                } else {
                    console.error('❌ [状态] 找不到 nextQiGain 元素');
                }
                
            } catch (error) {
                console.error('❌ [状态] 更新修炼状态失败:', error);
            }
            
            console.log('✅ [状态] 修炼状态显示已更新');
        }

        // 更新按钮状态
        function updateButtonStates() {
            console.log('🔘 [按钮] updateButtonStates 开始执行');
            
            // 🆕 检查突破冷却时间
            const now = Date.now();
            const timeSinceLastBreakthrough = now - lastBreakthroughTime;
            const isInCooldown = timeSinceLastBreakthrough < BREAKTHROUGH_COOLDOWN;
            
            console.log('🔘 [按钮] 突破状态检查:', {
                isBreakthroughInProgress,
                isInCooldown,
                timeSinceLastBreakthrough,
                BREAKTHROUGH_COOLDOWN
            });
            
            // 🆕 如果正在突破或在冷却时间内，禁用突破相关按钮
            if (isBreakthroughInProgress || isInCooldown) {
                // 🔧 修复：只禁用突破按钮，功法选择按钮保持可用
                const breakthroughBtn = document.querySelector('.btn-breakthrough');
                if (breakthroughBtn) {
                    breakthroughBtn.disabled = true;
                    breakthroughBtn.style.opacity = '0.5';
                    breakthroughBtn.style.cursor = 'not-allowed';
                }
                
                // 🔧 修复：确保功法选择按钮始终可用
                const techniqueBtn = document.querySelector('.btn-technique');
                if (techniqueBtn) {
                    techniqueBtn.disabled = false;
                    techniqueBtn.style.opacity = '';
                    techniqueBtn.style.cursor = '';
                }
                
                if (isInCooldown) {
                    const remainingTime = Math.ceil((BREAKTHROUGH_COOLDOWN - timeSinceLastBreakthrough) / 1000);
                    showMessage(`突破冷却中，请等待 ${remainingTime} 秒`, 'info');
                }
                
                console.log('🔘 [按钮] 突破进行中或冷却中，突破按钮已禁用，功法按钮保持可用');
                return;
            }
            
                        // 🆕 恢复按钮基础状态
            const allButtons = document.querySelectorAll('.action-button, .technique-button, .breakthrough-button');
            allButtons.forEach(btn => {
                btn.style.opacity = '';
                btn.style.cursor = '';
                // 不直接设置disabled=false，而是根据具体按钮类型来判断
            });

            // 🆕 魂力状态检查（如果魂力受损，显示养魂丹按钮）
            // 🔧 修复：实时检查魂力状态，不依赖缓存数据
            const currentSoulStatus = window.cultivationData?.soulStatus;
            const isSoulDamaged = currentSoulStatus?.is_damaged === true;
            
            console.log('🔘 [按钮] 魂力状态检查:', {
                hasSoulStatus: !!currentSoulStatus,
                isDamaged: isSoulDamaged,
                currentPower: currentSoulStatus?.current_power,
                soulStatus: currentSoulStatus
            });
            
            if (isSoulDamaged) {
                console.log('🩸 [按钮] 魂力受损，显示养魂丹按钮');
                // 🔧 修复：使用更通用的选择器
                const breakthroughBtn = document.querySelector('.btn-breakthrough') || document.querySelector('.soul-pill-button');
                if (!breakthroughBtn) {
                    console.error('❌ [按钮] 找不到按钮元素');
                    return;
                }
                
                console.log('🩸 [按钮] 找到按钮元素，当前class:', breakthroughBtn.className);
                
                // 改为养魂丹按钮
                breakthroughBtn.innerHTML = `
                    <div class="action-icon">🩸</div>
                    <div class="action-text">使用养魂丹</div>
                    <div class="action-description">修复受损魂力</div>
                `;
                breakthroughBtn.className = 'action-button soul-pill-button';
                breakthroughBtn.disabled = false;
                breakthroughBtn.onclick = openSoulPillModal;
                console.log('🩸 [按钮] 养魂丹按钮已设置');
                return;
            } else {
                console.log('💚 [按钮] 魂力正常，显示境界突破按钮');
            }

            // 正常渡劫突破按钮
            // 🔧 修复：使用更通用的选择器，因为按钮class可能已经被改为soul-pill-button
            const breakthroughBtn = document.querySelector('.btn-breakthrough') || document.querySelector('.soul-pill-button');
            if (breakthroughBtn) {
                console.log('🔘 [按钮] 找到按钮元素，当前class:', breakthroughBtn.className);
                
                breakthroughBtn.innerHTML = `
                    <div class="action-icon">🌟</div>
                    <div class="action-text">境界突破</div>
                    <div class="action-description">尝试突破到下一境界</div>
                `;
                breakthroughBtn.className = 'action-button btn-breakthrough';
                breakthroughBtn.onclick = openBreakthroughModal;
                
                // 检查是否可以突破
                const canBreakthrough = window.cultivationData && window.cultivationData.realmInfo && window.cultivationData.realmInfo.can_breakthrough;
                breakthroughBtn.disabled = !canBreakthrough;
                console.log('🔘 [按钮] 渡劫突破按钮已更新:', canBreakthrough ? '可用' : '禁用');
            } else {
                console.error('❌ [按钮] 找不到突破按钮元素');
            }

            // 🔧 修复：恢复其他按钮的正常状态
            const techniqueBtn = document.querySelector('.btn-technique');
            if (techniqueBtn) {
                techniqueBtn.disabled = false; // 功法选择按钮始终可用
            }
        }

        // 打开功法选择弹窗
        function openTechniqueModal() {
            console.log('📖 [功法] 打开功法选择弹窗');
            generateTechniqueList();
            const modal = document.getElementById('techniqueModal');
            if (modal) {
                modal.style.display = 'flex';
                console.log('📖 [功法] 弹窗已显示');
            } else {
                console.error('❌ [功法] 找不到功法弹窗元素');
            }
        }

        // 关闭功法选择弹窗
        function closeTechniqueModal() {
            document.getElementById('techniqueModal').style.display = 'none';
            selectedTechnique = null;
        }

        // 生成功法列表
        async function generateTechniqueList() {
            const techniqueList = document.getElementById('techniqueList');
            if (!techniqueList) {
                console.error('❌ [功法] 找不到功法列表元素');
                return;
            }
            
            techniqueList.innerHTML = '<div class="loading-message">正在加载功法...</div>';
            
            try {
                // 🔧 修复：修改API调用路径
                const response = await fetch(window.GameConfig ? window.GameConfig.getApiUrl('cultivation.php?action=get_attributes') : '../src/api/cultivation.php?action=get_attributes');
                const data = await response.json();
                
                console.log('📖 [功法] API响应数据:', data);
                
                if (!data.success || !data.attributes) {
                    console.error('❌ [功法] 获取功法数据失败:', data.message || '未知错误');
                    techniqueList.innerHTML = '<div class="error-message">获取功法数据失败</div>';
                    return;
                }
                
                let realTechniques = {};
                let currentTechnique = 'main';
                
                // 解析功法数据
                if (data.attributes.cultivation_techniques) {
                if (typeof data.attributes.cultivation_techniques === 'string') {
                        try {
                    realTechniques = JSON.parse(data.attributes.cultivation_techniques);
                        } catch (e) {
                            console.error('❌ [功法] 解析功法JSON失败:', e);
                            realTechniques = {};
                        }
                } else {
                    realTechniques = data.attributes.cultivation_techniques;
                    }
                }
                
                // 获取当前功法
                currentTechnique = data.attributes.current_technique || 'main';
                
                console.log('📖 [功法] 从API获取的功法数据:', realTechniques);
                console.log('📖 [功法] 当前使用的功法:', currentTechnique);
            
            techniqueList.innerHTML = '';
            
                // 确保有基础功法数据
                if (Object.keys(realTechniques).length === 0) {
                    realTechniques[currentTechnique] = {
                        name: '凝气决',
                        level: 1,
                        exp: 0,
                        type: 'basic',
                        unlocked: true
                    };
                }
                
                // 生成功法列表
                Object.keys(realTechniques).forEach(techniqueId => {
                    const techniqueData = realTechniques[techniqueId];
                    
                    const techniqueItem = document.createElement('div');
                    // 检查是否为当前装备的功法
                    const isCurrentTechnique = techniqueId === currentTechnique;
                    techniqueItem.className = `technique-item unlocked ${isCurrentTechnique ? 'current-equipped' : ''}`;
                    
                    // 计算功法效率
                    const efficiency = getTechniqueEfficiencyBonus(techniqueData.name, techniqueData.level);
                    
                    // 计算升级所需经验
                    const currentExp = techniqueData.exp || 0;
                    const maxExp = techniqueData.level * 100; // 每级需要 等级*100 经验
                    
                    // 为当前装备的功法添加标识
                    const equippedBadge = isCurrentTechnique ? '<span class="equipped-badge">已装备</span>' : '';
                    
                    // 🔧 修复：功法等级达到圆满后不显示进度，显示MAX
                    let progressDisplay;
                    if (techniqueData.level >= 9) {
                        progressDisplay = 'MAX';
                    } else {
                        progressDisplay = `${currentExp}/${maxExp}`;
                    }
                    
                    techniqueItem.innerHTML = `
                        <div class="technique-header">
                            <div class="technique-name">${techniqueData.name}</div>
                            ${equippedBadge}
                        </div>
                        <div class="technique-description">${formatTechniqueLevel(techniqueData.level)} • 进度 ${progressDisplay}</div>
                        <div class="technique-effects">基础修为: ${getTechniqueBaseQi(techniqueData.name, techniqueData.level)}点 • 修炼效率: +${efficiency}%</div>
                    `;
                    
                    // 添加点击事件
                    techniqueItem.addEventListener('click', function() {
                        selectTechnique({
                            id: techniqueId,
                            name: techniqueData.name,
                            description: `${formatTechniqueLevel(techniqueData.level)}`,
                            effects: `基础修为: ${getTechniqueBaseQi(techniqueData.name, techniqueData.level)}点 • 修炼效率: +${efficiency}%`,
                            cost: 0,
                            unlocked: true,
                            isCurrentTechnique: isCurrentTechnique
                        });
                    });
                    techniqueItem.style.cursor = 'pointer';
                    
                    techniqueList.appendChild(techniqueItem);
                });
            
            console.log('📖 [功法] 功法列表已生成，项目数:', Object.keys(realTechniques).length);
                
            } catch (error) {
                console.error('❌ [功法] 生成功法列表失败:', error);
                techniqueList.innerHTML = `<div class="error-message">加载功法列表失败: ${error.message}</div>`;
            }
        }

        // 选择功法
        function selectTechnique(technique) {
            // 移除之前的选中状态
            document.querySelectorAll('.technique-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            // 添加选中状态 - 通过technique查找对应的元素
            const techniqueItems = document.querySelectorAll('.technique-item');
            techniqueItems.forEach(item => {
                const nameEl = item.querySelector('.technique-name');
                if (nameEl && nameEl.textContent === technique.name) {
                    item.classList.add('selected');
                }
            });
            
            selectedTechnique = technique;
            
            // 启用确认按钮
            const confirmBtn = document.querySelector('.technique-confirm');
            if (confirmBtn) {
                confirmBtn.disabled = false;
            }
        }

        // 确认功法切换
        async function confirmTechniqueChange() {
            if (!selectedTechnique) {
                showMessage('请选择一个功法', 'error');
                return;
            }
            
            // 检查是否选择的是当前已装备的功法
            if (selectedTechnique.isCurrentTechnique) {
                showMessage(`${selectedTechnique.name} 已经是当前装备的功法`, 'info');
                return;
            }
            
            try {
                // 检查是否需要消耗资源
                if (selectedTechnique.cost > 0 && !selectedTechnique.unlocked) {
                    const response = await fetch(window.GameConfig ? window.GameConfig.getApiUrl('cultivation.php?action=learn_technique') : '../src/api/cultivation.php?action=learn_technique', {
                    method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            technique_id: selectedTechnique.id,
                            cost: selectedTechnique.cost,
                            cost_type: selectedTechnique.costType
                        })
                    });
                    
                const data = await response.json();
                
                    if (!data.success) {
                    showMessage(data.message, 'error');
                        return;
                    }
                    
                    // 解锁功法
                    techniques[selectedTechnique.id].unlocked = true;
                }
                
                // 调用功法切换API
                const switchResponse = await fetch(window.GameConfig ? window.GameConfig.getApiUrl('cultivation.php?action=switch_technique') : '../src/api/cultivation.php?action=switch_technique', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        technique_id: selectedTechnique.id
                    })
                });
                
                const switchData = await switchResponse.json();
                
                if (switchData.success) {
                    // 更新本地数据
                    window.cultivationData.currentTechnique = selectedTechnique.id;
                    
                    // 更新显示
                    await updateCultivationStatus();
                    
                    showMessage(`成功切换到 ${selectedTechnique.name}`, 'success');
                    closeTechniqueModal();
                } else {
                    showMessage(switchData.message || '功法切换失败', 'error');
                }
                
            } catch (error) {
                console.error('❌ [功法] 切换失败:', error);
                showMessage('功法切换失败', 'error');
            }
        }

        // 打开渡劫确认弹窗
        function openBreakthroughModal() {
            // 🆕 检查突破冷却时间
            const now = Date.now();
            const timeSinceLastBreakthrough = now - lastBreakthroughTime;
            if (timeSinceLastBreakthrough < BREAKTHROUGH_COOLDOWN) {
                const remainingTime = Math.ceil((BREAKTHROUGH_COOLDOWN - timeSinceLastBreakthrough) / 1000);
                showMessage(`突破冷却中，请等待 ${remainingTime} 秒`, 'error');
                return;
            }
            
            // 🆕 检查是否正在突破
            if (isBreakthroughInProgress) {
                showMessage('突破正在进行中，请等待完成', 'error');
                console.log('⚠️ [渡劫] 突破正在进行中，阻止重复渡劫');
                return;
            }
            
            if (!window.cultivationData.realmInfo || !window.cultivationData.realmInfo.can_breakthrough) {
                showMessage('当前修为不足，无法突破境界', 'error');
                return;
            }
            
            showBreakthroughModal();
        }

        // 显示渡劫弹窗
        function showBreakthroughModal() {
            const modal = document.getElementById('breakthroughModal');
            
            if (!window.cultivationData.realmInfo) {
                showMessage('修炼数据未加载', 'error');
                return;
            }
            
            const realm = window.cultivationData.realmInfo.current_realm;
            const nextRealm = window.cultivationData.realmInfo.next_realm;
            
            // 更新弹窗信息
            const currentRealmEl = document.getElementById('currentRealmText');
            if (currentRealmEl) {
                currentRealmEl.textContent = realm ? realm.realm_name : '开光期一阶';
            }
            
            const targetRealmEl = document.getElementById('targetRealmText');
            if (targetRealmEl) {
                targetRealmEl.textContent = nextRealm ? nextRealm.realm_name : '下一境界';
            }
            
            const baseRate = Math.round(window.cultivationData.realmInfo.breakthrough_rate || 50);
            const baseRateEl = document.getElementById('baseSuccessRate');
            if (baseRateEl) {
                baseRateEl.textContent = baseRate + '%';
            }
            
            // 🔧 修复：计算最终成功率（包含累计的渡劫丹加成）
            const finalRate = Math.min(baseRate + tribulationSuccessRateBonus, 100);
            const finalRateEl = document.getElementById('finalSuccessRate');
            if (finalRateEl) {
                finalRateEl.textContent = finalRate + '%';
                // 🔧 修复：如果成功率达到100%，显示特殊样式
                if (finalRate >= 100) {
                    finalRateEl.style.color = '#00ff00';
                    finalRateEl.style.fontWeight = 'bold';
                } else {
                    finalRateEl.style.color = '';
                    finalRateEl.style.fontWeight = '';
                }
            }
            
            // 生成渡劫丹列表
            generatePillList();
            
            // 🔧 修复：更新累计加成显示
            const bonusDisplay = document.getElementById('currentTribulationBonus');
            if (bonusDisplay) {
                bonusDisplay.textContent = `+${Math.round(tribulationSuccessRateBonus)}%`;
            }
            
            modal.style.display = 'flex';
        }

        // 生成丹药列表
        async function generatePillList() {
            const pillList = document.getElementById('tribulationPillList');
            pillList.innerHTML = '<div class="no-pills-message">正在加载丹药...</div>';
            
            try {
                // 从API获取可用丹药
                const response = await fetch(window.GameConfig ? window.GameConfig.getApiUrl('cultivation.php?action=get_pills') : '../src/api/cultivation.php?action=get_pills');
                const data = await response.json();
                
                if (data.success && data.pills) {
                    pillList.innerHTML = '';
                    
                    if (data.pills.length === 0) {
                        pillList.innerHTML = '<div class="no-pills-message">🈚 当前境界无可用渡劫丹</div>';
                        return;
                    }
                    
                    // 🆕 初始化当前成功率累加器
                    let currentSuccessRateBonus = 0;
                    
                    // 添加可用渡劫丹
                    data.pills.forEach(pill => {
                        const pillItem = document.createElement('div');
                        pillItem.className = 'pill-item';
                        pillItem.style.cssText = `
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            padding: 12px;
                            margin-bottom: 8px;
                            background: rgba(255, 255, 255, 0.1);
                            border-radius: 8px;
                            border: 1px solid rgba(156, 39, 176, 0.3);
                        `;
                        
                        // 🔧 修复：计算丹药效果（使用后端返回的值）
                        const effectPercent = pill.success_rate_bonus || 5;
                        
                        pillItem.innerHTML = `
                            <div class="pill-left" style="flex: 1;">
                                <div class="pill-name" style="font-size: 12px; color: #fff; font-weight: bold; margin-bottom: 2px;">
                                    ${pill.item_name}
                                </div>
                                <div class="pill-effect" style="font-size: 10px; color: #4CAF50;">
                                    ${pill.effect_description || '渡劫成功率 +' + effectPercent + '%'}
                                </div>
                            </div>
                            <div class="pill-count" style="font-size: 11px; color: #ffd700; font-weight: bold; margin-right: 10px;">
                                ×${pill.quantity}
                            </div>
                            <button class="use-pill-btn" 
                                    data-pill-id="${pill.item_id}" 
                                    data-pill-name="${pill.item_name}"
                                    data-success-rate="${effectPercent}"
                                    style="
                                        background: linear-gradient(135deg, #9c27b0, #7b1fa2);
                                        color: white;
                                        border: none;
                                        border-radius: 6px;
                                        padding: 4px 8px;
                                        font-size: 10px;
                                        cursor: pointer;
                                        transition: all 0.3s ease;
                                    "
                                    onclick="useTribulationPill(this)">
                                使用
                            </button>
                        `;
                        
                        pillList.appendChild(pillItem);
                    });
                    
                    // 🆕 添加成功率显示区域
                    const successRateDisplay = document.createElement('div');
                    successRateDisplay.id = 'tribulationSuccessRateDisplay';
                    successRateDisplay.style.cssText = `
                        margin-top: 15px;
                        padding: 10px;
                        background: rgba(76, 175, 80, 0.2);
                        border-radius: 8px;
                        border: 1px solid rgba(76, 175, 80, 0.4);
                        text-align: center;
                    `;
                    successRateDisplay.innerHTML = `
                        <div style="font-size: 11px; color: #bdc3c7; margin-bottom: 3px;">当前累计加成</div>
                        <div id="currentTribulationBonus" style="font-size: 14px; color: #4CAF50; font-weight: bold;">+0%</div>
                    `;
                    pillList.appendChild(successRateDisplay);
                    
                    console.log('🩸 [渡劫丹] 渡劫丹列表已生成，项目数:', data.pills.length);
                    
                } else {
                    console.error('获取渡劫丹失败:', data);
                    pillList.innerHTML = '<div class="no-pills-message">❌ 加载渡劫丹失败</div>';
                }
                
            } catch (error) {
                console.error('获取渡劫丹异常:', error);
                pillList.innerHTML = '<div class="no-pills-message">🌐 网络连接异常</div>';
            }
        }
        
        // 从丹药名称计算效果
        function getPillEffectFromName(pillName) {
            if (pillName.includes('渡劫丹')) {
                const gradeMatch = pillName.match(/([一二三四五六七八九])品/);
                if (gradeMatch) {
                    const gradeMap = { '一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6, '七': 7, '八': 8, '九': 9 };
                    const grade = gradeMap[gradeMatch[1]] || 1;
                    return grade * 2; // 每品级+2%
                }
                return 10; // 默认10%
            }
            return 0;
        }

        // 选择丹药
        function selectPill(pill) {
            // 移除之前的选中状态
            document.querySelectorAll('.pill-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            // 添加选中状态 - 通过pill查找对应的元素
            const pillItems = document.querySelectorAll('.pill-item');
            pillItems.forEach(item => {
                const nameEl = item.querySelector('.pill-name');
                if (nameEl && nameEl.textContent === pill.name) {
                    item.classList.add('selected');
                }
            });
            
            selectedPill = pill;
            
            // 更新最终成功率
            const baseRate = Math.round(window.cultivationData.realmInfo ? window.cultivationData.realmInfo.breakthrough_rate : 50);
            const pillBonus = parseInt(pill.effect.replace('%', '').replace('+', ''));
            const finalRate = Math.min(baseRate + pillBonus, 95);
            
            const finalRateEl = document.getElementById('finalSuccessRate');
            if (finalRateEl) {
                finalRateEl.textContent = Math.round(finalRate) + '%';
            }
        }

        // 关闭渡劫弹窗
        function closeBreakthroughModal() {
            document.getElementById('breakthroughModal').style.display = 'none';
            selectedPill = null;
        }

        // 🆕 使用渡劫丹的功能
        let tribulationSuccessRateBonus = 0; // 当前累计的渡劫成功率加成
        
        async function useTribulationPill(button) {
            const pillId = button.getAttribute('data-pill-id');
            const pillName = button.getAttribute('data-pill-name');
            const successRate = parseInt(button.getAttribute('data-success-rate'));
            
            try {
                console.log('🩸 [渡劫丹] 使用渡劫丹:', { pillId, pillName, successRate });
                
                // 调用后端API使用渡劫丹
                const response = await fetch(window.GameConfig ? window.GameConfig.getApiUrl('cultivation.php?action=use_tribulation_pill') : '../src/api/cultivation.php?action=use_tribulation_pill', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ 
                        pill_id: pillId,
                        pill_name: pillName,
                        success_rate_bonus: successRate
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    // 🔧 修复：累加成功率，允许渡劫丹加成到100%
                    const baseRate = Math.round(window.cultivationData.realmInfo ? window.cultivationData.realmInfo.breakthrough_rate : 50);
                    tribulationSuccessRateBonus += successRate;
                    
                    // 更新显示
                    const bonusDisplay = document.getElementById('currentTribulationBonus');
                    if (bonusDisplay) {
                        bonusDisplay.textContent = `+${Math.round(tribulationSuccessRateBonus)}%`;
                    }
                    
                    // 更新最终成功率显示（允许超过100%但限制显示为100%）
                    const finalRate = Math.min(baseRate + tribulationSuccessRateBonus, 100);
                    const finalRateEl = document.getElementById('finalSuccessRate');
                    if (finalRateEl) {
                        finalRateEl.textContent = Math.round(finalRate) + '%';
                        // 🔧 修复：如果成功率达到100%，显示特殊样式
                        if (finalRate >= 100) {
                            finalRateEl.style.color = '#00ff00';
                            finalRateEl.style.fontWeight = 'bold';
                        }
                    }
                    
                    // 减少丹药数量显示
                    const pillItem = button.closest('.pill-item');
                    const countEl = pillItem.querySelector('.pill-count');
                    if (countEl) {
                        const currentCount = parseInt(countEl.textContent.replace('×', ''));
                        const newCount = Math.max(0, currentCount - 1);
                        countEl.textContent = `×${newCount}`;
                        
                        // 如果数量为0，禁用按钮
                        if (newCount <= 0) {
                            button.disabled = true;
                            button.style.opacity = '0.5';
                            button.style.cursor = 'not-allowed';
                            button.textContent = '已用完';
                        }
                    }
                    
                    showMessage(`${data.message}，当前累计加成: +${tribulationSuccessRateBonus}%`, 'success');
                    
                } else {
                    showMessage(data.message || '使用渡劫丹失败', 'error');
                }
                
            } catch (error) {
                console.error('❌ [渡劫丹] 使用失败:', error);
                showMessage('使用渡劫丹失败', 'error');
            }
        }

        // 🔧 修复：更新selectPill函数为可选的（保持兼容性）
        function selectPill(pill) {
            // 这个函数现在主要用于兼容，实际使用渡劫丹通过按钮完成
            if (pill) {
                selectedPill = pill;
                console.log('选择渡劫丹（兼容模式）:', pill.name);
            } else {
                selectedPill = null;
                console.log('取消选择渡劫丹');
            }
        }

        // 🔧 修复：确认渡劫时应用累计的成功率加成
        async function confirmBreakthrough() {
            closeBreakthroughModal();
            
            // 🆕 将累计的成功率加成传递给渡劫API
            await performBreakthrough(tribulationSuccessRateBonus);
        }

        // 执行渡劫突破
        async function performBreakthrough(successRateBonus = 0) {
            try {
                // 🆕 设置突破状态
                isBreakthroughInProgress = true;
                lastBreakthroughTime = Date.now();
                updateButtonStates(); // 立即更新按钮状态
                
                // 🔧 修复：确保闪电完成标志初始化为false
                window.tribulationLightningComplete = false;
                
                console.log('🚀 [渡劫] 开始渡劫突破，成功率加成:', successRateBonus);
                
                // 暂停自动修炼
                if (cycleInterval) {
                    clearInterval(cycleInterval);
                }
                
                // 🆕 显示渡劫特效
                showTribulationEffect();
                
                // 🆕 添加终极保险机制：如果动画系统完全失效，8秒后强制完成
                const ultimateFailsafe = setTimeout(() => {
                    console.error('🚨 [渡劫] 终极保险机制触发！动画系统可能完全失效');
                    window.tribulationLightningComplete = true;
                }, 8000);
                
                // 🔧 修复：传递累计的成功率加成
                const response = await fetch(window.GameConfig ? window.GameConfig.getApiUrl('cultivation.php?action=breakthrough') : '../src/api/cultivation.php?action=breakthrough', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        tribulation_success_rate_bonus: successRateBonus
                    })
                });
                
                const data = await response.json();
                
                            // 🔧 修复：等待所有闪电完成后再显示结果，添加超时保护和强制完成机制
            const waitForLightningComplete = () => {
                return new Promise((resolve) => {
                    let timeoutCount = 0;
                    const maxTimeout = 50; // 最多等待5秒 (50 * 100ms)，进一步缩短等待时间
                    
                    const checkComplete = () => {
                        if (window.tribulationLightningComplete) {
                            console.log('⚡ [渡劫] 闪电特效正常完成');
                            resolve();
                        } else if (timeoutCount >= maxTimeout) {
                            console.warn('⚠️ [渡劫] 闪电特效超时，强制完成');
                            resolve();
                        } else {
                            timeoutCount++;
                            setTimeout(checkComplete, 100); // 每100ms检查一次
                        }
                    };
                    
                    // 🆕 立即检查一次，如果已经完成就直接返回
                    if (window.tribulationLightningComplete) {
                        console.log('⚡ [渡劫] 闪电特效已完成，立即返回');
                        resolve();
                        return;
                    }
                    
                    checkComplete();
                });
            };
                
                // 等待闪电特效完成
                await waitForLightningComplete();
                
                // 🆕 清除终极保险机制
                clearTimeout(ultimateFailsafe);
                console.log('⚡ [渡劫] 准备显示结果');
                
                if (data.success) {
                    if (data.breakthrough_success) {
                        showTribulationResult('success', data.message);
                        // 🆕 渡劫成功后重置累计加成
                        tribulationSuccessRateBonus = 0;
                        
                        // 🔥 显示属性变化弹窗 - 境界突破全属性+2
                        // 显示固定的境界突破属性变化（不需要API获取，直接显示+2）
                        const oldStats = {}; // 空的旧属性
                        const newStats = {
                            physique: 2,
                            comprehension: 2,
                            constitution: 2,
                            spirit: 2,
                            agility: 2
                        };
                        
                        // 显示属性变化弹窗
                        setTimeout(() => {
                            showAttributeChanges(oldStats, newStats, {
                                title: '境界突破成功！',
                                description: '全属性各+2点'
                            });
                        }, 2000); // 等待突破结果显示后再显示
                        
                    } else {
                        showTribulationResult('failure', data.message);
                        // 🆕 渡劫失败后也重置累计加成
                        tribulationSuccessRateBonus = 0;
                    }
                    
                    // 🔧 修改：等待结果显示1秒后再加载数据
                    setTimeout(async () => {
                        await loadCultivationData(); // 重新加载数据
                        await checkSoulStatus(); // 🆕 立即检查魂力状态
                    }, 1000); // 结果显示1秒后加载数据
                } else {
                    showTribulationResult('failure', data.message);
                }
                
            } catch (error) {
                console.error('❌ [渡劫突破] 失败:', error);
                hideTribulationEffect();
                showTribulationResult('failure', '渡劫突破失败');
            } finally {
                // 🆕 重置突破状态 - 确保无论如何都会重置
                console.log('🔄 [渡劫] 重置突破状态和相关标志');
                isBreakthroughInProgress = false;
                
                // 🔧 修复：延迟重置闪电标志，确保结果显示完成后再重置
                setTimeout(() => {
                    window.tribulationLightningComplete = false;
                    console.log('🔄 [渡劫] 闪电完成标志已重置');
                }, 1000);
                
                // 🔧 修复：确保正确恢复按钮状态
                setTimeout(() => {
                    updateButtonStates(); // 恢复按钮状态
                    console.log('🔘 [渡劫] 按钮状态已恢复');
                }, 100);
                
                // 重新开始自动修炼
                startCultivationCycle();
                selectedPill = null;
                // 🆕 重置累计加成显示
                const bonusDisplay = document.getElementById('currentTribulationBonus');
                if (bonusDisplay) {
                    bonusDisplay.textContent = '+0%';
                }
            }
        }

        // 🔧 移除：不再需要获取角色属性，直接显示固定的+2变化

        // 🆕 显示渡劫特效 - 美化增强版本
        function showTribulationEffect() {
            console.log('🌩️ [渡劫] 开始显示渡劫特效');
            
            // 🔧 修复：更新渡劫状态
            tribulationAnimationState.isActive = true;
            tribulationAnimationState.lightningComplete = false;
            
            // 🔧 修复：先清理之前的渡劫特效，避免冲突
            const existingOverlay = document.getElementById('tribulationOverlay');
            if (existingOverlay) {
                console.log('🧹 [渡劫] 清理之前的渡劫特效');
                existingOverlay.remove();
                tribulationAnimationState.overlayExists = false;
            }
            
            // 🔧 修复：重置闪电完成标志，确保每次渡劫都是全新状态
            window.tribulationLightningComplete = false;
            console.log('🔄 [渡劫] 重置闪电完成标志');
            
            // 🔧 修复：强制创建新的遮罩层，确保每次都能显示
            const overlay = document.createElement('div');
            overlay.id = 'tribulationOverlay';
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: linear-gradient(45deg, rgba(0,0,0,0.9), rgba(20,20,50,0.95));
                z-index: 9999;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                color: white;
                font-size: 24px;
                font-weight: bold;
            `;
            
            // 🔧 修复：立即添加到DOM，确保遮罩层存在
            document.body.appendChild(overlay);
            tribulationAnimationState.overlayExists = true;
            console.log('✅ [渡劫] 遮罩层已创建并添加到DOM');
            
            // 创建主雷云
            const mainCloud = document.createElement('div');
            mainCloud.id = 'tribulationCloud';
            mainCloud.style.cssText = `
                position: fixed;
                top: -25px;
                left: 50%;
                transform: translateX(-50%);
                width: 250px;
                height: 50px;
                background: radial-gradient(ellipse at 50% 60%, #555 30%, #333 60%, #111 85%, transparent 100%);
                border-radius: 50% 50% 60% 60%/60% 60% 100% 100%;
                box-shadow: 0 12px 40px 12px rgba(60,60,60,0.6);
                z-index: 10002;
                pointer-events: none;
                opacity: 0;
                animation: cloudAppear 1s ease-out forwards;
            `;
            
            // 创建文字提示
            const text = document.createElement('div');
            text.id = 'tribulationText'; // 🔧 修复：添加ID便于查找
            text.textContent = '⚡ 天劫降临 ⚡';
            text.style.cssText = `
                position: relative;
                z-index: 10000;
                text-align: center;
                text-shadow: 0 0 20px rgba(255,255,255,0.8);
                animation: tribulationPulse 1s infinite alternate;
                margin-top: 80px;
                font-size: 24px;
                font-weight: bold;
                color: white;
            `;
            
            overlay.appendChild(mainCloud);
            overlay.appendChild(text);
            // 🔧 修复：遮罩层已经在前面添加到DOM了，这里不需要重复添加
            
            // 🔧 修复：提高云朵汇聚速度
            setTimeout(() => {
                createSmallClouds(overlay);
            }, 600);
            
            // 🔧 修改：雷云形成后立即隐藏"天劫降临"文字
            setTimeout(() => {
                const textElement = document.getElementById('tribulationText');
                if (textElement) {
                    textElement.style.opacity = '0';
                    textElement.style.transition = 'opacity 0.5s ease-out';
                    console.log('🌩️ [渡劫] "天劫降临"文字已隐藏');
                    
                    // 🆕 完全隐藏后移除元素，避免占用空间
                    setTimeout(() => {
                        if (textElement.parentNode) {
                            textElement.style.display = 'none';
                            console.log('🌩️ [渡劫] "天劫降临"文字已完全移除');
                        }
                    }, 500); // 等待透明度动画完成
                }
            }, 1000); // 雷云形成1秒后隐藏文字
            
            // 🔧 修复：缩短闪电延迟，增加力量感
            setTimeout(() => {
                createEnhancedLightning();
            }, 1800);
        }

        // 🆕 创建小云朵汇聚效果
        function createSmallClouds(overlay) {
            const mainCloud = document.getElementById('tribulationCloud');
            if (!mainCloud) {
                console.error('❌ [小云朵] 找不到主雷云元素');
                return;
            }
            const mainRect = mainCloud.getBoundingClientRect();
            console.log('🌫️ [小云朵] 主雷云位置:', mainRect);
            
            // 固定创建2朵小云朵：左下角和右下角
            const cloudConfigs = [
                {
                    // 左下角小云朵
                    startX: -100,
                    startY: mainRect.top + Math.random() * 10,
                    targetX: mainRect.left - 100, // 贴在大云朵左边缘+20px内侧
                    targetY: Math.max(mainRect.bottom - 15, 20), // 确保在可视区域内，最少距离顶部20px
                    width: 275,
                    height: 48
                },
                {
                    // 下角小云朵
                    startX: 0,
                    startY: mainRect.top,
                    targetX: mainRect.left + 50, // 贴在大云朵左边缘+20px内侧
                    targetY: Math.max(mainRect.bottom - 15, 30), // 确保在可视区域内，最少距离顶部20px
                    width: 260,
                    height: 35
                },
                {
                    // 右下角小云朵
                    startX: window.innerWidth + 100,
                    startY: mainRect.top + Math.random() * 30,
                    targetX: mainRect.right - 20, // 贴在大云朵右边缘-20px内侧
                    targetY: Math.max(mainRect.bottom - 15, 20), // 确保在可视区域内，最少距离顶部20px
                    width: 275,
                    height: 48
                }
            ];
            
            cloudConfigs.forEach((config, i) => {
                console.log(`🌫️ [小云朵${i+1}] 配置:`, config);
                const smallCloud = document.createElement('div');
                smallCloud.className = 'small-cloud';
                
                smallCloud.style.cssText = `
                    position: fixed;
                    top: ${config.startY}px;
                    left: ${config.startX}px;
                    width: ${config.width}px;
                    height: ${config.height}px;
                    background: radial-gradient(ellipse at 50% 60%, #444 40%, #222 70%, transparent 100%);
                    border-radius: 50% 50% 60% 60%/60% 60% 100% 100%;
                    box-shadow: 0 6px 20px 6px rgba(40,40,40,0.4);
                    z-index: 10001;
                    pointer-events: none;
                    opacity: 0.8;
                    animation: cloudGather ${0.7 + i * 0.1}s ease-out forwards;
                    --start-x: ${config.startX}px;
                    --start-y: ${config.startY}px;
                    --target-x: ${config.targetX}px;
                    --target-y: ${config.targetY}px;
                `;
                
                overlay.appendChild(smallCloud);
                console.log(`🌫️ [小云朵${i+1}] 已创建并添加到overlay`);
            });
        }

        // 🆕 创建增强版闪电特效
        function createEnhancedLightning() {
            const overlay = document.getElementById('tribulationOverlay');
            if (!overlay) return;

            // 🔧 修复：清理之前可能残留的闪电SVG元素
            const existingSvgs = overlay.querySelectorAll('svg');
            if (existingSvgs.length > 0) {
                console.log(`🧹 [渡劫闪电] 清理 ${existingSvgs.length} 个残留的SVG元素`);
                existingSvgs.forEach(svg => svg.remove());
            }
            
            // 🔧 修复：确保遮罩层仍然存在
            if (!document.getElementById('tribulationOverlay')) {
                console.error('❌ [渡劫闪电] 遮罩层在闪电创建前丢失！');
                return;
            }

            // 获取雷云位置
            const cloud = document.getElementById('tribulationCloud');
            const cloudRect = cloud.getBoundingClientRect();
            const cloudCenterX = cloudRect.left + cloudRect.width / 2;
            const cloudCenterY = cloudRect.bottom;

            // 获取当前境界等级
            const realmLevel = getCurrentRealmLevel();
            const majorRealm = Math.floor((realmLevel - 1) / 10); // 大境界 (0-27)
            const minorRealm = (realmLevel - 1) % 10 + 1; // 小境界 (1-10)

            console.log(`⚡ [渡劫闪电] 境界等级: ${realmLevel}, 大境界: ${majorRealm}, 小境界: ${minorRealm}`);

            // 🔧 修复：计算闪电数量，主闪电最多9条
            const subLightningCount = minorRealm; // 每个小境界一条副闪电
            const rawMainLightningCount = Math.floor((minorRealm - 1) / 3) + 1; // 每3个小境界一条主闪电
            const mainLightningCount = Math.min(rawMainLightningCount, 9); // 限制最多9条主闪电

            // 获取境界对应的闪电颜色
            const lightningColor = getLightningColor(majorRealm);

            // 创建主闪电
            for (let i = 0; i < mainLightningCount; i++) {
                setTimeout(() => {
                    createMainLightning(overlay, cloudCenterX, cloudCenterY, lightningColor, i, mainLightningCount);
                }, i * 200);
            }

            // 创建副闪电
            for (let i = 0; i < subLightningCount; i++) {
                setTimeout(() => {
                    createSubLightning(overlay, cloudCenterX, cloudCenterY, lightningColor, i, subLightningCount);
                }, 500 + i * 100);
            }
            
            // 🔧 修复：正确计算所有闪电动画完成的时间
            // 主闪电：最后一条出现时间 + 动画总时长(0.3s strike + 3s flicker)
            const lastMainLightningStart = (mainLightningCount - 1) * 200;
            const mainLightningAnimationDuration = 300 + 3000; // 0.3s + 3s
            const mainLightningTotalTime = lastMainLightningStart + mainLightningAnimationDuration;
            
            // 副闪电：最后一条出现时间 + 动画总时长(0.25s strike + 2s flicker)
            const lastSubLightningStart = 500 + (subLightningCount - 1) * 100;
            const subLightningAnimationDuration = 250 + 2000; // 0.25s + 2s
            const subLightningTotalTime = lastSubLightningStart + subLightningAnimationDuration;
            
            // 取两者中的最大值
            const allLightningCompleteTime = Math.max(mainLightningTotalTime, subLightningTotalTime);
            
            console.log(`⚡ [渡劫闪电] 时间计算:
                主闪电: ${mainLightningCount}条, 最后开始: ${lastMainLightningStart}ms, 动画时长: ${mainLightningAnimationDuration}ms, 总计: ${mainLightningTotalTime}ms
                副闪电: ${subLightningCount}条, 最后开始: ${lastSubLightningStart}ms, 动画时长: ${subLightningAnimationDuration}ms, 总计: ${subLightningTotalTime}ms
                所有闪电完成时间: ${allLightningCompleteTime}ms`);
            
            // 🔧 修复：在所有闪电动画完成后设置完成标志，添加保险机制
            setTimeout(() => {
                console.log('⚡ [渡劫闪电] 所有闪电动画完成，设置完成标志');
                window.tribulationLightningComplete = true;
            }, allLightningCompleteTime);
            
            // 🆕 添加保险机制：如果计算有误，最多4秒后强制完成
            setTimeout(() => {
                if (!window.tribulationLightningComplete) {
                    console.warn('⚠️ [渡劫闪电] 保险机制触发，强制设置完成标志');
                    window.tribulationLightningComplete = true;
                }
            }, 4000); // 缩短保险机制时间到4秒
        }

        // 🆕 获取境界对应的闪电颜色（与境界系统一致）
        function getLightningColor(majorRealm) {
            const colors = [
                // 人界境界颜色 (0-13: 开光期到天仙期)
                '#ffffff', // 0: 开光期 - 纯白
                '#c0c0c0', // 1: 灵虚期 - 银白
                '#ffb6c1', // 2: 辟谷期 - 淡粉
                '#87ceeb', // 3: 心动期 - 天蓝
                '#9370db', // 4: 元化期 - 紫罗兰
                '#4169e1', // 5: 元婴期 - 皇家蓝
                '#9932cc', // 6: 离合期 - 暗兰花紫
                '#8a2be2', // 7: 空冥期 - 蓝紫
                '#4b0082', // 8: 寂灭期 - 靛青
                '#800080', // 9: 大乘期 - 紫色
                '#ff4500', // 10: 渡劫期 - 橙红
                '#ffd700', // 11: 凡仙期 - 金色
                '#daa520', // 12: 地仙期 - 金麒麟
                '#ffa500', // 13: 天仙期 - 橙色
                
                // 仙界境界颜色 (14-27: 真仙期到鸿蒙至元期)
                '#00ffff', // 14: 真仙期 - 青色
                '#40e0d0', // 15: 太乙真仙期 - 绿松石
                '#48d1cc', // 16: 太乙金仙期 - 中绿松石
                '#20b2aa', // 17: 太乙玄仙期 - 浅海绿
                '#008b8b', // 18: 大罗真仙期 - 暗青色
                '#5f9ea0', // 19: 大罗金仙期 - 军校蓝
                '#4682b4', // 20: 大罗玄仙期 - 钢蓝
                '#6495ed', // 21: 准圣期 - 矢车菊蓝
                '#7b68ee', // 22: 教主期 - 中石板蓝
                '#9370db', // 23: 混元期 - 中兰花紫
                '#ba55d3', // 24: 混元金仙期 - 中兰花紫
                '#da70d6', // 25: 混元至仙期 - 兰花紫
                '#ee82ee', // 26: 天道期 - 紫罗兰
                '#ff69b4'  // 27: 鸿蒙至元期 - 热粉红
            ];
            
            return colors[Math.min(majorRealm, colors.length - 1)];
        }

        // 🆕 创建主闪电
        function createMainLightning(overlay, cloudCenterX, cloudCenterY, color, index, total) {
            const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
            svg.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                z-index: 10001;
                pointer-events: none;
            `;

            // 🔧 修复：主闪电起点距离主雷云有一定距离
            const offsetRange = 80; // 增加起点间距
            const startX = cloudCenterX + (index - (total - 1) / 2) * (offsetRange / Math.max(total - 1, 1));
            const startY = cloudCenterY;
            
            // 🔧 修复：终点都汇聚到屏幕中心底部，并延伸120px
            const endX = window.innerWidth / 2;
            const endY = window.innerHeight + 120; // 延伸到屏幕外120px

            // 生成更蜿蜒的路径
            const pathData = generateEnhancedLightningPath(startX, startY, endX, endY, true);
            
            const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            path.setAttribute('d', pathData);
            path.setAttribute('stroke', color);
            path.setAttribute('stroke-width', '4');
            path.setAttribute('fill', 'none');
            path.setAttribute('filter', `drop-shadow(0 0 15px ${color}) drop-shadow(0 0 25px ${color})`);
            path.style.cssText = `
                opacity: 0;
                animation: lightningStrike 0.3s ease-out forwards, lightningFlicker 3s 0.3s ease-in-out;
            `;
            
            svg.appendChild(path);
            overlay.appendChild(svg);

            // 🔧 修复：延长清理时间，确保动画完整播放
            setTimeout(() => {
                if (svg && svg.parentNode) {
                    svg.parentNode.removeChild(svg);
                }
            }, 5000); // 增加到5秒，确保3.3s动画完整播放
        }

        // 🆕 创建副闪电
        function createSubLightning(overlay, cloudCenterX, cloudCenterY, color, index, total) {
            const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
            svg.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                z-index: 10000;
                pointer-events: none;
            `;

            // 🔧 修复：副闪电起点距离主雷云有更大距离
            const cloudRange = 150; // 增加副闪电起点间距
            const startX = cloudCenterX + (index - (total - 1) / 2) * (cloudRange / Math.max(total - 1, 1));
            const startY = cloudCenterY;
            
            // 🔧 修复：副闪电终点汇聚到主闪电落点（屏幕中心底部）
            const endX = window.innerWidth / 2;
            const endY = window.innerHeight + 120; // 与主闪电落点一致
            
            // 生成副闪电路径
            const pathData = generateEnhancedLightningPath(startX, startY, endX, endY, false);
            
            const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            path.setAttribute('d', pathData);
            path.setAttribute('stroke', color);
            path.setAttribute('stroke-width', '2');
            path.setAttribute('fill', 'none');
            path.setAttribute('filter', `drop-shadow(0 0 8px ${color})`);
            path.setAttribute('opacity', '0.7');
            path.style.cssText = `
                opacity: 0;
                animation: lightningStrike 0.25s ease-out forwards, lightningFlicker 2s 0.25s ease-in-out;
            `;
            
            svg.appendChild(path);
            overlay.appendChild(svg);

            // 🔧 修复：延长清理时间，确保动画完整播放
            setTimeout(() => {
                if (svg && svg.parentNode) {
                    svg.parentNode.removeChild(svg);
                }
            }, 4000); // 增加到4秒，确保2.25s动画完整播放
        }

        // 🆕 生成增强版闪电路径
        function generateEnhancedLightningPath(startX, startY, endX, endY, isMain) {
            const segments = isMain ? 15 : 12; // 主闪电更多段落
            const roughness = isMain ? 30 : 25; // 主闪电更蜿蜒
            let pathData = `M ${startX} ${startY}`;
            
            for (let i = 1; i <= segments; i++) {
                const progress = i / segments;
                
                // 基础路径点
                const baseX = startX + (endX - startX) * progress;
                const baseY = startY + (endY - startY) * progress;
                
                // 添加蜿蜒效果
                const zigzagFactor = Math.sin(progress * Math.PI) * 0.8; // 中间更蜿蜒
                const randomOffsetX = (Math.random() - 0.5) * roughness * zigzagFactor;
                const randomOffsetY = (Math.random() - 0.5) * 10;
                
                const x = baseX + randomOffsetX;
                const y = baseY + randomOffsetY;
                
                // 添加锯齿转折
                if (i < segments && Math.random() > 0.3) {
                    const zigX = x + (Math.random() - 0.5) * roughness * 0.6;
                    const zigY = y + (Math.random() - 0.5) * 8;
                    pathData += ` L ${zigX} ${zigY}`;
                }
                
                pathData += ` L ${x} ${y}`;
            }
            
            return pathData;
        }

        // 🆕 获取当前境界等级
        function getCurrentRealmLevel() {
            try {
                // 从页面中获取境界信息
                const realmElement = document.querySelector('.current-realm');
                if (!realmElement) {
                    console.warn('⚠️ [渡劫闪电] 未找到境界元素，使用默认等级1');
                    return 1;
                }
                
                const realmText = realmElement.textContent || '';
                console.log(`📊 [渡劫闪电] 境界文本: ${realmText}`);
                
                // 从全局变量获取（如果存在）
                if (typeof window.currentCharacter !== 'undefined' && window.currentCharacter.realm_level) {
                    const level = parseInt(window.currentCharacter.realm_level);
                    console.log(`📊 [渡劫闪电] 从全局变量获取境界等级: ${level}`);
                    return level;
                }
                
                // 从localStorage获取（如果存在）
                const storedData = localStorage.getItem('characterData');
                if (storedData) {
                    try {
                        const characterData = JSON.parse(storedData);
                        if (characterData.realm_level) {
                            const level = parseInt(characterData.realm_level);
                            console.log(`📊 [渡劫闪电] 从localStorage获取境界等级: ${level}`);
                            return level;
                        }
                    } catch (e) {
                        console.warn('⚠️ [渡劫闪电] localStorage数据解析失败');
                    }
                }
                
                // 根据境界名称推算等级（与数据库一致）
                const realmMappings = {
                    // 人界境界 (1-140级)
                    '开光': [1, 10],      // 开光期
                    '灵虚': [11, 20],     // 灵虚期
                    '辟谷': [21, 30],     // 辟谷期
                    '心动': [31, 40],     // 心动期
                    '元化': [41, 50],     // 元化期
                    '元婴': [51, 60],     // 元婴期
                    '离合': [61, 70],     // 离合期
                    '空冥': [71, 80],     // 空冥期
                    '寂灭': [81, 90],     // 寂灭期
                    '大乘': [91, 100],    // 大乘期
                    '渡劫': [101, 110],   // 渡劫期
                    '凡仙': [111, 120],   // 凡仙期
                    '地仙': [121, 130],   // 地仙期
                    '天仙': [131, 140],   // 天仙期
                    
                    // 仙界境界 (141-280级)
                    '真仙': [141, 150],        // 真仙期
                    '太乙真仙': [151, 160],    // 太乙真仙期
                    '太乙金仙': [161, 170],    // 太乙金仙期
                    '太乙玄仙': [171, 180],    // 太乙玄仙期
                    '大罗真仙': [181, 190],    // 大罗真仙期
                    '大罗金仙': [191, 200],    // 大罗金仙期
                    '大罗玄仙': [201, 210],    // 大罗玄仙期
                    '准圣': [211, 220],        // 准圣期
                    '教主': [221, 230],        // 教主期
                    '混元': [231, 240],        // 混元期
                    '混元金仙': [241, 250],    // 混元金仙期
                    '混元至仙': [251, 260],    // 混元至仙期
                    '天道': [261, 270],        // 天道期
                    '鸿蒙至元': [271, 280]     // 鸿蒙至元期
                };
                
                for (const [realmName, [minLevel, maxLevel]] of Object.entries(realmMappings)) {
                    if (realmText.includes(realmName)) {
                        // 尝试提取小境界数字 - 支持多种格式
                        let subLevel = 1;
                        
                        // 匹配 "五阶" 格式
                        const chineseMatch = realmText.match(/([一二三四五六七八九十])阶/);
                        if (chineseMatch) {
                            const chineseNumbers = {
                                '一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
                                '六': 6, '七': 7, '八': 8, '九': 9, '十': 10
                            };
                            subLevel = chineseNumbers[chineseMatch[1]] || 1;
                        } else {
                            // 匹配数字格式 "5阶" 或 "第5层"
                            const numberMatch = realmText.match(/(\d+)/);
                            if (numberMatch) {
                                subLevel = parseInt(numberMatch[1]);
                            }
                        }
                        
                        const level = minLevel + subLevel - 1;
                        console.log(`📊 [渡劫闪电] 根据境界名称推算等级: ${realmName} ${subLevel}阶 = ${level}`);
                        return Math.min(level, maxLevel);
                    }
                }
                
                console.warn('⚠️ [渡劫闪电] 无法识别境界，使用默认等级1');
                return 1;
                
            } catch (error) {
                console.error('❌ [渡劫闪电] 获取境界等级失败:', error);
                return 1;
            }
        }

        // 🆕 隐藏渡劫特效
        function hideTribulationEffect() {
            const overlay = document.getElementById('tribulationOverlay');
            if (overlay) {
                console.log('🧹 [渡劫] 隐藏渡劫特效，移除遮罩层');
                overlay.remove();
                tribulationAnimationState.overlayExists = false;
                tribulationAnimationState.isActive = false;
            } else {
                console.log('⚠️ [渡劫] 尝试隐藏渡劫特效，但遮罩层不存在');
            }
        }

        // 🆕 显示渡劫结果 - 新增专门的结果div
        function showTribulationResult(result, message) {
            console.log('🎯 [渡劫结果] 显示结果:', result, message);
            
            let overlay = document.getElementById('tribulationOverlay');
            if (!overlay || !tribulationAnimationState.overlayExists) {
                console.warn('⚠️ [渡劫结果] 遮罩层不存在，强制重新创建');
                // 🔧 修复：强制重新创建遮罩层，确保结果能够显示
                overlay = document.createElement('div');
                overlay.id = 'tribulationOverlay';
                overlay.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(45deg, rgba(0,0,0,0.9), rgba(20,20,50,0.95));
                    z-index: 9999;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    color: white;
                    font-size: 24px;
                    font-weight: bold;
                `;
                document.body.appendChild(overlay);
                tribulationAnimationState.overlayExists = true;
                console.log('✅ [渡劫结果] 遮罩层已强制重新创建');
            } else {
                console.log('✅ [渡劫结果] 遮罩层存在，复用现有遮罩层');
                
                // 🔧 修复：清理遮罩层中的所有闪电SVG元素，但保留遮罩层本身
                const existingSvgs = overlay.querySelectorAll('svg');
                if (existingSvgs.length > 0) {
                    console.log(`🧹 [渡劫结果] 清理遮罩层中的 ${existingSvgs.length} 个SVG元素`);
                    existingSvgs.forEach(svg => svg.remove());
                }
                
                // 🔧 修复：清理云朵和文字元素，但保留遮罩层
                const existingClouds = overlay.querySelectorAll('#tribulationCloud, .small-cloud, #tribulationText');
                if (existingClouds.length > 0) {
                    console.log(`🧹 [渡劫结果] 清理遮罩层中的 ${existingClouds.length} 个云朵/文字元素`);
                    existingClouds.forEach(element => element.remove());
                }
            }
            
            // 清理之前的结果显示
            const existingResult = document.getElementById('tribulationResult');
            if (existingResult) {
                existingResult.remove();
                console.log('🧹 [渡劫结果] 清理旧的结果显示');
            }
            
            const isSuccess = result === 'success';
            const icon = isSuccess ? '🎉' : '💥';
            const title = isSuccess ? '突破成功' : '突破失败';
            
            // 🆕 创建新的结果显示div - 移动端适配，显示在遮罩层中心
            const isMobile = window.innerWidth <= 768;
            const resultDiv = document.createElement('div');
            resultDiv.id = 'tribulationResult';
            
            // 🔧 修复：使用与遮罩层相同的布局方式，确保显示在中心
            resultDiv.style.cssText = `
                position: relative;
                z-index: 10010;
                text-align: center;
                color: #fff;
                font-size: ${isMobile ? '24px' : '32px'};
                font-weight: bold;
                text-shadow: ${isSuccess ? '0 0 20px rgba(76, 175, 80, 0.8)' : '0 0 20px rgba(255, 107, 107, 0.8)'};
                animation: ${isSuccess ? 'tribulationSuccess 3s ease-in-out infinite' : 'tribulationFailure 3s ease-in-out infinite'};
                margin-top: 80px;
                padding: ${isMobile ? '10px' : '15px'};
                max-width: 90%;
                word-wrap: break-word;
            `;
            
            resultDiv.innerHTML = `${icon} ${title} ${icon}`;
            overlay.appendChild(resultDiv);
            
            console.log('🎯 [渡劫结果] 新结果div已创建:', title);
            
            // 🆕 添加点击事件监听器，允许用户点击关闭（移除自动关闭倒计时）
            const clickHandler = () => {
                console.log('🎯 [渡劫结果] 用户点击关闭遮罩');
                hideTribulationEffect();
                overlay.removeEventListener('click', clickHandler);
            };
            overlay.addEventListener('click', clickHandler);
        }



        // 🆕 将功法等级数字转换为中文格式
        function formatTechniqueLevel(level) {
            if (level >= 10) {
                return '圆满';
            }
            
            const chineseNumbers = ['', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
            return chineseNumbers[level] + '阶';
        }

        // 格式化数字显示
        function formatNumber(num) {
            if (num >= 100000000) {
                return (num / 100000000).toFixed(1) + '亿';
            } else if (num >= 10000) {
                return (num / 10000).toFixed(1) + '万';
            } else {
                return num.toString();
            }
        }

        // 显示消息提示
        function showMessage(text, type = 'info') {
            const existingMessage = document.querySelector('.message');
            if (existingMessage) {
                existingMessage.remove();
            }
            
            const message = document.createElement('div');
            message.className = `message ${type}`;
            message.textContent = text;
            document.body.appendChild(message);
            
            setTimeout(() => {
                if (message.parentNode) {
                    message.parentNode.removeChild(message);
                }
            }, 2500);
        }

        // 点击弹窗外部关闭
        document.addEventListener('click', function(event) {
            const techniqueModal = document.getElementById('techniqueModal');
            const breakthroughModal = document.getElementById('breakthroughModal');
            const techniqueDetailModal = document.getElementById('techniqueDetailModal');
            const efficiencyDetailModal = document.getElementById('efficiencyDetailModal');
            const soulPillModal = document.getElementById('soulPillModal');
            
            if (event.target === techniqueModal) {
                closeTechniqueModal();
            }
            
            if (event.target === breakthroughModal) {
                closeBreakthroughModal();
            }
            
            if (event.target === techniqueDetailModal) {
                closeDetailModal('techniqueDetailModal');
            }
            
            if (event.target === efficiencyDetailModal) {
                closeDetailModal('efficiencyDetailModal');
            }
            
            if (event.target === soulPillModal) {
                closeSoulPillModal();
            }
        });

        // ESC键关闭弹窗
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const techniqueModal = document.getElementById('techniqueModal');
                const breakthroughModal = document.getElementById('breakthroughModal');
                const techniqueDetailModal = document.getElementById('techniqueDetailModal');
                const efficiencyDetailModal = document.getElementById('efficiencyDetailModal');
                const soulPillModal = document.getElementById('soulPillModal');
                
                if (techniqueModal.style.display === 'flex') {
                    closeTechniqueModal();
                }
                
                if (breakthroughModal.style.display === 'flex') {
                    closeBreakthroughModal();
                }
                
                if (techniqueDetailModal.style.display === 'flex') {
                    closeDetailModal('techniqueDetailModal');
                }
                
                
                if (efficiencyDetailModal.style.display === 'flex') {
                    closeDetailModal('efficiencyDetailModal');
                }
                
                if (soulPillModal.style.display === 'flex') {
                    closeSoulPillModal();
                }
            }
        });

        // 页面可见性变化处理（后台运行）
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                console.log('🌙 [修炼] 页面进入后台，修炼继续进行...');
                // 服务器端会自动处理离线修炼，不需要前端记录时间
            } else {
                console.log('🌅 [修炼] 页面回到前台');
                // 重新加载数据以获取最新状态
                loadCultivationData().catch(error => {
                    console.error('重新加载修炼数据失败:', error);
                });
            }
        });

        // 🆕 页面卸载时清理定时器
        window.addEventListener('beforeunload', function() {
            console.log('🔄 [清理] 页面卸载，清理定时器');
            if (cycleInterval) {
                clearInterval(cycleInterval);
            }
            if (soulCheckInterval) {
                clearInterval(soulCheckInterval);
            }
        });

        // 计算离线收益
        function calculateOfflineGains() {
            const leaveTime = localStorage.getItem('cultivation_leave_time');
            if (leaveTime) {
            const offlineTime = Date.now() - parseInt(leaveTime);
                const offlineCycles = Math.floor(offlineTime / 30000); // 🔧 修改：30秒一个周期
                
                if (offlineCycles > 0) {
                    console.log(`🌙 [离线] 离线 ${Math.floor(offlineTime/1000)} 秒，完成 ${offlineCycles} 个修炼周期`);
                    
                    // 清除离线时间记录
                    localStorage.removeItem('cultivation_leave_time');
                    
                    // 显示离线收益（如果需要的话）
                    showMessage(`离线修炼完成 ${offlineCycles} 个周天`, 'info');
                }
            }
        }
        
        // 显示功法详情弹窗
        async function showTechniqueDetail() {
            console.log('📖 [详情] 显示功法详情弹窗');
            
            try {
                // 从API获取当前功法详细信息
                const response = await fetch(window.GameConfig ? window.GameConfig.getApiUrl('cultivation.php?action=get_attributes') : '../src/api/cultivation.php?action=get_attributes');
                const data = await response.json();
                
                if (!data.success) {
                    showMessage('获取功法信息失败', 'error');
                    return;
                }
                
                // 解析功法数据
                let currentTechniqueData = null;
                let techniqueName = '凝气决';
                let techniqueLevel = 1;
                let techniqueExp = 0;
                let maxExp = 100;
                
                if (data.attributes && data.attributes.cultivation_techniques) {
                    try {
                        // 解析JSON格式的功法数据
                        let techniques = {};
                        if (typeof data.attributes.cultivation_techniques === 'string') {
                            techniques = JSON.parse(data.attributes.cultivation_techniques);
                        } else {
                            techniques = data.attributes.cultivation_techniques;
                        }
                        
                        console.log('📖 [详情] 解析的功法数据:', techniques);
                        
                        // 获取当前使用的功法
                        const currentTechniqueId = data.attributes.current_technique || 'main';
                        console.log('📖 [详情] 当前功法ID:', currentTechniqueId);
                        
                        if (techniques[currentTechniqueId]) {
                            currentTechniqueData = techniques[currentTechniqueId];
                            techniqueName = currentTechniqueData.name || '凝气决';
                            techniqueLevel = currentTechniqueData.level || 1;
                            techniqueExp = currentTechniqueData.exp || 0;
                            maxExp = techniqueLevel * 100; // 每级需要 等级*100 经验
                            
                            console.log('📖 [详情] 当前功法详情:', {
                                name: techniqueName,
                                level: techniqueLevel,
                                exp: techniqueExp,
                                maxExp: maxExp
                            });
                        }
                    } catch (parseError) {
                        console.error('解析功法数据失败:', parseError);
                    }
                }
                
                // 计算功法效率和基础修为
                const techniqueEfficiency = getTechniqueEfficiencyBonus(techniqueName, techniqueLevel);
                const techniqueBaseQi = getTechniqueBaseQi(techniqueName, techniqueLevel);
                
                // 更新弹窗内容
                document.getElementById('modalTechniqueName').textContent = techniqueName;
                document.getElementById('modalTechniqueLevel').textContent = formatTechniqueLevel(techniqueLevel);
                document.getElementById('modalTechniqueType').textContent = getTechniqueTypeByName(techniqueName, techniqueLevel);
                document.getElementById('modalTechniqueSource').textContent = getSourceText(currentTechniqueData ? currentTechniqueData.source : '系统默认');
                document.getElementById('modalTechniqueBaseQi').textContent = `${techniqueBaseQi}点`;
                document.getElementById('modalTechniqueEfficiency').textContent = `+${techniqueEfficiency}%`;
                // 🔧 修复：移除功法描述设置（已删除描述区域）
                // document.getElementById('modalTechniqueDescription').textContent = getDescriptionByName(techniqueName);
                
                // 🔧 修复：功法等级达到圆满后进度条显示MAX
                let progressPercent, progressText;
                if (techniqueLevel >= 9) {
                    progressPercent = 100;
                    progressText = 'MAX';
                } else {
                    progressPercent = maxExp > 0 ? Math.min((techniqueExp / maxExp) * 100, 100) : 0;
                    progressText = `${techniqueExp}/${maxExp}`;
                }
                
                document.getElementById('modalTechniqueProgress').style.width = `${progressPercent}%`;
                document.getElementById('modalTechniqueProgressText').textContent = progressText;
                
                // 显示弹窗
                document.getElementById('techniqueDetailModal').style.display = 'flex';
                
            } catch (error) {
                console.error('❌ [详情] 获取功法详情失败:', error);
                showMessage('获取功法详情失败', 'error');
            }
        }
        
        // 显示效率详情弹窗
        async function showEfficiencyDetail() {
            console.log('⚡ [详情] 显示效率详情弹窗');
            
            // 🔧 使用新的修为计算逻辑
            const qiCalculation = await calculateTotalQiGain();
            
            // 🔧 修复：正确分离各项效率加成
            const techniqueBonus = qiCalculation.techniqueEfficiency || 0;
            const realmBonus = qiCalculation.realmEfficiency || 0;
            const mapBonus = qiCalculation.mapEfficiency || 0;
            const equipmentBonus = 0; // 装备暂时没有效率加成
            
            const totalBonus = qiCalculation.totalEfficiency || 0;
            
            // 更新弹窗内容
            document.getElementById('modalTotalEfficiency').textContent = `+${totalBonus}%`;
            document.getElementById('techniqueEfficiencyBonus').textContent = `+${techniqueBonus}%`;
            document.getElementById('mapEfficiencyBonus').textContent = `+${mapBonus}%`;
            document.getElementById('realmEfficiencyBonus').textContent = `+${realmBonus}%`;
            document.getElementById('equipmentEfficiencyBonus').textContent = `+${equipmentBonus}%`;
            
            // 🔧 修改：更新计算公式显示
            document.getElementById('currentBaseQi').textContent = qiCalculation.baseQiGain.toString();
            document.getElementById('currentTotalBonus').textContent = `${totalBonus}%`;
            document.getElementById('currentFinalQi').textContent = qiCalculation.finalQiGain.toString();
            
            // 显示弹窗
            document.getElementById('efficiencyDetailModal').style.display = 'flex';
        }
        
        // 关闭详情弹窗
        function closeDetailModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }
        
        // 获取功法来源文本
        function getSourceText(source) {
            // 🔧 修复：改进来源文本处理
            if (source && source !== 'basic' && source !== '初始功法' && source !== '系统默认') {
                return source;
            }
            
            // 处理具体的来源映射
            const sourceMap = {
                'basic': '系统初始功法',
                'ningqi': '系统初始功法',
                'xiantian': '初始功法', 
                'juling': '坊市购买',
                'lianshen': '黑市购买',
                '系统迁移': '系统初始功法',
                '系统自动转换': '系统初始功法',
                '系统默认': '系统初始功法'
            };
            
            return sourceMap[source] || '系统初始功法';
        }
        
        // 根据功法名称获取描述
        function getDescriptionByName(techniqueName) {
            const descriptions = {
                '凝气决': '基础修炼功法，适合初学者使用，无特殊效果',
                '先天功': '增加吸收灵气效率的功法，提升修炼速度',
                '聚灵决': '坊市功法，大幅提升修炼效率，适合中期修炼', 
                '炼神术': '黑市高级功法，极大提升修炼效率，神魂修炼首选'
            };
            return descriptions[techniqueName] || '神秘的修炼功法';
        }
        
        // 根据功法等级获取功法类型
        function getTypeByLevel(level) {
            if (level <= 3) {
                return '初级功法';
            } else if (level <= 6) {
                return '中级功法';
            } else if (level <= 9) {
                return '高级功法';
            } else {
                return '顶级功法';
            }
        }

        // 🆕 根据功法名称和等级获取功法类型（与attributes.html保持一致）
        function getTechniqueTypeByName(techniqueName, techniqueLevel = 1) {
            // 根据功法名称和等级确定功法类型
            const techniqueTypes = {
                '凝气决': techniqueLevel <= 3 ? '初级功法' : techniqueLevel <= 6 ? '中级功法' : '高级功法',
                '先天功': techniqueLevel <= 3 ? '中级功法' : techniqueLevel <= 6 ? '高级功法' : '顶级功法',
                '聚灵决': techniqueLevel <= 3 ? '中级功法' : techniqueLevel <= 6 ? '高级功法' : '顶级功法',
                '炼神术': techniqueLevel <= 3 ? '高级功法' : '顶级功法',
                '太极真经': '仙级功法',
                '九转玄功': '仙级功法',
                '混沌诀': '神级功法'
            };
            
            return techniqueTypes[techniqueName] || '普通功法';
        }

        // 开始修炼周期
        function startCultivationCycle() {
            console.log('🔄 [修炼] 开始修炼周期');
            
            // 清除现有的周期定时器
            if (cycleInterval) {
                clearInterval(cycleInterval);
            }
            
            // 重置当前周期时间
            currentCycleTime = 0;
            
            // 每100毫秒更新一次进度条
            cycleInterval = setInterval(() => {
                updateCultivationProgress();
            }, 100);
        }
        
        // 更新修炼进度
        function updateCultivationProgress() {
            const cycleDuration = 30000; // 🔧 修改：30秒一个周期（原8秒太快）
            currentCycleTime += 100; // 增加100毫秒
            
            // 计算进度百分比
            const progress = Math.min((currentCycleTime / cycleDuration) * 100, 100);
                
                // 更新进度条
                const progressFill = document.getElementById('cultivationProgressFill');
                if (progressFill) {
                progressFill.style.width = `${progress}%`;
                }
                
            // 更新进度文本
            const progressText = document.getElementById('cultivationProgressText');
                if (progressText) {
                const remainingTime = Math.max(0, Math.ceil((cycleDuration - currentCycleTime) / 1000));
                    progressText.textContent = `${Math.floor(currentCycleTime / 1000)} / 30秒`;
                }
                
            // 周期完成
            if (currentCycleTime >= cycleDuration) {
                    completeCultivationCycle();
                }
        }

        // 完成修炼周期
        async function completeCultivationCycle() {
            console.log('✅ [修炼] 完成一个修炼周期');
            
            // 重置周期时间
            currentCycleTime = 0;
            
            // 调用自动修炼API
            try {
                const response = await fetch(window.GameConfig ? window.GameConfig.getApiUrl('cultivation.php?action=auto_cultivate') : '../src/api/cultivation.php?action=auto_cultivate', {
                    method: 'POST'
                });
                
                const data = await response.json();
                
                if (data.success) {
                    // 更新修为显示
                    if (data.qi_gained) {
                        console.log(`🌟 [修炼] 获得修为: ${data.qi_gained}`);
                        
                        // 更新当前修为显示
                        if (window.cultivationData && window.cultivationData.realmInfo && window.cultivationData.realmInfo.current_realm) {
                            window.cultivationData.realmInfo.current_realm.current_qi = data.new_qi;
                            updateRealmDisplay();
                        }
                    }
                    
                    // 检查是否可以突破
                    if (data.can_breakthrough !== undefined) {
                        if (window.cultivationData && window.cultivationData.realmInfo) {
                            window.cultivationData.realmInfo.can_breakthrough = data.can_breakthrough;
                            updateButtonStates();
                        }
                    }
                    
                    // 🆕 处理功法升级
                    if (data.technique_updated) {
                        console.log('📖 [功法] 功法经验或等级有更新');
                        // 如果功法详情弹窗是打开的，刷新显示
                        const techniqueModal = document.getElementById('techniqueDetailModal');
                        if (techniqueModal && techniqueModal.style.display === 'flex') {
                            await showTechniqueDetail(); // 重新加载功法详情
                        }
                        // 更新修炼状态显示（功法等级可能影响效率）
                        updateCultivationStatus();
                    }
                    
                    // 显示包含功法升级信息的消息
                    if (data.message && data.message.includes('升级')) {
                        showMessage(data.message, 'success');
                    }
                    
                } else {
                    console.error('❌ [修炼] 自动修炼失败:', data.message);
                }
                
            } catch (error) {
                console.error('❌ [修炼] 网络错误:', error);
            }
        }
        
        // 停止修炼周期
        function stopCultivationCycle() {
            console.log('⏹️ [修炼] 停止修炼周期');
            
            if (cycleInterval) {
                clearInterval(cycleInterval);
                cycleInterval = null;
            }
            
            // 重置进度条
            const progressFill = document.getElementById('cultivationProgressFill');
            if (progressFill) {
                progressFill.style.width = '0%';
            }
            
            const progressText = document.getElementById('cultivationProgressText');
            if (progressText) {
                progressText.textContent = '0 / 30秒';
            }
            
            currentCycleTime = 0;
        }

        // 🆕 页面加载时处理离线修炼收益
        async function handleOfflineCultivationOnLoad() {
            console.log('🌙 [离线修炼] 开始处理离线修炼收益...');
            
            // 🔍 检查是否刚刚登录，决定是否显示弹窗
            const justLoggedIn = localStorage.getItem('just_logged_in');
            const loginTime = localStorage.getItem('login_time');
            let shouldShowModal = false;
            
            if (justLoggedIn === 'true' && loginTime) {
                const timeSinceLogin = Date.now() - parseInt(loginTime);
                // 如果登录时间在5分钟内，认为是刚登录，显示弹窗
                if (timeSinceLogin < 5 * 60 * 1000) {
                    shouldShowModal = true;
                    console.log('🌙 [离线修炼] 检测到刚登录，将显示离线收益弹窗');
                }
                
                // 清除登录标记，避免重复显示
                localStorage.removeItem('just_logged_in');
                localStorage.removeItem('login_time');
            } else {
                console.log('🌙 [离线修炼] 非登录状态，离线收益将静默增加');
            }
            
            try {
                const response = await fetch(window.GameConfig ? window.GameConfig.getApiUrl('cultivation.php?action=handle_offline_cultivation') : '../src/api/cultivation.php?action=handle_offline_cultivation', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('🌙 [离线修炼] API响应:', data);
                
                if (data.success) {
                    if (data.offline_cycles > 0) {
                        console.log(`🌙 [离线修炼] 获得收益: ${data.offline_qi_gained} 修为，${data.offline_cycles} 个周期`);
                        
                        if (shouldShowModal) {
                            // 🎉 显示离线修炼收益详情弹窗（仅登录时）
                            console.log('🌙 [离线修炼] 显示离线收益弹窗');
                            
                            // 显示详细的离线收益弹窗
                            showOfflineRewardModal(data);
                        } else {
                            // 🔇 静默增加收益，不显示任何弹窗或消息
                            console.log('🌙 [离线修炼] 静默增加离线收益');
                        }
                        
                    } else {
                        console.log('🌙 [离线修炼] 无离线收益');
                    }
                } else {
                    console.error('🌙 [离线修炼] 处理失败:', data.message);
                    // 不显示错误消息，避免影响用户体验
                }
                
            } catch (error) {
                console.error('🌙 [离线修炼] 网络错误:', error);
                // 不显示错误消息，离线修炼失败不影响正常使用
            }
        }
        
        // 🆕 显示离线修炼收益弹窗
        function showOfflineRewardModal(rewardData) {
            // 创建弹窗HTML
            const modalHtml = `
                <div class="offline-reward-modal" id="offlineRewardModal" style="
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
                    display: flex;
        justify-content: center;
        align-items: center;
                    z-index: 1003;
        backdrop-filter: blur(5px);
    ">
        <div style="
            background: linear-gradient(135deg, rgba(26, 26, 46, 0.95), rgba(22, 33, 62, 0.95));
            border-radius: 20px;
                        padding: 25px;
            width: 90%;
                        max-width: 400px;
                        border: 2px solid rgba(255, 193, 7, 0.6);
                        box-shadow: 0 0 30px rgba(255, 193, 7, 0.4);
            text-align: center;
            color: white;
        ">
                        <div style="font-size: 18px; font-weight: bold; color: #ffd700; margin-bottom: 20px;">
                            🌙 离线修炼收益 🌙
            </div>
            
                        <div style="margin-bottom: 15px; font-size: 14px; line-height: 1.6;">
                            <div style="margin-bottom: 8px;">
                                ⏰ 离线时间: <span style="color: #4CAF50; font-weight: bold;">${rewardData.offline_time_hours}小时</span>
                            </div>
                            <div style="margin-bottom: 8px;">
                                🔄 修炼周期: <span style="color: #4CAF50; font-weight: bold;">${rewardData.offline_cycles}次</span>
                            </div>
                            <div style="margin-bottom: 8px;">
                                ⚡ 修为获得: <span style="color: #ffd700; font-weight: bold; font-size: 16px;">+${rewardData.offline_qi_gained}</span>
                            </div>
                            ${rewardData.technique_exp_gained > 0 ? `
                                <div style="margin-bottom: 8px;">
                                    📖 功法经验: <span style="color: #9c27b0; font-weight: bold;">+${rewardData.technique_exp_gained}</span>
                                </div>
                            ` : ''}
                            ${rewardData.technique_updated ? `
                                <div style="margin-bottom: 8px; color: #ff9800; font-weight: bold;">
                                    🎉 功法升级了！
                                </div>
                            ` : ''}
                            ${rewardData.is_limited ? `
                                <div style="margin-bottom: 8px; color: #ff6b6b; font-size: 12px;">
                                    ⚠️ 已达到48小时修炼上限
                                </div>
                            ` : ''}
            </div>
            
                        <div style="
                            background: rgba(0, 0, 0, 0.3);
                            border-radius: 10px;
                            padding: 10px;
                            margin-bottom: 20px;
                            font-size: 12px;
                            color: #bdc3c7;
                        ">
                            每30秒获得 ${rewardData.qi_per_cycle} 修为
            </div>
            
                        <button onclick="closeOfflineRewardModal()" style="
                            background: linear-gradient(135deg, #ffd700, #ffb300);
                            color: #333;
                    border: none;
                    border-radius: 10px;
                    padding: 12px 24px;
                            font-size: 14px;
                    font-weight: bold;
                    cursor: pointer;
                            transition: all 0.3s ease;
                        ">
                            确认收益
                        </button>
            </div>
        </div>
            `;
            
            // 添加到页面
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            
            // 3秒后自动关闭
            setTimeout(() => {
                closeOfflineRewardModal();
            }, 8000);
        }
        
        // 关闭离线收益弹窗
        function closeOfflineRewardModal() {
            const modal = document.getElementById('offlineRewardModal');
            if (modal) {
                modal.remove();
            }
        }

        // 🆕 启动魂力状态定期检查
        function startSoulStatusCheck() {
            // 清除现有的定时器
            if (soulCheckInterval) {
                clearInterval(soulCheckInterval);
            }
            
            // 每5秒检查一次魂力状态
            soulCheckInterval = setInterval(async () => {
                await checkSoulStatus();
            }, 5000);
            
            console.log('🔮 [魂力] 魂力状态定期检查已启动（每5秒）');
        }

        // 🆕 停止魂力状态定期检查
        function stopSoulStatusCheck() {
            if (soulCheckInterval) {
                clearInterval(soulCheckInterval);
                soulCheckInterval = null;
                console.log('🔮 [魂力] 魂力状态定期检查已停止');
            }
        }

        // 🆕 检查魂力状态
        async function checkSoulStatus() {
            try {
                console.log('🔮 [魂力] 正在检查魂力状态...');
                const response = await fetch(window.GameConfig ? window.GameConfig.getApiUrl('cultivation.php?action=get_soul_status') : '../src/api/cultivation.php?action=get_soul_status');
                const data = await response.json();
                
                if (data.success) {
                    const previousStatus = window.cultivationData.soulStatus;
                    window.cultivationData.soulStatus = data.soul_status;
                    
                    // 🆕 检查魂力状态是否发生变化
                    const statusChanged = !previousStatus || 
                        previousStatus.is_damaged !== data.soul_status.is_damaged;
                    
                    if (statusChanged) {
                        console.log('🔮 [魂力] 魂力状态发生变化:', {
                            之前: previousStatus?.is_damaged ? '受损' : '正常',
                            现在: data.soul_status.is_damaged ? '受损' : '正常'
                        });
                    }
                    
                    console.log('🔮 [魂力] 魂力状态:', data.soul_status);
                    updateSoulStatusDisplay();
                } else {
                    console.warn('⚠️ [魂力] 获取魂力状态失败:', data.message);
                }
            } catch (error) {
                console.error('❌ [魂力] 魂力状态检查失败:', error);
            }
        }

        // 🆕 更新魂力状态显示
        function updateSoulStatusDisplay() {
            const soulStatus = window.cultivationData.soulStatus;
            if (!soulStatus) return;
            
            const progressContainer = document.getElementById('progressContainer');
            const soulIndicator = document.getElementById('soulStatusIndicator');
            
            if (soulStatus.is_damaged) {
                // 🔧 魂力受损状态：隐藏修为进度条，显示魂力恢复进度条
                progressContainer.style.display = 'none';
                soulIndicator.style.display = 'block';
                
                // 更新魂力恢复进度
                const recoveryPercent = soulStatus.current_power;
                const soulFill = document.getElementById('soulRecoveryFill');
                const soulTime = document.getElementById('soulRecoveryTime');
                
                if (soulFill) {
                    soulFill.style.width = recoveryPercent + '%';
                }
                
                if (soulTime && soulStatus.time_to_full > 0) {
                    const minutes = Math.floor(soulStatus.time_to_full / 60);
                    const seconds = soulStatus.time_to_full % 60;
                    soulTime.textContent = `剩余时间：${minutes}分${seconds}秒`;
                } else if (soulTime) {
                    soulTime.textContent = '魂力即将恢复';
                }
                
                console.log('🩸 [魂力] 魂力受损，显示魂力恢复进度条，魂力值:', recoveryPercent.toFixed(1) + '%');
                
            } else {
                // 🔧 魂力正常状态：显示修为进度条，隐藏魂力恢复进度条
                progressContainer.style.display = 'block';
                soulIndicator.style.display = 'none';
                
                console.log('💚 [魂力] 魂力正常，显示修为进度条');
            }
            
            // 🆕 更新按钮状态以确保渡劫/养魂丹按钮正确切换
            console.log('🔮 [魂力] 魂力状态显示更新完成，调用按钮状态更新');
            updateButtonStates();
        }

        // 🆕 使用养魂丹
        async function useSoulPill(pillId) {
            try {
                const response = await fetch(window.GameConfig ? window.GameConfig.getApiUrl('cultivation.php?action=use_soul_pill') : '../src/api/cultivation.php?action=use_soul_pill', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ pill_id: pillId })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showMessage(data.message, 'success');
                    // 🆕 立即检查魂力状态，不等待定时器
                    await checkSoulStatus();
                    await updateCultivationStatus(); // 更新修炼状态
                    // 🔧 修复：更新按钮状态，确保魂力恢复后显示正确的按钮
                    updateButtonStates();
                    // 🆕 关闭养魂丹弹窗
                    closeSoulPillModal();
                } else {
                    showMessage(data.message, 'error');
                }
            } catch (error) {
                console.error('❌ [养魂丹] 使用失败:', error);
                showMessage('使用养魂丹失败', 'error');
            }
        }

        // 🆕 打开养魂丹选择弹窗
        function openSoulPillModal() {
            console.log('🩸 [养魂丹] 打开养魂丹选择弹窗');
            generateSoulPillList();
            const modal = document.getElementById('soulPillModal');
            if (modal) {
                modal.style.display = 'flex';
                console.log('🩸 [养魂丹] 弹窗已显示');
            } else {
                console.error('❌ [养魂丹] 找不到养魂丹弹窗元素');
            }
        }

        // 🆕 关闭养魂丹选择弹窗
        function closeSoulPillModal() {
            const modal = document.getElementById('soulPillModal');
            if (modal) {
                modal.style.display = 'none';
            }
            selectedSoulPill = null;
        }

        // 🆕 生成养魂丹列表
        async function generateSoulPillList() {
            const pillList = document.getElementById('soulPillList');
            if (!pillList) {
                console.error('❌ [养魂丹] 找不到养魂丹列表元素');
                return;
            }
            
            pillList.innerHTML = '<div style="text-align: center; color: #bdc3c7; padding: 20px;">正在加载养魂丹...</div>';
            
            try {
                // 🔧 获取当前境界信息以确定可用的养魂丹
                const currentRealmLevel = window.cultivationData.realmInfo?.current_realm?.level || 1;
                
                // 调用API获取可用的养魂丹
                const response = await fetch(window.GameConfig ? window.GameConfig.getApiUrl('cultivation.php?action=get_soul_pills') : '../src/api/cultivation.php?action=get_soul_pills', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ realm_level: currentRealmLevel })
                });
                
                const data = await response.json();
                console.log('🩸 [养魂丹] API响应:', data);
                
                if (data.success && data.soul_pills) {
                    pillList.innerHTML = '';
                    
                    if (data.soul_pills.length === 0) {
                        pillList.innerHTML = '<div style="text-align: center; color: #bdc3c7; padding: 20px;">🈚 暂无可用的养魂丹</div>';
                        return;
                    }
                    
                    // 生成养魂丹列表
                    data.soul_pills.forEach(pill => {
                        const pillItem = document.createElement('div');
                        pillItem.style.cssText = `
                            background: rgba(255, 255, 255, 0.1);
                            border-radius: 8px;
                            padding: 12px;
                            cursor: pointer;
                            transition: all 0.3s ease;
                            border: 1px solid transparent;
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                        `;
                        
                        // 计算恢复效果
                        const recoveryRate = pill.recovery_rate || 50;
                        
                        pillItem.innerHTML = `
                            <div style="text-align: left;">
                                <div style="font-size: 12px; color: #fff; font-weight: bold; margin-bottom: 2px;">
                                    ${pill.item_name}
                                </div>
                                <div style="font-size: 10px; color: #4CAF50;">
                                    恢复魂力: ${recoveryRate}%
                                </div>
                            </div>
                            <div style="font-size: 11px; color: #ffd700; font-weight: bold;">
                                ×${pill.quantity}
                            </div>
                        `;
                        
                        // 添加hover效果
                        pillItem.addEventListener('mouseenter', function() {
                            this.style.background = 'rgba(255, 255, 255, 0.15)';
                            this.style.transform = 'translateY(-1px)';
                        });
                        
                        pillItem.addEventListener('mouseleave', function() {
                            if (!this.classList.contains('selected')) {
                                this.style.background = 'rgba(255, 255, 255, 0.1)';
                                this.style.transform = 'translateY(0)';
                            }
                        });
                        
                        // 添加点击选择功能
                        pillItem.addEventListener('click', function() {
                            selectSoulPill({
                                id: pill.item_id,
                                name: pill.item_name,
                                recovery_rate: recoveryRate,
                                quantity: pill.quantity
                            }, pillItem);
                        });
                        
                        pillList.appendChild(pillItem);
                    });
                    
                    console.log('🩸 [养魂丹] 养魂丹列表已生成，项目数:', data.soul_pills.length);
                    
                } else {
                    console.error('❌ [养魂丹] 获取养魂丹数据失败:', data.message);
                    pillList.innerHTML = '<div style="text-align: center; color: #ff6b6b; padding: 20px;">❌ 获取养魂丹数据失败</div>';
                }
                
            } catch (error) {
                console.error('❌ [养魂丹] 网络错误:', error);
                pillList.innerHTML = '<div style="text-align: center; color: #ff6b6b; padding: 20px;">🌐 网络连接异常</div>';
            }
        }

        // 🆕 选择养魂丹
        let selectedSoulPill = null;
        function selectSoulPill(pill, element) {
            // 移除之前的选中状态
            const pillItems = document.querySelectorAll('#soulPillList > div');
            pillItems.forEach(item => {
                item.classList.remove('selected');
                if (!item.querySelector) return; // 跳过文本节点
                item.style.background = 'rgba(255, 255, 255, 0.1)';
                item.style.borderColor = 'transparent';
            });
            
            // 添加选中状态
            element.classList.add('selected');
            element.style.background = 'rgba(231, 76, 60, 0.2)';
            element.style.borderColor = '#e74c3c';
            
            selectedSoulPill = pill;
            
            // 启用确认按钮
            const confirmBtn = document.getElementById('confirmSoulPill');
            if (confirmBtn) {
                confirmBtn.disabled = false;
                confirmBtn.style.background = 'linear-gradient(135deg, #e74c3c, #c0392b)';
                confirmBtn.style.cursor = 'pointer';
            }
            
            console.log('🩸 [养魂丹] 选择了:', pill.name);
        }

        // 🆕 确认使用养魂丹
        async function confirmUseSoulPill() {
            if (!selectedSoulPill) {
                showMessage('请选择一个养魂丹', 'error');
                return;
            }
            
            try {
                console.log('🩸 [养魂丹] 使用养魂丹:', selectedSoulPill);

                const response = await fetch(window.GameConfig ? window.GameConfig.getApiUrl('cultivation.php?action=use_soul_pill') : '../src/api/cultivation.php?action=use_soul_pill', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ pill_id: selectedSoulPill.id })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showMessage(data.message, 'success');
                    closeSoulPillModal();
                    
                    // 🔧 修复：立即更新魂力状态数据
                    console.log('🩸 [养魂丹] 使用成功，立即更新魂力状态');
                    
                    // 重新检查魂力状态和更新显示
                    await checkSoulStatus();
                    await updateCultivationStatus();
                    
                    // 🔧 修复：延迟一点再更新按钮状态，确保数据已更新
                    setTimeout(() => {
                        console.log('🔘 [按钮] 养魂丹使用后，强制更新按钮状态');
                        updateButtonStates();
                    }, 100);
                    
                } else {
                    showMessage(data.message || '使用养魂丹失败', 'error');
                }
                
            } catch (error) {
                console.error('❌ [养魂丹] 使用失败:', error);
                showMessage('使用养魂丹失败', 'error');
            }
        }

        // 🆕 副闪电对称分布
        function createSubLightnings(container, num) {
            const containerRect = container.getBoundingClientRect();
            const svgWidth = containerRect.width || window.innerWidth;
            const centerX = svgWidth / 2;
            const spacing = 75 / (num - 1);
            for (let i = 0; i < num; i++) {
                let offset = (i - (num - 1) / 2) * spacing;
                let finalX = centerX + offset;
                createSubLightningPath(container, finalX);
            }
        }

        // 🧩 功法碎片合成系统
        let fragmentSynthesisData = {
            recipes: [],
            fragments: []
        };

        // 打开功法碎片合成弹窗
        async function openFragmentSynthesisModal() {
            try {
                document.getElementById('fragmentSynthesisModal').style.display = 'flex';
                await loadFragmentSynthesisData();
            } catch (error) {
                console.error('打开功法碎片合成弹窗失败:', error);
                showMessage('加载功法碎片数据失败', 'error');
            }
        }

        // 关闭功法碎片合成弹窗
        function closeFragmentSynthesisModal() {
            document.getElementById('fragmentSynthesisModal').style.display = 'none';
        }

        // 加载功法碎片合成数据
        async function loadFragmentSynthesisData() {
            try {
                // 只获取合成配方数据（包含用户拥有的碎片数量）
                const apiUrl = window.GameConfig ? window.GameConfig.getApiUrl('technique_fragment_synthesis.php?action=get_synthesis_recipes') : '../src/api/technique_fragment_synthesis.php?action=get_synthesis_recipes';
                const recipesResponse = await fetch(apiUrl);
                const recipesData = await recipesResponse.json();

                if (recipesData.success) {
                    fragmentSynthesisData.recipes = recipesData.recipes;
                    fragmentSynthesisData.userFragments = recipesData.user_fragments;
                    renderSynthesisRecipes();
                } else {
                    throw new Error(recipesData.message);
                }

            } catch (error) {
                console.error('加载功法碎片数据失败:', error);
                showMessage('加载数据失败：' + error.message, 'error');
            }
        }

        // 渲染合成配方列表
        function renderSynthesisRecipes() {
            const recipesList = document.getElementById('synthesisRecipesList');
            
            if (fragmentSynthesisData.recipes.length === 0) {
                recipesList.innerHTML = '<div class="empty-list-message">暂无可合成的功法</div>';
                return;
            }

            recipesList.innerHTML = fragmentSynthesisData.recipes.map(recipe => {
                const canSynthesize = recipe.can_synthesize;
                const statusClass = canSynthesize ? 'can-synthesize' : 'insufficient';
                const statusText = canSynthesize ? '可合成' : '材料不足';

                // 获取第一个材料信息（功法合成通常只需要一种碎片）
                const material = recipe.materials[0];

                return `
                    <div class="recipe-item ${statusClass}">
                        <div class="recipe-header">
                            <div class="recipe-name">${recipe.result_item_name}</div>
                            <div class="recipe-status ${statusClass}">${statusText}</div>
                        </div>
                        <div class="recipe-details">
                            需要：${material.item_name} × ${material.quantity}
                        </div>
                        <div class="recipe-requirement">
                            拥有：${material.owned_quantity} / ${material.quantity}
                        </div>
                        <button class="synthesize-button" 
                                onclick="synthesizeTechnique(${recipe.recipe_id})"
                                ${!canSynthesize ? 'disabled' : ''}>
                            ${canSynthesize ? '立即合成' : '材料不足'}
                        </button>
                    </div>
                `;
            }).join('');
        }

        // 渲染拥有的功法碎片
        function renderOwnedFragments() {
            const fragmentsList = document.getElementById('ownedFragmentsList');
            
            if (fragmentSynthesisData.fragments.length === 0) {
                fragmentsList.innerHTML = '<div class="empty-list-message">暂无功法碎片</div>';
                return;
            }

            fragmentsList.innerHTML = fragmentSynthesisData.fragments.map(fragment => {
                // 从配方中查找这个碎片可以合成什么
                let targetTechnique = '未知功法';
                if (fragmentSynthesisData.recipes) {
                    const recipe = fragmentSynthesisData.recipes.find(r => 
                        r.materials && r.materials.some(m => m.item_id == fragment.item_id)
                    );
                    if (recipe) {
                        targetTechnique = recipe.result_item_name;
                    }
                }

                return `
                    <div class="fragment-item">
                        <div class="fragment-name">${fragment.item_name}</div>
                        <div class="fragment-details">
                            <div class="fragment-quantity">数量：${fragment.quantity}</div>
                            <div class="fragment-target">可合成：${targetTechnique}</div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 合成功法
        async function synthesizeTechnique(recipeId) {
            try {
                const formData = new FormData();
                formData.append('action', 'synthesize_technique');
                formData.append('recipe_id', recipeId);

                const apiUrl = window.GameConfig ? window.GameConfig.getApiUrl('technique_fragment_synthesis.php') : '../src/api/technique_fragment_synthesis.php';
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    // 显示合成成功的详细提示信息
                    const successMessage = `🎉 功法合成成功！\n✨ 获得：${data.technique_name || '新功法'}\n🧩 消耗碎片：${data.fragments_used || '若干'}个`;
                    showMessage(successMessage, 'success');
                    
                    // 重新加载数据
                    await loadFragmentSynthesisData();
                    
                    // 重新加载功法列表（如果功法选择弹窗是打开的）
                    if (document.getElementById('techniqueModal').style.display === 'flex') {
                        await loadTechniques();
                    }
                    
                } else {
                    throw new Error(data.message);
                }

            } catch (error) {
                console.error('合成功法失败:', error);
                showMessage('合成失败：' + error.message, 'error');
            }
        }
    </script>

    <!-- 功法详情弹窗 -->
    <div class="detail-modal" id="techniqueDetailModal">
        <div class="detail-modal-content">
            <div class="detail-modal-header">
                <div class="detail-modal-title">📖 当前功法详情</div>
                <button class="detail-modal-close" onclick="closeDetailModal('techniqueDetailModal')">&times;</button>
            </div>
            <div class="detail-modal-body">
                <div class="technique-detail-section">
                    <div class="technique-main-info">
                        <div class="technique-name-display" id="modalTechniqueName">凝气决</div>
                        <div class="technique-level-display">等级: <span id="modalTechniqueLevel">1</span></div>
                    </div>

                    <div class="technique-progress-section">
                        <div class="progress-label">修炼进度</div>
                        <div class="progress-container">
                            <div class="progress-bar">
                                <div class="progress-fill" id="modalTechniqueProgress"></div>
                                <div class="progress-text" id="modalTechniqueProgressText">0/100</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="technique-stats-grid">
                        <div class="stat-item">
                            <div class="stat-icon">🏷️</div>
                            <div class="stat-label">功法类型</div>
                            <div class="stat-value" id="modalTechniqueType">初级功法</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon">📦</div>
                            <div class="stat-label">获得来源</div>
                            <div class="stat-value" id="modalTechniqueSource">初始功法</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon">💎</div>
                            <div class="stat-label">基础修为</div>
                            <div class="stat-value" id="modalTechniqueBaseQi">10点</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon">⚡</div>
                            <div class="stat-label">修炼效率</div>
                            <div class="stat-value" id="modalTechniqueEfficiency">+0%</div>
                        </div>
                    </div>
                    
                    <!-- 🔧 修复：移除功法描述区域 -->
                </div>
            </div>
        </div>
    </div>
    
    <!-- 效率详情弹窗 -->
    <div class="detail-modal" id="efficiencyDetailModal">
        <div class="detail-modal-content">
            <div class="detail-modal-header">
                <div class="detail-modal-title">⚡ 修炼效率详情</div>
                <button class="detail-modal-close" onclick="closeDetailModal('efficiencyDetailModal')">&times;</button>
            </div>
            <div class="detail-modal-body">
                <div class="efficiency-summary">
                    <div class="efficiency-total">
                        <div class="efficiency-icon">⚡</div>
                        <div class="efficiency-info">
                            <div class="efficiency-label">总修炼效率</div>
                            <div class="efficiency-value" id="modalTotalEfficiency">+0%</div>
                        </div>
                    </div>
                </div>
                
                <div class="efficiency-breakdown">
                    <div class="breakdown-title">加成来源明细</div>
                    
                    <div class="efficiency-item">
                        <div class="efficiency-source">
                            <div class="source-icon">📖</div>
                            <div class="source-name">所有功法</div>
                        </div>
                        <div class="source-value" id="techniqueEfficiencyBonus">+0%</div>
                    </div>
                    
                    <div class="efficiency-item">
                        <div class="efficiency-source">
                            <div class="source-icon">🧙</div>
                            <div class="source-name">境界等级</div>
                        </div>
                        <div class="source-value" id="realmEfficiencyBonus">+0%</div>
                    </div>
                    
                    <div class="efficiency-item">
                        <div class="efficiency-source">
                            <div class="source-icon">🗺️</div>
                            <div class="source-name">历练地图</div>
                        </div>
                        <div class="source-value" id="mapEfficiencyBonus">+0%</div>
                    </div>
                    
                    <div class="efficiency-item">
                        <div class="efficiency-source">
                            <div class="source-icon">💎</div>
                            <div class="source-name">装备加成（暂无）</div>
                        </div>
                        <div class="source-value" id="equipmentEfficiencyBonus">+0%</div>
                    </div>
                </div>
                
                <div class="efficiency-calculation">
                    <div class="calculation-title">修为计算公式</div>
                    <div class="calculation-formula">
                        <div class="formula-line">功法基础修为 = 所有已学功法基础修为之和</div>
                        <div class="formula-line">地图进度修为 = 最高通关关卡数 - 1</div>
                        <div class="formula-line">基础修为 = 功法基础修为 + 地图进度修为</div>
                        <div class="formula-line">总效率 = 功法效率 + 境界效率 + 地图效率</div>
                        <div class="formula-line">最终修为 = 基础修为 × (1 + 总效率加成)</div>
                        <div class="formula-example">
                            当前: <span id="currentBaseQi">10</span> × (1 + <span id="currentTotalBonus">0%</span>) = <span id="currentFinalQi">10</span> 点修为/30秒
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html> 











