<?php

/**
 * 装备集成API - 装备系统的统一管理中心
 * 
 * 🎯 职责分离设计说明：
 * 
 * ✅ 本API专门负责所有装备相关功能：
 * - 装备/武器数据获取（包含完整的随机属性）
 * - 装备属性计算（基础属性 + 随机属性合并）
 * - 装备穿戴/卸下操作
 * - 装备修理/回收系统
 * - 装备耐久度管理
 * - 背包管理（整理、扩展等）
 * - 物品详情查看（统一的属性展示）
 * - 消耗品使用逻辑
 * 
 * 🔧 与其他API的协作：
 * - battle_unified.php → 仅处理战斗逻辑，装备数据来源于本API
 * - cultivation.php → 修炼系统，装备属性加成来源于本API
 * - alchemy_system.php → 炼丹系统，丹药使用逻辑在本API中
 * 
 * 📋 数据一致性保证：
 * - 所有装备属性显示都来自此API，确保前端显示统一
 * - 背包中的装备属性与战斗界面的装备属性完全一致
 * - 装备随机属性通过custom_attributes字段统一管理
 * - 回收价格计算统一，避免不同界面价格不一致
 * 
 * 🎮 设计优势：
 * 1. 单一数据源：所有装备数据都从这里获取
 * 2. 属性一致性：无论在哪个界面查看，装备属性都相同
 * 3. 易于维护：装备系统的修改只需要改这一个文件
 * 4. 功能完整：包含装备生命周期的所有操作
 */

session_start();
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/functions.php'; // 修复：取消注释并修正路径
require_once __DIR__ . '/equipment_quality_system.php'; // 🔧 新增：引入品质系统
require_once __DIR__ . '/../includes/equipment_stats_manager.php'; // 🔧 新增：引入装备属性管理器

try {
    $pdo = getDatabase();

    if (!$pdo) {
        throw new Exception('数据库连接失败');
    }

    // 检查用户登录状态
    if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
        echo json_encode(['success' => false, 'message' => '请先登录']);
        exit;
    }

    $userId = $_SESSION['user_id'];
    $action = isset($_GET['action']) ? $_GET['action'] : (isset($_POST['action']) ? $_POST['action'] : '');

    switch ($action) {
        case 'get_all_equipment':
            getAllEquipmentUnified($userId, $pdo);
            break;
        case 'get_user_weapon_slots':
        case 'get_weapon_slots':
            getWeaponSlotsUnified($userId, $pdo);
            break;
        case 'get_item_detail':
            getItemDetailUnified($userId, $pdo);
            break;
        case 'get_item_detail_by_name':
            getItemDetailByNameUnified($userId, $pdo);
            break;
        case 'equip_weapon':
            equipWeaponUnified($userId, $pdo);
            break;
        case 'unequip_weapon':
            unequipWeaponUnified($userId, $pdo);
            break;
        case 'equip_item':
            equipItemUnified($userId, $pdo);
            break;
        case 'unequip_item':
            unequipItemUnified($userId, $pdo);
            break;
        case 'repair_weapon':
            repairWeaponUnified($userId, $pdo);
            break;
        case 'recycle_item':
            recycleItemUnified($userId, $pdo);
            break;
        case 'get_user_stats':
            getUserStatsUnified($userId, $pdo);
            break;
        case 'change_avatar':
            changeAvatarUnified($userId, $pdo);
            break;
        case 'get_available_avatars':
            getAvailableAvatarsUnified($userId, $pdo);
            break;
        case 'update_weapon_durability':
            updateWeaponDurability($userId, $pdo);
            break;
        case 'use_item':
            useItemUnified($userId, $pdo);
            break;
        case 'organize_inventory':
            organizeInventoryUnified($userId, $pdo);
            break;
        case 'expand_inventory':
            expandInventoryUnified($userId, $pdo);
            break;
        case 'add_equipment_with_quality': // 🔧 新增：带品质的装备获得
            addEquipmentWithQualityUnified($userId, $pdo);
            break;
        case 'get_dan_medicine_info':
            try {
                // 直接获取角色ID，而不是调用不存在的getCurrentCharacterId函数
                $stmt = $pdo->prepare("SELECT id FROM characters WHERE user_id = ? LIMIT 1");
                $stmt->execute([$userId]);
                $character = $stmt->fetch(PDO::FETCH_ASSOC);

                if (!$character) {
                    throw new Exception("找不到有效的角色信息");
                }

                $characterId = $character['id'];
                $danMedicineInfo = getDanMedicineInfo($pdo, $characterId);

                echo json_encode([
                    'success' => true,
                    'data' => $danMedicineInfo
                ]);
            } catch (Exception $e) {
                echo json_encode([
                    'success' => false,
                    'message' => $e->getMessage()
                ]);
            }
            break;
        case 'save_avatar_frame':
            saveAvatarFrameUnified($userId, $pdo);
            break;
        case 'get_available_frames':
            getAvailableFramesUnified($userId, $pdo);
            break;
        case 'get_item_image':
            getItemImageUnified($userId, $pdo);
            break;
        case 'get_item_set_info':
            getItemSetInfoUnified($userId, $pdo);
            break;
        default:
            echo json_encode(['success' => false, 'message' => '未知操作']);
    }
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}

/**
 * 🔄 核心装备数据处理函数 - 统一处理custom_attributes和品质等逻辑
 * @param array $items 原始装备数据数组
 * @return array 处理后的装备数据
 */
function processEquipmentData($items)
{
    foreach ($items as &$item) {
        // 🔧 统一：品质转换处理
        if (isset($item['rarity'])) {
            $rarityMapping = [
                '普通' => 'common',
                '稀有' => 'uncommon',
                '史诗' => 'rare',
                '传说' => 'epic',
                '神话' => 'legendary'
            ];

            if (isset($rarityMapping[$item['rarity']])) {
                $item['rarity'] = $rarityMapping[$item['rarity']];
            }

            $item['rarity'] = trim($item['rarity'], '"\'');
            $validRarities = ['common', 'uncommon', 'rare', 'epic', 'legendary'];
            if (!in_array($item['rarity'], $validRarities)) {
                $item['rarity'] = 'common';
            }
        } else {
            $item['rarity'] = 'common';
        }

        // 🔧 统一：处理custom_attributes中的calculated_attributes
        if (!empty($item['custom_attributes']) && $item['custom_attributes'] !== '{}') {
            $customAttributes = json_decode($item['custom_attributes'], true);
            if ($customAttributes && is_array($customAttributes)) {
                // 🎯 关键：用calculated_attributes覆盖基础属性
                if (isset($customAttributes['calculated_attributes']) && is_array($customAttributes['calculated_attributes'])) {
                    $calcAttrs = $customAttributes['calculated_attributes'];

                    if (isset($calcAttrs['physical_attack'])) $item['physical_attack'] = intval($calcAttrs['physical_attack']);
                    if (isset($calcAttrs['immortal_attack'])) $item['immortal_attack'] = intval($calcAttrs['immortal_attack']);
                    if (isset($calcAttrs['physical_defense'])) $item['physical_defense'] = intval($calcAttrs['physical_defense']);
                    if (isset($calcAttrs['immortal_defense'])) $item['immortal_defense'] = intval($calcAttrs['immortal_defense']);
                    if (isset($calcAttrs['hp_bonus'])) $item['hp_bonus'] = intval($calcAttrs['hp_bonus']);
                    if (isset($calcAttrs['mp_bonus'])) $item['mp_bonus'] = intval($calcAttrs['mp_bonus']);
                    if (isset($calcAttrs['speed_bonus'])) $item['speed_bonus'] = intval($calcAttrs['speed_bonus']);
                    if (isset($calcAttrs['critical_bonus'])) $item['critical_bonus'] = floatval($calcAttrs['critical_bonus']);
                    if (isset($calcAttrs['critical_damage'])) $item['critical_damage'] = floatval($calcAttrs['critical_damage']);
                    if (isset($calcAttrs['accuracy_bonus'])) $item['accuracy_bonus'] = floatval($calcAttrs['accuracy_bonus']);
                    if (isset($calcAttrs['dodge_bonus'])) $item['dodge_bonus'] = floatval($calcAttrs['dodge_bonus']);
                    if (isset($calcAttrs['critical_resistance'])) $item['critical_resistance'] = floatval($calcAttrs['critical_resistance']);
                }

                // 处理品质信息覆盖 - 🔧 修复：只有在原始rarity为空或默认值时才覆盖
                if (isset($customAttributes['rarity']) && !empty($customAttributes['rarity'])) {
                    // 如果原始rarity是默认值，或者custom_attributes中的rarity更准确，则使用custom_attributes中的值
                    if (!isset($item['rarity']) || $item['rarity'] === 'common' || empty($item['rarity'])) {
                        $item['rarity'] = $customAttributes['rarity'];
                    }
                }
                if (isset($customAttributes['rarity_en']) && !empty($customAttributes['rarity_en'])) {
                    $item['rarity_en'] = $customAttributes['rarity_en'];
                }

                // 合并其他自定义属性
                foreach ($customAttributes as $key => $value) {
                    if (!isset($item[$key]) || $item[$key] === null) {
                        $item[$key] = $value;
                    }
                }
            }
        }

        // 🔧 统一：确保所有属性字段都有默认值
        $item['physical_attack'] = isset($item['physical_attack']) ? intval($item['physical_attack']) : 0;
        $item['immortal_attack'] = isset($item['immortal_attack']) ? intval($item['immortal_attack']) : 0;
        $item['physical_defense'] = isset($item['physical_defense']) ? intval($item['physical_defense']) : 0;
        $item['immortal_defense'] = isset($item['immortal_defense']) ? intval($item['immortal_defense']) : 0;
        $item['hp_bonus'] = isset($item['hp_bonus']) ? intval($item['hp_bonus']) : 0;
        $item['mp_bonus'] = isset($item['mp_bonus']) ? intval($item['mp_bonus']) : 0;
        $item['speed_bonus'] = isset($item['speed_bonus']) ? intval($item['speed_bonus']) : 0;
        $item['critical_bonus'] = isset($item['critical_bonus']) ? floatval($item['critical_bonus']) : 0;
        $item['critical_damage'] = isset($item['critical_damage']) ? floatval($item['critical_damage']) : 0;
        $item['accuracy_bonus'] = isset($item['accuracy_bonus']) ? floatval($item['accuracy_bonus']) : 0;
        $item['dodge_bonus'] = isset($item['dodge_bonus']) ? floatval($item['dodge_bonus']) : 0;
        $item['critical_resistance'] = isset($item['critical_resistance']) ? floatval($item['critical_resistance']) : 0;
        $item['damage_multiplier'] = isset($item['damage_multiplier']) ? floatval($item['damage_multiplier']) : 1.0;
        $item['mp_cost'] = isset($item['mp_cost']) ? intval($item['mp_cost']) : 0;

        // 🔧 统一：图片路径处理
        if (!empty($item['icon_image'])) {
            if (strpos($item['icon_image'], 'assets/') === 0 || strpos($item['icon_image'], 'http') === 0) {
                $item['image_url'] = $item['icon_image'];
            } else {
                $item['image_url'] = 'assets/images/' . $item['icon_image'];
            }
        } else {
            $item['image_url'] = generateItemImagePath($item['name'], isset($item['item_type']) ? $item['item_type'] : 'weapon', isset($item['slot_type']) ? $item['slot_type'] : 'sword');
        }

        // 为model_image和detail_image提供默认值
        if (empty($item['model_image'])) {
            $item['model_image'] = $item['image_url'];
        }
        if (empty($item['detail_image'])) {
            $item['detail_image'] = $item['image_url'];
        }

        // 🔧 修复：为所有物品设置正确的item_id（优先使用game_item_id）
        if (isset($item['game_item_id'])) {
            $item['item_id'] = $item['game_item_id']; // 使用game_items表的ID
        }
    }

    return $items;
}

/**
 * 🔧 获取物品套装信息
 */
function getItemSetInfoUnified($userId, $pdo)
{
    try {
        $itemId = isset($_POST['item_id']) ? intval($_POST['item_id']) : 0;

        if ($itemId <= 0) {
            echo json_encode(['success' => false, 'message' => '无效的物品ID']);
            return;
        }

        // 获取角色ID
        $stmt = $pdo->prepare("SELECT id FROM characters WHERE user_id = ? LIMIT 1");
        $stmt->execute([$userId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$character) {
            echo json_encode(['success' => false, 'message' => '角色不存在']);
            return;
        }

        $characterId = $character['id'];

        // 查询物品的套装信息
        $stmt = $pdo->prepare("
            SELECT gi.set_id, es.set_name, es.max_pieces, es.effects
            FROM game_items gi
            LEFT JOIN game_item_sets es ON gi.set_id = es.id
            WHERE gi.id = ?
        ");
        $stmt->execute([$itemId]);
        $setInfo = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$setInfo || !$setInfo['set_id']) {
            echo json_encode(['success' => false, 'message' => '该物品不属于任何套装']);
            return;
        }

        // 查询已装备的同套装物品数量
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as equipped_count
            FROM character_equipment ce
            JOIN game_items gi ON ce.item_id = gi.id
            WHERE ce.character_id = ? AND gi.set_id = ?
        ");
        $stmt->execute([$characterId, $setInfo['set_id']]);
        $equippedCount = $stmt->fetch(PDO::FETCH_ASSOC)['equipped_count'];

        echo json_encode([
            'success' => true,
            'set_info' => [
                'set_id' => $setInfo['set_id'],
                'set_name' => $setInfo['set_name'],
                'effects' => $setInfo['effects'] ? json_decode($setInfo['effects'], true) : null
            ],
            'equipped_count' => intval($equippedCount),
            'total_pieces' => intval($setInfo['max_pieces'])
        ]);
    } catch (Exception $e) {
        error_log("获取套装信息失败: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => '获取套装信息失败: ' . $e->getMessage()]);
    }
}

/**
 * 🔄 重构：获取所有装备（使用统一处理函数）
 */
function getAllEquipmentUnified($userId, $pdo)
{
    try {
        // 获取角色ID
        $stmt = $pdo->prepare("SELECT id, inventory_slots FROM characters WHERE user_id = ? LIMIT 1");
        $stmt->execute([$userId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$character) {
            throw new Exception("找不到有效的角色信息");
        }

        $characterId = $character['id'];
        $inventorySlots = intval($character['inventory_slots'] ?: 30);

        // 获取背包物品
        $stmt = $pdo->prepare("
            SELECT ui.id, gi.item_name as name, gi.item_type, gi.slot_type, gi.description,
                   ui.quantity, ui.current_durability, ui.max_durability,
                   ui.enhancement_level, ui.custom_attributes, ui.bind_status,
                   gi.sell_price, gi.icon_image, gi.detail_image, gi.id as game_item_id,
                   CASE
                       WHEN ui.custom_attributes LIKE '%\"rarity\":%' THEN JSON_UNQUOTE(JSON_EXTRACT(ui.custom_attributes, '$.rarity'))
                       ELSE 'common'
                   END as rarity,
                gi.realm_requirement as level_requirement,
                   gi.hp_bonus, gi.mp_bonus, gi.physical_attack, gi.immortal_attack, 
                   gi.physical_defense, gi.immortal_defense, gi.critical_bonus, gi.critical_damage, 
                   gi.accuracy_bonus, gi.dodge_bonus, gi.critical_resistance, gi.speed_bonus, gi.special_effects,
                   ist.skill_name, ist.skill_description, ist.element_type, ist.mp_cost, ist.damage_multiplier,
                   ui.obtained_time, ui.obtained_source, ui.sort_weight
            FROM user_inventories ui
            JOIN game_items gi ON ui.item_id = gi.id
            LEFT JOIN item_skills ist ON gi.id = ist.item_id
            WHERE ui.character_id = ?
            ORDER BY ui.sort_weight ASC, ui.id ASC
        ");
        $stmt->execute([$characterId]);
        $inventory = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // 🎯 使用统一处理函数
        $inventory = processEquipmentData($inventory);

        // 为背包物品添加特有的字段
        foreach ($inventory as &$item) {
            // 🔧 修复：不要覆盖processEquipmentData中设置的item_id
            // $item['item_id'] = $item['id']; // 这行会覆盖game_item_id
            $item['type_name'] = ucfirst($item['item_type']);
            $item['upgrade_level'] = isset($item['enhancement_level']) ? $item['enhancement_level'] : 0;
            $item['quantity'] = intval(isset($item['quantity']) ? $item['quantity'] : 1);
            $item['level_requirement'] = intval(isset($item['level_requirement']) ? $item['level_requirement'] : 1);

            if (!isset($item['special_effects']) || $item['special_effects'] === null) {
                $item['special_effects'] = null;
            } else if ($item['special_effects'] && !empty($item['special_effects']) && $item['special_effects'] !== '{}') {
                $item['special_effects'] = json_decode($item['special_effects'], true);
            }
        }

        // 获取已装备的装备
        $stmt = $pdo->prepare("
            SELECT ce.slot_type, ce.item_id, ce.inventory_item_id, gi.item_name as name,
                   gi.rarity, gi.realm_requirement as level_requirement, gi.description, gi.icon_image,
                   gi.hp_bonus, gi.mp_bonus, gi.critical_bonus, gi.critical_damage, gi.accuracy_bonus,
                   gi.dodge_bonus, gi.critical_resistance, gi.speed_bonus, gi.special_effects,
                   gi.id as game_item_id, ui.current_durability, ui.max_durability
            FROM character_equipment ce
            JOIN game_items gi ON ce.item_id = gi.id
            LEFT JOIN user_inventories ui ON ce.inventory_item_id = ui.id
            WHERE ce.character_id = ? AND ce.item_id > 0 AND ce.slot_type != 'weapon'
        ");
        $stmt->execute([$characterId]);
        $equipped = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // 🎯 使用统一处理函数
        $equipped = processEquipmentData($equipped);

        echo json_encode([
            'success' => true,
            'inventory' => $inventory,
            'equipped' => $equipped,
            'inventory_slots' => $inventorySlots
        ]);
    } catch (Exception $e) {
        error_log("获取装备失败: " . $e->getMessage() . "\n" . $e->getTraceAsString());
        echo json_encode(['success' => false, 'message' => '获取装备失败: ' . $e->getMessage()]);
    }
}

/**
 * 🔄 重构：获取武器槽位（使用统一处理函数）
 */
function getWeaponSlotsUnified($userId, $pdo)
{
    try {
        // 获取角色ID
        $stmt = $pdo->prepare("SELECT id FROM characters WHERE user_id = ? LIMIT 1");
        $stmt->execute([$userId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$character) {
            throw new Exception("找不到有效的角色信息");
        }

        $characterId = $character['id'];

        // 🆕 检查并初始化武器槽位（如果需要）
        ensureWeaponSlotsExist($pdo, $characterId);

        // 获取武器槽位数据
        $stmt = $pdo->prepare("
            SELECT ce.slot_index, ce.item_id, ce.inventory_item_id,
                gi.id, gi.item_name as name, gi.description, gi.icon_image, gi.model_image, gi.detail_image,
                gi.physical_attack, gi.immortal_attack, gi.physical_defense, gi.immortal_defense,
                gi.hp_bonus, gi.mp_bonus, gi.speed_bonus, gi.critical_bonus, gi.critical_damage,
                gi.accuracy_bonus, gi.dodge_bonus, gi.critical_resistance, gi.sell_price,
                ist.skill_name, ist.skill_description, ist.element_type, ist.animation_model,
                ist.damage_multiplier, ist.mp_cost, ist.trigger_chance, ist.effect_duration,
                gi.rarity, gi.realm_requirement as level_requirement, gi.slot_type, ce.attack_order,
                ui.custom_attributes, ui.current_durability, ui.max_durability
            FROM character_equipment ce
            LEFT JOIN game_items gi ON ce.item_id = gi.id AND gi.item_type = 'weapon'
            LEFT JOIN item_skills ist ON gi.id = ist.item_id AND ist.skill_type = 'active'
            LEFT JOIN user_inventories ui ON ce.inventory_item_id = ui.id AND ui.character_id = ce.character_id
            WHERE ce.character_id = ? AND ce.slot_type = 'weapon' AND ce.slot_index BETWEEN 1 AND 6
            ORDER BY ce.slot_index
        ");
        $stmt->execute([$characterId]);
        $weaponSlots = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // 🎯 使用统一处理函数
        $weaponSlots = processEquipmentData($weaponSlots);

        // 为武器槽位添加特有的字段处理
        foreach ($weaponSlots as &$weapon) {
            if ($weapon['item_id'] > 0) {
                $weapon['inventory_item_id'] = $weapon['inventory_item_id'] ? intval($weapon['inventory_item_id']) : null;
                $weapon['current_durability'] = $weapon['current_durability'] ?: 0;
                $weapon['max_durability'] = $weapon['max_durability'] ?: 100;
                $weapon['level_requirement'] = $weapon['level_requirement'] ?: 1;
            } else {
                $weapon['inventory_item_id'] = null;
                $weapon['name'] = null;
                $weapon['icon_image'] = null;
                $weapon['model_image'] = null;
                $weapon['detail_image'] = null;
                $weapon['image_url'] = null;
                $weapon['description'] = null;
                $weapon['current_durability'] = 0;
                $weapon['max_durability'] = 0;
            }
        }

        // 确保返回6个槽位
        if (count($weaponSlots) != 6) {
            throw new Exception('武器槽位数量异常，期望6个，实际' . count($weaponSlots) . '个');
        }

        echo json_encode(['success' => true, 'weapon_slots' => $weaponSlots]);
    } catch (Exception $e) {
        error_log("获取武器槽位失败: " . $e->getMessage() . "\n" . $e->getTraceAsString());
        echo json_encode(['success' => false, 'message' => '获取武器槽位失败: ' . $e->getMessage()]);
    }
}

/** * 装备武器（统一版本） */
function equipWeaponUnified($userId, $pdo)
{
    $inventoryItemId = isset($_POST['inventory_item_id']) ? intval($_POST['inventory_item_id']) : 0;
    $slotNumber = isset($_POST['slot_number']) ? intval($_POST['slot_number']) : 0;

    if (!$inventoryItemId || !$slotNumber || $slotNumber < 1 || $slotNumber > 6) {
        echo json_encode(['success' => false, 'message' => '参数错误']);
        return;
    }

    try {
        $pdo->beginTransaction();

        // 获取用户的角色ID
        $stmt = $pdo->prepare("SELECT id FROM characters WHERE user_id = ? LIMIT 1");
        $stmt->execute([$userId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$character) {
            throw new Exception("找不到有效的角色信息");
        }

        $characterId = $character['id'];

        // 🆕 检查并初始化武器槽位（如果需要）
        ensureWeaponSlotsExist($pdo, $characterId);

        // 检查武器是否属于当前用户且是武器类型
        $stmt = $pdo->prepare("
            SELECT ui.*, gi.item_name, gi.item_type, gi.slot_type, gi.id as game_item_id
            FROM user_inventories ui
            JOIN game_items gi ON ui.item_id = gi.id
            WHERE ui.id = ? AND ui.character_id = ? AND gi.item_type = 'weapon'
        ");
        $stmt->execute([$inventoryItemId, $characterId]);
        $weapon = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$weapon) {
            throw new Exception('武器不存在或不属于当前用户');
        }

        // 🔧 正确的检查：检查目标槽位是否已有武器装备，如果有则进行替换
        $stmt = $pdo->prepare("
            SELECT ce.item_id, gi.item_name
            FROM character_equipment ce
            LEFT JOIN game_items gi ON ce.item_id = gi.id
            WHERE ce.character_id = ? AND ce.slot_type = 'weapon' AND ce.slot_index = ? AND ce.item_id > 0
        ");
        $stmt->execute([$characterId, $slotNumber]);
        $existingWeapon = $stmt->fetch(PDO::FETCH_ASSOC);

        $replacedWeaponName = null;
        if ($existingWeapon) {
            // 🔧 新增：如果槽位已有武器，记录被替换的武器名称
            $replacedWeaponName = $existingWeapon['item_name'];

            // 先卸下原武器（清空槽位）
            $stmt = $pdo->prepare("
                UPDATE character_equipment
                SET item_id = 0,
                    inventory_item_id = NULL,
                    enhancement_level = 0
                WHERE character_id = ? AND slot_type = 'weapon' AND slot_index = ?
            ");
            $stmt->execute([$characterId, $slotNumber]);
        }

        // 获取当前耐久度等信息
        $currentDurability = isset($weapon['current_durability']) ? $weapon['current_durability'] : (isset($weapon['max_durability']) ? $weapon['max_durability'] : 100);
        $maxDurability = isset($weapon['max_durability']) ? $weapon['max_durability'] : 100;
        $enhancementLevel = isset($weapon['enhancement_level']) ? $weapon['enhancement_level'] : 0;

        // 🔧 修复：更新武器槽位，如果槽位不存在则自动创建
        $stmt = $pdo->prepare("
            UPDATE character_equipment
            SET item_id = ?,
                inventory_item_id = ?,
                enhancement_level = ?
            WHERE character_id = ? AND slot_type = 'weapon' AND slot_index = ?
        ");
        $stmt->execute([
            $weapon['game_item_id'],
            $inventoryItemId,
            $enhancementLevel,
            $characterId,
            $slotNumber
        ]);

        // 🆕 如果UPDATE没有影响任何行，说明槽位不存在，需要创建
        if ($stmt->rowCount() == 0) {
            error_log("🔧 [装备修复] 武器槽位 {$slotNumber} 不存在，自动创建中...");

            // 插入新的武器槽位记录
            $stmt = $pdo->prepare("
                INSERT INTO character_equipment (
                    character_id, item_id, slot_type, slot_index,
                    inventory_item_id, enhancement_level, attack_order, is_active
                ) VALUES (?, ?, 'weapon', ?, ?, ?, ?, TRUE)
            ");
            $stmt->execute([
                $characterId,
                $weapon['game_item_id'],
                $slotNumber,
                $inventoryItemId,
                $enhancementLevel,
                $slotNumber
            ]);

            if ($stmt->rowCount() > 0) {
                error_log("✅ [装备修复] 武器槽位 {$slotNumber} 创建成功并装备武器");
            } else {
                throw new Exception("创建武器槽位失败");
            }
        }

        $pdo->commit();

        // 🔧 修改：根据是否替换武器显示不同的成功消息
        if ($replacedWeaponName) {
            echo json_encode([
                'success' => true,
                'message' => '已将 ' . $replacedWeaponName . ' 替换为 ' . $weapon['item_name'] . '（槽位' . $slotNumber . '）'
            ]);
        } else {
            echo json_encode([
                'success' => true,
                'message' => $weapon['item_name'] . ' 装备到武器槽位' . $slotNumber . '成功'
            ]);
        }
    } catch (Exception $e) {
        $pdo->rollBack();
        error_log("装备武器失败: " . $e->getMessage() . "\n" . $e->getTraceAsString());
        echo json_encode(['success' => false, 'message' => '装备武器失败: ' . $e->getMessage()]);
    }
}

/** * 卸下武器（统一版本） */
function unequipWeaponUnified($userId, $pdo)
{
    $slotNumber = isset($_POST['slot_number']) ? intval($_POST['slot_number']) : 0;

    if (!$slotNumber || $slotNumber < 1 || $slotNumber > 6) {
        echo json_encode(['success' => false, 'message' => '槽位编号错误']);
        return;
    }

    try {
        // 获取用户的角色ID
        $stmt = $pdo->prepare("SELECT id FROM characters WHERE user_id = ? LIMIT 1");
        $stmt->execute([$userId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$character) {
            throw new Exception("找不到有效的角色信息");
        }

        $characterId = $character['id'];

        // 🔧 修复：获取当前装备的武器名称用于消息显示
        $stmt = $pdo->prepare("
            SELECT ce.item_id, gi.item_name
            FROM character_equipment ce
            LEFT JOIN game_items gi ON ce.item_id = gi.id
            WHERE ce.character_id = ? AND ce.slot_type = 'weapon' AND ce.slot_index = ?
        ");
        $stmt->execute([$characterId, $slotNumber]);
        $weaponInfo = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$weaponInfo || $weaponInfo['item_id'] == 0) {
            throw new Exception('槽位' . $slotNumber . '没有装备武器');
        }

        // 🔧 修复：卸下武器，使用character_equipment表
        $stmt = $pdo->prepare("
            UPDATE character_equipment
            SET item_id = 0,
                inventory_item_id = NULL,
                enhancement_level = 0
            WHERE character_id = ? AND slot_type = 'weapon' AND slot_index = ?
        ");
        $stmt->execute([$characterId, $slotNumber]);

        echo json_encode(['success' => true, 'message' => $weaponInfo['item_name'] . ' 卸下成功']);
    } catch (Exception $e) {
        error_log("卸下武器失败: " . $e->getMessage() . "\n" . $e->getTraceAsString());
        echo json_encode(['success' => false, 'message' => '卸下武器失败: ' . $e->getMessage()]);
    }
}

/** * 装备物品（统一版本） */
function equipItemUnified($userId, $pdo)
{
    $inventoryItemId = isset($_POST['inventory_item_id']) ? intval($_POST['inventory_item_id']) : 0;

    if (!$inventoryItemId) {
        echo json_encode(['success' => false, 'message' => '缺少物品ID']);
        return;
    }

    try {
        $pdo->beginTransaction();

        // 获取用户的角色ID
        $stmt = $pdo->prepare("SELECT id FROM characters WHERE user_id = ? LIMIT 1");
        $stmt->execute([$userId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$character) {
            throw new Exception("找不到有效的角色信息");
        }

        $characterId = $character['id'];

        // 检查物品是否属于当前用户且可装备（排除武器，武器通过专门的武器槽位装备）
        $stmt = $pdo->prepare("
            SELECT ui.*, gi.item_name, gi.item_type, gi.slot_type, gi.id as game_item_id
            FROM user_inventories ui
            JOIN game_items gi ON ui.item_id = gi.id
            WHERE ui.id = ? AND ui.character_id = ? AND gi.item_type != 'weapon' AND gi.slot_type IS NOT NULL
        ");
        $stmt->execute([$inventoryItemId, $characterId]);
        $item = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$item) {
            // 提供更详细的错误信息
            $stmt = $pdo->prepare("
                SELECT ui.*, gi.item_name, gi.item_type, gi.slot_type
                 FROM user_inventories ui
                JOIN game_items gi ON ui.item_id = gi.id
                WHERE ui.id = ? AND ui.character_id = ?
            ");
            $stmt->execute([$inventoryItemId, $characterId]);
            $checkItem = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$checkItem) {
                throw new Exception('物品不存在或不属于您');
            } elseif ($checkItem['item_type'] === 'weapon') {
                throw new Exception('武器请通过武器槽位装备');
            } elseif (!$checkItem['slot_type']) {
                throw new Exception('该物品无法装备');
            } else {
                throw new Exception('物品类型不支持装备');
            }
        }

        // 🔧 修复：检查该类型装备是否已有装备，如果有则替换
        $stmt = $pdo->prepare("
            SELECT id FROM character_equipment
             WHERE character_id = ? AND slot_type = ?
        ");
        $stmt->execute([$characterId, $item['slot_type']]);
        $existingEquipment = $stmt->fetch(PDO::FETCH_ASSOC);

        // 获取当前耐久度等信息
        $currentDurability = isset($item['current_durability']) ? $item['current_durability'] : (isset($item['max_durability']) ? $item['max_durability'] : 100);
        $enhancementLevel = isset($item['enhancement_level']) ? $item['enhancement_level'] : 0;

        if ($existingEquipment) {
            // 🔧 修复：更新现有装备槽位
            $stmt = $pdo->prepare("
                UPDATE character_equipment
                SET item_id = ?,
                    inventory_item_id = ?,
                    enhancement_level = ?
                WHERE character_id = ? AND slot_type = ?
            ");
            $stmt->execute([
                $item['game_item_id'],
                $inventoryItemId,
                $enhancementLevel,
                $characterId,
                $item['slot_type']
            ]);
        } else {
            // 🔧 修复：创建新装备槽位
            $stmt = $pdo->prepare("
                INSERT INTO character_equipment (
                    character_id, item_id, inventory_item_id, slot_type, 
                    enhancement_level, is_active
                ) VALUES (?, ?, ?, ?, ?, TRUE)
            ");
            $stmt->execute([
                $characterId,
                $item['game_item_id'],
                $inventoryItemId,
                $item['slot_type'],
                $enhancementLevel
            ]);
        }

        $pdo->commit();
        echo json_encode(['success' => true, 'message' => $item['item_name'] . ' 装备成功']);
    } catch (Exception $e) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/** * 卸下装备（统一版本） */
function unequipItemUnified($userId, $pdo)
{
    $slotType = isset($_POST['slot_type']) ? $_POST['slot_type'] : '';

    if (!$slotType) {
        echo json_encode(['success' => false, 'message' => '缺少槽位类型']);
        return;
    }

    try {
        // 获取用户的角色ID
        $stmt = $pdo->prepare("SELECT id FROM characters WHERE user_id = ? LIMIT 1");
        $stmt->execute([$userId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$character) {
            throw new Exception("找不到有效的角色信息");
        }

        $characterId = $character['id'];

        // 🔧 修复：获取当前装备的物品名称用于消息显示
        $stmt = $pdo->prepare("
            SELECT ce.item_id, gi.item_name
            FROM character_equipment ce
            LEFT JOIN game_items gi ON ce.item_id = gi.id
            WHERE ce.character_id = ? AND ce.slot_type = ?
        ");
        $stmt->execute([$characterId, $slotType]);
        $equippedItem = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$equippedItem || $equippedItem['item_id'] == 0) {
            throw new Exception('该槽位没有装备物品');
        }

        // 🔧 修复：卸下装备
        $stmt = $pdo->prepare("
            UPDATE character_equipment
            SET item_id = 0,
                inventory_item_id = NULL,
                enhancement_level = 0
            WHERE character_id = ? AND slot_type = ?
        ");
        $stmt->execute([$characterId, $slotType]);

        $itemName = $equippedItem ? $equippedItem['item_name'] : '装备';
        echo json_encode(['success' => true, 'message' => $itemName . ' 卸下成功']);
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => '卸下装备失败: ' . $e->getMessage()]);
    }
}

/** * 修复武器（统一版本） */
function repairWeaponUnified($userId, $pdo)
{
    $inventoryItemId = isset($_POST['inventory_item_id']) ? intval($_POST['inventory_item_id']) : 0;

    if (!$inventoryItemId) {
        echo json_encode(['success' => false, 'message' => '缺少武器ID']);
        return;
    }

    try {
        $pdo->beginTransaction();

        // 获取用户的角色ID
        $stmt = $pdo->prepare("SELECT id FROM characters WHERE user_id = ? LIMIT 1");
        $stmt->execute([$userId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$character) {
            throw new Exception("找不到有效的角色信息");
        }

        $characterId = $character['id'];

        // 检查武器是否属于当前用户
        $stmt = $pdo->prepare("
            SELECT ui.*, gi.item_name, gi.item_type, gi.sell_price
            FROM user_inventories ui
            JOIN game_items gi ON ui.item_id = gi.id
            WHERE ui.id = ? AND ui.character_id = ? AND gi.item_type = 'weapon'
        ");
        $stmt->execute([$inventoryItemId, $characterId]);
        $weapon = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$weapon) {
            throw new Exception('武器不存在或不属于当前用户');
        }

        if ($weapon['current_durability'] >= $weapon['max_durability']) {
            throw new Exception('武器耐久度已满，无需修复');
        }

        // 计算修复费用（固定费率：每点耐久100金币）
        // 需要修复的耐久点数
        $durabilityToRepair = $weapon['max_durability'] - $weapon['current_durability'];

        // 修复费用 = 需要修复的耐久点数 × 100金币
        $repairCost = $durabilityToRepair * 100;

        // 确保至少1金币
        $repairCost = max(1, $repairCost);

        // 🔧 修复：检查用户金币（直接使用gold字段）
        $stmt = $pdo->prepare("SELECT gold FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$user) {
            throw new Exception('用户不存在');
        }

        $userGold = intval($user['gold']);

        if ($userGold < $repairCost) {
            throw new Exception("金币不足，无法修复。需要 {$repairCost} 金币，当前拥有 {$userGold} 金币");
        }

        // 🔧 修复：扣除用户金币
        $stmt = $pdo->prepare("
            UPDATE users
            SET gold = gold - ?
            WHERE id = ?
        ");
        $stmt->execute([$repairCost, $userId]);

        // 修复武器（恢复到最大耐久度）
        $stmt = $pdo->prepare("
            UPDATE user_inventories
            SET current_durability = max_durability
            WHERE id = ?
        ");
        $stmt->execute([$inventoryItemId]);

        $pdo->commit();
        echo json_encode([
            'success' => true,
            'message' => $weapon['item_name'] . ' 修复成功',
            'repair_cost' => $repairCost
        ]);
    } catch (Exception $e) {
        $pdo->rollBack();
        error_log("修复武器失败: " . $e->getMessage() . "\n" . $e->getTraceAsString());
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/** * 获取用户属性统计（统一版本） */
function getUserStatsUnified($userId, $pdo)
{
    try {
        // 获取用户基础数据
        $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$user) {
            throw new Exception('用户不存在');
        }

        // 获取用户的角色数据
        $stmt = $pdo->prepare("
            SELECT * FROM characters
             WHERE user_id = ?
        ");
        $stmt->execute([$userId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);

        // 🔧 重构：使用统一装备管理器
        require_once __DIR__ . '/../includes/equipment_stats_manager.php';

        // 🔧 修复：直接使用已获取的角色数据，不要重复查询覆盖
        $characterId = $character ? $character['id'] : 0;

        // 🔧 简化：统一获取所有装备属性（包括武器）
        $allEquipmentStats = EquipmentStatsManager::getAllEquipmentStats($pdo, $characterId);

        // 🔧 修复：初始化基础属性 - 统一使用正确的字段名
        $baseStats = [
            'physical_attack' => 20,
            'immortal_attack' => 15,
            'physical_defense' => 10,
            'immortal_defense' => 8,
            'hp_bonus' => 300,              // 修正：max_hp -> hp_bonus
            'mp_bonus' => 200,              // 修正：max_mp -> mp_bonus
            'speed_bonus' => 50,            // 修正：speed -> speed_bonus
            'critical_bonus' => 0.05,
            'accuracy_bonus' => 80.0,
            'dodge_bonus' => 5.0,
            'critical_resistance' => 0,
            'critical_damage' => 0,
            // 移除重复字段定义
        ];

        // 如果有角色数据，使用角色的属性
        if ($character) {
            // 获取境界属性加成
            $stmt = $pdo->prepare("
                SELECT rl.*
                 FROM realm_levels rl
                WHERE rl.id = ?
            ");
            $stmt->execute([$character['realm_id']]);
            $realm = $stmt->fetch(PDO::FETCH_ASSOC);

            // 将境界信息添加到角色数据中
            $character['realm_name'] = isset($realm['realm_name']) ? $realm['realm_name'] : '练气期';
            $character['realm_level'] = isset($realm['realm_level']) ? $realm['realm_level'] : 1;
            $character['hp_multiplier'] = isset($realm['hp_multiplier']) ? $realm['hp_multiplier'] : 1;
            $character['mp_multiplier'] = isset($realm['mp_multiplier']) ? $realm['mp_multiplier'] : 1;
            $character['attack_multiplier'] = isset($realm['attack_multiplier']) ? $realm['attack_multiplier'] : 1;
            $character['defense_multiplier'] = isset($realm['defense_multiplier']) ? $realm['defense_multiplier'] : 1;
            $character['speed_multiplier'] = isset($realm['speed_multiplier']) ? $realm['speed_multiplier'] : 1;

            // 使用通用属性计算函数（已包含装备加成，无需重复累加）
            $finalStats = calculateCharacterAttributes($character, $allEquipmentStats, [], []);
        } else {
            // 没有角色时使用默认值
            $finalStats = $baseStats;
        }

        // 添加用户基础信息
        $finalStats['username'] = $user['username'];

        // 如果有角色，使用角色信息
        if ($character) {
            $finalStats['character_id'] = $character['id'];
            $finalStats['character_name'] = $character['character_name'];
            $finalStats['character_avatar'] = $character['avatar_image'];
            $finalStats['avatar_image'] = $character['avatar_image'];
            $finalStats['avatar_frame'] = $character['avatar_frame']; // 🔧 新增：头像外框
            $finalStats['realm_id'] = $character['realm_id'];
            $finalStats['realm_name'] = isset($realm['realm_name']) ? $realm['realm_name'] : '练气期';
            $finalStats['realm_level'] = isset($realm['realm_level']) ? $realm['realm_level'] : 1;

            // 🔧 新增：添加背包容量字段
            $finalStats['inventory_slots'] = isset($character['inventory_slots']) ? intval($character['inventory_slots']) : 30;

            // 角色基础属性
            $finalStats['physique'] = $character['physique'];
            $finalStats['constitution'] = $character['constitution'];
            $finalStats['comprehension'] = $character['comprehension'];
            $finalStats['spirit'] = $character['spirit'];
            $finalStats['agility'] = $character['agility'];
        } else {
            // 没有角色时使用默认值
            $finalStats['character_name'] = '未创建角色';
            $finalStats['character_avatar'] = 'ck.png';
            $finalStats['avatar_image'] = 'ck.png';
            $finalStats['avatar_frame'] = 'base1.png'; // 🔧 新增：默认头像外框
            $finalStats['physique'] = 10;
            $finalStats['constitution'] = 10;
            $finalStats['comprehension'] = 10;
            $finalStats['spirit'] = 10;
            $finalStats['agility'] = 10;
            $finalStats['realm_id'] = 1;
            $finalStats['realm_name'] = '练气期';
            $finalStats['realm_level'] = 1;

            // 🔧 新增：添加默认背包容量
            $finalStats['inventory_slots'] = 30;
        }

        // 用户资源 - 确保不会有null或undefined
        $finalStats['spiritual_power'] = isset($user['spiritual_power']) ? intval($user['spiritual_power']) : 0;
        $finalStats['silver'] = isset($user['silver']) ? intval($user['silver']) : 0;
        $finalStats['spirit_stones'] = isset($user['spirit_stones']) ? intval($user['spirit_stones']) : 0;
        $finalStats['gold'] = isset($user['gold']) ? intval($user['gold']) : 0;

        // 确保所有属性都是有效数值，但保护字符串字段
        $stringFields = ['character_name', 'character_avatar', 'avatar_image', 'avatar_frame', 'realm_name', 'username'];

        foreach ($finalStats as $key => $value) {
            if (in_array($key, $stringFields)) {
                // 字符串字段：确保不为null，但保持字符串类型
                if ($value === null || $value === '') {
                    if (in_array($key, ['character_avatar', 'avatar_image'])) {
                        $finalStats[$key] = 'ck.png'; // 头像字段默认值
                    } elseif ($key === 'avatar_frame') {
                        $finalStats[$key] = 'base1.png'; // 头像外框默认值
                    } else {
                        $finalStats[$key] = '';
                    }
                }
                // 保持字符串类型，不转换为数值
            } elseif (is_numeric($value)) {
                $finalStats[$key] = floatval($value);
            } elseif ($value === null) {
                $finalStats[$key] = 0;
            }
        }

        echo json_encode([
            'success' => true,
            'user' => $finalStats,
            'stats' => $finalStats, // 🔧 新增：为前端提供stats字段
            'equipment_stats' => $allEquipmentStats, // 🔧 简化：统一的装备属性
            'base_stats' => $baseStats
        ]);
    } catch (Exception $e) {
        error_log("获取用户属性失败: " . $e->getMessage() . "\n" . $e->getTraceAsString());
        echo json_encode(['success' => false, 'message' => '获取用户属性失败: ' . $e->getMessage()]);
    }
}

// 🔧 重构：装备属性计算函数已移至统一管理器 (src/includes/equipment_stats_manager.php)

// 🔧 重构：武器属性计算函数已移至统一管理器 (src/includes/equipment_stats_manager.php)

/** * 回收物品（统一版本） */
function recycleItemUnified($userId, $pdo)
{
    $inventoryItemId = isset($_POST['inventory_item_id']) ? intval($_POST['inventory_item_id']) : 0;
    $recycleQuantity = isset($_POST['recycle_quantity']) ? intval($_POST['recycle_quantity']) : 1;

    if (!$inventoryItemId) {
        echo json_encode(['success' => false, 'message' => '缺少物品ID']);
        return;
    }

    if ($recycleQuantity <= 0) {
        echo json_encode(['success' => false, 'message' => '回收数量必须大于0']);
        return;
    }

    try {
        $pdo->beginTransaction();

        // 获取用户的角色ID
        $stmt = $pdo->prepare("SELECT id FROM characters WHERE user_id = ? LIMIT 1");
        $stmt->execute([$userId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$character) {
            throw new Exception("找不到有效的角色信息");
        }

        $characterId = $character['id'];

        // 检查物品是否属于当前用户
        $stmt = $pdo->prepare("
            SELECT ui.*, gi.item_name, gi.item_type, gi.sell_price
            FROM user_inventories ui
            JOIN game_items gi ON ui.item_id = gi.id
            WHERE ui.id = ? AND ui.character_id = ?
        ");
        $stmt->execute([$inventoryItemId, $characterId]);
        $item = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$item) {
            throw new Exception('物品不存在或不属于您');
        }

        // 🔧 新增：检查物品数量是否足够
        $currentQuantity = intval($item['quantity']);
        if ($recycleQuantity > $currentQuantity) {
            throw new Exception("回收数量不能超过当前拥有数量（{$currentQuantity}）");
        }

        // 🔧 修复：检查物品是否已装备（武器）- 统一使用character_equipment表
        if ($item['item_type'] === 'weapon') {
            // 🔧 修复关键问题：使用inventory_item_id而不是item_id来检查装备状态
            // 从character_equipment的custom_attributes中查找对应的inventory_item_id
            $stmt = $pdo->prepare("
                SELECT ce.slot_index, ce.inventory_item_id
                FROM character_equipment ce
                WHERE ce.character_id = ? AND ce.slot_type = 'weapon' AND ce.item_id > 0
            ");
            $stmt->execute([$characterId]);
            $weaponSlots = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $isEquipped = false;
            foreach ($weaponSlots as $slot) {
                $equippedInventoryId = intval($slot['inventory_item_id']);
                if ($equippedInventoryId === $inventoryItemId) {
                    $isEquipped = true;
                    $equippedSlot = $slot;
                    break;
                }
            }

            if ($isEquipped) {
                throw new Exception('已装备的武器无法回收，请先卸下（槽位 ' . $equippedSlot['slot_index'] . '）');
            }
        }

        // 🔧 修复：检查装备是否已装备 - 使用character_equipment表和inventory_item_id
        if (in_array($item['item_type'], ['armor', 'accessory', 'equipment'])) {
            // 🔧 修复关键问题：使用inventory_item_id而不是item_id来检查装备状态
            $stmt = $pdo->prepare("
                SELECT ce.slot_type, ce.inventory_item_id
                FROM character_equipment ce
                WHERE ce.character_id = ? AND ce.slot_type != 'weapon' AND ce.item_id > 0
            ");
            $stmt->execute([$characterId]);
            $equipmentSlots = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $isEquipped = false;
            $equippedSlotType = '';
            foreach ($equipmentSlots as $slot) {
                $equippedInventoryId = intval($slot['inventory_item_id']);
                if ($equippedInventoryId === $inventoryItemId) {
                    $isEquipped = true;
                    $equippedSlotType = $slot['slot_type'];
                    break;
                }
            }

            if ($isEquipped) {
                throw new Exception('已装备的 ' . $equippedSlotType . ' 无法回收，请先卸下');
            }
        }

        // 🔧 修复：根据品质计算回收价格
        $sellPrice = intval($item['sell_price']);

        // 获取物品品质信息
        $itemRarity = '普通'; // 默认品质

        // 🔧 修复：从合并后的game_items表获取品质信息
        $stmt = $pdo->prepare("
            SELECT gi.rarity, gi.special_effects 
            FROM user_inventories ui
            JOIN game_items gi ON ui.item_id = gi.id
            WHERE ui.id = ?
        ");
        $stmt->execute([$inventoryItemId]);
        $itemData = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($itemData) {
            // 优先使用直接的rarity字段
            if (!empty($itemData['rarity'])) {
                $itemRarity = $itemData['rarity'];
            }
            // 备用：从special_effects JSON字段获取品质信息
            elseif (!empty($itemData['special_effects'])) {
                $specialEffects = json_decode($itemData['special_effects'], true);
                if (isset($specialEffects['rarity'])) {
                    $itemRarity = $specialEffects['rarity'];
                }
            }
        }

        // 为没有售价的物品设置默认售价（根据类型和品质）
        if ($sellPrice <= 0) {
            $basePrice = 1;
            switch ($item['item_type']) {
                case 'weapon':
                    $basePrice = 10; // 武器基础售价
                    break;
                case 'armor':
                case 'equipment':
                    $basePrice = 6; // 装备基础售价
                    break;
                case 'consumable':
                    $basePrice = 2; // 消耗品基础售价
                    break;
                case 'material':
                    $basePrice = 3; // 材料基础售价
                    break;
                case 'currency':
                    $basePrice = 1; // 货币基础售价
                    break;
                default:
                    $basePrice = 2; // 其他物品基础售价
            }

            // 根据品质调整售价
            $qualityMultiplier = 1.0;
            switch ($itemRarity) {
                case '普通':
                    $qualityMultiplier = 1.0;
                    break;
                case '稀有':
                    $qualityMultiplier = 1.5;
                    break;
                case '史诗':
                    $qualityMultiplier = 2.0;
                    break;
                case '传说':
                    $qualityMultiplier = 3.0;
                    break;
                case '神话':
                    $qualityMultiplier = 5.0;
                    break;
            }

            $sellPrice = intval($basePrice * $qualityMultiplier);
        }

        // 计算回收价格（售价的40%，品质越高回收比例越高）
        $recycleRatio = 0.4; // 基础回收比例40%
        switch ($itemRarity) {
            case '稀有':
                $recycleRatio = 0.45; // 稀有45%
                break;
            case '史诗':
                $recycleRatio = 0.5; // 史诗50%
                break;
            case '传说':
                $recycleRatio = 0.55; // 传说55%
                break;
            case '神话':
                $recycleRatio = 0.6; // 神话60%
                break;
        }

        $singleItemRecyclePrice = max(1, intval($sellPrice * $recycleRatio)); // 单个物品至少给1金币
        $totalRecyclePrice = $singleItemRecyclePrice * $recycleQuantity; // 总回收价格

        if ($sellPrice <= 0) {
            throw new Exception('该物品无法回收');
        }

        // 🔧 修复：根据回收数量更新或删除物品
        if ($recycleQuantity >= $currentQuantity) {
            // 回收全部数量，删除整个物品条目
            $stmt = $pdo->prepare("DELETE FROM user_inventories WHERE id = ?");
            $stmt->execute([$inventoryItemId]);
        } else {
            // 回收部分数量，更新剩余数量
            $remainingQuantity = $currentQuantity - $recycleQuantity;
            $stmt = $pdo->prepare("UPDATE user_inventories SET quantity = ? WHERE id = ?");
            $stmt->execute([$remainingQuantity, $inventoryItemId]);
        }

        // 增加用户金币
        $stmt = $pdo->prepare("
            UPDATE users
            SET gold = COALESCE(gold, 0) + ?
            WHERE id = ?
        ");
        $stmt->execute([$totalRecyclePrice, $userId]);

        $pdo->commit();

        // 记录日志
        error_log("物品回收成功: 用户ID {$userId}, 角色ID {$characterId}, 物品ID {$inventoryItemId}, 物品名称 {$item['item_name']}, 回收数量 {$recycleQuantity}, 总回收价格 {$totalRecyclePrice}");

        // 🔧 修复：构建回收成功信息，包含数量
        $quantityText = $recycleQuantity > 1 ? " × {$recycleQuantity}" : "";
        $remainingText = ($recycleQuantity < $currentQuantity) ? "（剩余 " . ($currentQuantity - $recycleQuantity) . " 个）" : "";

        echo json_encode([
            'success' => true,
            'message' => "成功回收 {$item['item_name']}{$quantityText}，获得 {$totalRecyclePrice} 金币{$remainingText}",
            'recycle_price' => $totalRecyclePrice,
            'recycle_quantity' => $recycleQuantity,
            'single_price' => $singleItemRecyclePrice,
            'remaining_quantity' => $currentQuantity - $recycleQuantity,
            'item_name' => $item['item_name']
        ]);
    } catch (Exception $e) {
        $pdo->rollBack();
        error_log("物品回收失败: " . $e->getMessage() . "\n" . $e->getTraceAsString());
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/** * 更换角色头像 */
function changeAvatarUnified($userId, $pdo)
{
    $avatar = isset($_POST['avatar']) ? trim($_POST['avatar']) : '';

    if (!$avatar) {
        echo json_encode(['success' => false, 'message' => '缺少头像参数']);
        return;
    }

    try {
        // 验证头像文件名（安全检查）
        if (!preg_match('/^[a-zA-Z0-9_.-]+\.png$/i', $avatar)) {
            throw new Exception('头像文件名格式错误');
        }

        // 检查头像文件是否存在（预留付费头像检查）
        $isVipAvatar = strpos($avatar, 'vip/') === 0;
        $avatarPath = $isVipAvatar ?
            __DIR__ . '/../../public/assets/images/char/' . $avatar :
            __DIR__ . '/../../public/assets/images/char/' . $avatar;

        if (!file_exists($avatarPath)) {
            throw new Exception('头像文件不存在');
        }

        // 如果是付费头像，检查用户是否有权限（预留功能）
        if ($isVipAvatar) {
            // TODO: 检查用户是否购买了该付费头像
            // $hasPermission = checkVipAvatarPermission($userId, $avatar);
            // if (!$hasPermission) {
            //     throw new Exception('您还未购买此头像');
            // }
            echo json_encode(['success' => false, 'message' => '付费头像功能暂未开放']);
            return;
        }

        // 🔧 修复：更新角色头像 - 使用characters表的avatar_image字段
        $stmt = $pdo->prepare("UPDATE characters SET avatar_image = ? WHERE user_id = ?");
        $result = $stmt->execute([$avatar, $userId]);

        if ($result) {
            // 更新session
            $_SESSION['character_avatar'] = $avatar;

            echo json_encode([
                'success' => true,
                'message' => '头像更换成功',
                'avatar' => $avatar
            ]);
        } else {
            throw new Exception('头像更换失败');
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/** * 获取可用头像列表 */
function getAvailableAvatarsUnified($userId, $pdo)
{
    try {
        $avatars = [
            'free' => [],
            'vip' => []
        ];

        // 获取免费头像列表 - 修复路径
        $freeAvatarDir = __DIR__ . '/../../public/assets/images/char/';
        if (is_dir($freeAvatarDir)) {
            $files = scandir($freeAvatarDir);
            foreach ($files as $file) {
                if (preg_match('/\.png$/i', $file) && is_file($freeAvatarDir . $file)) {
                    $avatars['free'][] = [
                        'filename' => $file,
                        'name' => pathinfo($file, PATHINFO_FILENAME),
                        'type' => 'free',
                        'owned' => true // 免费头像都可用
                    ];
                }
            }
        } else {
            // 如果目录不存在，提供默认头像列表
            $avatars['free'] = [
                ['filename' => 'ck.png', 'name' => '初心', 'type' => 'free', 'owned' => true],
                ['filename' => 'cy.png', 'name' => '慈悲', 'type' => 'free', 'owned' => true],
                ['filename' => 'huaishang.png', 'name' => '怀伤', 'type' => 'free', 'owned' => true],
                ['filename' => 'my.png', 'name' => '明月', 'type' => 'free', 'owned' => true]
            ];
        }

        // 获取付费头像列表（预留功能）- 修复路径
        $vipAvatarDir = __DIR__ . '/../../public/assets/images/char/vip/';
        if (is_dir($vipAvatarDir)) {
            $files = scandir($vipAvatarDir);
            foreach ($files as $file) {
                if (preg_match('/\.png$/i', $file) && is_file($vipAvatarDir . $file)) {
                    // TODO: 检查用户是否购买了该付费头像
                    // $owned = checkVipAvatarPermission($userId, 'vip/' . $file);
                    $owned = false; // checkVipAvatarPermission($userId, 'vip/' . $file);

                    $avatars['vip'][] = [
                        'filename' => 'vip/' . $file,
                        'name' => pathinfo($file, PATHINFO_FILENAME),
                        'type' => 'vip',
                        'owned' => $owned,
                        'price' => 100 // 假设价格，可以从数据库获取
                    ];
                }
            }
        }

        echo json_encode([
            'success' => true,
            'avatars' => $avatars
        ]);
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => '获取头像列表失败: ' . $e->getMessage()]);
    }
}

/** * 🔧 新增：根据物品名称、类型和槽位生成图片路径 */
function generateItemImagePath($itemName, $itemType, $slotType = '')
{
    // 根据物品名称中的境界关键词确定等级
    $realmKeywords = [
        '开光' => 1,
        '灵虚' => 2,
        '辟谷' => 3,
        '心动' => 4,
        '元化' => 5,
        '元婴' => 6,
        '离合' => 7,
        '空冥' => 8,
        '寂灭' => 9,
        '大乘' => 10
    ];
    $realmLevel = 1; // 默认等级
    foreach ($realmKeywords as $keyword => $level) {
        if (strpos($itemName, $keyword) !== false) {
            $realmLevel = $level;
            break;
        }
    }

    if ($itemType === 'weapon') {
        if ($slotType === 'sword') {
            // 剑类武器图片映射
            $swordImages = [
                1 => 'bw_11001.png',
                2 => 'bw_11002.png',
                3 => 'bw_11003.png',
                4 => 'bw_11004.png',
                5 => 'bw_11301.png',
                6 => 'bw_11601.png',
                7 => 'bw_11602.png',
                8 => 'bw_11603.png',
                9 => 'bw_11604.png',
                10 => 'bw_11605.png'
            ];
            return 'assets/images/' . (isset($swordImages[$realmLevel]) ? $swordImages[$realmLevel] : $swordImages[1]);
        } elseif ($slotType === 'fan') {
            // 扇类武器图片映射
            $fanImages = [
                1 => 'bw_10301.png',
                2 => 'bw_10302.png',
                3 => 'bw_10601.png',
                4 => 'bw_10602.png',
                5 => 'bw_10801.png',
                6 => 'bw_11801.png',
                7 => 'bw_11802.png',
                8 => 'bw_11606.png',
                9 => 'bw_11607.png',
                10 => 'bw_11608.png'
            ];
            return 'assets/images/' . (isset($fanImages[$realmLevel]) ? $fanImages[$realmLevel] : $fanImages[1]);
        }
        return 'assets/images/battle_sword.png'; // 默认武器图片
    } elseif ($itemType === 'equipment') {
        if ($slotType === 'ring') {
            // 戒指类装备图片映射
            $ringImages = [
                1 => '1200.png',
                2 => '1201.png',
                3 => '1202.png',
                4 => '1203.png',
                5 => '1204.png',
                6 => '1205.png',
                7 => '1206.png',
                8 => '1207.png',
                9 => '1208.png',
                10 => '1209.png'
            ];
            return 'assets/images/' . (isset($ringImages[$realmLevel]) ? $ringImages[$realmLevel] : $ringImages[1]);
        } elseif ($slotType === 'bracers') {
            // 护臂类装备图片映射
            $bracersImages = [
                1 => 'zb_10101.png',
                2 => 'zb_10201.png',
                3 => 'zb_10202.png',
                4 => 'zb_10261.png',
                5 => 'zb_10301.png',
                6 => 'zb_10302.png',
                7 => 'zb_10361.png',
                8 => 'zb_10401.png',
                9 => 'zb_10402.png',
                10 => 'zb_10403.png'
            ];
            return 'assets/images/' . (isset($bracersImages[$realmLevel]) ? $bracersImages[$realmLevel] : $bracersImages[1]);
        } elseif ($slotType === 'chest') {
            // 胸甲类装备图片映射
            $chestImages = [
                1 => 'zb_20101.png',
                2 => 'zb_20201.png',
                3 => 'zb_20202.png',
                4 => 'zb_20261.png',
                5 => 'zb_20301.png',
                6 => 'zb_20302.png',
                7 => 'zb_20361.png',
                8 => 'zb_20401.png',
                9 => 'zb_20402.png',
                10 => 'zb_20403.png'
            ];
            return 'assets/images/' . (isset($chestImages[$realmLevel]) ? $chestImages[$realmLevel] : $chestImages[1]);
        }
        return 'assets/images/zb_10101.png'; // 默认装备图片
    } elseif ($itemType === 'material') {
        return 'assets/images/1100.png';
    } elseif ($itemType === 'consumable') {
        return 'assets/images/1101.png';
    } elseif ($itemType === 'currency') {
        return 'assets/images/1102.png';
    }

    return 'assets/images/1100.png'; // 默认图片
}

/** * 更新武器耐久度（统一版本） */
function updateWeaponDurability($userId, $pdo)
{
    try {
        // 获取角色ID
        $stmt = $pdo->prepare("SELECT id FROM characters WHERE user_id = ? LIMIT 1");
        $stmt->execute([$userId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$character) {
            throw new Exception("找不到有效的角色信息");
        }

        $characterId = $character['id'];

        // 获取武器更新数据
        $weaponUpdatesJson = isset($_POST['weapon_updates']) ? $_POST['weapon_updates'] : '';

        if (empty($weaponUpdatesJson)) {
            throw new Exception("缺少武器更新数据");
        }

        $weaponUpdates = json_decode($weaponUpdatesJson, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("武器更新数据格式错误");
        }

        // 开始事务
        $pdo->beginTransaction();

        try {
            foreach ($weaponUpdates as $update) {
                $inventoryId = $update['inventory_id'];
                $durabilityChange = $update['durability_change'];

                // 更新耐久度，使用正确的字段名 current_durability
                $stmt = $pdo->prepare("
                    UPDATE user_inventories 
                    SET current_durability = GREATEST(0, current_durability + ?) 
                    WHERE id = ? AND character_id = ?
                ");
                $stmt->execute([$durabilityChange, $inventoryId, $characterId]);

                if ($stmt->rowCount() === 0) {
                    throw new Exception("无效的物品ID: " . $inventoryId);
                }
            }

            // 提交事务
            $pdo->commit();

            // 返回成功响应
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'message' => '武器耐久度更新成功',
                'updates' => $weaponUpdates
            ]);
        } catch (Exception $e) {
            // 回滚事务
            $pdo->rollBack();
            throw $e;
        }
    } catch (Exception $e) {
        // 返回错误响应
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

/** * 🔒 安全函数：验证和清理inventory_item_id */
function validateInventoryItemId($inventoryItemId, $characterId, $pdo)
{
    // 验证ID格式
    $inventoryItemId = filter_var($inventoryItemId, FILTER_VALIDATE_INT);
    if ($inventoryItemId === false || $inventoryItemId <= 0) {
        return null;
    }

    // 🔧 修复：验证inventory_item_id是否存在且属于当前角色
    $stmt = $pdo->prepare("
        SELECT id FROM user_inventories
         WHERE id = ? AND character_id = ?
    ");
    $stmt->execute([$inventoryItemId, $characterId]);
    $exists = $stmt->fetchColumn();

    return $exists ? $inventoryItemId : null;
}

/** * 🔒 安全函数：安全地解析custom_attributes JSON */
function safeParseCustomAttributes($jsonString)
{
    if (empty($jsonString) || !is_string($jsonString)) {
        return [];
    }

    $decoded = json_decode($jsonString, true);
    if (json_last_error() !== JSON_ERROR_NONE || !is_array($decoded)) {
        error_log("JSON解析错误: " . json_last_error_msg() . " - 数据: " . $jsonString);
        return [];
    }

    // 过滤和验证数据
    $safe = [];
    if (isset($decoded['inventory_item_id'])) {
        $id = filter_var($decoded['inventory_item_id'], FILTER_VALIDATE_INT);
        if ($id !== false && $id > 0) {
            $safe['inventory_item_id'] = $id;
        }
    }

    // 保留其他安全的属性
    foreach ($decoded as $key => $value) {
        if ($key !== 'inventory_item_id' && is_scalar($value)) {
            $safe[$key] = $value;
        }
    }

    return $safe;
}

/** * 🔒 安全函数：安全地创建custom_attributes JSON */
function safeCreateCustomAttributes($inventoryItemId, $existingAttributes = null)
{
    // 验证inventory_item_id
    $inventoryItemId = filter_var($inventoryItemId, FILTER_VALIDATE_INT);
    if ($inventoryItemId === false || $inventoryItemId <= 0) {
        throw new Exception('无效的物品ID');
    }

    // 解析现有属性
    $customAttributesData = [];
    if (!empty($existingAttributes)) {
        $customAttributesData = safeParseCustomAttributes($existingAttributes);
    }

    // 设置inventory_item_id
    $customAttributesData['inventory_item_id'] = $inventoryItemId;

    return json_encode($customAttributesData, JSON_UNESCAPED_UNICODE);
}

// 🆕 使用道具功能（包括功法秘籍）
function useItemUnified($userId, $pdo)
{
    $inventoryItemId = isset($_POST['inventory_item_id']) ? intval($_POST['inventory_item_id']) : 0;

    if (!$inventoryItemId) {
        echo json_encode(['success' => false, 'message' => '缺少物品ID']);
        return;
    }

    try {
        $pdo->beginTransaction();

        // 获取用户的角色ID
        $stmt = $pdo->prepare("SELECT id FROM characters WHERE user_id = ? LIMIT 1");
        $stmt->execute([$userId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$character) {
            throw new Exception("找不到有效的角色信息");
        }

        $characterId = $character['id'];

        // 检查物品是否属于当前用户且可使用
        $stmt = $pdo->prepare("
            SELECT ui.*, gi.item_name, gi.item_type, gi.slot_type, gi.item_code, gi.description
            FROM user_inventories ui
            JOIN game_items gi ON ui.item_id = gi.id
            WHERE ui.id = ? AND ui.character_id = ? AND ui.quantity > 0
        ");
        $stmt->execute([$inventoryItemId, $characterId]);
        $item = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$item) {
            throw new Exception("物品不存在或已用完");
        }

        // 🆕 检查是否为功法秘籍
        if (
            $item['slot_type'] === 'technique_manual' || strpos($item['item_name'], '功法') !== false ||
            strpos($item['item_name'], '秘籍') !== false || strpos($item['item_name'], '心法') !== false
        ) {

            // 处理功法秘籍使用
            $result = useTechniqueManual($pdo, $userId, $characterId, $item);

            if ($result['success']) {
                // 扣除道具数量
                $stmt = $pdo->prepare("UPDATE user_inventories SET quantity = quantity - 1 WHERE id = ?");
                $stmt->execute([$inventoryItemId]);

                // 清理数量为0的物品
                $stmt = $pdo->prepare("DELETE FROM user_inventories WHERE id = ? AND quantity <= 0");
                $stmt->execute([$inventoryItemId]);

                $pdo->commit();
                echo json_encode($result);
                return;
            } else {
                throw new Exception($result['message']);
            }
        }

        // 🆕 检查是否为丹方
        if (
            strpos($item['item_name'], '丹方') !== false ||
            ($item['item_id'] >= 364 && $item['item_id'] <= 368)
        ) {

            // 处理丹方使用（学习丹方）
            $result = useRecipeManual($pdo, $userId, $characterId, $item);

            if ($result['success']) {
                // 扣除道具数量
                $stmt = $pdo->prepare("UPDATE user_inventories SET quantity = quantity - 1 WHERE id = ?");
                $stmt->execute([$inventoryItemId]);

                // 清理数量为0的物品
                $stmt = $pdo->prepare("DELETE FROM user_inventories WHERE id = ? AND quantity <= 0");
                $stmt->execute([$inventoryItemId]);

                $pdo->commit();
                echo json_encode($result);
                return;
            } else {
                throw new Exception($result['message']);
            }
        }

        // 🚫 检查是否为渡劫丹或养魂丹 - 禁止在背包中使用
        if (strpos($item['item_name'], '渡劫丹') !== false || strpos($item['item_name'], '养魂丹') !== false) {
            throw new Exception("渡劫丹和养魂丹只能在修炼界面使用，不能在背包中直接使用");
        }

        // 🔥 检查是否为属性丹 (ID: 314-358) - 优先处理
        if ($item['item_id'] >= 314 && $item['item_id'] <= 358) {
            $result = useAttributePill($pdo, $characterId, $item);
        }
        // 处理其他类型的道具使用（消耗品、灵石等）
        else {
            switch ($item['slot_type']) {
                case 'consumable':
                    $result = useConsumableItem($pdo, $characterId, $item);
                    break;

                case 'spirit':
                    $result = useSpiritItem($pdo, $characterId, $item);
                    break;

                default:
                    // 🔧 修复：对于item_type为consumable但slot_type为NULL的物品，也尝试作为消耗品处理
                    if ($item['item_type'] === 'consumable') {
                        $result = useConsumableItem($pdo, $characterId, $item);
                    } else {
                        throw new Exception("该物品无法使用");
                    }
            }
        }

        if ($result['success']) {
            // 扣除道具数量
            $stmt = $pdo->prepare("UPDATE user_inventories SET quantity = quantity - 1 WHERE id = ?");
            $stmt->execute([$inventoryItemId]);

            // 清理数量为0的物品
            $stmt = $pdo->prepare("DELETE FROM user_inventories WHERE id = ? AND quantity <= 0");
            $stmt->execute([$inventoryItemId]);

            $pdo->commit();
            echo json_encode($result);
        } else {
            throw new Exception($result['message']);
        }
    } catch (Exception $e) {
        $pdo->rollback();
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

// 🆕 使用功法秘籍
function useTechniqueManual($pdo, $userId, $characterId, $item)
{
    try {
        // 根据物品名称确定功法类型
        $techniqueName = '';
        $techniqueId = '';

        if (strpos($item['item_name'], '先天功') !== false) {
            $techniqueName = '先天功';
            $techniqueId = 'xiantian';
        } elseif (strpos($item['item_name'], '聚灵决') !== false) {
            $techniqueName = '聚灵决';
            $techniqueId = 'juling';
        } elseif (strpos($item['item_name'], '炼神术') !== false) {
            $techniqueName = '炼神术';
            $techniqueId = 'lianshen';
        } elseif (strpos($item['item_name'], '太极真经') !== false) {
            $techniqueName = '太极真经';
            $techniqueId = 'taiji';
        } elseif (strpos($item['item_name'], '九转玄功') !== false) {
            $techniqueName = '九转玄功';
            $techniqueId = 'jiuzhuan';
        } elseif (strpos($item['item_name'], '混沌诀') !== false) {
            $techniqueName = '混沌诀';
            $techniqueId = 'hundun';
        } else {
            return ['success' => false, 'message' => '未知的功法秘籍'];
        }

        // 获取角色当前的功法数据
        $stmt = $pdo->prepare("SELECT cultivation_techniques, current_technique FROM characters WHERE id = ?");
        $stmt->execute([$characterId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);

        $techniques = [];
        if ($character['cultivation_techniques']) {
            $techniques = json_decode($character['cultivation_techniques'], true);
        }

        // 检查是否已经学会该功法
        if (isset($techniques[$techniqueId])) {
            return ['success' => false, 'message' => "已经学会了{$techniqueName}，无法重复学习"];
        }

        // 学习新功法
        $techniques[$techniqueId] = [
            'name' => $techniqueName,
            'level' => 1,
            'exp' => 0,
            'type' => getTechniqueTypeByName($techniqueName),
            'source' => '功法秘籍',
            'learned_time' => date('Y-m-d H:i:s')
        ];

        // 如果是第一个功法，设置为当前功法
        if (!$character['current_technique'] || $character['current_technique'] === 'main') {
            $currentTechnique = $techniqueId;
        } else {
            $currentTechnique = $character['current_technique'];
        }

        // 更新数据库
        $stmt = $pdo->prepare("UPDATE characters SET cultivation_techniques = ?, current_technique = ? WHERE id = ?");
        $stmt->execute([json_encode($techniques), $currentTechnique, $characterId]);

        return [
            'success' => true,
            'message' => "成功学会{$techniqueName}！",
            'technique_name' => $techniqueName,
            'technique_type' => getTechniqueTypeByName($techniqueName)
        ];
    } catch (Exception $e) {
        return ['success' => false, 'message' => '学习功法失败: ' . $e->getMessage()];
    }
}

// 🆕 根据功法名称获取功法类型
function getTechniqueTypeByName($techniqueName)
{
    $typeMap = [
        '凝气决' => '初级功法',
        '先天功' => '中级功法',
        '聚灵决' => '中级功法',
        '炼神术' => '高级功法',
        '太极真经' => '仙级功法',
        '九转玄功' => '仙级功法',
        '混沌诀' => '神级功法'
    ];
    return isset($typeMap[$techniqueName]) ? $typeMap[$techniqueName] : '普通功法';
}

// 🆕 使用消耗品功能 - 增强版属性丹处理
function useConsumableItem($pdo, $characterId, $item)
{
    try {
        // 🔥 属性丹使用逻辑 (ID: 314-358)
        if ($item['item_id'] >= 314 && $item['item_id'] <= 358) {
            return useAttributePill($pdo, $characterId, $item);
        }

        // 其他消耗品处理
        switch ($item['item_name']) {
            case '小还丹':
                return useHealingPill($pdo, $characterId, $item, 'hp', 100);

            case '回魂丹':
                return useHealingPill($pdo, $characterId, $item, 'mp', 100);

            default:
                return [
                    'success' => true,
                    'message' => "使用了{$item['item_name']}"
                ];
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => "使用失败: " . $e->getMessage()
        ];
    }
}

// 🧪 属性丹使用处理函数
function useAttributePill($pdo, $characterId, $item)
{
    try {
        // 获取属性丹信息 - 🔧 修复：使用正确的物品ID而不是背包ID
        $pillInfo = getAttributePillInfo($item['item_id'], $item['item_name']);
        if (!$pillInfo) {
            throw new Exception("无效的属性丹");
        }

        // 获取角色当前属性丹使用记录
        $stmt = $pdo->prepare("SELECT attribute_pill_count FROM characters WHERE id = ?");
        $stmt->execute([$characterId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$character) {
            throw new Exception("角色不存在");
        }

        // 解析当前使用记录
        $pillCounts = [];
        if (!empty($character['attribute_pill_count'])) {
            $pillCounts = json_decode($character['attribute_pill_count'], true) ?: [];
        }

        // 🔧 修复：使用规范的key格式 type_tier
        $pillKey = $pillInfo['type'] . '_' . $pillInfo['tier'];
        $currentCount = isset($pillCounts[$pillKey]) ? $pillCounts[$pillKey] : 0;
        $maxLimit = $pillInfo['usage_limit'];

        // 检查是否已达到上限
        if ($currentCount >= $maxLimit) {
            $tierName = convertNumberToChinese($pillInfo['tier']) . '阶';
            throw new Exception("该阶{$tierName}{$pillInfo['attribute_display']}丹已达到使用上限({$maxLimit}颗)，继续服用会产生严重丹毒！");
        }

        // 获取角色当前属性值
        $attributeField = $pillInfo['attribute_field'];
        $stmt = $pdo->prepare("SELECT {$attributeField} FROM characters WHERE id = ?");
        $stmt->execute([$characterId]);
        $currentStats = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$currentStats) {
            throw new Exception("获取角色属性失败");
        }

        $currentValue = $currentStats[$attributeField];
        $newValue = $currentValue + $pillInfo['attribute_bonus'];

        // 更新属性值
        $stmt = $pdo->prepare("UPDATE characters SET {$attributeField} = ? WHERE id = ?");
        $stmt->execute([$newValue, $characterId]);

        // 更新使用记录
        $pillCounts[$pillKey] = $currentCount + 1;
        $pillCountsJson = json_encode($pillCounts);

        $stmt = $pdo->prepare("UPDATE characters SET attribute_pill_count = ? WHERE id = ?");
        $stmt->execute([$pillCountsJson, $characterId]);

        return [
            'success' => true,
            'message' => "成功使用{$pillInfo['display_name']}，{$pillInfo['attribute_display']}+{$pillInfo['attribute_bonus']}（已使用{$pillCounts[$pillKey]}/{$maxLimit}）",
            'effect' => [
                'attribute' => $pillInfo['attribute_display'],
                'bonus' => $pillInfo['attribute_bonus'],
                'used_count' => $pillCounts[$pillKey],
                'max_limit' => $maxLimit,
                'new_value' => $newValue
            ],
            // 🔥 新增：属性变化数据，用于属性变化弹窗
            'attribute_changes' => [
                'old_value' => $currentValue,
                'new_value' => $newValue,
                'attribute_field' => $attributeField,
                'attribute_display' => $pillInfo['attribute_display'],
                'bonus' => $pillInfo['attribute_bonus']
            ]
        ];
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => $e->getMessage()
        ];
    }
}

// 🔍 解析属性丹信息
function getAttributePillInfo($itemId, $itemName)
{
    global $pdo;

    try {
        // 从数据库获取物品的special_effects字段
        $stmt = $pdo->prepare("SELECT special_effects FROM game_items WHERE id = ?");
        $stmt->execute([$itemId]);
        $item = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$item || !$item['special_effects']) {
            return null;
        }

        // 解析special_effects JSON
        $specialEffects = json_decode($item['special_effects'], true);
        if (!$specialEffects || !isset($specialEffects['pill_category']) || $specialEffects['pill_category'] !== 'attribute_pill') {
            return null; // 不是属性丹
        }

        // 验证必要字段
        $requiredFields = ['pill_type', 'pill_tier', 'attribute_type', 'attribute_display', 'attribute_bonus'];
        foreach ($requiredFields as $field) {
            if (!isset($specialEffects[$field])) {
                return null;
            }
        }

        return [
            'type' => $specialEffects['pill_type'],                    // 丹药类型 (tenglong, luocha等)
            'tier' => $specialEffects['pill_tier'],                    // 丹药阶数 (1-9)
            'attribute_field' => $specialEffects['attribute_type'],    // 对应属性字段
            'attribute_display' => $specialEffects['attribute_display'], // 属性显示名称
            'attribute_bonus' => $specialEffects['attribute_bonus'],   // 属性加成值
            'usage_limit' => isset($specialEffects['usage_limit']) ? $specialEffects['usage_limit'] : 20,    // 使用上限
            'poison_weight' => isset($specialEffects['poison_weight']) ? $specialEffects['poison_weight'] : $specialEffects['pill_tier'], // 丹毒权重
            'display_name' => convertNumberToChinese($specialEffects['pill_tier']) . '阶' . $specialEffects['attribute_display'] . '丹'
        ];
    } catch (Exception $e) {
        error_log("获取属性丹信息失败: " . $e->getMessage());
        return null;
    }
}

// 🧮 计算丹毒等级
function calculateDanPoisonLevel($pillCounts)
{
    $totalPoisonScore = 0;
    foreach ($pillCounts as $key => $count) {
        // 解析key获取阶数：type_tier
        $parts = explode('_', $key);
        if (count($parts) >= 2) {
            $tier = intval($parts[1]);
            // 高阶丹药产生更多丹毒：阶数 * 使用次数
            $totalPoisonScore += $tier * $count;
        }
    }

    // 丹毒等级判定
    if ($totalPoisonScore <= 50) return 'low';      // 轻微
    elseif ($totalPoisonScore <= 150) return 'medium';  // 中等
    elseif ($totalPoisonScore <= 300) return 'high';    // 严重
    else return 'critical';  // 极危险
}

// ⚠️ 获取丹毒警告信息
function getDanPoisonWarning($level)
{
    switch ($level) {
        case 'low':
            return '丹毒轻微，身体状况良好';
        case 'medium':
            return '丹毒积累中等，建议控制服药频率';
        case 'high':
            return '丹毒较为严重，继续服药有风险！';
        case 'critical':
            return '丹毒已达极危险水平，立即停止服药！';
        default:
            return '丹毒状况未知';
    }
}

// 🔢 中文数字转阿拉伯数字
function convertChineseToNumber($chinese)
{
    $map = [
        '一' => 1,
        '二' => 2,
        '三' => 3,
        '四' => 4,
        '五' => 5,
        '六' => 6,
        '七' => 7,
        '八' => 8,
        '九' => 9
    ];
    return isset($map[$chinese]) ? $map[$chinese] : 1;
}

// 🔢 阿拉伯数字转中文数字
function convertNumberToChinese($number)
{
    $map = [
        1 => '一',
        2 => '二',
        3 => '三',
        4 => '四',
        5 => '五',
        6 => '六',
        7 => '七',
        8 => '八',
        9 => '九'
    ];
    return isset($map[$number]) ? $map[$number] : '一';
}

// 🩺 治疗类丹药使用
function useHealingPill($pdo, $characterId, $item, $type, $amount)
{
    try {
        $field = ($type === 'hp') ? 'current_hp' : 'current_mp';
        $maxField = ($type === 'hp') ? 'max_hp' : 'max_mp';

        // 获取当前和最大值
        $stmt = $pdo->prepare("SELECT {$field}, {$maxField} FROM characters WHERE id = ?");
        $stmt->execute([$characterId]);
        $stats = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$stats) {
            throw new Exception("角色不存在");
        }

        $current = $stats[$field];
        $max = $stats[$maxField];

        if ($current >= $max) {
            throw new Exception(($type === 'hp' ? '生命值' : '法力值') . "已满，无需使用");
        }

        $newValue = min($max, $current + $amount);
        $actualHeal = $newValue - $current;

        // 更新数值
        $stmt = $pdo->prepare("UPDATE characters SET {$field} = ? WHERE id = ?");
        $stmt->execute([$newValue, $characterId]);

        return [
            'success' => true,
            'message' => "使用{$item['item_name']}成功，回复" . ($type === 'hp' ? '生命值' : '法力值') . "{$actualHeal}点",
            'effect' => [
                'type' => $type,
                'heal_amount' => $actualHeal,
                'current' => $newValue,
                'max' => $max
            ]
        ];
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => $e->getMessage()
        ];
    }
}

// 🆕 使用灵石类物品
function useSpiritItem($pdo, $characterId, $item)
{
    // 实现灵石使用逻辑，比如转换为修为或资源
    return [
        'success' => true,
        'message' => "使用了{$item['item_name']}"
    ];
}

// 🆕 使用丹方 - 修复版：使用正确的user_learned_recipes表
function useRecipeManual($pdo, $userId, $characterId, $item)
{
    try {
        // 🔧 首先检查user_learned_recipes表是否存在
        $stmt = $pdo->prepare("SHOW TABLES LIKE 'user_learned_recipes'");
        $stmt->execute();
        $tableExists = $stmt->fetch();

        if (!$tableExists) {
            return [
                'success' => false,
                'message' => '丹方学习系统暂未开放，请等待后续版本更新。'
            ];
        }

        // 🔧 修复：属性丹方映射 - 物品ID到配方ID的正确映射
        $recipeMapping = [
            364 => 51,  // 一阶腾龙丹方 -> crafting_recipes ID: 51 (一阶腾龙丹)
            365 => 60,  // 一阶罗刹丹方 -> crafting_recipes ID: 60 (一阶罗刹丹)
            366 => 69,  // 一阶血气丹方 -> crafting_recipes ID: 69 (一阶血气丹)
            367 => 78,  // 一阶虚灵丹方 -> crafting_recipes ID: 78 (一阶虚灵丹)
            368 => 87,  // 一阶游龙丹方 -> crafting_recipes ID: 87 (一阶游龙丹)
        ];

        // 🔧 处理属性丹方 (ID: 364-368)
        if (isset($recipeMapping[$item['item_id']])) {
            $recipeId = $recipeMapping[$item['item_id']];

            // 🔧 首先检查crafting_recipes表是否存在对应配方
            $stmt = $pdo->prepare("SELECT id, recipe_name, description FROM crafting_recipes WHERE id = ?");
            $stmt->execute([$recipeId]);
            $recipeInfo = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$recipeInfo) {
                return [
                    'success' => false,
                    'message' => "配方数据不存在，配方ID: {$recipeId}。请联系管理员检查数据库配置。"
                ];
            }

            // 🔧 检查是否已经学会这个配方
            $stmt = $pdo->prepare("SELECT id FROM user_learned_recipes WHERE character_id = ? AND recipe_id = ?");
            $stmt->execute([$characterId, $recipeId]);
            $learned = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($learned) {
                return [
                    'success' => false,
                    'message' => "您已经学会了{$item['item_name']}！"
                ];
            }

            // 🔧 学习配方
            $stmt = $pdo->prepare("INSERT INTO user_learned_recipes (character_id, recipe_id, learned_source, learned_at) VALUES (?, ?, ?, NOW())");
            $stmt->execute([$characterId, $recipeId, '丹方学习']);

            return [
                'success' => true,
                'message' => "成功学会了{$item['item_name']}！现在可以炼制{$recipeInfo['recipe_name']}。",
                'recipe_info' => [
                    'recipe_id' => $recipeId,
                    'name' => $recipeInfo['recipe_name'],
                    'description' => $recipeInfo['description']
                ]
            ];
        }

        // 🆕 处理渡劫丹方和养魂丹方 (ID: 213-262)
        $isTribalPill = ($item['item_id'] >= 213 && $item['item_id'] <= 262);

        if ($isTribalPill) {
            // 🔧 检查crafting_recipes表是否存在
            $stmt = $pdo->prepare("SHOW TABLES LIKE 'crafting_recipes'");
            $stmt->execute();
            $craftingTableExists = $stmt->fetch();

            if (!$craftingTableExists) {
                return [
                    'success' => false,
                    'message' => '高级丹方学习系统暂未开放，请等待后续版本更新。'
                ];
            }

            // 查找对应的炼丹配方
            $stmt = $pdo->prepare("
                SELECT cr.id as recipe_id, cr.recipe_name, cr.description, gi.item_name as result_item_name
                 FROM crafting_recipes cr
                LEFT JOIN game_items gi ON cr.result_item_id = gi.id
                WHERE cr.recipe_name LIKE ? OR gi.item_name LIKE ?
            ");

            // 🔧 修复：从丹方名称中提取关键词来匹配配方
            $pillName = $item['item_name'];
            $searchKeyword = '';
            $pillType = '';

            // 🔧 先确定丹药类型，避免"渡劫期渡劫丹方"的混淆
            if (strpos($pillName, '渡劫丹方') !== false) {
                $pillType = '渡劫丹';
            } elseif (strpos($pillName, '养魂丹方') !== false) {
                $pillType = '养魂丹';
            } else {
                return [
                    'success' => false,
                    'message' => '无法识别该丹方类型（渡劫丹方或养魂丹方）。'
                ];
            }

            // 🔧 修复：提取境界名称，特殊处理"渡劫期"避免与"渡劫丹"混淆
            $realmKeywords = [
                '心动期',
                '元化期',
                '元婴期',
                '离合期',
                '空冥期',
                '寂灭期',
                '大乘期',
                '渡劫期',
                '凡仙期',
                '地仙期',
                '天仙期',
                '真仙期',
                '太乙真仙期',
                '太乙金仙期',
                '太乙玄仙期',
                '大罗真仙期',
                '大罗金仙期',
                '大罗玄仙期',
                '准圣期',
                '教主期',
                '混元期',
                '混元金仙期',
                '混元至仙期',
                '天道期',
                '鸿蒙至元期'
            ];

            foreach ($realmKeywords as $realm) {
                if (strpos($pillName, $realm) !== false) {
                    // 🔧 特殊处理：渡劫期的渡劫丹应该叫"渡劫渡劫丹"，但实际配方可能是"渡劫丹"
                    if ($realm === '渡劫期' && $pillType === '渡劫丹') {
                        $searchKeyword = '渡劫';
                    } elseif ($realm === '渡劫期' && $pillType === '养魂丹') {
                        $searchKeyword = '渡劫';
                    } else {
                        // 去掉"期"字
                        $searchKeyword = str_replace('期', '', $realm);
                    }
                    break;
                }
            }

            if (empty($searchKeyword)) {
                return [
                    'success' => false,
                    'message' => '无法识别该丹方对应的境界，请联系管理员。'
                ];
            }

            // 🔧 修复：构建搜索关键词，特殊处理渡劫期的情况
            if ($searchKeyword === '渡劫' && $pillType === '渡劫丹') {
                // 对于渡劫期渡劫丹，尝试多种可能的配方名称
                $searchPatterns = [
                    "%渡劫{$pillType}%",           // 渡劫渡劫丹
                    "%{$searchKeyword}{$pillType}%", // 渡劫渡劫丹
                    "%渡劫期{$pillType}%"          // 渡劫期渡劫丹（如果有的话）
                ];
            } else {
                $searchPatterns = ["%{$searchKeyword}{$pillType}%"];
            }
            // 🔧 修复：尝试多种搜索模式来找到匹配的配方
            $craftingRecipe = null;
            $usedPattern = '';

            foreach ($searchPatterns as $pattern) {
                $stmt->execute([$pattern, $pattern]);
                $craftingRecipe = $stmt->fetch(PDO::FETCH_ASSOC);
                if ($craftingRecipe) {
                    $usedPattern = $pattern;
                    break;
                }
                // 重置statement以便下次查询
                $stmt = $pdo->prepare("
                    SELECT cr.id as recipe_id, cr.recipe_name, cr.description, gi.item_name as result_item_name
                     FROM crafting_recipes cr
                    LEFT JOIN game_items gi ON cr.result_item_id = gi.id
                    WHERE cr.recipe_name LIKE ? OR gi.item_name LIKE ?
                ");
            }

            if (!$craftingRecipe) {
                return [
                    'success' => false,
                    'message' => "暂未找到对应的炼丹配方，该丹方可能需要更高的炼丹等级或特殊条件才能学习。搜索关键词：{$searchKeyword}{$pillType}，尝试的模式：" . implode(', ', $searchPatterns)
                ];
            }

            // 检查是否已经学会这个炼丹配方
            $recipeId = $craftingRecipe['recipe_id'];
            $stmt = $pdo->prepare("SELECT id FROM user_learned_recipes WHERE character_id = ? AND recipe_id = ?");
            $stmt->execute([$characterId, $recipeId]);
            $learned = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($learned) {
                return [
                    'success' => false,
                    'message' => "您已经学会了{$craftingRecipe['recipe_name']}！"
                ];
            }

            // 学习炼丹配方
            $stmt = $pdo->prepare("INSERT INTO user_learned_recipes (character_id, recipe_id, learned_source, learned_at) VALUES (?, ?, ?, NOW())");
            $stmt->execute([$characterId, $recipeId, '丹方学习']);

            return [
                'success' => true,
                'message' => "成功学会了{$craftingRecipe['recipe_name']}！现在可以炼制{$craftingRecipe['result_item_name']}。",
                'recipe_info' => [
                    'name' => $craftingRecipe['result_item_name'],
                    'recipe_name' => $craftingRecipe['recipe_name'],
                    'description' => $craftingRecipe['description'],
                    'type' => $pillType,
                    'realm' => $searchKeyword
                ]
            ];
        } else {
            // 🔧 对于其他特殊丹方的处理
            if (strpos($item['item_name'], '心动牌游戏') !== false) {
                return [
                    'success' => false,
                    'message' => '这是一个纪念品性质的特殊丹方，无法用于正常的炼丹系统。'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => '暂未找到对应的炼丹配方，该丹方可能需要更高的炼丹等级或特殊条件才能学习。'
                ];
            }
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => '学习丹方时发生错误: ' . $e->getMessage()
        ];
    }
}

// 🧪 获取丹药详情信息
function getDanMedicineInfo($pdo, $characterId)
{
    try {
        // 获取角色的丹毒记录
        $stmt = $pdo->prepare("SELECT attribute_pill_count FROM characters WHERE id = ?");
        $stmt->execute([$characterId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$character) {
            throw new Exception("角色不存在");
        }

        // 解析丹毒记录
        $pillCounts = [];
        if (!empty($character['attribute_pill_count'])) {
            $pillCounts = json_decode($character['attribute_pill_count'], true);
            if (!$pillCounts) {
                $pillCounts = [];
            }
        }

        // 整理各类属性丹的使用情况
        $danMedicineData = [
            'tenglong' => [], // 腾龙丹 (筋骨)
            'luocha' => [],   // 罗刹丹 (悟性)
            'xueqi' => [],    // 血气丹 (体魄)
            'xuling' => [],   // 虚灵丹 (神魂)
            'youlong' => []   // 游龙丹 (身法)
        ];

        // 🔧 基于special_effects获取属性丹信息
        $stmt = $pdo->prepare("
            SELECT id, item_name, special_effects 
            FROM game_items 
            WHERE id BETWEEN 314 AND 358 
            AND special_effects IS NOT NULL 
            ORDER BY id
        ");
        $stmt->execute();
        $attributePills = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // 分析各阶丹药使用情况
        foreach ($attributePills as $pill) {
            $specialEffects = json_decode($pill['special_effects'], true);
            if (!$specialEffects || !isset($specialEffects['pill_category']) || $specialEffects['pill_category'] !== 'attribute_pill') {
                continue;
            }

            $type = $specialEffects['pill_type'];
            $tier = $specialEffects['pill_tier'];
            $pillKey = $type . '_' . $tier;
            $currentCount = isset($pillCounts[$pillKey]) ? $pillCounts[$pillKey] : 0;
            $maxLimit = isset($specialEffects['usage_limit']) ? $specialEffects['usage_limit'] : 20;

            if (isset($danMedicineData[$type])) {
                $danMedicineData[$type][] = [
                    'tier' => $tier,
                    'tier_name' => convertNumberToChinese($tier) . '阶',
                    'current_count' => $currentCount,
                    'max_limit' => $maxLimit,
                    'usage_percent' => min(100, round(($currentCount / $maxLimit) * 100, 1)),
                    'attribute_display' => $specialEffects['attribute_display'],
                    'attribute_bonus' => $specialEffects['attribute_bonus']
                ];
            }
        }

        // 按阶数排序
        foreach ($danMedicineData as &$typeData) {
            usort($typeData, function ($a, $b) {
                return $a['tier'] - $b['tier'];
            });
        }

        // 计算丹毒等级
        $danPoisonLevel = calculateDanPoisonLevel($pillCounts);
        $danPoisonInfo = [
            'level' => $danPoisonLevel,
            'level_name' => getDanPoisonLevelName($danPoisonLevel),
            'description' => getDanPoisonWarning($danPoisonLevel)
        ];

        return [
            'pill_usage' => $danMedicineData,
            'dan_poison' => $danPoisonInfo
        ];
    } catch (Exception $e) {
        throw new Exception("获取丹药信息失败: " . $e->getMessage());
    }
}

// 🏷 获取丹毒等级名称
function getDanPoisonLevelName($level)
{
    switch ($level) {
        case 0:
            return '无';
        case 1:
            return '轻微';
        case 2:
            return '中等';
        case 3:
            return '严重';
        case 4:
            return '极重';
        default:
            return '未知';
    }
}

/** * 🆕 整理背包功能 - 合并可堆叠的相同物品 */
function organizeInventoryUnified($userId, $pdo)
{
    try {
        $pdo->beginTransaction();
        // 获取用户角色ID
        $stmt = $pdo->prepare("SELECT id FROM characters WHERE user_id = ? LIMIT 1");
        $stmt->execute([$userId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);
        if (!$character) throw new Exception("找不到有效的角色信息");
        $characterId = $character['id'];

        // 1. 合并可堆叠物品（如原有逻辑）
        $stmt = $pdo->prepare("
            SELECT ui.item_id, gi.item_name, gi.item_type, gi.max_stack, gi.is_stackable,
                GROUP_CONCAT(ui.id ORDER BY ui.quantity DESC, ui.obtained_time ASC) as inventory_ids,
                GROUP_CONCAT(ui.quantity ORDER BY ui.quantity DESC, ui.obtained_time ASC) as quantities,
                   SUM(ui.quantity) as total_quantity, COUNT(*) as slot_count
            FROM user_inventories ui
            JOIN game_items gi ON ui.item_id = gi.id
            WHERE ui.character_id = ?
                 AND gi.is_stackable = 1
                 AND gi.max_stack > 1
                 AND ui.bind_status = 'unbound'
            GROUP BY ui.item_id, gi.item_name, gi.item_type, gi.max_stack, gi.is_stackable
            HAVING COUNT(*) > 1 OR SUM(ui.quantity) != MAX(ui.quantity)
            ORDER BY gi.item_name
        ");
        $stmt->execute([$characterId]);
        $stackableItems = $stmt->fetchAll(PDO::FETCH_ASSOC);
        foreach ($stackableItems as $item) {
            $inventoryIds = explode(',', $item['inventory_ids']);
            $quantities = array_map('intval', explode(',', $item['quantities']));
            $totalQuantity = intval($item['total_quantity']);
            $maxStack = intval($item['max_stack']);
            $slotCount = intval($item['slot_count']);
            $minSlotsNeeded = ceil($totalQuantity / $maxStack);
            if ($slotCount > $minSlotsNeeded) {
                // 删除所有相关记录
                $placeholders = str_repeat('?,', count($inventoryIds) - 1) . '?';
                $stmt = $pdo->prepare("DELETE FROM user_inventories WHERE id IN ($placeholders)");
                $stmt->execute($inventoryIds);
                // 重新分配物品到最少的位置
                $remainingQuantity = $totalQuantity;
                while ($remainingQuantity > 0) {
                    $addToSlot = min($remainingQuantity, $maxStack);
                    require_once __DIR__ . '/../includes/inventory_utils.php';
                    $sortWeight = calculateSortWeight($pdo, $characterId, $item['item_type']);
                    $stmt = $pdo->prepare("
                        INSERT INTO user_inventories (
                            character_id, item_id, item_type, quantity,
                             obtained_time, obtained_source, bind_status, sort_weight
                        ) VALUES (?, ?, ?, ?, NOW(), 'organize', 'unbound', ?)
                    ");
                    $stmt->execute([$characterId, $item['item_id'], $item['item_type'], $addToSlot, $sortWeight]);
                    $remainingQuantity -= $addToSlot;
                }
            }
        }

        // 2. 重新为所有物品分配排序权重
        $stmt = $pdo->prepare("
            SELECT ui.id
                FROM user_inventories ui
                JOIN game_items gi ON ui.item_id = gi.id
                WHERE ui.character_id = ?
                ORDER BY 
                    CASE ui.item_type
                        WHEN 'weapon' THEN 1
                        WHEN 'equipment' THEN 2
                        WHEN 'consumable' THEN 3
                        WHEN 'material' THEN 4
                        WHEN 'currency' THEN 5
                        ELSE 6
                    END,
                    gi.item_name,
                ui.obtained_time DESC,
                ui.id DESC
            ");
        $stmt->execute([$characterId]);
        $allItems = $stmt->fetchAll(PDO::FETCH_ASSOC);
        // 🔧 修复：权重从大到小分配，最新的物品权重最大
        $weight = count($allItems);
        foreach ($allItems as $item) {
            $stmtUpdate = $pdo->prepare("UPDATE user_inventories SET sort_weight = ? WHERE id = ?");
            $stmtUpdate->execute([$weight, $item['id']]);
            $weight--; // 权重递减
        }

        $pdo->commit();
        echo json_encode([
            'success' => true,
            'message' => '背包整理完成，物品顺序已更新',
            'sorted_items' => count($allItems)
        ]);
    } catch (Exception $e) {
        if ($pdo->inTransaction()) $pdo->rollback();
        error_log("背包整理失败: " . $e->getMessage());
        echo json_encode([
            'success' => false,
            'message' => '背包整理失败: ' . $e->getMessage()
        ]);
    }
}

/** * 🆕 扩展背包空间功能 */
function expandInventoryUnified($userId, $pdo)
{
    try {
        $pdo->beginTransaction();

        // 获取用户的角色ID和当前背包空间
        $stmt = $pdo->prepare("SELECT id, inventory_slots FROM characters WHERE user_id = ? LIMIT 1");
        $stmt->execute([$userId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$character) {
            throw new Exception("找不到有效的角色信息");
        }

        $characterId = $character['id'];
        $currentSlots = intval($character['inventory_slots'] ?: 30);

        // 🔧 新规则：每次扩展5个格子，最高扩展20次
        $baseSlots = 30; // 基础30格
        $maxSlots = 130; // 最高130格 (30 + 20*5)
        $expandSlots = 5; // 每次扩展5格
        $maxExpandTimes = 20; // 最多扩展20次

        // 检查是否已达到扩展上限
        if ($currentSlots >= $maxSlots) {
            throw new Exception("背包容量已达到上限({$maxSlots}格)，无法继续扩展");
        }

        // 计算已扩展次数
        $expandedTimes = ($currentSlots - $baseSlots) / $expandSlots;
        if ($expandedTimes >= $maxExpandTimes) {
            throw new Exception("背包扩展次数已达到上限({$maxExpandTimes}次)");
        }

        // 检查芥子石数量（每次扩展需要1个芥子石）
        $requiredJiezi = 1;

        // 查找芥子石物品ID
        $stmt = $pdo->prepare("SELECT id FROM game_items WHERE item_name LIKE '%芥子石%' LIMIT 1");
        $stmt->execute();
        $jieziItem = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$jieziItem) {
            throw new Exception("芥子石物品不存在，请联系管理员");
        }

        $jieziItemId = $jieziItem['id'];

        // 统计用户的芥子石总数
        $stmt = $pdo->prepare("
            SELECT SUM(quantity) as total_jiezi
             FROM user_inventories
            WHERE character_id = ? AND item_id = ?
        ");
        $stmt->execute([$characterId, $jieziItemId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $totalJiezi = intval($result['total_jiezi'] ?: 0);

        if ($totalJiezi < $requiredJiezi) {
            throw new Exception("芥子石数量不足。需要{$requiredJiezi}个，当前只有{$totalJiezi}个");
        }

        // 消耗芥子石
        $remainingToConsume = $requiredJiezi;
        $stmt = $pdo->prepare("
            SELECT id, quantity
             FROM user_inventories
            WHERE character_id = ? AND item_id = ?
            ORDER BY quantity ASC
        ");
        $stmt->execute([$characterId, $jieziItemId]);
        $jieziStacks = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($jieziStacks as $stack) {
            if ($remainingToConsume <= 0) break;

            $stackQuantity = intval($stack['quantity']);
            $consumeFromStack = min($remainingToConsume, $stackQuantity);

            if ($consumeFromStack >= $stackQuantity) {
                // 删除整个堆叠
                $stmt = $pdo->prepare("DELETE FROM user_inventories WHERE id = ?");
                $stmt->execute([$stack['id']]);
            } else {
                // 减少堆叠数量
                $newQuantity = $stackQuantity - $consumeFromStack;
                $stmt = $pdo->prepare("UPDATE user_inventories SET quantity = ? WHERE id = ?");
                $stmt->execute([$newQuantity, $stack['id']]);
            }

            $remainingToConsume -= $consumeFromStack;
        }

        // 扩展背包空间：增加5个格子
        $newSlots = $currentSlots + $expandSlots;
        $stmt = $pdo->prepare("UPDATE characters SET inventory_slots = ? WHERE id = ?");
        $stmt->execute([$newSlots, $characterId]);

        $remainingExpandTimes = $maxExpandTimes - ($expandedTimes + 1);
        $pdo->commit();

        echo json_encode([
            'success' => true,
            'message' => "背包扩展成功！消耗了{$requiredJiezi}个芥子石，背包容量从{$currentSlots}格增加到{$newSlots}格。剩余可扩展次数：{$remainingExpandTimes}次",
            'data' => [
                'old_slots' => $currentSlots,
                'new_slots' => $newSlots,
                'expand_slots' => $expandSlots,
                'consumed_jiezi' => $requiredJiezi,
                'remaining_jiezi' => $totalJiezi - $requiredJiezi,
                'remaining_expand_times' => $remainingExpandTimes,
                'max_slots' => $maxSlots
            ]
        ]);
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollback();
        }
        error_log("背包扩展失败: " . $e->getMessage());
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

/** * 🎲 带品质的装备获得系统 * 支持随机品质生成和属性计算 */
function addEquipmentWithQualityUnified($userId, $pdo)
{
    try {
        // 获取参数
        $gameItemId = intval(isset($_POST['item_id']) ? $_POST['item_id'] : 0);
        $quantity = intval(isset($_POST['quantity']) ? $_POST['quantity'] : 1);
        $contextType = isset($_POST['context_type']) ? $_POST['context_type'] : 'normal'; // normal, boss, special, craft
        $forceRarity = isset($_POST['force_rarity']) ? $_POST['force_rarity'] : null; // 强制指定品质（可选）

        if ($gameItemId <= 0) {
            throw new Exception('无效的物品ID');
        }

        if ($quantity <= 0 || $quantity > 100) {
            throw new Exception('数量必须在1-100之间');
        }

        // 获取角色信息
        $stmt = $pdo->prepare("SELECT id, inventory_slots FROM characters WHERE user_id = ? LIMIT 1");
        $stmt->execute([$userId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$character) {
            throw new Exception("找不到角色信息");
        }

        $characterId = $character['id'];
        $maxSlots = intval($character['inventory_slots'] ?: 30);

        // 检查背包空间
        $stmt = $pdo->prepare("SELECT COUNT(*) as used_slots FROM user_inventories WHERE character_id = ?");
        $stmt->execute([$characterId]);
        $usedSlots = intval($stmt->fetch(PDO::FETCH_ASSOC)['used_slots']);

        if ($usedSlots + $quantity > $maxSlots) {
            throw new Exception("背包空间不足，需要 {$quantity} 个空位，当前只有 " . ($maxSlots - $usedSlots) . " 个空位");
        }

        // 获取物品基础信息
        $stmt = $pdo->prepare("
            SELECT gi.*
            FROM game_items gi
            WHERE gi.id = ?
        ");
        $stmt->execute([$gameItemId]);
        $gameItem = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$gameItem) {
            throw new Exception("物品不存在");
        }

        $addedItems = [];

        // 逐个生成装备（每个可能有不同品质）
        for ($i = 0; $i < $quantity; $i++) {
            // 使用品质系统生成装备
            $equipmentData = EquipmentQualitySystem::getEquipmentForInventory(
                $gameItemId,
                $forceRarity,
                $contextType
            );

            // 准备自定义属性（包含品质信息）
            $customAttributes = [
                'rarity' => $equipmentData['rarity'],
                'rarity_en' => $equipmentData['rarity_en'],
                'rarity_color' => $equipmentData['rarity_color'],
                'multiplier' => $equipmentData['multiplier'],
                'generation_time' => time(),
                'context_type' => $contextType
            ];

            // 如果有计算后的属性，保存到自定义属性中
            if (!empty($equipmentData['attributes'])) {
                $customAttributes['calculated_attributes'] = $equipmentData['attributes'];
            }

            // 确定物品类型
            $itemType = 'equipment';
            if ($gameItem['item_type'] === 'weapon') {
                $itemType = 'weapon';
            } elseif (in_array($gameItem['slot_type'], ['consumable', 'material', 'spirit', 'special'])) {
                $itemType = $gameItem['slot_type'];
            }

            // 插入到背包
            require_once __DIR__ . '/../includes/inventory_utils.php';
            $sortWeight = calculateSortWeight($pdo, $characterId, $itemType);

            $stmt = $pdo->prepare("
                INSERT INTO user_inventories
                (character_id, item_id, item_type, quantity, current_durability, max_durability,
                 enhancement_level, socket_gems, custom_attributes, bind_status, obtained_time, obtained_source, sort_weight)
                VALUES (?, ?, ?, 1, ?, ?, 0, '[]', ?, 'unbound', NOW(), ?, ?)
            ");

            $currentDurability = $gameItem['max_durability'] ?: 100;
            $maxDurability = $gameItem['max_durability'] ?: 100;
            $obtainedSource = "quality_system_{$contextType}";

            $stmt->execute([
                $characterId,
                $gameItemId,
                $itemType,
                $currentDurability,
                $maxDurability,
                json_encode($customAttributes, JSON_UNESCAPED_UNICODE),
                $obtainedSource,
                $sortWeight
            ]);

            $inventoryItemId = $pdo->lastInsertId();

            // 记录获得的物品信息
            $addedItems[] = [
                'inventory_id' => $inventoryItemId,
                'item_id' => $gameItemId,
                'name' => $gameItem['item_name'],
                'rarity' => $equipmentData['rarity'],
                'rarity_en' => $equipmentData['rarity_en'],
                'rarity_color' => $equipmentData['rarity_color'],
                'multiplier' => $equipmentData['multiplier'],
                'attributes' => $equipmentData['attributes'],
                'context_type' => $contextType
            ];
        }

        echo json_encode([
            'success' => true,
            'message' => "成功获得 {$quantity} 件装备",
            'data' => [
                'added_items' => $addedItems,
                'context_type' => $contextType,
                'total_count' => $quantity
            ]
        ]);
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

/** * 🔧 更新现有装备获得函数，集成品质系统 * 用于兼容现有的装备获得逻辑 */
function addEquipmentToInventoryWithQuality($characterId, $gameItemId, $quantity = 1, $contextType = 'normal', $pdo = null)
{
    if (!$pdo) {
        $pdo = getDatabase();
    }

    try {
        // 使用品质系统生成装备
        $equipmentData = EquipmentQualitySystem::getEquipmentForInventory($gameItemId, null, $contextType);

        // 准备自定义属性
        $customAttributes = [
            'rarity' => $equipmentData['rarity'],
            'rarity_en' => $equipmentData['rarity_en'],
            'rarity_color' => $equipmentData['rarity_color'],
            'multiplier' => $equipmentData['multiplier'],
            'generation_time' => time(),
            'context_type' => $contextType
        ];

        if (!empty($equipmentData['attributes'])) {
            $customAttributes['calculated_attributes'] = $equipmentData['attributes'];
        }

        // 获取基础物品信息
        $stmt = $pdo->prepare("SELECT * FROM game_items WHERE id = ?");
        $stmt->execute([$gameItemId]);
        $gameItem = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$gameItem) {
            throw new Exception("物品不存在: ID {$gameItemId}");
        }

        // 确定物品类型
        $itemType = 'equipment';
        if ($gameItem['item_type'] === 'weapon') {
            $itemType = 'weapon';
        } elseif (in_array($gameItem['slot_type'], ['consumable', 'material', 'spirit', 'special'])) {
            $itemType = $gameItem['slot_type'];
        }

        // 插入到背包
        $sortWeight = calculateSortWeight($pdo, $characterId, $itemType);

        $stmt = $pdo->prepare("
            INSERT INTO user_inventories
            (character_id, item_id, item_type, quantity, current_durability, max_durability,
             enhancement_level, socket_gems, custom_attributes, bind_status, obtained_time, obtained_source, sort_weight)
            VALUES (?, ?, ?, ?, ?, ?, 0, '[]', ?, 'unbound', NOW(), ?, ?)
        ");

        $currentDurability = $gameItem['max_durability'] ?: 100;
        $maxDurability = $gameItem['max_durability'] ?: 100;
        $obtainedSource = "quality_system_{$contextType}";

        $stmt->execute([
            $characterId,
            $gameItemId,
            $itemType,
            $quantity,
            $currentDurability,
            $maxDurability,
            json_encode($customAttributes, JSON_UNESCAPED_UNICODE),
            $obtainedSource,
            $sortWeight
        ]);

        return [
            'success' => true,
            'inventory_id' => $pdo->lastInsertId(),
            'rarity' => $equipmentData['rarity'],
            'multiplier' => $equipmentData['multiplier']
        ];
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => $e->getMessage()
        ];
    }
}

/**
 * 🔧 新增：获取单个物品详情（用于战斗界面等场景）
 */
function getItemDetailUnified($userId, $pdo)
{
    try {
        $itemId = isset($_GET['item_id']) ? intval($_GET['item_id']) : 0;

        if ($itemId <= 0) {
            throw new Exception("无效的物品ID");
        }

        error_log("getItemDetailUnified - 请求的物品ID: " . $itemId);

        // 首先获取用户的角色ID
        $stmt = $pdo->prepare("SELECT id FROM characters WHERE user_id = ? LIMIT 1");
        $stmt->execute([$userId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$character) {
            throw new Exception("找不到有效的角色信息");
        }

        $characterId = $character['id'];
        error_log("角色ID: " . $characterId);

        // 🎯 新逻辑：优先在背包中查找对应物品
        $inventoryItem = null;
        $gameItemId = null;

        // 步骤1：尝试作为user_inventories的ID查找
        $stmt = $pdo->prepare("
            SELECT ui.*, gi.id as game_item_id, gi.item_name
            FROM user_inventories ui
            LEFT JOIN game_items gi ON ui.item_id = gi.id
            WHERE ui.id = ? AND ui.character_id = ?
        ");
        $stmt->execute([$itemId, $characterId]);
        $inventoryItem = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($inventoryItem && $inventoryItem['game_item_id']) {
            // 找到了背包物品，使用对应的game_items ID
            $gameItemId = $inventoryItem['game_item_id'];
            error_log("✅ 在背包中找到物品，user_inventories ID: {$itemId}, game_items ID: {$gameItemId}");
        } else {
            // 步骤2：没找到背包物品，尝试作为game_items ID查找
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM game_items WHERE id = ?");
            $stmt->execute([$itemId]);
            $exists = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($exists['count'] > 0) {
                $gameItemId = $itemId;
                error_log("✅ 直接使用game_items ID: {$itemId}");

                // 步骤3：即使传入的是game_items ID，也要检查背包中是否有这个物品
                $stmt = $pdo->prepare("
                    SELECT ui.*, gi.item_name
                    FROM user_inventories ui
                    LEFT JOIN game_items gi ON ui.item_id = gi.id
                    WHERE ui.item_id = ? AND ui.character_id = ?
                    ORDER BY ui.id DESC
                    LIMIT 1
                ");
                $stmt->execute([$gameItemId, $characterId]);
                $inventoryItem = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($inventoryItem) {
                    error_log("✅ 在背包中找到对应的物品实例，将使用背包数据");
                } else {
                    error_log("ℹ️ 背包中没有这个物品，将使用基础数据");
                }
            } else {
                error_log("❌ ID {$itemId} 既不是有效的user_inventories ID，也不是有效的game_items ID");
                throw new Exception("物品ID {$itemId} 不存在");
            }
        }

        // 🔧 从game_items表获取物品的基础信息
        $stmt = $pdo->prepare("
            SELECT 
                gi.*,
                gi.physical_attack,
                gi.immortal_attack,
                gi.physical_defense,
                gi.immortal_defense,
                gi.hp_bonus,
                gi.mp_bonus,
                gi.speed_bonus,
                gi.critical_bonus,
                gi.critical_damage,
                gi.accuracy_bonus,
                gi.dodge_bonus,
                gi.critical_resistance,
                COALESCE(isk.skill_name, '') as skill_name,
                COALESCE(isk.damage_multiplier, 1.0) as damage_multiplier,
                COALESCE(isk.mp_cost, 0) as mp_cost,
                COALESCE(isk.trigger_chance, 0) as trigger_chance,
                COALESCE(isk.effect_duration, 0) as effect_duration,
                COALESCE(isk.animation_model, 'feijian') as animation_model,
                COALESCE(isk.skill_description, '') as skill_description,
                COALESCE(isk.element_type, 'neutral') as element_type
            FROM game_items gi
            LEFT JOIN item_skills isk ON gi.id = isk.item_id
            WHERE gi.id = ?
        ");
        $stmt->execute([$gameItemId]);
        $item = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$item) {
            throw new Exception("找不到指定的物品信息");
        }

        error_log("获取到基础物品信息: {$item['item_name']}");

        // 🔧 处理图片路径
        $iconImage = $item['icon_image'];
        if ($iconImage && strpos($iconImage, 'assets/') !== 0) {
            if (strpos($iconImage, 'equi/') === 0 || strpos($iconImage, 'weapon/') === 0) {
                $iconImage = 'assets/images/' . $iconImage;
            } elseif (strpos($iconImage, '/') === false) {
                $iconImage = 'assets/images/' . $iconImage;
            }
        }

        // 🔧 构建基础物品详情数据（从game_items获取）
        $itemDetail = [
            'id' => intval($item['id']),
            'name' => $item['item_name'] ?: '未知物品',
            'description' => $item['description'] ?: '暂无描述',
            'rarity' => $item['rarity'] ?: 'common',
            'level_requirement' => intval($item['level_requirement']) ?: 1,
            'realm_requirement' => intval($item['realm_requirement']) ?: 1,
            'slot_type' => $item['slot_type'] ?: 'material',
            'item_type' => $item['item_type'] ?: 'material',

            // 🔧 基础属性数据（确保为数值类型）
            'physical_attack' => intval($item['physical_attack']) ?: 0,
            'immortal_attack' => intval($item['immortal_attack']) ?: 0,
            'physical_defense' => intval($item['physical_defense']) ?: 0,
            'immortal_defense' => intval($item['immortal_defense']) ?: 0,

            // 🔧 删除：废弃的兼容字段已清理

            // 🔧 基础属性加成
            'hp_bonus' => intval($item['hp_bonus']) ?: 0,
            'mp_bonus' => intval($item['mp_bonus']) ?: 0,
            'speed_bonus' => intval($item['speed_bonus']) ?: 0,
            'critical_bonus' => floatval($item['critical_bonus']) ?: 0,
            'critical_damage' => floatval($item['critical_damage']) ?: 0,
            'accuracy_bonus' => floatval($item['accuracy_bonus']) ?: 0,
            'dodge_bonus' => floatval($item['dodge_bonus']) ?: 0,
            'critical_resistance' => floatval($item['critical_resistance']) ?: 0,

            // 🔧 技能相关属性
            'skill_name' => $item['skill_name'] ?: '',
            'damage_multiplier' => floatval($item['damage_multiplier']) ?: 1.0,
            'mp_cost' => intval($item['mp_cost']) ?: 0,
            'cooldown_time' => 0, // 数据库中没有cooldown字段，设为默认值
            'trigger_chance' => floatval($item['trigger_chance']) ?: 0,
            'effect_duration' => intval($item['effect_duration']) ?: 0,
            'animation_model' => $item['animation_model'] ?: 'feijian',
            'skill_description' => $item['skill_description'] ?: '',
            'element_type' => $item['element_type'] ?: 'neutral',

            // 🔧 图片和其他信息
            'icon_image' => $iconImage ?: 'assets/images/default_item.png',
            'image_url' => $iconImage ?: 'assets/images/default_item.png',
            'model_image' => $iconImage ?: 'assets/images/default_item.png',
            'detail_image' => $iconImage ?: 'assets/images/default_item.png',
            'sell_price' => intval($item['sell_price']) ?: 1,
            'special_effects' => $item['special_effects'] ?: null,

            // 🔧 默认状态信息
            'bind_status' => 0,
            'is_equipped' => false,
            'quantity' => 1,
            'current_durability' => ($item['item_type'] === 'weapon') ? 100 : null,
            'max_durability' => ($item['item_type'] === 'weapon') ? 100 : null
        ];

        // 🎯 核心逻辑：如果在背包中找到物品，使用背包数据覆盖基础数据
        if ($inventoryItem) {
            error_log("🎯 使用背包物品数据覆盖基础数据");

            // 更新背包相关的状态信息
            $itemDetail['bind_status'] = intval($inventoryItem['bind_status']) ?: 0;
            $itemDetail['quantity'] = intval($inventoryItem['quantity']) ?: 1;

            // 更新耐久度信息（如果有）
            if ($inventoryItem['current_durability'] !== null) {
                $itemDetail['current_durability'] = intval($inventoryItem['current_durability']);
                $itemDetail['max_durability'] = intval($inventoryItem['max_durability']) ?: 100;
            }

            // 检查是否已装备
            $stmt = $pdo->prepare("
                SELECT COUNT(*) as count 
                FROM character_equipment 
                WHERE inventory_item_id = ? AND character_id = ?
            ");
            $stmt->execute([$inventoryItem['id'], $characterId]);
            $equipped = $stmt->fetch(PDO::FETCH_ASSOC);
            $itemDetail['is_equipped'] = ($equipped['count'] > 0);

            // 🎯 关键：处理custom_attributes，让背包属性覆盖基础属性
            if (!empty($inventoryItem['custom_attributes'])) {
                $customAttrs = json_decode($inventoryItem['custom_attributes'], true);
                if (is_array($customAttrs)) {
                    error_log("🎯 解析到背包自定义属性: " . print_r($customAttrs, true));

                    // 处理嵌套的calculated_attributes
                    if (isset($customAttrs['calculated_attributes']) && is_array($customAttrs['calculated_attributes'])) {
                        $calculatedAttrs = $customAttrs['calculated_attributes'];
                        error_log("🎯 发现calculated_attributes: " . print_r($calculatedAttrs, true));

                        // 将calculated_attributes中的属性覆盖到主属性中
                        foreach ($calculatedAttrs as $key => $value) {
                            if (is_numeric($value) && $value > 0) {
                                $itemDetail[$key] = $value;
                                error_log("🎯 覆盖属性: {$key} = {$value}");
                            }
                        }
                    }

                    // 处理其他自定义属性（品质、倍率等）
                    foreach ($customAttrs as $key => $value) {
                        if ($key !== 'calculated_attributes') {
                            // 只覆盖有值的属性，空值保持基础数据
                            if ($value !== null && $value !== '' && $value !== 0) {
                                $itemDetail[$key] = $value;
                                error_log("🎯 覆盖其他属性: {$key} = {$value}");
                            }
                        }
                    }

                    // 特别处理品质信息
                    if (isset($customAttrs['rarity']) && !empty($customAttrs['rarity'])) {
                        $itemDetail['rarity'] = $customAttrs['rarity'];
                    }
                    if (isset($customAttrs['rarity_en']) && !empty($customAttrs['rarity_en'])) {
                        $itemDetail['rarity_en'] = $customAttrs['rarity_en'];
                    }

                    error_log("🎯 最终物品属性: 物理攻击={$itemDetail['physical_attack']}, 仙术攻击={$itemDetail['immortal_attack']}, 物理防御={$itemDetail['physical_defense']}, 仙术防御={$itemDetail['immortal_defense']}");
                } else {
                    error_log("⚠️ custom_attributes解析失败或不是数组");
                }
            } else {
                error_log("ℹ️ 背包物品没有custom_attributes，使用基础属性");
            }
        } else {
            error_log("ℹ️ 没有背包物品数据，使用基础属性");
        }

        // 🔧 确保品质映射正确
        $rarityMapping = [
            'common' => '普通',
            'uncommon' => '稀有',
            'rare' => '史诗',
            'epic' => '传说',
            'legendary' => '神话'
        ];

        if (isset($rarityMapping[$itemDetail['rarity']])) {
            $itemDetail['rarity_cn'] = $rarityMapping[$itemDetail['rarity']];
        } else {
            $itemDetail['rarity_cn'] = '普通';
        }

        error_log("🎯 最终返回的物品详情: " . json_encode($itemDetail, JSON_UNESCAPED_UNICODE));

        echo json_encode([
            'success' => true,
            'message' => '获取物品详情成功',
            'item' => $itemDetail
        ], JSON_UNESCAPED_UNICODE);
    } catch (Exception $e) {
        error_log("getItemDetailUnified 错误: " . $e->getMessage());
        echo json_encode([
            'success' => false,
            'message' => '获取物品详情失败: ' . $e->getMessage(),
            'item' => null
        ], JSON_UNESCAPED_UNICODE);
    }
}

/**
 * 🔧 新增：获取单个物品详情（用于战斗界面等场景）
 */
function getItemDetailByNameUnified($userId, $pdo)
{
    try {
        $itemName = isset($_GET['item_name']) ? trim($_GET['item_name']) : '';

        if (empty($itemName)) {
            throw new Exception("缺少物品名称");
        }

        error_log("getItemDetailByNameUnified - 请求的物品名称: " . $itemName);

        // 首先获取用户的角色ID
        $stmt = $pdo->prepare("SELECT id FROM characters WHERE user_id = ? LIMIT 1");
        $stmt->execute([$userId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$character) {
            throw new Exception("找不到有效的角色信息");
        }

        $characterId = $character['id'];
        error_log("角色ID: " . $characterId);

        // 🔧 优先查找用户背包中最新的该物品记录
        $stmt = $pdo->prepare("
            SELECT ui.*, gi.id as game_item_id, gi.item_name
            FROM user_inventories ui
            LEFT JOIN game_items gi ON ui.item_id = gi.id
            WHERE ui.character_id = ? AND gi.item_name = ?
            ORDER BY ui.id DESC
            LIMIT 1
        ");
        $stmt->execute([$characterId, $itemName]);
        $inventoryItem = $stmt->fetch(PDO::FETCH_ASSOC);

        $gameItemId = null;
        $useInventoryData = false;

        if ($inventoryItem && $inventoryItem['game_item_id']) {
            // 找到了user_inventories记录，使用对应的game_items ID
            $gameItemId = $inventoryItem['game_item_id'];
            $useInventoryData = true;
            error_log("✅ 在用户背包中找到物品: {$itemName}, game_items ID: {$gameItemId}");
        } else {
            // 没找到user_inventories记录，直接从game_items表查找
            $stmt = $pdo->prepare("SELECT id FROM game_items WHERE item_name = ? LIMIT 1");
            $stmt->execute([$itemName]);
            $gameItem = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($gameItem) {
                $gameItemId = $gameItem['id'];
                $useInventoryData = false;
                error_log("✅ 在game_items表中找到物品: {$itemName}, ID: {$gameItemId}");
            } else {
                error_log("❌ 物品名称 {$itemName} 不存在");
                throw new Exception("物品名称 {$itemName} 不存在");
            }
        }

        // 🔧 从game_items表获取物品的基础信息
        $stmt = $pdo->prepare("
            SELECT 
                gi.*,
                gi.physical_attack,
                gi.immortal_attack,
                gi.physical_defense,
                gi.immortal_defense,
                gi.hp_bonus,
                gi.mp_bonus,
                gi.speed_bonus,
                gi.critical_bonus,
                gi.critical_damage,
                gi.accuracy_bonus,
                gi.dodge_bonus,
                gi.critical_resistance,
                COALESCE(isk.skill_name, '') as skill_name,
                COALESCE(isk.damage_multiplier, 1.0) as damage_multiplier,
                COALESCE(isk.mp_cost, 0) as mp_cost,
                COALESCE(isk.trigger_chance, 0) as trigger_chance,
                COALESCE(isk.effect_duration, 0) as effect_duration,
                COALESCE(isk.animation_model, 'feijian') as animation_model,
                COALESCE(isk.skill_description, '') as skill_description,
                COALESCE(isk.element_type, 'neutral') as element_type
            FROM game_items gi
            LEFT JOIN item_skills isk ON gi.id = isk.item_id
            WHERE gi.id = ?
        ");
        $stmt->execute([$gameItemId]);
        $item = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$item) {
            throw new Exception("找不到指定的物品信息");
        }

        error_log("获取到基础物品信息: {$item['item_name']}");

        // 🔧 处理图片路径
        $iconImage = $item['icon_image'];
        if ($iconImage && strpos($iconImage, 'assets/') !== 0) {
            if (strpos($iconImage, 'equi/') === 0 || strpos($iconImage, 'weapon/') === 0) {
                $iconImage = 'assets/images/' . $iconImage;
            } elseif (strpos($iconImage, '/') === false) {
                $iconImage = 'assets/images/' . $iconImage;
            }
        }

        // 🔧 构建完整的物品详情数据
        $itemDetail = [
            'id' => intval($item['id']),
            'name' => $item['item_name'] ?: '未知物品',
            'description' => $item['description'] ?: '暂无描述',
            'rarity' => $item['rarity'] ?: 'common',
            'level_requirement' => intval($item['level_requirement']) ?: 1,
            'realm_requirement' => intval($item['realm_requirement']) ?: 1,
            'slot_type' => $item['slot_type'] ?: 'material',
            'item_type' => $item['item_type'] ?: 'material',

            // 🔧 属性数据（确保为数值类型）
            'physical_attack' => intval($item['physical_attack']) ?: 0,
            'immortal_attack' => intval($item['immortal_attack']) ?: 0,
            'physical_defense' => intval($item['physical_defense']) ?: 0,
            'immortal_defense' => intval($item['immortal_defense']) ?: 0,

            // 🔧 删除：废弃的兼容字段已清理

            // 🔧 基础属性加成
            'hp_bonus' => intval($item['hp_bonus']) ?: 0,
            'mp_bonus' => intval($item['mp_bonus']) ?: 0,
            'speed_bonus' => intval($item['speed_bonus']) ?: 0,
            'critical_bonus' => floatval($item['critical_bonus']) ?: 0,
            'critical_damage' => floatval($item['critical_damage']) ?: 0,
            'accuracy_bonus' => floatval($item['accuracy_bonus']) ?: 0,
            'dodge_bonus' => floatval($item['dodge_bonus']) ?: 0,
            'critical_resistance' => floatval($item['critical_resistance']) ?: 0,

            // 🔧 技能相关属性
            'skill_name' => $item['skill_name'] ?: '',
            'damage_multiplier' => floatval($item['damage_multiplier']) ?: 1.0,
            'mp_cost' => intval($item['mp_cost']) ?: 0,
            'cooldown_time' => 0, // 数据库中没有cooldown字段，设为默认值
            'trigger_chance' => floatval($item['trigger_chance']) ?: 0,
            'effect_duration' => intval($item['effect_duration']) ?: 0,
            'animation_model' => $item['animation_model'] ?: 'feijian',
            'skill_description' => $item['skill_description'] ?: '',
            'element_type' => $item['element_type'] ?: 'neutral',

            // 🔧 图片和其他信息
            'icon_image' => $iconImage ?: 'assets/images/default_item.png',
            'image_url' => $iconImage ?: 'assets/images/default_item.png',
            'model_image' => $iconImage ?: 'assets/images/default_item.png',
            'detail_image' => $iconImage ?: 'assets/images/default_item.png',
            'sell_price' => intval($item['sell_price']) ?: 1,
            'special_effects' => $item['special_effects'] ?: null,

            // 🔧 状态信息
            'bind_status' => 0, // 默认未绑定
            'is_equipped' => false, // 默认未装备
            'quantity' => 1, // 默认数量

            // 🔧 如果是武器，添加耐久度信息
            'current_durability' => ($item['item_type'] === 'weapon') ? 100 : null,
            'max_durability' => ($item['item_type'] === 'weapon') ? 100 : null
        ];

        // 🔧 如果有user_inventories数据，使用实际的背包数据
        if ($useInventoryData && $inventoryItem) {
            error_log("使用user_inventories数据，custom_attributes长度: " . strlen($inventoryItem['custom_attributes'] ?: ''));

            // 🔧 更新状态信息
            $itemDetail['bind_status'] = intval($inventoryItem['bind_status']) ?: 0;
            $itemDetail['quantity'] = intval($inventoryItem['quantity']) ?: 1;

            // 🔧 更新耐久度信息（如果有）
            if ($inventoryItem['current_durability'] !== null) {
                $itemDetail['current_durability'] = intval($inventoryItem['current_durability']);
                $itemDetail['max_durability'] = intval($inventoryItem['max_durability']) ?: 100;
            }

            // 🔧 检查是否已装备
            $stmt = $pdo->prepare("
                SELECT COUNT(*) as count 
                FROM character_equipment 
                WHERE inventory_item_id = ? AND character_id = ?
            ");
            $stmt->execute([$inventoryItem['id'], $characterId]);
            $equipped = $stmt->fetch(PDO::FETCH_ASSOC);
            $itemDetail['is_equipped'] = ($equipped['count'] > 0);

            // 🔧 修复：优先处理自定义属性，让自定义属性覆盖基础属性
            if (!empty($inventoryItem['custom_attributes'])) {
                $customAttrs = json_decode($inventoryItem['custom_attributes'], true);
                if (is_array($customAttrs)) {
                    error_log("解析到的自定义属性: " . print_r($customAttrs, true));

                    // 🔧 处理嵌套的calculated_attributes
                    if (isset($customAttrs['calculated_attributes']) && is_array($customAttrs['calculated_attributes'])) {
                        $calculatedAttrs = $customAttrs['calculated_attributes'];
                        error_log("发现calculated_attributes: " . print_r($calculatedAttrs, true));

                        // 🔧 将calculated_attributes中的属性直接合并到主属性中
                        foreach ($calculatedAttrs as $key => $value) {
                            if (is_numeric($value) && $value > 0) {
                                $itemDetail[$key] = $value;
                                error_log("设置属性: {$key} = {$value}");
                            }
                        }

                        // 🔧 删除：废弃的兼容字段计算已清理
                    }

                    // 🔧 处理其他自定义属性（品质、倍率等）
                    foreach ($customAttrs as $key => $value) {
                        if ($key !== 'calculated_attributes' && !isset($itemDetail[$key])) {
                            $itemDetail[$key] = $value;
                        }
                    }

                    // 🔧 特别处理品质信息
                    if (isset($customAttrs['rarity']) && !empty($customAttrs['rarity'])) {
                        $itemDetail['rarity'] = $customAttrs['rarity'];
                    }
                    if (isset($customAttrs['rarity_en']) && !empty($customAttrs['rarity_en'])) {
                        $itemDetail['rarity_en'] = $customAttrs['rarity_en'];
                    }

                    error_log("最终物品属性: 物理攻击={$itemDetail['physical_attack']}, 仙术攻击={$itemDetail['immortal_attack']}, 物理防御={$itemDetail['physical_defense']}, 仙术防御={$itemDetail['immortal_defense']}");
                } else {
                    error_log("⚠️ custom_attributes解析失败或不是数组");
                }
            } else {
                error_log("⚠️ 物品没有custom_attributes或为空");
            }
        } else {
            // 🔧 如果是装备类物品但没有user_inventories数据，尝试查找用户背包中的同类物品
            if (in_array($item['item_type'], ['weapon', 'armor', 'accessory'])) {
                $stmt = $pdo->prepare("
                    SELECT 
                        ui.*,
                        ce.durability as current_durability,
                        ce.max_durability
                    FROM user_inventories ui
                    LEFT JOIN character_equipment ce ON ui.id = ce.inventory_item_id AND ce.character_id = ?
                    WHERE ui.character_id = ? AND ui.item_id = ?
                    ORDER BY ui.id DESC
                    LIMIT 1
                ");
                $stmt->execute([$characterId, $characterId, $gameItemId]);
                $fallbackInventoryItem = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($fallbackInventoryItem) {
                    error_log("找到同类物品的背包记录，使用其custom_attributes");

                    // 使用找到的背包物品数据
                    $itemDetail['bind_status'] = intval($fallbackInventoryItem['bind_status']) ?: 0;
                    $itemDetail['is_equipped'] = !empty($fallbackInventoryItem['current_durability']);
                    $itemDetail['quantity'] = intval($fallbackInventoryItem['quantity']) ?: 1;

                    if ($fallbackInventoryItem['current_durability'] !== null) {
                        $itemDetail['current_durability'] = intval($fallbackInventoryItem['current_durability']);
                        $itemDetail['max_durability'] = intval($fallbackInventoryItem['max_durability']) ?: 100;
                    }

                    // 处理custom_attributes
                    if (!empty($fallbackInventoryItem['custom_attributes'])) {
                        $customAttrs = json_decode($fallbackInventoryItem['custom_attributes'], true);
                        if (is_array($customAttrs)) {
                            // 同样的custom_attributes处理逻辑
                            if (isset($customAttrs['calculated_attributes']) && is_array($customAttrs['calculated_attributes'])) {
                                foreach ($customAttrs['calculated_attributes'] as $key => $value) {
                                    if (is_numeric($value) && $value > 0) {
                                        $itemDetail[$key] = $value;
                                    }
                                }

                                // 🔧 删除：废弃的兼容字段计算已清理
                            }

                            foreach ($customAttrs as $key => $value) {
                                if ($key !== 'calculated_attributes' && !isset($itemDetail[$key])) {
                                    $itemDetail[$key] = $value;
                                }
                            }

                            if (isset($customAttrs['rarity']) && !empty($customAttrs['rarity'])) {
                                $itemDetail['rarity'] = $customAttrs['rarity'];
                            }
                        }
                    }
                } else {
                    error_log("⚠️ 在用户背包中未找到该物品记录");
                }
            }
        }

        echo json_encode([
            'success' => true,
            'message' => '获取物品详情成功',
            'item' => $itemDetail
        ], JSON_UNESCAPED_UNICODE);
    } catch (Exception $e) {
        error_log("获取物品详情失败: " . $e->getMessage() . "\n" . $e->getTraceAsString());
        echo json_encode([
            'success' => false,
            'message' => '获取物品详情失败: ' . $e->getMessage(),
            'item' => null
        ], JSON_UNESCAPED_UNICODE);
    }
}

/** * 保存用户头像外框设置 */
function saveAvatarFrameUnified($userId, $pdo)
{
    $avatarFrame = isset($_POST['avatar_frame']) ? trim($_POST['avatar_frame']) : '';

    if (!$avatarFrame) {
        echo json_encode(['success' => false, 'message' => '缺少头像外框参数']);
        return;
    }

    try {
        // 验证头像外框文件名（安全检查）
        if (!preg_match('/^[a-zA-Z0-9_\s\(\).-]+\.png$/i', $avatarFrame)) {
            throw new Exception('头像外框文件名格式错误');
        }

        // 检查头像外框文件是否存在
        $framePath = __DIR__ . '/../../public/assets/images/head/' . $avatarFrame;
        if (!file_exists($framePath)) {
            throw new Exception('头像外框文件不存在');
        }

        // 更新角色的头像外框设置
        $stmt = $pdo->prepare("UPDATE characters SET avatar_frame = ? WHERE user_id = ?");
        $result = $stmt->execute([$avatarFrame, $userId]);

        if ($result) {
            echo json_encode([
                'success' => true,
                'message' => '头像外框设置保存成功',
                'avatar_frame' => $avatarFrame
            ]);
        } else {
            throw new Exception('头像外框设置保存失败');
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/** * 获取可用头像外框列表 */
function getAvailableFramesUnified($userId, $pdo)
{
    try {
        $frames = [];

        // 获取头像外框目录
        $frameDir = __DIR__ . '/../../public/assets/images/head/';
        if (is_dir($frameDir)) {
            $files = scandir($frameDir);
            foreach ($files as $file) {
                if (preg_match('/\.png$/i', $file) && is_file($frameDir . $file)) {
                    // 根据文件名判断解锁条件（这里可以根据实际需求调整）
                    $unlocked = true; // 暂时全部解锁
                    $requirement = '';

                    // 根据文件名设置不同的解锁条件
                    if (strpos($file, 'base1') !== false) {
                        $name = '初入江湖';
                        $unlocked = true;
                    } elseif (strpos($file, 'base2') !== false) {
                        $name = '小有名气';
                        $unlocked = true;
                    } elseif (strpos($file, 'base3') !== false) {
                        $name = '名动一方';
                        $unlocked = true;
                    } elseif (strpos($file, 'base4') !== false) {
                        $name = '名闻天下';
                        $unlocked = false;
                    } elseif (strpos($file, 'base5') !== false) {
                        $name = '一代宗师';
                        $unlocked = false;
                    } elseif (strpos($file, 'base6') !== false) {
                        $name = '登峰造极';
                        $unlocked = false;
                    } elseif (strpos($file, 'wlmz1') !== false) {
                        $name = '武林神话';
                        $unlocked = false;
                        $requirement = '特殊成就';
                    } elseif (strpos($file, 'wlmz2') !== false) {
                        $name = '武林盟主';
                        $unlocked = false;
                        $requirement = '特殊成就';
                    } else {
                        $name = pathinfo($file, PATHINFO_FILENAME);
                        $unlocked = false;
                        $requirement = '特殊条件';
                    }

                    $frames[] = [
                        'filename' => $file,
                        'name' => $name,
                        'unlocked' => $unlocked,
                        'requirement' => $requirement
                    ];
                }
            }
        }

        // 按文件名排序
        usort($frames, function ($a, $b) {
            return strcmp($a['filename'], $b['filename']);
        });

        echo json_encode([
            'success' => true,
            'frames' => $frames
        ]);
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => '获取头像外框列表失败: ' . $e->getMessage()]);
    }
}

/**
 * 🔧 新增：简单直接地获取物品图片
 */
function getItemImageUnified($userId, $pdo)
{
    try {
        $itemName = isset($_GET['item_name']) ? $_GET['item_name'] : '';

        if (empty($itemName)) {
            throw new Exception("物品名称不能为空");
        }

        // 直接从数据库获取物品图片信息
        $stmt = $pdo->prepare("
            SELECT icon_image 
            FROM game_items 
            WHERE item_name = ? 
            LIMIT 1
        ");
        $stmt->execute([$itemName]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($result && !empty($result['icon_image'])) {
            // 处理图片路径，确保格式正确
            $iconImage = $result['icon_image'];

            // 如果不是完整路径，添加assets/images/前缀
            if (strpos($iconImage, 'assets/images/') !== 0 && strpos($iconImage, 'http') !== 0) {
                $iconImage = 'assets/images/' . $iconImage;
            }

            echo json_encode([
                'success' => true,
                'icon_image' => $iconImage,
                'item_name' => $itemName
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => '未找到物品或物品没有图片信息',
                'item_name' => $itemName
            ]);
        }
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

/**
 * 🆕 确保角色拥有完整的6个武器槽位
 * 这个函数会检查角色的武器槽位，如果不足6个则自动创建
 */
function ensureWeaponSlotsExist($pdo, $characterId)
{
    try {
        // 检查当前武器槽位数量
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count 
            FROM character_equipment 
            WHERE character_id = ? AND slot_type = 'weapon'
        ");
        $stmt->execute([$characterId]);
        $weaponSlotCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

        if ($weaponSlotCount < 6) {
            error_log("🔧 [武器槽位] 角色ID {$characterId} 武器槽位不足({$weaponSlotCount}/6)，正在初始化...");

            // 如果槽位不完整，删除现有的武器槽位重新创建
            if ($weaponSlotCount > 0 && $weaponSlotCount < 6) {
                // 获取现有已装备的武器信息，避免丢失
                $stmt = $pdo->prepare("
                    SELECT slot_index, item_id, inventory_item_id, enhancement_level
                    FROM character_equipment 
                    WHERE character_id = ? AND slot_type = 'weapon' AND item_id > 0
                ");
                $stmt->execute([$characterId]);
                $existingWeapons = $stmt->fetchAll(PDO::FETCH_ASSOC);

                // 删除现有不完整的武器槽位
                $stmt = $pdo->prepare("DELETE FROM character_equipment WHERE character_id = ? AND slot_type = 'weapon'");
                $stmt->execute([$characterId]);

                // 创建完整的6个武器槽位
                for ($i = 1; $i <= 6; $i++) {
                    $stmt = $pdo->prepare("
                        INSERT INTO character_equipment (
                            character_id, item_id, slot_type, slot_index, 
                            inventory_item_id, enhancement_level, attack_order, is_active
                        ) VALUES (?, 0, 'weapon', ?, NULL, 0, ?, TRUE)
                    ");
                    $stmt->execute([$characterId, $i, $i]);
                }

                // 重新装备之前的武器
                foreach ($existingWeapons as $weapon) {
                    $slotIndex = $weapon['slot_index'];
                    if ($slotIndex >= 1 && $slotIndex <= 6) {
                        $stmt = $pdo->prepare("
                            UPDATE character_equipment 
                            SET item_id = ?, inventory_item_id = ?, enhancement_level = ?
                            WHERE character_id = ? AND slot_type = 'weapon' AND slot_index = ?
                        ");
                        $stmt->execute([
                            $weapon['item_id'],
                            $weapon['inventory_item_id'],
                            $weapon['enhancement_level'],
                            $characterId,
                            $slotIndex
                        ]);

                        error_log("🔄 [武器槽位] 恢复武器到槽位 {$slotIndex}");
                    }
                }

                error_log("✅ [武器槽位] 已为角色ID {$characterId} 重建完整的6个武器槽位，恢复 " . count($existingWeapons) . " 件已装备武器");
            } else {
                // 完全没有武器槽位，直接创建
                for ($i = 1; $i <= 6; $i++) {
                    $stmt = $pdo->prepare("
                        INSERT INTO character_equipment (
                            character_id, item_id, slot_type, slot_index, 
                            attack_order, is_active
                        ) VALUES (?, 0, 'weapon', ?, ?, TRUE)
                    ");
                    $stmt->execute([$characterId, $i, $i]);
                }

                error_log("✅ [武器槽位] 已为角色ID {$characterId} 创建完整的6个武器槽位");
            }
        }

        return true;
    } catch (Exception $e) {
        error_log("❌ [武器槽位] 初始化失败: " . $e->getMessage());
        return false;
    }
}
