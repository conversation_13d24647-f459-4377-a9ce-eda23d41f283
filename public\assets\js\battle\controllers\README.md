# 战斗系统控制器 (Battle System Controllers)

## UI控制器 (BattleUIController)

### 职责范围
1. 管理所有战斗相关的UI元素
2. 处理UI更新和动画显示
3. 管理战斗状态显示
4. 处理物品掉落界面
5. 处理胜利/失败界面

### 依赖关系
- 不依赖其他模块，但被BattleSystem依赖
- 纯UI操作，不包含业务逻辑

### 主要功能
1. 战斗状态显示
   - 更新战斗状态
   - 显示当前技能
   - 显示区域信息
   
2. 武器系统UI
   - 显示武器槽位
   - 更新武器信息
   
3. 技能效果
   - 显示技能喊话
   - 管理技能动画容器
   
4. 战斗结果
   - 显示胜利面板
   - 显示掉落物品
   - 显示物品详情

### 使用方法
```javascript
// 创建UI控制器实例
const uiController = new BattleUIController();

// 初始化UI
uiController.initialize();

// 更新战斗状态
uiController.updateBattleStatus('战斗开始！');

// 显示技能喊话
await uiController.showSkillShout('剑气外放！');

// 显示胜利面板
await uiController.showVictoryPanel('战斗胜利！', droppedItems);
```

### 注意事项
1. 所有DOM操作都应该通过此控制器进行
2. 异步方法需要使用await等待完成
3. 保持与原有UI风格一致
4. 确保正确处理元素的创建和销毁

## 待完成的控制器

1. AnimationController (动画控制器)
   - 处理技能动画
   - 管理动画效果
   - 控制动画时序

2. AudioController (音效控制器)
   - 管理战斗音效
   - 控制背景音乐
   - 处理音量控制 