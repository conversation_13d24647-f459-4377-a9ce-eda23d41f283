/* 🏆 竞技场专用胜利面板样式 */
.arena-victory-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
}

.arena-victory-panel {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    border: 3px solid #FFD700;
    border-radius: 20px;
    padding: 30px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(255, 215, 0, 0.3);
    animation: arenaVictorySlideIn 0.5s ease-out;
}

@keyframes arenaVictorySlideIn {
    from {
        transform: translateY(-50px) scale(0.9);
        opacity: 0;
    }
    to {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

.arena-victory-header {
    text-align: center;
    margin-bottom: 25px;
    border-bottom: 2px solid #FFD700;
    padding-bottom: 15px;
}

.arena-victory-title {
    font-size: 2.2rem;
    font-weight: bold;
    margin: 0 0 10px 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    background: linear-gradient(45deg, #FFD700, #FFA500);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.arena-victory-subtitle {
    font-size: 1.1rem;
    color: #E6E6E6;
    margin: 0;
    opacity: 0.9;
}

.arena-rewards-section {
    margin-bottom: 25px;
    background: rgba(255, 215, 0, 0.1);
    border-radius: 15px;
    padding: 20px;
    border: 1px solid rgba(255, 215, 0, 0.3);
}

.arena-rewards-section h3 {
    color: #FFD700;
    margin: 0 0 15px 0;
    font-size: 1.3rem;
    text-align: center;
}

.arena-reward-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    padding: 10px 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    border: 1px solid rgba(255, 215, 0, 0.2);
}

.arena-reward-item:last-child {
    margin-bottom: 0;
}

.reward-icon {
    font-size: 1.2rem;
    margin-right: 10px;
}

.reward-label {
    color: #E6E6E6;
    font-weight: 500;
    flex: 1;
}

.reward-value {
    font-weight: bold;
    font-size: 1.1rem;
}

.reward-value.spirit-stones {
    color: #00D4AA;
    text-shadow: 0 0 5px rgba(0, 212, 170, 0.5);
}

.reward-value.rank-points.positive {
    color: #4CAF50;
    text-shadow: 0 0 5px rgba(76, 175, 80, 0.5);
}

.reward-value.rank-points.negative {
    color: #F44336;
    text-shadow: 0 0 5px rgba(244, 67, 54, 0.5);
}

.ai-puppet-notice {
    color: #FFA500;
    font-size: 0.9rem;
    text-align: center;
    margin: 15px 0 0 0;
    font-style: italic;
    opacity: 0.8;
}

.arena-stats-section {
    margin-bottom: 25px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.arena-stats-section h3 {
    color: #E6E6E6;
    margin: 0 0 15px 0;
    font-size: 1.2rem;
    text-align: center;
}

.arena-stats-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 10px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-label {
    color: #B0B0B0;
    font-size: 0.95rem;
}

.stat-value {
    color: #FFD700;
    font-weight: bold;
    font-size: 1rem;
}

.arena-victory-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.arena-victory-buttons .victory-btn {
    flex: 1;
    padding: 12px 20px;
    border: none;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.arena-victory-buttons .continue-btn {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    border: 2px solid #4CAF50;
}

.arena-victory-buttons .continue-btn:hover {
    background: linear-gradient(135deg, #45a049, #3d8b40);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
}

.arena-victory-buttons .exit-btn {
    background: linear-gradient(135deg, #607D8B, #546E7A);
    color: white;
    border: 2px solid #607D8B;
}

.arena-victory-buttons .exit-btn:hover {
    background: linear-gradient(135deg, #546E7A, #455A64);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(96, 125, 139, 0.4);
}

/* 📱 移动端适配 */
@media (max-width: 768px) {
    .arena-victory-panel {
        padding: 20px;
        margin: 20px;
        max-height: 85vh;
    }
    
    .arena-victory-title {
        font-size: 1.8rem;
    }
    
    .arena-victory-subtitle {
        font-size: 1rem;
    }
    
    .arena-rewards-section,
    .arena-stats-section {
        padding: 15px;
    }
    
    .arena-reward-item {
        padding: 8px 12px;
    }
    
    .arena-victory-buttons {
        flex-direction: column;
        gap: 10px;
    }
    
    .arena-victory-buttons .victory-btn {
        padding: 15px 20px;
        font-size: 1.1rem;
    }
} 