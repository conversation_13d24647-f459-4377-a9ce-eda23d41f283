# �� 一念修仙新技能模型添加完整指南 (v3.0)

## 📋 概述

本指南提供了在一念修仙战斗系统中添加新技能模型的完整步骤，基于**战斗系统v3.0架构**（统一配置系统、简化映射、数据库驱动）。

## 🚀 v3.0 重大更新 (2024年12月)

### ✨ 新特性
- **统一配置系统**：所有技能映射集中在 `skill-config.js` 中管理
- **数据库驱动**：完全由后台 `animation_model` 字段控制技能动画
- **简化映射**：删除复杂的别名映射和特殊判断逻辑
- **英文类名规范**：所有JavaScript类名必须使用英文

### 🎯 核心原则
- **单一数据源**：只有 `skill-config.js` 负责 animation_model 映射
- **完全可控**：您可以随意配置哪个技能用哪个动画
- **降级保护**：统一配置不可用时自动降级到本地配置
- **无冗余映射**：消除了多处重复的映射配置

## 🎮 技能系统架构 (v3.0)

### 📁 技能系统文件结构
```
public/assets/js/battle/skills/     # JavaScript技能模块
├── base-skill.js                  # 技能基类（不要修改）
├── skill-config.js                # 🌟 统一配置系统（v3.0核心）
├── skill-loader.js                # 技能加载器（降级方案）
├── feijian-skill.js               # 飞剑术（独立文件）
├── wanjianjue-skill.js            # 万剑诀（独立文件）
├── jujian-skill.js                # 巨剑术（独立文件）
├── lightning-skills.js            # 雷法技能模块
├── fire-skills.js                 # 火法技能模块
├── wood-skills.js                 # 木系技能模块
├── water-skills.js                # 水系技能模块
├── earth-skills.js                # 土系技能模块
├── metal-skills.js                # 金系技能模块
├── ice-skills.js                  # 冰系技能模块
├── wind-skills.js                 # 风系技能模块
├── hengzhan-skill.js              # 横斩（独立文件）
├── huichunjian-skill.js           # 回春剑（独立文件）
└── {新技能}-skill.js              # 新独立技能文件

public/assets/css/battle/skills/    # CSS动画样式
├── base-animations.css            # 基础动画（不要修改）
├── feijian-animations.css         # 飞剑术动画
├── wanjianjue-animations.css      # 万剑诀动画
├── lightning-animations.css       # 雷法动画
├── fire-animations.css            # 火法动画
└── {新技能}-animations.css       # 新技能动画样式
```

### 🔧 v3.0 统一配置系统

#### skill-config.js 结构
```javascript
class SkillConfig {
    constructor() {
        // 核心映射：animation_model → 技能名称
        this.animationModelMapping = {
            'feijian': '飞剑术',
            'wanjianjue': '万剑诀',
            'jujian': '巨剑术',
            'zhangxinlei': '掌心雷',
            'huoqiushu': '火球术',
            'hengzhan': '横斩',
            // ... 更多映射
        };

        // 技能实现配置：技能名称 → 模块配置
        this.skillImplementationMapping = {
            '飞剑术': { 
                module: 'feijian-skill', 
                class: 'FeiJianSkill',        // ✅ 英文类名
                css: 'feijian-animations',
                animationModel: 'feijian'
            },
            '火流星': { 
                module: 'fire-skills', 
                class: 'HuoLiuXingSkill',     // ✅ 英文类名
                css: 'huoliuxing-animations',
                animationModel: 'huoliuxing'
            },
            // ... 更多配置
        };
    }
}
```

## 🛠️ 添加新技能动画的完整步骤 (v3.0)

### 📋 前置条件
- **数据库技能记录**：由用户自行在 `item_skills` 表中添加技能记录
- **装备关联**：由用户自行将技能关联到对应装备
- **animation_model 字段**：确保数据库中的 `animation_model` 值与前端映射一致

### 步骤1：确定技能分类和命名规范

#### 1.1 技能分类规则 (v3.0)
- **独立技能**：重要/复杂技能使用独立文件 (推荐)
  - 例如：`feijian-skill.js`, `wanjianjue-skill.js`
- **模块技能**：相似技能可以放在同一模块
  - 例如：`lightning-skills.js` (掌心雷、雷剑)

#### 1.2 命名规范 (v3.0 强制英文)
- **技能名称**: 必须与数据库中`skill_name`字段完全一致
- **类名**: 英文拼音 + "Skill" (如: `HuoLiuXingSkill`) ❌不能用中文
- **文件名**: 技能英文名 + "-skill.js" (如: `huoliuxing-skill.js`)
- **CSS文件**: 技能英文名 + "-animations.css"
- **动画容器**: 技能英文名 + "-container"

### 步骤2：创建JavaScript技能模块 (v3.0优化)

#### 2.1 独立技能文件示例
创建 `huoliuxing-skill.js`:

```javascript
/**
 * 火流星技能模块
 * 对应 animation_model = 'huoliuxing'
 */

/**
 * 火流星技能类
 * ✅ 类名必须使用英文
 */
class HuoLiuXingSkill extends BaseSkill {
    constructor(battleSystem) {
        super(battleSystem);
        this.skillName = '火流星';  // 显示名称可以用中文
        this.elementType = 'fire';
        
        // v3.0新增：技能实例管理
        this.animationContainers = new Set();
        this.activeTimers = new Set();
    }

    async execute(skillData, weaponImage) {
        try {
            // 必须调用技能喊话
            await this.showSkillShout(this.skillName);
            
            // 调用具体的技能动画方法
            await this.createHuoLiuXing(weaponImage);
            
        } catch (error) {
            console.error(`❌ ${this.skillName} 执行失败:`, error);
            this.handleError(error, 'execute');
        }
    }
    
    async createHuoLiuXing(weaponImage) {
        // 获取施法者和目标位置
        const casterPos = this.getCharacterPosition(true);
        const targetPos = this.getCharacterPosition(false);
        
        // 创建技能动画容器
        const container = this.createElement('div', {
            className: 'huoliuxing-container',
            style: {
                position: 'absolute',
                top: '0',
                left: '0',
                width: '100%',
                height: '100%',
                pointerEvents: 'none',
                zIndex: 1000
            }
        });
        
        this.animationContainers.add(container);
        this.effectsContainer.appendChild(container);
        
        try {
            // 第一阶段：蓄力阶段
            await this.createChargeEffect(container, casterPos, weaponImage);
            
            // 第二阶段：流星坠落
            await this.createMeteorFall(container, targetPos);
            
            // 第三阶段：爆炸效果
            await this.createExplosionEffect(container, targetPos);
            
            // 击中效果（必须调用）
            this.createHitEffect(targetPos.x, targetPos.y, true);
            
        } finally {
            // 安全清理机制
            this.safeCleanupContainer(container);
        }
    }
    
    // ... 其他方法实现
}

// ✅ 导出技能类（必须按此格式，类名必须是英文）
window.HuoLiuXingSkills = { HuoLiuXingSkill };
```

### 步骤3：更新统一配置 (v3.0核心)

#### 3.1 更新 skill-config.js
```javascript
// 在 animationModelMapping 中添加映射
this.animationModelMapping = {
    // ... 现有映射 ...
    'huoliuxing': '火流星',  // 新增
};

// 在 skillImplementationMapping 中添加配置
this.skillImplementationMapping = {
    // ... 现有配置 ...
    '火流星': { 
        module: 'huoliuxing-skill',      // 文件名（不含.js）
        class: 'HuoLiuXingSkill',        // ✅ 英文类名
        css: 'huoliuxing-animations',    // CSS文件名（不含.css）
        animationModel: 'huoliuxing'     // 对应的 animation_model
    },
};
```

### 步骤4：创建CSS动画样式

创建 `huoliuxing-animations.css`:

```css
/* 火流星技能动画样式 */
.huoliuxing-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1000;
}

/* 蓄力效果 */
@keyframes huoliuxing-charge {
    0% { 
        transform: translate(-50%, -50%) scale(0); 
        opacity: 0; 
    }
    50% { 
        transform: translate(-50%, -50%) scale(1.2); 
        opacity: 1; 
    }
    100% { 
        transform: translate(-50%, -50%) scale(1); 
        opacity: 1; 
    }
}

/* 流星坠落 */
@keyframes huoliuxing-fall {
    0% { 
        left: var(--startX); 
        top: var(--startY); 
        transform: translate(-50%, -50%) scale(1); 
    }
    100% { 
        left: var(--targetX); 
        top: var(--targetY); 
        transform: translate(-50%, -50%) scale(1.5); 
    }
}

/* 爆炸效果 */
@keyframes huoliuxing-explosion {
    0% { 
        transform: translate(-50%, -50%) scale(0); 
        opacity: 1; 
    }
    50% { 
        transform: translate(-50%, -50%) scale(2); 
        opacity: 0.8; 
    }
    100% { 
        transform: translate(-50%, -50%) scale(3); 
        opacity: 0; 
    }
}

/* 移动端适配（必须包含） */
@media (max-width: 768px) {
    .huoliuxing-container {
        /* 移动端样式调整 */
    }
}
```

### 步骤5：数据库配置

确保数据库中的技能记录正确配置：

```sql
-- 示例：为火流星技能设置 animation_model
UPDATE item_skills 
SET animation_model = 'huoliuxing' 
WHERE skill_name = '火流星';
```

## 🎯 v3.0 开发核心规范

### ✅ 必须遵守的规则

1. **英文类名**：所有JavaScript类名必须使用英文
   - ✅ `class HuoLiuXingSkill`
   - ❌ `class 火流星Skill`

2. **统一配置优先**：优先使用 `skill-config.js`，`skill-loader.js` 仅作降级

3. **数据库驱动**：技能使用哪个动画完全由 `animation_model` 字段决定

4. **安全清理**：必须实现 `safeCleanupContainer` 和错误处理

5. **标准导出**：
   ```javascript
   // ✅ 正确的导出格式
   window.{ModuleName}Skills = { SkillClass };
   ```

### 🚫 已废弃的功能

- ❌ 别名映射系统
- ❌ 特殊判断逻辑  
- ❌ 复杂的技能名称处理
- ❌ 多处重复的映射配置

### 🔍 调试和测试

#### 检查技能配置
```javascript
// 在控制台中检查配置
console.log(window.SkillConfig.getAllSkillNames());
console.log(window.SkillConfig.getSkillConfig('火流星'));
```

#### 技能开发检查清单
- [ ] 类名使用英文
- [ ] 在 `skill-config.js` 中添加配置
- [ ] 创建对应的CSS文件
- [ ] 正确导出技能类
- [ ] 数据库 `animation_model` 字段配置正确
- [ ] 通过实际战斗测试验证

## 📋 常见问题解决

### Q: 技能找不到对应的类
**A**: 检查类导出是否正确，确保英文类名与配置中的 `class` 字段一致

### Q: 技能动画不显示
**A**: 检查CSS文件是否正确加载，确保动画容器和样式类名一致

### Q: 数据库配置无效
**A**: 确保 `animation_model` 字段值与 `skill-config.js` 中的映射键完全一致

---

*文档更新日期: 2024年12月*  
*当前版本: v3.0 - 统一配置系统* 