<?php
require_once __DIR__ . '/../includes/functions.php';

// 检查维护模式
if (isMaintenanceMode()) {
    echo json_encode([
        'success' => false,
        'message' => '游戏正在维护中，请稍后再试',
        'maintenance' => true
    ]);
    exit;
}

// 记录API调用（如果开启了调试）
if (DEBUG_LOG_API_CALLS) {
    writeLog("API调用: login.php", 'DEBUG', 'api.log');
}

setJsonResponse();

// 设置调试日志（根据配置）
if (isDebugMode()) {
    ini_set('display_errors', 1);
    ini_set('log_errors', 1);
    error_log("登录请求开始处理，时间：" . date('Y-m-d H:i:s'));
}

// 测试数据库连接
try {
    $pdo = getDatabase();
    error_log("数据库连接成功");
    
    // 检查数据库版本
    $tablesQuery = "SHOW TABLES";
    $stmt = $pdo->query($tablesQuery);
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    // 日志记录数据库表情况
    error_log("数据库表数量: " . count($tables));
    error_log("数据库表列表: " . implode(", ", $tables));
    
    // 检查users表结构
    $columnsQuery = "SHOW COLUMNS FROM users";
    $stmt = $pdo->query($columnsQuery);
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    error_log("users表字段: " . implode(", ", $columns));
    
} catch (Exception $e) {
    error_log("数据库连接测试失败：" . $e->getMessage());
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '请求方法不允许']);
    exit;
}

// 获取POST数据
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    $input = $_POST;
}

$username = trim(isset($input['username']) ? $input['username'] : '');
$password = isset($input['password']) ? $input['password'] : '';
$csrfToken = isset($input['csrf_token']) ? $input['csrf_token'] : '';

error_log("收到登录请求 - 用户名: " . $username . ", CSRF令牌长度: " . strlen($csrfToken));

// 验证CSRF令牌
if (!validateCSRFToken($csrfToken)) {
    error_log("CSRF令牌验证失败: " . $csrfToken);
    error_log("当前会话ID: " . session_id());
    error_log("当前会话状态: " . (session_status() == PHP_SESSION_ACTIVE ? '活跃' : '未活跃'));
    echo json_encode([
        'success' => false, 
        'message' => 'CSRF令牌验证失败，请刷新页面重试',
        'debug' => [
            'session_id' => session_id(),
            'csrf_token_length' => strlen($csrfToken),
            'csrf_token_prefix' => substr($csrfToken, 0, 10) . '...',
            'has_session_token' => isset($_SESSION['csrf_token'])
        ]
    ]);
    exit;
}

// 验证输入数据
if (empty($username)) {
    echo json_encode(['success' => false, 'message' => '用户名不能为空']);
    exit;
}

if (empty($password)) {
    echo json_encode(['success' => false, 'message' => '密码不能为空']);
    exit;
}

// 尝试登录
try {
    // 使用已存在的loginUser函数处理登录
    $result = loginUser($username, $password);
    
    // 详细记录登录结果
    if ($result['success']) {
        error_log("登录成功 - 用户ID: " . $result['user']['id'] . ", 用户名: " . $result['user']['username']);
        
        // 检查用户是否已经有角色
        $userId = $result['user']['id'];
        $pdo = getDatabase();
        $stmt = $pdo->prepare("SELECT id FROM characters WHERE user_id = ? LIMIT 1");
        $stmt->execute([$userId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // 如果没有角色，在会话中设置need_create_character标志
        if (!$character) {
            $_SESSION['need_create_character'] = true;
            $result['need_create_character'] = true;
            $result['redirect'] = 'character_creation.html';
            if (isDebugMode()) {
                error_log("用户没有角色，已设置need_create_character标志 = true");
            }
        } else {
            // 确保角色信息已存储在会话中
            $charDetailStmt = $pdo->prepare("
                SELECT c.id, c.character_name, c.avatar_image, c.realm_id,
                       r.realm_name, r.realm_level
                FROM characters c
                LEFT JOIN realm_levels r ON c.realm_id = r.id
                WHERE c.id = ?
            ");
            $charDetailStmt->execute([$character['id']]);
            $charDetail = $charDetailStmt->fetch(PDO::FETCH_ASSOC);
            
            if ($charDetail) {
                $_SESSION['character_id'] = $charDetail['id'];
                $_SESSION['character_name'] = $charDetail['character_name'];
                $_SESSION['character_avatar'] = $charDetail['avatar_image'];
                $_SESSION['realm_id'] = $charDetail['realm_id'];
                $_SESSION['realm_name'] = $charDetail['realm_name'];
                $_SESSION['realm_level'] = $charDetail['realm_level'];
                
                if (isDebugMode()) {
                    error_log("已将角色信息存储在会话中 - 角色ID: " . $charDetail['id']);
                }
                
                // 移除need_create_character标志（如果有）
                if (isset($_SESSION['need_create_character'])) {
                    unset($_SESSION['need_create_character']);
                    if (isDebugMode()) {
                        error_log("已移除need_create_character标志");
                    }
                }
            }
        }
        
        // 记录登录成功日志
        try {
            $loginLogQuery = "INSERT INTO login_logs (user_id, username, login_type, ip_address, user_agent, created_at) VALUES (?, ?, 'success', ?, ?, NOW())";
            $stmt = $pdo->prepare($loginLogQuery);
            $ipAddress = isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : 'unknown';
            $userAgent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : 'unknown';
            $stmt->execute([
                $result['user']['id'],
                $result['user']['username'],
                $ipAddress,
                $userAgent
            ]);
            error_log("登录日志记录成功");
        } catch (Exception $e) {
            error_log("登录日志记录失败: " . $e->getMessage());
        }
    } else {
        $errorMessage = isset($result['message']) ? $result['message'] : '未知错误';
        error_log("登录失败 - 原因: " . $errorMessage);
        
        // 记录登录失败日志
        try {
            $loginLogQuery = "INSERT INTO login_logs (username, login_type, fail_reason, ip_address, user_agent, created_at) VALUES (?, 'failed', ?, ?, ?, NOW())";
            $stmt = $pdo->prepare($loginLogQuery);
            $ipAddress = isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : 'unknown';
            $userAgent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : 'unknown';
            $stmt->execute([
                $username,
                $errorMessage,
                $ipAddress,
                $userAgent
            ]);
            error_log("登录失败日志记录成功");
        } catch (Exception $e) {
            error_log("登录失败日志记录失败: " . $e->getMessage());
        }
    }
    
    error_log("登录结果: " . json_encode($result));
    echo json_encode($result);
} catch (Exception $e) {
    error_log("登录过程中发生异常: " . $e->getMessage());
    error_log("错误堆栈: " . $e->getTraceAsString());
    echo json_encode(['success' => false, 'message' => '登录过程中发生错误: ' . $e->getMessage()]);
}
?> 