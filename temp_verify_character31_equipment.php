<?php
/**
 * 验证角色ID=31的套装装备添加情况
 */

require_once 'src/includes/db_connection.php';

try {
    $db = getDbConnection();
    $characterId = 31;
    
    echo "=== 验证角色ID={$characterId}的套装装备 ===\n\n";
    
    // 1. 验证角色信息
    $stmt = $db->prepare("SELECT id, character_name, user_id FROM characters WHERE id = ?");
    $stmt->execute([$characterId]);
    $character = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$character) {
        echo "错误: 未找到角色ID={$characterId}\n";
        exit;
    }
    
    echo "角色信息: {$character['character_name']} (用户ID: {$character['user_id']})\n\n";
    
    // 2. 统计背包中的套装装备
    $stmt = $db->prepare("
        SELECT 
            gis.set_name,
            gis.id as set_id,
            COUNT(ci.id) as item_count,
            GROUP_CONCAT(gi.slot_type) as slots,
            GROUP_CONCAT(gi.item_name SEPARATOR ' | ') as item_names
        FROM character_inventory ci
        JOIN game_items gi ON ci.item_id = gi.id
        JOIN game_item_sets gis ON gi.set_id = gis.id
        WHERE ci.character_id = ?
        GROUP BY gis.id, gis.set_name
        ORDER BY gis.set_name
    ");
    $stmt->execute([$characterId]);
    $setInventory = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($setInventory)) {
        echo "背包中没有找到套装装备\n";
        exit;
    }
    
    echo "背包中的套装装备统计:\n";
    echo "套装总数: " . count($setInventory) . "\n\n";
    
    $totalItems = 0;
    $completeSets = 0;
    
    foreach ($setInventory as $set) {
        $isComplete = $set['item_count'] >= 6;
        $status = $isComplete ? "✅ 完整" : "❌ 不完整";
        
        echo "套装: {$set['set_name']} (ID: {$set['set_id']})\n";
        echo "  装备数量: {$set['item_count']}/6 {$status}\n";
        echo "  槽位: {$set['slots']}\n";
        echo "  装备名称: {$set['item_names']}\n\n";
        
        $totalItems += $set['item_count'];
        if ($isComplete) $completeSets++;
    }
    
    echo "=== 统计总结 ===\n";
    echo "套装总数: " . count($setInventory) . "\n";
    echo "完整套装: {$completeSets}\n";
    echo "装备总数: {$totalItems}\n";
    
    // 3. 检查套装特殊效果
    echo "\n=== 套装特殊效果验证 ===\n";
    
    $stmt = $db->prepare("
        SELECT set_name, effects 
        FROM game_item_sets 
        WHERE id IN (
            SELECT DISTINCT gi.set_id 
            FROM character_inventory ci
            JOIN game_items gi ON ci.item_id = gi.id
            WHERE ci.character_id = ? AND gi.set_id IS NOT NULL
        )
        LIMIT 5
    ");
    $stmt->execute([$characterId]);
    $sampleSets = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($sampleSets as $set) {
        echo "套装: {$set['set_name']}\n";
        $effects = json_decode($set['effects'], true);
        
        if ($effects) {
            foreach (['two_piece', 'four_piece', 'six_piece'] as $pieceCount) {
                if (isset($effects[$pieceCount]['special_effect'])) {
                    echo "  {$pieceCount}: {$effects[$pieceCount]['special_effect']}\n";
                }
            }
        }
        echo "\n";
    }
    
    // 4. 测试装备功能
    echo "=== 装备测试建议 ===\n";
    echo "1. 进入装备界面，选择任意一套完整的6件套装备\n";
    echo "2. 进入竞技场，匹配对手进行PVP战斗\n";
    echo "3. 观察战斗日志中的套装特殊效果触发情况\n";
    echo "4. 检查浏览器控制台的详细套装效果日志\n\n";
    
    if ($completeSets >= 5) {
        echo "✅ 测试数据充足！角色拥有{$completeSets}套完整套装，可以进行全面的PVP套装特殊效果测试。\n";
    } else {
        echo "⚠️ 建议增加更多完整套装以进行充分测试。\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
?>
