<?php
/**
 * 批量更新所有套装特殊效果
 * 将不符合回合制的效果统一替换为合理效果
 */

try {
    $pdo = new PDO('mysql:host=127.0.0.1;port=3306;dbname=yn_game;charset=utf8mb4', 'root', 'mjlxz159');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== 开始批量更新套装特殊效果 ===\n\n";
    
    // 新的回合制特殊效果池
    $newEffects = [
        // 4件套效果池 - 偏向控制和辅助
        'four_piece_effects' => [
            '攻击时有15%概率使敌人眩晕1回合',
            '攻击时有20%概率降低敌人30%攻击力持续2回合', 
            '受到攻击时有25%概率减少50%伤害',
            '攻击时有18%概率降低敌人25%防御力持续3回合',
            '每回合结束时恢复最大生命值的8%',
            '受到攻击时有20%概率反弹25%伤害',
            '攻击时有12%概率使敌人无法使用特殊技能1回合',
            '生命值低于40%时受到伤害减少30%'
        ],
        
        // 6件套效果池 - 偏向高伤害和强力效果
        'six_piece_effects' => [
            '攻击时有20%概率造成250%伤害',
            '攻击时有25%概率无视敌人60%防御',
            '攻击造成伤害的35%转化为生命值',
            '攻击时有15%概率连续攻击2次',
            '战斗开始时获得最大生命值25%的护盾',
            '攻击时有8%概率造成350%伤害',
            '生命值低于30%时攻击力提升60%',
            '死亡时有25%概率恢复60%生命值复活(每场战斗限1次)'
        ]
    ];
    
    // 获取所有套装
    $stmt = $pdo->query("SELECT id, set_name, effects FROM game_item_sets ORDER BY id");
    $sets = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $updateCount = 0;
    $fourPieceIndex = 0;
    $sixPieceIndex = 0;
    
    foreach ($sets as $set) {
        $setName = $set['set_name'];
        $currentEffects = json_decode($set['effects'], true);
        
        if (!$currentEffects) {
            echo "⚠️ 跳过 {$setName}: 无效的effects JSON\n";
            continue;
        }
        
        $updated = false;
        
        // 更新4件套特殊效果
        if (isset($currentEffects['four_piece'])) {
            $newFourEffect = $newEffects['four_piece_effects'][$fourPieceIndex % count($newEffects['four_piece_effects'])];
            $currentEffects['four_piece']['special_effect'] = $newFourEffect;
            $updated = true;
            echo "✅ 更新 {$setName} 4件套: {$newFourEffect}\n";
            $fourPieceIndex++;
        }
        
        // 更新6件套特殊效果
        if (isset($currentEffects['six_piece'])) {
            $newSixEffect = $newEffects['six_piece_effects'][$sixPieceIndex % count($newEffects['six_piece_effects'])];
            $currentEffects['six_piece']['special_effect'] = $newSixEffect;
            $updated = true;
            echo "✅ 更新 {$setName} 6件套: {$newSixEffect}\n";
            $sixPieceIndex++;
        }
        
        // 如果有更新，保存到数据库
        if ($updated) {
            $newEffectsJson = json_encode($currentEffects, JSON_UNESCAPED_UNICODE);
            $updateStmt = $pdo->prepare("UPDATE game_item_sets SET effects = ? WHERE id = ?");
            $updateStmt->execute([$newEffectsJson, $set['id']]);
            $updateCount++;
            echo "💾 已保存 {$setName} 到数据库\n\n";
        }
    }
    
    echo "=== 批量更新完成 ===\n";
    echo "总共更新了 {$updateCount} 个套装的特殊效果\n\n";
    
    // 验证更新结果 - 随机检查几个套装
    echo "=== 验证更新结果 (随机抽样) ===\n";
    $verifyStmt = $pdo->query("SELECT set_name, effects FROM game_item_sets ORDER BY RAND() LIMIT 5");
    $verifyResults = $verifyStmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($verifyResults as $result) {
        $effects = json_decode($result['effects'], true);
        echo "【{$result['set_name']}】验证:\n";
        
        if (isset($effects['four_piece']['special_effect'])) {
            echo "  4件套: {$effects['four_piece']['special_effect']}\n";
        }
        if (isset($effects['six_piece']['special_effect'])) {
            echo "  6件套: {$effects['six_piece']['special_effect']}\n";
        }
        echo "\n";
    }
    
    echo "=== 新效果特点 ===\n";
    echo "✅ 所有时间单位改为'回合'\n";
    echo "✅ 移除'普通攻击'概念\n";  
    echo "✅ 简化触发机制\n";
    echo "✅ 平衡伤害数值\n";
    echo "✅ 增加多样化效果类型\n";
    echo "✅ 符合回合制战斗逻辑\n";
    
} catch (Exception $e) {
    echo '错误: ' . $e->getMessage() . "\n";
}
?>
