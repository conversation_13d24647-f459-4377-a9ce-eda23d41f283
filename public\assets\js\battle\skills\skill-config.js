/**
 * 技能配置统一管理
 * 包含技能映射、动画模型映射等所有配置
 */

class SkillConfig {
    constructor() {
        // 技能名称映射表 - 从animation_model映射到统一的技能名称
        // 这里映射到我们的技能动画模块使用的标准技能名称
        this.animationModelMapping = {
            'feijian': '剑气外放！',
            'hengzhan': '横斩',          
            'wanjianjue': '万剑诀', 
            'jujian': '巨剑术',
            'zhangxinlei': '掌心雷',       
            'leijian': '雷剑',             
            'huoqiushu': '火球术',
            'huoliuxing': '火流星',        
            'tengman': '藤蔓',
            'shuilongjuan': '水龙卷', 
            'yanshituci': '岩石突刺',
            'jinzhenbayu': '金针暴雨',
            'bingzhuishu': '冰锥术',
            'xuanbingjian': '玄冰剑',      
            'fengrensu': '风刃速',
            'youlong': '游龙剑',
            'huichunjian': '回春剑',
            'normal': '普通攻击'
        };

        // 技能实现映射表 - 从技能名称映射到实现配置
        this.skillImplementationMapping = {
            // 剑类技能 - 独立模块
            '剑气外放！': { 
                module: 'feijian-skill', 
                class: 'FeiJianSkill', 
                css: 'feijian-animations',
                animationModel: 'feijian'
            },
            '万剑诀': { 
                module: 'wanjianjue-skill', 
                class: 'WanJianJueSkill', 
                css: 'wanjianjue-animations',
                animationModel: 'wanjianjue'
            },
            '巨剑术': { 
                module: 'jujian-skill', 
                class: 'JuJianSkill', 
                css: 'jujian-animations',
                animationModel: 'jujian'
            },
            '游龙剑': { 
                module: 'youlong-skill', 
                class: 'YouLongJianSkill', 
                css: 'youlong-animations',
                animationModel: 'youlong'
            },

            // 雷法技能
            '掌心雷': { 
                module: 'lightning-skills', 
                class: 'ZhangXinLeiSkill', 
                css: 'lightning-animations',
                animationModel: 'zhangxinlei'
            },
            '雷剑': { 
                module: 'lightning-skills', 
                class: 'LeiJianSkill', 
                css: 'lightning-animations',
                animationModel: 'leijian'
            },

            // 火法技能
            '火球术': { 
                module: 'fire-skills', 
                class: 'HuoQiuShuSkill', 
                css: 'fire-animations',
                animationModel: 'huoqiushu'
            },
            '火流星': { 
                module: 'fire-skills', 
                class: 'HuoLiuXingSkill', 
                css: 'huoliuxing-animations',
                animationModel: 'huoliuxing'
            },

            // 木系技能
            '藤蔓': { 
                module: 'wood-skills', 
                class: 'TengManSkill', 
                css: 'wood-animations',
                animationModel: 'tengman'
            },
            '藤蔓缠绕': { 
                module: 'wood-skills', 
                class: 'TengManSkill', 
                css: 'wood-animations',
                animationModel: 'tengman'
            },
            '回春剑': { 
                module: 'huichunjian-skill', 
                class: 'HuiChunJianSkill', 
                css: 'huichunjian-animations',
                animationModel: 'huichunjian'
            },

            // 水系技能
            '水龙卷': { 
                module: 'water-skills', 
                class: 'ShuiLongJuanSkill', 
                css: 'water-animations',
                animationModel: 'shuilongjuan'
            },

            // 土系技能
            '岩石突刺': { 
                module: 'earth-skills', 
                class: 'YanShiTuCiSkill', 
                css: 'earth-animations',
                animationModel: 'yanshituci'
            },

            // 金系技能
            '金针暴雨': { 
                module: 'metal-skills', 
                class: 'JinZhenBaYuSkill', 
                css: 'metal-animations',
                animationModel: 'jinzhenbayu'
            },

            // 冰系技能
            '冰锥术': { 
                module: 'ice-skills', 
                class: 'BingZhuiShuSkill', 
                css: 'ice-animations',
                animationModel: 'bingzhuishu'
            },
            '玄冰剑': { 
                module: 'ice-skills', 
                class: 'XuanBingJianSkill', 
                css: 'ice-animations',
                animationModel: 'xuanbingjian'
            },

            // 风系技能
            '风刃术': { 
                module: 'wind-skills', 
                class: 'FengRenSuSkill', 
                css: 'wind-animations',
                animationModel: 'fengrensu'
            },
            '风刃速': { 
                module: 'wind-skills', 
                class: 'FengRenSuSkill', 
                css: 'wind-animations',
                animationModel: 'fengrensu'
            },

            // 独立技能文件
            '横斩': { 
                module: 'hengzhan-skill', 
                class: 'HengZhanSkill', 
                css: 'hengzhan-animations',
                animationModel: 'hengzhan'
            },

            // 基础攻击技能
            '普通攻击': { 
                module: 'normal-attack-skill', 
                class: 'NormalAttackSkill', 
                css: 'normal-attack-animations',
                animationModel: 'normal'
            }
        };

        // 技能别名映射已删除 - 不需要复杂的别名处理
    }

    /**
     * 根据animation_model获取技能名称
     * @param {string} animationModel 动画模型
     * @returns {string} 技能名称
     */
    getSkillNameByAnimationModel(animationModel) {
                    return this.animationModelMapping[animationModel] || '剑气外放！';
    }

    /**
     * 根据技能名称获取实现配置
     * @param {string} skillName 技能名称
     * @returns {Object|null} 技能配置
     */
    getSkillConfig(skillName) {
        return this.skillImplementationMapping[skillName] || null;
    }

    /**
     * 获取所有支持的animation_model
     * @returns {Array} animation_model列表
     */
    getAllAnimationModels() {
        return Object.keys(this.animationModelMapping);
    }

    /**
     * 获取所有支持的技能名称
     * @returns {Array} 技能名称列表
     */
    getAllSkillNames() {
        return Object.keys(this.skillImplementationMapping);
    }

    /**
     * 检查技能是否支持
     * @param {string} skillName 技能名称
     * @returns {boolean} 是否支持
     */
    isSkillSupported(skillName) {
        return this.skillImplementationMapping.hasOwnProperty(skillName);
    }

    /**
     * 获取技能的animation_model
     * @param {string} skillName 技能名称
     * @returns {string|null} animation_model
     */
    getAnimationModelBySkillName(skillName) {
        const config = this.getSkillConfig(skillName);
        return config ? config.animationModel : null;
    }

    /**
     * 添加新的技能配置
     * @param {string} skillName 技能名称
     * @param {Object} config 技能配置
     * @param {string} animationModel 动画模型
     */
    addSkillConfig(skillName, config, animationModel) {
        this.skillImplementationMapping[skillName] = {
            ...config,
            animationModel: animationModel
        };
        this.animationModelMapping[animationModel] = skillName;
    }


}

// 创建全局实例
window.SkillConfig = new SkillConfig();

console.log('✅ 技能配置统一管理器已加载'); 