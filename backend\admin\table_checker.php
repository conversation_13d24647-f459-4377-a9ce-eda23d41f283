<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>表名检测工具</title>
    <style>
        body { font-family: 'Microsoft YaHei', sans-serif; margin: 20px; }
        .table-list { border: 1px solid #ddd; padding: 15px; margin: 10px 0; }
        .table-name { color: #1890ff; font-weight: bold; }
        .suggested-mapping { background: #f0f9ff; padding: 10px; margin: 5px 0; border-left: 3px solid #1890ff; }
    </style>
</head>
<body>
    <h2>📊 数据库表名检测工具</h2>
    
    <?php
    // 使用主游戏数据库配置
    $db_config = [
        'host' => 'localhost',
        'username' => 'ynxx',
        'password' => 'mjlxz159',
        'database' => 'yn_game'
    ];

    try {
        $conn = new PDO(
            "mysql:host={$db_config['host']};dbname={$db_config['database']};charset=utf8mb4",
            $db_config['username'],
            $db_config['password']
        );
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        $stmt = $conn->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);

        echo "<div class='table-list'>";
        echo "<h3>🗃️ 数据库中的表 (" . count($tables) . "个)：</h3>";
        foreach ($tables as $table) {
            echo "<span class='table-name'>• {$table}</span><br>";
        }
        echo "</div>";

        // 建议的管理功能映射
        $suggestions = [
            '用户管理' => ['users', 'user', 'player', 'players'],
            '物品管理' => ['items', 'game_items', 'item'],
            '装备管理' => ['equipment', 'equipments', 'gear'],
            '技能管理' => ['skills', 'skill', 'abilities'],
            '宠物管理' => ['pets', 'pet', 'companions'],
            '地图管理' => ['maps', 'map', 'areas'],
            '怪物管理' => ['monsters', 'monster', 'enemies'],
            '掉落管理' => ['drops', 'drop', 'loot'],
            '任务管理' => ['quests', 'quest', 'missions'],
            '商店管理' => ['shops', 'shop', 'store'],
            '背包管理' => ['inventory', 'bag'],
            '用户属性' => ['user_stats', 'player_stats', 'stats'],
            '战斗日志' => ['battle_logs', 'combat_logs', 'logs']
        ];

        echo "<h3>💡 建议的管理功能映射：</h3>";
        foreach ($suggestions as $function => $possibleNames) {
            $found = false;
            foreach ($possibleNames as $name) {
                if (in_array($name, $tables)) {
                    echo "<div class='suggested-mapping'>";
                    echo "<strong>{$function}</strong> → 可以使用表: <span class='table-name'>{$name}</span>";
                    echo "</div>";
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                echo "<div style='color: #999; margin: 5px 0;'>";
                echo "<strong>{$function}</strong> → 未找到对应的表";
                echo "</div>";
            }
        }

    } catch(PDOException $e) {
        echo "<div style='color: red;'>连接错误: " . $e->getMessage() . "</div>";
    }
    ?>
    
    <div style="margin-top: 30px; padding: 15px; background: #fff3cd; border-left: 3px solid #ffc107;">
        <h4>🔧 使用说明：</h4>
        <p>1. 查看上面列出的实际表名</p>
        <p>2. 告诉我哪些表名需要修正</p>
        <p>3. 我会更新管理后台的表名映射</p>
    </div>
</body>
</html> 