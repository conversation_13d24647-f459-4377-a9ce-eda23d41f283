<?php
session_start();

echo "=== 检查套装表结构 ===\n";

// 数据库连接
$host = 'localhost';
$dbname = 'yn_game';
$username = 'ynxx';
$password = 'mjlxz159';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ 数据库连接成功\n";
} catch (PDOException $e) {
    die("❌ 数据库连接失败: " . $e->getMessage() . "\n");
}

// 检查套装表结构
echo "\n=== 套装表结构 ===\n";
$stmt = $pdo->query("DESCRIBE game_item_sets");
$columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "game_item_sets表字段:\n";
foreach ($columns as $column) {
    echo "- {$column['Field']} ({$column['Type']}) {$column['Null']} {$column['Key']}\n";
}

// 查看套装数据示例
echo "\n=== 套装数据示例 ===\n";
$stmt = $pdo->query("SELECT * FROM game_item_sets LIMIT 3");
$sets = $stmt->fetchAll(PDO::FETCH_ASSOC);

foreach ($sets as $set) {
    echo "\n套装: " . ($set['set_name'] ?? 'N/A') . "\n";
    foreach ($set as $key => $value) {
        echo "  {$key}: " . (strlen($value) > 100 ? substr($value, 0, 100) . '...' : $value) . "\n";
    }
}

// 检查是否有星衣道尊套装
echo "\n=== 查找星衣道尊套装 ===\n";
$stmt = $pdo->query("SELECT * FROM game_item_sets WHERE set_name LIKE '%星衣%' OR set_name LIKE '%道尊%'");
$starSets = $stmt->fetchAll(PDO::FETCH_ASSOC);

if (count($starSets) > 0) {
    foreach ($starSets as $set) {
        echo "找到套装: {$set['set_name']}\n";
        foreach ($set as $key => $value) {
            if (strpos($key, 'effect') !== false || strpos($key, '效果') !== false) {
                echo "  {$key}: {$value}\n";
            }
        }
    }
} else {
    echo "❌ 没有找到星衣道尊相关套装\n";
}

// 查找包含护盾效果的套装
echo "\n=== 查找护盾效果套装 ===\n";
$stmt = $pdo->query("SELECT * FROM game_item_sets");
$allSets = $stmt->fetchAll(PDO::FETCH_ASSOC);

$shieldSets = [];
foreach ($allSets as $set) {
    foreach ($set as $key => $value) {
        if (strpos($value, '护盾') !== false) {
            $shieldSets[] = [
                'set_name' => $set['set_name'],
                'field' => $key,
                'effect' => $value
            ];
            break;
        }
    }
}

if (count($shieldSets) > 0) {
    echo "找到包含护盾效果的套装:\n";
    foreach ($shieldSets as $shield) {
        echo "- {$shield['set_name']} ({$shield['field']}): {$shield['effect']}\n";
    }
} else {
    echo "❌ 没有找到包含护盾效果的套装\n";
}

echo "\n=== 检查完成 ===\n";
?>
