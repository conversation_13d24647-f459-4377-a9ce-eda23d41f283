<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <!-- 移动端适配核心meta标签 -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <!-- 🔧 PWA模式底部白边强力修复 - 必须最早执行 -->
    <script src="assets/js/pwa-fix.js"></script>

    <!-- 🔧 项目配置文件 - 必须早期加载 -->
    <script src="assets/js/config.js"></script>

    <!-- 新增HBuilder X优化meta标签 -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="format-detection" content="telephone=no">
    <!-- PWA支持 -->
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#d4af37">
        
    <title>登录 - 一念修仙</title>
    <link rel="stylesheet" href="assets/css/global.css">
    <link rel="stylesheet" href="assets/css/login.css">
    <!-- 🔧 视口高度修复脚本 -->
    <script src="assets/js/viewport-height-fix.js"></script>

</head>
<body>
    <div class="login-container">
        <h1 class="login-title">用户登录</h1>
        
        <div id="message" class="message" style="display: none;"></div>
        <div id="loading" class="loading">登录中...</div>
        <div id="csrfError" class="message error" style="display: none;">CSRF令牌获取失败</div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username" class="form-label">用户名/邮箱</label>
                <input type="text" id="username" name="username" class="form-input" placeholder="请输入用户名或邮箱" required>
            </div>
            
            <div class="form-group">
                <label for="password" class="form-label">密码</label>
                <input type="password" id="password" name="password" class="form-input" placeholder="请输入密码" required>
            </div>
            
            <!-- 🆕 添加记住密码选项 -->
            <div class="form-group" style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 25px;">
                <div style="display: flex; align-items: center;">
                    <input type="checkbox" id="remember_credentials">
                    <label for="remember_credentials" class="form-label" style="margin-bottom: 0; font-size: 14px; cursor: pointer;">记住密码</label>
                </div>
                <button type="button" id="clear_credentials" class="clear-credentials-btn" title="清除保存的登录信息">
                    🗑️ 清除
                </button>
            </div>
            
            <button type="submit" id="loginBtn" class="login-button">登录</button>
        </form>        
        
        <div class="links">
            <a href="register.html">还没有账号？点击注册</a>
            <a href="index.html">返回主页</a>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('loginForm');
            const messageDiv = document.getElementById('message');
            const loadingDiv = document.getElementById('loading');
            const loginBtn = document.getElementById('loginBtn');
            const usernameInput = document.getElementById('username');
            const passwordInput = document.getElementById('password');
            const rememberCheckbox = document.getElementById('remember_credentials');
            const clearCredentialsBtn = document.getElementById('clear_credentials');
            
            // 🆕 简单的加密/解密函数（用于本地存储密码）
            function simpleEncrypt(text) {
                return btoa(encodeURIComponent(text)).split('').reverse().join('');
            }
            
            function simpleDecrypt(encryptedText) {
                try {
                    return decodeURIComponent(atob(encryptedText.split('').reverse().join('')));
                } catch (e) {
                    return '';
                }
            }
            
            // 🆕 自动填充保存的凭据
            function loadSavedCredentials() {
                const savedUsername = localStorage.getItem('saved_username');
                const savedPassword = localStorage.getItem('saved_password');
                const rememberEnabled = localStorage.getItem('remember_credentials') === 'true';
                
                if (savedUsername) {
                    usernameInput.value = savedUsername;
                    usernameInput.classList.add('auto-filled');
                    console.log('🔑 已自动填充用户名:', savedUsername);
                }
                
                if (savedPassword && rememberEnabled) {
                    const decryptedPassword = simpleDecrypt(savedPassword);
                    if (decryptedPassword) {
                        passwordInput.value = decryptedPassword;
                        passwordInput.classList.add('auto-filled');
                        console.log('🔐 已自动填充密码');
                    }
                }
                
                if (rememberEnabled) {
                    rememberCheckbox.checked = true;
                }
                
                // 如果用户名和密码都已填充，显示欢迎消息并聚焦到登录按钮
                if (savedUsername && savedPassword && rememberEnabled) {
                    showMessage(`欢迎回来，${savedUsername}！`, 'success');
                    setTimeout(() => {
                        hideMessage();
                    }, 4000);
                    loginBtn.focus(); // 聚焦到登录按钮，方便用户按回车登录
                } else if (savedUsername) {
                    showMessage(`欢迎回来，${savedUsername}！`, 'success');
                    setTimeout(() => {
                        hideMessage();
                    }, 3000);
                    passwordInput.focus(); // 聚焦到密码输入框
                }
            }
            
            // 🆕 保存用户凭据
            function saveCredentials(username, password) {
                if (rememberCheckbox.checked) {
                    localStorage.setItem('saved_username', username);
                    localStorage.setItem('saved_password', simpleEncrypt(password));
                    localStorage.setItem('remember_credentials', 'true');
                    console.log('🔐 已保存用户凭据');
                } else {
                    // 如果未勾选记住密码，只保存用户名
                    localStorage.setItem('saved_username', username);
                    localStorage.removeItem('saved_password');
                    localStorage.setItem('remember_credentials', 'false');
                    console.log('👤 已保存用户名，未保存密码');
                }
            }
            
            // 🆕 监听输入框变化，移除自动填充样式
            usernameInput.addEventListener('input', function() {
                const savedUsername = localStorage.getItem('saved_username');
                if (this.value !== savedUsername) {
                    this.classList.remove('auto-filled');
                } else if (savedUsername) {
                    this.classList.add('auto-filled');
                }
            });
            
            passwordInput.addEventListener('input', function() {
                const savedPassword = localStorage.getItem('saved_password');
                const rememberEnabled = localStorage.getItem('remember_credentials') === 'true';
                if (savedPassword && rememberEnabled) {
                    const decryptedPassword = simpleDecrypt(savedPassword);
                    if (this.value !== decryptedPassword) {
                        this.classList.remove('auto-filled');
                    } else {
                        this.classList.add('auto-filled');
                    }
                } else {
                    this.classList.remove('auto-filled');
                }
            });
            
            // 🆕 记住密码复选框变化时的处理
            rememberCheckbox.addEventListener('change', function() {
                if (!this.checked) {
                    // 如果取消勾选，清除保存的密码
                    localStorage.removeItem('saved_password');
                    localStorage.setItem('remember_credentials', 'false');
                    passwordInput.classList.remove('auto-filled');
                    console.log('🚫 已清除保存的密码');
                }
            });
            
            // 🆕 页面加载时自动填充凭据
            loadSavedCredentials();
            
            // 🔍 检查URL参数中是否有用户名（保持原有功能）
            const urlParams = new URLSearchParams(window.location.search);
            const preFilledUsername = urlParams.get('username');
            if (preFilledUsername && !localStorage.getItem('saved_username')) {
                usernameInput.value = preFilledUsername;
                usernameInput.classList.add('auto-filled');
                passwordInput.focus();
                showMessage(`欢迎回来，${preFilledUsername}！请输入密码完成登录`, 'success');
                setTimeout(() => {
                    hideMessage();
                }, 3000);
            }
            
            // 获取CSRF令牌
            let csrfToken = '';
            getCsrfToken();
            
            function getCsrfToken() {
                fetch(window.GameConfig ? window.GameConfig.getApiUrl('csrf.php') : '../src/api/csrf.php')
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('CSRF令牌请求失败: ' + response.status);
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (!data.token) {
                            throw new Error('CSRF令牌无效');
                        }
                        csrfToken = data.token;
                        console.log('CSRF令牌获取成功，长度: ' + csrfToken.length);
                        
                        // 显示成功信息
                        document.getElementById('csrfError').style.display = 'none';
                    })
                    .catch(err => {
                        console.error('获取CSRF令牌失败:', err);
                        document.getElementById('csrfError').style.display = 'block';
                        document.getElementById('csrfError').textContent = '系统初始化失败，请刷新页面重试: ' + err.message;
                    });
            }
            
            function showMessage(text, type) {
                messageDiv.textContent = text;
                messageDiv.className = `message ${type}`;
                messageDiv.style.display = 'block';
                console.log(`显示消息: ${type} - ${text}`);
            }
            
            function hideMessage() {
                messageDiv.style.display = 'none';
            }
            
            function showLoading() {
                loadingDiv.style.display = 'block';
                loginBtn.disabled = true;
            }
            
            function hideLoading() {
                loadingDiv.style.display = 'none';
                loginBtn.disabled = false;
            }
            
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                if (!csrfToken) {
                    showMessage('系统未准备就绪，请刷新页面重试', 'error');
                    document.getElementById('csrfError').style.display = 'block';
                    document.getElementById('csrfError').textContent = 'CSRF令牌未获取，请刷新页面';
                    return;
                }
                
                hideMessage();
                
                const username = document.getElementById('username').value.trim();
                const password = document.getElementById('password').value;
                
                const data = {
                    username: username,
                    password: password,
                    csrf_token: csrfToken
                };
                
                console.log('准备发送登录请求:');
                console.log('- 用户名: ' + data.username);
                console.log('- CSRF令牌长度: ' + data.csrf_token.length);
                console.log('- CSRF令牌前10位: ' + data.csrf_token.substring(0, 10) + '...');
                
                showLoading();

                fetch(window.GameConfig ? window.GameConfig.getApiUrl('login.php') : '../src/api/login.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data),
                    credentials: 'same-origin'
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('HTTP错误: ' + response.status);
                    }
                    return response.json();
                })
                .then(result => {
                    hideLoading();
                    console.log('登录响应:', result);
                    
                    if (result.success) {
                        // 🆕 登录成功后保存用户凭据
                        saveCredentials(username, password);
                        
                        if (result.need_create_character) {
                            showMessage('登录成功！正在跳转到角色创建...', 'success');
                            console.log('需要创建角色，跳转到:', result.redirect);
                            setTimeout(() => {
                                window.location.href = result.redirect || 'character_creation.html';
                            }, 1000);
                        } else {
                            // 保存角色信息到本地存储以便其他页面使用
                            if (result.user && result.user.character_name) {
                                localStorage.setItem('character_name', result.user.character_name);
                                localStorage.setItem('character_id', result.user.character_id);
                                if (result.user.realm_name) {
                                    localStorage.setItem('realm_name', result.user.realm_name);
                                    localStorage.setItem('realm_level', result.user.realm_level);
                                }
                            }
                            
                            // 🌙 设置刚登录标记，用于控制离线收益弹窗显示
                            localStorage.setItem('just_logged_in', 'true');
                            localStorage.setItem('login_time', Date.now().toString());
                            
                            showMessage('登录成功！正在进入游戏...', 'success');
                            console.log('登录成功，跳转到:', result.redirect);
                            setTimeout(() => {
                                window.location.href = result.redirect || 'game.html';
                            }, 1000);
                        }
                    } else {
                        if (result.message && result.message.includes('CSRF')) {
                            // CSRF令牌验证失败，尝试刷新令牌
                            console.log('CSRF验证失败，重新获取令牌');
                            getCsrfToken();
                            showMessage(result.message + '，已尝试刷新令牌，请重试', 'error');
                        } else {
                            showMessage(result.message || '登录失败，请稍后重试', 'error');
                        }
                    }
                })
                .catch(error => {
                    hideLoading();
                    console.error('登录请求失败:', error);
                    showMessage('登录失败: ' + error.message, 'error');
                });
            });
            
            // 🆕 清除保存的登录信息
            clearCredentialsBtn.addEventListener('click', function() {
                localStorage.removeItem('saved_username');
                localStorage.removeItem('saved_password');
                localStorage.setItem('remember_credentials', 'false');
                usernameInput.classList.remove('auto-filled');
                passwordInput.classList.remove('auto-filled');
                rememberCheckbox.checked = false;
                showMessage('已清除保存的登录信息', 'success');
                setTimeout(() => {
                    hideMessage();
                }, 3000);
            });
        });
    </script>
</body>
</html> 