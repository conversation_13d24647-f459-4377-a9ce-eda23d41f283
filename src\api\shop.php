<?php
/**
 * 🏪 一念修仙商城系统API
 * 处理商城相关的所有操作
 */

// 错误处理设置
ini_set('display_errors', 0);
error_reporting(E_ALL);

// 设置JSON响应头
header('Content-Type: application/json; charset=utf-8');

try {
    // 引入全局配置和函数库
    require_once __DIR__ . '/../includes/functions.php';

    // 检查维护模式
    if (function_exists('isMaintenanceMode') && isMaintenanceMode()) {
        echo json_encode([
            'success' => false,
            'message' => '游戏正在维护中，请稍后再试',
            'maintenance' => true
        ]);
        exit;
    }

    // 记录API调用（如果开启了调试）
    if (defined('DEBUG_LOG_API_CALLS') && DEBUG_LOG_API_CALLS && function_exists('writeLog')) {
        writeLog("API调用: shop.php", 'DEBUG', 'api.log');
    }

    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        exit(0);
    }

    // 检查用户登录状态
    if (!function_exists('isLoggedIn') || !isLoggedIn()) {
        echo json_encode(array('success' => false, 'message' => '用户未登录'));
        exit;
    }

    // 获取用户ID - 使用session直接获取
    $userId = $_SESSION['user_id'] ?? null;
    if (!$userId) {
        echo json_encode(array('success' => false, 'message' => '无法获取用户信息'));
        exit;
    }

    $action = isset($_GET['action']) ? $_GET['action'] : (isset($_POST['action']) ? $_POST['action'] : '');

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => '系统初始化失败: ' . $e->getMessage()
    ]);
    exit;
}

try {
    // 获取数据库连接
    if (function_exists('getDatabaseConnection')) {
        $pdo = getDatabase();
    } else {
        // 向后兼容
        require_once __DIR__ . '/../config/database.php';
        $pdo = getDatabase();
    }

    if (!$pdo) {
        if (function_exists('writeLog')) {
            writeLog("商城系统: 数据库连接失败", 'ERROR', 'database.log');
        }
        throw new Exception('数据库连接失败');
    }

    switch ($action) {
        case 'get_shop_categories':
            getShopCategories($pdo);
            break;
        case 'get_shop_items':
            $category = isset($_GET['category']) ? $_GET['category'] : '';
            getShopItems($pdo, $userId, $category);
            break;
        case 'purchase_item':
            $itemId = isset($_POST['item_id']) ? $_POST['item_id'] : '';
            $quantity = isset($_POST['quantity']) ? intval($_POST['quantity']) : 1;
            purchaseItem($pdo, $userId, $itemId, $quantity);
            break;
        case 'get_user_resources':
            getUserResources($pdo, $userId);
            break;
        case 'get_user_purchases':
            getUserPurchases($pdo, $userId);
            break;
        default:
            echo json_encode(array('success' => false, 'message' => '无效的操作'));
    }
} catch (Exception $e) {
    error_log("商城系统错误: " . $e->getMessage());
    echo json_encode(array('success' => false, 'message' => '系统错误: ' . $e->getMessage()));
}

// 获取商城分类
function getShopCategories($pdo) {
    $categories = array(
        array(
            'id' => 'market',
            'name' => '坊市',
            'icon' => '💰',
            'currency' => 'gold',
            'currency_name' => '金币',
            'description' => '基础修炼功法和丹药'
        ),
        array(
            'id' => 'black_market',
            'name' => '黑市',
            'icon' => '🔹',
            'currency' => 'spirit_stones',
            'currency_name' => '灵石',
            'description' => '高级功法和稀有物品'
        )
    );
    
    echo json_encode(array('success' => true, 'categories' => $categories));
}

/**
 * 获取商店商品列表
 */
function getShopItems($pdo, $userId, $category = '') {
    try {
        // 根据当前位置确定商店类型 - 这里暂时用参数，实际应该从用户当前位置判断
        $shopType = isset($_GET['shop_type']) ? $_GET['shop_type'] : 'market'; // 'market' 或 'black_market'
        
        // 基础查询
        $sql = "
            SELECT 
                s.shop_key,
                s.category,
                s.gold_price,
                s.spirit_stone_price,
                s.max_purchase,
                s.level_requirement,
                s.special_tags,
                s.purchase_note,
                g.id as item_id,
                g.item_name as name,
                g.description,
                g.item_type,
                g.slot_type,
                g.icon_image
            FROM shop_items s
            JOIN game_items g ON s.item_id = g.id
            WHERE s.shop_type = ? AND s.is_active = 1
        ";
        
        // 根据商店类型过滤价格
        if ($shopType === 'market') {
            // 坊市：只显示有金币价格的商品
            $sql .= " AND s.gold_price > 0";
        } else if ($shopType === 'black_market') {
            // 黑市：只显示有灵石价格的商品  
            $sql .= " AND s.spirit_stone_price > 0";
        }
        
        // 分类过滤
        if (!empty($category)) {
            $sql .= " AND s.category = ?";
        }
        
        $sql .= " ORDER BY s.sort_order, s.id";
        
        $stmt = $pdo->prepare($sql);
        $params = [$shopType];
        if (!empty($category)) {
            $params[] = $category;
        }
        $stmt->execute($params);
        
        $items = array();
        $categories = array();
        
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            // 根据商店类型设置显示价格
            if ($shopType === 'market') {
                $row['price'] = $row['gold_price'];
                $row['currency'] = 'gold';
                $row['price_text'] = $row['gold_price'] . '金币';
            } else {
                $row['price'] = $row['spirit_stone_price']; 
                $row['currency'] = 'spirit_stones';
                $row['price_text'] = $row['spirit_stone_price'] . '灵石';
            }
            
            // 解析特殊标签并设置物品属性
            if ($row['special_tags']) {
                $row['special_tags'] = json_decode($row['special_tags'], true);
            }
            
            // 设置物品属性
            $row['id'] = $row['shop_key'];
            
            // 🔧 使用道具本身的图片，而不是默认图标
            if (!empty($row['icon_image'])) {
                // 如果有icon_image字段，处理路径
                if (strpos($row['icon_image'], 'assets/') === 0 || strpos($row['icon_image'], 'http') === 0) {
                    $row['image_url'] = $row['icon_image'];
                } else {
                    $row['image_url'] = 'assets/images/' . $row['icon_image'];
                }
            } else {
                // 如果没有icon_image，使用generateItemImagePath函数生成
                $row['image_url'] = generateItemImagePath($row['name'], $row['item_type'], $row['slot_type']);
            }
            
            $row['icon'] = getItemIcon($row['item_type'], $row['slot_type']);
            $row['rarity'] = getRarityFromTags($row['special_tags'] ?: array());
            $row['effect'] = $row['purchase_note'] ?: $row['description'];
            
            // 检查是否支持批量购买（材料类商品）
            $row['supports_bulk'] = ($row['item_type'] === 'material' && $row['max_purchase'] == 0);
            
            // 检查用户购买限制
            if ($row['max_purchase'] > 0) {
                $stmt2 = $pdo->prepare("
                    SELECT COUNT(*) as purchased_count 
                    FROM user_purchases 
                    WHERE user_id = ? AND item_id = ? AND item_type = 'shop'
                ");
                $stmt2->execute([$userId, $row['shop_key']]);
                $purchased = $stmt2->fetch(PDO::FETCH_ASSOC);
                // 🔧 修复：确保返回数值类型而非字符串
                $row['purchased_count'] = intval($purchased['purchased_count']);
                $row['purchased'] = intval($purchased['purchased_count']);
                $row['can_purchase'] = intval($purchased['purchased_count']) < intval($row['max_purchase']);
            } else {
                $row['purchased_count'] = 0;
                $row['purchased'] = 0;
                $row['can_purchase'] = true;
            }
            
            // 🔧 修复：确保其他数值字段也是数值类型
            $row['max_purchase'] = intval($row['max_purchase']);
            $row['price'] = intval($row['price']);
            
            // 按分类分组
            $categoryKey = $row['category'];
            if (!isset($categories[$categoryKey])) {
                $categories[$categoryKey] = array(
                    'category' => $categoryKey,
                    'category_name' => getCategoryDisplayName($categoryKey),
                    'items' => array()
                );
            }
            
            $categories[$categoryKey]['items'][] = $row;
        }
        
        // 转换为数组格式
        $itemsData = array_values($categories);
        
        echo json_encode(array(
            'success' => true,
            'shop_type' => $shopType,
            'category' => $category,
            'items' => $itemsData
        ));
        
    } catch (Exception $e) {
        echo json_encode(array('success' => false, 'message' => $e->getMessage()));
    }
}

// 获取分类显示名称
function getCategoryDisplayName($categoryKey) {
    $categoryNames = array(
        'technique_manual' => '功法秘籍',
        'alchemy_recipes' => '炼丹丹方',
        'alchemy_materials' => '炼丹材料',
        'furnaces' => '炼丹工具',
        'pills' => '丹药',
        'rare_items' => '稀有物品',
        'consumables' => '消耗品'
    );
    
    return isset($categoryNames[$categoryKey]) ? $categoryNames[$categoryKey] : '其他商品';
}

// 获取物品图标
function getItemIcon($itemType, $slotType) {
    if ($slotType === 'technique_manual') return '📜';
    if ($itemType === 'recipe') return '📋';
    if ($itemType === 'material') return '🌿';
    if ($itemType === 'furnace') return '🔥';
    if ($itemType === 'consumable') return '💊';
    if ($itemType === 'special') return '🎁';
    return '📦';
}

// 根据标签获取稀有度
function getRarityFromTags($tags) {
    if (in_array('传说', $tags)) return '传说';
    if (in_array('史诗', $tags)) return '史诗';
    if (in_array('稀有', $tags)) return '稀有';
    return '普通';
}

// 购买商品
function purchaseItem($pdo, $userId, $itemId, $quantity) {
    try {
        // 开始事务
        $pdo->beginTransaction();
        
        // 获取用户信息
        $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute(array($userId));
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            throw new Exception('用户不存在');
        }
        
        // 从数据库获取商品信息
        $stmt = $pdo->prepare("
            SELECT 
                s.*,
                g.item_name as name,
                g.description,
                g.item_type,
                g.slot_type
            FROM shop_items s
            JOIN game_items g ON s.item_id = g.id
            WHERE s.shop_key = ? AND s.is_active = 1
        ");
        $stmt->execute([$itemId]);
        $shopItem = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$shopItem) {
            throw new Exception('商品不存在或已下架');
        }
        
        // 确定价格和货币类型
        if ($shopItem['shop_type'] === 'market') {
            $price = intval($shopItem['gold_price']);
            $currency = 'gold';
            $userCurrency = intval($user['gold']);
        } else {
            $price = intval($shopItem['spirit_stone_price']);
            $currency = 'spirit_stones';
            $userCurrency = intval($user['spirit_stones']);
        }
        
        if ($price <= 0) {
            throw new Exception('商品价格配置错误');
        }
        
        $totalCost = $price * $quantity;
        
        // 检查用户货币是否足够
        if ($userCurrency < $totalCost) {
            $currencyName = ($currency === 'gold') ? '金币' : '灵石';
            throw new Exception("{$currencyName}不足，需要 {$totalCost}，当前拥有 {$userCurrency}");
        }
        
        // 检查限购
        if ($shopItem['max_purchase'] > 0) {
            $stmt = $pdo->prepare("
                SELECT COUNT(*) 
                FROM user_purchases 
                WHERE user_id = ? AND item_id = ? AND item_type = 'shop'
            ");
            $stmt->execute([$userId, $itemId]);
            $purchasedCount = intval($stmt->fetchColumn());
            
            if ($purchasedCount + $quantity > $shopItem['max_purchase']) {
                throw new Exception("超出限购数量，最多可购买 {$shopItem['max_purchase']} 个，已购买 {$purchasedCount} 个");
            }
        }
        
        // 扣除货币
        $stmt = $pdo->prepare("UPDATE users SET {$currency} = {$currency} - ? WHERE id = ?");
        $stmt->execute([$totalCost, $userId]);
        
        // 添加物品到背包
        for ($i = 0; $i < $quantity; $i++) {
            addItemToInventory($pdo, $userId, $shopItem['item_id'], 1);
            
            // 记录购买历史
            $stmt = $pdo->prepare("
                INSERT INTO user_purchases (user_id, item_id, item_type, purchase_time)
                VALUES (?, ?, 'shop', NOW())
            ");
            $stmt->execute([$userId, $itemId]);
        }
        
        $pdo->commit();
        
        // 🔧 新增：获取购买后的商品状态信息
        $newPurchasedCount = 0;
        if ($shopItem['max_purchase'] > 0) {
            $stmt = $pdo->prepare("
                SELECT COUNT(*) 
                FROM user_purchases 
                WHERE user_id = ? AND item_id = ? AND item_type = 'shop'
            ");
            $stmt->execute([$userId, $itemId]);
            $newPurchasedCount = intval($stmt->fetchColumn());
        }
        
        $currencyName = ($currency === 'gold') ? '金币' : '灵石';
        $message = "成功购买 {$quantity} 个 {$shopItem['name']}，消耗 {$totalCost} {$currencyName}";
        
        echo json_encode(array(
            'success' => true,
            'message' => $message,
            'item_name' => $shopItem['name'],
            'quantity' => $quantity,
            'cost' => $totalCost,
            'currency' => $currency,
            // 🔧 新增：返回商品状态信息以支持前端精细更新
            'item_status' => array(
                'item_id' => $itemId,
                'purchased_count' => $newPurchasedCount,
                'max_purchase' => intval($shopItem['max_purchase'])
            )
        ));
        
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollback();
        }
        echo json_encode(array('success' => false, 'message' => $e->getMessage()));
    }
}

// 添加物品到背包
function addItemToInventory($pdo, $userId, $itemId, $quantity) {
    // 获取角色ID
    $stmt = $pdo->prepare("SELECT id FROM characters WHERE user_id = ?");
    $stmt->execute([$userId]);
    $character = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$character) {
        throw new Exception('角色不存在');
    }
    
    $characterId = $character['id'];
    
    // 获取物品信息和堆叠限制
    $stmt = $pdo->prepare("SELECT max_stack, item_type, is_stackable FROM game_items WHERE id = ?");
    $stmt->execute([$itemId]);
    $gameItem = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$gameItem) {
        throw new Exception('物品不存在');
    }
    
    $maxStack = intval($gameItem['max_stack']);
    $itemType = $gameItem['item_type'];
    $isStackable = $gameItem['is_stackable'];
    
    // 引入背包工具函数
    require_once __DIR__ . '/../includes/inventory_utils.php';
    
    // 如果物品不可堆叠，逐个添加
    if (!$isStackable || $maxStack <= 1) {
        for ($i = 0; $i < $quantity; $i++) {
            $sortWeight = calculateSortWeight($pdo, $characterId, $itemType);
            $stmt = $pdo->prepare("
                INSERT INTO user_inventories (character_id, item_id, item_type, quantity, obtained_time, obtained_source, bind_status, sort_weight)
                VALUES (?, ?, ?, 1, NOW(), 'shop_purchase', 'unbound', ?)
            ");
            $stmt->execute([$characterId, $itemId, $itemType, $sortWeight]);
        }
        return;
    }
    
    // 可堆叠物品的智能分配逻辑
    $remainingQuantity = $quantity;
    
    // 1. 先尝试填充现有的未满堆叠位置
    $stmt = $pdo->prepare("
        SELECT id, quantity FROM user_inventories 
        WHERE character_id = ? AND item_id = ? AND quantity < ?
        ORDER BY quantity DESC
    ");
    $stmt->execute([$characterId, $itemId, $maxStack]);
    $existingSlots = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($existingSlots as $slot) {
        if ($remainingQuantity <= 0) break;
        
        $availableSpace = $maxStack - $slot['quantity'];
        $addToThisSlot = min($remainingQuantity, $availableSpace);
        
        // 更新现有槽位
        $stmt = $pdo->prepare("
            UPDATE user_inventories 
            SET quantity = quantity + ?, updated_at = NOW()
            WHERE id = ?
        ");
        $stmt->execute([$addToThisSlot, $slot['id']]);
        
        $remainingQuantity -= $addToThisSlot;
    }
    
    // 2. 如果还有剩余数量，创建新的背包位置
    while ($remainingQuantity > 0) {
        $addToNewSlot = min($remainingQuantity, $maxStack);
        $sortWeight = calculateSortWeight($pdo, $characterId, $itemType);
        
        // 创建新的背包位置
        $stmt = $pdo->prepare("
            INSERT INTO user_inventories (character_id, item_id, item_type, quantity, obtained_time, obtained_source, bind_status, sort_weight)
            VALUES (?, ?, ?, ?, NOW(), 'shop_purchase', 'unbound', ?)
        ");
        $stmt->execute([$characterId, $itemId, $itemType, $addToNewSlot, $sortWeight]);
        
        $remainingQuantity -= $addToNewSlot;
    }
}

// 获取用户资源
function getUserResources($pdo, $userId) {
    try {
        $stmt = $pdo->prepare("SELECT gold, spirit_stones FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user) {
            echo json_encode(array(
                'success' => true,
                'resources' => array(
                    'gold' => intval($user['gold']),
                    'spirit_stones' => intval($user['spirit_stones'])
                )
            ));
        } else {
            echo json_encode(array('success' => false, 'message' => '用户不存在'));
        }
    } catch (Exception $e) {
        echo json_encode(array('success' => false, 'message' => '获取用户资源失败: ' . $e->getMessage()));
    }
}

// 获取用户购买历史
function getUserPurchases($pdo, $userId) {
    try {
        $stmt = $pdo->prepare("
            SELECT item_id, item_type, purchase_time, COUNT(*) as purchase_count
            FROM user_purchases 
            WHERE user_id = ? 
            GROUP BY item_id, item_type
            ORDER BY purchase_time DESC
        ");
        $stmt->execute([$userId]);
        $purchases = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode(array('success' => true, 'purchases' => $purchases));
    } catch (Exception $e) {
        echo json_encode(array('success' => false, 'message' => '获取购买历史失败: ' . $e->getMessage()));
    }
}

// 🔧 根据物品名称、类型和槽位生成图片路径
function generateItemImagePath($itemName, $itemType, $slotType = '') {
    // 根据物品名称中的境界关键词确定等级
    $realmKeywords = [
        '开光' => 1, '灵虚' => 2, '辟谷' => 3, '心动' => 4, '元化' => 5,
        '元婴' => 6, '离合' => 7, '空冥' => 8, '寂灭' => 9, '大乘' => 10
    ];
    $realmLevel = 1; // 默认等级
    foreach ($realmKeywords as $keyword => $level) {
        if (strpos($itemName, $keyword) !== false) {
            $realmLevel = $level;
            break;
        }
    }
    
    if ($itemType === 'weapon') {
        if ($slotType === 'sword') {
            // 剑类武器图片映射
            $swordImages = [
                1 => 'bw_11001.png', 2 => 'bw_11002.png', 3 => 'bw_11003.png',
                4 => 'bw_11004.png', 5 => 'bw_11301.png', 6 => 'bw_11601.png',
                7 => 'bw_11602.png', 8 => 'bw_11603.png', 9 => 'bw_11604.png', 10 => 'bw_11605.png'
            ];
            return 'assets/images/' . (isset($swordImages[$realmLevel]) ? $swordImages[$realmLevel] : $swordImages[1]);
        } elseif ($slotType === 'fan') {
            // 扇类武器图片映射
            $fanImages = [
                1 => 'bw_10301.png', 2 => 'bw_10302.png', 3 => 'bw_10601.png',
                4 => 'bw_10602.png', 5 => 'bw_10801.png', 6 => 'bw_11801.png',
                7 => 'bw_11802.png', 8 => 'bw_11606.png', 9 => 'bw_11607.png', 10 => 'bw_11608.png'
            ];
            return 'assets/images/' . (isset($fanImages[$realmLevel]) ? $fanImages[$realmLevel] : $fanImages[1]);
        }
        return 'assets/images/battle_sword.png'; // 默认武器图片
    } elseif ($itemType === 'equipment') {
        if ($slotType === 'ring') {
            // 戒指类装备图片映射
            $ringImages = [
                1 => '1200.png', 2 => '1201.png', 3 => '1202.png', 4 => '1203.png', 5 => '1204.png',
                6 => '1205.png', 7 => '1206.png', 8 => '1207.png', 9 => '1208.png', 10 => '1209.png'
            ];
            return 'assets/images/' . (isset($ringImages[$realmLevel]) ? $ringImages[$realmLevel] : $ringImages[1]);
        } elseif ($slotType === 'bracers') {
            // 护臂类装备图片映射
            $bracersImages = [
                1 => 'zb_10101.png', 2 => 'zb_10201.png', 3 => 'zb_10202.png', 4 => 'zb_10261.png',
                5 => 'zb_10301.png', 6 => 'zb_10302.png', 7 => 'zb_10361.png', 8 => 'zb_10401.png',
                9 => 'zb_10402.png', 10 => 'zb_10403.png'
            ];
            return 'assets/images/' . (isset($bracersImages[$realmLevel]) ? $bracersImages[$realmLevel] : $bracersImages[1]);
        } elseif ($slotType === 'chest') {
            // 胸甲类装备图片映射
            $chestImages = [
                1 => 'zb_20101.png', 2 => 'zb_20201.png', 3 => 'zb_20202.png', 4 => 'zb_20261.png',
                5 => 'zb_20301.png', 6 => 'zb_20302.png', 7 => 'zb_20361.png', 8 => 'zb_20401.png',
                9 => 'zb_20402.png', 10 => 'zb_20403.png'
            ];
            return 'assets/images/' . (isset($chestImages[$realmLevel]) ? $chestImages[$realmLevel] : $chestImages[1]);
        }
        return 'assets/images/zb_10101.png'; // 默认装备图片
    } elseif ($itemType === 'material') {
        return 'assets/images/1100.png';
    } elseif ($itemType === 'consumable') {
        return 'assets/images/1101.png';
    } elseif ($itemType === 'currency') {
        return 'assets/images/1102.png';
    }
    
    return 'assets/images/1100.png'; // 默认图片
}

?> 