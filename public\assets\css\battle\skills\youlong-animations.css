/**
 * 游龙剑技能动画样式
 * 对应 animation_model = 'youlong'
 */

/* 🐉 游龙剑动画容器 */
.youlong-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1000;
    /* v2.0优化：启用硬件加速 */
    will-change: auto;
    transform: translateZ(0);
}

/* ===== 第一阶段：旋转开场效果 ===== */

/* 旋转剑样式 */
.youlong-rotating-sword {
    position: absolute;
    width: min(40px, 8vw);
    height: min(80px, 16vw);
    opacity: 1;
    transform: translate(-50%, -100%) scaleY(-1);
    transform-origin: center bottom;
    pointer-events: none;
    z-index: 200;
    
    /* 支持背景图片显示 */
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

/* 🔧 修复：旋转剑使用背景图片时的光晕效果 */
.youlong-rotating-sword.background-glow {
    /* 只对使用背景图片的旋转剑添加光晕 */
    -webkit-filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.8));
    filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.8)) 
            drop-shadow(0 0 16px rgba(255, 140, 0, 0.6))
            drop-shadow(0 0 24px rgba(255, 69, 0, 0.4));
}

/* 旋转剑武器图片样式 */
.youlong-rotating-sword .weapon-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    transform: scaleY(-1); /* 垂直翻转图片 */
    
    /* 为武器图片也添加光晕 */
    -webkit-filter: drop-shadow(0 0 6px rgba(255, 215, 0, 0.7));
    filter: drop-shadow(0 0 6px rgba(255, 215, 0, 0.7)) 
            drop-shadow(0 0 12px rgba(255, 140, 0, 0.5));
}

/* 主剑快速连续逆时针旋转动画 - 只转一圈 */
@keyframes youlong-main-rotate {
    0% { 
        transform: translate(-50%, -100%) scaleY(-1) rotate(0deg) scale(1.0); 
        opacity: 1; 
    }
    100% { 
        transform: translate(-50%, -100%) scaleY(-1) rotate(-360deg) scale(1.0); 
        opacity: 1; 
    }
}

/* 残影剑快速旋转动画 - 跟随主剑只转一圈，逐渐扩大和透明 */
@keyframes youlong-shadow-rotate {
    0% { 
        transform: translate(-50%, -100%) scaleY(-1) rotate(0deg) scale(1.0); 
        opacity: 0.7; 
    }
    25% { 
        transform: translate(-50%, -100%) scaleY(-1) rotate(-90deg) scale(1.25); 
        opacity: 0.6; 
    }
    50% { 
        transform: translate(-50%, -100%) scaleY(-1) rotate(-180deg) scale(1.5); 
        opacity: 0.5; 
    }
    75% { 
        transform: translate(-50%, -100%) scaleY(-1) rotate(-270deg) scale(1.75); 
        opacity: 0.4; 
    }
    100% { 
        transform: translate(-50%, -100%) scaleY(-1) rotate(-360deg) scale(2.0); 
        opacity: 0.3; 
    }
}

/* ===== 第二阶段：游龙编队 ===== */

/* 游龙剑样式 */
.youlong-dragon-sword {
    position: absolute;
    width: min(30px, 6vw);
    height: min(60px, 12vw);
    opacity: 0;
    transform: translate(-50%, -50%);
    pointer-events: none;
    z-index: 200;
    
    /* 支持背景图片显示 */
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

/* 🔧 修复：使用背景图片时的光晕效果 */
.youlong-dragon-sword.background-glow {
    /* 只对使用背景图片的剑添加光晕 */
    -webkit-filter: drop-shadow(0 0 6px rgba(255, 215, 0, 0.9)); 
    filter: drop-shadow(0 0 6px rgba(255, 215, 0, 0.9)) 
            drop-shadow(0 0 12px rgba(255, 140, 0, 0.7))
            drop-shadow(0 0 18px rgba(255, 69, 0, 0.5));
}

/* 游龙剑武器图片样式 */
.youlong-dragon-sword .weapon-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    
    /* 为武器图片也添加光晕 */
    -webkit-filter: drop-shadow(0 0 4px rgba(255, 215, 0, 0.8)) ;
    filter: drop-shadow(0 0 4px rgba(255, 215, 0, 0.8)) 
            drop-shadow(0 0 8px rgba(255, 140, 0, 0.6));
}

/* 小剑生成动画 */
@keyframes youlong-sword-appear {
    0% {
        transform: translate(-50%, -50%) scale(0) rotate(var(--initialAngle, 0deg));
        opacity: 0;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.2) rotate(var(--initialAngle, 0deg));
        opacity: 0.8;
    }
    100% {
        transform: translate(-50%, -50%) scale(1) rotate(var(--initialAngle, 0deg));
        opacity: 1;
    }
}

/* ===== 第三阶段：连续蛇形游动 ===== */

/* 连续蛇形游动动画 - 真正的蛇身效果 */
@keyframes youlong-continuous-snake {
    0% {
        left: var(--startX);
        top: var(--startY);
        transform: translate(-50%, -50%) rotate(var(--angle, 0deg)) scale(1);
        opacity: 1;
    }
    20% {
        /* 第一个蛇形弯曲点 */
        left: var(--quarter1X);
        top: var(--quarter1Y);
        transform: translate(-50%, -50%) rotate(calc(var(--angle, 0deg) + 10deg)) scale(1.1);
        opacity: 1;
    }
    40% {
        /* 蛇身中间弯曲 */
        left: var(--midX);
        top: var(--midY);
        transform: translate(-50%, -50%) rotate(calc(var(--angle, 0deg) - 15deg)) scale(1.15);
        opacity: 1;
    }
    60% {
        /* 第三个蛇形弯曲点 */
        left: var(--quarter3X);
        top: var(--quarter3Y);
        transform: translate(-50%, -50%) rotate(calc(var(--angle, 0deg) + 8deg)) scale(1.12);
        opacity: 1;
    }
    80% {
        /* 接近目标 - 蛇头准备攻击 */
        left: calc(var(--quarter3X) + (var(--targetX) - var(--quarter3X)) * 0.6);
        top: calc(var(--quarter3Y) + (var(--targetY) - var(--quarter3Y)) * 0.6);
        transform: translate(-50%, -50%) rotate(calc(var(--angle, 0deg) - 5deg)) scale(1.2);
        opacity: 1;
    }
    100% {
        /* 击中目标 */
        left: var(--targetX);
        top: var(--targetY);
        transform: translate(-50%, -50%) rotate(var(--angle, 0deg)) scale(1.3);
        opacity: 1;
    }
}

/* 原版蛇形飞行动画（保留备用） */
@keyframes youlong-snake-fly {
    0% {
        left: var(--startX);
        top: var(--startY);
        transform: translate(-50%, -50%) rotate(var(--angle, 0deg)) scale(1);
        opacity: 1;
        -webkit-filter: drop-shadow(0 0 6px rgba(255, 215, 0, 0.9)); 
        filter: drop-shadow(0 0 6px rgba(255, 215, 0, 0.9)) 
                drop-shadow(0 0 12px rgba(255, 140, 0, 0.7));
    }
    25% {
        /* 第一个控制点 - 蛇形弯曲 */
        left: calc(var(--startX) + (var(--midX) - var(--startX)) * 0.5);
        top: calc(var(--startY) + (var(--midY) - var(--startY)) * 0.5);
        transform: translate(-50%, -50%) rotate(calc(var(--angle, 0deg) + 15deg)) scale(1.1);
        opacity: 1;
        -webkit-filter: drop-shadow(0 0 8px rgba(255, 215, 0, 1));
        filter: drop-shadow(0 0 8px rgba(255, 215, 0, 1)) 
                drop-shadow(0 0 16px rgba(255, 140, 0, 0.8));
    }
    50% {
        /* 中间点 - 最大弯曲 */
        left: var(--midX);
        top: var(--midY);
        transform: translate(-50%, -50%) rotate(calc(var(--angle, 0deg) - 15deg)) scale(1.2);
        opacity: 1;
        -webkit-filter: drop-shadow(0 0 10px rgba(255, 215, 0, 1)); 
        filter: drop-shadow(0 0 10px rgba(255, 215, 0, 1)) 
                drop-shadow(0 0 20px rgba(255, 140, 0, 0.9));
    }
    75% {
        /* 第二个控制点 - 蛇形归正 */
        left: calc(var(--midX) + (var(--targetX) - var(--midX)) * 0.5);
        top: calc(var(--midY) + (var(--targetY) - var(--midY)) * 0.5);
        transform: translate(-50%, -50%) rotate(calc(var(--angle, 0deg) + 10deg)) scale(1.1);
        opacity: 1;
        -webkit-filter: drop-shadow(0 0 8px rgba(255, 215, 0, 1)); 
        filter: drop-shadow(0 0 8px rgba(255, 215, 0, 1)) 
                drop-shadow(0 0 16px rgba(255, 140, 0, 0.8));
    }
    100% {
        /* 终点 - 准备击中 */
        left: var(--targetX);
        top: var(--targetY);
        transform: translate(-50%, -50%) rotate(var(--angle, 0deg)) scale(1.3);
        opacity: 1;
        -webkit-filter: drop-shadow(0 0 12px rgba(255, 215, 0, 1)); 
        filter: drop-shadow(0 0 12px rgba(255, 215, 0, 1)) 
                drop-shadow(0 0 24px rgba(255, 140, 0, 1))
                drop-shadow(0 0 36px rgba(255, 69, 0, 0.8));
    }
}

/* ===== 第四阶段：直线向上飞出消失 ===== */

/* 直线向上飞出消失动画 */
@keyframes youlong-fly-up-exit {
    0% {
        left: var(--targetX);
        top: var(--targetY);
        transform: translate(-50%, -50%) rotate(var(--angle, 0deg)) scale(1.3);
        opacity: 1;
    }
    30% {
        left: var(--targetX);
        top: calc(var(--targetY) + (var(--flyUpY) - var(--targetY)) * 0.3);
        transform: translate(-50%, -50%) rotate(var(--angle, 0deg)) scale(1.1);
        opacity: 0.9;
    }
    60% {
        left: var(--targetX);
        top: calc(var(--targetY) + (var(--flyUpY) - var(--targetY)) * 0.7);
        transform: translate(-50%, -50%) rotate(var(--angle, 0deg)) scale(0.9);
        opacity: 0.6;
    }
    100% {
        left: var(--targetX);
        top: var(--flyUpY);
        transform: translate(-50%, -50%) rotate(var(--angle, 0deg)) scale(0.5);
        opacity: 0;
    }
}

/* 穿透后渐隐消失动画 - 继续向前移动同时渐隐 */
@keyframes youlong-fade-out {
    0% {
        left: var(--targetX);
        top: var(--targetY);
        opacity: 1;
        transform: translate(-50%, -50%) rotate(var(--angle, 0deg)) scale(1.3);
        -webkit-filter: drop-shadow(0 0 12px rgba(255, 215, 0, 1)); 
        filter: drop-shadow(0 0 12px rgba(255, 215, 0, 1)) 
                drop-shadow(0 0 24px rgba(255, 140, 0, 0.9));
    }
    50% {
        left: calc(var(--targetX) + (var(--penetrateX, var(--targetX)) - var(--targetX)) * 0.5);
        top: calc(var(--targetY) + (var(--penetrateY, var(--targetY)) - var(--targetY)) * 0.5);
        opacity: 0.6;
        transform: translate(-50%, -50%) rotate(var(--angle, 0deg)) scale(1.0);
        -webkit-filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.8)); 
        filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.8)) 
                drop-shadow(0 0 16px rgba(255, 140, 0, 0.6));
    }
    100% {
        left: var(--penetrateX, calc(var(--targetX) + 100px));
        top: var(--penetrateY, var(--targetY));
        opacity: 0;
        transform: translate(-50%, -50%) rotate(var(--angle, 0deg)) scale(0.3);
        -webkit-filter: drop-shadow(0 0 2px rgba(255, 215, 0, 0.2)); 
        filter: drop-shadow(0 0 2px rgba(255, 215, 0, 0.2));
    }
}

/* ===== 穿透效果（备用） ===== */

/* 立即穿透动画（万剑诀风格） */
@keyframes youlong-penetrate-immediate {
    0% {
        left: var(--targetX);
        top: var(--targetY);
        transform: translate(-50%, -50%) rotate(var(--angle, 0deg)) scale(1.2);
        opacity: 1;
        -webkit-filter: drop-shadow(0 0 10px rgba(255, 215, 0, 1)); 
        filter: drop-shadow(0 0 10px rgba(255, 215, 0, 1)) 
                drop-shadow(0 0 20px rgba(255, 140, 0, 0.9));
    }
    100% {
        left: var(--penetrateX);
        top: var(--penetrateY);
        transform: translate(-50%, -50%) rotate(var(--angle, 0deg)) scale(0.5);
        opacity: 0;
        -webkit-filter: drop-shadow(0 0 6px rgba(255, 215, 0, 0.5));
        filter: drop-shadow(0 0 6px rgba(255, 215, 0, 0.5)) 
                drop-shadow(0 0 12px rgba(255, 140, 0, 0.3));
    }
}

/* 穿透而过动画（原版保留） */
@keyframes youlong-penetrate {
    0% {
        left: var(--targetX);
        top: var(--targetY);
        transform: translate(-50%, -50%) rotate(var(--angle, 0deg)) scale(1.3);
        opacity: 1;
        -webkit-filter: drop-shadow(0 0 12px rgba(255, 215, 0, 1)); 
        filter: drop-shadow(0 0 12px rgba(255, 215, 0, 1)) 
                drop-shadow(0 0 24px rgba(255, 140, 0, 1));
    }
    30% {
        left: calc(var(--targetX) + (var(--penetrateX) - var(--targetX)) * 0.3);
        top: calc(var(--targetY) + (var(--penetrateY) - var(--targetY)) * 0.3);
        transform: translate(-50%, -50%) rotate(var(--angle, 0deg)) scale(1.1);
        opacity: 0.9;
        -webkit-filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.9)); 
        filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.9)) 
                drop-shadow(0 0 20px rgba(255, 140, 0, 0.8));
    }
    70% {
        left: calc(var(--targetX) + (var(--penetrateX) - var(--targetX)) * 0.7);
        top: calc(var(--targetY) + (var(--penetrateY) - var(--targetY)) * 0.7);
        transform: translate(-50%, -50%) rotate(var(--angle, 0deg)) scale(0.8);
        opacity: 0.6;
        -webkit-filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.7)); 
        filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.7)) 
                drop-shadow(0 0 16px rgba(255, 140, 0, 0.5));
    }
    100% {
        left: var(--penetrateX);
        top: var(--penetrateY);
        transform: translate(-50%, -50%) rotate(var(--angle, 0deg)) scale(0.3);
        opacity: 0;
        -webkit-filter: drop-shadow(0 0 4px rgba(255, 215, 0, 0.3)); 
        filter: drop-shadow(0 0 4px rgba(255, 215, 0, 0.3)) 
                drop-shadow(0 0 8px rgba(255, 140, 0, 0.2));
    }
}

/* ===== 特效动画 ===== */

/* 敌人受击晃动动画 */
@keyframes youlong-enemy-hit-shake {
    0%, 100% { 
        transform: translateX(0); 
    }
    10% { 
        transform: translateX(-8px); 
    }
    20% { 
        transform: translateX(8px); 
    }
    30% { 
        transform: translateX(-6px); 
    }
    40% { 
        transform: translateX(6px); 
    }
    50% { 
        transform: translateX(-4px); 
    }
    60% { 
        transform: translateX(4px); 
    }
    70% { 
        transform: translateX(-2px); 
    }
    80% { 
        transform: translateX(2px); 
    }
    90% { 
        transform: translateX(-1px); 
    }
}

/* 游龙冲击波 */
.youlong-dragon-shockwave {
    position: absolute;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, rgba(255, 215, 0, 0.8) 0%, rgba(255, 140, 0, 0.6) 50%, transparent 100%);
    -webkit-filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.8));
    filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.8));
    pointer-events: none;
    z-index: 300;
}

@keyframes youlong-shockwave {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
        background: radial-gradient(circle, rgba(255, 215, 0, 1) 0%, rgba(255, 140, 0, 0.8) 50%, transparent 100%);
    }
    50% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0.8;
        background: radial-gradient(circle, rgba(255, 215, 0, 0.8) 0%, rgba(255, 140, 0, 0.6) 50%, transparent 100%);
    }
    100% {
        transform: translate(-50%, -50%) scale(3);
        opacity: 0;
        background: radial-gradient(circle, rgba(255, 215, 0, 0.4) 0%, rgba(255, 140, 0, 0.2) 50%, transparent 100%);
    }
}

/* 游龙撞击粒子 */
.youlong-impact-particle {
    position: absolute;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: radial-gradient(circle, #fff 0%, #ffd700 100%);
    transform: translate(-50%, -50%);
    -webkit-filter: drop-shadow(0 0 4px rgba(255, 215, 0, 0.8));
    filter: drop-shadow(0 0 4px rgba(255, 215, 0, 0.8));
    pointer-events: none;
    z-index: 295;
}

@keyframes youlong-particle-scatter {
    0% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
        background: radial-gradient(circle, #fff 0%, #ffd700 100%);
    }
    50% {
        transform: translate(-50%, -50%) translate(calc(var(--particleX) * 0.5), calc(var(--particleY) * 0.5)) scale(1.5);
        opacity: 0.8;
        background: radial-gradient(circle, #ffd700 0%, #ff8c00 100%);
    }
    100% {
        transform: translate(-50%, -50%) translate(var(--particleX), var(--particleY)) scale(0);
        opacity: 0;
        background: radial-gradient(circle, #ff8c00 0%, #ff4500 100%);
    }
}

/* ===== 性能优化 ===== */

/* v2.0性能优化 */
.youlong-rotating-sword,
.youlong-dragon-sword,
.youlong-dragon-shockwave,
.youlong-impact-particle {
    will-change: transform, opacity;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    /* 减少重绘 */
    contain: layout style paint;
}

/* ===== 移动端适配 ===== */

@media (max-width: 768px) {
    
    .youlong-rotating-sword {
        width: 32px !important;
        height: 64px !important;
    }
    
    .youlong-dragon-sword {
        width: 24px !important;
        height: 48px !important;
    }
    
    .youlong-dragon-shockwave {
        width: 60px !important;
        height: 60px !important;
    }
    
    .youlong-impact-particle {
        width: 3px !important;
        height: 3px !important;
    }
}

/* v2.0新增：减少动画计算 */
@media (prefers-reduced-motion: reduce) {
    .youlong-rotating-sword,
    .youlong-dragon-sword,
    .youlong-dragon-shockwave,
    .youlong-impact-particle {
        animation-duration: 0.1s !important;
    }
} 