

        /* 主要内容区域 */
        .main-container {
            padding: 15px 15px 90px 15px; /* 增加内边距，底部多留空间 */
            height: 100vh;
            position: relative;
            z-index: 1;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
        }

        /* 顶部标题 */
        .page-header {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
            border-radius: 20px; /* 增加圆角 */
            padding: 20px; /* 增加内边距 */
            margin-bottom: 20px; /* 增加下边距 */
            border: 2px solid rgba(212, 175, 55, 0.4);
            backdrop-filter: blur(15px);
            box-shadow: 
                0 8px 25px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2),
                0 0 30px rgba(212, 175, 55, 0.1); /* 添加金色光晕 */
            text-align: center;
        }

        .page-title {
            font-size: 26px; /* 稍微增大字体 */
            font-weight: bold;
            color: #d4af37;
            text-shadow: 
                0 0 15px rgba(212, 175, 55, 0.8),
                2px 2px 4px rgba(0, 0, 0, 0.6);
            margin-bottom: 0;
        }

        /* 标签切换部分 */
        .tabs-container {
            margin-bottom: 20px; /* 增加下边距 */
            display: flex;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.1));
            border-radius: 18px; /* 增加圆角 */
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 
                0 4px 15px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        .tab {
            flex: 1;
            padding: 15px 8px; /* 增加内边距 */
            text-align: center;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            color: #ecf0f1;
        }

        .tab.active {
            background: linear-gradient(135deg, rgba(212, 175, 55, 0.3), rgba(212, 175, 55, 0.1));
            color: #d4af37;
            text-shadow: 0 0 10px rgba(212, 175, 55, 0.5);
        }

        .tab.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 25%;
            width: 50%;
            height: 3px;
            background: #d4af37;
            border-radius: 3px 3px 0 0;
        }

        /* 地图区域列表 */
        .map-areas {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 18px; /* 增加地图卡片间距 */
            padding-bottom: 20px; /* 底部额外空间 */
        }

        /* 地图区域卡片 */
        .map-area {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
            border-radius: 15px; /* 减小圆角 */
            padding: 18px; /* 减少内边距 */
            border: 2px solid rgba(212, 175, 55, 0.4);
            backdrop-filter: blur(15px);
            box-shadow: 
                0 6px 20px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2),
                0 0 15px rgba(212, 175, 55, 0.1); /* 减小光晕 */
            position: relative;
            transition: all 0.3s ease; /* 减少动画时长 */
            /* 为背景图片预留空间 */
            min-height: 160px; /* 大幅减少最小高度 */
            margin-bottom: 15px;
            overflow: hidden;
        }

        /* 🎨 地图背景遮罩层 - 确保文字可读性 */
        .map-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, 
                rgba(0, 0, 0, 0.5) 0%, 
                rgba(0, 0, 0, 0.3) 50%, 
                rgba(0, 0, 0, 0.5) 100%);
            z-index: 1;
            border-radius: 15px;
        }

        /* 确保地图内容在遮罩之上 */
        .map-area > * {
            position: relative;
            z-index: 2;
        }

        .map-area:hover {
            transform: translateY(-2px); /* 减少悬浮效果 */
            box-shadow: 
                0 8px 25px rgba(0, 0, 0, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.3),
                0 0 20px rgba(212, 175, 55, 0.2); /* 减小光晕 */
        }

        .map-area.unlocked {
            background: linear-gradient(135deg, 
                rgba(52, 152, 219, 0.3) 0%, 
                rgba(41, 128, 185, 0.2) 50%, 
                rgba(52, 152, 219, 0.3) 100%);
            border-color: rgba(52, 152, 219, 0.6);
        }

        /* 🎨 解锁地图的遮罩层调整 */
        .map-area.unlocked .map-overlay {
            background: linear-gradient(135deg, 
                rgba(52, 152, 219, 0.4) 0%, 
                rgba(41, 128, 185, 0.3) 50%, 
                rgba(52, 152, 219, 0.4) 100%);
        }

        .map-area.locked {
            background: linear-gradient(135deg, 
                rgba(52, 73, 94, 0.3) 0%, 
                rgba(44, 62, 80, 0.2) 50%, 
                rgba(52, 73, 94, 0.3) 100%);
            border-color: rgba(149, 165, 166, 0.4);
            opacity: 0.7;
        }

        /* 🎨 锁定地图的遮罩层调整 */
        .map-area.locked .map-overlay {
            background: linear-gradient(135deg, 
                rgba(52, 73, 94, 0.6) 0%, 
                rgba(44, 62, 80, 0.5) 50%, 
                rgba(52, 73, 94, 0.6) 100%);
        }

        /* 区域头部 */
        .area-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px; /* 减少下边距 */
        }

        .area-name {
            font-size: 20px; /* 减小字体 */
            font-weight: bold;
            color: #fff;
            text-shadow: 
                0 0 10px rgba(255, 255, 255, 0.6),
                2px 2px 4px rgba(0, 0, 0, 0.8),
                -1px -1px 2px rgba(0, 0, 0, 0.6);
            background: rgba(0, 0, 0, 0.4);
            padding: 6px 12px; /* 减少内边距 */
            border-radius: 10px; /* 减小圆角 */
            backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .area-progress {
            background: linear-gradient(135deg, rgba(212, 175, 55, 0.9), rgba(212, 175, 55, 0.7));
            color: #1a3a5c;
            padding: 4px 10px; /* 减少内边距 */
            border-radius: 12px;
            font-size: 13px; /* 减小字体 */
            font-weight: bold;
            border: 1px solid rgba(212, 175, 55, 0.3);
            text-shadow: none;
            backdrop-filter: blur(5px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        /* 锁定图标 */
        .lock-icon {
            position: absolute;
            top: 15px; /* 调整位置 */
            right: 15px;
            font-size: 24px; /* 减小图标 */
            color: #e74c3c;
            text-shadow: 
                0 0 10px rgba(231, 76, 60, 0.8),
                2px 2px 4px rgba(0, 0, 0, 0.8);
            background: rgba(0, 0, 0, 0.6);
            padding: 6px; /* 减少内边距 */
            border-radius: 50%;
            backdrop-filter: blur(8px);
            border: 2px solid rgba(231, 76, 60, 0.4);
        }

        /* 区域描述 */
        .area-description {
            color: #ecf0f1;
            font-size: 14px; /* 减小字体 */
            line-height: 1.4; /* 减少行高 */
            margin-bottom: 12px; /* 减少下边距 */
            text-align: center;
            text-shadow: 
                1px 1px 2px rgba(0, 0, 0, 0.8),
                0 0 5px rgba(0, 0, 0, 0.6);
            background: rgba(0, 0, 0, 0.4);
            padding: 10px 15px; /* 减少内边距 */
            border-radius: 10px; /* 减小圆角 */
            backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* 进入按钮 */
        .enter-button {
            background: linear-gradient(135deg, #d4af37, #b8941f);
            color: #1a3a5c;
            border: none;
            padding: 12px 25px; /* 减少内边距 */
            border-radius: 10px; /* 减小圆角 */
            font-size: 15px; /* 减小字体 */
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: block;
            margin: 0 auto;
            box-shadow: 
                0 4px 15px rgba(212, 175, 55, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            min-width: 100px; /* 减少最小宽度 */
        }

        .enter-button:hover {
            transform: translateY(-2px);
            box-shadow: 
                0 6px 20px rgba(212, 175, 55, 0.6),
                inset 0 1px 0 rgba(255, 255, 255, 0.4),
                0 0 15px rgba(212, 175, 55, 0.3);
        }

        .enter-button:active {
            transform: scale(0.96);
        }

        .enter-button:disabled {
            background: linear-gradient(135deg, #95a5a6, #7f8c8d);
            color: #bdc3c7;
            cursor: not-allowed;
            box-shadow: none;
        }

        .enter-button:disabled:hover {
            transform: none;
        }

        /* 秘境地图的特殊样式 */
        .map-area.dungeon {
            background: linear-gradient(135deg, 
                rgba(155, 89, 182, 0.3) 0%, 
                rgba(142, 68, 173, 0.2) 50%, 
                rgba(155, 89, 182, 0.3) 100%);
            border-color: rgba(155, 89, 182, 0.6);
        }

        /* 🎨 秘境地图的遮罩层调整 */
        .map-area.dungeon .map-overlay {
            background: linear-gradient(135deg, 
                rgba(155, 89, 182, 0.4) 0%, 
                rgba(142, 68, 173, 0.3) 50%, 
                rgba(155, 89, 182, 0.4) 100%);
        }

        .map-area.dungeon .area-name {
            color: #e8daef;
            text-shadow: 0 0 10px rgba(155, 89, 182, 0.8);
        }

        .map-area.dungeon .area-progress {
            color: #9b59b6;
            border-color: rgba(155, 89, 182, 0.3);
        }

        .map-area.dungeon.unlocked .enter-button {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
        }

        /* 地图限制信息 - 改为更简洁的样式 */
        .map-limits {
            display: flex;
            justify-content: center;
            gap: 12px; /* 减少间距 */
            margin-bottom: 8px; /* 减少下边距 */
            font-size: 12px;
        }

        .map-limit {
            background: rgba(0, 0, 0, 0.6);
            padding: 4px 10px; /* 减少内边距 */
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(8px);
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        .map-limit.realm {
            color: #3498db;
            border-color: rgba(52, 152, 219, 0.5);
        }

        /* 移除等级限制相关样式或修改为境界样式 */
        .map-limit.level {
            display: none; /* 隐藏等级相关显示 */
        }

        .map-limit.entry {
            color: #e74c3c;
            border-color: rgba(231, 76, 60, 0.5);
        }
        
        /* 新增：地图信息行 - 简化 */
        .area-info {
            display: flex;
            justify-content: center;
            gap: 15px; /* 保持适中间距 */
            margin-bottom: 8px; /* 减少下边距 */
        }
        
        .info-item {
            background: rgba(0, 0, 0, 0.6);
            padding: 4px 10px; /* 减少内边距 */
            border-radius: 10px; /* 减小圆角 */
            font-size: 12px; /* 减小字体 */
            display: flex;
            align-items: center;
            gap: 4px;
            backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        .info-label {
            display: none; /* 隐藏标签文字 */
        }
        
        .info-value {
            color: #ecf0f1;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        }
        
        .requirement.not-met {
            border: 1px solid rgba(231, 76, 60, 0.6);
        }
        
        .requirement.not-met .info-value {
            color: #e74c3c;
        }

        /* 新增：底部按钮区域 */
        .area-actions {
            display: flex;
            justify-content: center;
            gap: 10px;
        }
        
        .action-btn {
            background: linear-gradient(135deg, #d4af37, #b8941f);
            color: #1a3a5c;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(212, 175, 55, 0.4);
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(212, 175, 55, 0.5);
        }
        
        .action-btn:active {
            transform: scale(0.95);
        }
        
        .action-btn:disabled {
            background: linear-gradient(135deg, #95a5a6, #7f8c8d);
            color: #bdc3c7;
            cursor: not-allowed;
            box-shadow: none;
        }
        
        /* 内容面板 */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 加载提示和错误消息 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            backdrop-filter: blur(5px);
        }

        .loading-content {
            background: linear-gradient(135deg, rgba(52, 152, 219, 0.9), rgba(41, 128, 185, 0.8));
            padding: 20px 30px;
            border-radius: 15px;
            text-align: center;
            border: 2px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            margin: 0 auto 15px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .loading-message {
            color: #fff;
            font-size: 16px;
            font-weight: bold;
        }

        .error-message {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, rgba(231, 76, 60, 0.9), rgba(192, 57, 43, 0.8));
            padding: 12px 25px;
            border-radius: 10px;
            text-align: center;
            z-index: 1000;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.4);
            border: 1px solid rgba(255, 255, 255, 0.1);
            max-width: 80%;
        }
        
        .error-content {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .error-icon {
            font-size: 20px;
            color: #fff;
        }
        
        .error-text {
            color: #fff;
            font-size: 14px;
            font-weight: bold;
        }
        
        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 30px;
            color: #bdc3c7;
            font-style: italic;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 15px;
            margin: 20px 0;
            border: 1px dashed rgba(255, 255, 255, 0.1);
        }

        /* 底部按钮 */
        .bottom-actions {
            display: flex;
            justify-content: center;
            margin-top: 20px;
            margin-bottom: 10px;
        }
        
        .bottom-btn {
            background: linear-gradient(135deg, rgba(41, 128, 185, 0.8), rgba(52, 152, 219, 0.6));
            color: #fff;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        .bottom-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
        }

        /* 响应式优化 */
        @media (max-width: 480px) {
            .main-container {
                padding: 12px 12px 85px 12px;
            }
            
            .page-title {
                font-size: 22px;
            }
            
            .area-name {
                font-size: 18px;
                padding: 5px 10px;
            }
            
            .area-progress {
                font-size: 12px;
                padding: 3px 8px;
            }
            
            .enter-button {
                padding: 10px 20px;
                font-size: 14px;
            }
            
            .map-area {
                padding: 15px;
                min-height: 140px; /* 移动端进一步减小 */
            }

            .area-description {
                font-size: 13px;
                padding: 8px 12px;
            }

            .map-limits, .area-info {
                gap: 10px;
            }

            .map-limit, .info-item {
                padding: 3px 8px;
                font-size: 11px;
            }
        }

        @media (max-width: 360px) {
            .area-name {
                font-size: 16px;
                padding: 4px 8px;
            }
            
            .area-progress {
                font-size: 11px;
                padding: 2px 6px;
            }
            
            .area-description {
                font-size: 12px;
                padding: 6px 10px;
            }

            .enter-button {
                padding: 8px 16px;
                font-size: 13px;
            }

            .map-area {
                padding: 12px;
                min-height: 120px; /* 超小屏进一步压缩 */
            }
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 4px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(212, 175, 55, 0.5);
            border-radius: 2px;
        }