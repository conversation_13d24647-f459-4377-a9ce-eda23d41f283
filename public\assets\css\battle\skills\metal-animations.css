/**
 * 金系技能动画样式 - 金针暴雨
 * 对应 animation_model = 'jinzhenbayu'
 */

/* 动画容器 */
.jinzhenbayu-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1000;
}

/* === 蓄力阶段动画 === */

/* 金属魔法阵 */
.jinzhenbayu-magic-circle {
    position: absolute;
    width: 140px;
    height: 140px;
    transform: translate(-50%, -50%);
    border: 4px solid rgba(255, 215, 0, 0.9);
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255, 215, 0, 0.4) 0%, rgba(255, 223, 0, 0.2) 50%, transparent 100%);
    animation: jinzhenbayu-magic-circle 1.2s ease-out;
    box-shadow: 0 0 40px rgba(255, 215, 0, 0.8), inset 0 0 30px rgba(255, 223, 0, 0.6);
}

@keyframes jinzhenbayu-magic-circle {
    0% {
        transform: translate(-50%, -50%) scale(0) rotate(0deg);
        opacity: 0;
    }
    30% {
        transform: translate(-50%, -50%) scale(1.4) rotate(90deg);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(1) rotate(360deg);
        opacity: 1;
    }
}

/* 内圈金属符文 */
.jinzhenbayu-inner-runes {
    position: absolute;
    width: 100px;
    height: 100px;
    transform: translate(-50%, -50%);
    border: 3px solid rgba(255, 223, 0, 0.9);
    border-radius: 50%;
    background: conic-gradient(from 0deg, 
        rgba(255, 215, 0, 0.7), 
        rgba(255, 223, 0, 0.5), 
        rgba(255, 255, 224, 0.3), 
        rgba(255, 223, 0, 0.5), 
        rgba(255, 215, 0, 0.7));
    animation: jinzhenbayu-inner-runes 1.0s ease-out infinite;
}

@keyframes jinzhenbayu-inner-runes {
    0%, 100% {
        transform: translate(-50%, -50%) scale(0.8) rotate(0deg);
        opacity: 0.9;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.2) rotate(180deg);
        opacity: 0.6;
    }
}

/* 外圈锋利符文 */
.jinzhenbayu-outer-runes {
    position: absolute;
    width: 160px;
    height: 160px;
    transform: translate(-50%, -50%);
    border: 2px solid rgba(255, 255, 224, 0.7);
    border-radius: 50%;
    background: conic-gradient(from 180deg, 
        rgba(255, 223, 0, 0.4), 
        rgba(255, 255, 224, 0.2), 
        rgba(255, 248, 220, 0.1), 
        rgba(255, 255, 224, 0.2), 
        rgba(255, 223, 0, 0.4));
    animation: jinzhenbayu-outer-runes 1.6s ease-out infinite;
}

@keyframes jinzhenbayu-outer-runes {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1) rotate(0deg);
        opacity: 0.7;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.3) rotate(-180deg);
        opacity: 0.4;
    }
}

/* 武器图片旋转 */
.jinzhenbayu-weapon-sprite {
    position: absolute;
    width: 50px;
    height: 50px;
    transform: translate(-50%, -50%);
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    animation: jinzhenbayu-weapon-rotate 1.2s linear infinite;
    -webkit-filter: drop-shadow(0 0 12px rgba(255, 215, 0, 1));
    filter: drop-shadow(0 0 12px rgba(255, 215, 0, 1));
}

@keyframes jinzhenbayu-weapon-rotate {
    0% { transform: translate(-50%, -50%) rotate(0deg) scale(1); }
    20% { transform: translate(-50%, -50%) rotate(72deg) scale(1.2); }
    40% { transform: translate(-50%, -50%) rotate(144deg) scale(1.3); }
    60% { transform: translate(-50%, -50%) rotate(216deg) scale(1.2); }
    80% { transform: translate(-50%, -50%) rotate(288deg) scale(1.1); }
    100% { transform: translate(-50%, -50%) rotate(360deg) scale(1); }
}

/* 蓄力能量核心 */
.jinzhenbayu-energy-core {
    position: absolute;
    width: 40px;
    height: 40px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, rgba(255, 215, 0, 1) 0%, rgba(255, 223, 0, 0.8) 50%, transparent 100%);
    border-radius: 50%;
    animation: jinzhenbayu-energy-pulse 0.8s ease-in-out infinite alternate;
    box-shadow: 0 0 30px rgba(255, 215, 0, 1);
}

@keyframes jinzhenbayu-energy-pulse {
    0% {
        transform: translate(-50%, -50%) scale(0.6);
        box-shadow: 0 0 25px rgba(255, 215, 0, 0.8);
    }
    100% {
        transform: translate(-50%, -50%) scale(1.4);
        box-shadow: 0 0 35px rgba(255, 215, 0, 1);
    }
}

/* 金属粒子汇聚效果 */
.jinzhenbayu-charge-metal {
    position: absolute;
    width: 6px;
    height: 6px;
    transform: translate(-50%, -50%);
    background: linear-gradient(45deg, rgba(255, 215, 0, 1), rgba(255, 223, 0, 0.9));
    border-radius: 20%;
    animation: jinzhenbayu-metal-gather 1.0s ease-out forwards;
    box-shadow: 0 0 6px rgba(255, 215, 0, 0.8);
}

@keyframes jinzhenbayu-metal-gather {
    0% {
        transform: translate(calc(-50% + var(--chargeX)), calc(-50% + var(--chargeY))) scale(0) rotate(0deg);
        opacity: 0;
    }
    30% {
        transform: translate(calc(-50% + var(--chargeX) * 0.7), calc(-50% + var(--chargeY) * 0.7)) scale(1.3) rotate(120deg);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(0.3) rotate(360deg);
        opacity: 0;
    }
}

/* 环绕金光闪烁 */
.jinzhenbayu-charge-glint {
    position: absolute;
    width: 12px;
    height: 12px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, rgba(255, 223, 0, 0.9), rgba(255, 255, 224, 0.5));
    border-radius: 50%;
    animation: jinzhenbayu-glint-move 1s ease-out forwards;
}

@keyframes jinzhenbayu-glint-move {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0;
    }
    25% {
        transform: translate(-50%, -50%) scale(2);
        opacity: 1;
    }
    75% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0.7;
    }
    100% {
        transform: translate(-50%, -50%) scale(0.1);
        opacity: 0;
    }
}

/* 金属能量波纹 */
.jinzhenbayu-energy-ripple {
    position: absolute;
    width: 30px;
    height: 30px;
    transform: translate(-50%, -50%);
    border: 3px solid rgba(255, 215, 0, 0.8);
    border-radius: 50%;
    animation: jinzhenbayu-ripple-expand 0.8s ease-out forwards;
}

@keyframes jinzhenbayu-ripple-expand {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
        border-width: 3px;
    }
    100% {
        transform: translate(-50%, -50%) scale(10);
        opacity: 0;
        border-width: 1px;
    }
}

/* 锋利气息效果 */
.jinzhenbayu-sharpness {
    position: absolute;
    width: 3px;
    height: 20px;
    transform: translate(-50%, -50%);
    background: linear-gradient(to top, rgba(255, 215, 0, 0.9), rgba(255, 255, 224, 0.6));
    border-radius: 1px;
    animation: jinzhenbayu-sharpness-flash 1.2s ease-out forwards;
}

@keyframes jinzhenbayu-sharpness-flash {
    0% {
        transform: translate(-50%, -50%) scaleY(0) rotate(0deg);
        opacity: 0;
    }
    40% {
        transform: translate(-50%, -50%) scaleY(1) rotate(45deg);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scaleY(0.2) rotate(90deg);
        opacity: 0;
    }
}

/* 金属共鸣音效视觉 */
.jinzhenbayu-resonance {
    position: absolute;
    width: 25px;
    height: 25px;
    transform: translate(-50%, -50%);
    border: 2px solid rgba(255, 223, 0, 0.7);
    border-radius: 50%;
    animation: jinzhenbayu-resonance-wave 1.5s ease-out forwards;
}

@keyframes jinzhenbayu-resonance-wave {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0.7;
    }
    50% {
        transform: translate(-50%, -50%) scale(3);
        opacity: 0.4;
    }
    100% {
        transform: translate(-50%, -50%) scale(6);
        opacity: 0;
    }
}

/* === 发射阶段动画 === */

/* 预发射金属震颤 */
.jinzhenbayu-metal-vibration {
    position: absolute;
    width: 90px;
    height: 90px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, rgba(255, 215, 0, 0.6), transparent);
    border-radius: 50%;
    animation: jinzhenbayu-metal-vibration 0.3s ease-in-out 3;
}

@keyframes jinzhenbayu-metal-vibration {
    0%, 100% { transform: translate(-50%, -50%) scale(1); }
    50% { transform: translate(-50%, -50%) scale(1.5); }
}

/* 天空金针云团 */
.jinzhenbayu-needle-cloud {
    position: absolute;
    width: 100px;
    height: 60px;
    transform: translate(-50%, -50%);
    background: radial-gradient(ellipse, rgba(255, 215, 0, 0.7), rgba(255, 223, 0, 0.4), transparent);
    border-radius: 50%;
    animation: jinzhenbayu-cloud-form 1s ease-out forwards;
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
}

@keyframes jinzhenbayu-cloud-form {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.3);
        opacity: 0.8;
    }
    100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
}

/* 金针暴雨 - 第一波 */
.jinzhenbayu-needle-wave-1 {
    position: absolute;
    width: 2px;
    height: 15px;
    transform: translate(-50%, -50%);
    background: linear-gradient(to bottom, 
        rgba(255, 215, 0, 1) 0%, 
        rgba(255, 223, 0, 0.9) 50%, 
        rgba(255, 255, 224, 0.7) 100%);
    border-radius: 1px;
    animation: jinzhenbayu-needle-fall var(--fallDuration) linear forwards;
    box-shadow: 0 0 3px rgba(255, 215, 0, 0.8);
}

/* 金针暴雨 - 第二波 */
.jinzhenbayu-needle-wave-2 {
    position: absolute;
    width: 2px;
    height: 18px;
    transform: translate(-50%, -50%);
    background: linear-gradient(to bottom, 
        rgba(255, 215, 0, 1) 0%, 
        rgba(255, 223, 0, 0.9) 40%, 
        rgba(255, 255, 224, 0.8) 80%, 
        rgba(255, 248, 220, 0.6) 100%);
    border-radius: 1px;
    animation: jinzhenbayu-needle-fall var(--fallDuration) linear forwards;
    box-shadow: 0 0 4px rgba(255, 215, 0, 0.9);
}

/* 金针暴雨 - 第三波 */
.jinzhenbayu-needle-wave-3 {
    position: absolute;
    width: 3px;
    height: 20px;
    transform: translate(-50%, -50%);
    background: linear-gradient(to bottom, 
        rgba(255, 215, 0, 1) 0%, 
        rgba(255, 223, 0, 1) 30%, 
        rgba(255, 255, 224, 0.9) 60%, 
        rgba(255, 248, 220, 0.7) 100%);
    border-radius: 1px;
    animation: jinzhenbayu-needle-fall var(--fallDuration) linear forwards;
    box-shadow: 0 0 5px rgba(255, 215, 0, 1);
}

@keyframes jinzhenbayu-needle-fall {
    0% {
        transform: translate(-50%, -50%) translateY(0) rotate(0deg);
        opacity: 0;
    }
    10% {
        transform: translate(-50%, -50%) translateY(calc(var(--fallDistance) * 0.1)) rotate(5deg);
        opacity: 1;
    }
    90% {
        transform: translate(-50%, -50%) translateY(calc(var(--fallDistance) * 0.9)) rotate(15deg);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) translateY(var(--fallDistance)) rotate(20deg);
        opacity: 0;
    }
}

/* 金针轨迹效果 */
.jinzhenbayu-needle-trail {
    position: absolute;
    width: 1px;
    height: 8px;
    transform: translate(-50%, -50%);
    background: linear-gradient(to bottom, rgba(255, 223, 0, 0.8), transparent);
    border-radius: 1px;
    animation: jinzhenbayu-trail-fade 0.6s ease-out forwards;
}

@keyframes jinzhenbayu-trail-fade {
    0% {
        transform: translate(-50%, -50%) scaleY(0);
        opacity: 0.8;
    }
    50% {
        transform: translate(-50%, -50%) scaleY(1);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scaleY(0.3);
        opacity: 0;
    }
}

/* 金光闪烁效果 */
.jinzhenbayu-falling-glint {
    position: absolute;
    width: 4px;
    height: 4px;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 224, 0.9);
    border-radius: 50%;
    animation: jinzhenbayu-glint-flash 0.4s ease-out forwards;
}

@keyframes jinzhenbayu-glint-flash {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0.9;
    }
    50% {
        transform: translate(-50%, -50%) scale(2);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(0.2);
        opacity: 0;
    }
}

/* === 击中阶段动画 === */

/* 瞬间金属闪光 */
.jinzhenbayu-impact-flash {
    position: absolute;
    width: 80px;
    height: 80px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, rgba(255, 215, 0, 1), rgba(255, 223, 0, 0.8), transparent);
    border-radius: 50%;
    animation: jinzhenbayu-impact-flash 0.3s ease-out forwards;
}

@keyframes jinzhenbayu-impact-flash {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
    }
    40% {
        transform: translate(-50%, -50%) scale(2);
        opacity: 0.9;
    }
    100% {
        transform: translate(-50%, -50%) scale(5);
        opacity: 0;
    }
}

/* 金属风暴核心 */
.jinzhenbayu-storm-core {
    position: absolute;
    width: 90px;
    height: 90px;
    transform: translate(-50%, -50%);
    background: conic-gradient(from 0deg, 
        rgba(255, 215, 0, 1), 
        rgba(255, 223, 0, 0.8), 
        rgba(255, 255, 224, 0.6), 
        rgba(255, 223, 0, 0.8), 
        rgba(255, 215, 0, 1));
    border-radius: 50%;
    animation: jinzhenbayu-storm-core 1.8s ease-out forwards;
}

@keyframes jinzhenbayu-storm-core {
    0% {
        transform: translate(-50%, -50%) rotate(0deg) scale(0);
        opacity: 1;
    }
    30% {
        transform: translate(-50%, -50%) rotate(180deg) scale(1.4);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) rotate(720deg) scale(0.1);
        opacity: 0;
    }
}

/* 冲击波 */
.jinzhenbayu-impact-shockwave {
    position: absolute;
    width: 50px;
    height: 50px;
    transform: translate(-50%, -50%);
    border: 4px solid rgba(255, 215, 0, 0.8);
    border-radius: 50%;
    animation: jinzhenbayu-shockwave-expand 0.9s ease-out forwards;
}

@keyframes jinzhenbayu-shockwave-expand {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
        border-width: 4px;
    }
    100% {
        transform: translate(-50%, -50%) scale(8);
        opacity: 0;
        border-width: 1px;
    }
}

/* 金属碎片爆发 */
.jinzhenbayu-metal-fragment {
    position: absolute;
    width: 4px;
    height: 8px;
    transform: translate(-50%, -50%);
    background: linear-gradient(45deg, rgba(255, 215, 0, 1), rgba(255, 223, 0, 0.8));
    border-radius: 10%;
    animation: jinzhenbayu-fragment-fly calc(1.5s * var(--fragmentVelocity)) ease-out forwards;
}

@keyframes jinzhenbayu-fragment-fly {
    0% {
        transform: translate(-50%, -50%) rotate(var(--fragmentAngle)) scale(0);
        opacity: 1;
    }
    20% {
        transform: translate(-50%, -50%) 
                   rotate(var(--fragmentAngle)) 
                   translateX(calc(var(--fragmentDistance) * 0.2)) 
                   scale(1.5);
        opacity: 1;
    }
    60% {
        transform: translate(-50%, -50%) 
                   rotate(var(--fragmentAngle)) 
                   translateX(calc(var(--fragmentDistance) * 0.7)) 
                   scale(1);
        opacity: 0.7;
    }
    100% {
        transform: translate(-50%, -50%) 
                   rotate(var(--fragmentAngle)) 
                   translateX(var(--fragmentDistance)) 
                   scale(0.2);
        opacity: 0;
    }
}

/* 切割线效果 */
.jinzhenbayu-cut-line {
    position: absolute;
    width: var(--cutLength);
    height: 2px;
    transform: translate(-50%, -50%) rotate(var(--cutAngle));
    background: linear-gradient(to right, 
        transparent 0%, 
        rgba(255, 215, 0, 1) 20%, 
        rgba(255, 255, 224, 0.9) 50%, 
        rgba(255, 215, 0, 1) 80%, 
        transparent 100%);
    border-radius: 1px;
    animation: jinzhenbayu-cut-slash 0.8s ease-out forwards;
}

@keyframes jinzhenbayu-cut-slash {
    0% {
        transform: translate(-50%, -50%) rotate(var(--cutAngle)) scaleX(0);
        opacity: 0;
    }
    30% {
        transform: translate(-50%, -50%) rotate(var(--cutAngle)) scaleX(1.2);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) rotate(var(--cutAngle)) scaleX(1);
        opacity: 0;
    }
}

/* 金属粉尘云 */
.jinzhenbayu-metal-dust {
    position: absolute;
    width: 18px;
    height: 18px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, rgba(255, 223, 0, 0.7), transparent);
    border-radius: 50%;
    animation: jinzhenbayu-dust-spread 2.8s ease-out forwards;
}

@keyframes jinzhenbayu-dust-spread {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0.7;
    }
    30% {
        transform: translate(-50%, -50%) scale(5);
        opacity: 0.5;
    }
    100% {
        transform: translate(-50%, -50%) scale(10);
        opacity: 0;
    }
}

/* 金光余韵 */
.jinzhenbayu-afterglow {
    position: absolute;
    width: 12px;
    height: 12px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, rgba(255, 255, 224, 0.8), rgba(255, 223, 0, 0.4));
    border-radius: 50%;
    animation: jinzhenbayu-afterglow 2.5s ease-out forwards;
}

@keyframes jinzhenbayu-afterglow {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0.8;
    }
    40% {
        transform: translate(-50%, -50%) scale(2);
        opacity: 0.6;
    }
    100% {
        transform: translate(-50%, -50%) scale(4);
        opacity: 0;
    }
}

/* 金属共鸣余震 */
.jinzhenbayu-impact-resonance {
    position: absolute;
    width: 30px;
    height: 30px;
    transform: translate(-50%, -50%);
    border: 3px solid rgba(255, 215, 0, 0.6);
    border-radius: 50%;
    animation: jinzhenbayu-impact-resonance 2s ease-out forwards;
}

@keyframes jinzhenbayu-impact-resonance {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0.6;
    }
    50% {
        transform: translate(-50%, -50%) scale(3);
        opacity: 0.4;
    }
    100% {
        transform: translate(-50%, -50%) scale(6);
        opacity: 0;
    }
}

/* 敌人受击效果 */
@keyframes metal-hit {
    0% { -webkit-filter: hue-rotate(0deg) saturate(1); }
    0% { filter: hue-rotate(0deg) saturate(1); }
    20% { -webkit-filter: hue-rotate(60deg) saturate(1.8) brightness(1.5); }
    20% { filter: hue-rotate(60deg) saturate(1.8) brightness(1.5); }
    40% { -webkit-filter: hue-rotate(0deg) saturate(1) brightness(0.6); }
    40% { filter: hue-rotate(0deg) saturate(1) brightness(0.6); }
    60% { -webkit-filter: hue-rotate(60deg) saturate(1.5) brightness(1.3); }
    60% { filter: hue-rotate(60deg) saturate(1.5) brightness(1.3); }
    80% { -webkit-filter: hue-rotate(0deg) saturate(1) brightness(0.8); }
    80% { filter: hue-rotate(0deg) saturate(1) brightness(0.8); }
    100% { -webkit-filter: hue-rotate(0deg) saturate(1); }
    100% { filter: hue-rotate(0deg) saturate(1); }
}

@keyframes metal-shake {
    0%, 100% { transform: translate(0, 0); }
    6% { transform: translate(-1px, -1px); }
    12% { transform: translate(1px, 1px); }
    18% { transform: translate(-1px, 1px); }
    24% { transform: translate(1px, -1px); }
    30% { transform: translate(-1px, 0px); }
    36% { transform: translate(1px, -1px); }
    42% { transform: translate(-1px, 1px); }
    48% { transform: translate(1px, 0px); }
    54% { transform: translate(-1px, -1px); }
    60% { transform: translate(1px, 1px); }
    66% { transform: translate(-1px, 0px); }
    72% { transform: translate(1px, -1px); }
    78% { transform: translate(-1px, 1px); }
    84% { transform: translate(1px, 0px); }
    90% { transform: translate(-1px, -1px); }
    96% { transform: translate(1px, 1px); }
}

/* 移动端适配 */
@media (max-width: 768px) {
    .jinzhenbayu-container {
        transform: scale(0.8);
    }
    
    .jinzhenbayu-magic-circle {
        width: 120px;
        height: 120px;
    }
    
    .jinzhenbayu-needle-cloud {
        width: 80px;
        height: 50px;
    }
    
    .jinzhenbayu-impact-flash {
        width: 70px;
        height: 70px;
    }
} 