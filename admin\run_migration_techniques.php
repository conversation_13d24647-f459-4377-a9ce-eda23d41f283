<?php
/**
 * 修炼功法系统JSON化改造迁移脚本
 * 
 * 此脚本将执行以下操作：
 * 1. 为characters表添加新字段
 * 2. 将现有功法数据转换为JSON格式
 * 3. 创建触发器确保功法字段的一致性
 * 
 * 使用方法：从命令行运行此脚本，或在浏览器中访问
 * 注意：请确保在执行前备份数据库
 */

// 设置脚本执行时间为不限制
set_time_limit(0);

// 定义数据库连接参数 - 需要根据实际环境修改
define('DB_HOST', 'localhost');
define('DB_USER', 'ynxx');
define('DB_PASS', 'mjlxz159'); 
define('DB_NAME', 'yn_game');

// 显示错误信息
ini_set('display_errors', 1);
error_reporting(E_ALL);

// 确定脚本运行环境
$isCli = php_sapi_name() === 'cli';

// 输出函数，根据环境不同使用不同的输出方式
function output($message, $isError = false) {
    global $isCli;
    
    if ($isCli) {
        if ($isError) {
            echo "\033[31m" . $message . "\033[0m\n"; // 红色文本
        } else {
            echo $message . "\n";
        }
    } else {
        if ($isError) {
            echo '<div style="color: red; margin: 10px 0;">' . $message . '</div>';
        } else {
            echo '<div style="margin: 10px 0;">' . $message . '</div>';
        }
    }
    
    // 在非CLI模式下刷新输出缓冲区，使输出立即显示
    if (!$isCli) {
        ob_flush();
        flush();
    }
}

// HTML头部（仅在Web模式下使用）
if (!$isCli) {
    echo '<!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>修炼功法系统JSON化改造迁移</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
            h1 { color: #333; }
            .success { color: green; }
            .error { color: red; }
            .warning { color: orange; }
            .step { background: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 4px solid #4285f4; }
        </style>
    </head>
    <body>
        <h1>修炼功法系统JSON化改造迁移</h1>';
    
    // 添加直接执行按钮
    echo '
        <form method="post">
            <input type="hidden" name="confirm" value="1">
            <button type="submit" style="padding: 10px; background: #4285f4; color: white; border: none; cursor: pointer;">执行迁移脚本</button>
        </form>';
    
    // 如果没有确认，显示警告并停止执行
    if (!isset($_POST['confirm'])) {
        echo '<div class="warning">警告：此脚本将修改数据库结构。请确保在执行前已备份数据库。点击上方按钮确认执行。</div>';
        echo '</body></html>';
        exit;
    }
}

// 连接数据库
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->exec("SET NAMES utf8mb4");
    
    output("数据库连接成功!");
} catch (PDOException $e) {
    output("数据库连接失败: " . $e->getMessage(), true);
    if (!$isCli) echo '</body></html>';
    exit;
}

// 迁移步骤1：检查characters表是否存在cultivation_techniques字段
output('<div class="step">步骤1: 检查字段是否已存在</div>');

try {
    $stmt = $pdo->prepare("SHOW COLUMNS FROM `characters` LIKE 'cultivation_techniques'");
    $stmt->execute();
    $fieldExists = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($fieldExists) {
        output("字段 cultivation_techniques 已存在，跳过创建。");
    } else {
        // 迁移步骤2：添加新字段
        output('<div class="step">步骤2: 添加新字段</div>');
        
        try {
            $pdo->exec("ALTER TABLE `characters` 
                ADD COLUMN `cultivation_techniques` TEXT NULL COMMENT 'JSON格式的功法数据集合' AFTER `cultivation_technique_breakthrough`,
                ADD COLUMN `current_technique` VARCHAR(50) NULL COMMENT '当前使用的功法ID' AFTER `cultivation_techniques`");
            
            output("新字段添加成功!");
        } catch (PDOException $e) {
            output("添加新字段失败: " . $e->getMessage(), true);
        }
    }
} catch (PDOException $e) {
    output("检查字段失败: " . $e->getMessage(), true);
}

// 迁移步骤3：数据迁移
output('<div class="step">步骤3: 迁移现有数据到JSON格式</div>');

try {
    $stmt = $pdo->prepare("
        UPDATE `characters` 
        SET 
          `cultivation_techniques` = JSON_OBJECT(
            'main', 
            JSON_OBJECT(
              'name', IFNULL(`cultivation_technique`, '凝气决'),
              'level', IFNULL(`cultivation_technique_level`, 1),
              'exp', IFNULL(`cultivation_technique_exp`, 0),
              'breakthrough', IFNULL(`cultivation_technique_breakthrough`, 0),
              'type', '初级功法',
              'source', '系统迁移'
            )
          ),
          `current_technique` = 'main'
        WHERE 
          `cultivation_techniques` IS NULL OR `cultivation_techniques` = ''
    ");
    $stmt->execute();
    
    $rowCount = $stmt->rowCount();
    output("数据迁移成功！共更新 {$rowCount} 条记录。");
} catch (PDOException $e) {
    output("数据迁移失败: " . $e->getMessage(), true);
}

// 迁移步骤4：检查和创建触发器
output('<div class="step">步骤4: 检查和创建触发器</div>');

// 先检查并删除已存在的触发器
try {
    $stmt = $pdo->prepare("SHOW TRIGGERS WHERE `Trigger` = 'before_character_update'");
    $stmt->execute();
    if ($stmt->rowCount() > 0) {
        $pdo->exec("DROP TRIGGER IF EXISTS `before_character_update`");
        output("已删除现有的 before_character_update 触发器。");
    }
    
    $stmt = $pdo->prepare("SHOW TRIGGERS WHERE `Trigger` = 'before_character_insert'");
    $stmt->execute();
    if ($stmt->rowCount() > 0) {
        $pdo->exec("DROP TRIGGER IF EXISTS `before_character_insert`");
        output("已删除现有的 before_character_insert 触发器。");
    }
} catch (PDOException $e) {
    output("检查触发器失败: " . $e->getMessage(), true);
}

// 创建新的触发器
try {
    $pdo->exec("
        CREATE TRIGGER `before_character_update` BEFORE UPDATE ON `characters`
        FOR EACH ROW
        BEGIN
          -- 如果新数据中未设置JSON功法数据但有传统功法数据，则自动转换
          IF (NEW.`cultivation_techniques` IS NULL OR NEW.`cultivation_techniques` = '') AND 
             (NEW.`cultivation_technique` IS NOT NULL) THEN
            SET NEW.`cultivation_techniques` = JSON_OBJECT(
              'main', 
              JSON_OBJECT(
                'name', IFNULL(NEW.`cultivation_technique`, '凝气决'),
                'level', IFNULL(NEW.`cultivation_technique_level`, 1),
                'exp', IFNULL(NEW.`cultivation_technique_exp`, 0),
                'breakthrough', IFNULL(NEW.`cultivation_technique_breakthrough`, 0),
                'type', '初级功法',
                'source', '系统自动转换'
              )
            );
            SET NEW.`current_technique` = 'main';
          END IF;
          
          -- 如果有JSON功法数据，同步到传统字段以保持向后兼容
          IF NEW.`cultivation_techniques` IS NOT NULL AND NEW.`cultivation_techniques` != '' AND 
             NEW.`current_technique` IS NOT NULL THEN
            -- 提取当前功法的数据并更新传统字段
            SET @technique_data = JSON_EXTRACT(NEW.`cultivation_techniques`, CONCAT('$.', NEW.`current_technique`));
            
            IF @technique_data IS NOT NULL THEN
              SET NEW.`cultivation_technique` = JSON_UNQUOTE(JSON_EXTRACT(@technique_data, '$.name'));
              SET NEW.`cultivation_technique_level` = JSON_EXTRACT(@technique_data, '$.level');
              SET NEW.`cultivation_technique_exp` = JSON_EXTRACT(@technique_data, '$.exp');
              SET NEW.`cultivation_technique_breakthrough` = JSON_EXTRACT(@technique_data, '$.breakthrough');
            END IF;
          END IF;
        END;
    ");
    output("创建 before_character_update 触发器成功!");
    
    $pdo->exec("
        CREATE TRIGGER `before_character_insert` BEFORE INSERT ON `characters`
        FOR EACH ROW
        BEGIN
          -- 如果未设置JSON功法数据，则根据传统字段创建
          IF (NEW.`cultivation_techniques` IS NULL OR NEW.`cultivation_techniques` = '') THEN
            SET NEW.`cultivation_techniques` = JSON_OBJECT(
              'ningqi', 
              JSON_OBJECT(
                'name', IFNULL(NEW.`cultivation_technique`, '凝气决'),
                'level', IFNULL(NEW.`cultivation_technique_level`, 1),
                'exp', IFNULL(NEW.`cultivation_technique_exp`, 0),
                'breakthrough', IFNULL(NEW.`cultivation_technique_breakthrough`, 0),
                'type', '初级功法',
                'source', '系统默认'
              )
            );
            SET NEW.`current_technique` = 'ningqi';
          END IF;
        END;
    ");
    output("创建 before_character_insert 触发器成功!");
} catch (PDOException $e) {
    output("创建触发器失败: " . $e->getMessage(), true);
}

// 迁移步骤5：验证迁移结果
output('<div class="step">步骤5: 验证迁移结果</div>');

try {
    $stmt = $pdo->prepare("SELECT COUNT(*) as total, COUNT(cultivation_techniques) as with_json FROM characters");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $total = $result['total'];
    $withJson = $result['with_json'];
    
    if ($total == $withJson) {
        output("<div class='success'>验证成功！所有 {$total} 条角色记录都已更新JSON功法数据。</div>");
    } else {
        output("<div class='warning'>验证结果：总共 {$total} 条角色记录，其中 {$withJson} 条包含JSON功法数据。</div>");
    }
} catch (PDOException $e) {
    output("验证结果失败: " . $e->getMessage(), true);
}

output('<div class="step">迁移完成！</div>');
output("<div class='success'>修炼功法系统已成功迁移到JSON格式。您现在可以使用新的功法系统功能了。</div>");

// HTML尾部（仅在Web模式下使用）
if (!$isCli) {
    echo '</body></html>';
} 