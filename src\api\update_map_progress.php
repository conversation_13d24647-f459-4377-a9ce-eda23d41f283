<?php

/**
 * 地图进度更新API
 * 处理玩家手动前进到下一层或后退到上一层
 */

// 禁用错误显示，确保JSON响应纯净
ini_set('display_errors', 0);
error_reporting(E_ALL);

require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/auth.php';

header('Content-Type: application/json; charset=utf-8');

// 检查维护模式
if (isMaintenanceMode()) {
    echo json_encode([
        'success' => false,
        'message' => '游戏正在维护中，请稍后再试',
        'maintenance' => true
    ]);
    exit;
}

// 记录API调用（如果开启了调试）
if (DEBUG_LOG_API_CALLS) {
    writeLog("API调用: update_map_progress.php", 'DEBUG', 'api.log');
}

try {
    // 🔐 验证用户登录状态
    $userId = check_auth();
    if (!$userId) {
        echo json_encode([
            'success' => false,
            'error' => 'auth_required',
            'message' => '用户未登录或会话已过期，请重新登录'
        ]);
        exit;
    }

    // 🔧 获取数据库连接
    $db = getDatabase();
    if (!$db) {
        throw new Exception('数据库连接失败');
    }

    // 获取请求参数
    $action = isset($_POST['action']) ? $_POST['action'] : '';
    $currentStage = intval(isset($_POST['current_stage']) ? $_POST['current_stage'] : 1);

    if (empty($action)) {
        throw new Exception('无效的请求参数');
    }

    // 🔧 获取角色ID
    $characterId = get_character_id();
    if (!$characterId) {
        throw new Exception('角色信息不存在');
    }

    // 🔧 修复：map_id必须从POST参数获取
    if (!isset($_POST['map_id']) || empty($_POST['map_id'])) {
        throw new Exception('缺少地图ID参数');
    }

    $mapId = intval($_POST['map_id']);
    if ($mapId <= 0) {
        throw new Exception('无效的地图ID');
    }

    error_log("📍 使用指定地图ID: {$mapId}");

    // 开始事务
    $db->beginTransaction();

    try {
        // 获取当前地图进度
        $stmt = $db->prepare("SELECT current_stage, max_stage_reached FROM user_map_progress WHERE character_id = ? AND map_id = ?");
        $stmt->execute([$characterId, $mapId]);
        $progress = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$progress) {
            // 🔧 如果没有进度记录，需要获取地图代码并创建记录
            $stmt = $db->prepare("SELECT map_code FROM game_maps WHERE id = ?");
            $stmt->execute([$mapId]);
            $mapInfo = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$mapInfo) {
                throw new Exception('地图不存在');
            }

            $mapCode = $mapInfo['map_code'];

            $stmt = $db->prepare("INSERT INTO user_map_progress (character_id, map_id, map_code, current_stage, max_stage_reached) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute([$characterId, $mapId, $mapCode, 1, 1]);
            $progress = ['current_stage' => 1, 'max_stage_reached' => 1];
        }

        $newStage = $currentStage;
        $newMaxStage = $progress['max_stage_reached'];

        if ($action === 'advance_stage') {
            // 🎉 前进到下一层
            $newStage = $currentStage + 1;
            $newMaxStage = max($newMaxStage, $newStage); // 更新最高通关记录

            error_log("🎉 玩家手动前进：从第{$currentStage}层到第{$newStage}层，最高层数：{$newMaxStage}");
        } elseif ($action === 'retreat_stage') {
            // 🔻 回退到上一层
            $newStage = max(1, $currentStage - 1); // 确保不小于第1层
            // 注意：最高通关记录不变

            error_log("🔻 玩家手动回退：从第{$currentStage}层到第{$newStage}层，最高层数保持：{$newMaxStage}");
        } elseif ($action === 'set_stage') {
            // 🆕 设置到指定层数（大地图快速进入功能）
            $targetStage = intval(isset($_POST['target_stage']) ? $_POST['target_stage'] : 1);

            // 验证目标层数的合理性
            if ($targetStage < 1) {
                throw new Exception('目标层数不能小于1');
            }

            if ($targetStage > $progress['max_stage_reached']) {
                throw new Exception("目标层数不能超过最高通关记录（第{$progress['max_stage_reached']}层）");
            }

            $newStage = $targetStage;
            // 设置层数时不改变最高通关记录

            error_log("🎯 玩家快速设置：从第{$currentStage}层设置到第{$newStage}层，最高层数保持：{$newMaxStage}");
        } else {
            throw new Exception('未知的操作类型');
        }

        // 更新地图进度
        $stmt = $db->prepare("UPDATE user_map_progress SET current_stage = ?, max_stage_reached = ? WHERE character_id = ? AND map_id = ?");
        $stmt->execute([$newStage, $newMaxStage, $characterId, $mapId]);

        // 提交事务
        $db->commit();

        $message = '';
        switch ($action) {
            case 'advance_stage':
                $message = "已前进到第{$newStage}层";
                break;
            case 'retreat_stage':
                $message = "已回退到第{$newStage}层";
                break;
            case 'set_stage':
                $message = "已设置到第{$newStage}层";
                break;
            default:
                $message = "操作完成，当前第{$newStage}层";
        }

        echo json_encode([
            'success' => true,
            'message' => $message,
            'new_stage' => $newStage,
            'max_stage_reached' => $newMaxStage
        ]);
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
} catch (Exception $e) {
    error_log("地图进度更新失败: " . $e->getMessage());
    error_log("错误堆栈: " . $e->getTraceAsString());

    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'error_details' => $e->getTraceAsString()
    ]);
}
