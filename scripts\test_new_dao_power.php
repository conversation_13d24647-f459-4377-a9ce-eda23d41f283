<?php
/**
 * 测试新的道行值计算系统
 * 基于实际战斗属性的道行值计算
 */

require_once __DIR__ . '/../src/config/database.php';

try {
    $pdo = getDatabase();
    if (!$pdo) {
        throw new Exception("数据库连接失败");
    }
    
    echo "=== 新道行值计算系统测试 ===\n\n";
    
    // 包含必要的文件
    require_once __DIR__ . '/../src/includes/equipment_stats_manager.php';
    require_once __DIR__ . '/../src/api/immortal_arena.php';
    
    // 获取几个测试玩家
    $stmt = $pdo->query("
        SELECT c.*, r.realm_name
        FROM characters c
        LEFT JOIN realm_levels r ON c.realm_id = r.id
        ORDER BY c.arena_dao_power DESC
        LIMIT 8
    ");
    
    $players = $stmt->fetchAll();
    
    echo "测试玩家道行值重新计算...\n\n";
    
    foreach ($players as $player) {
        echo "玩家: {$player['character_name']} (ID: {$player['id']})\n";
        echo "境界: {$player['realm_id']} - {$player['realm_name']}\n";
        echo "当前道行值: {$player['arena_dao_power']}\n";
        
        // 获取战斗属性详情
        $combatStats = getCombatStats($pdo, $player);
        
        echo "战斗属性详情:\n";
        printf("  物理攻击: %d, 仙法攻击: %d (总攻击: %d)\n", 
            $combatStats['physical_attack'], 
            $combatStats['immortal_attack'],
            $combatStats['physical_attack'] + $combatStats['immortal_attack']
        );
        printf("  物理防御: %d, 仙法防御: %d (总防御: %d)\n", 
            $combatStats['physical_defense'], 
            $combatStats['immortal_defense'],
            $combatStats['physical_defense'] + $combatStats['immortal_defense']
        );
        printf("  生命加成: %d, 法力加成: %d, 速度加成: %d\n", 
            $combatStats['hp_bonus'], 
            $combatStats['mp_bonus'], 
            $combatStats['speed_bonus']
        );
        printf("  暴击率: %.1f%%, 暴击伤害: %.1f%%, 命中: %.1f%%, 闪避: %.1f%%\n", 
            $combatStats['critical_bonus'], 
            $combatStats['critical_damage'] * 100, 
            $combatStats['accuracy_bonus'], 
            $combatStats['dodge_bonus']
        );
        
        // 计算各项战力分数
        $attackPower = calculateAttackPower($combatStats);
        $defensePower = calculateDefensePower($combatStats);
        $utilityPower = calculateUtilityPower($combatStats);
        
        echo "战力分解:\n";
        printf("  攻击战力: %.0f\n", $attackPower);
        printf("  防御战力: %.0f\n", $defensePower);
        printf("  辅助战力: %.0f\n", $utilityPower);
        
        // 计算新道行值
        $newDaoPower = calculateDaoPower($pdo, $player);
        $difference = $newDaoPower - $player['arena_dao_power'];
        $percentChange = $player['arena_dao_power'] > 0 ? ($difference / $player['arena_dao_power'] * 100) : 0;
        
        echo "新道行值: {$newDaoPower}\n";
        echo "变化: {$difference} (" . round($percentChange, 1) . "%)\n";
        
        // 评估合理性
        $totalCombatPower = $attackPower + $defensePower + $utilityPower;
        $powerRatio = $totalCombatPower > 0 ? ($newDaoPower / $totalCombatPower) : 0;
        
        if ($powerRatio >= 0.8 && $powerRatio <= 1.5) {
            echo "✅ 道行值与战力匹配度良好\n";
        } elseif ($powerRatio < 0.8) {
            echo "⚠️  道行值可能偏低\n";
        } else {
            echo "⚠️  道行值可能偏高\n";
        }
        
        echo "----------------------------------------\n\n";
    }
    
    // 分析新的道行值分布
    echo "=== 如果应用新计算方式的道行值分布预测 ===\n";
    
    $stmt = $pdo->query("SELECT * FROM characters ORDER BY id");
    $allPlayers = $stmt->fetchAll();
    
    $newDaoPowers = [];
    foreach ($allPlayers as $player) {
        $newDaoPower = calculateDaoPower($pdo, $player);
        $newDaoPowers[] = $newDaoPower;
    }
    
    // 统计分布
    $ranges = [
        '500以下' => [0, 500],
        '501-1000' => [501, 1000],
        '1001-2000' => [1001, 2000],
        '2001-3000' => [2001, 3000],
        '3001-5000' => [3001, 5000],
        '5000以上' => [5001, 999999]
    ];
    
    foreach ($ranges as $rangeName => $range) {
        $count = 0;
        $min = 999999;
        $max = 0;
        $sum = 0;
        
        foreach ($newDaoPowers as $daoPower) {
            if ($daoPower >= $range[0] && $daoPower <= $range[1]) {
                $count++;
                $min = min($min, $daoPower);
                $max = max($max, $daoPower);
                $sum += $daoPower;
            }
        }
        
        if ($count > 0) {
            $avg = $sum / $count;
            printf("道行值范围 %-10s: %2d个玩家, 最小值: %4d, 最大值: %4d, 平均值: %4.0f\n",
                $rangeName, $count, $min, $max, $avg);
        }
    }
    
    // 计算标准差
    $mean = array_sum($newDaoPowers) / count($newDaoPowers);
    $variance = 0;
    foreach ($newDaoPowers as $daoPower) {
        $variance += pow($daoPower - $mean, 2);
    }
    $stdDev = sqrt($variance / count($newDaoPowers));
    $coefficient = $stdDev / $mean;
    
    echo "\n统计信息:\n";
    printf("平均道行值: %.0f\n", $mean);
    printf("标准差: %.0f\n", $stdDev);
    printf("变异系数: %.3f\n", $coefficient);
    
    if ($coefficient > 0.3) {
        echo "✅ 道行值分布差异化良好\n";
    } else {
        echo "⚠️  道行值分布仍然过于集中\n";
    }
    
    // 检查境界相关性
    echo "\n=== 境界与道行值相关性检查 ===\n";
    
    $realmGroups = [];
    foreach ($allPlayers as $i => $player) {
        $realmId = $player['realm_id'];
        if (!isset($realmGroups[$realmId])) {
            $realmGroups[$realmId] = [];
        }
        $realmGroups[$realmId][] = $newDaoPowers[$i];
    }
    
    ksort($realmGroups);
    $prevAvg = 0;
    $correlationOK = true;
    
    foreach ($realmGroups as $realmId => $daoPowers) {
        if (count($daoPowers) >= 1) {
            $avg = array_sum($daoPowers) / count($daoPowers);
            $min = min($daoPowers);
            $max = max($daoPowers);
            
            printf("境界 %2d: %2d个玩家, 道行值范围: %4.0f-%4.0f, 平均: %4.0f\n",
                $realmId, count($daoPowers), $min, $max, $avg);
            
            if ($avg < $prevAvg) {
                $correlationOK = false;
            }
            $prevAvg = $avg;
        }
    }
    
    if ($correlationOK) {
        echo "\n✅ 境界与道行值相关性良好\n";
    } else {
        echo "\n⚠️  境界与道行值相关性需要改进\n";
    }
    
    echo "\n=== 测试完成 ===\n";
    echo "新的道行值计算系统更好地反映了玩家的实际战斗能力！\n";
    
} catch (Exception $e) {
    echo "测试失败: " . $e->getMessage() . "\n";
    echo "错误详情: " . $e->getTraceAsString() . "\n";
}
?>
