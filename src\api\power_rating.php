<?php
// 🔧 最优先：完全禁用所有错误输出
error_reporting(0);
ini_set('display_errors', 0);
ini_set('log_errors', 0);

// 🔧 开启调试模式，记录详细日志
if (isset($_GET['debug']) || isset($_POST['debug'])) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    echo "<!-- DEBUG MODE ENABLED -->\n";
}

require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/auth.php';

// 🔧 临时禁用错误输出，确保JSON响应纯净
error_reporting(0);
ini_set('display_errors', 0);

// 检查维护模式
if (isMaintenanceMode()) {
    echo json_encode([
        'success' => false,
        'message' => '游戏正在维护中，请稍后再试',
        'maintenance' => true
    ]);
    exit;
}

// 记录API调用（如果开启了调试）
if (DEBUG_LOG_API_CALLS) {
    writeLog("API调用: power_rating.php", 'DEBUG', 'api.log');
}

header('Content-Type: application/json; charset=utf-8');

class PowerRating
{

    /**
     * 计算角色总战力
     */
    public static function calculateCharacterPower($characterData, $equipment = null)
    {
        $basePower = self::calculateBasePower($characterData);
        $equipmentPower = $equipment ? self::calculateTotalEquipmentPower($equipment) : 0;
        $realmLevel = isset($characterData['realm_level']) ? $characterData['realm_level'] : 1;
        $realmMultiplier = self::calculateRealmMultiplier($realmLevel);

        $totalPower = ($basePower + $equipmentPower) * $realmMultiplier;

        return round($totalPower);
    }

    /**
     * 计算基础战力
     */
    private static function calculateBasePower($stats)
    {
        // 攻击力取最高值
        $physicalAttack = isset($stats['physical_attack_total']) ? $stats['physical_attack_total'] : 0;
        $immortalAttack = isset($stats['immortal_attack_total']) ? $stats['immortal_attack_total'] : 0;
        $attackPower = max($physicalAttack, $immortalAttack);

        // 防御力取平均值
        $physicalDefense = isset($stats['physical_defense_total']) ? $stats['physical_defense_total'] : 0;
        $immortalDefense = isset($stats['immortal_defense_total']) ? $stats['immortal_defense_total'] : 0;
        $defensePower = ($physicalDefense + $immortalDefense) / 2;

        // 生命值按0.1权重
        $hp = isset($stats['hp_total']) ? $stats['hp_total'] : 0;
        $hpPower = $hp * 0.1;

        // 速度直接计算
        $speedPower = isset($stats['speed_total']) ? $stats['speed_total'] : 0;

        return $attackPower + $defensePower + $hpPower + $speedPower;
    }

    /**
     * 境界倍率计算
     */
    private static function calculateRealmMultiplier($realmLevel)
    {
        return 1 + ($realmLevel * 0.1);
    }

    /**
     * 装备总战力计算
     */
    private static function calculateTotalEquipmentPower($equipment)
    {
        $totalPower = 0;

        foreach ($equipment as $item) {
            if (is_array($item) && isset($item['item_type'])) {
                $itemType = $item['item_type'];
                if (in_array($itemType, array('weapon', 'equipment', 'armor', 'accessory'))) {
                    $totalPower += self::calculateSingleEquipmentPower($item);
                }
            }
        }

        return $totalPower;
    }

    /**
     * 🔥 重新设计：基于用户反馈的合理化战力权重系统 v3.0
     * 
     * @param array $equipment 装备数据
     * @return int 装备战力值
     */
    public static function calculateSingleEquipmentPower($equipment)
    {
        // 检查是否为装备类物品
        $slotType = isset($equipment['slot_type']) ? $equipment['slot_type'] : '';
        $itemType = isset($equipment['item_type']) ? $equipment['item_type'] : '';

        // 🔧 调试：记录装备识别信息
        error_log("🔍 [战力计算] 装备识别: slot_type='{$slotType}', item_type='{$itemType}'");

        // 装备类型包括：武器、防具、饰品
        $equipmentSlots = array('sword', 'fan', 'chest', 'legs', 'feet', 'ring', 'necklace', 'bracelet');
        $equipmentTypes = array('weapon', 'armor', 'accessory');

        $isEquipment = in_array($slotType, $equipmentSlots) || in_array($itemType, $equipmentTypes);

        // 🔧 调试：记录是否识别为装备
        error_log("🔍 [战力计算] 装备判定: isEquipment=" . ($isEquipment ? 'true' : 'false'));

        if (!$isEquipment) {
            error_log("⚠️ [战力计算] 非装备物品，返回0战力");
            return 0; // 非装备物品不计算战力
        }

        $power = 0;
        $powerBreakdown = array(); // 记录各属性贡献

        // === 🔥 重新设计的科学化权重系统 v4.0 ===

        // 核心攻击属性 - 权重2.0，这是战力的核心
        $physicalAttack = isset($equipment['physical_attack']) ? intval($equipment['physical_attack']) : 0;
        $immortalAttack = isset($equipment['immortal_attack']) ? intval($equipment['immortal_attack']) : 0;
        $attackPower = max($physicalAttack, $immortalAttack);
        $attackContribution = $attackPower * 2.0;
        $power += $attackContribution;
        if ($attackContribution > 0) {
            $powerBreakdown[] = "攻击力: {$attackPower} × 2.0 = {$attackContribution}";
        }

        // 防御力 - 权重1.5，重要但略低于攻击
        $physicalDefense = isset($equipment['physical_defense']) ? intval($equipment['physical_defense']) : 0;
        $immortalDefense = isset($equipment['immortal_defense']) ? intval($equipment['immortal_defense']) : 0;
        $defensePower = max($physicalDefense, $immortalDefense);
        $defenseContribution = $defensePower * 1.5;
        $power += $defenseContribution;
        if ($defenseContribution > 0) {
            $powerBreakdown[] = "防御力: {$defensePower} × 1.5 = {$defenseContribution}";
        }

        // === 暴击系统 - 科学化权重 ===

        // 暴击率 - 权重1.5 (进一步降低)
        // 理由：暴击率影响有限，不应该主导战力
        $criticalBonus = isset($equipment['critical_bonus']) ? floatval($equipment['critical_bonus']) : 0;
        $criticalContribution = $criticalBonus * 1.5;
        $power += $criticalContribution;
        if ($criticalContribution > 0) {
            $powerBreakdown[] = "暴击率: {$criticalBonus} × 1.5 = {$criticalContribution}";
        }

        // 暴击伤害 - 权重30 (继续降低)
        // 理由：暴击伤害是倍率加成，不应该过度影响战力评分
        $criticalDamage = isset($equipment['critical_damage']) ? floatval($equipment['critical_damage']) : 0;
        $criticalDamageContribution = $criticalDamage * 30;
        $power += $criticalDamageContribution;
        if ($criticalDamageContribution > 0) {
            $powerBreakdown[] = "暴击伤害: {$criticalDamage} × 30 = {$criticalDamageContribution}";
        }

        // 免暴率 - 权重1.2，防御性暴击属性
        $criticalResistance = isset($equipment['critical_resistance']) ? floatval($equipment['critical_resistance']) : 0;
        $resistanceContribution = $criticalResistance * 1.2;
        $power += $resistanceContribution;
        if ($resistanceContribution > 0) {
            $powerBreakdown[] = "免暴率: {$criticalResistance} × 1.2 = {$resistanceContribution}";
        }

        // === 命中闪避系统 - 适度权重 ===

        // 命中率 - 权重0.8 (进一步降低)
        $accuracyBonus = isset($equipment['accuracy_bonus']) ? floatval($equipment['accuracy_bonus']) : 0;
        $accuracyContribution = $accuracyBonus * 0.8;
        $power += $accuracyContribution;
        if ($accuracyContribution > 0) {
            $powerBreakdown[] = "命中率: {$accuracyBonus} × 0.8 = {$accuracyContribution}";
        }

        // 闪避率 - 权重1.0，防御价值
        $dodgeBonus = isset($equipment['dodge_bonus']) ? floatval($equipment['dodge_bonus']) : 0;
        $dodgeContribution = $dodgeBonus * 1.0;
        $power += $dodgeContribution;
        if ($dodgeContribution > 0) {
            $powerBreakdown[] = "闪避率: {$dodgeBonus} × 1.0 = {$dodgeContribution}";
        }

        // === 生存和机动性 - 适度权重 ===

        // 生命值 - 权重0.08 (适度降低)
        $hpBonus = isset($equipment['hp_bonus']) ? intval($equipment['hp_bonus']) : 0;
        $hpContribution = $hpBonus * 0.08;
        $power += $hpContribution;
        if ($hpContribution > 0) {
            $powerBreakdown[] = "生命值: {$hpBonus} × 0.08 = {$hpContribution}";
        }

        // 法力值 - 权重0.05 (适度降低)
        $mpBonus = isset($equipment['mp_bonus']) ? intval($equipment['mp_bonus']) : 0;
        $mpContribution = $mpBonus * 0.05;
        $power += $mpContribution;
        if ($mpContribution > 0) {
            $powerBreakdown[] = "法力值: {$mpBonus} × 0.05 = {$mpContribution}";
        }

        // 速度 - 权重1.0 (进一步降低)
        $speedBonus = isset($equipment['speed_bonus']) ? intval($equipment['speed_bonus']) : 0;
        $speedContribution = $speedBonus * 1.0;
        $power += $speedContribution;
        if ($speedContribution > 0) {
            $powerBreakdown[] = "速度: {$speedBonus} × 1.0 = {$speedContribution}";
        }

        // === 特殊效果处理 ===
        $specialEffects = isset($equipment['special_effects']) ? $equipment['special_effects'] : '';
        $specialContribution = 0;
        if (!empty($specialEffects)) {
            $specialContribution = self::calculateSpecialEffectsPower($specialEffects);
            $power += $specialContribution;
            if ($specialContribution > 0) {
                $powerBreakdown[] = "特殊效果: {$specialContribution}";
            }
        }

        $finalPower = max(0, round($power));

        // 🔧 调试：输出详细的战力计算过程
        $equipmentName = isset($equipment['name']) ? $equipment['name'] : '未知装备';
        error_log("⚔️ [战力计算v4.0] {$equipmentName}: " . implode(', ', $powerBreakdown) . " = 总战力{$finalPower}");

        return $finalPower;
    }

    /**
     * 🔥 重新设计：特殊效果战力评估
     * 基于实际效果影响给予合理战力加成
     * 
     * @param string $specialEffects 特殊效果描述
     * @return int 特殊效果战力加成
     */
    private static function calculateSpecialEffectsPower($specialEffects)
    {
        if (empty($specialEffects)) {
            return 0;
        }

        $power = 0;
        $effects = strtolower($specialEffects);

        // === 伤害增强类效果 ===
        if (strpos($effects, '伤害') !== false || strpos($effects, '攻击') !== false) {
            $power += 5; // 降低伤害类效果
        }

        // === 防御增强类效果 ===
        if (strpos($effects, '防御') !== false || strpos($effects, '抵抗') !== false || strpos($effects, '免疫') !== false) {
            $power += 4; // 降低防御类效果
        }

        // === 生命恢复类效果 ===
        if (strpos($effects, '恢复') !== false || strpos($effects, '治疗') !== false || strpos($effects, '回血') !== false) {
            $power += 3; // 降低恢复类效果
        }

        // === 特殊状态类效果 ===
        if (strpos($effects, '眩晕') !== false || strpos($effects, '冰冻') !== false || strpos($effects, '麻痹') !== false) {
            $power += 6; // 降低控制类效果
        }

        // === 属性提升类效果 ===
        if (strpos($effects, '提升') !== false || strpos($effects, '增加') !== false || strpos($effects, '加成') !== false) {
            $power += 2; // 降低属性提升类效果
        }

        // === 五行克制类效果 ===
        if (
            strpos($effects, '金') !== false || strpos($effects, '木') !== false ||
            strpos($effects, '水') !== false || strpos($effects, '火') !== false ||
            strpos($effects, '土') !== false || strpos($effects, '五行') !== false
        ) {
            $power += 3; // 降低五行效果
        }

        // === 稀有效果类型 ===
        if (strpos($effects, '无视') !== false || strpos($effects, '穿透') !== false) {
            $power += 8; // 降低无视类效果
        }

        if (strpos($effects, '反弹') !== false || strpos($effects, '反击') !== false) {
            $power += 5; // 降低反击类效果
        }

        // === 特殊词汇加成 ===
        if (strpos($effects, '神') !== false || strpos($effects, '仙') !== false || strpos($effects, '圣') !== false) {
            $power += 2; // 降低高级词汇加成
        }

        // 基础特效加成（有特效就给予基础分数）
        if (!empty($specialEffects)) {
            $power += 3; // 降低基础特效分数
        }

        // 限制最大特效战力，避免过高
        return min($power, 20); // 最多20点特效战力，大幅降低
    }

    /**
     * 威胁等级评估
     */
    public static function calculateThreatLevel($monsterPower, $playerPower)
    {
        $powerRatio = $playerPower > 0 ? $monsterPower / $playerPower : 999;

        if ($powerRatio < 0.7) return array('text' => '轻松', 'color' => '#2ecc71');
        if ($powerRatio < 1.0) return array('text' => '普通', 'color' => '#f39c12');
        if ($powerRatio < 1.5) return array('text' => '困难', 'color' => '#e67e22');
        if ($powerRatio < 2.0) return array('text' => '危险', 'color' => '#e74c3c');
        return array('text' => '致命', 'color' => '#8e44ad');
    }

    /**
     * 装备对比
     */
    public static function compareEquipment($currentEquipment, $newEquipment)
    {
        $currentPower = $currentEquipment ? self::calculateSingleEquipmentPower($currentEquipment) : 0;
        $newPower = self::calculateSingleEquipmentPower($newEquipment);

        return array(
            'current_power' => $currentPower,
            'new_power' => $newPower,
            'is_better' => $newPower > $currentPower,
            'power_difference' => $newPower - $currentPower
        );
    }

    /**
     * 根据传入的属性数据计算角色战力（不查询数据库）
     * @param array $characterData 角色属性数据
     * @return int 战力值
     */
    public static function calculateCharacterPowerFromData($characterData)
    {
        // 计算基础战力
        $basePower = self::calculateBasePower($characterData);

        // 计算境界倍率
        $realmMultiplier = self::calculateRealmMultiplier(isset($characterData['realm_level']) ? $characterData['realm_level'] : 1);

        // 角色战力 = 基础战力 × 境界倍率
        // 注意：这里不包含装备战力，因为属性数据已经是包含装备加成的总值
        $totalPower = $basePower * $realmMultiplier;

        return round($totalPower);
    }
}

// API处理
if (!isLoggedIn()) {
    echo json_encode(array('success' => false, 'message' => '用户未登录'));
    exit;
}

$action = isset($_POST['action']) ? $_POST['action'] : (isset($_GET['action']) ? $_GET['action'] : '');

try {
    $pdo = getDatabase();
    $character_id = get_character_id();

    switch ($action) {
        case 'get_character_power':
            // 🔧 修复：直接使用现有的属性计算函数，避免查询不存在的表
            try {
                // 获取角色基础数据 - 🔧 修复：characters表的主键是id，不是character_id
                $stmt = $pdo->prepare("SELECT * FROM characters WHERE id = ?");
                $stmt->execute(array($character_id));
                $characterData = $stmt->fetch(PDO::FETCH_ASSOC);

                if (!$characterData) {
                    throw new Exception('角色数据不存在');
                }

                // 🔧 修复：使用正确的函数获取角色总属性，包含套装加成
                require_once dirname(__DIR__) . '/includes/equipment_stats_manager.php';
                $equipmentBonus = EquipmentStatsManager::getAllEquipmentStats($pdo, $character_id);
                $setBonus = getCharacterSetBonus($pdo, $character_id);
                $attributes = calculateCharacterAttributes($characterData, $equipmentBonus, [], [], $setBonus);

                // 合并数据，包含角色基础信息和计算后的属性
                $mergedData = array_merge($characterData, $attributes);

                // 使用简化的战力计算方法，直接基于总属性计算
                $totalPower = PowerRating::calculateCharacterPowerFromData($mergedData);

                echo json_encode(array(
                    'success' => true,
                    'power_rating' => $totalPower,
                    'debug_info' => array(
                        'character_id' => $character_id,
                        'realm_level' => isset($mergedData['realm_level']) ? $mergedData['realm_level'] : 'unknown'
                    )
                ));
            } catch (Exception $e) {
                error_log("战力计算错误: " . $e->getMessage());
                echo json_encode(array(
                    'success' => false,
                    'message' => '战力计算失败: ' . $e->getMessage()
                ));
            }
            break;

        case 'get_equipment_power':
            $equipmentId = isset($_POST['equipment_id']) ? $_POST['equipment_id'] : (isset($_GET['equipment_id']) ? $_GET['equipment_id'] : 0);

            $equipment = null;
            $customAttributes = null;

            // 首先尝试作为背包ID查询（优先，因为有calculated_attributes）
            $stmt = $pdo->prepare("
                SELECT gi.*, ui.custom_attributes
                FROM user_inventories ui 
                JOIN game_items gi ON ui.item_id = gi.id 
                WHERE ui.id = ? AND ui.character_id = ?
            ");
            $stmt->execute(array($equipmentId, $character_id));
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($result) {
                $equipment = $result;
                if (!empty($result['custom_attributes'])) {
                    $customAttributes = json_decode($result['custom_attributes'], true);
                }
            } else {
                // 如果没找到，尝试作为游戏物品ID查询
                $stmt = $pdo->prepare("SELECT * FROM game_items WHERE id = ?");
                $stmt->execute(array($equipmentId));
                $equipment = $stmt->fetch(PDO::FETCH_ASSOC);
            }

            if (!$equipment) {
                throw new Exception('装备不存在');
            }

            // 🔧 如果有calculated_attributes，使用它们覆盖基础属性
            if ($customAttributes && isset($customAttributes['calculated_attributes']) && is_array($customAttributes['calculated_attributes'])) {
                $calculatedAttrs = $customAttributes['calculated_attributes'];
                error_log("战力计算使用calculated_attributes: " . json_encode($calculatedAttrs));

                // 将calculated_attributes中的属性覆盖到装备数据中
                foreach ($calculatedAttrs as $key => $value) {
                    if (is_numeric($value)) {
                        $equipment[$key] = $value;
                    }
                }
            }

            $power = PowerRating::calculateSingleEquipmentPower($equipment);

            echo json_encode(array(
                'success' => true,
                'power_rating' => $power,
                'debug_version' => 'v4.0_with_calculated_attributes',
                'used_calculated_attributes' => ($customAttributes && isset($customAttributes['calculated_attributes']))
            ));
            break;

        case 'check_better_equipment':
            $slot = isset($_POST['slot']) ? $_POST['slot'] : (isset($_GET['slot']) ? $_GET['slot'] : '');

            if (empty($slot)) {
                // 🔧 修复：根据项目实际设计，使用6个武器槽位 + 防具槽位
                $allBetterItems = array();

                // === 武器槽位检查 (6个槽位) ===
                // 获取当前所有武器槽位的装备
                $stmt = $pdo->prepare("
                    SELECT ce.slot_index, gi.*, ui.custom_attributes
                    FROM character_equipment ce
                    JOIN game_items gi ON ce.item_id = gi.id
                    LEFT JOIN user_inventories ui ON ce.inventory_item_id = ui.id
                    WHERE ce.character_id = ? AND ce.slot_type = 'weapon' AND ce.item_id > 0
                ");
                $stmt->execute(array($character_id));
                $currentWeapons = $stmt->fetchAll(PDO::FETCH_ASSOC);

                // 🔧 修复：计算当前最低武器战力（作为比较基准）
                $minCurrentWeaponPower = PHP_INT_MAX; // 初始化为最大值
                $weaponPowerDetails = array(); // 用于调试

                foreach ($currentWeapons as $weapon) {
                    // 🔧 确保每个武器都正确处理calculated_attributes
                    if (!empty($weapon['custom_attributes'])) {
                        $customAttributes = json_decode($weapon['custom_attributes'], true);
                        if ($customAttributes && isset($customAttributes['calculated_attributes']) && is_array($customAttributes['calculated_attributes'])) {
                            $calculatedAttrs = $customAttributes['calculated_attributes'];
                            foreach ($calculatedAttrs as $key => $value) {
                                if (is_numeric($value)) {
                                    $weapon[$key] = $value;
                                }
                            }
                        }
                    }

                    $weaponPower = PowerRating::calculateSingleEquipmentPower($weapon);
                    $weaponPowerDetails[] = array(
                        'slot' => isset($weapon['slot_index']) ? $weapon['slot_index'] : 'unknown',
                        'name' => isset($weapon['item_name']) ? $weapon['item_name'] : 'unknown',
                        'power' => $weaponPower,
                        'has_custom_attrs' => !empty($weapon['custom_attributes'])
                    );

                    if ($weaponPower < $minCurrentWeaponPower) {
                        $minCurrentWeaponPower = $weaponPower;
                    }
                }
                // 如果没有武器，设为0
                if ($minCurrentWeaponPower === PHP_INT_MAX) {
                    $minCurrentWeaponPower = 0;
                }

                // 🔧 获取背包中所有武器 - 排除已装备的
                $stmt = $pdo->prepare("
                    SELECT ui.id as inventory_id, gi.*, ui.custom_attributes
                    FROM user_inventories ui
                    JOIN game_items gi ON ui.item_id = gi.id
                    LEFT JOIN character_equipment ce ON ce.inventory_item_id = ui.id AND ce.character_id = ?
                    WHERE ui.character_id = ? AND gi.item_type = 'weapon' AND ce.inventory_item_id IS NULL
                ");
                $stmt->execute(array($character_id, $character_id));
                $inventoryWeapons = $stmt->fetchAll(PDO::FETCH_ASSOC);

                foreach ($inventoryWeapons as $item) {
                    // 处理calculated_attributes
                    if (!empty($item['custom_attributes'])) {
                        $customAttributes = json_decode($item['custom_attributes'], true);
                        if ($customAttributes && isset($customAttributes['calculated_attributes']) && is_array($customAttributes['calculated_attributes'])) {
                            $calculatedAttrs = $customAttributes['calculated_attributes'];
                            foreach ($calculatedAttrs as $key => $value) {
                                if (is_numeric($value)) {
                                    $item[$key] = $value;
                                }
                            }
                        }
                    }

                    $itemPower = PowerRating::calculateSingleEquipmentPower($item);
                    // 🔧 关键修复：只要背包武器战力比当前最低武器战力高，就显示箭头
                    if ($itemPower > $minCurrentWeaponPower) {
                        $allBetterItems[] = $item['inventory_id'];
                    }
                }

                // === 防具槽位检查 ===
                $armorSlots = array('chest', 'legs', 'feet', 'ring', 'necklace', 'bracelet');
                foreach ($armorSlots as $armorSlot) {
                    // 获取当前装备
                    $stmt = $pdo->prepare("
                        SELECT gi.*, ui.custom_attributes
                        FROM character_equipment ce
                        JOIN game_items gi ON ce.item_id = gi.id
                        LEFT JOIN user_inventories ui ON ce.inventory_item_id = ui.id
                        WHERE ce.character_id = ? AND gi.slot_type = ?
                    ");
                    $stmt->execute(array($character_id, $armorSlot));
                    $currentArmor = $stmt->fetch(PDO::FETCH_ASSOC);

                    // 处理当前装备的calculated_attributes
                    if ($currentArmor && !empty($currentArmor['custom_attributes'])) {
                        $customAttributes = json_decode($currentArmor['custom_attributes'], true);
                        if ($customAttributes && isset($customAttributes['calculated_attributes']) && is_array($customAttributes['calculated_attributes'])) {
                            $calculatedAttrs = $customAttributes['calculated_attributes'];
                            foreach ($calculatedAttrs as $key => $value) {
                                if (is_numeric($value)) {
                                    $currentArmor[$key] = $value;
                                }
                            }
                        }
                    }

                    // 获取背包中该槽位的装备 - 排除已装备的
                    $stmt = $pdo->prepare("
                        SELECT ui.id as inventory_id, gi.*, ui.custom_attributes
                        FROM user_inventories ui
                        JOIN game_items gi ON ui.item_id = gi.id
                        LEFT JOIN character_equipment ce ON ce.item_id = gi.id AND ce.character_id = ?
                        WHERE ui.character_id = ? AND gi.slot_type = ? AND ce.item_id IS NULL
                    ");
                    $stmt->execute(array($character_id, $character_id, $armorSlot));
                    $inventoryArmors = $stmt->fetchAll(PDO::FETCH_ASSOC);

                    $currentArmorPower = $currentArmor ? PowerRating::calculateSingleEquipmentPower($currentArmor) : 0;

                    foreach ($inventoryArmors as $item) {
                        // 处理calculated_attributes
                        if (!empty($item['custom_attributes'])) {
                            $customAttributes = json_decode($item['custom_attributes'], true);
                            if ($customAttributes && isset($customAttributes['calculated_attributes']) && is_array($customAttributes['calculated_attributes'])) {
                                $calculatedAttrs = $customAttributes['calculated_attributes'];
                                foreach ($calculatedAttrs as $key => $value) {
                                    if (is_numeric($value)) {
                                        $item[$key] = $value;
                                    }
                                }
                            }
                        }

                        $itemPower = PowerRating::calculateSingleEquipmentPower($item);
                        if ($itemPower > $currentArmorPower) {
                            $allBetterItems[] = $item['inventory_id'];
                        }
                    }
                }

                echo json_encode(array(
                    'success' => true,
                    'better_items' => array_unique($allBetterItems),
                    'slot' => 'all_equipment',
                    'debug_info' => array(
                        'min_weapon_power' => $minCurrentWeaponPower,
                        'total_better_items' => count(array_unique($allBetterItems)),
                        'current_weapons_count' => count($currentWeapons),
                        'inventory_weapons_count' => count($inventoryWeapons),
                        'character_id' => $character_id,
                        'weapon_power_details' => $weaponPowerDetails // 🔧 新增：详细的武器战力信息
                    )
                ));
            } else {
                // 检查指定槽位 - 暂时保留原逻辑以兼容现有调用
                if (!in_array($slot, array('sword', 'fan'))) {
                    echo json_encode(array(
                        'success' => false,
                        'message' => '只支持武器装备战力对比'
                    ));
                    break;
                }

                // 获取当前装备 - 通过背包记录获取calculated_attributes
                $stmt = $pdo->prepare("
                    SELECT gi.*, ui.custom_attributes
                    FROM character_equipment ce
                    JOIN game_items gi ON ce.item_id = gi.id
                    LEFT JOIN user_inventories ui ON ce.inventory_item_id = ui.id
                    WHERE ce.character_id = ? AND gi.slot_type = ?
                ");
                $stmt->execute(array($character_id, $slot));
                $currentEquipment = $stmt->fetch(PDO::FETCH_ASSOC);

                // 🔧 处理当前装备的calculated_attributes
                if ($currentEquipment && !empty($currentEquipment['custom_attributes'])) {
                    $customAttributes = json_decode($currentEquipment['custom_attributes'], true);
                    if ($customAttributes && isset($customAttributes['calculated_attributes']) && is_array($customAttributes['calculated_attributes'])) {
                        $calculatedAttrs = $customAttributes['calculated_attributes'];
                        // 将calculated_attributes中的属性覆盖到装备数据中
                        foreach ($calculatedAttrs as $key => $value) {
                            if (is_numeric($value)) {
                                $currentEquipment[$key] = $value;
                            }
                        }
                    }
                }

                // 获取背包中该槽位的所有装备 - 使用正确的表连接，排除已装备的，包含custom_attributes
                $stmt = $pdo->prepare("
                    SELECT ui.id as inventory_id, gi.*, ui.custom_attributes
                    FROM user_inventories ui
                    JOIN game_items gi ON ui.item_id = gi.id
                    LEFT JOIN character_equipment ce ON ce.item_id = gi.id AND ce.character_id = ?
                    WHERE ui.character_id = ? AND gi.slot_type = ? AND ce.item_id IS NULL
                ");
                $stmt->execute(array($character_id, $character_id, $slot));
                $inventoryItems = $stmt->fetchAll(PDO::FETCH_ASSOC);

                $betterItems = array();
                $currentPower = $currentEquipment ? PowerRating::calculateSingleEquipmentPower($currentEquipment) : 0;

                foreach ($inventoryItems as $item) {
                    // 🔧 处理calculated_attributes
                    if (!empty($item['custom_attributes'])) {
                        $customAttributes = json_decode($item['custom_attributes'], true);
                        if ($customAttributes && isset($customAttributes['calculated_attributes']) && is_array($customAttributes['calculated_attributes'])) {
                            $calculatedAttrs = $customAttributes['calculated_attributes'];
                            // 将calculated_attributes中的属性覆盖到装备数据中
                            foreach ($calculatedAttrs as $key => $value) {
                                if (is_numeric($value)) {
                                    $item[$key] = $value;
                                }
                            }
                        }
                    }

                    $itemPower = PowerRating::calculateSingleEquipmentPower($item);
                    if ($itemPower > $currentPower) {
                        $betterItems[] = $item['inventory_id']; // 使用背包ID
                    }
                }

                echo json_encode(array(
                    'success' => true,
                    'better_items' => $betterItems,
                    'current_power' => $currentPower,
                    'slot' => $slot
                ));
            }
            break;

        case 'check_weapon_slot_comparison':
            $weaponId = isset($_POST['weapon_id']) ? $_POST['weapon_id'] : (isset($_GET['weapon_id']) ? $_GET['weapon_id'] : 0);

            if (empty($weaponId)) {
                throw new Exception('武器ID不能为空');
            }

            // 获取该武器的战力
            $stmt = $pdo->prepare("SELECT * FROM game_items WHERE id = ?");
            $stmt->execute(array($weaponId));
            $selectedWeapon = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$selectedWeapon) {
                throw new Exception('武器不存在');
            }

            $selectedWeaponPower = PowerRating::calculateSingleEquipmentPower($selectedWeapon);

            // 获取当前所有装备的战力
            $stmt = $pdo->prepare("
                SELECT gi.slot_type, gi.*
                FROM character_equipment ce
                JOIN game_items gi ON ce.item_id = gi.id
                WHERE ce.character_id = ?
            ");
            $stmt->execute(array($character_id));
            $currentEquipments = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $comparisons = array();
            $slotTypes = array('chest', 'legs', 'feet', 'ring', 'necklace', 'bracelet');

            foreach ($slotTypes as $slotType) {
                $currentEquipment = null;
                foreach ($currentEquipments as $eq) {
                    if ($eq['slot_type'] === $slotType) {
                        $currentEquipment = $eq;
                        break;
                    }
                }

                $currentPower = $currentEquipment ? PowerRating::calculateSingleEquipmentPower($currentEquipment) : 0;
                $isWorse = $currentPower < $selectedWeaponPower;

                $comparisons[$slotType] = array(
                    'current_power' => $currentPower,
                    'weapon_power' => $selectedWeaponPower,
                    'is_worse' => $isWorse
                );
            }

            echo json_encode(array(
                'success' => true,
                'comparisons' => $comparisons,
                'weapon_power' => $selectedWeaponPower
            ));
            break;

        case 'compare_weapon_slots':
            $weaponId = isset($_POST['weapon_id']) ? $_POST['weapon_id'] : (isset($_GET['weapon_id']) ? $_GET['weapon_id'] : 0);

            if (empty($weaponId)) {
                throw new Exception('武器ID不能为空');
            }

            // 🔧 获取要装备武器的数据和战力 - 包含calculated_attributes
            $stmt = $pdo->prepare("
                SELECT gi.*, ui.custom_attributes
                FROM user_inventories ui
                JOIN game_items gi ON ui.item_id = gi.id
                WHERE ui.id = ? AND ui.character_id = ?
            ");
            $stmt->execute(array($weaponId, $character_id));
            $selectedWeapon = $stmt->fetch(PDO::FETCH_ASSOC);

            // 🔧 处理selected武器的calculated_attributes
            if ($selectedWeapon && !empty($selectedWeapon['custom_attributes'])) {
                $customAttributes = json_decode($selectedWeapon['custom_attributes'], true);
                if ($customAttributes && isset($customAttributes['calculated_attributes']) && is_array($customAttributes['calculated_attributes'])) {
                    $calculatedAttrs = $customAttributes['calculated_attributes'];
                    foreach ($calculatedAttrs as $key => $value) {
                        if (is_numeric($value)) {
                            $selectedWeapon[$key] = $value;
                        }
                    }
                }
            }

            if (!$selectedWeapon) {
                throw new Exception('武器不存在');
            }

            $selectedWeaponPower = PowerRating::calculateSingleEquipmentPower($selectedWeapon);

            // 获取所有武器槽位的装备情况
            $slotComparisons = array();
            for ($slotIndex = 1; $slotIndex <= 6; $slotIndex++) {
                // 🔧 获取该槽位当前装备的武器 - 包含calculated_attributes
                $stmt = $pdo->prepare("
                    SELECT gi.*, ui.custom_attributes
                    FROM character_equipment ce
                    JOIN game_items gi ON ce.item_id = gi.id
                    LEFT JOIN user_inventories ui ON ce.inventory_item_id = ui.id
                    WHERE ce.character_id = ? AND ce.slot_index = ?
                ");
                $stmt->execute(array($character_id, $slotIndex));
                $currentWeapon = $stmt->fetch(PDO::FETCH_ASSOC);

                // 🔧 处理当前武器的calculated_attributes
                if ($currentWeapon && !empty($currentWeapon['custom_attributes'])) {
                    $customAttributes = json_decode($currentWeapon['custom_attributes'], true);
                    if ($customAttributes && isset($customAttributes['calculated_attributes']) && is_array($customAttributes['calculated_attributes'])) {
                        $calculatedAttrs = $customAttributes['calculated_attributes'];
                        foreach ($calculatedAttrs as $key => $value) {
                            if (is_numeric($value)) {
                                $currentWeapon[$key] = $value;
                            }
                        }
                    }
                }

                $currentPower = $currentWeapon ? PowerRating::calculateSingleEquipmentPower($currentWeapon) : 0;

                // 判断比较结果
                $comparisonResult = 'equal';
                if ($selectedWeaponPower > $currentPower) {
                    $comparisonResult = 'better';
                } elseif ($selectedWeaponPower < $currentPower) {
                    $comparisonResult = 'worse';
                }

                $slotComparisons[$slotIndex] = array(
                    'slot_index' => $slotIndex,
                    'current_power' => $currentPower,
                    'selected_power' => $selectedWeaponPower,
                    'comparison' => $comparisonResult,
                    'has_weapon' => $currentWeapon ? true : false,
                    'current_weapon_name' => $currentWeapon ? $currentWeapon['name'] : null
                );
            }

            echo json_encode(array(
                'success' => true,
                'selected_weapon_power' => $selectedWeaponPower,
                'slot_comparisons' => $slotComparisons
            ));
            break;

        case 'test_equipment_power':
            // 测试装备战力计算
            $equipmentData = isset($_POST['equipment_data']) ? json_decode($_POST['equipment_data'], true) : null;

            if (!$equipmentData) {
                throw new Exception('装备数据为空');
            }

            $power = PowerRating::calculateSingleEquipmentPower($equipmentData);

            echo json_encode(array(
                'success' => true,
                'power_rating' => $power,
                'equipment_name' => isset($equipmentData['name']) ? $equipmentData['name'] : '测试装备',
                'debug_info' => '请查看PHP错误日志获取详细计算过程'
            ));
            break;

        case 'calculate_character_power_from_stats':
            // 根据传入的属性数据计算角色战力
            $characterData = isset($_POST['character_data']) ? json_decode($_POST['character_data'], true) : null;

            if (!$characterData) {
                throw new Exception('角色数据为空');
            }

            // 直接使用传入的数据计算战力，不查询数据库
            $totalPower = PowerRating::calculateCharacterPowerFromData($characterData);

            echo json_encode(array(
                'success' => true,
                'power_rating' => $totalPower,
                'debug_info' => '根据属性数据计算的战力值'
            ));
            break;

        default:
            throw new Exception('未知的操作');
    }
} catch (Exception $e) {
    echo json_encode(array('success' => false, 'message' => $e->getMessage()));
}
