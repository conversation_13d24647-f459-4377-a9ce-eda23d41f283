/* 普通攻击动画样式 - 怪物技能模板版本 */

/* 动画容器 */
.normal-attack-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1000;
}

/* ========================================
 * 第一阶段：蓄力准备动画
 * ======================================== */

/* 蓄力核心 */
.normal-attack-charge-core {
    width: 30px;
    height: 30px;
    background: radial-gradient(circle, 
        #ffffff 0%, 
        #87CEEB 20%, 
        #4682B4 50%, 
        #1E90FF 70%,
        transparent 90%);
    border-radius: 50%;
    animation: normalAttackChargeCore 0.6s ease-out;
    box-shadow: 
        0 0 20px #87CEEB,
        0 0 40px rgba(135, 206, 235, 0.6);
}

@keyframes normalAttackChargeCore {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.2) rotate(0deg);
        box-shadow: 0 0 10px #87CEEB;
    }
    50% {
        opacity: 0.8;
        transform: translate(-50%, -50%) scale(1.2) rotate(180deg);
        box-shadow: 
            0 0 25px #4682B4,
            0 0 50px rgba(70, 130, 180, 0.8);
    }
    100% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.5) rotate(360deg);
        box-shadow: 
            0 0 30px #1E90FF,
            0 0 60px rgba(30, 144, 255, 0.6);
    }
}

/* 武器发光效果 */
.normal-attack-weapon-sprite {
    width: 50px;
    height: 50px;
    animation: normalAttackWeaponGlow 0.8s ease-out;
    filter: drop-shadow(0 0 15px #87CEEB) brightness(1.3);
}

@keyframes normalAttackWeaponGlow {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8) rotate(-30deg);
        filter: drop-shadow(0 0 10px #87CEEB) brightness(1);
    }
    50% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.1) rotate(0deg);
        filter: drop-shadow(0 0 20px #4682B4) brightness(1.5);
    }
    100% {
        opacity: 0.8;
        transform: translate(-50%, -50%) scale(1) rotate(10deg);
        filter: drop-shadow(0 0 15px #87CEEB) brightness(1.3);
    }
}

/* ========================================
 * 第二阶段：攻击飞行动画
 * ======================================== */

/* 攻击投射物 */
.normal-attack-projectile {
    width: 60px;
    height: 8px;
    background: linear-gradient(90deg, 
        transparent 0%,
        #87CEEB 10%,
        #4682B4 50%,
        #1E90FF 90%,
        transparent 100%);
    border-radius: 4px;
    animation: normalAttackProjectileFly 1s ease-out;
    filter: drop-shadow(0 0 10px #87CEEB);
}

/* ✅ 简化的飞行动画实现 - 直接使用 left/top 变化 */
@keyframes normalAttackProjectileFly {
    0% {
        opacity: 0;
        left: var(--startX);
        top: var(--startY);
        transform: translate(-50%, -50%) rotate(var(--angle)) scale(0.4);
        filter: drop-shadow(0 0 5px #87CEEB);
    }
    10% {
        opacity: 1;
        transform: translate(-50%, -50%) rotate(var(--angle)) scale(0.8);
        filter: drop-shadow(0 0 10px #4682B4);
    }
    90% {
        opacity: 0.9;
        left: var(--targetX);
        top: var(--targetY);
        transform: translate(-50%, -50%) rotate(var(--angle)) scale(1.2);
        filter: drop-shadow(0 0 15px #1E90FF);
    }
    100% {
        opacity: 0.3;
        left: var(--targetX);
        top: var(--targetY);
        transform: translate(-50%, -50%) rotate(var(--angle)) scale(0.8);
        filter: drop-shadow(0 0 8px #87CEEB);
    }
}

/* ========================================
 * 第三阶段：击中爆炸动画
 * ======================================== */

/* 爆炸效果 */
.normal-attack-explosion {
    width: 60px;
    height: 60px;
    background: radial-gradient(circle, 
        rgba(255, 255, 255, 0.8) 0%, 
        #87CEEB 25%, 
        #4682B4 50%, 
        #1E90FF 75%, 
        transparent 100%);
    border-radius: 50%;
    animation: normalAttackExplosion 0.6s ease-out;
    box-shadow: 
        0 0 25px #87CEEB,
        0 0 50px rgba(135, 206, 235, 0.7);
}

@keyframes normalAttackExplosion {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.2);
        box-shadow: 0 0 10px #87CEEB;
    }
    20% {
        opacity: 0.9;
        transform: translate(-50%, -50%) scale(1.2);
        box-shadow: 
            0 0 35px #4682B4,
            0 0 70px rgba(70, 130, 180, 0.9);
    }
    50% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.5);
        box-shadow: 
            0 0 40px #1E90FF,
            0 0 80px rgba(30, 144, 255, 0.8);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(2.5);
        box-shadow: 
            0 0 20px #87CEEB,
            0 0 40px rgba(135, 206, 235, 0.4);
    }
}

/* ========================================
 * 移动端适配
 * ======================================== */

@media (max-width: 768px) {
    .normal-attack-charge-core {
        width: 24px;
        height: 24px;
    }
    
    .normal-attack-weapon-sprite {
        width: 40px;
        height: 40px;
    }
    
    .normal-attack-projectile {
        width: 45px;
        height: 6px;
    }
    
    .normal-attack-explosion {
        width: 45px;
        height: 45px;
    }
} 