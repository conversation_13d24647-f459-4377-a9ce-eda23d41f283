<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta charset="UTF-8" />
        <!-- 移动端适配核心meta标签 -->
        <meta
            name="viewport"
            content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover"
        />

        <!-- 🔧 PWA模式底部白边强力修复 - 必须最早执行 -->
        <script src="assets/js/pwa-fix.js"></script>

        <!-- 🎛️ 全局调试开关 - 必须最早加载 -->
        <script src="assets/js/global-debug-switch.js"></script>

        <!-- 🔧 项目配置文件 - 必须早期加载 -->
        <script src="assets/js/config.js"></script>

        <!-- WebView优化配置 -->
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
        <meta name="screen-orientation" content="portrait" />
        <meta name="x5-orientation" content="portrait" />
        <meta name="full-screen" content="yes" />
        <meta name="x5-fullscreen" content="true" />
        <meta name="browsermode" content="application" />
        <meta name="x5-page-mode" content="app" />
        <!-- 禁用长按菜单 -->
        <meta name="format-detection" content="telephone=no,email=no,address=no" />
        <!-- 强制使用最新版本 -->
        <meta http-equiv="Cache-Control" content="no-siteapp" />
        <meta http-equiv="Cache-Control" content="no-transform" />
        <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
        <meta http-equiv="Pragma" content="no-cache" />
        <meta http-equiv="Expires" content="0" />
        <!-- 启用硬件加速 -->
        <meta name="renderer" content="webkit" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
        <!-- iOS图标 -->
        <link rel="apple-touch-icon" sizes="180x180" href="/assets/images/app-icon-180.png" />
        <link rel="apple-touch-icon" sizes="167x167" href="/assets/images/app-icon-167.png" />
        <link rel="apple-touch-icon" sizes="152x152" href="/assets/images/app-icon-152.png" />
        <link rel="apple-touch-icon" sizes="120x120" href="/assets/images/app-icon-120.png" />
        <!-- PWA支持 -->
        <link rel="manifest" href="manifest.json" />
        <meta name="theme-color" content="#d4af37" />

        <title>一念仙魔 -背包装备</title>
        <!-- 🔧 新增：引入全局样式文件 -->
        <link rel="stylesheet" href="assets/css/global.css" />
        <!-- 🔧 新增：引入独立的装备页面样式文件 -->
        <link rel="stylesheet" href="assets/css/equipment-integrated.css" />
        <!-- 🔧 新增：引入独立的物品详情弹窗组件 -->
        <link rel="stylesheet" href="assets/css/item-detail-popup.css" />
        <!-- 🔥 引入战力系统样式 -->
        <link rel="stylesheet" href="assets/css/power-rating.css" />
        <!-- 引入通用导航样式 -->
        <link rel="stylesheet" href="assets/css/common-navigation.css" />
        <!-- 套装系统样式 -->
        <link rel="stylesheet" href="assets/css/equipment-sets.css" />

        <!-- 🔧 视口高度修复脚本 -->
        <script src="assets/js/viewport-height-fix.js"></script>

        <!-- 🔧 移除：不再需要组件加载器 -->
        <script src="assets/js/realm-system.js"></script>
        <!-- 🔧 新增：引入独立的物品详情弹窗组件 -->
        <script src="assets/js/item-detail-popup.js"></script>
        <!-- 🔧 新增：引入通用属性变化浮窗组件 -->
        <script src="assets/js/attribute-changes-popup.js"></script>
        <!-- 🔧 引入AJAX管理器 -->
        <script src="assets/js/ajax-manager.js"></script>
        <!-- 🔧 新增：引入独立的装备页面脚本文件 -->
        <script src="assets/js/equipment-integrated.js"></script>
        <!-- 🔥 引入战力系统脚本 -->
        <script src="assets/js/power-rating.js"></script>
        <!-- 套装系统脚本 -->
        <script src="assets/js/equipment-set-manager.js"></script>
        <!-- 🔑 全局登录检查系统 -->
        <script src="assets/js/auth-check.js"></script>
        <!-- 🎵 全局音乐管理器 -->
        <script src="assets/js/global-music-manager.js"></script>
    </head>
    <body>
        <div class="main-container">
            <!-- 🔧 修改：合并角色装备和武器配置到一个区域 -->
            <div class="character-equipment-section">
                <!-- 角色装备区域 -->
                <div class="character-section">
                    <!-- 弧形装备槽位容器 -->
                    <div class="equipment-arc">
                        <!-- 装备槽位按弧形排列 -->
                        <div class="equipment-slot" data-slot="ring" title="戒指">戒指</div>
                        <div class="equipment-slot" data-slot="bracers" title="护臂">护臂</div>
                        <div class="equipment-slot" data-slot="accessory" title="佩饰">佩饰</div>
                        <div class="equipment-slot" data-slot="chest" title="衣服">衣服</div>
                        <div class="equipment-slot" data-slot="belt" title="腰带">腰带</div>
                        <div class="equipment-slot" data-slot="boots" title="鞋子">鞋子</div>

                        <!-- 角色头像 -->
                        <div class="character-avatar" id="main-avatar">N</div>

                        <!-- 🔥 角色战力显示 - 位于角色脚下 -->
                        <div class="character-power-display" id="character-power-display">
                            <span class="power-icon">⚔️</span>
                            <span class="power-value" id="character-power-value">计算中...</span>
                        </div>
                    </div>
                </div>

                <!-- 武器配置区域 - 紧凑显示 -->
                <div class="weapon-section">
                    <div class="weapon-grid" id="weapon-slots">
                        <div class="weapon-slot" data-slot="1" title="武器槽位1">
                            <div>1</div>
                            <div class="weapon-durability"><div class="durability-bar" style="width: 0%"></div></div>
                        </div>
                        <div class="weapon-slot" data-slot="2" title="武器槽位2">
                            <div>2</div>
                            <div class="weapon-durability"><div class="durability-bar" style="width: 0%"></div></div>
                        </div>
                        <div class="weapon-slot" data-slot="3" title="武器槽位3">
                            <div>3</div>
                            <div class="weapon-durability"><div class="durability-bar" style="width: 0%"></div></div>
                        </div>
                        <div class="weapon-slot" data-slot="4" title="武器槽位4">
                            <div>4</div>
                            <div class="weapon-durability"><div class="durability-bar" style="width: 0%"></div></div>
                        </div>
                        <div class="weapon-slot" data-slot="5" title="武器槽位5">
                            <div>5</div>
                            <div class="weapon-durability"><div class="durability-bar" style="width: 0%"></div></div>
                        </div>
                        <div class="weapon-slot" data-slot="6" title="武器槽位6">
                            <div>6</div>
                            <div class="weapon-durability"><div class="durability-bar" style="width: 0%"></div></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 🔧 修改：重新设计背包区域布局 -->
            <div class="inventory-section">
                <!-- 背包标签页 -->
                <div class="tabs">
                    <button class="tab active" onclick="switchTab('all')">全部</button>
                    <button class="tab" onclick="switchTab('weapon')">武器</button>
                    <button class="tab" onclick="switchTab('equipment')">装备</button>
                    <button class="tab" onclick="switchTab('other')">其他</button>
                    <button class="tab" onclick="switchTab('sets')">套装</button>
                </div>

                <!-- 背包网格 -->
                <div class="inventory-grid" id="inventory-grid">
                    <div class="loading">正在加载...</div>
                </div>

                <!-- 套装显示区域 -->
                <div id="equipment-sets-container" class="equipment-sets-section" style="display: none">
                    <!-- 套装内容会由JavaScript动态插入 -->
                </div>

                <!-- 套装状态显示容器 -->
                <div id="set-status-container" class="set-status-section">
                    <!-- 套装状态会由equipment-set-manager.js动态插入 -->
                </div>

                <!-- 🔧 修改：背包管理按钮区域 - 单独一排 -->
                <div class="inventory-management" id="inventory-management">
                    <!-- 背包空间信息 -->
                    <div class="inventory-space-info" id="inventory-space-info">
                        <span class="space-icon">🎒</span>
                        <span class="space-count" id="space-count">0/30</span>
                        <button
                            class="space-expand-btn"
                            onclick="expandInventorySpace()"
                            title="使用芥子石扩展背包空间"
                        >
                            扩展
                        </button>
                    </div>

                    <!-- 整理按钮 -->
                    <button
                        class="inventory-organize-btn"
                        onclick="organizeInventory()"
                        title="自动整理背包，合并可堆叠的相同物品"
                    >
                        <span class="organize-icon">📦</span>
                        <span class="organize-text">整理</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 添加通用导航容器 -->
        <div id="common-navigation"></div>

        <!-- 底部导航栏会由 common-navigation.js 自动插入 -->
        <script src="assets/js/common-navigation.js"></script>

        <!-- 角色头像选择弹窗 -->
        <div id="avatarSelectorModal" class="avatar-modal" style="display: none">
            <div class="avatar-modal-overlay" onclick="closeAvatarSelector()"></div>
            <div class="avatar-modal-content">
                <div class="avatar-modal-header">
                    <h3>选择角色形象</h3>
                    <button class="avatar-modal-close" onclick="closeAvatarSelector()">×</button>
                </div>
                <div class="avatar-modal-body">
                    <!-- 免费头像区域 -->
                    <div class="avatar-section">
                        <div class="avatar-section-title">
                            <span class="avatar-section-icon">🆓</span>
                            <span>免费形象</span>
                        </div>
                        <div class="avatar-grid" id="freeAvatarGrid">
                            <!-- 免费头像会通过JavaScript动态加载 -->
                        </div>
                    </div>

                    <!-- 付费头像区域 -->
                    <div class="avatar-section">
                        <div class="avatar-section-title">
                            <span class="avatar-section-icon">💎</span>
                            <span>精美形象</span>
                            <small class="avatar-section-desc">（付费解锁）</small>
                        </div>
                        <div class="avatar-grid" id="vipAvatarGrid">
                            <!-- 付费头像会通过JavaScript动态加载 -->
                        </div>
                    </div>
                </div>
                <div class="avatar-modal-footer">
                    <button class="avatar-modal-btn avatar-modal-btn-cancel" onclick="closeAvatarSelector()">
                        取消
                    </button>
                    <button class="avatar-modal-btn avatar-modal-btn-confirm" onclick="confirmAvatarChange()">
                        确定
                    </button>
                </div>
            </div>
        </div>
    </body>
</html>
