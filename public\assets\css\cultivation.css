/* 修炼界面专用背景 */
body.cultivation-page {
    background: url('../images/cultivation_bg_2.jpeg') no-repeat center center fixed;
    background-size: cover;
    position: relative;
}

body.cultivation-page::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    z-index: -1;
}

        /* 主要内容区域 */
        .main-container {
            padding: 8px 8px 80px 8px;
            height: 100vh;
            position: relative;
            z-index: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        /* 顶部标题栏 */
        .header {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.12), rgba(255, 255, 255, 0.04));
            border-radius: 12px;
            padding: 8px 12px;
            margin-bottom: 8px;
            border: 1px solid rgba(212, 175, 55, 0.4);
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 40px;
        }
        
        .header-title {
            font-size: 16px;
            font-weight: bold;
            color: #d4af37;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .back-btn {
            background: rgba(212, 175, 55, 0.2);
            border: 1px solid rgba(212, 175, 55, 0.5);
            border-radius: 8px;
            color: #d4af37;
            padding: 4px 8px;
            font-size: 12px;
            cursor: pointer;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 3px;
        }

        /* 修炼界面主体 */
        .cultivation-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 8px;
            overflow: hidden;
        }

        /* 顶部境界信息区域 */
        .realm-section {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.03));
            border-radius: 12px;
            padding: 15px;
            border: 1px solid rgba(76, 175, 80, 0.4);
            backdrop-filter: blur(8px);
            min-height: 120px;
            position: relative;
            overflow: visible;
        }

        .realm-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(76, 175, 80, 0.15) 0%, transparent 70%);
            animation: breathe 4s ease-in-out infinite;
            pointer-events: none;
        }

        @keyframes breathe {
            0%, 100% {
                opacity: 0.4;
                transform: scale(1);
            }
            50% {
                opacity: 0.7;
                transform: scale(1.1);
            }
        }

        .realm-info {
            position: relative;
            z-index: 2;
            text-align: center;
        }
        
        .current-realm {
            font-size: 16px;
            color: #4CAF50;
            font-weight: bold;
            margin-bottom: 3px;
            text-shadow: 0 0 10px rgba(76, 175, 80, 0.6);
        }
        
        .realm-level {
            font-size: 10px;
            color: #bdc3c7;
            margin-bottom: 6px;
        }
        
        .qi-progress {
            margin: 8px 0;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.6), rgba(26, 26, 46, 0.8));
            border-radius: 15px;
            overflow: hidden;
            border: 2px solid rgba(76, 175, 80, 0.6);
            position: relative;
            box-shadow: 
                inset 0 2px 4px rgba(0, 0, 0, 0.3),
                0 2px 8px rgba(76, 175, 80, 0.3);
        }
        
        .progress-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, 
                transparent 0%, 
                rgba(76, 175, 80, 0.15) 25%, 
                rgba(139, 195, 74, 0.25) 50%, 
                rgba(76, 175, 80, 0.15) 75%, 
                transparent 100%);
            animation: progressGlow 3s ease-in-out infinite;
            pointer-events: none;
        }
        
        @keyframes progressGlow {
            0%, 100% { opacity: 0.4; }
            50% { opacity: 0.8; }
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, 
                #4CAF50 0%, 
                #66BB6A 25%, 
                #81C784 50%, 
                #66BB6A 75%, 
                #4CAF50 100%);
            width: 0%;
            transition: width 1.2s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            border-radius: 13px;
            box-shadow: 
                0 0 12px rgba(76, 175, 80, 0.7),
                inset 0 1px 0 rgba(255, 255, 255, 0.3),
                inset 0 -1px 0 rgba(0, 0, 0, 0.2);
        }
        
        .progress-fill::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            right: 2px;
            height: 6px;
            background: linear-gradient(90deg, 
                transparent, 
                rgba(255, 255, 255, 0.4), 
                transparent);
            border-radius: 8px;
        }
        
        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, 
                transparent, 
                rgba(255, 255, 255, 0.6), 
                transparent);
            animation: shimmer 2.5s infinite linear;
        }
        
        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }
        
        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 11px;
            color: #fff;
            font-weight: bold;
            text-shadow: 
                0 1px 2px rgba(0, 0, 0, 0.8),
                0 0 4px rgba(0, 0, 0, 0.6),
                0 0 8px rgba(76, 175, 80, 0.5);
            z-index: 10;
            letter-spacing: 0.5px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
        }

        .breakthrough-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 8px;
            font-size: 11px;
        }

        .success-rate {
            color: #4CAF50;
            font-weight: bold;
        }

        /* 中部功能区域 */
        .functions-section {
            display: flex;
            gap: 8px;
            min-height: 100px;
        }

        .function-panel {
            flex: 1;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.03));
            border-radius: 12px;
            padding: 6px;
            border: 1px solid rgba(212, 175, 55, 0.3);
            backdrop-filter: blur(8px);
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .panel-title {
            font-size: 11px;
            font-weight: bold;
            color: #d4af37;
            margin-bottom: 6px;
            text-align: center;
        }

        /* 资源面板 */
        .resources-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4px;
            flex: 1;
        }
        
        .resource-item {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 6px;
            padding: 6px 4px;
            text-align: center;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        
        .resource-icon {
            font-size: 12px;
            margin-bottom: 2px;
        }
        
        .resource-value {
            font-size: 10px;
            font-weight: bold;
            color: #d4af37;
            margin-bottom: 1px;
        }
        
        .resource-name {
            font-size: 7px;
            color: #bdc3c7;
        }

        /* 底部操作区域 */
        .actions-section {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.03));
            border-radius: 12px;
            padding: 6px;
            border: 1px solid rgba(212, 175, 55, 0.3);
            backdrop-filter: blur(8px);
            height: 120px;
            display: flex;
            flex-direction: column;
        }

        .action-buttons-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 4px;
            flex: 1;
        }

        .action-button {
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.8), rgba(76, 175, 80, 0.6));
            border: 1px solid rgba(76, 175, 80, 0.8);
            border-radius: 8px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 4px;
            position: relative;
            overflow: hidden;
            min-height: 60px;
        }
        
        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
        }

        .action-button:disabled {
            background: linear-gradient(135deg, #666, #555);
            border-color: #555;
            cursor: not-allowed;
            transform: none;
        }

        .action-button.cultivating {
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.8), rgba(255, 193, 7, 0.6));
            border-color: rgba(255, 193, 7, 0.8);
        }

        .action-button.cultivating::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: cultivating-shimmer 1.5s infinite;
        }

        @keyframes cultivating-shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }
        
        .btn-breakthrough {
            background: linear-gradient(135deg, rgba(156, 39, 176, 0.8), rgba(156, 39, 176, 0.6));
            border-color: rgba(156, 39, 176, 0.8);
        }

        .btn-breakthrough:hover {
            box-shadow: 0 4px 12px rgba(156, 39, 176, 0.4);
        }

        .btn-meditation {
            background: linear-gradient(135deg, rgba(33, 150, 243, 0.8), rgba(33, 150, 243, 0.6));
            border-color: rgba(33, 150, 243, 0.8);
        }

        .btn-meditation:hover {
            box-shadow: 0 4px 12px rgba(33, 150, 243, 0.4);
        }

        .btn-circulation {
            background: linear-gradient(135deg, rgba(255, 152, 0, 0.8), rgba(255, 152, 0, 0.6));
            border-color: rgba(255, 152, 0, 0.8);
        }

        .btn-circulation:hover {
            box-shadow: 0 4px 12px rgba(255, 152, 0, 0.4);
        }

        .action-icon {
            font-size: 14px;
            margin-bottom: 3px;
        }

        .action-text {
            font-size: 10px;
            font-weight: bold;
        }
        
        .action-description {
            font-size: 7px;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 1px;
            line-height: 1.2;
        }

        /* 修炼进度条 */
        .cultivation-progress {
            margin: 8px 0;
            display: none;
        }

        .cultivation-progress.active {
            display: block;
        }

        .cultivation-timer {
            text-align: center;
            font-size: 9px;
            color: #bdc3c7;
            margin-bottom: 4px;
        }
        
        /* 消息提示 */
        .message {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            padding: 8px 15px;
            border-radius: 15px;
            font-size: 11px;
            font-weight: bold;
            z-index: 1000;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(8px);
        }
        
        .message.success {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border: 1px solid #4CAF50;
        }
        
        .message.error {
            background: linear-gradient(135deg, #f44336, #da190b);
            color: white;
            border: 1px solid #f44336;
        }

        .message.info {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            color: white;
            border: 1px solid #2196F3;
        }

        /* 横屏适配 */
        @media (orientation: landscape) and (max-height: 500px) {
            .main-container {
                padding: 4px 4px 60px 4px;
            }
            
            .header {
                height: 32px;
                padding: 4px 8px;
                margin-bottom: 4px;
            }
            
            .cultivation-container {
                gap: 4px;
            }
            
            .realm-section {
                height: 80px;
            }
            
            .functions-section {
                min-height: 80px;
            }
            
            .actions-section {
                height: 120px;
            }
        }

        /* 修炼状态指示器 */
        .cultivation-status {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: bold;
            display: none;
        }

        .cultivation-status.active {
            display: block;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            animation: statusPulse 2s infinite;
        }

        @keyframes statusPulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        /* 渡劫确认弹窗 */
        .breakthrough-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1001;
            backdrop-filter: blur(5px);
        }

        .breakthrough-modal-content {
            background: linear-gradient(135deg, rgba(26, 26, 46, 0.95), rgba(22, 33, 62, 0.95));
            border-radius: 15px;
            padding: 15px;
            width: 90%;
            max-width: 360px;
            max-height: 90vh;
            overflow-y: auto;
            border: 2px solid rgba(156, 39, 176, 0.6);
            box-shadow: 
                0 0 30px rgba(156, 39, 176, 0.4),
                inset 0 0 20px rgba(255, 255, 255, 0.1);
            text-align: center;
        }
        
        .breakthrough-title {
            font-size: 16px;
            font-weight: bold;
            color: #9c27b0;
            margin-bottom: 12px;
            text-shadow: 0 0 10px rgba(156, 39, 176, 0.5);
        }

        .breakthrough-info {
            margin-bottom: 20px;
            font-size: 13px;
            line-height: 1.6;
            color: #fff;
        }

        .realm-transition {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 8px;
            margin: 8px 0;
            border: 1px solid rgba(156, 39, 176, 0.3);
        }

        .realm-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 6px 0;
        }

        .realm-label {
            font-size: 12px;
            color: #bdc3c7;
            min-width: 70px;
            text-align: left;
        }

        .realm-value {
            font-size: 13px;
            color: #fff;
            font-weight: bold;
            flex: 1;
            text-align: right;
        }

        .success-rate-section {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 8px;
            margin: 10px 0;
            border: 1px solid rgba(76, 175, 80, 0.3);
        }

        .rate-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 8px 0;
        }
        
        .rate-label {
            font-size: 12px;
            color: #bdc3c7;
        }
        
        .rate-value {
            font-size: 14px;
            font-weight: bold;
        }
        
        .base-rate {
            color: #4CAF50;
        }

        .final-rate {
            color: #ffd700;
            font-size: 16px;
        }

        .risk-warning {
            background: rgba(255, 107, 107, 0.2);
            border-radius: 6px;
            padding: 8px;
            margin: 10px 0;
            border: 1px solid rgba(255, 107, 107, 0.4);
        }

        .risk-text {
            font-size: 11px;
            color: #ff6b6b;
            line-height: 1.4;
        }

        .breakthrough-pills {
            margin: 12px 0;
            padding: 10px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            border: 1px solid rgba(255, 215, 0, 0.3);
        }

        .pill-section-title {
            font-size: 12px;
            color: #ffd700;
            margin-bottom: 8px;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
        }

        .pill-list {
            display: flex;
            flex-direction: column;
            gap: 6px;
            max-height: 120px;
            overflow-y: auto;
            padding-right: 5px;
        }

        /* 丹药列表滚动条样式 */
        .pill-list::-webkit-scrollbar {
            width: 4px;
        }

        .pill-list::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 2px;
        }

        .pill-list::-webkit-scrollbar-thumb {
            background: rgba(255, 215, 0, 0.5);
            border-radius: 2px;
        }

        .pill-list::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 215, 0, 0.7);
        }

        .pill-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 12px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }

        .pill-item:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-1px);
        }

        .pill-item.selected {
            background: rgba(255, 215, 0, 0.2);
            border-color: #ffd700;
            box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
        }

        .pill-item.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pill-left {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            flex: 1;
        }

        .pill-name {
            font-size: 12px;
            color: #fff;
            font-weight: bold;
            margin-bottom: 2px;
        }

        .pill-effect {
            font-size: 10px;
            color: #4CAF50;
        }

        .pill-count {
            font-size: 11px;
            color: #ffd700;
            font-weight: bold;
            min-width: 40px;
            text-align: right;
        }

        .no-pills-message {
            text-align: center;
            color: #bdc3c7;
            font-size: 11px;
            padding: 20px;
            font-style: italic;
        }

        .breakthrough-buttons {
            display: flex;
            gap: 12px;
            justify-content: center;
            margin-top: 15px;
        }

        .breakthrough-button {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-size: 13px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 100px;
        }

        .breakthrough-confirm {
            background: linear-gradient(135deg, #9c27b0, #7b1fa2);
            color: white;
            box-shadow: 0 4px 15px rgba(156, 39, 176, 0.3);
        }

        .breakthrough-confirm:hover {
            background: linear-gradient(135deg, #7b1fa2, #6a1b9a);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(156, 39, 176, 0.4);
        }

        .breakthrough-cancel {
            background: linear-gradient(135deg, #666, #888);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 102, 102, 0.3);
        }

        .breakthrough-cancel:hover {
            background: linear-gradient(135deg, #777, #999);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(119, 119, 119, 0.4);
        }

        /* 功法加成提示 */
        .technique-bonus-tip {
            position: absolute;
            bottom: -30px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: #4CAF50;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 9px;
            white-space: nowrap;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .technique-info:hover .technique-bonus-tip {
            opacity: 1;
        }

        /* 修炼状态网格 */
        .cultivation-status-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 6px;
            flex: 1;
        }
        
        .status-item {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.4), rgba(26, 26, 46, 0.6));
            border-radius: 10px;
            padding: 6px 4px;
            text-align: center;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            border: 1px solid rgba(212, 175, 55, 0.3);
            box-shadow: 
                inset 0 1px 2px rgba(212, 175, 55, 0.1),
                0 2px 4px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }
        
        .status-item:hover {
            transform: translateY(-1px);
            box-shadow: 
                inset 0 1px 2px rgba(212, 175, 55, 0.2),
                0 4px 8px rgba(0, 0, 0, 0.3);
        }
        
        .status-icon {
            font-size: 14px;
            margin-bottom: 3px;
            filter: drop-shadow(0 0 4px rgba(212, 175, 55, 0.5));
        }
        
        .status-value {
            font-size: 10px;
            font-weight: bold;
            color: #ffd700;
            margin-bottom: 1px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
        }
        
        .status-name {
            font-size: 8px;
            color: #d4af37;
            font-weight: 500;
            text-shadow: 0 1px 1px rgba(0, 0, 0, 0.6);
        }

        /* 修炼进度区域 */
        .cultivation-progress-section {
            background: linear-gradient(135deg, rgba(255, 100, 0, 0.1), rgba(255, 50, 0, 0.05));
            border-radius: 12px;
            padding: 12px;
            border: 1px solid rgba(255, 100, 0, 0.4);
            backdrop-filter: blur(8px);
            box-shadow: 0 4px 12px rgba(255, 100, 0, 0.2);
            position: relative;
        }
        
        .cultivation-progress-section::before {
            content: '';
            position: absolute;
            top: -1px;
            left: -1px;
            right: -1px;
            bottom: -1px;
            background: linear-gradient(45deg, 
                rgba(255, 100, 0, 0.4) 0%, 
                rgba(255, 150, 0, 0.3) 25%, 
                rgba(255, 100, 0, 0.4) 50%, 
                rgba(255, 150, 0, 0.3) 75%, 
                rgba(255, 100, 0, 0.4) 100%);
            border-radius: 12px;
            z-index: -1;
            animation: cultivationBorderGlow 3s ease-in-out infinite;
        }
        
        @keyframes cultivationBorderGlow {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
        }
        
        .progress-title {
            font-size: 13px;
            font-weight: bold;
            color: #ff9800;
            text-align: center;
            margin-bottom: 10px;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
        }

        .progress-container {
            position: relative;
            margin-bottom: 12px;
        }

        /* 周天运行专用进度条样式 */
        .cultivation-progress-section .progress-bar {
            background: linear-gradient(135deg, 
                rgba(0, 0, 0, 0.4) 0%, 
                rgba(26, 26, 46, 0.5) 50%, 
                rgba(0, 0, 0, 0.4) 100%);
            border-radius: 15px;
            height: 16px;
            position: relative;
            overflow: hidden;
            border: 2px solid rgba(255, 100, 0, 0.6);
            box-shadow: 
                inset 0 2px 4px rgba(0, 0, 0, 0.3),
                0 2px 8px rgba(255, 100, 0, 0.3);
        }
        
        .cultivation-progress-section .progress-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, 
                transparent 0%, 
                rgba(255, 100, 0, 0.2) 25%, 
                rgba(255, 150, 0, 0.3) 50%, 
                rgba(255, 100, 0, 0.2) 75%, 
                transparent 100%);
            animation: cultivationFlow 1.5s linear infinite;
            pointer-events: none;
        }
        
        @keyframes cultivationFlow {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
        
        /* 添加第二层能量流动效果 */
        .cultivation-progress-section .progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, 
                transparent 0%, 
                rgba(255, 200, 100, 0.15) 20%, 
                transparent 40%, 
                rgba(255, 200, 100, 0.15) 60%, 
                transparent 80%, 
                rgba(255, 200, 100, 0.15) 100%);
            animation: cultivationPulse 3s ease-in-out infinite;
            pointer-events: none;
        }
        
        @keyframes cultivationPulse {
            0%, 100% { 
                opacity: 0.3; 
                transform: scale(1);
            }
            50% { 
                opacity: 0.7; 
                transform: scale(1.05);
            }
        }
        
        .cultivation-progress-section .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, 
                #ff5722 0%, 
                #ff9800 25%, 
                #ffc107 50%, 
                #ff9800 75%, 
                #ff5722 100%);
            width: 0%;
            transition: width 0.05s linear;
            position: relative;
            border-radius: 12px;
            box-shadow: 
                0 0 15px rgba(255, 100, 0, 0.8),
                inset 0 1px 0 rgba(255, 255, 255, 0.4),
                inset 0 -1px 0 rgba(0, 0, 0, 0.3);
            overflow: hidden;
            animation: cultivationBreathing 4s ease-in-out infinite;
        }
        
        @keyframes cultivationBreathing {
            0%, 100% { 
                filter: brightness(1) saturate(1);
                background-size: 100% 100%;
            }
            25% { 
                filter: brightness(1.1) saturate(1.2);
                background-size: 105% 105%;
            }
            50% { 
                filter: brightness(1.2) saturate(1.4);
                background-size: 110% 110%;
            }
            75% { 
                filter: brightness(1.1) saturate(1.2);
                background-size: 105% 105%;
            }
        }
        
        .cultivation-progress-section .progress-fill::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, 
                transparent, 
                rgba(255, 255, 255, 0.6), 
                transparent);
            animation: cultivationShine 2s ease-in-out infinite;
        }
        
        @keyframes cultivationShine {
            0% { left: -100%; opacity: 0; }
            20% { opacity: 1; }
            80% { opacity: 1; }
            100% { left: 100%; opacity: 0; }
        }
        
        .cultivation-progress-section .progress-fill::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            right: 2px;
            height: 6px;
            background: linear-gradient(90deg, 
                rgba(255, 255, 255, 0.6), 
                rgba(255, 255, 255, 0.2), 
                rgba(255, 255, 255, 0.6));
            border-radius: 8px;
            animation: cultivationHighlight 3s ease-in-out infinite;
        }
        
        @keyframes cultivationHighlight {
            0%, 100% { 
                opacity: 0.4;
                transform: scaleX(1);
            }
            50% { 
                opacity: 0.8;
                transform: scaleX(1.1);
            }
        }

        .progress-info {
            display: flex;
            justify-content: center;
            font-size: 11px;
            color: #ff9800;
            margin-top: 6px;
            font-weight: 500;
        }

        .progress-info span {
            display: flex;
            align-items: center;
            gap: 4px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        }

        /* 功法选择弹窗 */
        .technique-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1001;
            backdrop-filter: blur(5px);
        }

        .technique-modal-content {
            background: linear-gradient(135deg, rgba(26, 26, 46, 0.95), rgba(22, 33, 62, 0.95));
            border-radius: 20px;
            padding: 25px;
            width: 90%;
            max-width: 400px;
            max-height: 80vh;
            overflow-y: auto;
            border: 2px solid rgba(76, 175, 80, 0.6);
            box-shadow: 
                0 0 30px rgba(76, 175, 80, 0.4),
                inset 0 0 20px rgba(255, 255, 255, 0.1);
            text-align: center;
        }
        
        .technique-title {
            font-size: 18px;
            font-weight: bold;
            color: #4CAF50;
            margin-bottom: 15px;
            text-shadow: 0 0 10px rgba(76, 175, 80, 0.5);
        }

        .technique-list {
            margin: 15px 0;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .technique-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }

        .technique-item:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .technique-item.selected {
            background: rgba(76, 175, 80, 0.3);
            border-color: #4CAF50;
        }

        .technique-item.locked {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .technique-name {
            font-size: 14px;
            font-weight: bold;
            color: #fff;
            margin-bottom: 4px;
        }

        .technique-description {
            font-size: 11px;
            color: #bdc3c7;
            margin-bottom: 4px;
        }

        .technique-effects {
            font-size: 10px;
            color: #4CAF50;
        }

        .technique-cost {
            font-size: 10px;
            color: #ffd700;
            margin-top: 4px;
        }

        .technique-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 15px;
        }

        .technique-button {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .technique-confirm {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
        }

        .technique-confirm:hover:not(:disabled) {
            background: linear-gradient(135deg, #45a049, #3d8b40);
            transform: translateY(-2px);
        }

        .technique-confirm:disabled {
            background: linear-gradient(135deg, #666, #555);
            cursor: not-allowed;
        }

        .technique-cancel {
            background: linear-gradient(135deg, #666, #888);
            color: white;
        }

        .technique-cancel:hover {
            background: linear-gradient(135deg, #777, #999);
            transform: translateY(-2px);
        }

        /* 按钮样式更新 */
        .btn-technique {
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.8), rgba(76, 175, 80, 0.6));
            border-color: rgba(76, 175, 80, 0.8);
        }

        .btn-technique:hover {
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
        }

        @keyframes cultivationHighlight {
            0%, 100% { 
                opacity: 0.4;
                transform: scaleX(1);
            }
            50% { 
                opacity: 0.8;
                transform: scaleX(1.1);
            }
        }

        /* 周天运行进度条文字样式 */
        .cultivation-progress-section .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 10px;
            font-weight: bold;
            color: #fff;
            text-shadow: 
                0 1px 2px rgba(0, 0, 0, 0.8),
                0 0 8px rgba(255, 100, 0, 0.6);
            z-index: 2;
            animation: cultivationTextPulse 2s ease-in-out infinite;
        }
        
        @keyframes cultivationTextPulse {
            0%, 100% { 
                text-shadow: 
                    0 1px 2px rgba(0, 0, 0, 0.8),
                    0 0 8px rgba(255, 100, 0, 0.6);
                transform: translate(-50%, -50%) scale(1);
            }
            50% { 
                text-shadow: 
                    0 1px 2px rgba(0, 0, 0, 0.8),
                    0 0 15px rgba(255, 100, 0, 0.9);
                transform: translate(-50%, -50%) scale(1.05);
            }
        }

        /* 可点击的状态项样式 */
        .technique-detail-trigger:hover {
            cursor: pointer;
            border-color: rgba(79, 172, 254, 0.6);
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.5), rgba(26, 26, 46, 0.7));
            transform: translateY(-2px);
            box-shadow: 
                inset 0 1px 2px rgba(79, 172, 254, 0.3),
                0 6px 12px rgba(79, 172, 254, 0.2);
        }
        
        .efficiency-detail-trigger:hover {
            cursor: pointer;
            border-color: rgba(255, 193, 7, 0.6);
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.5), rgba(26, 26, 46, 0.7));
            transform: translateY(-2px);
            box-shadow: 
                inset 0 1px 2px rgba(255, 193, 7, 0.3),
                0 6px 12px rgba(255, 193, 7, 0.2);
        }

        /* 详情弹窗样式 */
        .detail-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1002;
            backdrop-filter: blur(5px);
            padding: 10px;
        }
        
        .detail-modal-content {
            background: linear-gradient(135deg, rgba(26, 26, 46, 0.95), rgba(22, 33, 62, 0.95));
            border-radius: 20px;
            width: 90%;
            max-width: 400px;
            max-height: 95vh;
            overflow: hidden;
            border: 2px solid rgba(79, 172, 254, 0.6);
            box-shadow: 
                0 0 30px rgba(79, 172, 254, 0.4),
                inset 0 0 20px rgba(255, 255, 255, 0.1);
            display: flex;
            flex-direction: column;
        }
        
        .detail-modal-header {
            background: linear-gradient(135deg, rgba(79, 172, 254, 0.8), rgba(0, 242, 254, 0.6));
            padding: 12px 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid rgba(79, 172, 254, 0.3);
            flex-shrink: 0;
        }
        
        .detail-modal-title {
            font-size: 16px;
            font-weight: bold;
            color: #fff;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }
        
        .detail-modal-close {
            background: none;
            border: none;
            color: #fff;
            font-size: 20px;
            cursor: pointer;
            padding: 0;
            width: 25px;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.3s ease;
        }
        
        .detail-modal-close:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.1);
        }
        
        .detail-modal-body {
            padding: 15px;
            overflow-y: visible;
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        /* 功法详情样式 */
        .technique-main-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding: 10px;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.3), rgba(26, 26, 46, 0.4));
            border-radius: 12px;
            border: 1px solid rgba(79, 172, 254, 0.3);
        }
        
        .technique-name-display {
            font-size: 16px;
            font-weight: bold;
            color: #4facfe;
            text-shadow: 0 0 10px rgba(79, 172, 254, 0.5);
        }
        
        .technique-level-display {
            background: linear-gradient(135deg, rgba(79, 172, 254, 0.8), rgba(0, 242, 254, 0.6));
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 11px;
            color: #fff;
            font-weight: bold;
        }
        
        .technique-progress-section {
            margin-bottom: 10px;
            padding: 10px;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.2), rgba(26, 26, 46, 0.3));
            border-radius: 12px;
            border: 1px solid rgba(76, 175, 80, 0.3);
        }
        
        .progress-label {
            font-size: 11px;
            color: #bdc3c7;
            margin-bottom: 5px;
            text-align: center;
        }
        
        .progress-container {
            position: relative;
            margin-bottom: 8px;
        }
        
        /* 功法详情弹窗中的进度条样式 */
        .technique-progress-section .progress-bar {
            width: 100%;
            height: 16px;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.6), rgba(26, 26, 46, 0.8));
            border-radius: 15px;
            overflow: hidden;
            border: 2px solid rgba(76, 175, 80, 0.6);
            position: relative;
            box-shadow: 
                inset 0 2px 4px rgba(0, 0, 0, 0.3),
                0 2px 8px rgba(76, 175, 80, 0.3);
        }
        
        .technique-progress-section .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, 
                #4CAF50 0%, 
                #66BB6A 25%, 
                #81C784 50%, 
                #66BB6A 75%, 
                #4CAF50 100%);
            width: 0%;
            transition: width 1.2s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            border-radius: 13px;
            box-shadow: 
                0 0 12px rgba(76, 175, 80, 0.7),
                inset 0 1px 0 rgba(255, 255, 255, 0.3),
                inset 0 -1px 0 rgba(0, 0, 0, 0.2);
        }
        
        .technique-progress-section .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 10px;
            color: #fff;
            font-weight: bold;
            text-shadow: 
                0 1px 2px rgba(0, 0, 0, 0.8),
                0 0 4px rgba(0, 0, 0, 0.6);
            z-index: 10;
            letter-spacing: 0.5px;
            pointer-events: none;
        }
        
        .technique-stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 6px;
            margin-bottom: 10px;
        }
        
        .stat-item {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.4), rgba(26, 26, 46, 0.6));
            border-radius: 10px;
            padding: 6px;
            text-align: center;
            border: 1px solid rgba(212, 175, 55, 0.3);
        }
        
        .stat-icon {
            font-size: 14px;
            margin-bottom: 3px;
        }
        
        .stat-label {
            font-size: 8px;
            color: #bdc3c7;
            margin-bottom: 2px;
        }
        
        .stat-value {
            font-size: 11px;
            font-weight: bold;
            color: #ffd700;
        }
        
        .technique-description-section {
            padding: 10px;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.2), rgba(26, 26, 46, 0.3));
            border-radius: 12px;
            border: 1px solid rgba(155, 89, 182, 0.3);
            flex: 1;
            min-height: 0;
        }
        
        .description-label {
            font-size: 11px;
            color: #d4af37;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .description-text {
            font-size: 10px;
            color: #ecf0f1;
            line-height: 1.3;
        }
        
        /* 效率详情样式 */
        .efficiency-summary {
            margin-bottom: 10px;
            padding: 12px;
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 152, 0, 0.05));
            border-radius: 15px;
            border: 2px solid rgba(255, 193, 7, 0.4);
            text-align: center;
        }
        
        .efficiency-total {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        .efficiency-icon {
            font-size: 24px;
            filter: drop-shadow(0 0 10px rgba(255, 193, 7, 0.6));
        }
        
        .efficiency-label {
            font-size: 11px;
            color: #bdc3c7;
            margin-bottom: 3px;
        }
        
        .efficiency-value {
            font-size: 18px;
            font-weight: bold;
            color: #ffd700;
            text-shadow: 0 0 10px rgba(255, 215, 0, 0.6);
        }
        
        .efficiency-breakdown {
            margin-bottom: 10px;
        }
        
        .breakdown-title {
            font-size: 13px;
            color: #d4af37;
            margin-bottom: 8px;
            font-weight: bold;
            text-align: center;
        }
        
        .efficiency-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 10px;
            margin-bottom: 5px;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.3), rgba(26, 26, 46, 0.4));
            border-radius: 10px;
            border: 1px solid rgba(79, 172, 254, 0.2);
        }
        
        .efficiency-source {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .source-icon {
            font-size: 14px;
        }
        
        .source-name {
            font-size: 11px;
            color: #ecf0f1;
        }
        
        .source-value {
            font-size: 11px;
            font-weight: bold;
            color: #4CAF50;
        }
        
        .efficiency-calculation {
            padding: 10px;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.4), rgba(26, 26, 46, 0.5));
            border-radius: 12px;
            border: 1px solid rgba(156, 39, 176, 0.3);
        }
        
        .calculation-title {
            font-size: 11px;
            color: #d4af37;
            margin-bottom: 6px;
            font-weight: bold;
            text-align: center;
        }
        
        .calculation-formula {
            font-size: 10px;
            color: #bdc3c7;
            line-height: 1.4;
        }
        
        .formula-line {
            margin-bottom: 3px;
        }
        
        .formula-example {
            margin-top: 6px;
            padding: 6px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 6px;
            color: #4CAF50;
            font-weight: bold;
            font-size: 9px;
        }

        .technique-item.locked {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* 已装备功法样式 */
        .technique-item.current-equipped {
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(255, 152, 0, 0.1));
            border: 2px solid rgba(255, 193, 7, 0.6);
            box-shadow: 
                0 0 15px rgba(255, 193, 7, 0.3),
                inset 0 0 10px rgba(255, 255, 255, 0.1);
            position: relative;
        }

        .technique-item.current-equipped:hover {
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.3), rgba(255, 152, 0, 0.2));
            border-color: rgba(255, 193, 7, 0.8);
            box-shadow: 
                0 0 20px rgba(255, 193, 7, 0.5),
                inset 0 0 15px rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }

        .technique-item.current-equipped::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, 
                rgba(255, 193, 7, 0.4) 0%, 
                rgba(255, 152, 0, 0.3) 25%, 
                rgba(255, 193, 7, 0.4) 50%, 
                rgba(255, 152, 0, 0.3) 75%, 
                rgba(255, 193, 7, 0.4) 100%);
            border-radius: 10px;
            z-index: -1;
            animation: equippedGlow 3s ease-in-out infinite;
        }

        @keyframes equippedGlow {
            0%, 100% { 
                opacity: 0.6;
                transform: scale(1);
            }
            50% { 
                opacity: 1;
                transform: scale(1.02);
            }
        }

        /* 功法头部布局 */
        .technique-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
        }

        /* 已装备标识 */
        .equipped-badge {
            background: linear-gradient(135deg, #ffd700, #ffb300);
            color: #333;
            font-size: 9px;
            font-weight: bold;
            padding: 2px 6px;
            border-radius: 10px;
            box-shadow: 
                0 2px 4px rgba(255, 215, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            text-shadow: none;
            animation: badgePulse 2s ease-in-out infinite;
        }

        @keyframes badgePulse {
            0%, 100% { 
                opacity: 0.9;
                transform: scale(1);
            }
            50% { 
                opacity: 1;
                transform: scale(1.05);
            }
        }

        /* 🆕 魂力状态指示器样式 */
        .soul-status-indicator {
            margin-top: 8px;
            padding: 6px 8px;
            background: rgba(231, 76, 60, 0.15);
            border: 1px solid rgba(231, 76, 60, 0.3);
            border-radius: 8px;
            backdrop-filter: blur(5px);
        }

        .soul-status-text {
            font-size: 10px;
            color: #e74c3c;
            text-align: center;
            font-weight: bold;
            margin-bottom: 4px;
        }

        .soul-recovery-bar {
            width: 100%;
            height: 12px;
            background: rgba(0, 0, 0, 0.4);
            border-radius: 6px;
            overflow: hidden;
            border: 1px solid rgba(231, 76, 60, 0.5);
            margin-bottom: 3px;
        }

        .soul-recovery-fill {
            height: 100%;
            background: linear-gradient(135deg, #e74c3c 0%, #ff6b6b 50%, #e74c3c 100%);
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 5px;
            box-shadow: 0 0 8px rgba(231, 76, 60, 0.5);
        }

        .soul-recovery-time {
            font-size: 9px;
            color: #bdc3c7;
            text-align: center;
            margin-top: 3px;
        }

        /* 魂力受损时的进度条样式 */
        .progress-bar.soul-damaged {
            border-color: rgba(231, 76, 60, 0.6);
        }

        .progress-bar.soul-damaged .progress-fill {
            background: linear-gradient(135deg, #e74c3c, #c0392b, #e74c3c);
            box-shadow: 0 0 12px rgba(231, 76, 60, 0.7);
        }

        /* 养魂丹按钮样式 */
        .soul-pill-button {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            border: 1px solid rgba(231, 76, 60, 0.6);
            color: #fff;
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .soul-pill-button:hover {
            background: linear-gradient(135deg, #c0392b, #a93226);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
        }

        .soul-pill-button:disabled {
            background: rgba(189, 195, 199, 0.3);
            border-color: rgba(189, 195, 199, 0.4);
            color: #95a5a6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .main-container {
                padding: 6px 6px 70px 6px;
            }
            
            .header {
                height: 36px;
                padding: 6px 10px;
                margin-bottom: 6px;
            }
            
            .header-title {
                font-size: 14px;
            }
            
            .back-btn {
                padding: 3px 6px;
                font-size: 11px;
            }
            
            .cultivation-container {
                gap: 6px;
            }
            
            /* 境界信息区域移动端优化 */
            .realm-section {
                padding: 12px 10px; /* 减少左右内边距 */
                min-height: auto; /* 移除最小高度限制 */
                border-radius: 10px;
            }
            
            .current-realm {
                font-size: 15px; /* 稍微减小字体 */
                margin-bottom: 3px;
            }
            
            .realm-level {
                font-size: 9px;
                margin-bottom: 5px;
            }
            
            .qi-progress {
                margin: 6px 0; /* 减少上下边距 */
            }
            
            .progress-bar {
                height: 18px; /* 稍微减小高度 */
            }
            
            .progress-text {
                font-size: 10px; /* 稍微减小字体 */
            }
            
            .breakthrough-info {
                margin-top: 6px;
                font-size: 10px; /* 减小字体 */
                flex-wrap: wrap; /* 允许换行 */
                gap: 5px;
            }
            
            .success-rate {
                font-size: 11px; /* 突破成功率字体 */
            }
            
            /* 魂力状态指示器移动端优化 */
            .soul-status-indicator {
                margin-top: 6px;
                padding: 5px 6px;
                border-radius: 6px;
            }
            
            .soul-status-text {
                font-size: 9px;
                margin-bottom: 3px;
            }
            
            .soul-recovery-bar {
                height: 10px;
                margin-bottom: 2px;
            }
            
            .soul-recovery-time {
                font-size: 8px;
                margin-top: 2px;
            }
            
            /* 功能区域移动端优化 */
            .functions-section {
                min-height: 80px;
            }
            
            .function-panel {
                padding: 4px;
                border-radius: 10px;
            }
            
            .panel-title {
                font-size: 10px;
                margin-bottom: 4px;
            }
            
            .status-item {
                padding: 4px 3px;
                border-radius: 8px;
            }
            
            .status-icon {
                font-size: 12px;
                margin-bottom: 2px;
            }
            
            .status-value {
                font-size: 9px;
            }
            
            .status-name {
                font-size: 7px;
            }
            
            /* 修炼进度区域移动端优化 */
            .cultivation-progress-section {
                padding: 10px;
                border-radius: 10px;
            }
            
            .progress-title {
                font-size: 12px;
                margin-bottom: 8px;
            }
            
            .cultivation-progress-section .progress-bar {
                height: 14px;
            }
            
            .cultivation-progress-section .progress-text {
                font-size: 9px;
            }
            
            .progress-info {
                font-size: 10px;
                margin-top: 5px;
            }
            
            /* 操作按钮区域移动端优化 */
            .actions-section {
                height: 100px;
                padding: 4px;
            }
            
            .action-buttons-grid {
                gap: 4px;
                grid-template-columns: 1fr 1fr 1fr;
            }
            
            .action-button {
                padding: 4px;
                border-radius: 8px;
            }
            
            .action-icon {
                font-size: 12px;
                margin-bottom: 2px;
            }
            
            .action-text {
                font-size: 9px;
            }
            
            .action-description {
                font-size: 6px;
                margin-top: 1px;
            }
        }
        
        /* 极小屏幕适配 (iPhone SE等) */
        @media (max-width: 375px) {
            .main-container {
                padding: 4px 4px 60px 4px;
            }
            
            .realm-section {
                padding: 8px 6px;
            }
            
            .current-realm {
                font-size: 14px;
            }
            
            .realm-level {
                font-size: 8px;
            }
            
            .progress-bar {
                height: 16px;
            }
            
            .progress-text {
                font-size: 9px;
            }
            
            .breakthrough-info {
                font-size: 9px;
            }
            
            .success-rate {
                font-size: 10px;
            }
            
            .functions-section {
                min-height: 70px;
            }
            
            .actions-section {
                height: 90px;
            }
            
            .action-text {
                font-size: 8px;
            }
            
            .action-description {
                font-size: 5px;
            }
        }

        /* 🆕 渡劫特效动画 - 增强版 */
        @keyframes tribulationPulse {
            0% { 
                opacity: 0.8; 
                transform: scale(1);
                text-shadow: 0 0 20px rgba(255,255,255,0.8);
            }
            100% { 
                opacity: 1; 
                transform: scale(1.05);
                text-shadow: 0 0 30px rgba(135,206,235,1), 0 0 40px rgba(255,255,255,0.9);
            }
        }

        @keyframes cloudAppear {
            0% { 
                opacity: 0;
                transform: translateX(-50%) scale(0.8);
            }
            100% { 
                opacity: 0.9;
                transform: translateX(-50%) scale(1);
            }
        }

        @keyframes cloudGather {
            0% { 
                opacity: 0.8;
                transform: scale(0.8);
                left: var(--start-x);
                top: var(--start-y);
            }
            100% { 
                opacity: 0.6;
                transform: scale(0.6) translate(-50%, -50%);
                left: var(--target-x);
                top: var(--target-y);
            }
        }

        @keyframes lightningStrike {
            0% { 
                opacity: 0; 
                stroke-dasharray: 1000;
                stroke-dashoffset: 1000;
                filter: drop-shadow(0 0 5px currentColor);
            }
            30% {
                opacity: 0.8;
                stroke-dashoffset: 300;
                filter: drop-shadow(0 0 15px currentColor) drop-shadow(0 0 30px currentColor);
            }
            60% {
                opacity: 1;
                stroke-dashoffset: 0;
                filter: drop-shadow(0 0 25px currentColor) drop-shadow(0 0 50px currentColor);
            }
            100% { 
                opacity: 1; 
                stroke-dashoffset: 0;
                filter: drop-shadow(0 0 20px currentColor) drop-shadow(0 0 40px currentColor);
            }
        }

        @keyframes lightningFlicker {
            0%, 100% { 
                opacity: 1; 
                filter: drop-shadow(0 0 20px currentColor) drop-shadow(0 0 40px currentColor);
            }
            5% { 
                opacity: 0.3; 
                filter: drop-shadow(0 0 10px currentColor);
            }
            10% { 
                opacity: 1; 
                filter: drop-shadow(0 0 25px currentColor) drop-shadow(0 0 50px currentColor);
            }
            15% { 
                opacity: 0.4; 
                filter: drop-shadow(0 0 12px currentColor);
            }
            20% { 
                opacity: 1; 
                filter: drop-shadow(0 0 20px currentColor) drop-shadow(0 0 40px currentColor);
            }
            25% { 
                opacity: 0.2; 
                filter: drop-shadow(0 0 8px currentColor);
            }
            30% { 
                opacity: 1; 
                filter: drop-shadow(0 0 30px currentColor) drop-shadow(0 0 60px currentColor);
            }
            85% { 
                opacity: 0.5; 
                filter: drop-shadow(0 0 15px currentColor);
            }
            90% { 
                opacity: 1; 
                filter: drop-shadow(0 0 25px currentColor) drop-shadow(0 0 50px currentColor);
            }
            95% { 
                opacity: 0.3; 
                filter: drop-shadow(0 0 10px currentColor);
            }
        }

        /* 🆕 渡劫结果动画 */
        @keyframes tribulationSuccess {
            0% { 
                transform: scale(1);
                text-shadow: 0 0 20px rgba(76, 175, 80, 0.8);
            }
            25% { 
                transform: scale(1.2);
                text-shadow: 0 0 30px rgba(76, 175, 80, 1), 0 0 50px rgba(76, 175, 80, 0.6);
            }
            50% { 
                transform: scale(1.1);
                text-shadow: 0 0 40px rgba(76, 175, 80, 1), 0 0 60px rgba(76, 175, 80, 0.8);
            }
            75% { 
                transform: scale(1.15);
                text-shadow: 0 0 35px rgba(76, 175, 80, 1), 0 0 55px rgba(76, 175, 80, 0.7);
            }
            100% { 
                transform: scale(1.1);
                text-shadow: 0 0 30px rgba(76, 175, 80, 0.9), 0 0 50px rgba(76, 175, 80, 0.6);
            }
        }

        @keyframes tribulationFailure {
            0% { 
                transform: scale(1);
                text-shadow: 0 0 20px rgba(255, 107, 107, 0.8);
            }
            15% { 
                transform: scale(0.9) rotate(-2deg);
                text-shadow: 0 0 25px rgba(255, 107, 107, 1);
            }
            30% { 
                transform: scale(1.1) rotate(2deg);
                text-shadow: 0 0 30px rgba(255, 107, 107, 1), 0 0 50px rgba(255, 107, 107, 0.6);
            }
            45% { 
                transform: scale(0.95) rotate(-1deg);
                text-shadow: 0 0 25px rgba(255, 107, 107, 1);
            }
            60% { 
                transform: scale(1.05) rotate(1deg);
                text-shadow: 0 0 35px rgba(255, 107, 107, 1), 0 0 55px rgba(255, 107, 107, 0.7);
            }
            75% { 
                transform: scale(0.98) rotate(-0.5deg);
                text-shadow: 0 0 30px rgba(255, 107, 107, 0.9);
            }
            100% { 
                transform: scale(1) rotate(0deg);
                text-shadow: 0 0 25px rgba(255, 107, 107, 0.8), 0 0 45px rgba(255, 107, 107, 0.5);
            }
        }

        /* 功法碎片合成弹窗样式 */
        .fragment-synthesis-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1001;
            backdrop-filter: blur(5px);
        }

        .fragment-synthesis-modal-content {
            background: linear-gradient(135deg, rgba(26, 26, 46, 0.95), rgba(22, 33, 62, 0.95));
            border-radius: 20px;
            padding: 20px 0 80px 0;
            width: 90%;
            max-width: 800px;
            height: calc(100vh - 100px);
            margin-top: 20px;
            overflow-y: auto;
            border: 2px solid rgba(139, 105, 20, 0.6);
            box-shadow: 0 0 30px rgba(139, 105, 20, 0.4);
            color: white;
        }

        .fragment-synthesis-title {
            font-size: 20px;
            font-weight: bold;
            color: #8b6914;
            text-align: center;
            margin-bottom: 20px;
            text-shadow: 0 0 10px rgba(139, 105, 20, 0.6);
            padding: 0 20px;
        }

        .fragment-synthesis-content {
            padding: 0 20px;
        }

        .synthesis-main-content {
            display: flex;
            gap: 20px;
            height: calc(100% - 120px);
        }

        .synthesis-recipes-section,
        .owned-fragments-section {
            flex: 1;
            margin-bottom: 20px;
        }

        .recipes-title,
        .fragments-title {
            font-size: 16px;
            font-weight: bold;
            color: #d4af37;
            margin-bottom: 10px;
            border-bottom: 1px solid rgba(212, 175, 55, 0.3);
            padding-bottom: 5px;
        }

        .recipes-list,
        .fragments-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
            max-height: calc(100vh - 300px);
            overflow-y: auto;
        }

        .recipe-item,
        .fragment-item {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            border: 1px solid rgba(139, 105, 20, 0.3);
            border-radius: 10px;
            padding: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .recipe-item:hover,
        .fragment-item:hover {
            border-color: rgba(139, 105, 20, 0.6);
            box-shadow: 0 0 15px rgba(139, 105, 20, 0.3);
            transform: translateY(-2px);
        }

        .recipe-item.can-synthesize {
            border-color: rgba(76, 175, 80, 0.6);
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(76, 175, 80, 0.05));
        }

        .recipe-item.can-synthesize:hover {
            border-color: rgba(76, 175, 80, 0.8);
            box-shadow: 0 0 15px rgba(76, 175, 80, 0.4);
        }

        .recipe-item.insufficient {
            opacity: 0.6;
            border-color: rgba(231, 76, 60, 0.3);
        }

        .recipe-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
        }

        .recipe-name,
        .fragment-name {
            font-weight: bold;
            color: #d4af37;
            font-size: 14px;
        }

        .recipe-status {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 12px;
            font-weight: bold;
        }

        .recipe-status.can-synthesize {
            background: rgba(76, 175, 80, 0.2);
            color: #4CAF50;
            border: 1px solid rgba(76, 175, 80, 0.4);
        }

        .recipe-status.insufficient {
            background: rgba(231, 76, 60, 0.2);
            color: #e74c3c;
            border: 1px solid rgba(231, 76, 60, 0.4);
        }

        .recipe-details {
            font-size: 12px;
            color: #bdc3c7;
            line-height: 1.4;
        }

        .recipe-requirement {
            margin-top: 5px;
            font-size: 11px;
            color: #95a5a6;
        }

        .fragment-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 5px;
        }

        .fragment-quantity {
            font-size: 12px;
            color: #3498db;
            font-weight: bold;
        }

        .fragment-target {
            font-size: 11px;
            color: #95a5a6;
        }

        .synthesize-button {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            border: none;
            border-radius: 8px;
            color: white;
            padding: 8px 16px;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 8px;
            width: 100%;
        }

        .synthesize-button:hover {
            background: linear-gradient(135deg, #45a049, #3d8b40);
            box-shadow: 0 0 10px rgba(76, 175, 80, 0.4);
        }

        .synthesize-button:disabled {
            background: linear-gradient(135deg, #666, #555);
            cursor: not-allowed;
            opacity: 0.6;
        }

        .fragment-synthesis-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 20px;
        }

        .fragment-synthesis-button {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 100px;
        }

        .synthesis-cancel {
            background: linear-gradient(135deg, #666, #888);
            color: white;
        }

        .synthesis-cancel:hover {
            background: linear-gradient(135deg, #777, #999);
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
        }

        .empty-list-message {
            text-align: center;
            color: #95a5a6;
            font-size: 14px;
            padding: 20px;
            font-style: italic;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .fragment-synthesis-modal-content {
                width: 95%;
                max-width: none;
                padding: 15px 0 70px 0;
                height: calc(100vh - 85px);
                margin-top: 15px;
            }

            .synthesis-main-content {
                flex-direction: column;
                gap: 15px;
            }

            .fragment-synthesis-title {
                font-size: 18px;
                padding: 0 15px;
            }

            .fragment-synthesis-content {
                padding: 0 15px;
            }

            .recipes-title,
            .fragments-title {
                font-size: 14px;
            }

            .recipe-item,
            .fragment-item {
                padding: 10px;
            }

            .recipe-name,
            .fragment-name {
                font-size: 13px;
            }

            .fragment-synthesis-button {
                padding: 10px 20px;
                font-size: 13px;
            }
        }

        .btn-synthesis {
            background: linear-gradient(135deg, rgba(255, 152, 0, 0.8), rgba(255, 152, 0, 0.6));
            border-color: rgba(255, 152, 0, 0.8);
        }

        .btn-synthesis:hover {
            box-shadow: 0 4px 12px rgba(255, 152, 0, 0.4);
        }