/**
 * 技能动画基类
 * 提供所有技能动画的通用功能和标准接口
 */
class BaseSkill {
    constructor(battleSystem, isEnemySkill = false) {
        this.battleSystem = battleSystem;
        this.effectsContainer = battleSystem.effectsContainer;
        this.battleContainer = battleSystem.battleContainer;
        this.isEnemySkill = isEnemySkill; // 是否为怪物技能
        

    }

    /**
     * 获取角色位置信息
     * @param {boolean} isPlayer 是否获取玩家位置（true=玩家位置，false=敌人位置）
     * @returns {Object} 位置信息
     */
    getCharacterPosition(isPlayer) {
        // 🔧 简化逻辑：直接根据参数选择对应的角色
        // isPlayer=true -> 获取玩家位置 (.player)
        // isPlayer=false -> 获取敌人位置 (.enemy)
        const selector = isPlayer ? '.player' : '.enemy';
        const element = document.querySelector(selector);
        if (!element) {
            console.error(`❌ [BaseSkill] 找不到元素: ${selector}`);
            return { x: 0, y: 0, centerX: 0, centerY: 0 };
        }
        
        const rect = element.getBoundingClientRect();
        const containerRect = this.battleContainer.getBoundingClientRect();
        
        const centerX = rect.left - containerRect.left + rect.width / 2;
        const centerY = rect.top - containerRect.top + rect.height / 2;
        
        const result = {
            element,
            rect,
            centerX,
            centerY,
            x: centerX,
            y: centerY,
            left: rect.left - containerRect.left,
            top: rect.top - containerRect.top,
            width: rect.width,
            height: rect.height
        };
        
        return result;
    }

    /**
     * 创建技能元素
     * @param {string} className CSS类名
     * @param {Object} options 配置选项
     * @returns {HTMLElement} 创建的元素
     */
    createElement(className, options = {}) {
        const element = document.createElement('div');
        element.className = className;
        
        if (options.style) {
            Object.assign(element.style, options.style);
        }
        
        if (options.position) {
            element.style.left = `${options.position.x}px`;
            element.style.top = `${options.position.y}px`;
        }
        
        if (options.cssVariables) {
            Object.entries(options.cssVariables).forEach(([key, value]) => {
                element.style.setProperty(key, value);
            });
        }
        
        return element;
    }

    /**
     * 添加武器图片到元素
     * @param {HTMLElement} element 目标元素
     * @param {string} weaponImage 武器图片路径
     * @param {Object} options 配置选项
     */
    addWeaponImage(element, weaponImage, options = {}) {
        if (weaponImage) {
            const weaponImg = document.createElement('img');
            weaponImg.src = weaponImage;
            weaponImg.className = 'weapon-image';
            weaponImg.style.width = options.width || '100%';
            weaponImg.style.height = options.height || '100%';
            weaponImg.style.objectFit = 'contain';
            
            weaponImg.onerror = () => {
                // 图片加载失败时使用默认图片
                if (window.ImagePathManager) {
                    weaponImg.src = window.ImagePathManager.getWeaponImage('battle_sword.png');
                } else {
                    weaponImg.src = 'assets/images/battle_sword.png';
                }
            };
            
            element.appendChild(weaponImg);
        } else {
            // 使用背景图片作为备选方案
            const defaultImage = window.ImagePathManager ? 
                window.ImagePathManager.getWeaponImage('battle_sword.png') :
                'assets/images/battle_sword.png';
            
            element.style.backgroundImage = `url('${defaultImage}')`;
            element.style.backgroundSize = 'contain';
            element.style.backgroundRepeat = 'no-repeat';
            element.style.backgroundPosition = 'center';
        }
    }

    /**
     * 创建击中特效
     * @param {number} x X坐标
     * @param {number} y Y坐标
     * @param {boolean} isPlayerAttack 是否为玩家攻击
     */
    createHitEffect(x, y, isPlayerAttack) {
        // 🔧 正确的攻击方向：isPlayerAttack 直接表示攻击方向
        const actualIsPlayerAttack = isPlayerAttack;
        // 击中闪光
        const hitFlash = this.createElement('sword-hit-flash', {
            position: { x, y }
        });
        this.effectsContainer.appendChild(hitFlash);

        // 击中粒子
        for (let i = 0; i < 12; i++) {
            const particle = this.createElement('sword-hit-particle', {
                position: { x, y },
                cssVariables: {
                    '--particleX': `${Math.cos(i / 12 * Math.PI * 2) * (30 + Math.random() * 40)}px`,
                    '--particleY': `${Math.sin(i / 12 * Math.PI * 2) * (30 + Math.random() * 40)}px`
                }
            });
            particle.style.animationDelay = `${Math.random() * 0.1}s`;
            this.effectsContainer.appendChild(particle);
            
            setTimeout(() => particle.remove(), 600);
        }

        // 冲击波
        const shockwave = this.createElement('sword-hit-shockwave', {
            position: { x, y }
        });
        this.effectsContainer.appendChild(shockwave);

        // 角色受击动画
        this.applyHitAnimation(actualIsPlayerAttack);

        // 清理特效
        setTimeout(() => {
            hitFlash.remove();
            shockwave.remove();
        }, 500);
    }

    /**
     * 应用角色受击动画
     * @param {boolean} isPlayerAttack 是否为玩家攻击
     */
    applyHitAnimation(isPlayerAttack) {
        const targetSelector = isPlayerAttack ? '.enemy .character-sprite' : '.player .character-sprite';
        const targetSprite = document.querySelector(targetSelector);
        
        if (targetSprite) {
            const currentAnimation = targetSprite.style.animation;
            if (currentAnimation.includes('sword-struck')) {
                targetSprite.style.animation = 'sword-struck 0.4s ease-out, sword-hit-shake 0.2s ease-out';
            } else {
                targetSprite.style.animation = isPlayerAttack ? 'sword-struck 0.4s ease-out' : 'sword-hit-shake 0.5s ease-out forwards';
                
                setTimeout(() => {
                    if (targetSprite.style.animation.includes('sword-struck') || 
                        targetSprite.style.animation.includes('sword-hit-shake')) {
                        targetSprite.style.animation = '';
                    }
                }, isPlayerAttack ? 400 : 400);
            }
        }
    }

    /**
     * 等待指定时间
     * @param {number} ms 毫秒数
     * @returns {Promise} Promise对象
     */
    wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 显示技能喊话
     * @param {string} skillName 技能名称
     * @param {boolean} isEnemy 是否为敌人（如果未提供，则使用this.isEnemySkill）
     */
    async showSkillShout(skillName, isEnemy = null) {
        // 先清理所有现有的技能喊话，防止重叠
        const existingShouts = document.querySelectorAll('.skill-shout');
        existingShouts.forEach(shout => shout.remove());
        
        // 如果没有显式指定isEnemy，则使用this.isEnemySkill
        const actualIsEnemy = isEnemy !== null ? isEnemy : this.isEnemySkill;
        
        return this.battleSystem.showSkillShout(skillName, actualIsEnemy);
    }

    /**
     * 执行技能动画 - 子类必须重写此方法
     * @param {Object} skillData 技能数据
     * @param {string} weaponImage 武器图片
     * @returns {Promise} 动画完成的Promise
     */
    async execute(skillData, weaponImage) {
        throw new Error('子类必须实现execute方法');
    }
}

// 导出基类
window.BaseSkill = BaseSkill; 