<?php
/**
 * 一念修仙 - 战斗关卡信息API
 * 提供战斗关卡的怪物、奖励等信息
 */

// 🔧 修复：禁用错误显示，避免HTML输出污染JSON
ini_set('display_errors', 0);
error_reporting(E_ALL);

// 允许跨域请求
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, POST");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// 引入数据库配置
require_once __DIR__ . '/../config/database.php';

// 引入通用函数
require_once __DIR__ . '/../includes/functions.php';

// 🔧 修复：使用正确的数据库连接方式
try {
    // 使用配置文件中定义的常量
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    
    // 检查连接
    if ($conn->connect_error) {
        throw new Exception("数据库连接失败: " . $conn->connect_error);
    }
    
    // 设置字符集
    $conn->set_charset("utf8mb4");
} catch (Exception $e) {
    error_log("数据库连接错误: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => "数据库连接失败"]);
    exit;
}

// 获取会话中的用户信息
// 🔧 修复：避免重复启动session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
$userId = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;
$characterId = isset($_SESSION['character_id']) ? $_SESSION['character_id'] : null;

// 🔧 修复：为测试环境提供默认用户信息
if (!$userId || !$characterId) {
    // 检查是否是测试环境
    $isTestEnvironment = isset($_GET['test']) && $_GET['test'] === '1';
    
    if ($isTestEnvironment) {
        // 测试环境使用默认用户
        $userId = 1;
        $characterId = 1;
        error_log("使用测试环境默认用户: userId=$userId, characterId=$characterId");
    } else {
        echo json_encode(['success' => false, 'message' => '请先登录']);
        exit;
    }
}

// 获取请求参数
$mapId = isset($_GET['map_id']) ? intval($_GET['map_id']) : 0;
$stageNumber = isset($_GET['stage_number']) ? intval($_GET['stage_number']) : 0;

// 检查参数是否有效
if ($mapId <= 0 || $stageNumber <= 0) {
    echo json_encode(['success' => false, 'message' => '无效的地图或关卡ID']);
    exit;
}

// 获取战斗关卡信息
getBattleStageInfo($conn, $characterId, $mapId, $stageNumber);

/**
 * 获取战斗关卡信息
 */
function getBattleStageInfo($conn, $characterId, $mapId, $stageNumber) {
    // 获取关卡信息
    $sql = "SELECT ms.*,
                  gm.map_name, gm.map_code, gm.background_image
            FROM map_stages ms
            LEFT JOIN game_maps gm ON ms.map_id = gm.id
            WHERE ms.map_id = ? AND ms.stage_number = ?";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ii", $mapId, $stageNumber);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 0) {
        echo json_encode(['success' => false, 'message' => '找不到指定关卡']);
        return;
    }
    
    $stageInfo = $result->fetch_assoc();
    
    // 记录日志
    error_log("Battle Stage Info - Map ID: $mapId, Stage: $stageNumber, Results: " . json_encode($stageInfo));
    
    // 修正查询中的base_level字段不存在问题
    if (!isset($stageInfo['base_mp'])) {
        $stageInfo['base_mp'] = 50; // 默认值
    }
    
    // 获取用户进度
    $sql = "SELECT * FROM user_map_progress WHERE character_id = ? AND map_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ii", $characterId, $mapId);
    $stmt->execute();
    $progressResult = $stmt->get_result();
    
    $progress = null;
    if ($progressResult->num_rows > 0) {
        $progress = $progressResult->fetch_assoc();
    } else {
        // 创建新的进度记录
        $mapCode = $stageInfo['map_code'];
        $sql = "INSERT INTO user_map_progress (character_id, map_id, map_code, current_stage, max_stage_reached) 
                VALUES (?, ?, ?, 1, 1)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("iis", $characterId, $mapId, $mapCode);
        $stmt->execute();
        
        $progress = [
            'character_id' => $characterId,
            'map_id' => $mapId,
            'current_stage' => 1,
            'max_stage_reached' => 1,
            'total_battles' => 0,
            'total_victories' => 0
        ];
    }
    
    // 获取怪物掉落信息
    $sql = "SELECT dg.id as drop_group_id, dg.group_name, dg.group_type, dg.total_weight, dg.max_drops, dg.min_drops, dg.guarantee_rare
            FROM map_drop_configs mdc
            JOIN drop_groups dg ON mdc.drop_group_id = dg.id
            WHERE mdc.map_id = ? AND (mdc.stage_number IS NULL OR mdc.stage_number = ?)
            LIMIT 1";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ii", $mapId, $stageNumber);
    $stmt->execute();
    $dropResult = $stmt->get_result();
    
    $dropInfo = null;
    if ($dropResult->num_rows > 0) {
        $dropInfo = $dropResult->fetch_assoc();
        
        // 获取掉落组中的物品
        $sql = "SELECT dgi.*, gi.item_name, gi.item_type, gi.description as item_description
                FROM drop_group_items dgi
                JOIN game_items gi ON dgi.item_id = gi.id
                WHERE dgi.group_id = ?";
        
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $dropInfo['drop_group_id']);
        $stmt->execute();
        $itemsResult = $stmt->get_result();
        
        $dropItems = [];
        while ($item = $itemsResult->fetch_assoc()) {
            $dropItems[] = $item;
        }
        
        $dropInfo['items'] = $dropItems;
    }
    
    // 构建怪物详细信息
    $monsterInfo = [
        'id' => isset($stageInfo['monster_id']) ? $stageInfo['monster_id'] : 0,
        'name' => isset($stageInfo['monster_name']) ? $stageInfo['monster_name'] : '未知怪物',
        'type' => isset($stageInfo['monster_tier']) ? $stageInfo['monster_tier'] : 'normal',
        'level' => isset($stageInfo['monster_level']) ? $stageInfo['monster_level'] : 1,
        'count' => isset($stageInfo['monster_count']) ? $stageInfo['monster_count'] : 1,
        'hp' => isset($stageInfo['base_hp']) ? ($stageInfo['base_hp'] * $stageInfo['monster_level']) : 100,
        'mp' => isset($stageInfo['base_mp']) ? $stageInfo['base_mp'] : 50,
        'attack' => isset($stageInfo['base_attack']) ? ($stageInfo['base_attack'] * (1 + ($stageInfo['monster_level'] * 0.1))) : 10,
        'defense' => isset($stageInfo['base_defense']) ? ($stageInfo['base_defense'] * (1 + ($stageInfo['monster_level'] * 0.05))) : 5,
        'speed' => isset($stageInfo['base_speed']) ? $stageInfo['base_speed'] : 10,
        'critical_bonus' => isset($stageInfo['critical_bonus']) ? $stageInfo['critical_bonus'] : 5,
        'dodge_bonus' => isset($stageInfo['dodge_bonus']) ? $stageInfo['dodge_bonus'] : 5,
        'critical_resistance' => isset($stageInfo['critical_resistance']) ? $stageInfo['critical_resistance'] : 5,
        'spirit_stone_reward' => isset($stageInfo['spirit_stone_reward']) && $stageInfo['spirit_stone_reward'] > 0 ? $stageInfo['spirit_stone_reward'] : ($stageInfo['monster_level'] * 10),
        'gold_reward' => isset($stageInfo['gold_reward']) && $stageInfo['gold_reward'] > 0 ? $stageInfo['gold_reward'] : ($stageInfo['monster_level'] * 5),
        'skills' => isset($stageInfo['skills']) ? json_decode($stageInfo['skills'], true) : [],
        'avatar_image' => isset($stageInfo['avatar_image']) ? $stageInfo['avatar_image'] : 'assets/images/enemy/monster_default.png',
        'model_image' => isset($stageInfo['model_image']) ? $stageInfo['model_image'] : 'assets/images/enemy/monster_default.png',
        'description' => isset($stageInfo['description']) ? $stageInfo['description'] : '一只神秘的怪物',
        'ai_pattern' => isset($stageInfo['ai_pattern']) ? $stageInfo['ai_pattern'] : 'conservative'
    ];
    
    // 记录怪物信息
    error_log("Monster Info: " . json_encode($monsterInfo));
    
    // 构建战斗奖励信息
    $rewardInfo = [
        'spirit_stone' => isset($stageInfo['spirit_stone_reward']) ? $stageInfo['spirit_stone_reward'] : 10,
        'gold' => isset($stageInfo['gold_reward']) ? $stageInfo['gold_reward'] : 5,
        'drop_rate' => isset($stageInfo['drop_rate_modifier']) ? $stageInfo['drop_rate_modifier'] : 1.0,
        'drop_group' => $dropInfo
    ];
    
    // 记录奖励信息
    error_log("Reward Info: " . json_encode($rewardInfo));
    
    // 构建关卡信息
    $battleStageInfo = [
        'map_id' => $mapId,
        'map_name' => $stageInfo['map_name'],
        'map_code' => $stageInfo['map_code'],
        'background_image' => $stageInfo['background_image'],
        'stage_number' => $stageNumber,
        'stage_name' => $stageInfo['stage_name'],
        'is_boss_stage' => $stageInfo['is_boss_stage'] == 1,
        'monster' => $monsterInfo,
        'rewards' => $rewardInfo,
        'progress' => $progress
    ];
    
    echo json_encode([
        'success' => true, 
        'battle_stage_info' => $battleStageInfo
    ]);
}

// 关闭数据库连接
$conn->close();
?> 