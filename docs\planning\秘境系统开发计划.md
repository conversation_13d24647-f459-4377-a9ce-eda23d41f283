# 🏛️ 一念修仙 - 秘境系统开发计划

*版本：v1.0*  
*创建日期：2024年12月19日*  
*最后更新：2024年12月19日*

---

## 📋 项目背景

基于现有游戏的**放置类、自动战斗、文字游戏**特性，设计符合网页游戏功能限制的多人秘境系统。

### 🎯 核心设计原则
1. **放置优先**：全自动战斗，无需手动操作
2. **休闲体验**：碎片时间游戏，少动脑多收菜
3. **数值成长**：核心是属性提升和装备收集
4. **简化特色**：避免复杂机制，保持游戏流畅性
5. **合理消耗**：平衡钥匙产出与消耗

---

## 🗺️ 现有游戏分析

### 八大历练地图
| 地图ID | 地图名称 | 关卡数 | 境界要求 | 特色属性 |
|--------|----------|--------|----------|----------|
| 1 | 太乙峰 | 60关 | 低级 | 修仙起点 |
| 2 | 碧水寒潭 | 90关 | 低级 | 寒冰属性 |
| 3 | 赤焰谷 | 120关 | 中级 | 火焰属性 |
| 4 | 幽冥鬼域 | 140关 | 中级 | 阴邪属性 |
| 5 | 青云仙山 | 150关 | 中高级 | 仙灵属性 |
| 6 | 星辰古战场 | 140关 | 高级 | 星辰属性 |
| 7 | 混元虚空 | 140关 | 高级 | 虚空属性 |
| 8 | 洪荒秘境 | 175关 | 最高级 | 洪荒属性 |

### 奇遇系统现状
- ✅ **奇遇值累积**：战斗胜利获得1-3点，1000点触发奇遇
- ✅ **秘境钥匙产出**：通过"秘境地图发现"事件获得（概率5%）
- ✅ **现有钥匙**：太乙峰、碧水寒潭、赤焰谷、幽冥鬼域、青云仙山（5种）
- ⏳ **需新增钥匙**：星辰古战场、混元虚空、洪荒秘境（3种）

---

## 🎮 秘境系统设计方案

### 🏗️ 一、系统架构

#### 1.1 核心机制
- **队伍制**：1-4人组队，自己建队即为单人模式
- **全自动战斗**：类似现有战斗系统，提前设置装备技能自动对战
- **分层挑战**：每个秘境10-15层，队伍实力决定能通关多少层
- **钥匙消耗**：灵活的钥匙使用机制，避免浪费

#### 1.2 游戏流程
```
创建/加入队伍 → 选择秘境 → 选择难度 → 开启秘境 → 消耗钥匙 → 自动战斗 → 分层推进 → 获得奖励
```

### 🗝️ 二、钥匙系统优化

#### 2.1 钥匙使用机制（解决消化问题）
**核心设计**：
- 进入秘境需要**1把完整钥匙**（使用后钥匙消失）
- 钥匙碎片通过商店每日限购获得
- 钥匙碎片可在背包中合成完整钥匙

**钥匙碎片合成规则**：
- 每把完整钥匙需要**10个对应碎片**合成
- 背包中点击碎片显示"合成"按钮
- 点击合成消耗10个碎片，获得1把完整钥匙

#### 2.2 钥匙获取途径
1. **奇遇事件**：获得完整钥匙（概率5%）
2. **商店购买**：每日限购钥匙碎片（每种2-5个）
3. **碎片合成**：10个碎片合成1把完整钥匙
4. **活动奖励**：节日活动等特殊获取

#### 2.3 商店系统扩展
**新增商店功能**：
- 增加每日限购字段支持
- 使用境界等级控制商品显示
- 钥匙碎片作为日限购商品上架

### 🏛️ 三、八大秘境设计（简化版）

#### 3.1 通用设计
- **层数**：每个秘境15层
- **难度等级**：简单、困难、绝境三个难度
- **难度递增**：每层怪物属性+15%，困难模式+30%，绝境模式+50%
- **BOSS层**：第5、10、15层为BOSS层，奖励翻倍
- **队伍加成**：多人组队时怪物血量+20%/人，但奖励也+20%/人

#### 3.2 秘境列表（五行属性设计）

**🏔️ 太乙秘境（金属性主导）**
- **适合境界**：1-50级
- **五行特色**：金、土、水属性技能伤害+30%，木、火属性技能伤害-20%
- **主要产出**：基础修炼资源、低级装备
- **钥匙**：太乙峰秘境钥匙

**🌊 寒潭秘境（水属性主导）**
- **适合境界**：30-80级
- **五行特色**：水、金、木属性技能伤害+30%，火、土属性技能伤害-20%
- **主要产出**：寒冰属性装备、抗寒丹药
- **钥匙**：碧水寒潭秘境钥匙

**🔥 烈焰秘境（火属性主导）**
- **适合境界**：50-120级
- **五行特色**：火、木、土属性技能伤害+30%，水、金属性技能伤害-20%
- **主要产出**：火焰属性装备、炼体丹药
- **钥匙**：赤焰谷秘境钥匙

**👻 幽冥秘境（综合属性）**
- **适合境界**：80-160级
- **五行特色**：所有属性技能伤害+15%（阴邪之地，五行混乱）
- **主要产出**：幽冥属性装备、养魂丹药
- **钥匙**：幽冥鬼域秘境钥匙

**☁️ 青云秘境（木属性主导）**
- **适合境界**：120-200级
- **五行特色**：木、水、火属性技能伤害+30%，金、土属性技能伤害-20%
- **主要产出**：高品质装备、仙灵丹药
- **钥匙**：青云仙山秘境钥匙

**⭐ 星辰秘境（金属性主导）**
- **适合境界**：160-220级
- **五行特色**：金、土、水属性技能伤害+30%，木、火属性技能伤害-20%
- **主要产出**：星辰属性装备、战魂精华
- **钥匙**：星辰古战场秘境钥匙

**🌌 虚空秘境（综合属性）**
- **适合境界**：200-260级
- **五行特色**：所有属性技能伤害+20%（虚空乱流，五行失序）
- **主要产出**：虚空属性装备、空间宝物
- **钥匙**：混元虚空秘境钥匙

**🌍 洪荒秘境（土属性主导）**
- **适合境界**：240-280级
- **五行特色**：土、火、金属性技能伤害+30%，木、水属性技能伤害-20%
- **主要产出**：神话品质装备、洪荒至宝
- **钥匙**：洪荒秘境钥匙

### 🔧 四、多人战斗系统技术分析

#### 4.1 技术挑战和可行性
**核心问题**：现有战斗系统是否支持多人组队混战？

**技术考虑**：
1. **战斗系统大改版工程量**：
   - 需要将技能攻击目标改为：单人、多人、全体
   - 需要将攻击次数改为：1、2、3次等多种选择
   - 需要重新设计技能动画的目标选择逻辑
   - 需要调整AI系统支持多目标选择

2. **简化方案建议**：
   - **队伍合并战力**：将队伍成员属性合并，作为一个"超级角色"对战
   - **轮流出手**：队伍成员按序轮流使用技能，保持现有单对单格式
   - **属性叠加**：队伍总血量 = 所有成员血量之和，攻击力取平均值

3. **技术实现方案**：
   - 方案A（复杂）：完全重构战斗系统，支持多对多战斗
   - 方案B（中等）：队伍属性合并，单对单战斗格式
   - 方案C（简单）：队伍成员轮流单挑，分别计算结果

**推荐方案B**：队伍属性合并，既保持现有技术架构，又实现多人协作效果。

### 🗄️ 五、数据库设计

#### 5.1 商店系统扩展 (shop_items表更新)
```sql
-- 为shop_items表新增每日限购字段
ALTER TABLE `shop_items` 
ADD COLUMN `daily_limit` int(11) DEFAULT NULL COMMENT '每日限购数量，NULL表示无限制',
ADD COLUMN `daily_purchased` int(11) DEFAULT '0' COMMENT '今日已购买数量',
ADD COLUMN `last_reset_date` date DEFAULT NULL COMMENT '最后重置日期';

-- 现有level_requirement字段用于控制商品显示
-- level_requirement: 达到指定境界等级才显示该商品

-- 钥匙碎片商品示例
INSERT INTO shop_items (item_name, item_type, price, currency_type, level_requirement, daily_limit, description) VALUES
('太乙峰秘境钥匙碎片', 'secret_key_fragment', 100, 'spirit_stones', 1, 3, '用于合成太乙峰秘境钥匙的碎片'),
('碧水寒潭秘境钥匙碎片', 'secret_key_fragment', 150, 'spirit_stones', 30, 3, '用于合成碧水寒潭秘境钥匙的碎片'),
('赤焰谷秘境钥匙碎片', 'secret_key_fragment', 200, 'spirit_stones', 50, 2, '用于合成赤焰谷秘境钥匙的碎片');
```

#### 5.2 秘境基础表 (secret_realms)
```sql
CREATE TABLE `secret_realms` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '秘境ID',
  `realm_name` varchar(100) NOT NULL COMMENT '秘境名称',
  `realm_code` varchar(50) NOT NULL COMMENT '秘境代码',
  `map_id` int(11) NOT NULL COMMENT '对应地图ID',
  `required_keys` int(11) DEFAULT '1' COMMENT '所需完整钥匙数',
  `difficulty_simple` tinyint(1) DEFAULT '1' COMMENT '是否开放简单难度',
  `difficulty_hard` tinyint(1) DEFAULT '1' COMMENT '是否开放困难难度', 
  `difficulty_extreme` tinyint(1) DEFAULT '1' COMMENT '是否开放绝境难度',
  `max_layers` int(11) DEFAULT '15' COMMENT '最大层数',
  `max_players` int(11) DEFAULT '4' COMMENT '最大玩家数',
  `difficulty_base` int(11) NOT NULL COMMENT '基础难度等级',
  `realm_description` text COMMENT '秘境描述',
  `buff_effects` text COMMENT 'JSON格式增益效果',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_realm_code` (`realm_code`),
  KEY `idx_map_id` (`map_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='秘境基础配置表';
```

#### 5.3 秘境队伍表 (secret_realm_teams)
```sql
CREATE TABLE `secret_realm_teams` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '队伍ID',
  `team_leader_id` int(11) NOT NULL COMMENT '队长角色ID',
  `realm_id` int(11) NOT NULL COMMENT '挑战的秘境ID',
  `difficulty_level` enum('simple','hard','extreme') NOT NULL COMMENT '挑战难度',
  `team_code` varchar(20) NOT NULL COMMENT '队伍邀请码',
  `current_layer` int(11) DEFAULT '1' COMMENT '当前层数',
  `max_reached_layer` int(11) DEFAULT '0' COMMENT '最高到达层数',
  `team_status` enum('waiting','fighting','completed','failed') DEFAULT 'waiting' COMMENT '队伍状态',
  `team_members` text NOT NULL COMMENT 'JSON格式队伍成员信息',
  `auto_battle_settings` text COMMENT 'JSON格式自动战斗设置',
  `total_rewards` text COMMENT 'JSON格式总奖励',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `started_at` timestamp NULL DEFAULT NULL COMMENT '开始时间',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_team_code` (`team_code`),
  KEY `idx_team_leader` (`team_leader_id`),
  KEY `idx_realm_id` (`realm_id`),
  KEY `idx_team_status` (`team_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='秘境队伍表';
```

#### 5.4 秘境个人记录表 (secret_realm_records)
```sql
CREATE TABLE `secret_realm_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `character_id` int(11) NOT NULL COMMENT '角色ID',
  `realm_id` int(11) NOT NULL COMMENT '秘境ID',
  `best_layer` int(11) DEFAULT '0' COMMENT '最高通过层数',
  `total_entries` int(11) DEFAULT '0' COMMENT '总进入次数',
  `total_completions` int(11) DEFAULT '0' COMMENT '总完成次数',
  `today_entries` int(11) DEFAULT '0' COMMENT '今日进入次数',
  `last_entry_date` date DEFAULT NULL COMMENT '最后进入日期',
  `total_rewards_received` text COMMENT 'JSON格式累计奖励',
  `personal_best_time` int(11) DEFAULT NULL COMMENT '个人最佳通关时间(秒)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_character_realm` (`character_id`, `realm_id`),
  KEY `idx_character_id` (`character_id`),
  KEY `idx_best_layer` (`best_layer`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='秘境个人记录表';
```

#### 5.5 背包物品合成支持
```sql
-- 为user_inventories表新增合成相关字段（如果需要）
-- 或者直接在现有背包系统中添加合成按钮逻辑

-- 钥匙碎片合成规则配置
CREATE TABLE `item_synthesis_rules` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '合成规则ID',
  `target_item_id` int(11) NOT NULL COMMENT '目标物品ID',
  `target_item_name` varchar(100) NOT NULL COMMENT '目标物品名称',
  `required_fragments` int(11) NOT NULL COMMENT '所需碎片数量',
  `fragment_item_id` int(11) NOT NULL COMMENT '碎片物品ID',
  `fragment_item_name` varchar(100) NOT NULL COMMENT '碎片物品名称',
  `synthesis_type` enum('secret_key','equipment','other') DEFAULT 'secret_key' COMMENT '合成类型',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  PRIMARY KEY (`id`),
  KEY `idx_target_item` (`target_item_id`),
  KEY `idx_fragment_item` (`fragment_item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='物品合成规则表';

-- 秘境钥匙合成规则示例
INSERT INTO item_synthesis_rules (target_item_id, target_item_name, required_fragments, fragment_item_id, fragment_item_name) VALUES
(1003, '太乙峰秘境钥匙', 10, 2001, '太乙峰秘境钥匙碎片'),
(1004, '碧水寒潭秘境钥匙', 10, 2002, '碧水寒潭秘境钥匙碎片'),
(1005, '赤焰谷秘境钥匙', 10, 2003, '赤焰谷秘境钥匙碎片');
```

### 🎮 六、前端页面设计

#### 6.1 秘境大厅页面结构
```
秘境大厅 (secret_realms.html)
├── 头部导航
├── 钥匙碎片显示区
├── 秘境列表区
│   ├── 秘境卡片展示
│   └── 个人记录显示
├── 队伍管理区
│   ├── 创建队伍
│   ├── 加入队伍
│   └── 队伍状态
└── 奖励预览区
```

#### 6.2 核心页面要素
- **简洁界面**：避免复杂操作，一键进入
- **状态清晰**：队伍状态、碎片数量、个人记录一目了然
- **自动战斗**：进入后全程自动，显示战斗过程即可
- **实时更新**：队伍进度、奖励获得的实时显示

### 🔧 七、后端API设计

#### 7.1 主要API列表
```php
// src/api/secret_realms_api.php - 秘境管理API
// - get_realms_list: 获取秘境列表和个人记录
// - get_key_fragments: 获取钥匙碎片信息
// - claim_daily_fragments: 领取每日免费碎片
// - create_team: 创建队伍
// - join_team: 加入队伍
// - start_realm_challenge: 开始秘境挑战
// - get_team_progress: 获取队伍挑战进度
// - get_battle_log: 获取战斗日志

// src/api/secret_battle_api.php - 秘境战斗API
// - auto_battle_layer: 自动战斗某一层
// - get_layer_result: 获取层级战斗结果
// - complete_realm: 完成秘境挑战
// - abandon_realm: 放弃秘境挑战
// - distribute_rewards: 分配队伍奖励
```

#### 7.2 自动战斗逻辑
```php
// 基于现有战斗系统的自动战斗逻辑
function autoBattleLayer($teamId, $layerNumber) {
    // 1. 获取队伍成员战力
    // 2. 计算层级怪物属性
    // 3. 模拟战斗过程（类似现有挂机系统）
    // 4. 计算战斗结果
    // 5. 分配奖励
    // 6. 决定是否继续下一层
}
```

### 🎁 八、奖励系统

#### 8.1 分层奖励机制
```
第1-4层：基础奖励
第5层 (BOSS)：奖励 × 2
第6-9层：进阶奖励
第10层 (BOSS)：奖励 × 2
第11-14层：高级奖励
第15层 (最终BOSS)：奖励 × 3
```

#### 8.2 奖励计算公式
```
基础奖励 = 对应地图单关奖励 × 5
层级加成 = 基础奖励 × (层数 × 0.1)
队伍加成 = 基础奖励 × (队伍人数 × 0.2)
BOSS加成 = 基础奖励 × BOSS倍数
秘境特色加成 = 根据特色效果额外计算

最终奖励 = (基础奖励 + 层级加成 + 队伍加成) × BOSS加成 × 特色加成
```

#### 8.3 特殊奖励
- **首通奖励**：每个秘境首次完成15层获得大量额外奖励
- **排行榜奖励**：每周根据通关层数排行发放奖励
- **连胜奖励**：连续成功完成秘境获得额外奖励

### 🚀 九、开发实施计划

#### 阶段一：基础框架 (3-4天)
**目标**：搭建秘境系统基础架构
- [ ] 创建数据库表结构
- [ ] 实现钥匙碎片系统
- [ ] 开发基础API接口
- [ ] 完成前端秘境大厅页面

**验收标准**：
- 能够查看秘境列表
- 钥匙碎片正常获取和消耗
- 队伍创建和加入功能正常

#### 阶段二：自动战斗核心 (4-5天)
**目标**：实现自动战斗逻辑
- [ ] 适配现有战斗系统到秘境
- [ ] 实现分层推进机制
- [ ] 开发奖励计算和分配
- [ ] 完成战斗日志和进度显示

**验收标准**：
- 能够自动战斗并推进层数
- 奖励计算准确
- 战斗过程可视化显示

#### 阶段三：八大秘境内容 (3-4天)
**目标**：配置八个秘境的具体内容
- [ ] 配置八大秘境的基础数据
- [ ] 实现简化的特色效果
- [ ] 调整难度和奖励平衡
- [ ] 完成秘境专属内容

**验收标准**：
- 八个秘境都能正常挑战
- 特色效果正常生效
- 难度递增合理

#### 阶段四：优化和完善 (2-3天)
**目标**：系统优化和用户体验提升
- [ ] 性能优化和数据缓存
- [ ] UI/UX优化
- [ ] 平衡性调整
- [ ] bug修复和测试

**验收标准**：
- 系统运行稳定
- 用户体验流畅
- 数值平衡合理

### 📊 十、数值平衡参考

#### 10.1 钥匙获取平衡
```
每日商店碎片：2-5个 (每种秘境)
奇遇完整钥匙：6个/月 (通过奇遇)
碎片合成钥匙：10个碎片 = 1把钥匙
单次挑战消耗：1把完整钥匙
月可挑战次数：约30-50次 (商店碎片+奇遇钥匙)
```

#### 10.2 难度递增参考
```
基础怪物属性：与对应地图相同
层级递增：每层+15%属性
队伍加成：每人+20%怪物血量
BOSS加成：血量+100%，攻击+50%
```

#### 10.3 奖励产出平衡
```
单层基础奖励：对应地图 × 5倍
完成15层总奖励：约等于刷75关普通地图
时间投入比：秘境30分钟 vs 普通地图60分钟
收益比：秘境效率约为普通地图的2倍（考虑钥匙稀缺性）
```

### 📝 十一、后续扩展计划

#### 11.1 短期扩展 (1-2个月)
- **季节秘境**：节日限定秘境，特殊奖励
- **挑战模式**：增加额外难度选项
- **公会秘境**：公会成员协作挑战的大型秘境

#### 11.2 长期扩展 (3-6个月)
- **跨服秘境**：多服务器玩家协作挑战
- **排位秘境**：竞技性质的秘境挑战
- **自定义秘境**：玩家可创建和分享的秘境

### 🔍 十二、风险评估和应对

#### 12.1 技术风险
**风险**：多人同步可能存在延迟和同步问题
**应对**：采用队伍状态同步机制，避免实时同步需求

**风险**：自动战斗可能过于简单，缺乏策略性
**应对**：通过装备配置和队伍搭配增加策略深度

#### 12.2 平衡性风险
**风险**：奖励过高影响普通地图价值
**应对**：控制钥匙产出，限制秘境收益占比

**风险**：钥匙碎片系统可能过于复杂
**应对**：提供详细的新手引导和说明

#### 12.3 用户体验风险
**风险**：多人组队可能存在等待时间过长
**应对**：支持单人进入，提供AI队友填充功能

---

## 📋 总结

这个重新设计的秘境系统具有以下特点：

### ✨ 符合游戏特性
- **放置类核心**：全自动战斗，符合现有游戏风格
- **简化操作**：一键进入，无需复杂手动操作
- **碎片时间**：单次挑战30分钟左右，适合休闲游戏
- **数值成长**：奖励丰富，提供明确的成长路径

### 🔧 解决用户担忧
- **钥匙消化**：碎片制度避免钥匙浪费
- **单多人合并**：统一队伍系统，自建队即单人
- **特色简化**：每个秘境只有一个简单的增益效果
- **层级奖励**：根据通关层数给予对应奖励

### 🎯 开发可行性
- **基于现有系统**：复用战斗、奖励、背包等系统
- **模块化设计**：便于分阶段开发和后续扩展
- **数据驱动**：通过配置调整平衡性，无需修改代码

这个文档将作为后续开发的指导文档，随着开发进展不断完善和更新。

---

*文档状态：初版完成，等待评审和反馈* 