<?php
/**
 * 为角色ID=31添加24套完整的6件套装备用于测试PVP套装特殊效果
 */

require_once 'src/includes/db_connection.php';

try {
    $db = getDbConnection();
    $db->beginTransaction();
    
    $characterId = 31;
    echo "=== 为角色ID={$characterId}添加测试套装装备 ===\n";
    
    // 1. 验证角色存在
    $stmt = $db->prepare("SELECT id, character_name, user_id FROM characters WHERE id = ?");
    $stmt->execute([$characterId]);
    $character = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$character) {
        throw new Exception("未找到角色ID={$characterId}");
    }
    
    echo "目标角色: {$character['character_name']} (用户ID: {$character['user_id']})\n\n";
    
    // 2. 定义24套不同的测试套装配置
    $testSets = [
        // 攻击类套装 (1-8)
        ['name' => '烈焰战神', 'type' => 'attack', 'effect' => '暴击伤害', 'rate' => 18, 'color' => '传说'],
        ['name' => '疾风剑圣', 'type' => 'attack', 'effect' => '连击', 'rate' => 15, 'color' => '史诗'],
        ['name' => '破军霸王', 'type' => 'attack', 'effect' => '破甲', 'rate' => 12, 'color' => '传说'],
        ['name' => '嗜血魔君', 'type' => 'attack', 'effect' => '吸血', 'rate' => 20, 'color' => '史诗'],
        ['name' => '雷霆战将', 'type' => 'attack', 'effect' => '暴击伤害', 'rate' => 22, 'color' => '传说'],
        ['name' => '影刃刺客', 'type' => 'attack', 'effect' => '连击', 'rate' => 18, 'color' => '史诗'],
        ['name' => '龙鳞战士', 'type' => 'attack', 'effect' => '破甲', 'rate' => 15, 'color' => '传说'],
        ['name' => '血月狂徒', 'type' => 'attack', 'effect' => '吸血', 'rate' => 25, 'color' => '史诗'],
        
        // 防御类套装 (9-16)
        ['name' => '玄武守护', 'type' => 'defense', 'effect' => '格挡', 'rate' => 25, 'color' => '传说'],
        ['name' => '反击战神', 'type' => 'defense', 'effect' => '反击', 'rate' => 18, 'color' => '史诗'],
        ['name' => '荆棘铠甲', 'type' => 'defense', 'effect' => '荆棘反弹', 'rate' => 30, 'color' => '传说'],
        ['name' => '不灭金身', 'type' => 'defense', 'effect' => '免疫', 'rate' => 10, 'color' => '史诗'],
        ['name' => '钢铁堡垒', 'type' => 'defense', 'effect' => '格挡', 'rate' => 28, 'color' => '传说'],
        ['name' => '复仇之魂', 'type' => 'defense', 'effect' => '反击', 'rate' => 20, 'color' => '史诗'],
        ['name' => '刺甲战神', 'type' => 'defense', 'effect' => '荆棘反弹', 'rate' => 35, 'color' => '传说'],
        ['name' => '圣光护盾', 'type' => 'defense', 'effect' => '免疫', 'rate' => 12, 'color' => '史诗'],
        
        // 混合类套装 (17-24)
        ['name' => '天帝战甲', 'type' => 'mixed', 'effect' => '暴击+格挡', 'rate' => 20, 'color' => '传说'],
        ['name' => '修罗血甲', 'type' => 'mixed', 'effect' => '吸血+反击', 'rate' => 18, 'color' => '史诗'],
        ['name' => '九天玄甲', 'type' => 'mixed', 'effect' => '连击+荆棘', 'rate' => 22, 'color' => '传说'],
        ['name' => '太极八卦', 'type' => 'mixed', 'effect' => '破甲+免疫', 'rate' => 15, 'color' => '史诗'],
        ['name' => '混沌战神', 'type' => 'mixed', 'effect' => '暴击+反击', 'rate' => 25, 'color' => '传说'],
        ['name' => '无极剑仙', 'type' => 'mixed', 'effect' => '连击+格挡', 'rate' => 20, 'color' => '史诗'],
        ['name' => '万法归宗', 'type' => 'mixed', 'effect' => '吸血+荆棘', 'rate' => 28, 'color' => '传说'],
        ['name' => '至尊帝皇', 'type' => 'mixed', 'effect' => '破甲+免疫', 'rate' => 18, 'color' => '史诗']
    ];
    
    // 3. 装备槽位配置
    $equipmentSlots = [
        ['slot' => 'sword', 'name_suffix' => '剑', 'type' => 'weapon', 'attack' => 300, 'defense' => 50],
        ['slot' => 'chest', 'name_suffix' => '胸甲', 'type' => 'armor', 'attack' => 50, 'defense' => 200],
        ['slot' => 'legs', 'name_suffix' => '护腿', 'type' => 'armor', 'attack' => 30, 'defense' => 150],
        ['slot' => 'feet', 'name_suffix' => '战靴', 'type' => 'armor', 'attack' => 40, 'defense' => 120],
        ['slot' => 'ring', 'name_suffix' => '指环', 'type' => 'accessory', 'attack' => 80, 'defense' => 80],
        ['slot' => 'necklace', 'name_suffix' => '项链', 'type' => 'accessory', 'attack' => 100, 'defense' => 100]
    ];
    
    $totalItemsAdded = 0;
    
    // 4. 为每套装备创建套装和物品
    foreach ($testSets as $index => $setConfig) {
        $setName = $setConfig['name'] . '套装';
        echo "创建套装 " . ($index + 1) . ": {$setName}\n";
        
        // 生成套装特殊效果
        $specialEffects = generateSetEffects($setConfig);
        
        // 创建套装记录
        $stmt = $db->prepare("
            INSERT INTO game_item_sets (set_name, description, effects, rarity, realm_requirement, status)
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $setName,
            "测试PVP套装特殊效果 - " . $setConfig['effect'] . "类型",
            json_encode($specialEffects, JSON_UNESCAPED_UNICODE),
            $setConfig['color'],
            1,
            'active'
        ]);
        $setId = $db->lastInsertId();
        
        // 为每个槽位创建装备
        foreach ($equipmentSlots as $slot) {
            $itemName = $setConfig['name'] . $slot['name_suffix'];
            
            // 创建装备物品
            $stmt = $db->prepare("
                INSERT INTO game_items (
                    item_code, item_name, item_type, slot_type, set_id,
                    rarity, realm_requirement, description,
                    physical_attack, physical_defense, max_hp, max_mp,
                    sell_price, durability, max_durability
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                'test_set_' . $setId . '_' . $slot['slot'],
                $itemName,
                $slot['type'],
                $slot['slot'],
                $setId,
                $setConfig['color'],
                1,
                "测试套装部件 - {$itemName}",
                $slot['attack'],
                $slot['defense'],
                400, // HP
                150, // MP
                2000, // 售价
                100, // 当前耐久
                100  // 最大耐久
            ]);
            $itemId = $db->lastInsertId();
            
            // 添加到角色背包
            $stmt = $db->prepare("
                INSERT INTO character_inventory (character_id, item_id, quantity, durability)
                VALUES (?, ?, 1, 100)
            ");
            $stmt->execute([$characterId, $itemId]);
            
            $totalItemsAdded++;
        }
        
        echo "  - 已添加6件装备到背包\n";
    }
    
    $db->commit();
    echo "\n=== 套装装备添加完成 ===\n";
    echo "总计添加: {$totalItemsAdded}件装备 (24套 × 6件)\n";
    echo "包含套装类型:\n";
    echo "- 攻击类套装: 8套 (暴击、连击、破甲、吸血)\n";
    echo "- 防御类套装: 8套 (格挡、反击、荆棘、免疫)\n";
    echo "- 混合类套装: 8套 (多种效果组合)\n";
    echo "\n角色ID={$characterId}现在可以测试完整的PVP套装特殊效果系统！\n";
    
} catch (Exception $e) {
    $db->rollBack();
    echo "错误: " . $e->getMessage() . "\n";
}

// 生成套装特殊效果的辅助函数
function generateSetEffects($setConfig) {
    $baseAttack = 100;
    $baseDefense = 80;
    $baseHp = 300;
    
    $effects = [
        "two_piece" => [
            "physical_attack" => $baseAttack,
            "physical_defense" => $baseDefense,
            "hp_bonus" => $baseHp
        ],
        "four_piece" => [
            "physical_attack" => $baseAttack * 2,
            "physical_defense" => $baseDefense * 2,
            "hp_bonus" => $baseHp * 2,
            "critical_bonus" => 0.05
        ],
        "six_piece" => [
            "physical_attack" => $baseAttack * 3,
            "physical_defense" => $baseDefense * 3,
            "hp_bonus" => $baseHp * 3,
            "critical_bonus" => 0.1
        ]
    ];
    
    // 根据套装类型添加特殊效果
    switch ($setConfig['type']) {
        case 'attack':
            $effects["two_piece"]["special_effect"] = getAttackEffect($setConfig['effect'], $setConfig['rate']);
            $effects["four_piece"]["special_effect"] = getAttackEffect($setConfig['effect'], $setConfig['rate'] + 5);
            $effects["six_piece"]["special_effect"] = getAttackEffect($setConfig['effect'], $setConfig['rate'] + 10);
            break;
            
        case 'defense':
            $effects["two_piece"]["special_effect"] = getDefenseEffect($setConfig['effect'], $setConfig['rate']);
            $effects["four_piece"]["special_effect"] = getDefenseEffect($setConfig['effect'], $setConfig['rate'] + 5);
            $effects["six_piece"]["special_effect"] = getDefenseEffect($setConfig['effect'], $setConfig['rate'] + 10);
            break;
            
        case 'mixed':
            $effects["two_piece"]["special_effect"] = getMixedEffect($setConfig['effect'], $setConfig['rate'], 'basic');
            $effects["four_piece"]["special_effect"] = getMixedEffect($setConfig['effect'], $setConfig['rate'], 'enhanced');
            $effects["six_piece"]["special_effect"] = getMixedEffect($setConfig['effect'], $setConfig['rate'], 'ultimate');
            break;
    }
    
    return $effects;
}

function getAttackEffect($effectType, $rate) {
    switch ($effectType) {
        case '暴击伤害':
            return "攻击时有触发概率{$rate}%造成额外100%暴击伤害";
        case '连击':
            return "攻击时有触发概率{$rate}%进行连击攻击";
        case '破甲':
            return "攻击时有触发概率{$rate}%无视敌人50%防御";
        case '吸血':
            return "攻击时有触发概率{$rate}%吸血回复造成伤害的30%";
        default:
            return "攻击时有触发概率{$rate}%造成额外伤害";
    }
}

function getDefenseEffect($effectType, $rate) {
    switch ($effectType) {
        case '格挡':
            return "受到攻击时有触发概率{$rate}%格挡减少40%伤害";
        case '反击':
            return "受到攻击时有触发概率{$rate}%反击造成攻击力60%的伤害";
        case '荆棘反弹':
            return "受到攻击时有触发概率{$rate}%荆棘反弹35%伤害";
        case '免疫':
            return "受到攻击时有触发概率{$rate}%完全免疫伤害";
        default:
            return "受到攻击时有触发概率{$rate}%减少伤害";
    }
}

function getMixedEffect($effectType, $rate, $level) {
    $effects = explode('+', $effectType);
    $effect1 = trim($effects[0]);
    $effect2 = isset($effects[1]) ? trim($effects[1]) : '';
    
    $multiplier = ($level === 'basic') ? 1 : (($level === 'enhanced') ? 1.2 : 1.5);
    $adjustedRate = intval($rate * $multiplier);
    
    $attackPart = '';
    $defensePart = '';
    
    // 处理攻击效果
    if (in_array($effect1, ['暴击', '连击', '破甲', '吸血'])) {
        $attackPart = getAttackEffect($effect1 . ($effect1 === '暴击' ? '伤害' : ''), $adjustedRate);
    }
    if (in_array($effect2, ['暴击', '连击', '破甲', '吸血'])) {
        $attackPart = getAttackEffect($effect2 . ($effect2 === '暴击' ? '伤害' : ''), $adjustedRate);
    }

    // 处理防御效果
    if (in_array($effect1, ['格挡', '反击', '荆棘', '免疫'])) {
        $defensePart = getDefenseEffect($effect1 . ($effect1 === '荆棘' ? '反弹' : ''), $adjustedRate);
    }
    if (in_array($effect2, ['格挡', '反击', '荆棘', '免疫'])) {
        $defensePart = getDefenseEffect($effect2 . ($effect2 === '荆棘' ? '反弹' : ''), $adjustedRate);
    }
    
    if ($attackPart && $defensePart) {
        return $attackPart . '，' . $defensePart;
    }
    
    return $attackPart ?: $defensePart ?: "综合效果触发概率{$adjustedRate}%";
}
?>
