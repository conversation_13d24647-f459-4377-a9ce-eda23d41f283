-- 一念修仙数据库常用查询
-- 使用 SQLTools 扩展执行这些查询

-- ==========================================
-- 用户和角色查询
-- ==========================================

-- 查看所有用户
SELECT id, username, email, created_at, last_login 
FROM users 
ORDER BY created_at DESC 
LIMIT 10;

-- 查看用户的角色信息
SELECT u.username, c.character_name, c.level, c.realm, c.experience
FROM users u
JOIN characters c ON u.id = c.user_id
ORDER BY c.level DESC;

-- 查看在线用户
SELECT u.username, c.character_name, c.level
FROM users u
JOIN characters c ON u.id = c.user_id
WHERE u.last_login > DATE_SUB(NOW(), INTERVAL 1 HOUR);

-- ==========================================
-- 角色属性查询
-- ==========================================

-- 查看角色详细属性
SELECT character_name, level, realm, experience, 
       health, mana, attack, defense, speed,
       spiritual_root, cultivation_technique
FROM characters
WHERE id = 1;

-- 查看等级排行榜
SELECT character_name, level, realm, experience
FROM characters
ORDER BY level DESC, experience DESC
LIMIT 20;

-- 查看战力排行榜
SELECT character_name, level, realm, 
       (attack + defense + speed) as total_power
FROM characters
ORDER BY total_power DESC
LIMIT 20;

-- ==========================================
-- 装备查询
-- ==========================================

-- 查看角色装备
SELECT ce.*, gi.name as item_name, gi.quality, gi.type
FROM character_equipment ce
JOIN game_items gi ON ce.item_id = gi.id
WHERE ce.character_id = 1 AND ce.equipped = 1;

-- 查看背包物品
SELECT ci.*, gi.name, gi.quality, gi.type, gi.description
FROM character_inventory ci
JOIN game_items gi ON ci.item_id = gi.id
WHERE ci.character_id = 1
ORDER BY gi.quality DESC, gi.name;

-- 查看装备套装效果
SELECT character_id, set_name, pieces_count, set_bonus
FROM character_equipment_sets
WHERE character_id = 1;

-- ==========================================
-- 战斗和竞技场查询
-- ==========================================

-- 查看战斗记录
SELECT br.*, c1.character_name as attacker_name, 
       c2.character_name as defender_name
FROM battle_records br
LEFT JOIN characters c1 ON br.attacker_id = c1.id
LEFT JOIN characters c2 ON br.defender_id = c2.id
ORDER BY br.battle_time DESC
LIMIT 10;

-- 查看竞技场排名
SELECT iar.*, c.character_name, c.level, c.realm
FROM immortal_arena_ranks iar
JOIN characters c ON iar.character_id = c.id
ORDER BY iar.rank ASC
LIMIT 20;

-- 查看竞技场匹配池
SELECT iamp.*, c.character_name, c.level, c.realm
FROM immortal_arena_match_pool iamp
JOIN characters c ON iamp.character_id = c.id
ORDER BY iamp.dao_power DESC;

-- ==========================================
-- 修炼和技能查询
-- ==========================================

-- 查看角色技能
SELECT cs.*, gt.name as technique_name, gt.description
FROM character_skills cs
JOIN game_techniques gt ON cs.technique_id = gt.id
WHERE cs.character_id = 1;

-- 查看修炼记录
SELECT * FROM cultivation_records
WHERE character_id = 1
ORDER BY cultivation_time DESC
LIMIT 10;

-- 查看灵根信息
SELECT character_name, spiritual_root, 
       spiritual_root_quality, spiritual_root_attributes
FROM characters
WHERE spiritual_root IS NOT NULL;

-- ==========================================
-- 商店和交易查询
-- ==========================================

-- 查看商店物品
SELECT si.*, gi.name, gi.quality, gi.type, gi.description
FROM shop_items si
JOIN game_items gi ON si.item_id = gi.id
WHERE si.available = 1
ORDER BY si.price ASC;

-- 查看交易记录
SELECT * FROM transaction_logs
ORDER BY transaction_time DESC
LIMIT 20;

-- ==========================================
-- 系统统计查询
-- ==========================================

-- 用户统计
SELECT 
    COUNT(*) as total_users,
    COUNT(CASE WHEN last_login > DATE_SUB(NOW(), INTERVAL 1 DAY) THEN 1 END) as daily_active,
    COUNT(CASE WHEN last_login > DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as weekly_active
FROM users;

-- 角色等级分布
SELECT 
    CASE 
        WHEN level BETWEEN 1 AND 10 THEN '1-10级'
        WHEN level BETWEEN 11 AND 30 THEN '11-30级'
        WHEN level BETWEEN 31 AND 50 THEN '31-50级'
        WHEN level BETWEEN 51 AND 80 THEN '51-80级'
        ELSE '80级以上'
    END as level_range,
    COUNT(*) as count
FROM characters
GROUP BY level_range
ORDER BY MIN(level);

-- 装备品质分布
SELECT gi.quality, COUNT(*) as count
FROM character_inventory ci
JOIN game_items gi ON ci.item_id = gi.id
GROUP BY gi.quality
ORDER BY 
    CASE gi.quality
        WHEN '神话' THEN 5
        WHEN '传说' THEN 4
        WHEN '史诗' THEN 3
        WHEN '稀有' THEN 2
        WHEN '普通' THEN 1
        ELSE 0
    END DESC;
