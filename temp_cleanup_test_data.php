<?php
/**
 * 清理角色ID=31的测试套装装备数据
 * 只删除刚才添加的测试数据，保留原有游戏数据
 */

require_once 'src/config/database.php';

try {
    $db = getDatabase();
    $characterId = 31;
    
    echo "=== 开始清理角色ID={$characterId}的测试数据 ===\n\n";
    
    // 1. 查询角色信息
    $stmt = $db->prepare("SELECT character_name, user_id FROM characters WHERE id = ?");
    $stmt->execute([$characterId]);
    $character = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$character) {
        throw new Exception("未找到角色ID={$characterId}");
    }
    
    echo "目标角色: {$character['character_name']} (用户ID: {$character['user_id']})\n\n";
    
    // 2. 查找今天添加的测试装备（通过obtained_source='test_equipment'识别）
    $stmt = $db->prepare("
        SELECT ui.id, ui.item_id, gi.item_name, gi.set_id, gis.set_name
        FROM user_inventories ui
        JOIN game_items gi ON ui.item_id = gi.id
        LEFT JOIN game_item_sets gis ON gi.set_id = gis.id
        WHERE ui.character_id = ? AND ui.obtained_source = 'test_equipment'
        ORDER BY gis.set_name, gi.item_name
    ");
    $stmt->execute([$characterId]);
    $testItems = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($testItems)) {
        echo "✅ 未找到需要删除的测试装备数据\n";
        exit(0);
    }
    
    echo "找到 " . count($testItems) . " 件测试装备需要删除:\n\n";
    
    // 3. 按套装分组显示
    $setGroups = [];
    foreach ($testItems as $item) {
        $setName = $item['set_name'] ?: '未知套装';
        if (!isset($setGroups[$setName])) {
            $setGroups[$setName] = [];
        }
        $setGroups[$setName][] = $item;
    }
    
    foreach ($setGroups as $setName => $items) {
        echo "套装: {$setName} ({$items[0]['set_id']})\n";
        foreach ($items as $item) {
            echo "  - {$item['item_name']} (背包ID: {$item['id']}, 物品ID: {$item['item_id']})\n";
        }
        echo "\n";
    }
    
    // 4. 确认删除操作
    echo "=== 开始删除操作 ===\n";
    
    $deletedCount = 0;
    $deletedSets = [];
    
    // 删除背包中的测试装备
    $stmt = $db->prepare("DELETE FROM user_inventories WHERE character_id = ? AND obtained_source = 'test_equipment'");
    $result = $stmt->execute([$characterId]);
    $deletedInventoryItems = $stmt->rowCount();
    
    echo "✅ 已删除背包中的测试装备: {$deletedInventoryItems} 件\n";
    
    // 5. 查找并删除刚才创建的测试套装（通过item_code识别）
    $stmt = $db->prepare("
        SELECT DISTINCT gi.set_id, gis.set_name
        FROM game_items gi
        LEFT JOIN game_item_sets gis ON gi.set_id = gis.id
        WHERE gi.item_code LIKE 'test_set_%'
    ");
    $stmt->execute();
    $testSets = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($testSets)) {
        echo "\n找到 " . count($testSets) . " 个测试套装需要删除:\n";
        foreach ($testSets as $set) {
            echo "  - {$set['set_name']} (ID: {$set['set_id']})\n";
        }
        
        // 删除测试套装的物品
        $stmt = $db->prepare("DELETE FROM game_items WHERE item_code LIKE 'test_set_%'");
        $result = $stmt->execute();
        $deletedItems = $stmt->rowCount();
        
        echo "✅ 已删除测试物品: {$deletedItems} 件\n";
        
        // 删除测试套装配置
        $testSetIds = array_column($testSets, 'set_id');
        if (!empty($testSetIds)) {
            $placeholders = str_repeat('?,', count($testSetIds) - 1) . '?';
            $stmt = $db->prepare("DELETE FROM game_item_sets WHERE id IN ({$placeholders})");
            $result = $stmt->execute($testSetIds);
            $deletedSets = $stmt->rowCount();
            
            echo "✅ 已删除测试套装配置: {$deletedSets} 个\n";
        }
    }
    
    // 6. 验证清理结果
    echo "\n=== 清理结果验证 ===\n";
    
    // 检查背包中是否还有测试装备
    $stmt = $db->prepare("
        SELECT COUNT(*) as count 
        FROM user_inventories 
        WHERE character_id = ? AND obtained_source = 'test_equipment'
    ");
    $stmt->execute([$characterId]);
    $remainingTestItems = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    // 检查是否还有测试物品
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM game_items WHERE item_code LIKE 'test_set_%'");
    $stmt->execute();
    $remainingTestGameItems = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    echo "背包中剩余测试装备: {$remainingTestItems} 件\n";
    echo "数据库中剩余测试物品: {$remainingTestGameItems} 件\n";
    
    if ($remainingTestItems == 0 && $remainingTestGameItems == 0) {
        echo "\n✅ 测试数据清理完成！所有测试装备已成功删除。\n";
    } else {
        echo "\n⚠️  警告：仍有测试数据残留，请检查。\n";
    }
    
    // 7. 显示角色当前的装备情况
    echo "\n=== 角色当前装备情况 ===\n";
    $stmt = $db->prepare("
        SELECT COUNT(*) as total_items,
               COUNT(DISTINCT gi.set_id) as unique_sets
        FROM user_inventories ui
        JOIN game_items gi ON ui.item_id = gi.id
        WHERE ui.character_id = ? AND gi.set_id IS NOT NULL
    ");
    $stmt->execute([$characterId]);
    $currentStats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "当前背包中套装装备总数: {$currentStats['total_items']} 件\n";
    echo "涉及套装种类: {$currentStats['unique_sets']} 个\n";
    
    echo "\n=== 清理操作完成 ===\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    exit(1);
}
?>
