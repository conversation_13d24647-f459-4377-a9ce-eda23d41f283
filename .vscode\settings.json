{
    // PHP配置
    "php.validate.executablePath": "E:/phpstudy_pro/Extensions/php/php7.4.3nts/php.exe",
    "php.debug.executablePath": "E:/phpstudy_pro/Extensions/php/php7.4.3nts/php.exe",
    "php.executablePath": "E:/phpstudy_pro/Extensions/php/php7.4.3nts/php.exe",

    // Intelephense配置
    "intelephense.files.maxSize": 5000000,
    "intelephense.completion.insertUseDeclaration": true,
    "intelephense.completion.fullyQualifyGlobalConstantsAndFunctions": false,

    // 代码格式化
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "[php]": {
        "editor.defaultFormatter": "bmewburn.vscode-intelephense-client",
        "editor.tabSize": 4,
        "editor.insertSpaces": true
    },
    "[javascript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode",
        "editor.tabSize": 4
    },
    "[html]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode",
        "editor.tabSize": 4
    },
    "[css]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode",
        "editor.tabSize": 4
    },

    // 文件关联
    "files.associations": {
        "*.php": "php",
        "*.html": "html",
        "*.js": "javascript",
        "*.css": "css"
    },

    // Live Server配置
    "liveServer.settings.root": "/",
    "liveServer.settings.CustomBrowser": "chrome",
    "liveServer.settings.port": 5500,

    // Git配置
    "git.enableSmartCommit": true,
    "git.confirmSync": false,

    // 编辑器配置
    "editor.tabSize": 4,
    "editor.insertSpaces": true,
    "editor.wordWrap": "on",
    "editor.minimap.enabled": true,

    // 文件排除
    "files.exclude": {
        "**/node_modules": true,
        "**/vendor": true,
        "**/.git": true,
        "**/temp_*": true,
        "**/test_*": true,
        "**/debug_*": true
    },

    // 搜索排除
    "search.exclude": {
        "**/node_modules": true,
        "**/vendor": true,
        "**/.git": true,
        "**/temp_*": true,
        "**/test_*": true,
        "**/debug_*": true
    },

    // SQLTools数据库连接配置
    "sqltools.connections": [
        {
            "name": "一念修仙-主数据库",
            "driver": "MySQL",
            "server": "localhost",
            "port": 3306,
            "database": "yn_game",
            "username": "ynxx",
            "password": "mjlxz159",
            "connectionTimeout": 60
        }
    ],

    // REST Client配置
    "rest-client.environmentVariables": {
        "local": {
            "baseUrl": "http://localhost/yinian",
            "apiUrl": "http://localhost/yinian/src/api"
        }
    },

    // Intelephense高级配置
    "intelephense.environment.includePaths": ["src/includes", "src/config"],
    "intelephense.files.associations": ["*.php", "*.phtml"],

    // PHP代码检查配置 - 已禁用以避免phpcs未安装的错误
    "phpcs.enable": false,
    "phpcs.standard": "PSR12",
    "php.validate.enable": true,

    // 项目特定配置
    "emmet.includeLanguages": {
        "php": "html"
    },

    // 自动保存配置
    "files.autoSave": "afterDelay",
    "files.autoSaveDelay": 1000,

    // AI和智能开发配置
    "github.copilot.enable": {
        "*": false
    },
    "github.copilot.advanced": {
        "secret_key": "github_copilot_key",
        "length": 500,
        "temperature": 0.1,
        "top_p": 1,
        "indentationMode": {
            "python": "spaces",
            "javascript": "spaces",
            "php": "spaces"
        }
    },

    // 代码安全扫描
    "sonarlint.rules": {
        "php:S1192": "off",
        "php:S138": "off"
    },
    "snyk.advanced.additionalParameters": "--severity-threshold=medium",

    // 性能监控
    "todo-tree.general.tags": [
        "BUG",
        "HACK",
        "FIXME",
        "TODO",
        "XXX",
        "[ ]",
        "[x]",
        "OPTIMIZE",
        "SECURITY"
    ],
    "todo-tree.highlights.defaultHighlight": {
        "icon": "alert",
        "type": "tag",
        "foreground": "red",
        "background": "white",
        "opacity": 50,
        "iconColour": "blue"
    },

    // MCP配置 - 智能开发助手
    "mcp.servers": {
        "filesystem": {
            "command": "npx",
            "args": ["-y", "@modelcontextprotocol/server-filesystem", "e:/phpstudy_pro/WWW/yinian"],
            "env": {
                "ALLOWED_DIRS": "e:/phpstudy_pro/WWW/yinian"
            }
        },
        "database": {
            "command": "npx",
            "args": ["-y", "@modelcontextprotocol/server-mysql"],
            "env": {
                "MYSQL_HOST": "localhost",
                "MYSQL_PORT": "3306",
                "MYSQL_USER": "ynxx",
                "MYSQL_PASSWORD": "mjlxz159",
                "MYSQL_DATABASE": "yn_game"
            }
        },
        "git": {
            "command": "npx",
            "args": ["-y", "@modelcontextprotocol/server-git", "--repository", "."],
            "env": {
                "GIT_AUTHOR_NAME": "一念修仙开发者",
                "GIT_AUTHOR_EMAIL": "<EMAIL>"
            }
        }
    },

    // 智能代码分析
    "ai.codeAnalysis": {
        "enabled": true,
        "autoSuggest": true,
        "contextAware": true,
        "projectSpecific": true
    },

    // 高级编辑器功能
    "editor.suggest.snippetsPreventQuickSuggestions": false,
    "editor.inlineSuggest.enabled": true,
    "editor.bracketPairColorization.enabled": true,
    "editor.guides.bracketPairs": "active",
    "editor.codeActionsOnSave": {
        "source.fixAll": "explicit",
        "source.organizeImports": "explicit"
    },

    // 智能感知增强
    "typescript.suggest.autoImports": true,
    "javascript.suggest.autoImports": true,
    "php.suggest.basic": true,

    // 工作区信任
    "security.workspace.trust.untrustedFiles": "open",
    "security.workspace.trust.banner": "never",
    "security.workspace.trust.startupPrompt": "never"
}
