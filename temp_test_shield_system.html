<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>护盾系统完整测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .test-button:hover {
            transform: scale(1.05);
        }
        .test-button.primary {
            background: linear-gradient(135deg, #2196F3, #1976D2);
        }
        .test-button.danger {
            background: linear-gradient(135deg, #f44336, #d32f2f);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        .shield-demo {
            border: 2px solid #4da6ff;
            background: rgba(77, 166, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .shield-value {
            color: #4da6ff;
            font-weight: bold;
            text-shadow: 0 0 4px #4da6ff;
            font-size: 18px;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .log-container {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background: #f8f9fa;
            font-family: monospace;
            font-size: 12px;
        }
        .test-steps {
            background: #e3f2fd;
            padding: 15px;
            border-left: 4px solid #2196F3;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🛡️ 护盾系统完整测试</h1>
    
    <div class="test-section">
        <h2>📋 测试目标</h2>
        <div class="info status">
            <strong>本次测试将验证以下功能：</strong>
            <ol>
                <li>✅ 后端数据传输：套装效果数据是否正确传递到前端</li>
                <li>🔧 字段映射修复：hp_bonus → max_hp 转换是否生效</li>
                <li>🛡️ 护盾计算：战斗开始时护盾值是否正确计算</li>
                <li>🎨 UI显示：护盾值是否在界面中正确显示</li>
                <li>⚔️ 伤害吸收：护盾是否能正确吸收伤害</li>
                <li>✨ 视觉效果：护盾吸收时是否有动画效果</li>
            </ol>
        </div>
    </div>

    <div class="test-section">
        <h2>🔧 测试工具</h2>
        
        <button class="test-button primary" onclick="testDataIntegrity()">
            📊 1. 测试数据完整性
        </button>
        
        <button class="test-button primary" onclick="testFieldMapping()">
            🔄 2. 测试字段映射
        </button>
        
        <button class="test-button primary" onclick="openBattleWithDebug()">
            ⚔️ 3. 打开战斗页面(调试模式)
        </button>
        
        <button class="test-button" onclick="simulateShieldSystem()">
            🧪 4. 模拟护盾系统
        </button>
        
        <button class="test-button danger" onclick="clearLogs()">
            🗑️ 清空日志
        </button>
        
        <div id="testResults"></div>
    </div>

    <div class="test-section">
        <h2>🛡️ 护盾系统演示</h2>
        <div class="shield-demo">
            <h3>护盾效果预览</h3>
            <div>玩家生命值: <span id="demo-hp">10000/10000</span></div>
            <div>护盾值: <span id="demo-shield" class="shield-value">🛡️0</span></div>
            <div style="margin-top: 10px;">
                <button class="test-button" onclick="addShield()">添加护盾</button>
                <button class="test-button" onclick="takeDamage()">受到伤害</button>
                <button class="test-button" onclick="resetDemo()">重置</button>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>📝 测试步骤</h2>
        <div class="test-steps">
            <h3>🔍 手动验证步骤：</h3>
            <ol>
                <li><strong>点击"1. 测试数据完整性"</strong> - 验证后端API返回正确的套装效果数据</li>
                <li><strong>点击"2. 测试字段映射"</strong> - 验证hp_bonus到max_hp的转换</li>
                <li><strong>点击"3. 打开战斗页面"</strong> - 在实际战斗中测试护盾系统</li>
                <li><strong>在战斗页面中：</strong>
                    <ul>
                        <li>打开浏览器开发者工具(F12) → Console标签页</li>
                        <li>观察战斗开始时的控制台输出</li>
                        <li>检查是否显示"🔥 套装效果触发检查 - 战斗开始时"</li>
                        <li>检查是否显示"🛡️ 护盾效果: +XXX"</li>
                        <li>观察HP条旁边是否显示蓝色护盾图标</li>
                        <li>进行战斗，观察护盾是否正确吸收伤害</li>
                    </ul>
                </li>
            </ol>
        </div>
    </div>

    <div class="test-section">
        <h2>📝 实时日志</h2>
        <div id="logContainer" class="log-container">
            <div>等待测试开始...</div>
        </div>
    </div>

    <script>
        let logContainer = document.getElementById('logContainer');
        let testResults = document.getElementById('testResults');
        let demoHp = 10000;
        let demoMaxHp = 10000;
        let demoShield = 0;

        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span style="color: #666;">[${timestamp}]</span> ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function showResult(message, type = 'info') {
            const resultElement = document.createElement('div');
            resultElement.className = `status ${type}`;
            resultElement.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            testResults.appendChild(resultElement);
            testResults.scrollTop = testResults.scrollHeight;
        }

        async function testDataIntegrity() {
            try {
                addLog('🔄 正在测试数据完整性...', 'info');
                showResult('🔄 测试步骤1：验证后端数据传输...', 'info');
                
                const response = await fetch('../src/api/battle_unified.php?action=init_battle&map_id=1&map_code=kunlun&stage_number=1');
                const result = await response.json();
                
                if (result.success && result.data && result.data.player_data) {
                    const playerData = result.data.player_data;
                    addLog('✅ 后端API调用成功');
                    showResult('✅ 后端API调用成功', 'success');
                    
                    // 检查关键字段
                    const checks = [
                        { field: 'hp_bonus', value: playerData.hp_bonus, expected: 'number' },
                        { field: 'set_special_effects', value: playerData.set_special_effects, expected: 'array' },
                        { field: 'physical_attack', value: playerData.physical_attack, expected: 'number' },
                        { field: 'immortal_attack', value: playerData.immortal_attack, expected: 'number' }
                    ];
                    
                    let allPassed = true;
                    checks.forEach(check => {
                        const actualType = Array.isArray(check.value) ? 'array' : typeof check.value;
                        if (actualType === check.expected && check.value !== undefined) {
                            addLog(`✅ ${check.field}: ${actualType} = ${JSON.stringify(check.value)}`);
                        } else {
                            addLog(`❌ ${check.field}: 期望${check.expected}，实际${actualType} = ${check.value}`, 'error');
                            allPassed = false;
                        }
                    });
                    
                    if (allPassed) {
                        showResult('✅ 数据完整性检查通过', 'success');
                    } else {
                        showResult('❌ 数据完整性检查失败', 'error');
                    }
                    
                    // 详细显示套装效果
                    if (playerData.set_special_effects && playerData.set_special_effects.length > 0) {
                        addLog(`📊 找到${playerData.set_special_effects.length}个套装特殊效果:`);
                        playerData.set_special_effects.forEach((effect, index) => {
                            addLog(`  ${index + 1}. ${effect.set_name}: ${effect.effect}`);
                            if (effect.effect.includes('护盾')) {
                                showResult(`🛡️ 发现护盾效果: ${effect.effect}`, 'success');
                            }
                        });
                    }
                    
                } else {
                    addLog('❌ 后端API调用失败', 'error');
                    showResult(`❌ 后端API调用失败: ${result.message || '未知错误'}`, 'error');
                }
            } catch (error) {
                addLog(`❌ 请求失败: ${error.message}`, 'error');
                showResult(`❌ 请求失败: ${error.message}`, 'error');
            }
        }

        async function testFieldMapping() {
            addLog('🔄 正在测试字段映射...', 'info');
            showResult('🔄 测试步骤2：验证字段映射修复...', 'info');
            
            // 模拟battle-manager.js中的字段转换逻辑
            const mockPlayerData = {
                hp_bonus: 8500,
                mp_bonus: 2100,
                physical_attack: 245,
                immortal_attack: 180
            };
            
            addLog('📊 模拟玩家数据:', 'info');
            addLog(`  hp_bonus: ${mockPlayerData.hp_bonus}`);
            addLog(`  mp_bonus: ${mockPlayerData.mp_bonus}`);
            
            // 应用字段转换逻辑
            const finalStats = { ...mockPlayerData };
            
            if (finalStats.hp_bonus && !finalStats.max_hp) {
                finalStats.max_hp = finalStats.hp_bonus;
                addLog(`🔧 HP字段转换: hp_bonus(${finalStats.hp_bonus}) → max_hp(${finalStats.max_hp})`);
                showResult(`✅ HP字段转换成功: ${finalStats.max_hp}`, 'success');
            }
            
            if (finalStats.mp_bonus && !finalStats.max_mp) {
                finalStats.max_mp = finalStats.mp_bonus;
                addLog(`🔧 MP字段转换: mp_bonus(${finalStats.mp_bonus}) → max_mp(${finalStats.max_mp})`);
                showResult(`✅ MP字段转换成功: ${finalStats.max_mp}`, 'success');
            }
            
            // 计算预期护盾值
            const expectedShield = Math.floor(finalStats.max_hp * 0.2); // 20%护盾
            addLog(`🛡️ 预期护盾值: ${expectedShield} (基于${finalStats.max_hp}最大生命值的20%)`);
            showResult(`🛡️ 预期护盾值: ${expectedShield}`, 'info');
        }

        function openBattleWithDebug() {
            addLog('🚀 正在打开战斗页面(调试模式)...');
            showResult('🚀 测试步骤3：打开战斗页面进行实际测试...', 'info');
            
            // 提示用户观察要点
            showResult('📋 请在战斗页面中观察以下要点：', 'warning');
            showResult('1. 打开浏览器开发者工具(F12) → Console标签页', 'info');
            showResult('2. 观察战斗开始时是否显示"🔥 套装效果触发检查"', 'info');
            showResult('3. 检查是否显示"🛡️ 护盾效果: +XXX"', 'info');
            showResult('4. 观察HP条旁边是否显示蓝色🛡️图标', 'info');
            showResult('5. 进行战斗测试护盾吸收效果', 'info');
            
            // 在新窗口中打开战斗页面
            const battleWindow = window.open('battle.html?map_code=kunlun&stage=1', '_blank');
            
            // 等待页面加载后注入调试代码
            setTimeout(() => {
                if (battleWindow && !battleWindow.closed) {
                    try {
                        // 注入调试代码到战斗页面
                        battleWindow.console.log('🔧 调试模式已激活');
                        battleWindow.addEventListener('load', () => {
                            battleWindow.console.log('=== 护盾系统调试开始 ===');
                        });
                    } catch (e) {
                        addLog('⚠️ 无法注入调试代码（跨域限制），请手动在控制台中观察', 'warning');
                    }
                }
            }, 2000);
        }

        function simulateShieldSystem() {
            addLog('🧪 开始模拟护盾系统...', 'info');
            showResult('🧪 测试步骤4：模拟护盾系统运行...', 'info');
            
            // 模拟护盾计算
            const maxHp = 8500;
            const shieldPercent = 20;
            const shieldAmount = Math.floor(maxHp * shieldPercent / 100);
            
            addLog(`🛡️ 护盾计算: ${maxHp} × ${shieldPercent}% = ${shieldAmount}`);
            showResult(`🛡️ 护盾计算结果: ${shieldAmount}点护盾`, 'success');
            
            // 模拟伤害吸收
            let currentShield = shieldAmount;
            let currentHp = maxHp;
            const damages = [500, 1000, 1500, 2000];
            
            addLog('⚔️ 模拟伤害吸收测试:');
            damages.forEach((damage, index) => {
                addLog(`  第${index + 1}次攻击: ${damage}点伤害`);
                
                if (currentShield > 0) {
                    if (currentShield >= damage) {
                        currentShield -= damage;
                        addLog(`    护盾吸收全部伤害，剩余护盾: ${currentShield}`);
                    } else {
                        const remainingDamage = damage - currentShield;
                        currentHp -= remainingDamage;
                        addLog(`    护盾吸收${currentShield}伤害，生命值受到${remainingDamage}伤害`);
                        currentShield = 0;
                        addLog(`    当前状态: HP ${currentHp}/${maxHp}, 护盾 ${currentShield}`);
                    }
                } else {
                    currentHp -= damage;
                    addLog(`    直接伤害生命值: ${damage}点`);
                    addLog(`    当前状态: HP ${currentHp}/${maxHp}, 护盾 ${currentShield}`);
                }
            });
            
            showResult(`✅ 护盾系统模拟完成，最终状态: HP ${currentHp}/${maxHp}, 护盾 ${currentShield}`, 'success');
        }

        // 护盾演示功能
        function addShield() {
            demoShield = Math.floor(demoMaxHp * 0.2);
            updateDemo();
            addLog(`🛡️ 演示：添加护盾 ${demoShield}点`);
        }

        function takeDamage() {
            const damage = 1000;
            addLog(`⚔️ 演示：受到 ${damage}点伤害`);
            
            if (demoShield > 0) {
                if (demoShield >= damage) {
                    demoShield -= damage;
                    addLog(`🛡️ 护盾吸收全部伤害，剩余护盾: ${demoShield}`);
                } else {
                    const remainingDamage = damage - demoShield;
                    demoHp -= remainingDamage;
                    addLog(`🛡️ 护盾吸收${demoShield}伤害，生命值受到${remainingDamage}伤害`);
                    demoShield = 0;
                }
            } else {
                demoHp -= damage;
                addLog(`💔 直接伤害生命值: ${damage}点`);
            }
            
            demoHp = Math.max(0, demoHp);
            updateDemo();
        }

        function resetDemo() {
            demoHp = demoMaxHp;
            demoShield = 0;
            updateDemo();
            addLog('🔄 演示重置');
        }

        function updateDemo() {
            document.getElementById('demo-hp').textContent = `${demoHp}/${demoMaxHp}`;
            document.getElementById('demo-shield').textContent = demoShield > 0 ? `🛡️${demoShield}` : '🛡️0';
            document.getElementById('demo-shield').style.display = demoShield > 0 ? 'inline' : 'inline';
            document.getElementById('demo-shield').style.opacity = demoShield > 0 ? '1' : '0.3';
        }

        function clearLogs() {
            logContainer.innerHTML = '<div>日志已清空，等待新的测试...</div>';
            testResults.innerHTML = '';
            addLog('🗑️ 日志已清空');
        }

        // 页面加载时的初始化
        window.onload = function() {
            addLog('📋 护盾系统测试页面已加载');
            showResult('📋 测试页面已准备就绪，请按顺序执行测试步骤', 'info');
            updateDemo();
        };
    </script>
</body>
</html>
