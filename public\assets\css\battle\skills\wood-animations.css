/* 木系技能动画样式 - 藤蔓缠绕 */

.tengman-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1000;
}

/* === 第一阶段：蓄力 - 自然生机汇聚 === */

/* 地面魔法阵 */
.tengman-magic-circle {
    position: absolute;
    width: 120px;
    height: 120px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, 
        rgba(34, 139, 34, 0.9) 0%, 
        rgba(0, 128, 0, 0.7) 30%, 
        rgba(50, 205, 50, 0.5) 60%, 
        transparent 80%);
    border: 3px solid rgba(34, 139, 34, 0.8);
    border-radius: 50%;
    box-shadow: 
        0 0 30px rgba(34, 139, 34, 0.8),
        inset 0 0 40px rgba(0, 128, 0, 0.4);
    animation: tengman-magic-circle 0.6s ease-out;
}

@keyframes tengman-magic-circle {
    0% {
        transform: translate(-50%, -50%) scale(0.1) rotate(0deg);
        opacity: 0;
    }
    30% {
        transform: translate(-50%, -50%) scale(0.8) rotate(120deg);
        opacity: 0.8;
    }
    100% {
        transform: translate(-50%, -50%) scale(1) rotate(360deg);
        opacity: 1;
    }
}

/* 内圈符文 */
.tengman-inner-runes {
    position: absolute;
    width: 80px;
    height: 80px;
    transform: translate(-50%, -50%);
    background: 
        repeating-conic-gradient(
            from 0deg,
            rgba(144, 238, 144, 0.8) 0deg 15deg,
            transparent 15deg 30deg
        );
    border-radius: 50%;
    animation: tengman-inner-runes 0.6s ease-out;
}

@keyframes tengman-inner-runes {
    0% {
        transform: translate(-50%, -50%) scale(0) rotate(0deg);
        opacity: 0;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.2) rotate(-180deg);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(1) rotate(-360deg);
        opacity: 0.9;
    }
}

/* 外圈符文 */
.tengman-outer-runes {
    position: absolute;
    width: 140px;
    height: 140px;
    transform: translate(-50%, -50%);
    background: 
        repeating-conic-gradient(
            from 0deg,
            rgba(34, 139, 34, 0.6) 0deg 20deg,
            transparent 20deg 40deg
        );
    border-radius: 50%;
    animation: tengman-outer-runes 0.6s ease-out;
}

@keyframes tengman-outer-runes {
    0% {
        transform: translate(-50%, -50%) scale(0) rotate(0deg);
        opacity: 0;
    }
    40% {
        transform: translate(-50%, -50%) scale(1.1) rotate(240deg);
        opacity: 0.8;
    }
    100% {
        transform: translate(-50%, -50%) scale(1) rotate(480deg);
        opacity: 0.7;
    }
}

/* 武器图片在魔法阵中心旋转 */
.tengman-weapon-sprite {
    position: absolute;
    width: 100px;
    height: 100px;
    transform: translate(-50%, -50%);
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    z-index: 1001; /* 确保在魔法阵之上 */
    animation: tengman-weapon-sprite 0.6s ease-out;
}

@keyframes tengman-weapon-sprite {
    0% {
        transform: translate(-50%, -50%) scale(0) rotate(0deg);
        opacity: 0;
        -webkit-filter: brightness(1) drop-shadow(0 0 0px rgba(144, 238, 144, 0));
        filter: brightness(1) drop-shadow(0 0 0px rgba(144, 238, 144, 0));
    }
    20% {
        transform: translate(-50%, -50%) scale(0.8) rotate(90deg);
        opacity: 0.7;
        -webkit-filter: brightness(1.2) drop-shadow(0 0 10px rgba(144, 238, 144, 0.6));
        filter: brightness(1.2) drop-shadow(0 0 10px rgba(144, 238, 144, 0.6));
    }
    60% {
        transform: translate(-50%, -50%) scale(1.1) rotate(270deg);
        opacity: 1;
        -webkit-filter: brightness(1.4) drop-shadow(0 0 20px rgba(144, 238, 144, 0.9));
        filter: brightness(1.4) drop-shadow(0 0 20px rgba(144, 238, 144, 0.9));
    }
    80% {
        transform: translate(-50%, -50%) scale(1.2) rotate(360deg);
        opacity: 1;
        -webkit-filter: brightness(1.5) drop-shadow(0 0 25px rgba(144, 238, 144, 1));
        filter: brightness(1.5) drop-shadow(0 0 25px rgba(144, 238, 144, 1));
    }
    100% {
        transform: translate(-50%, -50%) scale(0.3) rotate(450deg);
        opacity: 0;
        -webkit-filter: brightness(2) drop-shadow(0 0 30px rgba(144, 238, 144, 0.8));
        filter: brightness(2) drop-shadow(0 0 30px rgba(144, 238, 144, 0.8));
    }
}

/* 蓄力能量核心 */
.tengman-energy-core {
    position: absolute;
    width: 20px;
    height: 20px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle,
        rgba(144, 238, 144, 1) 0%,
        rgba(50, 205, 50, 0.9) 40%,
        rgba(34, 139, 34, 0.7) 80%,
        transparent 100%);
    border-radius: 50%;
    box-shadow: 0 0 20px rgba(144, 238, 144, 1);
    animation: tengman-energy-core 0.6s ease-out;
}

@keyframes tengman-energy-core {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0;
    }
    50% {
        transform: translate(-50%, -50%) scale(2);
        opacity: 1;
        box-shadow: 0 0 40px rgba(144, 238, 144, 1);
    }
    100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.9;
        box-shadow: 0 0 20px rgba(144, 238, 144, 0.8);
    }
}

/* 蓄力绿色粒子 */
.tengman-charge-particle {
    position: absolute;
    width: 4px;
    height: 4px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle,
        rgba(144, 238, 144, 1) 0%,
        rgba(50, 205, 50, 0.8) 60%,
        transparent 100%);
    border-radius: 50%;
    box-shadow: 0 0 8px rgba(50, 205, 50, 0.8);
    animation: tengman-charge-particle 1.2s ease-out infinite;
}

@keyframes tengman-charge-particle {
    0% {
        transform: translate(-50%, -50%) 
                   translate(calc(var(--chargeX) * 3), calc(var(--chargeY) * 3))
                   scale(0);
        opacity: 0;
    }
    30% {
        transform: translate(-50%, -50%) 
                   translate(calc(var(--chargeX) * 1.5), calc(var(--chargeY) * 1.5))
                   scale(1.5);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) 
                   translate(0px, 0px)
                   scale(0.5);
        opacity: 0.8;
    }
}

/* 环绕小草萌发 */
.tengman-charge-grass {
    position: absolute;
    width: 3px;
    height: 15px;
    transform: translate(-50%, -100%);
    background: linear-gradient(to top,
        rgba(34, 139, 34, 1) 0%,
        rgba(50, 205, 50, 0.9) 70%,
        rgba(144, 238, 144, 0.7) 100%);
    border-radius: 2px 2px 0 0;
    transform-origin: bottom center;
    animation: tengman-charge-grass 0.8s ease-out both;
}

@keyframes tengman-charge-grass {
    0% {
        transform: translate(-50%, -100%) scaleY(0) rotate(0deg);
        opacity: 0;
    }
    60% {
        transform: translate(-50%, -100%) scaleY(1.2) rotate(8deg);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -100%) scaleY(1) rotate(0deg);
        opacity: 0.9;
    }
}

/* 能量波纹 */
.tengman-energy-ripple {
    position: absolute;
    width: 60px;
    height: 60px;
    transform: translate(-50%, -50%);
    border: 2px solid rgba(34, 139, 34, 0.8);
    border-radius: 50%;
    animation: tengman-energy-ripple 0.8s ease-out both;
}

@keyframes tengman-energy-ripple {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
        border-width: 3px;
    }
    100% {
        transform: translate(-50%, -50%) scale(2.5);
        opacity: 0;
        border-width: 1px;
    }
}

/* === 第二阶段：发射 - 藤蔓生长延伸 === */

/* 地面震动效果 */
.tengman-ground-shake {
    position: absolute;
    width: 80px;
    height: 80px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle,
        rgba(34, 139, 34, 0.6) 0%,
        rgba(50, 205, 50, 0.4) 50%,
        transparent 100%);
    border-radius: 50%;
    animation: tengman-ground-shake 0.15s ease-in-out;
}

@keyframes tengman-ground-shake {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1);
    }
    50% {
        transform: translate(-50%, -50%) scale(1.3);
    }
}

/* 主藤蔓段 */
.tengman-vine-segment {
    position: absolute;
    transform: translate(-50%, -50%) rotate(var(--vineAngle));
    background: linear-gradient(to bottom,
        rgba(34, 139, 34, 0.9) 0%,
        rgba(50, 205, 50, 0.8) 30%,
        rgba(34, 139, 34, 0.9) 70%,
        rgba(0, 100, 0, 0.7) 100%);
    -webkit-clip-path: polygon(40% 0%, 60% 0%, 80% 100%, 20% 100%);
    clip-path: polygon(40% 0%, 60% 0%, 80% 100%, 20% 100%);
    border-radius: 2px;
    box-shadow: 
        0 0 8px rgba(34, 139, 34, 0.6),
        inset 2px 0 4px rgba(144, 238, 144, 0.4);
    animation: tengman-vine-segment 0.5s ease-out both;
    transition: opacity 0.2s ease-out;
}

/* 移动端中间主线藤蔓 - 更突出 */
.tengman-vine-segment.main-vine {
    -webkit-clip-path: polygon(42% 0%, 58% 0%, 85% 100%, 15% 100%);
    clip-path: polygon(42% 0%, 58% 0%, 85% 100%, 15% 100%);
    box-shadow: 
        0 0 12px rgba(34, 139, 34, 0.8),
        inset 2px 0 6px rgba(144, 238, 144, 0.6);
}

/* 藤蔓淡出效果（蛇身尾部消失） */
.tengman-vine-segment.tengman-vine-fade-out {
    animation: tengman-vine-fade-out 0.2s ease-out both;
}

/* 藤蔓穿透效果（击中敌人后继续前进） */
.tengman-vine-segment.tengman-vine-penetrating {
    animation: tengman-vine-penetrate 0.2s ease-out forwards !important;
}

/* 藤蔓瞬间消失效果（击中敌人后立即淡出） */
.tengman-vine-segment.tengman-vine-instant-fade {
    animation: tengman-vine-instant-fade 0.1s ease-out both;
}

@keyframes tengman-vine-segment {
    0% {
        transform: translate(-50%, -50%) rotate(var(--vineAngle)) scaleY(0);
        opacity: 0;
    }
    100% {
        transform: translate(-50%, -50%) rotate(var(--vineAngle)) scaleY(1);
        opacity: 1;
    }
}

@keyframes tengman-vine-fade-out {
    0% {
        opacity: 1;
        transform: translate(-50%, -50%) rotate(var(--vineAngle)) scaleY(1);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -50%) rotate(var(--vineAngle)) scaleY(0.3);
    }
}

@keyframes tengman-vine-penetrate {
    0% {
        transform: translate(-50%, -50%) rotate(var(--vineAngle));
        opacity: 1;
        -webkit-filter: brightness(1.5) drop-shadow(0 0 8px #00ff00);
        filter: brightness(1.5) drop-shadow(0 0 8px #00ff00);
    }
    50% {
        transform: translate(-50%, -50%) rotate(var(--vineAngle));
        opacity: 0.5;
        -webkit-filter: brightness(1.2) drop-shadow(0 0 4px #00ff00);
        filter: brightness(1.2) drop-shadow(0 0 4px #00ff00);
    }
    100% {
        transform: translate(-50%, -50%) rotate(var(--vineAngle));
        opacity: 0;
        -webkit-filter: brightness(0.8) drop-shadow(0 0 0px #00ff00);
        filter: brightness(0.8) drop-shadow(0 0 0px #00ff00);
    }
}

@keyframes tengman-vine-instant-fade {
    0% {
        opacity: 1;
        transform: translate(-50%, -50%) rotate(var(--vineAngle)) scale(1);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -50%) rotate(var(--vineAngle)) scale(0.8);
    }
}

/* === 第三阶段：击中 - 绿色光圈扩散 + 尖锥穿透 === */

/* 击中光圈扩散 */
.tengman-impact-ripple {
    position: absolute;
    width: 30px;
    height: 30px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle,
        rgba(144, 238, 144, 0.9) 0%,
        rgba(50, 205, 50, 0.7) 30%,
        rgba(34, 139, 34, 0.5) 60%,
        rgba(0, 128, 0, 0.3) 80%,
        transparent 100%);
    border: 3px solid rgba(144, 238, 144, 1);
    border-radius: 50%;
    box-shadow: 
        0 0 30px rgba(144, 238, 144, 1),
        inset 0 0 20px rgba(50, 205, 50, 0.6);
    animation: tengman-impact-ripple 0.4s ease-out both;
}

@keyframes tengman-impact-ripple {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0;
        border-width: 6px;
        box-shadow: 
            0 0 50px rgba(144, 238, 144, 1),
            inset 0 0 30px rgba(50, 205, 50, 0.8);
    }
    30% {
        transform: translate(-50%, -50%) scale(1.2);
        opacity: 1;
        border-width: 4px;
        box-shadow: 
            0 0 60px rgba(144, 238, 144, 1),
            inset 0 0 40px rgba(50, 205, 50, 0.7);
    }
    100% {
        transform: translate(-50%, -50%) scale(4);
        opacity: 0;
        border-width: 1px;
        box-shadow: 
            0 0 20px rgba(144, 238, 144, 0.3),
            inset 0 0 10px rgba(50, 205, 50, 0.2);
    }
}

/* 击中扩散粒子 */
.tengman-impact-particle {
    position: absolute;
    width: 6px;
    height: 6px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle,
        rgba(255, 255, 255, 1) 0%,
        rgba(144, 238, 144, 0.9) 40%,
        rgba(50, 205, 50, 0.7) 70%,
        transparent 100%);
    border-radius: 50%;
    box-shadow: 0 0 12px rgba(144, 238, 144, 0.8);
    animation: tengman-impact-particle 0.6s ease-out both;
}

@keyframes tengman-impact-particle {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0;
    }
    20% {
        transform: translate(-50%, -50%) scale(2);
        opacity: 1;
        box-shadow: 0 0 20px rgba(144, 238, 144, 1);
    }
    100% {
        transform: translate(-50%, -50%) 
                   translate(var(--impactX), var(--impactY))
                   scale(0.3);
        opacity: 0;
        box-shadow: 0 0 8px rgba(144, 238, 144, 0.3);
    }
}

/* === 第四阶段：余韵 - 自然生机散发 === */

/* 飞舞绿叶 */
.tengman-falling-leaf {
    position: absolute;
    width: 8px;
    height: 12px;
    transform: translate(-50%, -50%);
    background: radial-gradient(ellipse,
        rgba(50, 205, 50, 0.9) 0%,
        rgba(34, 139, 34, 0.8) 40%,
        rgba(0, 100, 0, 0.6) 80%,
        transparent 100%);
    border-radius: 4px 0 4px 0;
    animation: tengman-falling-leaf 2.0s ease-out both;
}

@keyframes tengman-falling-leaf {
    0% {
        transform: translate(-50%, -50%) scale(0) rotate(0deg);
        opacity: 0;
    }
    20% {
        transform: translate(-50%, -50%) scale(1.2) rotate(90deg);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) 
                   translate(var(--fallX), var(--fallY))
                   scale(0.8) rotate(var(--leafRotate));
        opacity: 0;
    }
}

/* 生机光环 */
.tengman-life-aura {
    position: absolute;
    width: 100px;
    height: 100px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle,
        rgba(144, 238, 144, 0.3) 0%,
        rgba(50, 205, 50, 0.2) 40%,
        rgba(34, 139, 34, 0.1) 70%,
        transparent 100%);
    border: 2px solid rgba(144, 238, 144, 0.4);
    border-radius: 50%;
    animation: tengman-life-aura 1.5s ease-out;
}

@keyframes tengman-life-aura {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.2);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(2);
        opacity: 0;
    }
}

/* 治愈光点 */
.tengman-healing-sparkle {
    position: absolute;
    width: 3px;
    height: 3px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle,
        rgba(255, 255, 255, 1) 0%,
        rgba(144, 238, 144, 0.8) 50%,
        transparent 100%);
    border-radius: 50%;
    box-shadow: 0 0 6px rgba(255, 255, 255, 0.8);
    animation: tengman-healing-sparkle 1.5s ease-out infinite;
}

@keyframes tengman-healing-sparkle {
    0%, 100% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 1;
    }
}

/* === 移动端适配 === */
@media (max-width: 768px) {
    .tengman-magic-circle {
        width: 80px;
        height: 80px;
    }
    
    .tengman-inner-runes {
        width: 60px;
        height: 60px;
    }
    
    .tengman-outer-runes {
        width: 100px;
        height: 100px;
    }
    
    .tengman-energy-core {
        width: 15px;
        height: 15px;
    }
    
    .tengman-weapon-sprite {
        width: 70px;
        height: 70px;
    }
    
    .tengman-charge-particle {
        width: 3px;
        height: 3px;
    }
    
    .tengman-charge-grass {
        width: 2px;
        height: 10px;
    }
    
    .tengman-vine-segment {
        -webkit-clip-path: polygon(40% 0%, 60% 0%, 80% 100%, 20% 100%);
        clip-path: polygon(40% 0%, 60% 0%, 80% 100%, 20% 100%);
    }
    
    .tengman-impact-ripple {
        width: 30px;
        height: 30px;
    }
    
    .tengman-impact-particle {
        width: 6px;
        height: 6px;
    }
    
    .tengman-falling-leaf {
        width: 6px;
        height: 9px;
    }
    
    .tengman-life-aura {
        width: 70px;
        height: 70px;
    }
    
    .tengman-healing-sparkle {
        width: 2px;
        height: 2px;
    }
    
    .tengman-circle-spike {
        width: 12px;
        height: 45px;
    }
    
    .tengman-center-explosion {
        width: 45px;
        height: 45px;
    }
    
    .tengman-explosion-particle {
        width: 4px;
        height: 4px;
    }
}

/* 环绕小尖锥 */
.tengman-circle-spike {
    position: absolute;
    width: 12px;
    height: 45px;
    transform: translate(-50%, -50%) rotate(var(--spikeAngle));
    background: linear-gradient(to bottom,
        rgba(34, 139, 34, 1) 0%,
        rgba(50, 205, 50, 1) 30%,
        rgba(34, 139, 34, 1) 70%,
        rgba(0, 100, 0, 0.8) 100%);
    /* 创建尖锥形状 - 修复为更明显的尖刺形状 */
    -webkit-clip-path: polygon(50% 0%, 70% 20%, 60% 100%, 40% 100%, 30% 20%);
    clip-path: polygon(50% 0%, 70% 20%, 60% 100%, 40% 100%, 30% 20%);
    box-shadow: 
        0 0 15px rgba(34, 139, 34, 0.8),
        inset 2px 0 4px rgba(144, 238, 144, 0.5),
        0 0 25px rgba(50, 205, 50, 0.6);
    animation: tengman-circle-spike 0.8s ease-out both;
    /* 添加发光效果 */
    -webkit-filter: drop-shadow(0 0 8px rgba(34, 139, 34, 0.9));
    filter: drop-shadow(0 0 8px rgba(34, 139, 34, 0.9));
}

@keyframes tengman-circle-spike {
    0% {
        transform: translate(-50%, -50%) rotate(var(--spikeAngle)) scaleY(0) scaleX(0.5);
        opacity: 0;
        filter: drop-shadow(0 0 4px rgba(34, 139, 34, 0.5));
    }
    20% {
        transform: translate(-50%, -50%) rotate(var(--spikeAngle)) scaleY(1) scaleX(1);
        opacity: 1;
        filter: drop-shadow(0 0 12px rgba(34, 139, 34, 1));
    }
    40% {
        transform: translate(-50%, -50%) rotate(var(--spikeAngle)) scaleY(2) scaleX(1.2);
        opacity: 1;
        filter: drop-shadow(0 0 16px rgba(50, 205, 50, 1));
    }
    60% {
        transform: translate(-50%, -50%) 
                   translate(calc(var(--targetX) * 0.8), calc(var(--targetY) * 0.8))
                   rotate(var(--spikeAngle)) scaleY(2.5) scaleX(1.3);
        opacity: 1;
        filter: drop-shadow(0 0 20px rgba(50, 205, 50, 1));
    }
    100% {
        transform: translate(-50%, -50%) 
                   translate(var(--targetX), var(--targetY))
                   rotate(var(--spikeAngle)) scaleY(0.5) scaleX(0.8);
        opacity: 0;
        filter: drop-shadow(0 0 8px rgba(34, 139, 34, 0.3));
    }
}

/* 中心爆炸效果 */
.tengman-center-explosion {
    position: absolute;
    width: 45px;
    height: 45px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle,
        rgba(144, 238, 144, 1) 0%,
        rgba(50, 205, 50, 0.9) 30%,
        rgba(34, 139, 34, 0.7) 60%,
        transparent 100%);
    border: 3px solid rgba(50, 205, 50, 1);
    border-radius: 50%;
    box-shadow: 
        0 0 30px rgba(144, 238, 144, 1),
        inset 0 0 20px rgba(50, 205, 50, 0.5);
    animation: tengman-center-explosion 0.8s ease-out both;
}

@keyframes tengman-center-explosion {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0;
    }
    30% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 1;
        box-shadow: 
            0 0 50px rgba(144, 238, 144, 1),
            inset 0 0 30px rgba(50, 205, 50, 0.8);
    }
    100% {
        transform: translate(-50%, -50%) scale(3);
        opacity: 0;
        box-shadow: 
            0 0 20px rgba(144, 238, 144, 0.3),
            inset 0 0 10px rgba(50, 205, 50, 0.2);
    }
}

/* 爆炸粒子 */
.tengman-explosion-particle {
    position: absolute;
    width: 4px;
    height: 4px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle,
        rgba(144, 238, 144, 1) 0%,
        rgba(50, 205, 50, 0.9) 50%,
        transparent 100%);
    border-radius: 50%;
    box-shadow: 0 0 8px rgba(50, 205, 50, 0.8);
    animation: tengman-explosion-particle 1.0s ease-out both;
}

@keyframes tengman-explosion-particle {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0;
    }
    20% {
        transform: translate(-50%, -50%) scale(2);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) 
                   translate(var(--explodeX), var(--explodeY))
                   scale(0.5);
        opacity: 0;
    }
}

/* === 敌人受击动画 === */

@keyframes tengman-pierce-struck {
    0% {
        transform: translateX(0) translateY(0);
        -webkit-filter: hue-rotate(0deg) brightness(1);
        filter: hue-rotate(0deg) brightness(1);
    }
    10% {
        transform: translateX(-4px) translateY(-2px);
        -webkit-filter: hue-rotate(120deg) brightness(1.4);
        filter: hue-rotate(120deg) brightness(1.4);
    }
    20% {
        transform: translateX(4px) translateY(2px);
        -webkit-filter: hue-rotate(120deg) brightness(1.3);
        filter: hue-rotate(120deg) brightness(1.3);
    }
    30% {
        transform: translateX(-3px) translateY(-1px);
        -webkit-filter: hue-rotate(120deg) brightness(1.2);
        filter: hue-rotate(120deg) brightness(1.2);
    }
    40% {
        transform: translateX(3px) translateY(1px);
        -webkit-filter: hue-rotate(120deg) brightness(1.1);
        filter: hue-rotate(120deg) brightness(1.1);
    }
    100% {
        transform: translateX(0) translateY(0);
        -webkit-filter: hue-rotate(0deg) brightness(1);
        filter: hue-rotate(0deg) brightness(1);
    }
}

/* 分支藤蔓系统已移除，专注于蛇身主藤蔓效果 */

/* === 文件结束 === */ 