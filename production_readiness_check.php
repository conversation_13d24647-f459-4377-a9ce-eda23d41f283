<?php
/**
 * 一念修仙项目生产环境就绪检查
 * 全面检查项目是否准备好发布到生产环境
 */

require_once __DIR__ . '/setting.php';

echo "=== 一念修仙项目生产环境就绪检查 ===\n\n";

$issues = [];
$warnings = [];
$passed = [];

// 1. 环境配置检查
echo "1. 环境配置检查:\n";
echo "----------------------------------------\n";

// 检查游戏环境设置
if (GAME_ENV === 'development') {
    $issues[] = "游戏环境仍设置为开发模式 (GAME_ENV = 'development')";
    echo "   ❌ 游戏环境: " . GAME_ENV . " (应设置为 'production')\n";
} else {
    $passed[] = "游戏环境配置正确";
    echo "   ✅ 游戏环境: " . GAME_ENV . "\n";
}

// 检查调试模式
if (GAME_DEBUG === true) {
    $issues[] = "调试模式仍然开启 (GAME_DEBUG = true)";
    echo "   ❌ 调试模式: 开启 (生产环境应关闭)\n";
} else {
    $passed[] = "调试模式已关闭";
    echo "   ✅ 调试模式: 关闭\n";
}

// 检查维护模式
if (GAME_MAINTENANCE === true) {
    $warnings[] = "维护模式已开启，用户无法访问";
    echo "   ⚠️  维护模式: 开启\n";
} else {
    $passed[] = "维护模式已关闭";
    echo "   ✅ 维护模式: 关闭\n";
}

// 2. PHP配置检查
echo "\n2. PHP配置检查:\n";
echo "----------------------------------------\n";

// 检查PHP版本
$phpVersion = PHP_VERSION;
echo "   PHP版本: $phpVersion\n";
if (version_compare($phpVersion, '7.4.0', '>=')) {
    $passed[] = "PHP版本符合要求";
    echo "   ✅ PHP版本符合要求 (>= 7.4.0)\n";
} else {
    $issues[] = "PHP版本过低，建议升级到7.4+";
    echo "   ❌ PHP版本过低\n";
}

// 检查错误显示设置
$displayErrors = ini_get('display_errors');
if ($displayErrors == '0' || $displayErrors === false) {
    $passed[] = "错误显示已关闭";
    echo "   ✅ 错误显示: 关闭\n";
} else {
    $issues[] = "错误显示仍然开启，可能泄露敏感信息";
    echo "   ❌ 错误显示: 开启 (生产环境应关闭)\n";
}

// 检查必要扩展
$requiredExtensions = ['pdo', 'pdo_mysql', 'json', 'session', 'mbstring', 'openssl'];
echo "   必要扩展检查:\n";
foreach ($requiredExtensions as $ext) {
    if (extension_loaded($ext)) {
        echo "     ✅ $ext\n";
    } else {
        $issues[] = "缺少必要的PHP扩展: $ext";
        echo "     ❌ $ext (缺失)\n";
    }
}

// 3. 数据库配置检查
echo "\n3. 数据库配置检查:\n";
echo "----------------------------------------\n";

try {
    $pdo = getDatabaseConnection();
    if ($pdo) {
        $passed[] = "数据库连接正常";
        echo "   ✅ 数据库连接: 成功\n";
        
        // 检查数据库版本
        $stmt = $pdo->query("SELECT VERSION() as version");
        $version = $stmt->fetch(PDO::FETCH_ASSOC)['version'];
        echo "   数据库版本: $version\n";
        
        // 检查字符集
        $stmt = $pdo->query("SELECT @@character_set_database as charset");
        $charset = $stmt->fetch(PDO::FETCH_ASSOC)['charset'];
        if ($charset === 'utf8mb4') {
            $passed[] = "数据库字符集正确";
            echo "   ✅ 字符集: $charset\n";
        } else {
            $warnings[] = "数据库字符集不是utf8mb4，可能影响emoji支持";
            echo "   ⚠️  字符集: $charset (建议使用utf8mb4)\n";
        }
        
        // 检查表数量
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = '" . DB_NAME . "'");
        $tableCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "   数据库表数量: $tableCount\n";
        
        if ($tableCount >= 40) {
            $passed[] = "数据库表结构完整";
            echo "   ✅ 表结构: 完整 ($tableCount 个表)\n";
        } else {
            $issues[] = "数据库表数量不足，可能缺少必要表结构";
            echo "   ❌ 表结构: 不完整 (仅 $tableCount 个表)\n";
        }
        
    } else {
        $issues[] = "数据库连接失败";
        echo "   ❌ 数据库连接: 失败\n";
    }
} catch (Exception $e) {
    $issues[] = "数据库连接异常: " . $e->getMessage();
    echo "   ❌ 数据库连接异常: " . $e->getMessage() . "\n";
}

// 4. 文件权限检查
echo "\n4. 文件权限检查:\n";
echo "----------------------------------------\n";

$writableDirs = [
    LOGS_DIR => '日志目录',
    BACKUP_DIR => '备份目录'
];

foreach ($writableDirs as $dir => $desc) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            $passed[] = "$desc 可写";
            echo "   ✅ $desc: 可写\n";
        } else {
            $issues[] = "$desc 不可写，可能影响日志记录";
            echo "   ❌ $desc: 不可写\n";
        }
    } else {
        $warnings[] = "$desc 不存在";
        echo "   ⚠️  $desc: 不存在\n";
    }
}

// 5. 安全配置检查
echo "\n5. 安全配置检查:\n";
echo "----------------------------------------\n";

// 检查会话配置
if (SESSION_SECURE === false) {
    $warnings[] = "会话安全设置为false，HTTPS环境建议设为true";
    echo "   ⚠️  会话安全: 关闭 (HTTPS环境建议开启)\n";
} else {
    $passed[] = "会话安全配置正确";
    echo "   ✅ 会话安全: 开启\n";
}

// 检查密码策略
if (PASSWORD_MIN_LENGTH >= 6) {
    $passed[] = "密码最小长度设置合理";
    echo "   ✅ 密码最小长度: " . PASSWORD_MIN_LENGTH . " 位\n";
} else {
    $warnings[] = "密码最小长度过短，建议至少6位";
    echo "   ⚠️  密码最小长度: " . PASSWORD_MIN_LENGTH . " 位 (建议至少6位)\n";
}

// 6. 性能配置检查
echo "\n6. 性能配置检查:\n";
echo "----------------------------------------\n";

// 检查OPcache
if (extension_loaded('opcache') && ini_get('opcache.enable')) {
    $passed[] = "OPcache已启用";
    echo "   ✅ OPcache: 已启用\n";
} else {
    $warnings[] = "OPcache未启用，建议启用以提升性能";
    echo "   ⚠️  OPcache: 未启用 (建议启用)\n";
}

// 检查内存限制
$memoryLimit = ini_get('memory_limit');
echo "   PHP内存限制: $memoryLimit\n";

// 检查执行时间限制
$maxExecutionTime = ini_get('max_execution_time');
echo "   最大执行时间: {$maxExecutionTime}秒\n";

// 7. 生成总结报告
echo "\n=== 检查总结 ===\n";
echo "通过检查: " . count($passed) . " 项\n";
echo "警告: " . count($warnings) . " 项\n";
echo "严重问题: " . count($issues) . " 项\n\n";

if (count($issues) > 0) {
    echo "❌ 严重问题 (必须修复):\n";
    foreach ($issues as $issue) {
        echo "   • $issue\n";
    }
    echo "\n";
}

if (count($warnings) > 0) {
    echo "⚠️  警告 (建议修复):\n";
    foreach ($warnings as $warning) {
        echo "   • $warning\n";
    }
    echo "\n";
}

// 生产就绪评估
if (count($issues) === 0) {
    if (count($warnings) === 0) {
        echo "🎉 项目完全准备好发布到生产环境！\n";
    } else {
        echo "✅ 项目基本准备好发布，建议修复警告项以获得最佳性能。\n";
    }
} else {
    echo "🚫 项目尚未准备好发布，请先修复所有严重问题。\n";
}

echo "\n建议下一步操作:\n";
if (count($issues) > 0) {
    echo "1. 修复所有严重问题\n";
    echo "2. 重新运行此检查脚本\n";
} else {
    echo "1. 进行安全性审计\n";
    echo "2. 执行性能压力测试\n";
    echo "3. 建立监控和备份策略\n";
}

?>
