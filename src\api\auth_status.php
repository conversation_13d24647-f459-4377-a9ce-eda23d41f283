<?php
/**
 * 登录状态检查API
 * 提供详细的用户认证状态信息
 * 用于全局登录状态检查
 */

// 引入必要的文件
require_once __DIR__ . '/../includes/functions.php';

// 检查维护模式
if (isMaintenanceMode()) {
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode([
        'success' => false,
        'logged_in' => false,
        'message' => '游戏正在维护中',
        'maintenance' => true
    ]);
    exit;
}

// 记录API调用日志
if (DEBUG_LOG_API_CALLS) {
    writeLog("API调用: auth_status.php", 'DEBUG', 'api.log');
}

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// 启动会话 - 使用统一的会话参数
if (session_status() == PHP_SESSION_NONE) {
    session_set_cookie_params([
        'lifetime' => 0,
        'path' => '/yinian/',
        'domain' => '',
        'secure' => false,
        'httponly' => true,
        'samesite' => 'Lax'
    ]);
    session_start();
}

// 记录请求日志
$request_id = uniqid('auth_');
error_log("[$request_id] 🔍 登录状态检查API被调用");
error_log("[$request_id] 📊 会话ID: " . session_id());
error_log("[$request_id] 🌐 客户端IP: " . (isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : 'unknown'));
error_log("[$request_id] 🕐 请求时间: " . date('Y-m-d H:i:s'));

/**
 * 获取详细的认证状态信息
 */
function getDetailedAuthStatus() {
    global $request_id;
    
    $status = [
        'success' => false,
        'logged_in' => false,
        'user_id' => null,
        'character_id' => null,
        'session_info' => [
            'session_id' => session_id(),
            'session_active' => session_status() === PHP_SESSION_ACTIVE,
            'has_user_session' => isset($_SESSION['user_id']),
            'has_character_session' => isset($_SESSION['character_id'])
        ],
        'timestamp' => time(),
        'request_id' => $request_id
    ];
    
    try {
        // 1. 基础会话检查
        if (!isset($_SESSION['user_id'])) {
            error_log("[$request_id] ❌ 会话中无用户ID");
            $status['message'] = '用户未登录';
            return $status;
        }
        
        $user_id = $_SESSION['user_id'];
        $character_id = isset($_SESSION['character_id']) ? $_SESSION['character_id'] : null;
        
        error_log("[$request_id] 📋 会话信息 - 用户ID: $user_id, 角色ID: " . ($character_id ? $character_id : '无'));
        
        // 2. 数据库验证用户存在性
        $pdo = getDatabase();
        
        if (!$pdo) {
            error_log("[$request_id] ❌ 数据库连接失败");
            $status['message'] = '数据库连接失败';
            return $status;
        }
        
        $stmt = $pdo->prepare("
            SELECT id, username, email, status, last_login_time, login_count
            FROM users 
            WHERE id = ? AND status = 'active'
        ");
        $stmt->execute([$user_id]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            error_log("[$request_id] ❌ 数据库中找不到有效用户: $user_id");
            $status['message'] = '用户账号无效';
            return $status;
        }
        
        error_log("[$request_id] ✅ 用户验证通过: " . $user['username']);
        
        // 3. 角色信息验证（如果有角色ID）
        $character_info = null;
        if ($character_id) {
            $char_stmt = $pdo->prepare("
                SELECT c.id, c.character_name, c.user_id, c.avatar_image,
                       r.realm_name, r.realm_level
                FROM characters c
                LEFT JOIN realm_levels r ON c.realm_id = r.id
                WHERE c.id = ? AND c.user_id = ?
            ");
            $char_stmt->execute([$character_id, $user_id]);
            $character_info = $char_stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$character_info) {
                error_log("[$request_id] ⚠️ 角色信息无效: $character_id");
                // 角色无效时清除会话中的角色信息
                unset($_SESSION['character_id']);
                unset($_SESSION['character_name']);
                $character_id = null;
            } else {
                error_log("[$request_id] ✅ 角色验证通过: " . $character_info['character_name']);
            }
        }
        
        // 4. 构建成功响应
        $status['success'] = true;
        $status['logged_in'] = true;
        $status['user_id'] = (int)$user_id;
        $status['character_id'] = $character_id ? (int)$character_id : null;
        $status['message'] = '用户已登录';
        
        // 用户信息
        $status['user_info'] = [
            'id' => (int)$user['id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'last_login_time' => $user['last_login_time'],
            'login_count' => (int)$user['login_count']
        ];
        
        // 角色信息
        if ($character_info) {
            $status['character_info'] = [
                'id' => (int)$character_info['id'],
                'name' => $character_info['character_name'],
                'avatar' => $character_info['avatar_image'],
                'realm_name' => $character_info['realm_name'],
                'realm_level' => (int)$character_info['realm_level']
            ];
        }
        
        // 会话完整性检查
        $status['session_integrity'] = [
            'user_session_valid' => true,
            'character_session_valid' => $character_id ? true : false,
            'needs_character_creation' => !$character_id
        ];
        
        error_log("[$request_id] ✅ 登录状态检查完成 - 用户: " . $user['username'] . 
                  ", 角色: " . ($character_info ? $character_info['character_name'] : '无'));
        
        return $status;
        
    } catch (Exception $e) {
        error_log("[$request_id] 🚨 登录状态检查异常: " . $e->getMessage());
        error_log("[$request_id] 📍 异常堆栈: " . $e->getTraceAsString());
        
        $status['message'] = '系统错误';
        $status['error'] = $e->getMessage();
        return $status;
    }
}

/**
 * 处理不同的请求类型
 */
function handleRequest() {
    global $request_id;
    
    $action = isset($_GET['action']) ? $_GET['action'] : 'check';
    
    switch ($action) {
        case 'check':
            // 标准登录状态检查
            return getDetailedAuthStatus();
            
        case 'quick':
            // 快速检查（仅检查会话）
            $quick_status = [
                'success' => isset($_SESSION['user_id']),
                'logged_in' => isset($_SESSION['user_id']),
                'user_id' => isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null,
                'character_id' => isset($_SESSION['character_id']) ? $_SESSION['character_id'] : null,
                'timestamp' => time(),
                'request_id' => $request_id
            ];
            error_log("[$request_id] ⚡ 快速检查结果: " . ($quick_status['logged_in'] ? '已登录' : '未登录'));
            return $quick_status;
            
        case 'refresh':
            // 刷新会话信息
            if (!isset($_SESSION['user_id'])) {
                return [
                    'success' => false,
                    'logged_in' => false,
                    'message' => '用户未登录',
                    'request_id' => $request_id
                ];
            }
            
            // 重新从数据库获取最新信息并更新会话
            try {
                $pdo = getDatabase();
                
                $user_id = $_SESSION['user_id'];
                
                // 获取用户信息
                $stmt = $pdo->prepare("SELECT username, email FROM users WHERE id = ? AND status = 'active'");
                $stmt->execute([$user_id]);
                $user = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($user) {
                    $_SESSION['username'] = $user['username'];
                    $_SESSION['email'] = $user['email'];
                }
                
                // 获取角色信息
                if (isset($_SESSION['character_id'])) {
                    $char_stmt = $pdo->prepare("
                        SELECT c.character_name, c.avatar_image, c.realm_id,
                               r.realm_name, r.realm_level
                        FROM characters c
                        LEFT JOIN realm_levels r ON c.realm_id = r.id
                        WHERE c.id = ? AND c.user_id = ?
                    ");
                    $char_stmt->execute([$_SESSION['character_id'], $user_id]);
                    $character = $char_stmt->fetch(PDO::FETCH_ASSOC);
                    
                    if ($character) {
                        $_SESSION['character_name'] = $character['character_name'];
                        $_SESSION['character_avatar'] = $character['avatar_image'];
                        $_SESSION['realm_id'] = $character['realm_id'];
                        $_SESSION['realm_name'] = $character['realm_name'];
                        $_SESSION['realm_level'] = $character['realm_level'];
                    }
                }
                
                error_log("[$request_id] 🔄 会话信息已刷新");
                return getDetailedAuthStatus();
                
            } catch (Exception $e) {
                error_log("[$request_id] 🚨 刷新会话失败: " . $e->getMessage());
                return [
                    'success' => false,
                    'logged_in' => false,
                    'message' => '刷新会话失败',
                    'error' => $e->getMessage(),
                    'request_id' => $request_id
                ];
            }
            
        default:
            error_log("[$request_id] ❓ 未知的请求类型: $action");
            return [
                'success' => false,
                'message' => '未知的请求类型',
                'request_id' => $request_id
            ];
    }
}

// 处理请求并返回结果
try {
    $result = handleRequest();
    
    // 记录响应日志
    $log_result = array_merge($result, ['session_data' => '***隐藏***']); // 隐藏敏感信息
    error_log("[$request_id] 📤 响应结果: " . json_encode($log_result, JSON_UNESCAPED_UNICODE));
    
    echo json_encode($result, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    error_log("[$request_id] 💥 处理请求时发生致命错误: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'logged_in' => false,
        'message' => '系统错误',
        'error' => $e->getMessage(),
        'request_id' => $request_id
    ], JSON_UNESCAPED_UNICODE);
}
?> 