/**
 * 基础技能动画样式
 * 包含所有技能共用的基础动画效果
 */

/* 基础技能容器 */
.skill-container {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 200;
}

/* 通用武器图片样式 */
.weapon-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    image-rendering: crisp-edges;
}

/* 通用击中特效 */
.hit-flash {
    position: absolute;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: radial-gradient(circle,
        rgba(255, 255, 255, 1) 0%,
        rgba(200, 220, 255, 0.8) 30%,
        rgba(150, 180, 255, 0.4) 60%,
        transparent 100%
    );
    transform: translate(-50%, -50%);
    animation: hit-flash-expand 0.3s ease-out forwards;
    -webkit-filter: blur(1px);
    filter: blur(1px);
    pointer-events: none;
    z-index: 300;
}

@keyframes hit-flash-expand {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
    }
    50% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.8;
    }
    100% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0;
    }
}

/* 通用粒子效果 */
.hit-particle {
    position: absolute;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255, 255, 255, 1) 0%, rgba(200, 220, 255, 0.8) 100%);
    animation: particle-scatter 0.6s ease-out forwards;
    -webkit-filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.8));
    filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.8));
    pointer-events: none;
    z-index: 295;
}

@keyframes particle-scatter {
    0% {
        transform: translate(0, 0) scale(1);
        opacity: 1;
    }
    100% {
        transform: translate(var(--particleX), var(--particleY)) scale(0);
        opacity: 0;
    }
}

/* 通用冲击波效果 */
.hit-shockwave {
    position: absolute;
    width: 80px;
    height: 80px;
    border: 2px solid rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: shockwave-expand 0.5s ease-out forwards;
    -webkit-filter: drop-shadow(0 0 10px rgba(200, 220, 255, 0.6));
    filter: drop-shadow(0 0 10px rgba(200, 220, 255, 0.6));
    pointer-events: none;
    z-index: 290;
}

@keyframes shockwave-expand {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
        border-color: rgba(255, 255, 255, 0.9);
    }
    50% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0.6;
        border-color: rgba(200, 220, 255, 0.7);
    }
    100% {
        transform: translate(-50%, -50%) scale(3);
        opacity: 0;
        border-color: rgba(150, 180, 255, 0.3);
    }
}

/* 角色受击动画 */
@keyframes character-hit-shake {
    0%, 100% {
        transform: translateX(0) translateY(0);
        -webkit-filter: brightness(1);
        filter: brightness(1);
    }
    10% {
        transform: translateX(-4px) translateY(-3px);
        -webkit-filter: brightness(1.5);
        filter: brightness(1.5);
    }
    20% {
        transform: translateX(3px) translateY(2px);
        -webkit-filter: brightness(1.2);
        filter: brightness(1.2);
    }
    30% {
        transform: translateX(-3px) translateY(4px);
        -webkit-filter: brightness(1.8);
        filter: brightness(1.8);
    }
    40% {
        transform: translateX(4px) translateY(-2px);
        -webkit-filter: brightness(1.3);
        filter: brightness(1.3);
    }
    50% {
        transform: translateX(-2px) translateY(-4px);
        -webkit-filter: brightness(1.6);
        filter: brightness(1.6);
    }
    60% {
        transform: translateX(3px) translateY(3px);
        -webkit-filter: brightness(1.2);
        filter: brightness(1.2);
    }
    70% {
        transform: translateX(-1px) translateY(1px);
        -webkit-filter: brightness(1.4);
        filter: brightness(1.4);
    }
    80% {
        transform: translateX(1px) translateY(-1px);
        -webkit-filter: brightness(1.1);
        filter: brightness(1.1);
    }
    90% {
        transform: translateX(-1px) translateY(0px);
        -webkit-filter: brightness(1.1);
        filter: brightness(1.1);
    }
}

/* 技能喊话效果 */
.skill-shout {
    position: absolute;
    top: 30%;
    left: 50%;
    transform: translateX(-50%);
    font-size: 28px;
    font-weight: bold;
    color: #FFD700;
    text-shadow: 
        2px 2px 0px #8B4513,
        0 0 20px rgba(255, 215, 0, 0.8),
        0 0 40px rgba(255, 215, 0, 0.6);
    animation: skill-shout-appear 1.5s ease-out forwards;
    z-index: 400;
    pointer-events: none;
    white-space: nowrap;
}

@keyframes skill-shout-appear {
    0% {
        transform: translateX(-50%) translateY(30px) scale(0.5);
        opacity: 0;
    }
    20% {
        transform: translateX(-50%) translateY(0px) scale(1.2);
        opacity: 1;
    }
    80% {
        transform: translateX(-50%) translateY(-10px) scale(1);
        opacity: 1;
    }
    100% {
        transform: translateX(-50%) translateY(-20px) scale(0.8);
        opacity: 0;
    }
}

/* 充能效果基础样式 */
.charge-effect {
    position: absolute;
    border-radius: 50%;
    pointer-events: none;
}

.charge-core {
    width: 20px;
    height: 20px;
    background: radial-gradient(circle,
        rgba(255, 255, 255, 1) 0%,
        rgba(200, 220, 255, 0.8) 50%,
        transparent 100%
    );
    animation: charge-core-pulse 0.8s ease-out forwards;
    -webkit-filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.8));
    filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.8));
}

@keyframes charge-core-pulse {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(2);
        opacity: 0;
    }
}

.charge-particle {
    width: 6px;
    height: 6px;
    background: radial-gradient(circle, rgba(255, 255, 255, 1) 0%, rgba(200, 220, 255, 0.8) 100%);
    animation: charge-particle-converge 0.8s ease-out forwards;
    -webkit-filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.8));
    filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.8));
}

@keyframes charge-particle-converge {
    0% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
    80% {
        transform: translate(
            calc(var(--targetX) - 50%), 
            calc(var(--targetY) - 50%)
        ) scale(0.5);
        opacity: 0.8;
    }
    100% {
        transform: translate(
            calc(var(--targetX) - 50%), 
            calc(var(--targetY) - 50%)
        ) scale(0);
        opacity: 0;
    }
}

/* 移动端适配 */
@media (max-width: 480px) {
    .skill-shout {
        font-size: 22px;
        top: 25%;
    }
    
    .hit-flash {
        width: 40px;
        height: 40px;
    }
    
    .hit-shockwave {
        width: 60px;
        height: 60px;
    }
    
    .charge-core {
        width: 15px;
        height: 15px;
    }
    
    .charge-particle {
        width: 4px;
        height: 4px;
    }
} 