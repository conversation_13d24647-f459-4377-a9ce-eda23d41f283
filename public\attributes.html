<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover">
    
    <!-- 🔧 PWA模式底部白边强力修复 - 必须最早执行 -->
    <script src="assets/js/pwa-fix.js"></script>
    
    <!-- 🎛️ 全局调试开关 - 必须最早加载 -->
    <script src="assets/js/global-debug-switch.js"></script>

    <!-- 🔧 项目配置文件 - 必须早期加载 -->
    <script src="assets/js/config.js"></script>

    <!-- 新增HBuilder X优化meta标签 -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="format-detection" content="telephone=no">
    <!-- PWA支持 -->
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#d4af37">
    
    <title>一念修仙 - 角色属性</title>
    
    <!-- 原有CSS保持不变 -->
    <!-- 🔧 新增：引入全局样式文件 -->
    <link rel="stylesheet" href="assets/css/global.css">
    <link rel="stylesheet" href="assets/css/attributes.css">
    <link rel="stylesheet" href="assets/css/common-navigation.css">
    
    <!-- 🔧 视口高度修复脚本 -->
    <script src="assets/js/viewport-height-fix.js"></script>

    <!-- 🔑 全局登录检查系统 -->
    <script src="assets/js/auth-check.js"></script>

    <!-- 🎵 全局音乐管理器 -->
    <script src="assets/js/global-music-manager.js"></script>

</head>
<body>
    <div class="main-container">
        <!-- 属性详情区域 -->
        <div class="attributes-section">
            <!-- 统一显示所有属性 -->
            <div class="attribute-panel active">
                <!-- 修为属性 -->
                <div class="attribute-category">
                    <h3 class="attribute-category-title">👨‍🦱 修为属性</h3>
                    <div class="attribute-summary-box cultivation-attributes">
                        <div class="attribute-grid">
                            <div class="attribute-compact-item constitution" onclick="showAttributeDetail('constitution')">
                                <div class="attribute-compact-icon">💪</div>
                                <div class="attribute-compact-info">
                                    <div class="attribute-compact-label">筋骨</div>
                                    <div class="attribute-compact-value" id="constitution">0</div>
                                </div>
                            </div>
                            <div class="attribute-compact-item wisdom" onclick="showAttributeDetail('wisdom')">
                                <div class="attribute-compact-icon">🧠</div>
                                <div class="attribute-compact-info">
                                    <div class="attribute-compact-label">悟性</div>
                                    <div class="attribute-compact-value" id="wisdom">0</div>
                                </div>
                            </div>
                            <div class="attribute-compact-item physique" onclick="showAttributeDetail('physique')">
                                <div class="attribute-compact-icon">❤️</div>
                                <div class="attribute-compact-info">
                                    <div class="attribute-compact-label">体魄</div>
                                    <div class="attribute-compact-value" id="physique">0</div>
                                </div>
                            </div>
                            <div class="attribute-compact-item soul" onclick="showAttributeDetail('soul')">
                                <div class="attribute-compact-icon">👁️</div>
                                <div class="attribute-compact-info">
                                    <div class="attribute-compact-label">神魂</div>
                                    <div class="attribute-compact-value" id="soul">0</div>
                                </div>
                            </div>
                            <div class="attribute-compact-item agility" onclick="showAttributeDetail('agility')">
                                <div class="attribute-compact-icon">🦶</div>
                                <div class="attribute-compact-info">
                                    <div class="attribute-compact-label">身法</div>
                                    <div class="attribute-compact-value" id="agility">0</div>
                                </div>
                            </div>     
                        </div>
                    </div>
                </div>

                <!-- 战斗属性 -->
                <div class="attribute-category">
                    <h3 class="attribute-category-title">⚔️ 战斗属性</h3>
                    <div class="attribute-summary-box battle-attributes">
                        <div class="attribute-grid">
                            <div class="attribute-compact-item physical-attack" onclick="showAttributeDetail('physical-attack')">
                                <div class="attribute-compact-icon">⚔️</div>
                                <div class="attribute-compact-info">
                                    <div class="attribute-compact-label">物理攻击力</div>
                                    <div class="attribute-compact-value" id="physical-attack-display">0</div>
                                </div>
                            </div>
                            <div class="attribute-compact-item immortal-attack" onclick="showAttributeDetail('immortal-attack')">
                                <div class="attribute-compact-icon">✨</div>
                                <div class="attribute-compact-info">
                                    <div class="attribute-compact-label">法术攻击力</div>
                                    <div class="attribute-compact-value" id="immortal-attack-display">0</div>
                                </div>
                            </div>
                            <div class="attribute-compact-item physical-defense" onclick="showAttributeDetail('physical-defense')">
                                <div class="attribute-compact-icon">🛡️</div>
                                <div class="attribute-compact-info">
                                    <div class="attribute-compact-label">物理防御力</div>
                                    <div class="attribute-compact-value" id="physical-defense-display">0</div>
                                </div>
                            </div>
                            <div class="attribute-compact-item immortal-defense" onclick="showAttributeDetail('immortal-defense')">
                                <div class="attribute-compact-icon">🔮</div>
                                <div class="attribute-compact-info">
                                    <div class="attribute-compact-label">法术防御力</div>
                                    <div class="attribute-compact-value" id="immortal-defense-display">0</div>
                                </div>
                            </div>
                            <div class="attribute-compact-item hp" onclick="showAttributeDetail('hp')">
                                <div class="attribute-compact-icon">❤️</div>
                                <div class="attribute-compact-info">
                                    <div class="attribute-compact-label">生命值</div>
                                    <div class="attribute-compact-value" id="hp-display">0</div>
                                </div>
                            </div>
                            <div class="attribute-compact-item speed" onclick="showAttributeDetail('speed')">
                                <div class="attribute-compact-icon">⚡</div>
                                <div class="attribute-compact-info">
                                    <div class="attribute-compact-label">速度</div>
                                    <div class="attribute-compact-value" id="speed-display">0</div>
                                </div>
                            </div>
                            <div class="attribute-compact-item mp" onclick="showAttributeDetail('mp')">
                                <div class="attribute-compact-icon">💙</div>
                                <div class="attribute-compact-info">
                                    <div class="attribute-compact-label">法力值</div>
                                    <div class="attribute-compact-value" id="mp-display">50</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 五行伤害属性 -->
                <div class="attribute-category">
                    <h3 class="attribute-category-title">🌟 五行伤害</h3>
                    <div class="attribute-summary-box cultivation-attributes">
                        <div class="attribute-grid">
                            <div class="attribute-compact-item gold-root" onclick="showAttributeDetail('metal-damage')">
                                <div class="attribute-compact-icon">⚔️</div>
                                <div class="attribute-compact-info">
                                    <div class="attribute-compact-label">金系伤害</div>
                                    <div class="attribute-compact-value" id="metal-damage-display">0</div>
                                </div>
                            </div>
                            <div class="attribute-compact-item wood-root" onclick="showAttributeDetail('wood-damage')">
                                <div class="attribute-compact-icon">🌲</div>
                                <div class="attribute-compact-info">
                                    <div class="attribute-compact-label">木系伤害</div>
                                    <div class="attribute-compact-value" id="wood-damage-display">0</div>
                                </div>
                            </div>
                            <div class="attribute-compact-item water-root" onclick="showAttributeDetail('water-damage')">
                                <div class="attribute-compact-icon">💧</div>
                                <div class="attribute-compact-info">
                                    <div class="attribute-compact-label">水系伤害</div>
                                    <div class="attribute-compact-value" id="water-damage-display">0</div>
                                </div>
                            </div>
                            <div class="attribute-compact-item fire-root" onclick="showAttributeDetail('fire-damage')">
                                <div class="attribute-compact-icon">🔥</div>
                                <div class="attribute-compact-info">
                                    <div class="attribute-compact-label">火系伤害</div>
                                    <div class="attribute-compact-value" id="fire-damage-display">0</div>
                                </div>
                            </div>
                            <div class="attribute-compact-item earth-root" onclick="showAttributeDetail('earth-damage')">
                                <div class="attribute-compact-icon">🗻</div>
                                <div class="attribute-compact-info">
                                    <div class="attribute-compact-label">土系伤害</div>
                                    <div class="attribute-compact-value" id="earth-damage-display">0</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 动态属性 -->
                <div class="attribute-category">
                    <h3 class="attribute-category-title">⚡ 动态属性</h3>
                    <div class="attribute-summary-box dynamic-attributes">
                        <div class="attribute-grid">
                            <div class="attribute-compact-item accuracy-bonus" onclick="showAttributeDetail('accuracy-bonus')">
                                <div class="attribute-compact-icon">🎯</div>
                                <div class="attribute-compact-info">
                                    <div class="attribute-compact-label">命中率</div>
                                    <div class="attribute-compact-value" id="accuracy-bonus-display">85</div>
                                </div>
                            </div>
                            <div class="attribute-compact-item dodge-bonus" onclick="showAttributeDetail('dodge-bonus')">
                                <div class="attribute-compact-icon">💨</div>
                                <div class="attribute-compact-info">
                                    <div class="attribute-compact-label">闪避率</div>
                                    <div class="attribute-compact-value" id="dodge-bonus-display">5</div>
                                </div>
                            </div>
                            <div class="attribute-compact-item critical-bonus" onclick="showAttributeDetail('critical-bonus')">
                                <div class="attribute-compact-icon">💥</div>
                                <div class="attribute-compact-info">
                                    <div class="attribute-compact-label">暴击率</div>
                                    <div class="attribute-compact-value" id="critical-bonus-display">5</div>
                                </div>
                            </div>
                            <div class="attribute-compact-item critical-damage" onclick="showAttributeDetail('critical-damage')">
                                <div class="attribute-compact-icon">⚡</div>
                                <div class="attribute-compact-info">
                                    <div class="attribute-compact-label">暴击伤害</div>
                                    <div class="attribute-compact-value" id="critical-damage-display">150</div>
                                </div>
                            </div>
                            <div class="attribute-compact-item critical-resistance" onclick="showAttributeDetail('critical-resistance')">
                                <div class="attribute-compact-icon">🛡️</div>
                                <div class="attribute-compact-info">
                                    <div class="attribute-compact-label">抗暴</div>
                                    <div class="attribute-compact-value" id="critical-resistance-display">5</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 套装特殊效果 -->
                <div class="attribute-category" id="set-effects-category" style="display: none;">
                    <h3 class="attribute-category-title">🎭 套装特殊效果</h3>
                    <div class="attribute-summary-box set-effects-container">
                        <div id="set-effects-list" class="set-effects-list">
                            <!-- 套装特殊效果将在这里动态显示 -->
                        </div>
                    </div>
                </div>

                <!-- 功法与丹药 -->
                <div class="attribute-category">
                    <h3 class="attribute-category-title">🧘 功法与丹药</h3>
                    <div class="attribute-summary-box cultivation-attributes">
                        <div class="attribute-grid">
                            <div class="attribute-compact-item cultivation-technique" onclick="showCultivationModal()">
                                <div class="attribute-compact-icon">📜</div>
                                <div class="attribute-compact-info">
                                    <div class="attribute-compact-label">功法</div>
                                    <div class="attribute-compact-value" id="cultivation-technique-display">查看详情</div>
                                </div>
                            </div>
                            <div class="attribute-compact-item dan-medicine" onclick="showDanMedicineModal()">
                                <div class="attribute-compact-icon">💊</div>
                                <div class="attribute-compact-info">
                                    <div class="attribute-compact-label">丹药</div>
                                    <div class="attribute-compact-value" id="dan-medicine-display">查看详情</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 属性详情弹窗 -->
        <div class="attribute-detail-modal" id="attribute-detail-modal" style="display: none;">
            <div class="attribute-detail-content">
                <div class="attribute-detail-header">
                    <div class="attribute-detail-title">
                        <span id="detail-attribute-icon">📊</span>
                        <span id="detail-attribute-name">属性详情</span>
                    </div>
                    <button class="attribute-detail-close" onclick="closeAttributeDetail()">×</button>
                </div>
                <div class="attribute-detail-body">
                    <div class="attribute-detail-total">
                        <span>总值:</span>
                        <span id="detail-attribute-total">0</span>
                    </div>
                    <div class="attribute-detail-sources">
                        <div class="attribute-source-item">
                            <div class="source-name">基础值</div>
                            <div class="source-value" id="detail-base-value">0</div>
                        </div>
                        <div class="attribute-source-item">
                            <div class="source-name">装备加成</div>
                            <div class="source-value" id="detail-equipment-value">0</div>
                        </div>


                    </div>
                    <div class="attribute-detail-description" id="detail-attribute-description">
                        属性说明
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 弹窗遮罩层 -->
        <div id="attribute-popup-overlay" class="attribute-popup-overlay" style="display: none;"></div>

        <!-- 丹药详情弹窗 -->
        <div class="attribute-detail-modal" id="dan-medicine-modal" style="display: none;">
            <div class="attribute-detail-content" style="max-width: 500px;">
                <div class="attribute-detail-header">
                    <div class="attribute-detail-title">
                        <span>💊</span>
                        <span>丹药使用详情</span>
                    </div>
                    <button class="attribute-detail-close" onclick="closeDanMedicineModal()">×</button>
                </div>
                <div class="attribute-detail-body">
                    <div class="dan-medicine-section">
                        <h4>属性丹使用记录</h4>
                        <p class="dan-medicine-description">每阶丹药最多只能使用20颗，超过上限会产生严重丹毒</p>
                        
                        <!-- 腾龙丹 -->
                        <div class="dan-medicine-category">
                            <div class="dan-medicine-category-header">
                                <div class="dan-medicine-icon">💪</div>
                                <div class="dan-medicine-name">腾龙丹 (筋骨)</div>
                            </div>
                            <div class="dan-medicine-tiers" id="tenglong-tiers"></div>
                        </div>
                        
                        <!-- 罗刹丹 -->
                        <div class="dan-medicine-category">
                            <div class="dan-medicine-category-header">
                                <div class="dan-medicine-icon">🧠</div>
                                <div class="dan-medicine-name">罗刹丹 (悟性)</div>
                            </div>
                            <div class="dan-medicine-tiers" id="luocha-tiers"></div>
                        </div>
                        
                        <!-- 血气丹 -->
                        <div class="dan-medicine-category">
                            <div class="dan-medicine-category-header">
                                <div class="dan-medicine-icon">❤️</div>
                                <div class="dan-medicine-name">血气丹 (体魄)</div>
                            </div>
                            <div class="dan-medicine-tiers" id="xueqi-tiers"></div>
                        </div>
                        
                        <!-- 虚灵丹 -->
                        <div class="dan-medicine-category">
                            <div class="dan-medicine-category-header">
                                <div class="dan-medicine-icon">👁️</div>
                                <div class="dan-medicine-name">虚灵丹 (神魂)</div>
                            </div>
                            <div class="dan-medicine-tiers" id="xuling-tiers"></div>
                        </div>
                        
                        <!-- 游龙丹 -->
                        <div class="dan-medicine-category">
                            <div class="dan-medicine-category-header">
                                <div class="dan-medicine-icon">🦶</div>
                                <div class="dan-medicine-name">游龙丹 (身法)</div>
                            </div>
                            <div class="dan-medicine-tiers" id="youlong-tiers"></div>
                        </div>
                    </div>
                    
                    <div class="dan-medicine-warning" id="dan-medicine-warning">
                        <div class="warning-icon">⚠️</div>
                        <div class="warning-text">丹毒等级: <span id="dan-poison-level">轻微</span> - <span id="dan-poison-description">身体状况良好</span></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 功法详情弹窗 -->
        <div class="attribute-detail-modal" id="cultivation-modal" style="display: none;">
            <div class="attribute-detail-content" style="max-width: 450px;">
                <div class="attribute-detail-header">
                    <div class="attribute-detail-title">
                        <span>📜</span>
                        <span>功法详情</span>
                    </div>
                    <button class="attribute-detail-close" onclick="closeCultivationModal()">×</button>
                </div>
                <div class="attribute-detail-body">
                    <div class="cultivation-techniques-list">
                        <h4>已学功法</h4>
                        <div class="techniques-container" id="learned-techniques-container">
                            <!-- 这里会通过JS动态添加已学功法 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航栏会由 common-navigation.js 自动插入 -->
    <script src="assets/js/common-navigation.js"></script>
    <script>
        let currentUser = null;
        let attributeSourceData = {}; // 存储各属性的来源数据

        // 页面加载
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('属性页面开始加载...');
            
            try {
                // 直接加载属性数据
                await loadAttributeData();
                
                console.log('属性页面加载完成');
            } catch (error) {
                console.error('属性页面加载错误:', error);
            }
        });

        // 加载属性数据
        async function loadAttributeData() {
            try {
                console.log('开始加载属性数据...');

                // 添加时间戳防止缓存
                const timestamp = Date.now();
                const apiUrl = window.GameConfig ?
                    window.GameConfig.getApiUrl(`cultivation.php?action=get_attributes&t=${timestamp}`) :
                    `../src/api/cultivation.php?action=get_attributes&t=${timestamp}`;

                // 从API获取属性数据 - 修复路径
                const response = await fetch(apiUrl, {
                    method: 'GET',
                    headers: {
                        'Cache-Control': 'no-cache',
                        'Pragma': 'no-cache'
                    }
                });
                
                // 检查响应状态
                if (!response.ok) {
                    console.error('API响应状态错误:', response.status, response.statusText);
                    throw new Error(`API响应错误: ${response.status} ${response.statusText}`);
                }
                
                // 获取响应文本用于调试
                const responseText = await response.text();
                console.log('API原始响应:', responseText);
                
                // 尝试解析JSON
                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (parseError) {
                    console.error('JSON解析错误:', parseError.message);
                    console.error('收到的响应文本:', responseText);
                    throw new Error(`JSON解析错误: ${parseError.message}`);
                }
                
                if (data.success) {
                    console.log('属性数据加载成功:', data);
                    currentUser = data.attributes;
                    
                    // 🔧 新增：保存当前属性数据到全局变量，供属性详情弹窗使用
                    window.currentAttributeData = data.attributes;
                    
                    // 收集属性来源数据
                    collectAttributeSourceData(data.attributes);
                    // 更新显示
                    updateAttributesDisplay(data.attributes);
                    // 更新修炼与丹药信息
                    updateCultivationInfo(data.attributes);
                } else {
                    console.error('获取属性数据失败:', data.message);
                    alert(`获取属性数据失败: ${data.message}`);
                }
            } catch (error) {
                console.error('加载属性数据错误:', error);
                alert(`加载属性数据错误: ${error.message}`);
            }
        }

        // 收集属性来源数据（基础值、装备加成、武器加成等）
        function collectAttributeSourceData(userData) {
            console.log('🔍 开始收集属性来源数据...');
            console.log('📊 原始用户数据:', userData);
            console.log('🧪 原始丹药数据:', userData.attribute_pill_count);
            
            // 🧪 解析丹药使用记录
            let pillBonuses = {
                constitution: 0,  // 筋骨 (血气丹)
                comprehension: 0, // 悟性 (罗刹丹)
                physique: 0,      // 体魄 (腾龙丹)
                spirit: 0,        // 神魂 (虚灵丹)
                agility: 0        // 身法 (游龙丹)
            };
            
            if (userData.attribute_pill_count) {
                try {
                    let pillCounts = {};
                    if (typeof userData.attribute_pill_count === 'string') {
                        pillCounts = JSON.parse(userData.attribute_pill_count);
                    } else {
                        pillCounts = userData.attribute_pill_count;
                    }
                    
                    // 计算各属性的丹药加成
                    for (let key in pillCounts) {
                        const count = pillCounts[key];
                        const parts = key.split('_');
                        if (parts.length >= 2) {
                            const type = parts[0];
                            const tier = parseInt(parts[1]);
                            const bonus = tier * count; // 每阶丹药提供阶数点属性，使用次数累加
                            
                            switch (type) {
                                case 'tenglong':
                                    pillBonuses.physique += bonus;
                                    break;
                                case 'luocha':
                                    pillBonuses.comprehension += bonus;
                                    break;
                                case 'xueqi':
                                    pillBonuses.constitution += bonus;
                                    break;
                                case 'xuling':
                                    pillBonuses.spirit += bonus;
                                    break;
                                case 'youlong':
                                    pillBonuses.agility += bonus;
                                    break;
                            }
                        }
                    }
                    
                    console.log('🧪 解析丹药加成:', pillBonuses);
                    console.log('📊 原始丹药数据:', pillCounts);
                    
                } catch (e) {
                    console.error('解析丹药数据失败:', e);
                }
            }
            
            // 🎯 使用新的API属性分解数据 - 适配新字段结构
            attributeSourceData = {
                // 战斗属性 - 使用API返回的*_base、*_equipment、*_set字段
                'physical-attack': {
                    base: Math.floor(userData.physical_attack_base || 0),
                    equipment: Math.floor(userData.physical_attack_equipment || 0),
                    set: Math.floor(userData.physical_attack_set || 0)
                },
                'immortal-attack': {
                    base: Math.floor(userData.immortal_attack_base || 0),
                    equipment: Math.floor(userData.immortal_attack_equipment || 0),
                    set: Math.floor(userData.immortal_attack_set || 0)
                },
                'physical-defense': {
                    base: Math.floor(userData.physical_defense_base || 0),
                    equipment: Math.floor(userData.physical_defense_equipment || 0),
                    set: Math.floor(userData.physical_defense_set || 0)
                },
                'immortal-defense': {
                    base: Math.floor(userData.immortal_defense_base || 0),
                    equipment: Math.floor(userData.immortal_defense_equipment || 0),
                    set: Math.floor(userData.immortal_defense_set || 0)
                },
                'hp': {
                    base: Math.floor(userData.hp_base || 0),
                    equipment: Math.floor(userData.hp_equipment || 0),
                    set: Math.floor(userData.hp_set || 0)
                },
                'mp': {
                    base: Math.floor(userData.mp_base || 0),
                    equipment: Math.floor(userData.mp_equipment || 0),
                    set: Math.floor(userData.mp_set || 0)
                },
                'speed': {
                    base: Math.round(userData.speed_base || 0),
                    equipment: Math.round(userData.speed_equipment || 0),
                    set: Math.round(userData.speed_set || 0)
                },
                
                // 🔧 修复：动态属性 - 使用新的字段结构，包含套装加成
                'accuracy-bonus': {
                    base: Math.floor(userData.accuracy_bonus_base || 0),
                    equipment: Math.floor(userData.accuracy_bonus_equipment || 0),
                    set: Math.floor(userData.accuracy_bonus_set || 0)
                },
                'dodge-bonus': {
                    base: Math.floor(userData.dodge_bonus_base || 0),
                    equipment: Math.floor(userData.dodge_bonus_equipment || 0),
                    set: Math.floor(userData.dodge_bonus_set || 0)
                },
                'critical-bonus': {
                    base: Math.floor(userData.critical_bonus_base || 0),
                    equipment: Math.floor(userData.critical_bonus_equipment || 0),
                    set: Math.floor(userData.critical_bonus_set || 0)
                },
                'critical-damage': {
                    base: Math.floor((userData.critical_damage_base || 0) * 100),
                    equipment: Math.floor((userData.critical_damage_equipment || 0) * 100),
                    set: Math.floor((userData.critical_damage_set || 0) * 100)
                },
                'critical-resistance': {
                    base: Math.floor(userData.critical_resistance_base || 0),
                    equipment: Math.floor(userData.critical_resistance_equipment || 0),
                    set: Math.floor(userData.critical_resistance_set || 0)
                },
                
                // 🧪 修为属性 - 添加丹药加成计算
                'constitution': {
                    base: Math.floor((parseInt(userData.constitution) || 0) - pillBonuses.constitution),
                    pills: Math.floor(pillBonuses.constitution),
                    points: 0
                },
                'wisdom': {
                    base: Math.floor((parseInt(userData.comprehension) || 0) - pillBonuses.comprehension),
                    pills: Math.floor(pillBonuses.comprehension),
                    points: 0
                },
                'physique': {
                    base: Math.floor((parseInt(userData.physique) || 0) - pillBonuses.physique),
                    pills: Math.floor(pillBonuses.physique),
                    points: 0
                },
                'soul': {
                    base: Math.floor((parseInt(userData.spirit) || 0) - pillBonuses.spirit),
                    pills: Math.floor(pillBonuses.spirit),
                    points: 0
                },
                'agility': {
                    base: Math.floor((parseInt(userData.agility) || 0) - pillBonuses.agility),
                    pills: Math.floor(pillBonuses.agility),
                    points: 0
                },
                
                // 五行伤害属性 - 根据灵根值计算 (灵根值 × 1.2)
                'metal-damage': {
                    base: Math.floor((parseInt(userData.gold_root) || 0) * 1.2)
                },
                'wood-damage': {
                    base: Math.floor((parseInt(userData.wood_root) || 0) * 1.2)
                },
                'water-damage': {
                    base: Math.floor((parseInt(userData.water_root) || 0) * 1.2)
                },
                'fire-damage': {
                    base: Math.floor((parseInt(userData.fire_root) || 0) * 1.2)
                },
                'earth-damage': {
                    base: Math.floor((parseInt(userData.earth_root) || 0) * 1.2)
                }
            };
            
            console.log('🎯 最终属性来源数据:', attributeSourceData);
        }

        // 更新属性显示
        function updateAttributesDisplay(userData) {
            // 更新战斗属性
            updateBattleAttributes(userData);

            // 更新动态属性
            updateDynamicAttributes(userData);

            // 更新修为属性
            updateCultivationAttributes(userData);

            // 更新灵根属性
            updateRootAttributes(userData);

            // 更新套装特殊效果
            updateSetSpecialEffects(userData);
        }

        // 更新战斗属性
        function updateBattleAttributes(userData) {
            // 使用新的total字段，fallback到旧字段以保持兼容性
            document.getElementById('physical-attack-display').textContent = Math.floor(userData.physical_attack_total || userData.physical_attack || 0);
            document.getElementById('immortal-attack-display').textContent = Math.floor(userData.immortal_attack_total || userData.immortal_attack || 0);
            document.getElementById('physical-defense-display').textContent = Math.floor(userData.physical_defense_total || userData.physical_defense || 0);
            document.getElementById('immortal-defense-display').textContent = Math.floor(userData.immortal_defense_total || userData.immortal_defense || 0);
            document.getElementById('hp-display').textContent = Math.floor(userData.hp_total || userData.hp || 0);
            document.getElementById('speed-display').textContent = Math.round(userData.speed_total || userData.speed || 0);
            document.getElementById('mp-display').textContent = Math.floor(userData.mp_total || userData.mp || 0);
        }

        // 更新动态属性
        function updateDynamicAttributes(userData) {
            document.getElementById('accuracy-bonus-display').textContent = Math.floor(userData.accuracy_bonus_total || userData.accuracy_bonus || 0);
            document.getElementById('dodge-bonus-display').textContent = Math.floor(userData.dodge_bonus_total || userData.dodge_bonus || 0);
            document.getElementById('critical-bonus-display').textContent = Math.floor(userData.critical_bonus_total || userData.critical_bonus || 0);
            // 修复暴击伤害显示 - 如果是小数值则转换为百分比，如果已经是百分比则直接使用
            let criticalDamage = userData.critical_damage_total || userData.critical_damage || 1.5;
            if (criticalDamage < 10) {
                // 如果值小于10，说明是小数格式(如1.5)，需要转换为百分比
                criticalDamage = criticalDamage * 100;
            }
            document.getElementById('critical-damage-display').textContent = Math.floor(criticalDamage) + '%';
            document.getElementById('critical-resistance-display').textContent = Math.floor(userData.critical_resistance_total || userData.critical_resistance || 0);
        }

        // 更新修为属性
        function updateCultivationAttributes(userData) {
            // 更新修为属性 - 确保使用正确的数据库字段名和HTML元素ID
            console.log('更新修为属性，原始数据:', {
                constitution: userData.constitution,
                comprehension: userData.comprehension,
                physique: userData.physique,
                spirit: userData.spirit,
                agility: userData.agility
            });
            
            // 使用正确的字段映射，并取整显示
            document.getElementById('constitution').textContent = Math.floor(userData.constitution || 0);
            document.getElementById('wisdom').textContent = Math.floor(userData.comprehension || 0); // 数据库字段是comprehension，HTML元素ID是wisdom
            document.getElementById('physique').textContent = Math.floor(userData.physique || 0);
            document.getElementById('soul').textContent = Math.floor(userData.spirit || 0); // 数据库字段是spirit，HTML元素ID是soul
            document.getElementById('agility').textContent = Math.floor(userData.agility || 0);
            
            console.log('修为属性更新完成');
        }

        // 更新五行伤害属性
        function updateRootAttributes(userData) {
            // 计算五行伤害 (灵根值 × 1.2)
            document.getElementById('metal-damage-display').textContent = Math.floor((userData.gold_root || 0) * 1.2);
            document.getElementById('wood-damage-display').textContent = Math.floor((userData.wood_root || 0) * 1.2);
            document.getElementById('water-damage-display').textContent = Math.floor((userData.water_root || 0) * 1.2);
            document.getElementById('fire-damage-display').textContent = Math.floor((userData.fire_root || 0) * 1.2);
            document.getElementById('earth-damage-display').textContent = Math.floor((userData.earth_root || 0) * 1.2);
        }

        // 显示属性详情
        function showAttributeDetail(attributeId) {
            const modal = document.getElementById('attribute-detail-modal');
            const nameElement = document.getElementById('detail-attribute-name');
            const iconElement = document.getElementById('detail-attribute-icon');
            const totalElement = document.getElementById('detail-attribute-total');
            const sourcesContainer = document.querySelector('.attribute-detail-sources');
            const descriptionElement = document.getElementById('detail-attribute-description');
            
            // 清空之前的内容
            sourcesContainer.innerHTML = '';
            
            // 设置属性名称和图标
            const attributeNames = {
                'physical-attack': { name: '物理攻击力', icon: '⚔️' },
                'immortal-attack': { name: '法术攻击力', icon: '✨' },
                'physical-defense': { name: '物理防御力', icon: '🛡️' },
                'immortal-defense': { name: '法术防御力', icon: '🔮' },
                'hp': { name: '生命值', icon: '❤️' },
                'speed': { name: '速度', icon: '⚡' },
                'accuracy-bonus': { name: '命中率', icon: '🎯' },
                'dodge-bonus': { name: '闪避率', icon: '💨' },
                'critical-bonus': { name: '暴击率', icon: '💥' },
                'critical-damage': { name: '暴击伤害', icon: '⚡' },
                'mp': { name: '法力值', icon: '💙' },
                'critical-resistance': { name: '抗暴', icon: '🛡️' },
                'constitution': { name: '筋骨', icon: '💪' },
                'wisdom': { name: '悟性', icon: '🧠' },
                'physique': { name: '体魄', icon: '❤️' },
                'soul': { name: '神魂', icon: '👁️' },
                'agility': { name: '身法', icon: '🦶' },
                'experience': { name: '经验值', icon: '⭐' },
                'metal-damage': { name: '金系伤害', icon: '⚔️' },
                'wood-damage': { name: '木系伤害', icon: '🌲' },
                'water-damage': { name: '水系伤害', icon: '💧' },
                'fire-damage': { name: '火系伤害', icon: '🔥' },
                'earth-damage': { name: '土系伤害', icon: '🗻' },
                'cultivation-technique': { name: '功法', icon: '📜' },
                'technique-level': { name: '功法等级', icon: '⭐' },
                'technique-exp': { name: '功法经验', icon: '📊' },
                'breakthrough-count': { name: '突破次数', icon: '🌟' }
            };
            
            nameElement.textContent = attributeNames[attributeId]?.name || '未知属性';
            iconElement.textContent = attributeNames[attributeId]?.icon || '📊';
            
            // 设置属性详细说明
            const attributeDescriptions = {
                'constitution': {
                    name: '筋骨',
                    description: '身体力量和骨骼强度，是物理攻击力的基础。',
                    effects: [
                        '影响物理攻击力和暴击',
                        '⭐ 境界突破：获得+2筋骨',
                        '✨ 每点筋骨：物攻+1%、暴击+1%'
                    ],
                    tips: '主要通过境界突破、腾龙丹提升。'
                },
                'wisdom': {
                    name: '悟性',
                    description: '对天道的理解和法术掌控能力，影响法力和法术防御。',
                    effects: [
                        '影响法力值和法术防御',
                        '⭐ 境界突破：获得+2悟性',
                        '✨ 每点悟性：法力+1%、仙防+1%'
                    ],
                    tips: '主要通过境界突破、罗刹丹提升。'
                },
                'physique': {
                    name: '体魄',
                    description: '身体强健程度和生命力，决定生命值和抗暴能力。',
                    effects: [
                        '影响生命值和抗暴击',
                        '⭐ 境界突破：获得+2体魄',
                        '✨ 每点体魄：气血+1%、抗暴+1%'
                    ],
                    tips: '主要通过境界突破、血气丹提升。'
                },
                'soul': {
                    name: '神魂',
                    description: '精神力量和灵魂强度，影响法术攻击和闪避。',
                    effects: [
                        '影响法术攻击和闪避',
                        '⭐ 境界突破：获得+2神魂',
                        '✨ 每点神魂：仙攻+1%、闪避+1%'
                    ],
                    tips: '主要通过境界突破、虚灵丹提升。'
                },
                'agility': {
                    name: '身法',
                    description: '身体敏捷度和反应速度，影响速度和物理防御。',
                    effects: [
                        '影响速度和物理防御',
                        '⭐ 境界突破：获得+2身法',
                        '✨ 每点身法：速度+1%、物防+1%'
                    ],
                    tips: '主要通过境界突破、游龙丹提升。'
                },
                'physical-attack': {
                    name: '物理攻击力',
                    description: '使用武器造成的物理伤害。',
                    effects: [
                        '来源：筋骨属性、武器、装备',
                        '影响普通攻击和物理技能伤害'
                    ],
                    tips: '通过强化筋骨和装备武器提升。'
                },
                'immortal-attack': {
                    name: '法术攻击力',
                    description: '使用法术造成的法术伤害。',
                    effects: [
                        '来源：神魂属性、法器、功法',
                        '影响法术技能伤害'
                    ],
                    tips: '通过修炼神魂和装备法器提升。'
                },
                'physical-defense': {
                    name: '物理防御力',
                    description: '减少物理攻击伤害的能力。',
                    effects: [
                        '来源：身法属性、装备',
                        '减少物理攻击伤害'
                    ],
                    tips: '通过修炼身法和装备护甲提升。'
                },
                'immortal-defense': {
                    name: '法术防御力',
                    description: '抵抗法术攻击的能力。',
                    effects: [
                        '来源：悟性属性、装备',
                        '减少法术攻击伤害'
                    ],
                    tips: '通过修炼悟性和装备提升。'
                },
                'hp': {
                    name: '生命值',
                    description: '角色能承受的总伤害量，计算公式：(100 + 体魄×10) × 境界倍率 + 装备加成',
                    effects: [
                        '🧮 计算公式：基础100 + 体魄×10',
                        '⚡ 境界倍率：随境界提升而增加',
                        '🛡️ 装备加成：通过装备提供额外生命值',
                        '💀 生命值耗尽会导致战斗失败'
                    ],
                    tips: '主要通过修炼体魄属性提升，境界突破可获得倍率加成。'
                },
                'speed': {
                    name: '速度',
                    description: '影响战斗行动顺序，计算公式：身法×5 × 境界倍率 + 装备加成',
                    effects: [
                        '🧮 计算公式：身法×5',
                        '⚡ 境界倍率：随境界提升而增加',
                        '🛡️ 装备加成：通过装备提供额外速度',
                        '⚔️ 决定战斗中的行动顺序'
                    ],
                    tips: '主要通过修炼身法属性提升，轻甲和饰品通常有速度加成。'
                },
                'accuracy-bonus': {
                    name: '命中率',
                    description: '攻击命中目标的概率，计算公式：基础85% + 悟性×0.5% + 装备加成',
                    effects: [
                        '🧮 计算公式：基础85% + 悟性×0.5%',
                        '🎯 影响攻击是否命中目标',
                        '🛡️ 装备加成：通过装备提供额外命中率',
                        '⚔️ 与对方闪避率对抗'
                    ],
                    tips: '通过修炼悟性属性和装备命中属性提升。'
                },
                'dodge-bonus': {
                    name: '闪避率',
                    description: '躲避敌人攻击的概率，计算公式：基础5% + 身法×0.3% + 装备加成',
                    effects: [
                        '🧮 计算公式：基础5% + 身法×0.3%',
                        '💨 躲避敌人的攻击',
                        '🛡️ 装备加成：通过装备提供额外闪避率',
                        '⚔️ 与对方命中率对抗'
                    ],
                    tips: '通过修炼身法属性和装备闪避属性提升。'
                },
                'critical-bonus': {
                    name: '暴击率',
                    description: '发动暴击攻击的概率，计算公式：基础5% + 身法×0.2% + 装备加成',
                    effects: [
                        '🧮 计算公式：基础5% + 身法×0.2%',
                        '💥 触发暴击伤害',
                        '🛡️ 装备加成：通过装备提供额外暴击率',
                        '⚔️ 受对方抗暴属性影响'
                    ],
                    tips: '通过修炼身法属性和装备暴击属性提升。'
                },
                'critical-damage': {
                    name: '暴击伤害',
                    description: '暴击时的伤害倍率，基础为150%。',
                    effects: [
                        '来源：装备、武器技能',
                        '暴击时的伤害倍率',
                        '基础暴击伤害为150%'
                    ],
                    tips: '通过装备暴击伤害属性的装备提升。'
                },
                'mp': {
                    name: '法力值',
                    description: '释放技能时消耗的法力，计算公式：(50 + 神魂 × 5) × 境界倍率 + 装备加成',
                    effects: [
                        '🧮 计算公式：基础50 + 神魂×5',
                        '⚡ 境界倍率：随境界提升而增加',
                        '🛡️ 装备加成：通过装备提供额外法力值',
                        '🔥 技能消耗：不同技能消耗不同法力值',
                        '⚠️ 法力不足时无法释放技能'
                    ],
                    tips: '主要通过修炼神魂属性提升，境界突破可获得倍率加成。'
                },
                'critical-resistance': {
                    name: '抗暴',
                    description: '降低受到暴击概率，计算公式：体魄×0.1% + 装备加成',
                    effects: [
                        '🧮 计算公式：体魄×0.1%',
                        '🛡️ 降低敌人对自己的暴击概率',
                        '🔧 装备加成：通过装备提供额外抗暴',
                        '💪 基于体魄属性计算'
                    ],
                    tips: '通过修炼体魄属性提升，重甲装备通常有抗暴加成。'
                },
                'metal-damage': {
                    name: '金系伤害',
                    description: '金系技能造成的额外伤害，计算公式：金灵根值 × 1.2',
                    effects: [
                        '🧮 计算公式：金灵根值 × 1.2',
                        '⚔️ 使用金系技能时附加此伤害',
                        '🌟 金系技能包括金属性武器技能',
                        '⚡ 与五行相克关系影响最终伤害'
                    ],
                    tips: '基于角色天生金灵根计算，无法直接提升，需通过特殊方法改变灵根。'
                },
                'wood-damage': {
                    name: '木系伤害',
                    description: '木系技能造成的额外伤害，计算公式：木灵根值 × 1.2',
                    effects: [
                        '🧮 计算公式：木灵根值 × 1.2',
                        '⚔️ 使用木系技能时附加此伤害',
                        '🌟 木系技能包括木属性武器技能',
                        '⚡ 与五行相克关系影响最终伤害'
                    ],
                    tips: '基于角色天生木灵根计算，无法直接提升，需通过特殊方法改变灵根。'
                },
                'water-damage': {
                    name: '水系伤害',
                    description: '水系技能造成的额外伤害，计算公式：水灵根值 × 1.2',
                    effects: [
                        '🧮 计算公式：水灵根值 × 1.2',
                        '⚔️ 使用水系技能时附加此伤害',
                        '🌟 水系技能包括水属性武器技能',
                        '⚡ 与五行相克关系影响最终伤害'
                    ],
                    tips: '基于角色天生水灵根计算，无法直接提升，需通过特殊方法改变灵根。'
                },
                'fire-damage': {
                    name: '火系伤害',
                    description: '火系技能造成的额外伤害，计算公式：火灵根值 × 1.2',
                    effects: [
                        '🧮 计算公式：火灵根值 × 1.2',
                        '⚔️ 使用火系技能时附加此伤害',
                        '🌟 火系技能包括火属性武器技能',
                        '⚡ 与五行相克关系影响最终伤害'
                    ],
                    tips: '基于角色天生火灵根计算，无法直接提升，需通过特殊方法改变灵根。'
                },
                'earth-damage': {
                    name: '土系伤害',
                    description: '土系技能造成的额外伤害，计算公式：土灵根值 × 1.2',
                    effects: [
                        '🧮 计算公式：土灵根值 × 1.2',
                        '⚔️ 使用土系技能时附加此伤害',
                        '🌟 土系技能包括土属性武器技能',
                        '⚡ 与五行相克关系影响最终伤害'
                    ],
                    tips: '基于角色天生土灵根计算，无法直接提升，需通过特殊方法改变灵根。'
                }
            };
            
            // 获取属性总值和来源数据
            let totalValue = 0;
            const sourceData = attributeSourceData[attributeId];
            
            if (sourceData) {
                // 🔧 修复：优先使用API返回的*_total字段作为总值，fallback到计算值
                const totalFieldMap = {
                    'physical-attack': 'physical_attack_total',
                    'immortal-attack': 'immortal_attack_total', 
                    'physical-defense': 'physical_defense_total',
                    'immortal-defense': 'immortal_defense_total',
                    'hp': 'hp_total',
                    'mp': 'mp_total',
                    'speed': 'speed_total',
                    'accuracy-bonus': 'accuracy_bonus_total',
                    'dodge-bonus': 'dodge_bonus_total',
                    'critical-bonus': 'critical_bonus_total',
                    'critical-damage': 'critical_damage_total',
                    'critical-resistance': 'critical_resistance_total'
                };
                
                const totalField = totalFieldMap[attributeId];
                if (totalField && window.currentAttributeData && window.currentAttributeData[totalField] !== undefined) {
                    totalValue = window.currentAttributeData[totalField];
                    // 🔧 修复暴击伤害显示：如果是暴击伤害，需要转换为百分比显示
                    if (attributeId === 'critical-damage') {
                        if (totalValue < 10) {
                            totalValue = totalValue * 100; // 小数格式转百分比
                        }
                        totalElement.textContent = Math.floor(totalValue) + '%';
                    } else {
                        totalElement.textContent = Math.floor(totalValue);
                    }
                } else {
                    // 计算总值 - fallback方案
                    totalValue = Object.values(sourceData).reduce((sum, val) => sum + val, 0);
                    // 🔧 修复暴击伤害显示：如果是暴击伤害，需要转换为百分比显示
                    if (attributeId === 'critical-damage') {
                        totalElement.textContent = Math.floor(totalValue) + '%';
                    } else {
                        totalElement.textContent = Math.floor(totalValue);
                    }
                }
                
                // 添加各来源
                for (const [source, value] of Object.entries(sourceData)) {
                    if (value === 0) continue; // 跳过为0的来源
                    
                    // 处理不同来源的显示名称和颜色
                    let sourceName, sourceColor;
                    switch (source) {
                        case 'base':
                            // 🔧 五行伤害属性的基础值实际来源于灵根
                            if (attributeId.includes('-damage')) {
                                const elementName = {
                                    'metal-damage': '金灵根',
                                    'wood-damage': '木灵根', 
                                    'water-damage': '水灵根',
                                    'fire-damage': '火灵根',
                                    'earth-damage': '土灵根'
                                };
                                sourceName = elementName[attributeId] || '基础值';
                                sourceColor = {
                                    'metal-damage': '#ffd700',
                                    'wood-damage': '#008000',
                                    'water-damage': '#0000ff', 
                                    'fire-damage': '#ff4500',
                                    'earth-damage': '#996515'
                                }[attributeId] || '#3498db';
                            } else {
                                sourceName = '基础值';
                                sourceColor = '#3498db';
                            }
                        break;
                        case 'equipment':
                            sourceName = '装备加成';
                            sourceColor = '#9b59b6';
                        break;
                        case 'set':
                            sourceName = '🎭 套装加成';
                            sourceColor = '#ff6b6b';
                        break;
                        case 'points':
                            sourceName = '属性点数';
                            sourceColor = '#2ecc71';
                        break;
                        case 'pills':
                            sourceName = '🧪 丹药加成';
                            sourceColor = '#e67e22';
                        break;
                        case 'gold-root':
                            sourceName = '金灵根';
                            sourceColor = '#ffd700';
                        break;
                        case 'wood-root':
                            sourceName = '木灵根';
                            sourceColor = '#008000';
                        break;
                        case 'water-root':
                            sourceName = '水灵根';
                            sourceColor = '#0000ff';
                        break;
                        case 'fire-root':
                            sourceName = '火灵根';
                            sourceColor = '#ff4500';
                        break;
                        case 'earth-root':
                            sourceName = '土灵根';
                            sourceColor = '#996515';
                        break;
                    default:
                            sourceName = source;
                            sourceColor = '#7f8c8d';
                    }
                    
                    // 添加来源项
                    const sourceItem = document.createElement('div');
                    sourceItem.className = 'attribute-source-item';
                    sourceItem.innerHTML = `
                        <div class="source-name">${sourceName}</div>
                        <div class="source-value" style="color: ${sourceColor};">+${Math.floor(value)}</div>
                    `;
                    sourcesContainer.appendChild(sourceItem);
                }
            } else {
                // 没有来源数据的情况
                totalValue = document.getElementById(attributeId)?.textContent || document.getElementById(`${attributeId}-display`)?.textContent || '0';
                
                // 🔧 修复暴击伤害显示：如果是暴击伤害，需要转换为百分比显示
                if (attributeId === 'critical-damage') {
                    totalElement.textContent = Math.floor(totalValue) + '%';
                } else {
                    totalElement.textContent = Math.floor(totalValue);
                }
                
                const sourceItem = document.createElement('div');
                sourceItem.className = 'attribute-source-item';
                sourceItem.innerHTML = `
                    <div class="source-name">当前值</div>
                    <div class="source-value">${Math.floor(totalValue)}</div>
                `;
                sourcesContainer.appendChild(sourceItem);
            }
            
            // 设置属性说明
            if (attributeDescriptions[attributeId] && descriptionElement) {
                const desc = attributeDescriptions[attributeId];
                descriptionElement.innerHTML = `
                    <div class="attribute-explanation">
                        <h4 style="color: #d4af37; margin-bottom: 10px;">🔍 ${desc.name}详解</h4>
                        <p style="color: #fff; line-height: 1.6; margin-bottom: 15px;">${desc.description}</p>
                        
                        <h5 style="color: #e67e22; margin-bottom: 8px;">⚡ 主要效果</h5>
                        <ul style="color: #bdc3c7; line-height: 1.5; margin-bottom: 15px; padding-left: 20px;">
                            ${desc.effects.map(effect => `<li>${effect}</li>`).join('')}
                        </ul>
                        
                        <h5 style="color: #27ae60; margin-bottom: 8px;">💡 提升建议</h5>
                        <p style="color: #87ceeb; line-height: 1.5; font-style: italic;">${desc.tips}</p>
                    </div>
                `;
            } else if (descriptionElement) {
                // 默认说明
                descriptionElement.innerHTML = `
                    <div class="attribute-explanation">
                        <p style="color: #bdc3c7; text-align: center; padding: 20px;">
                            该属性的详细说明正在完善中...
                        </p>
                    </div>
                `;
            }
            
            // 为境界属性添加特殊处理
            if (attributeId === 'level') {
                const level = currentUser.level;
                const realmText = RealmSystem.getFullRealm(level);
                const realmIcon = RealmSystem.getRealmIcon(level);
                const realmColor = RealmSystem.getRealmColor(level);
                
                nameElement.textContent = '境界';
                iconElement.textContent = realmIcon;
                totalElement.innerHTML = `<span style="background: linear-gradient(45deg, ${realmColor}, ${shiftColor(realmColor, 30)}); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;">${realmText}</span>`;
                
                // 添加境界说明
                const description = document.createElement('div');
                description.className = 'attribute-detail-description';
                description.innerHTML = `
                    当前境界：${RealmSystem.getLevelRealm(level)}<br>
                    下一境界：${RealmSystem.getLevelRealm(level + 1)}<br>
                    突破进度：${getBreakthroughProgress(level)}%
                `;
                sourcesContainer.appendChild(description);
            }
            
            // 显示弹窗
            modal.style.display = 'flex';
            document.getElementById('attribute-popup-overlay').style.display = 'block';
        }

        // 关闭属性详情
        function closeAttributeDetail() {
            document.getElementById('attribute-detail-modal').style.display = 'none';
            document.getElementById('attribute-popup-overlay').style.display = 'none';
        }
        
        // 显示修炼与丹药弹窗
        function showCultivationModal() {
            document.getElementById('cultivation-modal').style.display = 'flex';
            updateCultivationModal();
        }
        
        // 关闭修炼与丹药弹窗
        function closeCultivationModal() {
            document.getElementById('cultivation-modal').style.display = 'none';
        }
        
        // 🧪 显示丹药详情弹窗 - 增强版
        function showDanMedicineModal() {
            const modal = document.getElementById('dan-medicine-modal');
            modal.style.display = 'flex';
            
            // 从API获取最新的丹药信息
            fetch(window.GameConfig ? window.GameConfig.getApiUrl('equipment_integrated.php') : '../src/api/equipment_integrated.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=get_dan_medicine_info'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateDanMedicineModalContent(data.data);
                } else {
                    console.error('获取丹药信息失败:', data.message);
                    // 使用fallback数据
                    updateDanMedicineModalContent({
                        pill_usage: {
                            tenglong: [], luocha: [], xueqi: [], xuling: [], youlong: []
                        },
                        dan_poison: {
                            level: 'low',
                            level_name: '轻微',
                            description: '身体状况良好'
                        }
                    });
                }
            })
            .catch(error => {
                console.error('请求丹药信息时出错:', error);
                // 显示错误提示但仍然打开弹窗
                updateDanMedicineModalContent({
                    pill_usage: {
                        tenglong: [], luocha: [], xueqi: [], xuling: [], youlong: []
                    },
                    dan_poison: {
                        level: 'low',
                        level_name: '无法获取',
                        description: '网络错误，无法获取丹毒信息'
                    }
                });
            });
        }
        
        // 🎨 更新丹药弹窗内容
        function updateDanMedicineModalContent(danMedicineData) {
            const pillUsage = danMedicineData.pill_usage;
            const danPoison = danMedicineData.dan_poison;
            
            // 更新各类属性丹的显示
            updatePillTiersFromAPI('tenglong', pillUsage.tenglong);
            updatePillTiersFromAPI('luocha', pillUsage.luocha);
            updatePillTiersFromAPI('xueqi', pillUsage.xueqi);
            updatePillTiersFromAPI('xuling', pillUsage.xuling);
            updatePillTiersFromAPI('youlong', pillUsage.youlong);
            
            // 更新丹毒警告
            updateDanPoisonWarningFromAPI(danPoison);
        }
        
        // 📊 从API数据更新特定属性丹的阶数显示
        function updatePillTiersFromAPI(pillType, tierData) {
            const tiersContainer = document.getElementById(`${pillType}-tiers`);
            if (!tiersContainer) return;
            
            tiersContainer.innerHTML = '';
            
            if (tierData && tierData.length > 0) {
                tierData.forEach(tier => {
                    const tierItem = document.createElement('div');
                    tierItem.className = 'dan-medicine-tier-item';
                    
                    const usageColor = getUsageColor(tier.usage_percent);
                    
                    tierItem.innerHTML = `
                        <div class="tier-info">
                            <div class="tier-name">${tier.tier_name}</div>
                            <div class="tier-count">${tier.current_count}/${tier.max_limit}</div>
                        </div>
                        <div class="tier-progress-container">
                            <div class="tier-progress" style="width: ${tier.usage_percent}%; background: ${usageColor};"></div>
                        </div>
                    `;
                    
                    tiersContainer.appendChild(tierItem);
                });
            } else {
                // 显示前三阶的默认状态
                for (let tier = 1; tier <= 3; tier++) {
                    const maxLimit = 10 + (tier * 10);
                    const tierItem = document.createElement('div');
                    tierItem.className = 'dan-medicine-tier-item';
                    
                    tierItem.innerHTML = `
                        <div class="tier-info">
                            <div class="tier-name">${getChineseTierName(tier)}阶</div>
                            <div class="tier-count">0/${maxLimit}</div>
                        </div>
                        <div class="tier-progress-container">
                            <div class="tier-progress" style="width: 0%; background: linear-gradient(to right, #4facfe, #00f2fe);"></div>
                        </div>
                    `;
                    
                    tiersContainer.appendChild(tierItem);
                }
            }
        }
        
        // ⚠️ 从API数据更新丹毒警告显示
        function updateDanPoisonWarningFromAPI(danPoisonData) {
            const warningElement = document.getElementById('dan-medicine-warning');
            const levelElement = document.getElementById('dan-poison-level');
            const descriptionElement = document.getElementById('dan-poison-description');
            
            if (levelElement) levelElement.textContent = danPoisonData.level_name;
            if (descriptionElement) descriptionElement.textContent = danPoisonData.description;
            
            // 根据丹毒等级调整警告颜色
            if (warningElement) {
                switch (danPoisonData.level) {
                    case 'low':
                        warningElement.style.background = 'rgba(46, 204, 113, 0.2)';
                        warningElement.style.borderColor = 'rgba(46, 204, 113, 0.3)';
                        warningElement.style.color = '#2ecc71';
                        break;
                    case 'medium':
                        warningElement.style.background = 'rgba(241, 196, 15, 0.2)';
                        warningElement.style.borderColor = 'rgba(241, 196, 15, 0.3)';
                        warningElement.style.color = '#f1c40f';
                        break;
                    case 'high':
                        warningElement.style.background = 'rgba(231, 76, 60, 0.2)';
                        warningElement.style.borderColor = 'rgba(231, 76, 60, 0.3)';
                        warningElement.style.color = '#e74c3c';
                        break;
                    case 'critical':
                        warningElement.style.background = 'rgba(192, 57, 43, 0.3)';
                        warningElement.style.borderColor = 'rgba(192, 57, 43, 0.5)';
                        warningElement.style.color = '#c0392b';
                        break;
                    default:
                        warningElement.style.background = 'rgba(108, 117, 125, 0.2)';
                        warningElement.style.borderColor = 'rgba(108, 117, 125, 0.3)';
                        warningElement.style.color = '#6c757d';
                        break;
                }
            }
        }

        // 关闭丹药详情弹窗
        function closeDanMedicineModal() {
            document.getElementById('dan-medicine-modal').style.display = 'none';
            document.getElementById('attribute-popup-overlay').style.display = 'none';
        }

        // 更新丹药信息 - 重构为按阶数显示
        function updateDanMedicineInfo(userData) {
            // 这个函数已被 showDanMedicineModal 中的API调用替代
            // 保留为兼容性函数，但实际功能已迁移到API
            console.log('updateDanMedicineInfo 已废弃，请使用 showDanMedicineModal');
        }

        // 获取使用量颜色
        function getUsageColor(percent) {
            if (percent < 50) return 'linear-gradient(to right, #4facfe, #00f2fe)';
            else if (percent < 80) return 'linear-gradient(to right, #f1c40f, #f39c12)';
            else return 'linear-gradient(to right, #e74c3c, #c0392b)';
        }
        
        // 获取中文阶数名称
        function getChineseTierName(tier) {
            const names = ['', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
            return names[tier] || tier;
        }

        // 数字变化动画
        function animateNumber(elementId, targetValue) {
            const element = document.getElementById(elementId);
            if (!element) return;
            
            const startValue = parseInt(element.textContent) || 0;
            const duration = 800; // 动画持续时间（毫秒）
            const stepTime = 20; // 每一步的时间间隔（毫秒）
            const steps = duration / stepTime;
            const increment = (targetValue - startValue) / steps;
            
            let currentValue = startValue;
            let step = 0;
            
            const animation = setInterval(() => {
                step++;
                currentValue += increment;
                
                if (step >= steps) {
                    clearInterval(animation);
                    currentValue = targetValue;
                }
                
                element.textContent = Math.round(currentValue);
            }, stepTime);
        }

        // 🔧 修复iOS滚动问题 - 只禁用下拉刷新，不禁用正常滚动
        let startY = 0;
        let isScrolling = false;
        
        document.addEventListener('touchstart', function(e) {
            startY = e.touches[0].clientY;
            isScrolling = false;
        }, { passive: true });
        
        document.addEventListener('touchmove', function(e) {
            const currentY = e.touches[0].clientY;
            const deltaY = currentY - startY;
            
            // 只在页面顶部且向下滑动时禁用（防止下拉刷新）
            const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
            if (scrollTop === 0 && deltaY > 0) {
                e.preventDefault();
            }
        }, { passive: false });

        // 🔧 保留双指缩放禁用（游戏界面不需要缩放）
        document.addEventListener('gesturestart', function(e) {
            e.preventDefault();
        });

        document.addEventListener('gesturechange', function(e) {
            e.preventDefault();
        });

        document.addEventListener('gestureend', function(e) {
            e.preventDefault();
        });

        // 获取突破进度（示例函数）
        function getBreakthroughProgress(level) {
            // 这里可以根据实际游戏规则计算突破进度
            return Math.min(99, Math.floor(Math.random() * 100));
        }

        // 辅助函数：调整颜色亮度
        function shiftColor(hex, percent) {
            let r = parseInt(hex.substring(1,3), 16);
            let g = parseInt(hex.substring(3,5), 16);
            let b = parseInt(hex.substring(5,7), 16);

            r = parseInt(r * (100 + percent) / 100);
            g = parseInt(g * (100 + percent) / 100);
            b = parseInt(b * (100 + percent) / 100);

            r = (r < 255) ? r : 255;
            g = (g < 255) ? g : 255;
            b = (b < 255) ? b : 255;

            const rr = ((r.toString(16).length == 1) ? "0" + r.toString(16) : r.toString(16));
            const gg = ((g.toString(16).length == 1) ? "0" + g.toString(16) : g.toString(16));
            const bb = ((b.toString(16).length == 1) ? "0" + b.toString(16) : b.toString(16));

            return "#" + rr + gg + bb;
        }

        // 🆕 获取功法来源文本
        function getTechniqueSource(technique) {
            // 优先使用technique.source，如果没有则根据功法特征判断
            if (technique && technique.source) {
                // 🔧 处理各种来源的映射
                const sourceMap = {
                    'basic': '系统初始功法',
                    'ningqi': '系统初始功法',
                    'xiantian': '初始功法', 
                    'juling': '坊市购买',
                    'lianshen': '黑市购买',
                    '系统迁移': '系统初始功法',
                    '系统自动转换': '系统初始功法',
                    '系统默认': '系统初始功法'
                };
                
                // 如果在映射中找到对应值，使用映射后的值，否则直接返回原值
                return sourceMap[technique.source] || technique.source;
            }
            
            // 根据功法特征推断来源
            if (technique && technique.name) {
                switch (technique.name) {
                    case '凝气决':
                        return '系统初始功法';
                    case '先天功':
                        return '高级功法商店';
                    case '聚灵决':
                        return '修炼获得';
                    case '炼神术':
                        return '秘境奖励';
                    case '太极真经':
                        return '仙界传承';
                    case '九转玄功':
                        return '上古遗迹';
                    case '混沌诀':
                        return '神级传承';
                    default:
                        return '系统默认';
                }
            }
            
            return '系统初始功法';
        }

        // 🆕 获取功法类型
        function getTechniqueType(techniqueName, techniqueLevel = 1) {
            // 根据功法名称和等级确定功法类型
            const techniqueTypes = {
                '凝气决': techniqueLevel <= 3 ? '初级功法' : techniqueLevel <= 6 ? '中级功法' : '高级功法',
                '先天功': techniqueLevel <= 3 ? '中级功法' : techniqueLevel <= 6 ? '高级功法' : '顶级功法',
                '聚灵决': techniqueLevel <= 3 ? '中级功法' : techniqueLevel <= 6 ? '高级功法' : '顶级功法',
                '炼神术': techniqueLevel <= 3 ? '高级功法' : '顶级功法',
                '太极真经': '仙级功法',
                '九转玄功': '仙级功法',
                '混沌诀': '神级功法'
            };
            
            return techniqueTypes[techniqueName] || '普通功法';
        }

        // 🆕 将功法等级数字转换为中文格式
        function formatTechniqueLevel(level) {
            if (level >= 10) {
                return '圆满';
            }
            
            const chineseNumbers = ['', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
            return chineseNumbers[level] + '阶';
        }

        // 🆕 更新功法信息
        function updateCultivationInfo(userData) {
            try {
                // 更新功法显示
                let techniqueText = '无功法';
                let techniques = {};
                let currentTechnique = null;
                
                if (userData.cultivation_techniques) {
                    if (typeof userData.cultivation_techniques === 'string') {
                        try {
                            techniques = JSON.parse(userData.cultivation_techniques);
                        } catch (e) {
                            console.error('解析功法数据失败:', e);
                            techniques = {};
                        }
                    } else {
                        techniques = userData.cultivation_techniques;
                    }
                    
                    const currentTechniqueId = userData.current_technique;
                    if (currentTechniqueId && techniques[currentTechniqueId]) {
                        currentTechnique = techniques[currentTechniqueId];
                        techniqueText = `${currentTechnique.name} ${formatTechniqueLevel(currentTechnique.level || 1)}`;
                    }
                }
                
                // const techniqueElement = document.getElementById('cultivation-technique-display');
                // if (techniqueElement) {
                //     techniqueElement.textContent = techniqueText;
                // }
                
                // 更新功法弹窗内容
                updateCultivationModal(currentTechnique, techniques);
                
                console.log('功法信息已更新:', techniqueText);
            } catch (error) {
                console.error('更新功法信息失败:', error);
            }
        }
        
        // 🆕 更新功法弹窗内容
        function updateCultivationModal(currentTechnique, allTechniques) {
            try {
                // 获取当前功法的ID
                const userData = currentUser; // 使用全局的currentUser变量
                const currentTechniqueId = userData ? userData.current_technique : null;
                
                // 更新已学功法列表
                const learnedContainer = document.getElementById('learned-techniques-container');
                if (learnedContainer && allTechniques) {
                    learnedContainer.innerHTML = '';
                    
                    Object.keys(allTechniques).forEach(techniqueId => {
                        const technique = allTechniques[techniqueId];
                        const isCurrentTechnique = techniqueId === currentTechniqueId;
                        
                        const techniqueItem = document.createElement('div');
                        techniqueItem.className = `technique-item ${isCurrentTechnique ? 'current-technique' : ''}`;
                        
                        // 构建功法项HTML，添加当前功法标签
                        let techniqueHtml = `
                            <div class="technique-info">
                                <div class="technique-name-wrapper">
                                    <span class="technique-name">${technique.name}</span>
                                    ${isCurrentTechnique ? '<span class="current-technique-badge">当前功法</span>' : ''}
                                </div>
                                <div class="technique-type">${getTechniqueType(technique.name, technique.level)}</div>
                            </div>
                            <div class="technique-data">
                                <div class="technique-level-display">${formatTechniqueLevel(technique.level || 1)}</div>
                                <div class="technique-source">${getTechniqueSource(technique)}</div>
                            </div>
                        `;
                        
                        techniqueItem.innerHTML = techniqueHtml;
                        learnedContainer.appendChild(techniqueItem);
                    });
                    
                    if (Object.keys(allTechniques).length === 0) {
                        learnedContainer.innerHTML = '<div style="text-align: center; color: #bdc3c7; padding: 20px;">暂无已学功法</div>';
                    }
                }
                
                console.log('功法弹窗已更新');
            } catch (error) {
                console.error('更新功法弹窗失败:', error);
            }
        }

        // 更新套装特殊效果显示
        async function updateSetSpecialEffects(userData) {
            try {
                // 获取角色ID
                const characterId = userData.character_id || userData.id;
                if (!characterId) {
                    console.log('没有角色ID，跳过套装特殊效果更新');
                    return;
                }

                // 调用套装系统API获取特殊效果
                const timestamp = Date.now();
                const apiUrl = window.GameConfig ?
                    window.GameConfig.getApiUrl(`equipment_set_system.php?action=get_character_sets&character_id=${characterId}&t=${timestamp}`) :
                    `../src/api/equipment_set_system.php?action=get_character_sets&character_id=${characterId}&t=${timestamp}`;
                const response = await fetch(apiUrl, {
                    method: 'GET',
                    headers: {
                        'Cache-Control': 'no-cache',
                        'Pragma': 'no-cache'
                    }
                });

                // 检查响应状态
                if (!response.ok) {
                    console.warn('套装API响应状态异常:', response.status, response.statusText);
                    hideSetSpecialEffects();
                    return;
                }

                const responseText = await response.text();

                // 检查是否是服务器配置错误
                if (responseText.includes('No input file specified')) {
                    console.warn('服务器配置问题: No input file specified - 套装特殊效果暂时不可用');
                    hideSetSpecialEffects();
                    return;
                }

                // 尝试解析JSON
                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (parseError) {
                    console.warn('套装API响应JSON解析失败:', parseError.message);
                    console.warn('响应内容:', responseText.substring(0, 200));
                    hideSetSpecialEffects();
                    return;
                }

                if (data.success && data.sets && data.sets.length > 0) {
                    // 收集所有特殊效果
                    const specialEffects = [];

                    data.sets.forEach(set => {
                        if (set.active_effects && typeof set.active_effects === 'object') {
                            // active_effects 是一个对象，包含 two_piece, four_piece, six_piece 等键
                            Object.entries(set.active_effects).forEach(([effectType, effect]) => {
                                if (effect && effect.special_effect) {
                                    // 确定效果类型的显示名称
                                    let effectTypeName = '';
                                    switch(effectType) {
                                        case 'two_piece':
                                            effectTypeName = '2件套';
                                            break;
                                        case 'four_piece':
                                            effectTypeName = '4件套';
                                            break;
                                        case 'six_piece':
                                            effectTypeName = '6件套';
                                            break;
                                        default:
                                            effectTypeName = effectType;
                                    }

                                    specialEffects.push({
                                        setName: set.set_name,
                                        pieces: set.pieces_count,
                                        maxPieces: set.max_pieces,
                                        effect: effect.special_effect,
                                        effectType: effectTypeName,
                                        rarity: set.rarity || '普通'
                                    });
                                }
                            });
                        }
                    });

                    // 显示特殊效果
                    displaySetSpecialEffects(specialEffects);
                } else {
                    // 没有套装特殊效果
                    hideSetSpecialEffects();
                }

            } catch (error) {
                console.error('获取套装特殊效果失败:', error);
                hideSetSpecialEffects();
            }
        }

        // 显示套装特殊效果
        function displaySetSpecialEffects(effects) {
            const category = document.getElementById('set-effects-category');
            const effectsList = document.getElementById('set-effects-list');

            if (effects.length === 0) {
                hideSetSpecialEffects();
                return;
            }

            // 显示套装特殊效果区域
            category.style.display = 'block';

            // 清空现有内容
            effectsList.innerHTML = '';

            // 生成特殊效果图标映射
            const effectIcons = {
                '眩晕': '😵',
                '冰冻': '🧊',
                '燃烧': '🔥',
                '中毒': '☠️',
                '麻痹': '⚡',
                '沉默': '🤐',
                '减速': '🐌',
                '虚弱': '😰',
                '流血': '🩸',
                '混乱': '🌀',
                '恐惧': '😱',
                '双倍伤害': '💥',
                '无视防御': '🗡️',
                '吸血': '🧛',
                '反弹': '🛡️',
                '暴击': '⚡',
                '穿透': '🏹'
            };

            // 为每个特殊效果创建显示项
            effects.forEach(effectData => {
                const effectItem = document.createElement('div');
                effectItem.className = 'set-effect-item';

                // 尝试从效果描述中提取关键词来选择图标
                let icon = '🎭'; // 默认图标
                for (const [keyword, emoji] of Object.entries(effectIcons)) {
                    if (effectData.effect.includes(keyword)) {
                        icon = emoji;
                        break;
                    }
                }

                effectItem.innerHTML = `
                    <div class="set-effect-icon">${icon}</div>
                    <div class="set-effect-content">
                        <div class="set-effect-name">${effectData.setName} (${effectData.effectType})</div>
                        <div class="set-effect-description">${effectData.effect}</div>
                    </div>
                    <div class="set-effect-pieces">${effectData.pieces}/${effectData.maxPieces}件</div>
                `;

                effectsList.appendChild(effectItem);
            });
        }

        // 隐藏套装特殊效果区域
        function hideSetSpecialEffects() {
            const category = document.getElementById('set-effects-category');
            category.style.display = 'none';
        }

        // 页面加载完成后初始化 - 已在上面的DOMContentLoaded中处理，此处删除重复调用
    </script>
</body>
</html>