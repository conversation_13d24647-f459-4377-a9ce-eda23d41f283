/**
 * 回春剑技能动画样式 - 独立文件
 * 绿色魔法阵蓄力 + 旋转飞剑攻击 + 藤蔓穿刺效果
 */

/* 技能容器 */
.huichunjian-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1000;
    /* v2.0优化：启用硬件加速 */
    will-change: auto;
    transform: translateZ(0);
}

/* === 第一阶段：绿色魔法阵蓄力 + 旋转飞剑 === */

/* 绿色魔法阵 */
.huichunjian-magic-circle {
    position: absolute;
    width: 140px;
    height: 140px;
    background: radial-gradient(circle, 
        rgba(34, 139, 34, 0.9) 0%, 
        rgba(0, 128, 0, 0.7) 25%, 
        rgba(50, 205, 50, 0.5) 50%, 
        rgba(144, 238, 144, 0.3) 75%, 
        transparent 100%);
    border: 3px solid rgba(34, 139, 34, 0.8);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    box-shadow: 
        0 0 30px rgba(34, 139, 34, 0.8),
        inset 0 0 40px rgba(0, 128, 0, 0.4);
    animation: huichunjian-magic-circle 1.5s ease-out;
}

@keyframes huichunjian-magic-circle {
    0% {
        transform: translate(-50%, -50%) scale(0.1) rotate(0deg);
        opacity: 0;
    }
    30% {
        transform: translate(-50%, -50%) scale(0.8) rotate(180deg);
        opacity: 0.8;
    }
    70% {
        transform: translate(-50%, -50%) scale(1.1) rotate(360deg);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(1) rotate(540deg);
        opacity: 1;
    }
}

/* 内圈符文 */
.huichunjian-inner-runes {
    position: absolute;
    width: 90px;
    height: 90px;
    background: 
        repeating-conic-gradient(
            from 0deg,
            rgba(144, 238, 144, 0.9) 0deg 12deg,
            transparent 12deg 24deg
        );
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: huichunjian-inner-runes 1.5s ease-out;
}

@keyframes huichunjian-inner-runes {
    0% {
        transform: translate(-50%, -50%) scale(0) rotate(0deg);
        opacity: 0;
    }
    40% {
        transform: translate(-50%, -50%) scale(1.3) rotate(-270deg);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(1) rotate(-540deg);
        opacity: 0.9;
    }
}

/* 外圈符文 */
.huichunjian-outer-runes {
    position: absolute;
    width: 160px;
    height: 160px;
    background: 
        repeating-conic-gradient(
            from 0deg,
            rgba(34, 139, 34, 0.7) 0deg 18deg,
            transparent 18deg 36deg
        );
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: huichunjian-outer-runes 1.5s ease-out;
}

@keyframes huichunjian-outer-runes {
    0% {
        transform: translate(-50%, -50%) scale(0) rotate(0deg);
        opacity: 0;
    }
    35% {
        transform: translate(-50%, -50%) scale(1.2) rotate(360deg);
        opacity: 0.8;
    }
    100% {
        transform: translate(-50%, -50%) scale(1) rotate(720deg);
        opacity: 0.7;
    }
}

/* 中心旋转飞剑 */
.huichunjian-center-sword {
    position: absolute;
    width: 40px;
    height: 80px;
    transform: translate(-50%, -50%);
    pointer-events: none;
    z-index: 1001;
    
    /* 支持背景图片显示 */
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    
    animation: huichunjian-center-sword 1.5s ease-out forwards;
}

/* 为武器图片添加专门的发光效果 */
.huichunjian-center-sword[style*="background-image"] {
    /* 武器图片使用drop-shadow */
    -webkit-filter: drop-shadow(0 0 15px rgba(34, 139, 34, 0.9));
    filter: drop-shadow(0 0 15px rgba(34, 139, 34, 0.9))
            drop-shadow(0 0 30px rgba(50, 205, 50, 0.7))
            drop-shadow(0 0 8px rgba(144, 238, 144, 0.6));
}

/* 无背景图片时的默认剑形样式 */
.huichunjian-center-sword:not([style*="background-image"]) {
    background: linear-gradient(to bottom, 
        rgba(144, 238, 144, 0.95), 
        rgba(50, 205, 50, 0.85), 
        rgba(34, 139, 34, 0.75));
    -webkit-clip-path: polygon(50% 0%, 40% 20%, 45% 80%, 50% 100%, 55% 80%, 60% 20%);
    clip-path: polygon(50% 0%, 40% 20%, 45% 80%, 50% 100%, 55% 80%, 60% 20%);
    border-radius: 2px;
    border: 1px solid rgba(34, 139, 34, 0.6);
    -webkit-filter: drop-shadow(0 0 12px rgba(34, 139, 34, 0.8));
    filter: drop-shadow(0 0 12px rgba(34, 139, 34, 0.8))
            drop-shadow(0 0 25px rgba(50, 205, 50, 0.6));
}

@keyframes huichunjian-center-sword {
    0% {
        transform: translate(-50%, -50%) scale(0.3) rotate(0deg);
        opacity: 0;
    }
    25% {
        transform: translate(-50%, -50%) scale(0.6) rotate(180deg);
        opacity: 0.7;
    }
    50% {
        transform: translate(-50%, -50%) scale(0.9) rotate(360deg);
        opacity: 0.9;
    }
    75% {
        transform: translate(-50%, -50%) scale(1.2) rotate(540deg);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(1.3) rotate(720deg);
        opacity: 1;
    }
}

/* 蓄力能量核心 */
.huichunjian-energy-core {
    position: absolute;
    width: 25px;
    height: 25px;
    background: radial-gradient(circle,
        rgba(144, 238, 144, 1) 0%,
        rgba(50, 205, 50, 0.9) 30%,
        rgba(34, 139, 34, 0.7) 70%,
        transparent 100%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    box-shadow: 0 0 25px rgba(144, 238, 144, 1);
    animation: huichunjian-energy-core 1.5s ease-out;
}

@keyframes huichunjian-energy-core {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0;
    }
    40% {
        transform: translate(-50%, -50%) scale(2.5);
        opacity: 1;
        box-shadow: 0 0 50px rgba(144, 238, 144, 1);
    }
    70% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0.8;
        box-shadow: 0 0 35px rgba(144, 238, 144, 0.8);
    }
    100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.9;
        box-shadow: 0 0 25px rgba(144, 238, 144, 0.8);
    }
}

/* 蓄力绿色粒子 */
.huichunjian-charge-particle {
    position: absolute;
    width: 5px;
    height: 5px;
    background: radial-gradient(circle,
        rgba(144, 238, 144, 1) 0%,
        rgba(50, 205, 50, 0.8) 50%,
        transparent 100%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    box-shadow: 0 0 10px rgba(50, 205, 50, 0.8);
    animation: huichunjian-charge-particle 1.5s ease-out infinite;
}

@keyframes huichunjian-charge-particle {
    0% {
        transform: translate(-50%, -50%) translate(var(--chargeX), var(--chargeY)) scale(0);
        opacity: 0;
    }
    20% {
        transform: translate(-50%, -50%) translate(calc(var(--chargeX) * 0.7), calc(var(--chargeY) * 0.7)) scale(1.5);
        opacity: 1;
    }
    80% {
        transform: translate(-50%, -50%) translate(calc(var(--chargeX) * 0.1), calc(var(--chargeY) * 0.1)) scale(1);
        opacity: 0.8;
    }
    100% {
        transform: translate(-50%, -50%) translate(0px, 0px) scale(0.5);
        opacity: 0;
    }
}

/* === 第二阶段：飞剑旋转甩飞攻击 === */

/* 飞剑直线飞行动画 - 剑尖朝向敌人，不旋转 */
@keyframes huichunjian-sword-fly {
    0% {
        left: var(--startX);
        top: var(--startY);
        transform: translate(-50%, -50%) rotate(720deg) scale(1.3);
        opacity: 1;
    }
    20% {
        left: calc(var(--startX) + (var(--targetX) - var(--startX)) * 0.1);
        top: calc(var(--startY) + (var(--targetY) - var(--startY)) * 0.1);
        transform: translate(-50%, -50%) rotate(var(--flyAngle, 0deg)) scale(1.1);
        opacity: 1;
    }
    100% {
        left: var(--targetX);
        top: var(--targetY);
        transform: translate(-50%, -50%) rotate(var(--flyAngle, 0deg)) scale(1.5);
        opacity: 0;
    }
}

/* 飞行轨迹 */
.huichunjian-flight-trail {
    position: absolute;
    width: 8px;
    height: 8px;
    background: radial-gradient(circle,
        rgba(144, 238, 144, 0.9) 0%,
        rgba(50, 205, 50, 0.6) 40%,
        transparent 100%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    opacity: var(--trailOpacity, 0.8);
    animation: huichunjian-flight-trail 1.2s ease-out forwards;
}

@keyframes huichunjian-flight-trail {
    0% {
        left: var(--startX);
        top: var(--startY);
        transform: translate(-50%, -50%) scale(0);
        opacity: 0;
    }
    15% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: var(--trailOpacity, 0.8);
    }
    50% {
        left: calc(var(--startX) + (var(--targetX) - var(--startX)) * 0.6);
        top: calc(var(--startY) + (var(--targetY) - var(--startY)) * 0.6);
        transform: translate(-50%, -50%) scale(1);
        opacity: var(--trailOpacity, 0.8);
    }
    100% {
        left: var(--targetX);
        top: var(--targetY);
        transform: translate(-50%, -50%) scale(0.3);
        opacity: 0;
    }
}

/* === 第三阶段：8方向武器穿刺 + 藤蔓环绕效果 === */

/* 击中光圈扩散 */
.huichunjian-impact-ripple {
    position: absolute;
    width: 30px;
    height: 30px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle,
        rgba(144, 238, 144, 0.9) 0%,
        rgba(50, 205, 50, 0.7) 30%,
        rgba(34, 139, 34, 0.5) 60%,
        rgba(0, 128, 0, 0.3) 80%,
        transparent 100%);
    border: 3px solid rgba(144, 238, 144, 1);
    border-radius: 50%;
    box-shadow: 
        0 0 30px rgba(144, 238, 144, 1),
        inset 0 0 20px rgba(50, 205, 50, 0.6);
    animation: huichunjian-impact-ripple 0.4s ease-out both;
}

@keyframes huichunjian-impact-ripple {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0;
        border-width: 6px;
        box-shadow: 
            0 0 10px rgba(144, 238, 144, 0.5),
            inset 0 0 5px rgba(50, 205, 50, 0.3);
    }
    50% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 1;
        border-width: 3px;
        box-shadow: 
            0 0 40px rgba(144, 238, 144, 1),
            inset 0 0 30px rgba(50, 205, 50, 0.8);
    }
    100% {
        transform: translate(-50%, -50%) scale(6);
        opacity: 0;
        border-width: 1px;
        box-shadow: 
            0 0 60px rgba(144, 238, 144, 0.3),
            inset 0 0 40px rgba(50, 205, 50, 0.2);
    }
}

/* 击中扩散粒子 */
.huichunjian-impact-particle {
    position: absolute;
    width: 6px;
    height: 6px;
    background: radial-gradient(circle,
        rgba(144, 238, 144, 1) 0%,
        rgba(50, 205, 50, 0.8) 60%,
        transparent 100%);
    border-radius: 50%;
    animation: huichunjian-impact-particle 0.6s ease-out both;
    box-shadow: 0 0 8px rgba(144, 238, 144, 0.8);
}

@keyframes huichunjian-impact-particle {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0;
    }
    20% {
        transform: translate(-50%, -50%) scale(2);
        opacity: 1;
    }
    100% {
        transform: translate(calc(-50% + var(--impactX)), calc(-50% + var(--impactY))) scale(0.5);
        opacity: 0;
    }
}

/* 🔧 修复：8方向武器穿刺动画 - 使用简单的left/top移动 */
.huichunjian-circle-weapon {
    position: absolute;
    width: 60px;
    height: 60px;
    animation: huichunjian-circle-weapon 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    -webkit-filter: drop-shadow(0 0 10px rgba(144, 238, 144, 0.8));
    filter: drop-shadow(0 0 10px rgba(144, 238, 144, 0.8));
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    pointer-events: none;
    z-index: 1001;
}

@keyframes huichunjian-circle-weapon {
    0% {
        left: var(--startX);
        top: var(--startY);
        transform: translate(-50%, -50%) rotate(var(--weaponAngle)) scale(0.8);
        opacity: 0;
    }
    15% {
        left: var(--startX);
        top: var(--startY);
        transform: translate(-50%, -50%) rotate(var(--weaponAngle)) scale(1.2);
        opacity: 1;
        -webkit-filter: drop-shadow(0 0 15px rgba(144, 238, 144, 1));
        filter: drop-shadow(0 0 15px rgba(144, 238, 144, 1));
    }
    25% {
        left: calc(var(--startX) + (var(--targetX) - var(--startX)) * 0.1);
        top: calc(var(--startY) + (var(--targetY) - var(--startY)) * 0.1);
        transform: translate(-50%, -50%) rotate(var(--weaponAngle)) scale(1.4);
        opacity: 1;
    }
    70% {
        left: var(--targetX);
        top: var(--targetY);
        transform: translate(-50%, -50%) rotate(var(--weaponAngle)) scale(1.1);
        opacity: 0.9;
        -webkit-filter: drop-shadow(0 0 20px rgba(144, 238, 144, 0.9));
        filter: drop-shadow(0 0 20px rgba(144, 238, 144, 0.9));
    }
    85% {
        left: calc(var(--targetX) + (var(--targetX) - var(--startX)) * 0.15);
        top: calc(var(--targetY) + (var(--targetY) - var(--startY)) * 0.15);
        transform: translate(-50%, -50%) rotate(var(--weaponAngle)) scale(0.9);
        opacity: 0.7;
    }
    100% {
        left: calc(var(--targetX) + (var(--targetX) - var(--startX)) * 0.3);
        top: calc(var(--targetY) + (var(--targetY) - var(--startY)) * 0.3);
        transform: translate(-50%, -50%) rotate(var(--weaponAngle)) scale(0.5);
        opacity: 0;
    }
}

/* 中心爆炸效果 */
.huichunjian-center-explosion {
    position: absolute;
    width: 40px;
    height: 40px;
    background: radial-gradient(circle,
        rgba(255, 255, 255, 0.9) 0%,
        rgba(144, 238, 144, 0.8) 30%,
        rgba(50, 205, 50, 0.6) 60%,
        rgba(34, 139, 34, 0.4) 80%,
        transparent 100%);
    border-radius: 50%;
    animation: huichunjian-center-explosion 0.5s ease-out both;
    box-shadow: 
        0 0 20px rgba(144, 238, 144, 1),
        inset 0 0 15px rgba(50, 205, 50, 0.8);
}

@keyframes huichunjian-center-explosion {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0;
        box-shadow: 
            0 0 5px rgba(144, 238, 144, 0.5),
            inset 0 0 3px rgba(50, 205, 50, 0.3);
    }
    30% {
        transform: translate(-50%, -50%) scale(3);
        opacity: 1;
        box-shadow: 
            0 0 40px rgba(144, 238, 144, 1),
            inset 0 0 20px rgba(50, 205, 50, 0.9);
    }
    100% {
        transform: translate(-50%, -50%) scale(8);
        opacity: 0;
        box-shadow: 
            0 0 60px rgba(144, 238, 144, 0.3),
            inset 0 0 30px rgba(50, 205, 50, 0.2);
    }
}

/* 爆炸粒子 */
.huichunjian-explosion-particle {
    position: absolute;
    width: 8px;
    height: 8px;
    background: radial-gradient(circle,
        rgba(255, 255, 255, 0.9) 0%,
        rgba(144, 238, 144, 0.8) 40%,
        rgba(50, 205, 50, 0.6) 80%,
        transparent 100%);
    border-radius: 50%;
    animation: huichunjian-explosion-particle 0.8s ease-out both;
    box-shadow: 0 0 10px rgba(144, 238, 144, 0.8);
}

@keyframes huichunjian-explosion-particle {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0;
    }
    20% {
        transform: translate(-50%, -50%) scale(2.5);
        opacity: 1;
    }
    100% {
        transform: translate(calc(-50% + var(--explodeX)), calc(-50% + var(--explodeY))) scale(0.3);
        opacity: 0;
    }
}

/* 穿刺受击动画 */
@keyframes huichunjian-pierce-struck {
    0% {
        transform: translateX(0) translateY(0);
        -webkit-filter: hue-rotate(0deg) brightness(1);
        filter: hue-rotate(0deg) brightness(1);
    }
    10% {
        transform: translateX(-4px) translateY(-2px);
        -webkit-filter: hue-rotate(120deg) brightness(1.4);
        filter: hue-rotate(120deg) brightness(1.4);
    }
    20% {
        transform: translateX(4px) translateY(2px);
        -webkit-filter: hue-rotate(120deg) brightness(1.3);
        filter: hue-rotate(120deg) brightness(1.3);
    }
    30% {
        transform: translateX(-3px) translateY(-1px);
        -webkit-filter: hue-rotate(120deg) brightness(1.2);
        filter: hue-rotate(120deg) brightness(1.2);
    }
    40% {
        transform: translateX(3px) translateY(1px);
        -webkit-filter: hue-rotate(120deg) brightness(1.1);
        filter: hue-rotate(120deg) brightness(1.1);
    }
    50% {
        transform: translateX(-2px) translateY(1px);
        -webkit-filter: hue-rotate(120deg) brightness(1.2);
        filter: hue-rotate(120deg) brightness(1.2);
    }
    60% {
        transform: translateX(2px) translateY(-1px);
        -webkit-filter: hue-rotate(120deg) brightness(1.1);
        filter: hue-rotate(120deg) brightness(1.1);
    }
    70% {
        transform: translateX(-1px) translateY(0px);
        -webkit-filter: hue-rotate(60deg) brightness(1.05);
        filter: hue-rotate(60deg) brightness(1.05);
    }
    80% {
        transform: translateX(1px) translateY(0px);
        -webkit-filter: hue-rotate(30deg) brightness(1.02);
        filter: hue-rotate(30deg) brightness(1.02);
    }
    90% {
        transform: translateX(0px) translateY(0px);
        -webkit-filter: hue-rotate(15deg) brightness(1.01);
        filter: hue-rotate(15deg) brightness(1.01);
    }
    100% {
        transform: translateX(0) translateY(0);
        -webkit-filter: hue-rotate(0deg) brightness(1);
        filter: hue-rotate(0deg) brightness(1);
    }
}

/* 藤蔓环绕效果 */
.huichunjian-impact-vine {
    position: absolute;
    width: 8px;
    height: 40px;
    background: linear-gradient(to top,
        rgba(34, 139, 34, 0.9) 0%,
        rgba(50, 205, 50, 0.8) 50%,
        rgba(144, 238, 144, 0.6) 100%);
    border-radius: 4px 4px 80% 80%;
    animation: huichunjian-impact-vine 1.2s ease-out both;
    transform-origin: bottom center;
    box-shadow: 0 0 8px rgba(50, 205, 50, 0.7);
    border: 1px solid rgba(50, 205, 50, 0.3);
}

@keyframes huichunjian-impact-vine {
    0% {
        transform: translate(-50%, -100%) rotate(var(--vineAngle)) scaleY(0);
        opacity: 0;
    }
    25% {
        transform: translate(-50%, -100%) rotate(var(--vineAngle)) scaleY(1.2);
        opacity: 0.8;
    }
    50% {
        transform: translate(-50%, -100%) rotate(var(--vineAngle)) scaleY(1);
        opacity: 1;
    }
    75% {
        transform: translate(-50%, -100%) rotate(var(--vineAngle)) scaleY(0.8);
        opacity: 0.6;
    }
    100% {
        transform: translate(-50%, -100%) rotate(var(--vineAngle)) scaleY(0);
        opacity: 0;
    }
}

/* v2.0性能优化 */
.huichunjian-magic-circle,
.huichunjian-inner-runes,
.huichunjian-outer-runes,
.huichunjian-center-sword,
.huichunjian-energy-core,
.huichunjian-charge-particle,
.huichunjian-flight-trail,
.huichunjian-impact-core,
.huichunjian-impact-vine,
.huichunjian-impact-particle,
.huichunjian-circle-weapon,
.huichunjian-center-explosion,
.huichunjian-explosion-particle,
.huichunjian-impact-ripple {
    will-change: transform, opacity;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    /* 减少重绘 */
    contain: layout style paint;
}

/* === 移动端适配 === */
@media (max-width: 768px) {
    .huichunjian-container {
        transform: scale(0.85);
    }
    
    .huichunjian-magic-circle {
        width: 110px !important;
        height: 110px !important;
    }
    
    .huichunjian-inner-runes {
        width: 70px !important;
        height: 70px !important;
    }
    
    .huichunjian-outer-runes {
        width: 130px !important;
        height: 130px !important;
    }
    
    .huichunjian-center-sword {
        width: 30px !important;
        height: 60px !important;
    }
    
    .huichunjian-circle-weapon {
        width: 40px !important;
        height: 40px !important;
    }
    
    .huichunjian-center-explosion {
        width: 30px !important;
        height: 30px !important;
    }
    
    .huichunjian-energy-core {
        width: 20px !important;
        height: 20px !important;
    }
    
    .huichunjian-charge-particle {
        width: 4px !important;
        height: 4px !important;
    }
    
    .huichunjian-flight-trail {
        width: 6px !important;
        height: 6px !important;
    }
    
    .huichunjian-impact-particle {
        width: 4px !important;
        height: 4px !important;
    }
}

/* v2.0新增：减少动画计算 */
@media (prefers-reduced-motion: reduce) {
    .huichunjian-magic-circle,
    .huichunjian-inner-runes,
    .huichunjian-outer-runes,
    .huichunjian-center-sword,
    .huichunjian-energy-core,
    .huichunjian-charge-particle,
    .huichunjian-flight-trail,
    .huichunjian-circle-weapon,
    .huichunjian-center-explosion,
    .huichunjian-explosion-particle,
    .huichunjian-impact-vine,
    .huichunjian-impact-particle,
    .huichunjian-impact-ripple {
        animation-duration: 0.2s !important;
    }
}

/* === 文件结束 === */
