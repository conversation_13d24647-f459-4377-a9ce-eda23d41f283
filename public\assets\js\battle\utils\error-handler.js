/**
 * 一念修仙 - 错误处理管理器
 * 提供API重试、错误捕获、用户友好提示等功能
 */

class BattleErrorHandler {
    constructor() {
        this.maxRetries = 3;
        this.retryDelay = 1000; // 基础重试延迟（毫秒）
        this.errorLog = [];
        this.isErrorToastVisible = false;
        
        // 初始化全局错误处理
        this.initGlobalErrorHandling();
        
        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('error-handler', '🛡️ 战斗错误处理器初始化完成');
        }
    }
    
    /**
     * 初始化全局错误处理
     */
    initGlobalErrorHandling() {
        // 捕获JavaScript错误
        window.addEventListener('error', (event) => {
            this.handleGlobalError('JavaScript错误', event.error, {
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno
            });
        });
        
        // 捕获Promise错误
        window.addEventListener('unhandledrejection', (event) => {
            this.handleGlobalError('Promise错误', event.reason);
            event.preventDefault(); // 防止错误在控制台显示
        });
        
        // 捕获资源加载错误
        window.addEventListener('error', (event) => {
            if (event.target !== window) {
                this.handleResourceError(event.target);
            }
        }, true);
    }
    
    /**
     * 处理全局错误
     */
    handleGlobalError(type, error, details = {}) {
        const errorInfo = {
            type,
            message: error?.message || error,
            stack: error?.stack,
            timestamp: new Date().toISOString(),
            details
        };
        
        this.logError(errorInfo);
        
        // 显示用户友好的错误提示
        this.showErrorToast('系统出现问题，正在尝试恢复...', 'warning');
        
        // 尝试恢复战斗系统
        this.attemptSystemRecovery();
    }
    
    /**
     * 处理资源加载错误
     */
    handleResourceError(element) {
        const resourceType = element.tagName.toLowerCase();
        const resourceSrc = element.src || element.href;
        
        console.warn(`资源加载失败: ${resourceType} - ${resourceSrc}`);
        
        // 对于关键资源，尝试使用备用资源
        if (resourceType === 'img' && resourceSrc.includes('assets/images/')) {
            this.handleImageLoadError(element);
        }
    }
    
    /**
     * 处理图片加载错误
     */
    handleImageLoadError(imgElement) {
        const src = imgElement.src;
        
        // 设置默认图片
        if (src.includes('enemy/')) {
            imgElement.src = 'assets/images/enemy/yelang.png';
        } else if (src.includes('char/')) {
            imgElement.src = 'assets/images/char/ck.png';
        } else if (src.includes('equi/')) {
            imgElement.src = 'assets/images/battle_sword.png';
        }
    }
    
    /**
     * API调用重试机制
     */
    async callApiWithRetry(apiFunction, options = {}) {
        const {
            maxRetries = this.maxRetries,
            retryDelay = this.retryDelay,
            fallbackData = null,
            description = 'API调用'
        } = options;
        
        let lastError;
        
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                console.log(`🔄 ${description} - 第${attempt}次尝试`);
                const result = await apiFunction();
                
                if (attempt > 1) {
                    console.log(`✅ ${description} - 重试成功`);
                    this.showErrorToast('连接已恢复', 'success');
                }
                
                return result;
                
            } catch (error) {
                lastError = error;
                console.warn(`❌ ${description} - 第${attempt}次失败:`, error.message);
                
                if (attempt < maxRetries) {
                    // 指数退避策略
                    const delay = retryDelay * Math.pow(2, attempt - 1);
                    console.log(`⏳ ${delay}ms后重试...`);
                    await this.delay(delay);
                } else {
                    console.error(`💥 ${description} - 所有重试失败`);
                }
            }
        }
        
        // 所有重试都失败，使用降级方案
        if (fallbackData) {
            console.log(`🔄 ${description} - 使用降级数据`);
            this.showErrorToast('使用离线模式', 'warning');
            return { success: true, data: fallbackData, fallback: true };
        }
        
        // 记录错误并抛出
        this.logError({
            type: 'API调用失败',
            description,
            error: lastError.message,
            attempts: maxRetries,
            timestamp: new Date().toISOString()
        });
        
        throw lastError;
    }
    
    /**
     * 延迟函数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    /**
     * 显示错误提示
     */
    showErrorToast(message, type = 'error') {
        // 防止重复显示
        if (this.isErrorToastVisible) {
            return;
        }
        
        this.isErrorToastVisible = true;
        
        const toast = document.createElement('div');
        toast.className = `error-toast error-toast-${type}`;
        toast.innerHTML = `
            <div class="error-toast-content">
                <span class="error-toast-icon">${this.getToastIcon(type)}</span>
                <span class="error-toast-message">${message}</span>
            </div>
        `;
        
        // 添加样式
        Object.assign(toast.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '12px 16px',
            borderRadius: '6px',
            color: 'white',
            fontSize: '14px',
            zIndex: '10000',
            maxWidth: '300px',
            boxShadow: '0 4px 12px rgba(0,0,0,0.3)',
            transform: 'translateX(100%)',
            transition: 'transform 0.3s ease'
        });
        
        // 根据类型设置背景色
        const colors = {
            error: '#f44336',
            warning: '#ff9800',
            success: '#4caf50',
            info: '#2196f3'
        };
        toast.style.backgroundColor = colors[type] || colors.error;
        
        document.body.appendChild(toast);
        
        // 动画显示
        setTimeout(() => {
            toast.style.transform = 'translateX(0)';
        }, 100);
        
        // 自动隐藏
        setTimeout(() => {
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
                this.isErrorToastVisible = false;
            }, 300);
        }, type === 'success' ? 2000 : 4000);
    }
    
    /**
     * 获取提示图标
     */
    getToastIcon(type) {
        const icons = {
            error: '❌',
            warning: '⚠️',
            success: '✅',
            info: 'ℹ️'
        };
        return icons[type] || icons.error;
    }
    
    /**
     * 记录错误
     */
    logError(errorInfo) {
        this.errorLog.push(errorInfo);
        
        // 保持错误日志在合理大小
        if (this.errorLog.length > 100) {
            this.errorLog = this.errorLog.slice(-50);
        }
        
        console.error('🚨 错误记录:', errorInfo);
    }
    
    /**
     * 尝试系统恢复
     */
    attemptSystemRecovery() {
        console.log('🔧 尝试系统恢复...');
        
        // 检查关键DOM元素
        const battleContainer = document.querySelector('.battle-container');
        const effectsContainer = document.querySelector('.effects-container');
        
        if (!battleContainer) {
            console.error('❌ 战斗容器丢失，无法恢复');
            this.showErrorToast('系统错误，请刷新页面', 'error');
            return false;
        }
        
        if (!effectsContainer && battleContainer) {
            console.log('🔧 重建效果容器...');
            const newEffectsContainer = document.createElement('div');
            newEffectsContainer.className = 'effects-container';
            battleContainer.appendChild(newEffectsContainer);
        }
        
        // 检查战斗系统实例
        if (window.battleSystem && typeof window.battleSystem.initialize === 'function') {
            console.log('🔧 尝试重新初始化战斗系统...');
            window.battleSystem.initialize().catch(error => {
                console.error('❌ 战斗系统恢复失败:', error);
                this.showErrorToast('系统恢复失败，请刷新页面', 'error');
            });
        }
        
        return true;
    }
    
    /**
     * 获取默认战斗数据（降级方案）
     */
    getDefaultBattleData() {
        return {
            playerData: {
                name: '修仙者',
                level: 1,
                        hp_bonus: 300,
        physical_attack: 20,
        physical_defense: 10,
                avatar: 'assets/images/char/ck.png'
            },
            enemyData: {
                name: '山精',
                level: 1,
                hp: 100,
                attack: 15,
                defense: 5,
                avatar: 'assets/images/enemy/yelang.png'
            },
            weaponSlots: [
                {
                    id: 1001,
                    name: '初学者飞剑',
                    skill_name: 'feijian',
                    physical_attack: 10,
                    animation_model: 'feijian'
                }
            ]
        };
    }
    
    /**
     * 获取错误统计
     */
    getErrorStats() {
        const stats = {
            total: this.errorLog.length,
            byType: {},
            recent: this.errorLog.slice(-10)
        };
        
        this.errorLog.forEach(error => {
            stats.byType[error.type] = (stats.byType[error.type] || 0) + 1;
        });
        
        return stats;
    }
    
    /**
     * 清除错误日志
     */
    clearErrorLog() {
        this.errorLog = [];
        console.log('🧹 错误日志已清除');
    }
}

// 创建全局错误处理器实例
if (!window.battleErrorHandler) {
    window.battleErrorHandler = new BattleErrorHandler();
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BattleErrorHandler;
} else {
    window.BattleErrorHandler = BattleErrorHandler;
} 