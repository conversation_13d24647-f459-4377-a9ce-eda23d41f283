# 🎯 历练地图境界掉落系统设计方案（简化版）

## 📋 需求分析

**目标**：让历练地图的怪物只掉落与其当前大境界相匹配的装备，提供更合理的游戏体验。

**现状问题**：
- 当前掉落系统按地图划分，不考虑怪物具体境界
- 低境界怪物可能掉落高境界装备，不合理
- 高境界玩家在低级地图得到无用装备

## 🗄️ 现有数据库结构分析

### 现有掉落系统表结构（✅ 基础完善，无需新增表）

1. **drop_groups** (掉落组表) - ✅ 8条数据
   ```
   id, group_name, group_type, total_weight, max_drops, min_drops, 
   guarantee_rare, description, is_active, created_at
   ```

2. **drop_group_items** (掉落组物品表) - ✅ 已有境界相关字段
   ```
   id, group_id, item_id, drop_weight, min_quantity, max_quantity,
   level_min, level_max, realm_requirement, special_condition, 
   is_boss_only
   ```
   **重要**：已有 `realm_requirement` 字段！

3. **map_drop_configs** (地图掉落配置表) - ✅ 正常运行
   ```
   链接地图与掉落组的关系配置
   ```

### 关键发现：现有结构已经支持境界掉落！
- `drop_group_items.realm_requirement` 字段已存在
- 只需要完善境界范围控制和更新掉落逻辑

## 🎯 简化设计方案（基于现有表结构）

### 核心原则：最小改动，最大效果

**不新增表，只增加字段和优化逻辑**

## 📋 分步骤实施计划

---

### 🔵 【步骤1】数据库字段完善 `[预计时间: 30分钟]`

**目标**：在现有表基础上增加境界范围控制字段

#### 1.1 为 drop_group_items 表增加境界范围字段
```sql
ALTER TABLE drop_group_items 
ADD COLUMN realm_min INT DEFAULT 1 COMMENT '最小境界要求',
ADD COLUMN realm_max INT DEFAULT 280 COMMENT '最大境界要求',
ADD INDEX idx_realm_range (realm_min, realm_max);
```

#### 1.2 备份现有掉落配置
```sql
CREATE TABLE drop_group_items_backup AS 
SELECT * FROM drop_group_items;
```

**完成标志**：
- ✅ 字段添加成功
- ✅ 索引创建完成
- ✅ 数据备份完成

---

### 🔵 【步骤2】大境界掉落组重构 `[预计时间: 45分钟]`

**目标**：按28个大境界重新组织掉落组，并分类物品类型

#### 2.1 大境界掉落组设计（28个大境界 × 物品类型）
```php
// 大境界划分（每10级一个大境界，基于项目实际境界体系）
$majorRealmConfigs = [
    1 => ['realm_min' => 1,   'realm_max' => 10,  'name' => '开光期'],
    2 => ['realm_min' => 11,  'realm_max' => 20,  'name' => '灵虚期'],
    3 => ['realm_min' => 21,  'realm_max' => 30,  'name' => '辟谷期'],
    4 => ['realm_min' => 31,  'realm_max' => 40,  'name' => '心动期'],
    5 => ['realm_min' => 41,  'realm_max' => 50,  'name' => '元化期'],
    6 => ['realm_min' => 51,  'realm_max' => 60,  'name' => '元婴期'],
    7 => ['realm_min' => 61,  'realm_max' => 70,  'name' => '离合期'],
    8 => ['realm_min' => 71,  'realm_max' => 80,  'name' => '空冥期'],
    9 => ['realm_min' => 81,  'realm_max' => 90,  'name' => '寂灭期'],
    10 => ['realm_min' => 91,  'realm_max' => 100, 'name' => '大乘期'],
    11 => ['realm_min' => 101, 'realm_max' => 110, 'name' => '渡劫期'],
    12 => ['realm_min' => 111, 'realm_max' => 120, 'name' => '凡仙期'],
    13 => ['realm_min' => 121, 'realm_max' => 130, 'name' => '地仙期'],
    14 => ['realm_min' => 131, 'realm_max' => 140, 'name' => '天仙期'],
    15 => ['realm_min' => 141, 'realm_max' => 150, 'name' => '真仙期'],
    16 => ['realm_min' => 151, 'realm_max' => 160, 'name' => '太乙真仙期'],
    17 => ['realm_min' => 161, 'realm_max' => 170, 'name' => '太乙金仙期'],
    18 => ['realm_min' => 171, 'realm_max' => 180, 'name' => '太乙玄仙期'],
    19 => ['realm_min' => 181, 'realm_max' => 190, 'name' => '大罗真仙期'],
    20 => ['realm_min' => 191, 'realm_max' => 200, 'name' => '大罗金仙期'],
    21 => ['realm_min' => 201, 'realm_max' => 210, 'name' => '大罗玄仙期'],
    22 => ['realm_min' => 211, 'realm_max' => 220, 'name' => '准圣期'],
    23 => ['realm_min' => 221, 'realm_max' => 230, 'name' => '教主期'],
    24 => ['realm_min' => 231, 'realm_max' => 240, 'name' => '混元期'],
    25 => ['realm_min' => 241, 'realm_max' => 250, 'name' => '混元金仙期'],
    26 => ['realm_min' => 251, 'realm_max' => 260, 'name' => '混元至仙期'],
    27 => ['realm_min' => 261, 'realm_max' => 270, 'name' => '天道期'],
    28 => ['realm_min' => 271, 'realm_max' => 280, 'name' => '鸿蒙至元期']
];

// 掉落物品类型分类
$dropItemTypes = [
    'equipment' => '装备类',    // 武器、护甲、饰品
    'material' => '材料类',     // 炼丹材料、制造材料
    'pill' => '丹药类',        // 各种成品丹药
    'recipe' => '丹方类',      // 炼丹配方
    'consumable' => '消耗品类'  // 其他消耗品
];
```

#### 2.2 创建大境界掉落组
```sql
-- 创建大境界装备掉落组（优先实现）
INSERT INTO drop_groups (group_name, group_type, max_drops, min_drops, description) VALUES
('开光期装备掉落', 'equipment', 3, 1, '境界1-10的装备掉落'),
('灵虚期装备掉落', 'equipment', 3, 1, '境界11-20的装备掉落'),
('辟谷期装备掉落', 'equipment', 3, 1, '境界21-30的装备掉落'),
-- ... 共28个装备掉落组

-- 预留其他类型掉落组（后续扩展）
('开光期材料掉落', 'material', 2, 1, '境界1-10的材料掉落'),
('开光期丹药掉落', 'pill', 2, 1, '境界1-10的丹药掉落');
-- ...
```

#### 2.3 配置装备到大境界掉落组
```sql
-- 将现有装备按境界要求分配到对应大境界掉落组
-- 每个装备主要在其对应大境界组中（90%权重），少量在前后大境界组中（5%权重）
INSERT INTO drop_group_items (group_id, item_id, realm_min, realm_max, drop_weight)
SELECT 
    对应大境界掉落组ID,
    gi.id,
    FLOOR((gi.realm_requirement - 1) / 10) * 10 + 1,  -- 大境界最小值
    FLOOR((gi.realm_requirement - 1) / 10) * 10 + 10, -- 大境界最大值
    CASE 
        WHEN gi.realm_requirement BETWEEN 大境界最小值 AND 大境界最大值 THEN 60  -- 主要掉落
        WHEN gi.realm_requirement BETWEEN 前一大境界范围 THEN 30                    -- 较多掉落
        WHEN gi.realm_requirement BETWEEN 后一大境界范围 THEN 10                    -- 少量掉落
        ELSE 0
    END as drop_weight
FROM game_items gi
WHERE gi.item_type IN ('weapon', 'armor', 'accessory');
```

**完成标志**：
- ✅ 28个大境界装备掉落组创建完成
- ✅ 装备按大境界分配完成（60%主要+30%前境界+10%后境界）
- ✅ 物品类型分类框架搭建完成

---

### 🔵 【步骤3】掉落逻辑优化 `[预计时间: 60分钟]`

**目标**：修改 `battle_drops_unified.php` 增加境界过滤

#### 3.1 添加怪物境界获取函数
```php
/**
 * 获取怪物当前境界（关键修正：看怪物境界，不是玩家境界）
 */
function getMonsterRealm($mapId, $stageNumber) {
    global $db;
    $stmt = $db->prepare("SELECT realm_level FROM map_stages WHERE map_id = ? AND stage_number = ?");
    $stmt->execute([$mapId, $stageNumber]);
    return $stmt->fetchColumn() ?: 1;
}

/**
 * 根据怪物境界计算大境界编号
 */
function getMajorRealmByMonsterRealm($monsterRealm) {
    return ceil($monsterRealm / 10); // 1-10归入大境界1，11-20归入大境界2，以此类推
}

/**
 * 获取怪物境界对应的掉落组（当前+前后大境界）
 */
function getDropGroupsByMonsterRealm($monsterRealm, $itemType = 'equipment') {
    global $db;
    
    $majorRealm = getMajorRealmByMonsterRealm($monsterRealm);
    $dropGroups = [];
    
    // 主要掉落：当前大境界（60%权重）
    $stmt = $db->prepare("
        SELECT id, group_name FROM drop_groups 
        WHERE group_name = ? AND is_active = 1
    ");
    $stmt->execute(["大境界{$majorRealm}{$itemType}掉落"]);
    if ($group = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $dropGroups[] = ['group_id' => $group['id'], 'weight' => 60];
    }
    
    // 次要掉落：前一个大境界（30%权重）
    if ($majorRealm > 1) {
        $stmt->execute(["大境界" . ($majorRealm - 1) . "{$itemType}掉落"]);
        if ($group = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $dropGroups[] = ['group_id' => $group['id'], 'weight' => 30];
        }
    }
    
    // 次要掉落：后一个大境界（10%权重）
    if ($majorRealm < 28) {
        $stmt->execute(["大境界" . ($majorRealm + 1) . "{$itemType}掉落"]);
        if ($group = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $dropGroups[] = ['group_id' => $group['id'], 'weight' => 10];
        }
    }
    
    return $dropGroups;
}
```

#### 3.2 修改掉落查询逻辑
```php
/**
 * 根据怪物境界获取掉落物品（关键修正：基于怪物境界，不是角色境界）
 */
function getMonsterRealmDropItems($mapId, $stageNumber, $itemType = 'equipment') {
    global $db;
    
    // 获取怪物境界
    $monsterRealm = getMonsterRealm($mapId, $stageNumber);
    
    // 获取对应的掉落组
    $dropGroups = getDropGroupsByMonsterRealm($monsterRealm, $itemType);
    
    $allDropItems = [];
    foreach ($dropGroups as $dropGroup) {
        $stmt = $db->prepare("
            SELECT dgi.*, gi.item_name, gi.item_type, gi.icon_image, gi.description, gi.sell_price,
                   gi.physical_attack, gi.immortal_attack, gi.physical_defense, gi.immortal_defense,
                   gi.hp_bonus, gi.mp_bonus, gi.speed_bonus, gi.critical_bonus, gi.critical_damage,
                   gi.accuracy_bonus, gi.dodge_bonus, gi.block_bonus,
                   ? as group_weight
            FROM drop_group_items dgi
            JOIN game_items gi ON dgi.item_id = gi.id
            WHERE dgi.group_id = ? AND gi.is_active = 1
            ORDER BY dgi.drop_weight DESC
        ");
        $stmt->execute([$dropGroup['weight'], $dropGroup['group_id']]);
        $allDropItems = array_merge($allDropItems, $stmt->fetchAll(PDO::FETCH_ASSOC));
    }
    
    return $allDropItems;
}
```

#### 3.3 集成到现有掉落计算流程
在 `calculateDropsNewStructure()` 函数中替换原有查询逻辑

**完成标志**：
- ✅ 新函数添加完成
- ✅ 掉落查询逻辑修改完成
- ✅ 与现有系统无缝集成

---

### 🔵 【步骤4】测试验证系统 `[预计时间: 45分钟]`

**目标**：验证境界掉落系统正常工作

#### 4.1 创建测试脚本
```php
// scripts/test_realm_drops.php
// 测试不同境界角色在不同地图的掉落情况
```

#### 4.2 测试用例设计
- 低境界怪物（境界1-10）的掉落：主要掉大境界1装备，少量掉大境界2装备
- 中境界怪物（境界101-110）的掉落：主要掉大境界11装备，少量掉大境界10和12装备
- 高境界怪物（境界271-280）的掉落：主要掉大境界28装备，少量掉大境界27装备
- 边界测试：境界19-21怪物的掉落（大境界2-3的交界处）

#### 4.3 验证掉落合理性
- 确认掉落装备符合怪物境界要求（60%当前大境界+30%前大境界+10%后大境界）
- 验证不会出现严重境界不匹配的装备（跨2个以上大境界）
- 检查各物品类型的掉落概率是否正常
- 验证历练地图只掉装备，为秘境等其他区域预留其他类型掉落

**完成标志**：
- ✅ 测试脚本运行正常
- ✅ 所有测试用例通过
- ✅ 掉落装备境界匹配正确

---

### 🔵 【步骤5】前端界面优化 `[预计时间: 30分钟]`

**目标**：在掉落显示中增加境界要求信息

#### 5.1 修改掉落物品显示
在胜利面板中显示装备的境界要求信息

#### 5.2 增加境界不匹配提示
如果获得的装备境界要求高于当前境界，给出提示

**完成标志**：
- ✅ 掉落装备显示境界要求
- ✅ 境界不匹配提示正常
- ✅ 界面美观度良好

---

### 🔵 【步骤6】数据完整性检查 `[预计时间: 30分钟]`

**目标**：确保所有装备都有正确的境界配置

#### 6.1 检查装备境界覆盖度
```sql
-- 检查各境界范围的装备数量分布
SELECT 
    CASE 
        WHEN realm_requirement BETWEEN 1 AND 10 THEN '开光期'
        WHEN realm_requirement BETWEEN 11 AND 20 THEN '灵虚期'
        WHEN realm_requirement BETWEEN 21 AND 30 THEN '辟谷期'
        WHEN realm_requirement BETWEEN 31 AND 40 THEN '心动期'
        WHEN realm_requirement BETWEEN 41 AND 50 THEN '元化期'
        WHEN realm_requirement BETWEEN 51 AND 60 THEN '元婴期'
        WHEN realm_requirement BETWEEN 61 AND 70 THEN '离合期'
        WHEN realm_requirement BETWEEN 71 AND 80 THEN '空冥期'
        WHEN realm_requirement BETWEEN 81 AND 90 THEN '寂灭期'
        WHEN realm_requirement BETWEEN 91 AND 100 THEN '大乘期'
        WHEN realm_requirement BETWEEN 101 AND 110 THEN '渡劫期'
        WHEN realm_requirement BETWEEN 111 AND 120 THEN '凡仙期'
        WHEN realm_requirement BETWEEN 121 AND 130 THEN '地仙期'
        WHEN realm_requirement BETWEEN 131 AND 140 THEN '天仙期'
        WHEN realm_requirement BETWEEN 141 AND 150 THEN '真仙期'
        WHEN realm_requirement BETWEEN 151 AND 160 THEN '太乙真仙期'
        WHEN realm_requirement BETWEEN 161 AND 170 THEN '太乙金仙期'
        WHEN realm_requirement BETWEEN 171 AND 180 THEN '太乙玄仙期'
        WHEN realm_requirement BETWEEN 181 AND 190 THEN '大罗真仙期'
        WHEN realm_requirement BETWEEN 191 AND 200 THEN '大罗金仙期'
        WHEN realm_requirement BETWEEN 201 AND 210 THEN '大罗玄仙期'
        WHEN realm_requirement BETWEEN 211 AND 220 THEN '准圣期'
        WHEN realm_requirement BETWEEN 221 AND 230 THEN '教主期'
        WHEN realm_requirement BETWEEN 231 AND 240 THEN '混元期'
        WHEN realm_requirement BETWEEN 241 AND 250 THEN '混元金仙期'
        WHEN realm_requirement BETWEEN 251 AND 260 THEN '混元至仙期'
        WHEN realm_requirement BETWEEN 261 AND 270 THEN '天道期'
        WHEN realm_requirement BETWEEN 271 AND 280 THEN '鸿蒙至元期'
        ELSE '其他境界'
    END as realm_range,
    COUNT(*) as equipment_count
FROM game_items 
WHERE item_type IN ('weapon', 'armor', 'accessory')
GROUP BY realm_range
ORDER BY MIN(realm_requirement);
```

#### 6.2 修复境界配置缺失的装备
为缺失境界要求的装备补充合理的境界配置

**完成标志**：
- ✅ 所有装备都有境界要求配置
- ✅ 各境界装备数量分布合理
- ✅ 无境界配置缺失问题

---

### 🔵 【步骤7】性能优化 `[预计时间: 30分钟]`

**目标**：确保新的境界过滤不影响掉落系统性能

#### 7.1 添加必要索引
```sql
-- 确保查询性能的索引
ALTER TABLE drop_group_items 
ADD INDEX idx_group_realm (group_id, realm_min, realm_max),
ADD INDEX idx_realm_range_item (realm_min, realm_max, item_id);
```

#### 7.2 查询性能测试
测试境界过滤查询的执行时间

**完成标志**：
- ✅ 索引添加完成
- ✅ 查询性能满足要求（<100ms）
- ✅ 无性能回归问题

---

### 🔵 【步骤8】生产环境部署 `[预计时间: 15分钟]`

**目标**：将境界掉落系统部署到生产环境

#### 8.1 备份生产数据
- 备份 drop_group_items 表
- 备份 battle_drops_unified.php 文件

#### 8.2 执行部署
- 执行数据库字段添加
- 更新掉落逻辑代码
- 更新境界配置数据

#### 8.3 部署后验证
- 验证系统正常运行
- 检查掉落功能正常
- 监控错误日志

**完成标志**：
- ✅ 生产环境部署成功
- ✅ 系统运行正常
- ✅ 无异常错误

---

## 🔧 扩展方案：未来新装备/新地图

### 新装备添加流程
```sql
-- 1. 添加新装备到 game_items 表（设置 realm_requirement）
INSERT INTO game_items (item_name, realm_requirement, ...) 
VALUES ('新装备', 目标境界, ...);

-- 2. 将新装备添加到对应地图的掉落组
INSERT INTO drop_group_items (group_id, item_id, realm_min, realm_max, drop_weight) 
SELECT 对应地图掉落组ID, LAST_INSERT_ID(), 地图境界最小值, 地图境界最大值, 10;
```

### 新地图/新区域添加流程
```sql
-- 1. 为新区域创建特定物品类型的掉落配置
-- 例如：秘境地图需要掉落材料和丹药
INSERT INTO map_drop_configs (map_id, drop_group_id, drop_chance, item_type) 
VALUES 
(新地图ID, 对应大境界材料掉落组ID, 60.00, 'material'),    -- 秘境掉材料
(新地图ID, 对应大境界丹药掉落组ID, 40.00, 'pill'),       -- 秘境掉丹药
(新地图ID, 对应大境界装备掉落组ID, 80.00, 'equipment'); -- 历练地图掉装备

-- 2. 如果需要特殊掉落规则，可创建区域专属掉落组
INSERT INTO drop_groups (group_name, group_type, max_drops, min_drops) 
VALUES ('秘境大境界X特殊掉落', 'special', 2, 1);

-- 3. 配置特殊掉落物品
INSERT INTO drop_group_items (group_id, item_id, drop_weight)
SELECT 特殊掉落组ID, gi.id, 权重
FROM game_items gi
WHERE gi.item_type = '特定类型' AND gi.realm_requirement BETWEEN X AND Y;
```

## 📊 预计总体时间安排

| 步骤 | 预计时间 | 依赖关系 | 风险级别 |
|------|----------|----------|----------|
| 步骤1：数据库字段完善 | 30分钟 | 无 | 低 |
| 步骤2：地图境界配置 | 45分钟 | 依赖步骤1 | 低 |
| 步骤3：掉落逻辑优化 | 60分钟 | 依赖步骤2 | 中 |
| 步骤4：测试验证 | 45分钟 | 依赖步骤3 | 中 |
| 步骤5：前端优化 | 30分钟 | 可并行 | 低 |
| 步骤6：数据检查 | 30分钟 | 可并行 | 低 |
| 步骤7：性能优化 | 30分钟 | 依赖步骤3 | 低 |
| 步骤8：生产部署 | 15分钟 | 依赖所有步骤 | 中 |

**总计预估时间：4.5小时**

## 🎯 预期效果

1. **游戏体验优化**
   - 装备掉落更符合游戏逻辑
   - 避免境界不匹配的装备掉落
   - 提供渐进式装备升级路径

2. **系统维护性**
   - 基于现有表结构，维护简单
   - 扩展新装备/新地图流程标准化
   - 配置清晰，易于调整

3. **技术优势**
   - 最小化代码改动，降低风险
   - 保持向后兼容性
   - 性能影响最小

---

## 📝 实施注意事项

1. **数据安全**：每个步骤前都要备份相关数据
2. **分批实施**：可以先在1-2个地图测试，再全面推广
3. **回滚预案**：保留原有掉落逻辑，可通过配置开关切换
4. **用户体验**：新系统对现有玩家应该是体验提升，不是限制

---

*设计方案更新日期: 2024年12月19日*  
*基于现有系统优化，最小改动实现境界掉落功能* 