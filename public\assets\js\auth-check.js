/**
 * 全局登录状态检查模块
 * 用于在所有需要登录的页面中自动检查用户登录状态
 * 如果用户未登录，自动跳转到登录页面
 */

// 全局配置
const AUTH_CONFIG = {
    // 登录页面路径
    LOGIN_PAGE: 'login.html',
    // 新的认证状态API路径 - 修复路径问题
    AUTH_STATUS_API: window.GameConfig ? window.GameConfig.getApiUrl('auth_status.php') : '../src/api/auth_status.php',
    // 用户信息API路径（备用）
    USER_INFO_API: window.GameConfig ? window.GameConfig.getApiUrl('user_info.php') : '../src/api/user_info.php',
    // 会话调试API路径（备用）
    SESSION_DEBUG_API: window.GameConfig ? window.GameConfig.getApiUrl('create_character.php') : '../src/api/create_character.php',
    // 不需要登录检查的页面列表
    EXCLUDED_PAGES: [
        'login.html',
        'register.html',
        'index.html'
    ],
    // 检查间隔（毫秒）- 延长到更合理的时间
    CHECK_INTERVAL: 1800000, // 30分钟检查一次 (30 * 60 * 1000)
    // 🔧 温和模式：是否启用自动检查（默认关闭，只在真正需要时开启）
    AUTO_CHECK_ENABLED: false, // 🔧 默认关闭定期检查
    // 快速检查间隔（毫秒）- 延长时间
    QUICK_CHECK_INTERVAL: 1800000, // 30分钟快速检查一次 (30 * 60 * 1000)
    // 🔧 新增：可见性检查防抖时间（毫秒）- 大幅延长
    VISIBILITY_DEBOUNCE: 300000, // 5分钟内不重复检查 (5 * 60 * 1000)
    // 🔧 新增：焦点检查防抖时间（毫秒）- 大幅延长
    FOCUS_DEBOUNCE: 300000, // 5分钟内不重复检查 (5 * 60 * 1000)
    // 🔧 新增：温和模式设置
    GENTLE_MODE: {
        // 是否启用温和模式（不主动退出登录，只在真正需要时检查）
        ENABLED: true,
        // 允许的连续检查失败次数
        MAX_FAILURES: 3,
        // 温和检查间隔（只在用户主动操作时检查）
        PASSIVE_CHECK_INTERVAL: 3600000, // 1小时检查一次 (60 * 60 * 1000)
        // 是否禁用焦点和可见性检查
        DISABLE_FOCUS_VISIBILITY_CHECK: true
    }
};

// 全局状态
let authCheckInterval = null;
let quickCheckInterval = null;
let isCheckingAuth = false;
let lastAuthCheck = 0;
let lastQuickCheck = 0;
let lastVisibilityCheck = 0; // 🔧 新增：上次可见性检查时间
let lastFocusCheck = 0; // 🔧 新增：上次焦点检查时间
let consecutiveFailures = 0;
let lastSuccessfulCheck = Date.now(); // 🔧 新增：上次成功检查时间

/**
 * 检查当前页面是否需要登录验证
 * @returns {boolean} 是否需要登录验证
 */
function needsAuthCheck() {
    const currentPage = window.location.pathname.split('/').pop();
    return !AUTH_CONFIG.EXCLUDED_PAGES.includes(currentPage);
}

/**
 * 显示登录状态检查的加载提示
 */
function showAuthCheckLoading() {
    // 创建或显示加载提示
    let loadingDiv = document.getElementById('auth-check-loading');
    if (!loadingDiv) {
        loadingDiv = document.createElement('div');
        loadingDiv.id = 'auth-check-loading';
        loadingDiv.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            font-size: 18px;
        `;
        loadingDiv.innerHTML = '<div>正在验证登录状态...</div>';
        document.body.appendChild(loadingDiv);
    }
    loadingDiv.style.display = 'flex';
}

/**
 * 隐藏登录状态检查的加载提示
 */
function hideAuthCheckLoading() {
    const loadingDiv = document.getElementById('auth-check-loading');
    if (loadingDiv) {
        loadingDiv.style.display = 'none';
    }
}

/**
 * 跳转到登录页面
 * @param {string} message 提示消息
 */
function redirectToLogin(message = '请先登录后再访问此页面') {
    console.log('🔄 跳转到登录页面:', message);
    
    // 清除可能存在的定时器
    if (authCheckInterval) {
        clearInterval(authCheckInterval);
        authCheckInterval = null;
    }
    if (quickCheckInterval) {
        clearInterval(quickCheckInterval);
        quickCheckInterval = null;
    }
    
    // 显示提示消息
    if (message && typeof alert !== 'undefined') {
        alert(message);
    }
    
    // 跳转到登录页面
    window.location.href = AUTH_CONFIG.LOGIN_PAGE;
}

/**
 * 🔧 新增：温和检查登录状态（不立即跳转，返回状态供调用者决定）
 * @param {string} checkType 检查类型
 * @returns {Promise<{isLoggedIn: boolean, shouldRedirect: boolean, data: any}>}
 */
async function gentleAuthCheck(checkType = 'quick') {
    try {
        console.log('🔍 执行温和登录状态检查...', { checkType });
        
        // 使用快速检查API
        const authResponse = await fetch(`${AUTH_CONFIG.AUTH_STATUS_API}?action=${checkType}`, {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'Cache-Control': 'no-cache'
            }
        });
        
        if (!authResponse.ok) {
            console.warn(`⚠️ 认证API请求失败: ${authResponse.status}，假设用户仍登录`);
            return { isLoggedIn: true, shouldRedirect: false, data: null };
        }
        
        const authData = await authResponse.json();
        console.log('📊 温和检查API响应:', authData);
        
        // 检查登录状态
        if (authData.success && authData.logged_in) {
            console.log('✅ 温和检查：用户已登录');
            consecutiveFailures = 0; // 重置失败计数
            lastSuccessfulCheck = Date.now();
            return { isLoggedIn: true, shouldRedirect: false, data: authData };
        }
        
        // 用户未登录，但在温和模式下需要更谨慎
        console.log('❌ 温和检查：用户未登录，但需要进一步确认');
        consecutiveFailures++;
        
        // 🔧 温和模式：只有在连续多次失败时才考虑跳转
        if (AUTH_CONFIG.GENTLE_MODE.ENABLED) {
            if (consecutiveFailures >= AUTH_CONFIG.GENTLE_MODE.MAX_FAILURES) {
                console.log('🚨 连续检查失败次数过多，确认需要重新登录');
                return { isLoggedIn: false, shouldRedirect: true, data: authData };
            } else {
                console.log(`⚠️ 登录检查失败 (${consecutiveFailures}/${AUTH_CONFIG.GENTLE_MODE.MAX_FAILURES})，暂不跳转`);
                return { isLoggedIn: false, shouldRedirect: false, data: authData };
            }
        }
        
        return { isLoggedIn: false, shouldRedirect: true, data: authData };
        
    } catch (error) {
        console.error('🚨 温和登录状态检查失败:', error);
        consecutiveFailures++;
        
        // 网络错误在温和模式下假设用户仍然登录
        console.log('⚠️ 网络错误，假设用户仍然登录');
        return { isLoggedIn: true, shouldRedirect: false, data: null };
    }
}

/**
 * 执行登录状态检查
 * @param {boolean} showLoading 是否显示加载提示
 * @param {boolean} isInitialCheck 是否为初始检查
 * @param {string} checkType 检查类型 ('check', 'quick', 'refresh')
 * @returns {Promise<boolean>} 登录状态
 */
async function checkAuthStatus(showLoading = false, isInitialCheck = false, checkType = 'check') {
    // 防止重复检查
    if (isCheckingAuth) {
        console.log('⏳ 登录状态检查正在进行中，跳过此次检查');
        return true; // 🔧 假设用户仍登录，避免重复检查导致跳转
    }
    
    isCheckingAuth = true;
    const checkStartTime = Date.now();
    
    try {
        console.log('🔍 开始检查登录状态...', {
            showLoading,
            isInitialCheck,
            checkType,
            currentPage: window.location.pathname
        });
        
        if (showLoading) {
            showAuthCheckLoading();
        }
        
        // 🔧 对于非初始检查，使用温和检查
        if (!isInitialCheck && AUTH_CONFIG.GENTLE_MODE.ENABLED) {
            const result = await gentleAuthCheck(checkType);
            
            if (result.shouldRedirect) {
                return false; // 需要跳转
            }
            
            return result.isLoggedIn;
        }
        
        // 使用认证状态API（完整检查）
        const authResponse = await fetch(`${AUTH_CONFIG.AUTH_STATUS_API}?action=${checkType}`, {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'Cache-Control': 'no-cache'
            }
        });
        
        if (!authResponse.ok) {
            throw new Error(`认证API请求失败: ${authResponse.status}`);
        }
        
        const authData = await authResponse.json();
        console.log('📊 认证API响应:', authData);
        
        // 检查登录状态
        if (authData.success && authData.logged_in) {
            console.log('✅ 用户已登录');
            consecutiveFailures = 0; // 重置失败计数
            lastSuccessfulCheck = Date.now();
            
            if (checkType === 'check') {
                lastAuthCheck = Date.now();
            } else if (checkType === 'quick') {
                lastQuickCheck = Date.now();
            }
            
            return true;
        }
        
        // 用户未登录
        console.log('❌ 用户未登录:', authData.message);
        consecutiveFailures++;
        
        return false;
        
    } catch (error) {
        console.error('🚨 登录状态检查失败:', error);
        consecutiveFailures++;
        
        // 如果连续失败次数过多，可能是网络问题
        if (consecutiveFailures >= 3) {
            console.warn('⚠️ 连续检查失败次数过多，可能存在网络问题');
        }
        
        // 网络错误时，如果是初始检查，仍然跳转到登录页面
        if (isInitialCheck) {
            console.log('🔄 初始检查失败，跳转到登录页面');
            return false;
        }
        
        // 🔧 非初始检查的网络错误，在温和模式下假设用户仍然登录
        if (AUTH_CONFIG.GENTLE_MODE.ENABLED) {
            console.log('⚠️ 非初始检查失败，温和模式假设用户仍然登录');
            return true;
        }
        
        return false;
        
    } finally {
        isCheckingAuth = false;
        if (showLoading) {
            hideAuthCheckLoading();
        }
        
        const checkDuration = Date.now() - checkStartTime;
        console.log(`⏱️ 登录状态检查完成，耗时: ${checkDuration}ms`);
    }
}

/**
 * 快速登录状态检查（用于定期检查）
 * @returns {Promise<boolean>} 登录状态
 */
async function quickAuthCheck() {
    // 🔧 优化：如果距离上次快速检查时间太短，跳过检查
    const now = Date.now();
    if (now - lastQuickCheck < AUTH_CONFIG.QUICK_CHECK_INTERVAL) {
        console.log('⏱️ 快速检查防抖中，跳过检查');
        return true; // 🔧 修复：返回true避免误跳转
    }
    
    console.log('⚡ 执行快速登录检查...');
    return await checkAuthStatus(false, false, 'quick');
}

/**
 * 初始化登录状态检查
 * 在页面加载时调用
 */
async function initAuthCheck() {
    // 检查当前页面是否需要登录验证
    if (!needsAuthCheck()) {
        console.log('📄 当前页面无需登录验证:', window.location.pathname);
        return;
    }
    
    console.log('🚀 初始化登录状态检查...');
    
    // 🔧 新增：输出配置信息
    console.log('📊 登录检查配置:', {
        CHECK_INTERVAL: `${AUTH_CONFIG.CHECK_INTERVAL}ms (${AUTH_CONFIG.CHECK_INTERVAL / 60000}分钟)`,
        QUICK_CHECK_INTERVAL: `${AUTH_CONFIG.QUICK_CHECK_INTERVAL}ms (${AUTH_CONFIG.QUICK_CHECK_INTERVAL / 60000}分钟)`,
        VISIBILITY_DEBOUNCE: `${AUTH_CONFIG.VISIBILITY_DEBOUNCE}ms (${AUTH_CONFIG.VISIBILITY_DEBOUNCE / 1000}秒)`,
        FOCUS_DEBOUNCE: `${AUTH_CONFIG.FOCUS_DEBOUNCE}ms (${AUTH_CONFIG.FOCUS_DEBOUNCE / 1000}秒)`,
        AUTO_CHECK_ENABLED: AUTH_CONFIG.AUTO_CHECK_ENABLED,
        GENTLE_MODE_ENABLED: AUTH_CONFIG.GENTLE_MODE.ENABLED,
        DISABLE_FOCUS_VISIBILITY_CHECK: AUTH_CONFIG.GENTLE_MODE.DISABLE_FOCUS_VISIBILITY_CHECK
    });
    
    // 执行初始登录检查
    const isLoggedIn = await checkAuthStatus(true, true, 'check');
    
    if (!isLoggedIn) {
        redirectToLogin('登录状态已过期，请重新登录');
        return;
    }
    
    // 🔧 根据温和模式决定是否启动定期检查
    if (AUTH_CONFIG.AUTO_CHECK_ENABLED && !AUTH_CONFIG.GENTLE_MODE.ENABLED) {
        console.log('🔄 启动传统定期检查模式');
        startPeriodicAuthCheck();
        startQuickAuthCheck();
    } else if (AUTH_CONFIG.GENTLE_MODE.ENABLED) {
        console.log('🔧 温和模式已启用，跳过定期检查');
    } else {
        console.log('⏸️ 自动检查已禁用');
    }
    
    // 🔧 根据温和模式决定是否启用页面监听器
    if (!AUTH_CONFIG.GENTLE_MODE.ENABLED || !AUTH_CONFIG.GENTLE_MODE.DISABLE_FOCUS_VISIBILITY_CHECK) {
        console.log('👁️ 启用页面可见性和焦点监听');
        // 监听页面可见性变化，当页面重新可见时检查登录状态
        document.addEventListener('visibilitychange', handleVisibilityChange);
        // 监听窗口焦点变化
        window.addEventListener('focus', handleWindowFocus);
    } else {
        console.log('🔧 温和模式：已禁用页面可见性和焦点监听');
    }
    
    console.log('✅ 登录状态检查初始化完成');
}

/**
 * 启动定期登录状态检查
 */
function startPeriodicAuthCheck() {
    if (authCheckInterval) {
        clearInterval(authCheckInterval);
    }
    
    authCheckInterval = setInterval(async () => {
        // 🔧 新增：如果页面不可见，跳过检查
        if (document.hidden) {
            console.log('📄 页面不可见，跳过定期检查');
            return;
        }
        
        // 🔧 优化：如果距离上次检查时间太短，跳过检查
        const now = Date.now();
        if (now - lastAuthCheck < AUTH_CONFIG.CHECK_INTERVAL) {
            console.log('⏱️ 定期检查防抖中，跳过检查');
            return;
        }
        
        console.log('🔄 执行定期登录状态检查...');
        const isLoggedIn = await checkAuthStatus(false, false, 'check');
        
        if (!isLoggedIn) {
            redirectToLogin('登录状态已过期，请重新登录');
        }
    }, AUTH_CONFIG.CHECK_INTERVAL);
    
    console.log(`⏰ 已启动定期登录检查，间隔: ${AUTH_CONFIG.CHECK_INTERVAL}ms`);
}

/**
 * 启动快速登录状态检查
 */
function startQuickAuthCheck() {
    if (quickCheckInterval) {
        clearInterval(quickCheckInterval);
    }
    
    quickCheckInterval = setInterval(async () => {
        // 🔧 新增：如果页面不可见，跳过检查
        if (document.hidden) {
            console.log('📄 页面不可见，跳过快速检查');
            return;
        }
        
        // 🔧 优化：执行快速检查（内部已有防抖机制）
        const isLoggedIn = await quickAuthCheck();
        
        if (!isLoggedIn) {
            redirectToLogin('登录状态已过期，请重新登录');
        }
    }, AUTH_CONFIG.QUICK_CHECK_INTERVAL);
    
    console.log(`⚡ 已启动快速登录检查，间隔: ${AUTH_CONFIG.QUICK_CHECK_INTERVAL}ms`);
}

/**
 * 停止定期登录状态检查
 */
function stopPeriodicAuthCheck() {
    if (authCheckInterval) {
        clearInterval(authCheckInterval);
        authCheckInterval = null;
        console.log('⏹️ 已停止定期登录检查');
    }
    
    if (quickCheckInterval) {
        clearInterval(quickCheckInterval);
        quickCheckInterval = null;
        console.log('⏹️ 已停止快速登录检查');
    }
}

/**
 * 🔧 修改：处理页面可见性变化（温和模式）
 */
function handleVisibilityChange() {
    // 🔧 温和模式：禁用焦点和可见性主动检查
    if (AUTH_CONFIG.GENTLE_MODE.ENABLED && AUTH_CONFIG.GENTLE_MODE.DISABLE_FOCUS_VISIBILITY_CHECK) {
        console.log('🔧 温和模式：已禁用页面可见性检查');
        return;
    }
    
    if (!document.hidden && needsAuthCheck()) {
        // 🔧 新增：检查防抖时间，避免频繁检查
        const now = Date.now();
        if (now - lastVisibilityCheck < AUTH_CONFIG.VISIBILITY_DEBOUNCE) {
            console.log('⏱️ 页面可见性检查防抖中，跳过检查');
            return;
        }
        
        lastVisibilityCheck = now;
        console.log('👁️ 页面重新可见，进行温和检查...');
        
        // 🔧 使用温和检查，不会立即跳转
        gentleAuthCheck('quick').then(result => {
            if (result.shouldRedirect) {
                redirectToLogin('登录状态已过期，请重新登录');
            } else if (!result.isLoggedIn) {
                console.log('⚠️ 页面可见性检查发现登录状态异常，但暂不跳转');
            }
        }).catch(error => {
            console.warn('⚠️ 页面可见性检查失败:', error);
        });
    }
}

/**
 * 🔧 修改：处理窗口焦点变化（温和模式）
 */
function handleWindowFocus() {
    // 🔧 温和模式：禁用焦点和可见性主动检查
    if (AUTH_CONFIG.GENTLE_MODE.ENABLED && AUTH_CONFIG.GENTLE_MODE.DISABLE_FOCUS_VISIBILITY_CHECK) {
        console.log('🔧 温和模式：已禁用窗口焦点检查');
        return;
    }
    
    if (needsAuthCheck()) {
        // 🔧 新增：检查防抖时间，避免频繁检查
        const now = Date.now();
        if (now - lastFocusCheck < AUTH_CONFIG.FOCUS_DEBOUNCE) {
            console.log('⏱️ 窗口焦点检查防抖中，跳过检查');
            return;
        }
        
        lastFocusCheck = now;
        console.log('🎯 窗口重新获得焦点，进行温和检查...');
        
        // 🔧 使用温和检查，不会立即跳转
        gentleAuthCheck('quick').then(result => {
            if (result.shouldRedirect) {
                redirectToLogin('登录状态已过期，请重新登录');
            } else if (!result.isLoggedIn) {
                console.log('⚠️ 窗口焦点检查发现登录状态异常，但暂不跳转');
            }
        }).catch(error => {
            console.warn('⚠️ 窗口焦点检查失败:', error);
        });
    }
}

/**
 * 手动触发登录状态检查
 * 可以在其他脚本中调用
 * @param {boolean} showLoading 是否显示加载提示
 * @param {string} checkType 检查类型
 * @returns {Promise<boolean>} 登录状态
 */
async function manualAuthCheck(showLoading = true, checkType = 'check') {
    console.log('🔧 手动触发登录状态检查...');
    return await checkAuthStatus(showLoading, false, checkType);
}

/**
 * 刷新会话信息
 * @param {boolean} showLoading 是否显示加载提示
 * @returns {Promise<boolean>} 刷新结果
 */
async function refreshSession(showLoading = true) {
    console.log('🔄 刷新会话信息...');
    return await checkAuthStatus(showLoading, false, 'refresh');
}

/**
 * 清理登录检查相关资源
 * 在页面卸载时调用
 */
function cleanupAuthCheck() {
    stopPeriodicAuthCheck();
    document.removeEventListener('visibilitychange', handleVisibilityChange);
    window.removeEventListener('focus', handleWindowFocus);
    console.log('🧹 登录检查资源已清理');
}

// 页面卸载时清理资源
window.addEventListener('beforeunload', cleanupAuthCheck);

// 导出公共接口
window.AuthCheck = {
    init: initAuthCheck,
    check: manualAuthCheck,
    quickCheck: quickAuthCheck,
    gentleCheck: checkLoginStatusGently,
    refresh: refreshSession,
    cleanup: cleanupAuthCheck,
    config: AUTH_CONFIG,
    setGentleMode: setGentleModeConfig,
    getStatus: getAuthCheckStatus
};

// 自动初始化（当DOM加载完成时）
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initAuthCheck);
} else {
    // DOM已经加载完成
    initAuthCheck();
}

console.log('✅ 全局登录状态检查模块已加载');

/**
 * 🔧 新增：手动触发温和登录检查（供其他代码调用）
 * @param {boolean} showNotification 是否显示通知
 * @returns {Promise<{isLoggedIn: boolean, needAction: boolean, message: string}>}
 */
async function checkLoginStatusGently(showNotification = false) {
    console.log('🔧 手动温和登录状态检查...');
    
    try {
        const result = await gentleAuthCheck('quick');
        
        const response = {
            isLoggedIn: result.isLoggedIn,
            needAction: result.shouldRedirect,
            message: result.isLoggedIn ? '用户已登录' : '用户未登录'
        };
        
        if (showNotification && !result.isLoggedIn) {
            console.warn('⚠️ 检测到登录状态异常');
            if (result.shouldRedirect) {
                console.warn('🚨 建议用户重新登录');
            }
        }
        
        return response;
        
    } catch (error) {
        console.error('🚨 手动检查失败:', error);
        return {
            isLoggedIn: true, // 出错时假设用户仍登录
            needAction: false,
            message: '检查失败，假设用户已登录'
        };
    }
}

/**
 * 🔧 新增：动态设置温和模式配置
 * @param {Object} options 配置选项
 */
function setGentleModeConfig(options = {}) {
    console.log('🔧 更新温和模式配置:', options);
    
    if (typeof options.enabled === 'boolean') {
        AUTH_CONFIG.GENTLE_MODE.ENABLED = options.enabled;
        console.log(`🔧 温和模式: ${options.enabled ? '启用' : '禁用'}`);
    }
    
    if (typeof options.maxFailures === 'number') {
        AUTH_CONFIG.GENTLE_MODE.MAX_FAILURES = options.maxFailures;
        console.log(`🔧 最大失败次数: ${options.maxFailures}`);
    }
    
    if (typeof options.disableFocusVisibilityCheck === 'boolean') {
        AUTH_CONFIG.GENTLE_MODE.DISABLE_FOCUS_VISIBILITY_CHECK = options.disableFocusVisibilityCheck;
        console.log(`🔧 禁用焦点/可见性检查: ${options.disableFocusVisibilityCheck}`);
    }
    
    if (typeof options.autoCheckEnabled === 'boolean') {
        AUTH_CONFIG.AUTO_CHECK_ENABLED = options.autoCheckEnabled;
        console.log(`🔧 自动检查: ${options.autoCheckEnabled ? '启用' : '禁用'}`);
        
        // 根据新配置重新初始化
        if (options.autoCheckEnabled && !AUTH_CONFIG.GENTLE_MODE.ENABLED) {
            console.log('🔄 重新启动定期检查');
            startPeriodicAuthCheck();
            startQuickAuthCheck();
        } else {
            console.log('⏹️ 停止定期检查');
            stopPeriodicAuthCheck();
        }
    }
}

/**
 * 🔧 新增：获取当前登录检测状态信息
 * @returns {Object} 状态信息
 */
function getAuthCheckStatus() {
    return {
        config: {
            gentleMode: AUTH_CONFIG.GENTLE_MODE.ENABLED,
            autoCheck: AUTH_CONFIG.AUTO_CHECK_ENABLED,
            disableFocusVisibility: AUTH_CONFIG.GENTLE_MODE.DISABLE_FOCUS_VISIBILITY_CHECK
        },
        state: {
            isChecking: isCheckingAuth,
            consecutiveFailures: consecutiveFailures,
            lastSuccessfulCheck: lastSuccessfulCheck,
            lastAuthCheck: lastAuthCheck,
            lastQuickCheck: lastQuickCheck
        },
        intervals: {
            authCheckRunning: authCheckInterval !== null,
            quickCheckRunning: quickCheckInterval !== null
        }
    };
} 