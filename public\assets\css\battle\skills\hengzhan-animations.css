/**
 * 横斩技能动画样式 - 独立文件
 * 剑类/火法混合技能：巨剑旋转蓄力 + 直接击中 + 火焰爆炸
 */

/* === 主容器 === */
.hengzhan-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1000;
}

/* === 第一阶段：蓄力旋转剑 === */
.hengzhan-main-sword {
    position: absolute;
    width: 60px;
    height: 120px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    transform-origin: center center;
    -webkit-filter: drop-shadow(0 0 15px #ffaa00) drop-shadow(0 0 30px #ff6600);
    filter: drop-shadow(0 0 15px #ffaa00) drop-shadow(0 0 30px #ff6600);
    animation: hengzhan-spin-charge 2s ease-out forwards;
}

@keyframes hengzhan-spin-charge {
    0% {
        transform: translate(-50%, -50%) rotate(0deg) scale(1);
        -webkit-filter: drop-shadow(0 0 15px #ffaa00) drop-shadow(0 0 30px #ff6600);
        filter: drop-shadow(0 0 15px #ffaa00) drop-shadow(0 0 30px #ff6600);
    }
    50% {
        transform: translate(-50%, -50%) rotate(720deg) scale(1.5);
        -webkit-filter: drop-shadow(0 0 25px #ffaa00) drop-shadow(0 0 50px #ff6600) drop-shadow(0 0 75px #ff0000);
        filter: drop-shadow(0 0 25px #ffaa00) drop-shadow(0 0 50px #ff6600) drop-shadow(0 0 75px #ff0000);
    }
    100% {
        width: var(--finalLength);
        height: calc(var(--finalLength) * 2);
        transform: translate(-50%, -50%) rotate(1530deg) scale(2);
        -webkit-filter: drop-shadow(0 0 40px #ffaa00) drop-shadow(0 0 80px #ff6600) drop-shadow(0 0 120px #ff0000);
        filter: drop-shadow(0 0 40px #ffaa00) drop-shadow(0 0 80px #ff6600) drop-shadow(0 0 120px #ff0000);
    }
}

/* === 剑影虚影效果 === */
.hengzhan-sword-shadow {
    position: absolute;
    width: 60px;
    height: 120px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    transform-origin: center center;
    animation: hengzhan-shadow-spin 2s ease-out forwards;
}

@keyframes hengzhan-shadow-spin {
    0% {
        transform: translate(-50%, -50%) rotate(0deg) scale(0.8);
        opacity: 0.6;
        -webkit-filter: drop-shadow(0 0 10px #ffaa00);
        filter: drop-shadow(0 0 10px #ffaa00);
    }
    100% {
        width: var(--finalLength);
        height: calc(var(--finalLength) * 2);
        transform: translate(-50%, -50%) rotate(1200deg) scale(1.8);
        opacity: 0.2;
        -webkit-filter: drop-shadow(0 0 30px #ff6600);
        filter: drop-shadow(0 0 30px #ff6600);
    }
}

/* === 旋转能量场 === */
.hengzhan-energy-field {
    width: 80px;
    height: 80px;
    border: 3px solid rgba(255, 170, 0, 0.6);
    border-radius: 50%;
    animation: hengzhan-energy-rotate 2s linear infinite;
}

@keyframes hengzhan-energy-rotate {
    0% {
        transform: translate(-50%, -50%) rotate(0deg) scale(1);
        border-color: rgba(255, 170, 0, 0.6);
        box-shadow: 0 0 20px rgba(255, 170, 0, 0.4);
    }
    50% {
        transform: translate(-50%, -50%) rotate(180deg) scale(1.5);
        border-color: rgba(255, 102, 0, 0.8);
        box-shadow: 0 0 40px rgba(255, 102, 0, 0.6);
    }
    100% {
        transform: translate(-50%, -50%) rotate(360deg) scale(2);
        border-color: rgba(255, 0, 0, 1);
        box-shadow: 0 0 60px rgba(255, 0, 0, 0.8);
    }
}

/* === 蓄力粒子环绕 === */
.hengzhan-charge-particle {
    width: 8px;
    height: 8px;
    background: radial-gradient(circle, #ffaa00 0%, #ff6600 50%, transparent 100%);
    border-radius: 50%;
    animation: hengzhan-particle-orbit 1.5s linear infinite;
}

@keyframes hengzhan-particle-orbit {
    0% {
        transform: translate(-50%, -50%) rotate(var(--orbitAngle)) translateX(var(--orbitRadius)) rotate(calc(-1 * var(--orbitAngle)));
        opacity: 1;
        box-shadow: 0 0 10px #ffaa00;
    }
    100% {
        transform: translate(-50%, -50%) rotate(calc(var(--orbitAngle) + 360deg)) translateX(calc(var(--orbitRadius) + 20px)) rotate(calc(-1 * (var(--orbitAngle) + 360deg)));
        opacity: 0;
        box-shadow: 0 0 20px #ff0000;
    }
}

/* === 蓄力冲击波 === */
.hengzhan-charge-shockwave {
    width: 60px;
    height: 60px;
    border: 2px solid rgba(255, 170, 0, 0.8);
    border-radius: 50%;
    animation: hengzhan-shockwave-expand 0.6s ease-out forwards;
}

@keyframes hengzhan-shockwave-expand {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
        border-color: rgba(255, 170, 0, 0.8);
    }
    100% {
        transform: translate(-50%, -50%) scale(3);
        opacity: 0;
        border-color: rgba(255, 0, 0, 0.3);
    }
}

/* === 第二阶段：直接击中动画 === */
@keyframes hengzhan-direct-strike {
    0% {
        transform: translate(-50%, -50%) rotate(1440deg) scale(2);
        left: var(--casterX);
        top: var(--casterY);
    }
    100% {
        transform: translate(-50%, -50%) rotate(var(--strikeAngle)) scale(1.5);
        left: var(--strikeX);
        top: var(--strikeY);
        -webkit-filter: drop-shadow(0 0 60px #ffaa00) drop-shadow(0 0 120px #ff6600) drop-shadow(0 0 180px #ff0000);
        filter: drop-shadow(0 0 60px #ffaa00) drop-shadow(0 0 120px #ff6600) drop-shadow(0 0 180px #ff0000);
    }
}

/* === 击中冲击效果 === */
.hengzhan-strike-impact {
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(255, 170, 0, 0.8) 0%, rgba(255, 102, 0, 0.6) 30%, rgba(255, 0, 0, 0.4) 60%, transparent 100%);
    border-radius: 50%;
    animation: hengzhan-impact-burst 0.4s ease-out forwards;
}

@keyframes hengzhan-impact-burst {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(3);
        opacity: 0;
    }
}

/* === 击中火花 === */
.hengzhan-strike-spark {
    width: 6px;
    height: 6px;
    background: #ffaa00;
    border-radius: 50%;
    animation: hengzhan-spark-fly 0.8s ease-out forwards;
}

@keyframes hengzhan-spark-fly {
    0% {
        transform: translate(-50%, -50%) rotate(var(--sparkAngle)) translateX(0);
        opacity: 1;
        box-shadow: 0 0 10px #ffaa00;
    }
    100% {
        transform: translate(-50%, -50%) rotate(var(--sparkAngle)) translateX(var(--sparkDistance));
        opacity: 0;
        box-shadow: 0 0 5px #ff6600;
    }
}

/* === 火焰斩切路线 === */
.hengzhan-flame-slash {
    width: var(--slashLength);
    height: 8px;
    background: linear-gradient(to right, 
        transparent 0%, 
        rgba(255, 170, 0, 0.8) 20%, 
        rgba(255, 102, 0, 1) 50%, 
        rgba(255, 0, 0, 0.8) 80%, 
        transparent 100%);
    border-radius: 4px;
    transform-origin: left center;
    animation: hengzhan-flame-slash-sweep 0.2s ease-out forwards;
    box-shadow: 
        0 0 10px rgba(255, 170, 0, 0.6),
        0 0 20px rgba(255, 102, 0, 0.4),
        0 0 30px rgba(255, 0, 0, 0.2);
}

@keyframes hengzhan-flame-slash-sweep {
    0% {
        transform: translateY(-50%) scaleX(0);
        opacity: 1;
    }
    100% {
        transform: translateY(-50%) scaleX(1);
        opacity: 0.8;
    }
}

/* === 火焰轨迹粒子 === */
.hengzhan-flame-particle {
    width: 12px;
    height: 12px;
    background: radial-gradient(circle, #ffaa00 0%, #ff6600 60%, transparent 100%);
    border-radius: 50%;
    animation: hengzhan-flame-particle-burst 0.3s ease-out forwards;
}

@keyframes hengzhan-flame-particle-burst {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
        box-shadow: 0 0 15px #ffaa00;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.2);
        opacity: 0.8;
        box-shadow: 0 0 25px #ff6600;
    }
    100% {
        transform: translate(-50%, -50%) scale(0.3);
        opacity: 0;
        box-shadow: 0 0 10px #ff0000;
    }
}

/* === 火焰斩切波 === */
.hengzhan-slash-wave {
    width: var(--waveLength);
    height: var(--waveWidth);
    background: linear-gradient(to right, 
        transparent 0%, 
        rgba(255, 170, 0, 0.6) 25%, 
        rgba(255, 102, 0, 0.8) 50%, 
        rgba(255, 0, 0, 0.6) 75%, 
        transparent 100%);
    border-radius: calc(var(--waveWidth) / 2);
    transform-origin: left center;
    animation: hengzhan-slash-wave-expand 0.25s ease-out forwards;
}

@keyframes hengzhan-slash-wave-expand {
    0% {
        transform: translateY(-50%) scaleX(0);
        opacity: 0.8;
    }
    50% {
        transform: translateY(-50%) scaleX(0.7);
        opacity: 0.6;
    }
    100% {
        transform: translateY(-50%) scaleX(1);
        opacity: 0;
    }
}

/* === 斩切冲击效果 === */
.hengzhan-slash-impact {
    width: 80px;
    height: 80px;
    background: radial-gradient(ellipse, 
        rgba(255, 170, 0, 0.9) 0%, 
        rgba(255, 102, 0, 0.7) 30%, 
        rgba(255, 0, 0, 0.5) 60%, 
        transparent 100%);
    border-radius: 50%;
    animation: hengzhan-slash-impact-pulse 0.3s ease-out forwards;
}

@keyframes hengzhan-slash-impact-pulse {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0.8;
    }
    100% {
        transform: translate(-50%, -50%) scale(2.5);
        opacity: 0;
    }
}

/* === 第三阶段：火焰爆炸效果（复用火球术样式） === */

/* 爆炸闪光 */
.explosion-flash {
    width: 30px;
    height: 30px;
    background: radial-gradient(circle, #ffffff 0%, #ffff00 30%, #ff6600 70%, transparent 100%);
    border-radius: 50%;
    animation: explosion-flash-burst 0.15s ease-out forwards;
}

@keyframes explosion-flash-burst {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(8);
        opacity: 0;
    }
}

/* 爆炸核心 */
.explosion-core {
    width: 60px;
    height: 60px;
    background: radial-gradient(circle, #ffff00 0%, #ff6600 40%, #ff0000 70%, #8B0000 100%);
    border-radius: 50%;
    animation: explosion-core-expand 0.6s ease-out forwards;
}

@keyframes explosion-core-expand {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
        box-shadow: 0 0 20px #ff6600, 0 0 40px #ff0000;
    }
    50% {
        transform: translate(-50%, -50%) scale(2);
        opacity: 0.9;
        box-shadow: 0 0 40px #ff6600, 0 0 80px #ff0000, 0 0 120px #8B0000;
    }
    100% {
        transform: translate(-50%, -50%) scale(3);
        opacity: 0;
        box-shadow: 0 0 60px #ff6600, 0 0 120px #ff0000, 0 0 180px #8B0000;
    }
}

/* 爆炸冲击波 */
.explosion-shockwave {
    width: 80px;
    height: 80px;
    border: 3px solid rgba(255, 102, 0, 0.8);
    border-radius: 50%;
    animation: explosion-shockwave-expand 0.8s ease-out forwards;
}

@keyframes explosion-shockwave-expand {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
        border-color: rgba(255, 102, 0, 0.8);
    }
    100% {
        transform: translate(-50%, -50%) scale(6);
        opacity: 0;
        border-color: rgba(139, 0, 0, 0.3);
    }
}

/* 火焰环 */
.explosion-fire-ring {
    width: 100px;
    height: 100px;
    border: 8px solid transparent;
    border-radius: 50%;
    background: conic-gradient(from 0deg, #ff6600, #ff0000, #ffff00, #ff6600, #8B0000, #ff6600);
    animation: explosion-fire-ring-spin 1.2s linear forwards;
}

@keyframes explosion-fire-ring-spin {
    0% {
        transform: translate(-50%, -50%) scale(0) rotate(0deg);
        opacity: 1;
        -webkit-filter: blur(0px);
        filter: blur(0px);
    }
    50% {
        transform: translate(-50%, -50%) scale(2) rotate(180deg);
        opacity: 0.8;
        -webkit-filter: blur(1px);
        filter: blur(1px);
    }
    100% {
        transform: translate(-50%, -50%) scale(4) rotate(360deg);
        opacity: 0;
        -webkit-filter: blur(3px);
        filter: blur(3px);
    }
}

/* 爆炸烟雾 */
.explosion-smoke {
    width: 40px;
    height: 40px;
    background: radial-gradient(circle, rgba(139, 69, 19, 0.6) 0%, rgba(105, 105, 105, 0.4) 50%, transparent 100%);
    border-radius: 50%;
    animation: explosion-smoke-rise 2s ease-out forwards;
}

@keyframes explosion-smoke-rise {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0.8;
        -webkit-filter: blur(0px);
        filter: blur(0px);
    }
    50% {
        transform: translate(-50%, -50%) scale(1.5) translateY(-30px);
        opacity: 0.6;
        -webkit-filter: blur(2px);
        filter: blur(2px);
    }
    100% {
        transform: translate(-50%, -50%) scale(3) translateY(-80px);
        opacity: 0;
        -webkit-filter: blur(5px);
        filter: blur(5px);
    }
}

/* 火焰粒子 */
.fire-particle {
    width: 8px;
    height: 8px;
    background: radial-gradient(circle, #ffff00 0%, #ff6600 50%, #ff0000 100%);
    border-radius: 50%;
}

@keyframes fire-particle {
    0% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
        box-shadow: 0 0 10px #ff6600;
    }
    50% {
        transform: translate(-50%, -50%) translate(calc(var(--moveX) * 0.7), calc(var(--moveY) * 0.7)) scale(0.8);
        opacity: 0.7;
        box-shadow: 0 0 15px #ff0000;
    }
    100% {
        transform: translate(-50%, -50%) translate(var(--moveX), var(--moveY)) scale(0.3);
        opacity: 0;
        box-shadow: 0 0 5px #8B0000;
    }
}

/* 热浪扭曲效果 */
.explosion-heat-distortion {
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, transparent 30%, rgba(255, 102, 0, 0.1) 50%, transparent 70%);
    border-radius: 50%;
    animation: explosion-heat-wave 1.5s ease-in-out forwards;
}

@keyframes explosion-heat-wave {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
        -webkit-filter: blur(0px);
        filter: blur(0px);
    }
    50% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0.6;
        -webkit-filter: blur(2px);
        filter: blur(2px);
    }
    100% {
        transform: translate(-50%, -50%) scale(3);
        opacity: 0;
        -webkit-filter: blur(5px);
        filter: blur(5px);
    }
}

/* === 敌人受击动画 === */
@keyframes fire-hit {
    0%, 100% { 
        -webkit-filter: none; 
        filter: none; 
        transform: scale(1);
    }
    10%, 30%, 50%, 70%, 90% { 
        -webkit-filter: brightness(1.5) hue-rotate(0deg) saturate(1.2) drop-shadow(0 0 20px rgba(255, 102, 0, 0.8));
        filter: brightness(1.5) hue-rotate(0deg) saturate(1.2) drop-shadow(0 0 20px rgba(255, 102, 0, 0.8));
        transform: scale(1.05);
    }
    20%, 40%, 60%, 80% { 
        -webkit-filter: brightness(1.8) hue-rotate(10deg) saturate(1.4) drop-shadow(0 0 30px rgba(255, 0, 0, 0.9));
        filter: brightness(1.8) hue-rotate(10deg) saturate(1.4) drop-shadow(0 0 30px rgba(255, 0, 0, 0.9));
        transform: scale(0.98);
    }
}

@keyframes fire-shake {
    0%, 100% { transform: translateX(0); }
    10% { transform: translateX(-3px); }
    20% { transform: translateX(3px); }
    30% { transform: translateX(-2px); }
    40% { transform: translateX(2px); }
    50% { transform: translateX(-1px); }
    60% { transform: translateX(1px); }
    70% { transform: translateX(-1px); }
    80% { transform: translateX(1px); }
    90% { transform: translateX(-0.5px); }
}

/* === 移动端适配 === */
@media (max-width: 768px) {
    .hengzhan-container {
        transform: scale(0.8);
        transform-origin: center center;
    }
    
    .hengzhan-main-sword,
    .hengzhan-sword-shadow {
        width: 50px;
        height: 100px;
    }
    
    .hengzhan-energy-field {
        width: 60px;
        height: 60px;
    }
    
    .hengzhan-charge-particle {
        width: 6px;
        height: 6px;
    }
    
    .hengzhan-flame-slash {
        height: 6px;
    }
    
    .hengzhan-flame-particle {
        width: 10px;
        height: 10px;
    }
}

/* === 硬件加速优化 === */
.hengzhan-main-sword,
.hengzhan-sword-shadow,
.hengzhan-energy-field,
.hengzhan-charge-particle,
.hengzhan-charge-shockwave,
.hengzhan-strike-impact,
.hengzhan-strike-spark,
.hengzhan-flame-slash,
.hengzhan-flame-particle,
.hengzhan-slash-wave,
.hengzhan-slash-impact,
.explosion-flash,
.explosion-core,
.explosion-shockwave,
.explosion-fire-ring,
.explosion-smoke,
 .fire-particle,
 .explosion-heat-distortion {
     will-change: transform, opacity;
     transform: translateZ(0);
 }