<?php

/**
 * 一念修仙项目安全性全面审计
 * 检查SQL注入、XSS、CSRF等安全漏洞
 */

require_once __DIR__ . '/setting.php';

echo "=== 一念修仙项目安全性审计 ===\n\n";

$securityIssues = [];
$securityWarnings = [];
$securityPassed = [];

// 1. SQL注入防护检查
echo "1. SQL注入防护检查:\n";
echo "----------------------------------------\n";

$apiFiles = glob('src/api/*.php');
$sqlInjectionVulnerable = [];
$sqlInjectionSafe = [];

foreach ($apiFiles as $file) {
    $content = file_get_contents($file);

    // 检查是否使用了不安全的SQL查询（直接拼接用户输入到SQL中）
    if (
        preg_match('/\$_(?:GET|POST|REQUEST)[^;]*\.\s*[\'"](?:SELECT|INSERT|UPDATE|DELETE)/i', $content) ||
        preg_match('/(?:SELECT|INSERT|UPDATE|DELETE)[^;]*\.\s*\$_(?:GET|POST|REQUEST)/i', $content)
    ) {
        $sqlInjectionVulnerable[] = basename($file);
    }

    // 检查是否使用了参数化查询
    if (preg_match('/prepare\s*\(\s*[\'"]/', $content) && preg_match('/execute\s*\(\s*\[/', $content)) {
        $sqlInjectionSafe[] = basename($file);
    }
}

if (count($sqlInjectionVulnerable) > 0) {
    $securityIssues[] = "发现可能的SQL注入漏洞: " . implode(', ', $sqlInjectionVulnerable);
    echo "   ❌ 发现可能的SQL注入漏洞:\n";
    foreach ($sqlInjectionVulnerable as $file) {
        echo "      • $file\n";
    }
} else {
    $securityPassed[] = "未发现明显的SQL注入漏洞";
    echo "   ✅ 未发现明显的SQL注入漏洞\n";
}

echo "   使用参数化查询的文件: " . count($sqlInjectionSafe) . " 个\n";

// 2. XSS防护检查
echo "\n2. XSS防护检查:\n";
echo "----------------------------------------\n";

$htmlFiles = glob('public/*.html');
$xssVulnerable = [];
$xssSafe = [];

foreach ($htmlFiles as $file) {
    $content = file_get_contents($file);

    // 检查是否直接输出用户输入
    if (preg_match('/innerHTML\s*=\s*[^;]*(?:\$_|data\.)/i', $content)) {
        $xssVulnerable[] = basename($file);
    }

    // 检查是否使用了安全的输出方法
    if (preg_match('/textContent\s*=|createTextNode/i', $content)) {
        $xssSafe[] = basename($file);
    }
}

if (count($xssVulnerable) > 0) {
    $securityWarnings[] = "发现可能的XSS风险: " . implode(', ', $xssVulnerable);
    echo "   ⚠️  发现可能的XSS风险:\n";
    foreach ($xssVulnerable as $file) {
        echo "      • $file\n";
    }
} else {
    $securityPassed[] = "未发现明显的XSS漏洞";
    echo "   ✅ 未发现明显的XSS漏洞\n";
}

// 3. CSRF防护检查
echo "\n3. CSRF防护检查:\n";
echo "----------------------------------------\n";

$csrfProtected = 0;
$csrfUnprotected = [];

foreach ($apiFiles as $file) {
    $content = file_get_contents($file);

    // 检查是否有CSRF令牌验证
    if (preg_match('/csrf|token/i', $content)) {
        $csrfProtected++;
    } else {
        // 检查是否是需要CSRF保护的POST请求处理文件
        if (preg_match('/\$_POST/i', $content)) {
            $csrfUnprotected[] = basename($file);
        }
    }
}

if (count($csrfUnprotected) > 0) {
    $securityWarnings[] = "部分POST接口可能缺少CSRF保护: " . implode(', ', $csrfUnprotected);
    echo "   ⚠️  部分POST接口可能缺少CSRF保护:\n";
    foreach ($csrfUnprotected as $file) {
        echo "      • $file\n";
    }
} else {
    $securityPassed[] = "CSRF防护配置良好";
    echo "   ✅ CSRF防护配置良好\n";
}

// 4. 会话安全检查
echo "\n4. 会话安全检查:\n";
echo "----------------------------------------\n";

// 检查会话配置
$sessionSecure = SESSION_SECURE;
$sessionHttpOnly = SESSION_HTTPONLY;
$sessionSameSite = SESSION_SAMESITE;

echo "   会话安全配置:\n";
echo "     Secure: " . ($sessionSecure ? '✅ 启用' : '⚠️  禁用') . "\n";
echo "     HttpOnly: " . ($sessionHttpOnly ? '✅ 启用' : '❌ 禁用') . "\n";
echo "     SameSite: $sessionSameSite\n";

if (!$sessionHttpOnly) {
    $securityIssues[] = "会话HttpOnly未启用，存在XSS风险";
}

if (!$sessionSecure) {
    $securityWarnings[] = "会话Secure未启用，HTTPS环境建议启用";
}

// 5. 文件上传安全检查
echo "\n5. 文件上传安全检查:\n";
echo "----------------------------------------\n";

$uploadVulnerable = [];
foreach ($apiFiles as $file) {
    $content = file_get_contents($file);

    // 检查文件上传处理
    if (preg_match('/\$_FILES/i', $content)) {
        // 检查是否有文件类型验证
        if (!preg_match('/mime|extension|type.*check/i', $content)) {
            $uploadVulnerable[] = basename($file);
        }
    }
}

if (count($uploadVulnerable) > 0) {
    $securityWarnings[] = "文件上传可能缺少类型验证: " . implode(', ', $uploadVulnerable);
    echo "   ⚠️  文件上传可能缺少类型验证:\n";
    foreach ($uploadVulnerable as $file) {
        echo "      • $file\n";
    }
} else {
    $securityPassed[] = "文件上传安全配置良好";
    echo "   ✅ 文件上传安全配置良好\n";
}

// 6. 敏感信息泄露检查
echo "\n6. 敏感信息泄露检查:\n";
echo "----------------------------------------\n";

$sensitiveFiles = [];
$allFiles = array_merge($apiFiles, $htmlFiles);

foreach ($allFiles as $file) {
    $content = file_get_contents($file);

    // 检查是否包含敏感信息
    if (preg_match('/password\s*=\s*[\'"][^\'"]+[\'"]|api_key\s*=\s*[\'"][^\'"]+[\'"]/i', $content)) {
        $sensitiveFiles[] = basename($file);
    }
}

if (count($sensitiveFiles) > 0) {
    $securityWarnings[] = "发现可能包含敏感信息的文件: " . implode(', ', $sensitiveFiles);
    echo "   ⚠️  发现可能包含敏感信息的文件:\n";
    foreach ($sensitiveFiles as $file) {
        echo "      • $file\n";
    }
} else {
    $securityPassed[] = "未发现敏感信息泄露";
    echo "   ✅ 未发现敏感信息泄露\n";
}

// 7. 输入验证检查
echo "\n7. 输入验证检查:\n";
echo "----------------------------------------\n";

$inputValidationGood = 0;
$inputValidationPoor = [];

foreach ($apiFiles as $file) {
    $content = file_get_contents($file);

    // 检查是否有输入验证
    if (preg_match('/filter_var|preg_match|strlen|is_numeric|ctype_/i', $content)) {
        $inputValidationGood++;
    } else {
        if (preg_match('/\$_(?:GET|POST|REQUEST)/i', $content)) {
            $inputValidationPoor[] = basename($file);
        }
    }
}

if (count($inputValidationPoor) > 0) {
    $securityWarnings[] = "部分接口可能缺少输入验证: " . implode(', ', $inputValidationPoor);
    echo "   ⚠️  部分接口可能缺少输入验证:\n";
    foreach ($inputValidationPoor as $file) {
        echo "      • $file\n";
    }
} else {
    $securityPassed[] = "输入验证配置良好";
    echo "   ✅ 输入验证配置良好\n";
}

// 8. 错误处理安全检查
echo "\n8. 错误处理安全检查:\n";
echo "----------------------------------------\n";

$errorHandlingSafe = true;
if (GAME_DEBUG === true) {
    $securityIssues[] = "调试模式仍然开启，可能泄露敏感信息";
    $errorHandlingSafe = false;
    echo "   ❌ 调试模式开启，可能泄露敏感信息\n";
} else {
    echo "   ✅ 调试模式已关闭\n";
}

$displayErrors = ini_get('display_errors');
if ($displayErrors == '1' || $displayErrors === true) {
    $securityIssues[] = "错误显示仍然开启，可能泄露系统信息";
    $errorHandlingSafe = false;
    echo "   ❌ 错误显示开启，可能泄露系统信息\n";
} else {
    echo "   ✅ 错误显示已关闭\n";
}

if ($errorHandlingSafe) {
    $securityPassed[] = "错误处理安全配置良好";
}

// 生成安全审计报告
echo "\n=== 安全审计总结 ===\n";
echo "安全检查通过: " . count($securityPassed) . " 项\n";
echo "安全警告: " . count($securityWarnings) . " 项\n";
echo "严重安全问题: " . count($securityIssues) . " 项\n\n";

if (count($securityIssues) > 0) {
    echo "❌ 严重安全问题 (必须修复):\n";
    foreach ($securityIssues as $issue) {
        echo "   • $issue\n";
    }
    echo "\n";
}

if (count($securityWarnings) > 0) {
    echo "⚠️  安全警告 (建议修复):\n";
    foreach ($securityWarnings as $warning) {
        echo "   • $warning\n";
    }
    echo "\n";
}

// 安全评级
$totalChecks = count($securityPassed) + count($securityWarnings) + count($securityIssues);
$securityScore = (count($securityPassed) / $totalChecks) * 100;

echo "🛡️  安全评级: ";
if ($securityScore >= 90) {
    echo "优秀 ({$securityScore}%)\n";
} elseif ($securityScore >= 80) {
    echo "良好 ({$securityScore}%)\n";
} elseif ($securityScore >= 70) {
    echo "一般 ({$securityScore}%)\n";
} else {
    echo "需要改进 ({$securityScore}%)\n";
}

echo "\n建议下一步操作:\n";
if (count($securityIssues) > 0) {
    echo "1. 立即修复所有严重安全问题\n";
    echo "2. 重新进行安全审计\n";
} else {
    echo "1. 修复安全警告项以提升安全性\n";
    echo "2. 进行渗透测试验证\n";
    echo "3. 建立安全监控机制\n";
}
