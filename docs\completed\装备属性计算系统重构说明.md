# 装备属性计算系统重构说明

## 📋 重构背景

之前装备属性计算逻辑分散在多个文件中，存在以下问题：
1. **代码重复**：`calculateEquipmentStatsUnified`和`calculateWeaponStatsUnified`函数在多个文件中重复定义
2. **维护困难**：修改属性计算逻辑需要同时修改多个文件
3. **逻辑不一致**：不同文件中的计算逻辑可能存在差异
4. **测试困难**：分散的代码难以进行统一测试

## 🔧 重构方案

### 新增统一管理器
创建 `src/includes/equipment_stats_manager.php` 作为装备属性计算的统一管理器。

### 核心类：EquipmentStatsManager

#### 主要方法：

1. **getEquipmentStats($pdo, $characterId)**
   - 获取角色的装备属性加成（不包含武器）
   - 返回完整的装备属性数组

2. **getWeaponStats($pdo, $characterId)**
   - 获取角色的武器属性加成
   - 返回武器属性数组

3. **getAllEquipmentStats($pdo, $characterId)**
   - 获取角色的所有装备属性加成（装备+武器）
   - 返回合并后的属性数组

4. **getEquipmentBonusCompatible($pdo, $characterId)**
   - 获取兼容格式的装备加成（用于旧版API兼容）
   - 包含字段名映射（如accuracy_bonus → hit_rate）

5. **getUserStatsFormat($pdo, $characterId)**
   - 获取用户统计格式的装备属性（用于前端显示）
   - 分别返回装备和武器属性

### 核心特性

#### 🔧 真实属性优先
```php
// 优先使用user_inventories表中的calculated_attributes
if (!empty($item['inventory_custom_attributes'])) {
    $customAttributes = json_decode($item['inventory_custom_attributes'], true);
    if (isset($customAttributes['calculated_attributes'])) {
        $actualAttributes = $customAttributes['calculated_attributes'];
        // 使用真实属性
    }
}

// 向后兼容：如果没有真实属性，使用基础模板属性
if (empty($actualAttributes)) {
    $actualAttributes = [
        'physical_attack' => intval($item['physical_attack']),
        // ... 其他基础属性
    ];
}
```

#### 🔗 统一数据库查询
```sql
SELECT
    gi.item_name,
    gi.hp_bonus, gi.mp_bonus, gi.speed_bonus,
    gi.physical_attack, gi.immortal_attack, gi.physical_defense, gi.immortal_defense,
    gi.critical_rate, gi.critical_damage, gi.accuracy_bonus, gi.dodge_bonus, gi.block_bonus,
    ui.custom_attributes as inventory_custom_attributes
FROM character_equipment ce
JOIN game_items gi ON ce.item_id = gi.id
LEFT JOIN user_inventories ui ON ce.inventory_item_id = ui.id
WHERE ce.character_id = ? AND gi.item_type != 'weapon' AND ce.item_id > 0
```

#### 📊 完整属性支持
支持所有装备属性：
- 攻击属性：physical_attack, immortal_attack
- 防御属性：physical_defense, immortal_defense
- 生命魔法：hp_bonus, mp_bonus
- 速度属性：speed_bonus
- 战斗属性：critical_rate, critical_damage, accuracy_bonus, dodge_bonus, block_bonus

## 📁 修改的文件

### 1. 新增文件
- `src/includes/equipment_stats_manager.php` - 统一装备属性管理器

### 2. 修改的文件

#### src/api/cultivation.php
```php
// 修改前：150行复杂的getEquipmentBonus函数
function getEquipmentBonus($pdo, $characterId) {
    // 150行重复代码...
}

// 修改后：3行简洁调用
require_once __DIR__ . '/../includes/equipment_stats_manager.php';

function getEquipmentBonus($pdo, $characterId) {
    return EquipmentStatsManager::getEquipmentBonusCompatible($pdo, $characterId);
}
```

#### src/api/equipment_integrated.php
```php
// 修改前：分别调用两个函数
$equipmentStats = calculateEquipmentStatsUnified($userId, $pdo);
$weaponStats = calculateWeaponStatsUnified($userId, $pdo);

// 修改后：统一管理器调用
require_once __DIR__ . '/../includes/equipment_stats_manager.php';
$equipmentStats = EquipmentStatsManager::getEquipmentStats($pdo, $characterId);
$weaponStats = EquipmentStatsManager::getWeaponStats($pdo, $characterId);

// 删除了重复的函数定义（约200行代码）
```

#### src/api/user_info.php
```php
// 修改前：直接调用分散函数
$equipmentStats = calculateEquipmentStatsUnified($userId, $pdo);
$weaponStats = calculateWeaponStatsUnified($userId, $pdo);

// 修改后：统一管理器调用
require_once __DIR__ . '/../includes/equipment_stats_manager.php';
$equipmentStats = EquipmentStatsManager::getEquipmentStats($pdo, $characterId);
$weaponStats = EquipmentStatsManager::getWeaponStats($pdo, $characterId);
```

## 🎯 重构效果

### 代码减少
- **删除重复代码**：约400行重复的装备属性计算代码
- **统一逻辑**：所有装备属性计算都使用同一套逻辑
- **简化调用**：从复杂的函数调用简化为统一的类方法调用

### 维护性提升
- **单一职责**：装备属性计算逻辑集中在一个文件中
- **易于测试**：可以独立测试装备属性计算逻辑
- **易于扩展**：新增属性类型只需修改一个地方

### 兼容性保证
- **向后兼容**：保留了所有原有的全局函数
- **数据兼容**：优先使用真实属性，向后兼容基础属性
- **接口兼容**：返回数据格式与原来完全一致

## 🔍 使用示例

### 基础使用
```php
require_once __DIR__ . '/../includes/equipment_stats_manager.php';

// 获取装备属性（不含武器）
$equipmentStats = EquipmentStatsManager::getEquipmentStats($pdo, $characterId);

// 获取武器属性
$weaponStats = EquipmentStatsManager::getWeaponStats($pdo, $characterId);

// 获取所有装备属性
$allStats = EquipmentStatsManager::getAllEquipmentStats($pdo, $characterId);
```

### 兼容性调用
```php
// 旧版API兼容格式
$bonus = EquipmentStatsManager::getEquipmentBonusCompatible($pdo, $characterId);
// 返回格式包含hit_rate, dodge_rate等兼容字段名

// 用户统计格式
$userStats = EquipmentStatsManager::getUserStatsFormat($pdo, $characterId);
// 返回 ['equipment_stats' => ..., 'weapon_stats' => ...]
```

### 向后兼容函数
```php
// 这些全局函数仍然可用，内部调用统一管理器
$equipmentStats = calculateEquipmentStatsUnified($characterId, $pdo);
$weaponStats = calculateWeaponStatsUnified($characterId, $pdo);
$bonus = getEquipmentBonusUnified($pdo, $characterId);
```

## 🚀 性能优化

### 查询优化
- **单次查询**：一次SQL查询获取所有需要的数据
- **LEFT JOIN**：高效关联user_inventories表获取真实属性
- **条件优化**：精确的WHERE条件减少无效数据

### 内存优化
- **按需计算**：只计算需要的属性类型
- **数据复用**：避免重复的数据库查询
- **及时释放**：计算完成后及时释放临时变量

## 📋 测试建议

### 功能测试
1. **装备属性计算**：验证装备属性计算的准确性
2. **武器属性计算**：验证武器属性计算的准确性
3. **真实属性优先**：验证优先使用calculated_attributes
4. **向后兼容**：验证基础属性的兼容性

### 性能测试
1. **查询性能**：对比重构前后的查询时间
2. **内存使用**：监控内存使用情况
3. **并发测试**：验证高并发下的稳定性

### 兼容性测试
1. **API兼容**：验证所有API返回格式不变
2. **前端兼容**：验证前端显示正常
3. **数据兼容**：验证新旧数据都能正确处理

## 📝 注意事项

### 开发注意
1. **引入文件**：使用前必须`require_once`管理器文件
2. **参数顺序**：注意$pdo和$characterId的参数顺序
3. **错误处理**：管理器内部已包含完整的错误处理

### 维护注意
1. **统一修改**：装备属性相关修改只需修改管理器文件
2. **测试覆盖**：修改后需要测试所有使用装备属性的功能
3. **文档更新**：重大修改需要更新相关文档

---

**重构完成日期**：2024年12月19日  
**重构范围**：装备属性计算系统全面重构  
**代码减少**：约400行重复代码  
**维护性**：显著提升  
**兼容性**：完全向后兼容 