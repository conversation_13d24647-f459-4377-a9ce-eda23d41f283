<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <!-- 移动端适配核心meta标签 -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover">
    
    <!-- 🔧 PWA模式底部白边强力修复 - 必须最早执行 -->
    <script src="assets/js/pwa-fix.js"></script>
    
    <!-- 🎛️ 全局调试开关 - 必须最早加载 -->
    <script src="assets/js/global-debug-switch.js"></script>

    <!-- 🔧 项目配置文件 - 必须早期加载 -->
    <script src="assets/js/config.js"></script>

    <!-- WebView优化配置 -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="screen-orientation" content="portrait">
    <meta name="x5-orientation" content="portrait">
    <meta name="full-screen" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta name="browsermode" content="application">
    <meta name="x5-page-mode" content="app">
    <!-- 禁用长按菜单 -->
    <meta name="format-detection" content="telephone=no,email=no,address=no">
    <!-- 强制使用最新版本 -->
    <meta http-equiv="Cache-Control" content="no-siteapp">
    <meta http-equiv="Cache-Control" content="no-transform">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <!-- 启用硬件加速 -->
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">

    <!-- PWA支持 -->
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#d4af37">

    <title>升仙大会 - 论道</title>
    <!-- 🔧 引入全局样式文件 -->
    <link rel="stylesheet" href="assets/css/global.css">
    <link rel="stylesheet" href="assets/css/immortal_arena.css">
    
    <!-- 🔧 视口高度修复脚本 -->
    <script src="assets/js/viewport-height-fix.js"></script>
    
    <!-- 引入通用导航样式 -->
    <link rel="stylesheet" href="assets/css/common-navigation.css">
    
    <!-- 🔑 全局登录检查系统 -->
    <script src="assets/js/auth-check.js"></script>
    <!-- 🎵 全局音乐管理器 -->
    <script src="assets/js/global-music-manager.js"></script>
</head>
<body class="arena-page">
    <div class="main-container">
        <!-- 顶部标题栏 -->
        <div class="header">
            <div class="header-title">
                <span>🏆</span>
                升仙大会·论道
            </div>
            <a href="game.html" class="back-btn">
                <span>🔙</span>
                返回
            </a>
        </div>

        <!-- 角色信息区域 -->
        <div class="character-info-section">
            <div class="character-card">
                <div class="character-avatar" id="characterAvatar">
                    <div class="character-avatar-content" id="characterAvatarContent"></div>
                </div>
                <div class="character-details">
                    <div class="character-name" id="characterName">加载中...</div>
                    <div class="character-realm" id="characterRealm">开光期</div>
                    <div class="character-power">道行值: <span id="characterPower">1000</span></div>
                </div>
            </div>
            <div class="rank-info">
                <div class="current-rank" id="currentRank">
                    <div class="rank-icon">🥉</div>
                    <div class="rank-text">练气期</div>
                </div>
                <div class="rank-progress">
                    <div class="progress-text" id="rankProgress">3胜/5战</div>
                    <div class="rank-points">
                        <span class="points-text">积分: <span id="rankPoints">0</span></span>
                        <span class="points-needed" id="pointsNeeded">下一段位需要50积分</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 今日战绩区域 -->
        <div class="arena-summary-section">
            <div class="daily-stats">
                <div class="stats-title">今日战绩</div>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value" id="todayWins">0</div>
                        <div class="stat-label">胜场</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="todayLoses">0</div>
                        <div class="stat-label">败场</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="todayRewards">0</div>
                        <div class="stat-label">灵石</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="remainingAttempts">10</div>
                        <div class="stat-label">剩余次数</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能按钮网格 -->
        <div class="function-grid">
            <button class="function-btn" onclick="openRecordsModal()">
                <div class="function-icon">📋</div>
                <div class="function-text">战绩</div>
            </button>
            <button class="function-btn" onclick="openRankingsModal()">
                <div class="function-icon">🏆</div>
                <div class="function-text">排行榜</div>
            </button>
            <button class="function-btn" onclick="openRulesModal()">
                <div class="function-icon">❓</div>
                <div class="function-text">规则</div>
            </button>
        </div>

        <!-- 主要操作区域 -->
        <div class="arena-actions">
            <!-- 匹配按钮区域 -->
            <div class="match-section">
                <div class="match-status" id="matchStatus">点击开始匹配</div>
                <button class="match-btn" id="matchBtn" onclick="startMatching()">
                    <div class="match-btn-content">
                        <div class="match-icon">⚔️</div>
                        <div class="match-text">寻找对手</div>
                    </div>
                </button>                
            </div>
        </div>
    </div>



    <!-- 战绩弹窗 -->
    <div class="modal-overlay" id="recordsModal">
        <div class="modal-content records-modal">
            <div class="modal-header">
                <div class="modal-title">📋 战斗记录</div>
                <button class="modal-close" onclick="closeRecordsModal()">×</button>
            </div>
            <div class="modal-body">
                <div class="records-list" id="recordsList">
                    <div class="loading">加载中...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 排行榜弹窗 -->
    <div class="modal-overlay" id="rankingsModal">
        <div class="modal-content rankings-modal">
            <div class="modal-header">
                <div class="modal-title">🏆 排行榜</div>
                <button class="modal-close" onclick="closeRankingsModal()">×</button>
            </div>
            <div class="modal-body">
                <div class="rankings-list" id="rankingsList">
                    <div class="loading">加载中...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 规则说明弹窗 -->
    <div class="modal-overlay" id="rulesModal">
        <div class="modal-content rules-modal">
            <div class="modal-header">
                <div class="modal-title">❓ 论道规则</div>
                <button class="modal-close" onclick="closeRulesModal()">×</button>
            </div>
            <div class="modal-body">
                <div class="rules-content">
                    <div class="rule-section">
                        <h4>⚔️ 匹配规则</h4>
                        <ul>
                            <li>系统将在15秒内为您匹配合适的对手</li>
                            <li>匹配基于道行值范围，确保公平竞技</li>
                            <li>若无合适对手，将匹配灵智傀儡</li>
                        </ul>
                    </div>
                    <div class="rule-section">
                        <h4>🏆 段位系统</h4>
                        <ul>
                            <li>共10个段位：叩道境 → 归墟境</li>
                            <li>胜负记录影响段位晋升</li>
                            <li>段位越高，奖励越丰厚</li>
                        </ul>
                    </div>
                    <div class="rule-section">
                        <h4>💰 奖励机制</h4>
                        <ul>
                            <li>胜利获得灵石奖励</li>
                            <li>连胜有额外奖励加成</li>
                            <li>段位越高奖励倍数越大</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 匹配结果弹窗 -->
    <div class="modal-overlay" id="matchResultModal">
        <div class="modal-content match-result-modal">
            <div class="modal-header">
                <div class="modal-title">⚔️ 找到对手</div>
            </div>
            <div class="modal-body">
                <div class="opponent-info" id="opponentInfo">
                    <!-- 对手信息将通过JavaScript填充 -->
                </div>
                <div class="battle-countdown" id="battleCountdown">
                    <div class="countdown-text">3秒后开始战斗</div>
                    <div class="countdown-number" id="countdownNumber">3</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入境界系统 -->
    <script src="assets/js/realm-system.js"></script>
    
    <!-- 底部导航栏会由 common-navigation.js 自动插入 -->
    <script src="assets/js/common-navigation.js"></script>
    <script src="assets/js/immortal-arena.js"></script>

</body>
</html> 