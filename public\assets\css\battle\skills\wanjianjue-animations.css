/**
 * 万剑诀技能动画样式
 * 对应 animation_model = 'wanjianjue'
 */

/* 🌟 万剑诀动画样式 */
.skill-sword {
    position: absolute;
    width: min(40px, 8vw);
    height: min(80px, 16vw);
    transform: translate(-50%, -50%);
    transform-origin: center;
    opacity: 0;
    -webkit-filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.8));
    filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.8));
    pointer-events: none;
    z-index: 200;
    
    /* 支持背景图片显示 */
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

/* 万剑诀武器图片样式 */
.skill-sword .weapon-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    -webkit-filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.8));
    filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.8));
}

/* 万剑诀剑生成动画 - 修改为直接平移，剑尖朝上 */
@keyframes skill-sword-appear {
    0% {
        transform-origin: center 25%;
        transform: translate(
            calc(var(--initialX) - 50%),
            calc(var(--initialY) - 50%)
        ) scale(0) rotate(180deg); /* 旋转180度让剑尖朝上 */
        opacity: 0;
    }
    100% {
        transform-origin: center 25%;
        transform: translate(
            calc(var(--initialX) + cos(var(--swordAngle)) * var(--swordRadius) - 50%),
            calc(var(--initialY) + sin(var(--swordAngle)) * var(--swordRadius) - 50%)
        ) scale(1) rotate(180deg); /* 最终位置，剑尖朝上 */
        opacity: 1;
    }
}

/* 万剑诀剑攻击动画 - 修改为保持剑尖朝上 */
@keyframes skill-sword-attack {
    0% {
        transform-origin: center 25%;
        transform: translate(
            calc(var(--initialX) + cos(var(--swordAngle)) * var(--swordRadius) - 50%),
            calc(var(--initialY) + sin(var(--swordAngle)) * var(--swordRadius) - 50%)
        ) scale(1) rotate(180deg); /* 旋转180度让剑尖朝上 */
        opacity: 1;
    }
    /* 35%时间飞行25%距离 - 慢速启动 */
    35% {
        transform-origin: center 25%;
        transform: translate(
            calc(var(--initialX) + cos(var(--swordAngle)) * var(--swordRadius) + (var(--targetX) - var(--initialX) - cos(var(--swordAngle)) * var(--swordRadius)) * 0.25 - 50%),
            calc(var(--initialY) + sin(var(--swordAngle)) * var(--swordRadius) + (var(--attackTargetY) - var(--initialY) - sin(var(--swordAngle)) * var(--swordRadius)) * 0.25 - 50%)
        ) scale(1) rotate(180deg); /* 保持剑尖朝上 */
        opacity: 1;
    }
    /* 接下来35%时间飞行45%距离 - 中等速度 */
    70% {
        transform-origin: center 25%;
        transform: translate(
            calc(var(--initialX) + cos(var(--swordAngle)) * var(--swordRadius) + (var(--targetX) - var(--initialX) - cos(var(--swordAngle)) * var(--swordRadius)) * 0.7 - 50%),
            calc(var(--initialY) + sin(var(--swordAngle)) * var(--swordRadius) + (var(--attackTargetY) - var(--initialY) - sin(var(--swordAngle)) * var(--swordRadius)) * 0.7 - 50%)
        ) scale(1) rotate(180deg); /* 保持剑尖朝上 */
        opacity: 1;
    }
    /* 最后30%时间飞行剩余30%距离 - 高速冲刺 */
    100% {
        transform-origin: center 25%;
        transform: translate(calc(var(--targetX) - 50%), calc(var(--attackTargetY) - 50%)) rotate(180deg); /* 保持剑尖朝上 */
        opacity: 0; /* 对标原版：动画结尾opacity为0 */
    }
}

/* 万剑诀剑穿透动画 - 修改为保持剑尖朝上 */
@keyframes skill-sword-penetrate {
    0% {
        transform-origin: center 25%;
        transform: translate(calc(var(--targetX) - 50%), calc(var(--attackTargetY) - 50%)) rotate(180deg); /* 旋转180度让剑尖朝上 */
        opacity: 1;
    }
    100% {
        transform-origin: center 25%;
        transform: translate(calc(var(--targetX) - 50%), calc(var(--finalTargetY) - 50%)) rotate(180deg); /* 保持剑尖朝上 */
        opacity: 0;
    }
}

/* 🌟 万剑诀特效样式 */
.sword-formation-container {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 190;
}

.sword-formation-energy {
    position: absolute;
    width: 120px;
    height: 120px;
    background: radial-gradient(circle,
        rgba(255, 255, 255, 0.3) 0%,
        rgba(200, 220, 255, 0.2) 30%,
        rgba(150, 180, 255, 0.1) 60%,
        transparent 100%
    );
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: formation-energy 1.2s ease-out forwards;
    -webkit-filter: blur(2px);
    filter: blur(2px);
}

@keyframes formation-energy {
    0% {
        transform: translate(-50%, -50%) scale(0) rotate(0deg);
        opacity: 0;
    }
    30% {
        transform: translate(-50%, -50%) scale(1) rotate(120deg);
        opacity: 0.6;
    }
    60% {
        transform: translate(-50%, -50%) scale(1.5) rotate(240deg);
        opacity: 0.4;
    }
    100% {
        transform: translate(-50%, -50%) scale(2) rotate(360deg);
        opacity: 0;
    }
}

.sword-formation-circle {
    position: absolute;
    border: 2px solid rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: formation-circle 1.2s ease-out forwards;
    -webkit-filter: drop-shadow(0 0 10px rgba(200, 220, 255, 0.8));
    filter: drop-shadow(0 0 10px rgba(200, 220, 255, 0.8));
}

@keyframes formation-circle {
    0% {
        transform: translate(-50%, -50%) scale(0) rotate(0deg);
        opacity: 0;
        border-color: rgba(255, 255, 255, 0.8);
    }
    30% {
        transform: translate(-50%, -50%) scale(1.2) rotate(180deg);
        opacity: 0.8;
        border-color: rgba(200, 220, 255, 0.7);
    }
    60% {
        transform: translate(-50%, -50%) scale(1.5) rotate(360deg);
        opacity: 0.6;
        border-color: rgba(150, 180, 255, 0.5);
    }
    100% {
        transform: translate(-50%, -50%) scale(2) rotate(540deg);
        opacity: 0;
        border-color: rgba(100, 150, 255, 0.2);
    }
}

.sword-formation-rune {
    position: absolute;
    width: 8px;
    height: 8px;
    background: radial-gradient(circle, rgba(255, 255, 255, 1) 0%, rgba(200, 220, 255, 0.8) 100%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: formation-rune 1.2s ease-out forwards;
    -webkit-filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.8));
    filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.8));
}

@keyframes formation-rune {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0;
    }
    20% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0.8;
    }
    40% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
    70% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0;
    }
}

/* 移动端适配 */
@media (max-width: 480px) {
    .skill-sword {
        width: min(32px, 6vw);
        height: min(64px, 12vw);
    }
    
    .sword-formation-energy {
        width: 96px;
        height: 96px;
    }
}

/* 🎯 击中特效样式 */
.sword-hit-flash {
    position: absolute;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: radial-gradient(circle,
        rgba(255, 255, 255, 1) 0%,
        rgba(200, 220, 255, 0.8) 30%,
        rgba(150, 180, 255, 0.4) 60%,
        transparent 100%
    );
    transform: translate(-50%, -50%);
    animation: sword-hit-flash-expand 0.3s ease-out forwards;
    -webkit-filter: blur(1px);
    filter: blur(1px);
    pointer-events: none;
    z-index: 300;
}

@keyframes sword-hit-flash-expand {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
    }
    50% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.8;
    }
    100% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0;
    }
}

.sword-hit-particle {
    position: absolute;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255, 255, 255, 1) 0%, rgba(200, 220, 255, 0.8) 100%);
    animation: sword-hit-particle-scatter 0.6s ease-out forwards;
    -webkit-filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.8));
    filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.8));
    pointer-events: none;
    z-index: 295;
}

@keyframes sword-hit-particle-scatter {
    0% {
        transform: translate(0, 0) scale(1);
        opacity: 1;
    }
    100% {
        transform: translate(var(--particleX), var(--particleY)) scale(0);
        opacity: 0;
    }
}

.sword-hit-shockwave {
    position: absolute;
    width: 80px;
    height: 80px;
    border: 2px solid rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: sword-hit-shockwave-expand 0.5s ease-out forwards;
    -webkit-filter: drop-shadow(0 0 10px rgba(200, 220, 255, 0.6));
    filter: drop-shadow(0 0 10px rgba(200, 220, 255, 0.6));
    pointer-events: none;
    z-index: 290;
}

@keyframes sword-hit-shockwave-expand {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
        border-color: rgba(255, 255, 255, 0.9);
    }
    50% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0.6;
        border-color: rgba(200, 220, 255, 0.7);
    }
    100% {
        transform: translate(-50%, -50%) scale(3);
        opacity: 0;
        border-color: rgba(150, 180, 255, 0.3);
    }
}

/* 🎭 角色受击动画 */
@keyframes sword-struck {
    0%, 100% {
        transform: translateX(0) translateY(0);
        -webkit-filter: brightness(1);
        filter: brightness(1);
    }
    12% {
        transform: translateX(-5px) translateY(-3px);
        -webkit-filter: brightness(1.8) saturate(1.5);
        filter: brightness(1.8) saturate(1.5);
    }
    25% {
        transform: translateX(4px) translateY(2px);
        -webkit-filter: brightness(1.4) saturate(1.2);
        filter: brightness(1.4) saturate(1.2);
    }
    37% {
        transform: translateX(-4px) translateY(4px);
        -webkit-filter: brightness(2.0) saturate(1.8);
        filter: brightness(2.0) saturate(1.8);
    }
    50% {
        transform: translateX(5px) translateY(-2px);
        -webkit-filter: brightness(1.6) saturate(1.4);
        filter: brightness(1.6) saturate(1.4);
    }
    62% {
        transform: translateX(-3px) translateY(-5px);
        -webkit-filter: brightness(1.9) saturate(1.7);
        filter: brightness(1.9) saturate(1.7);
    }
    75% {
        transform: translateX(3px) translateY(3px);
        -webkit-filter: brightness(1.3) saturate(1.1);
        filter: brightness(1.3) saturate(1.1);
    }
    87% {
        transform: translateX(-2px) translateY(1px);
        -webkit-filter: brightness(1.5) saturate(1.3);
        filter: brightness(1.5) saturate(1.3);
    }
}

@keyframes sword-hit-shake {
    0%, 100% {
        transform: translateX(0) translateY(0);
        -webkit-filter: brightness(1);
        filter: brightness(1);
    }
    10% {
        transform: translateX(-4px) translateY(-3px);
        -webkit-filter: brightness(1.5);
        filter: brightness(1.5);
    }
    20% {
        transform: translateX(3px) translateY(2px);
        -webkit-filter: brightness(1.2);
        filter: brightness(1.2);
    }
    30% {
        transform: translateX(-3px) translateY(4px);
        -webkit-filter: brightness(1.8);
        filter: brightness(1.8);
    }
    40% {
        transform: translateX(4px) translateY(-2px);
        -webkit-filter: brightness(1.3);
        filter: brightness(1.3);
    }
    50% {
        transform: translateX(-2px) translateY(-4px);
        -webkit-filter: brightness(1.6);
        filter: brightness(1.6);
    }
    60% {
        transform: translateX(3px) translateY(3px);
        -webkit-filter: brightness(1.2);
        filter: brightness(1.2);
    }
    70% {
        transform: translateX(-1px) translateY(1px);
        -webkit-filter: brightness(1.4);
        filter: brightness(1.4);
    }
    80% {
        transform: translateX(1px) translateY(-1px);
        -webkit-filter: brightness(1.1);
        filter: brightness(1.1);
    }
    90% {
        transform: translateX(-1px) translateY(0px);
        -webkit-filter: brightness(1.1);
        filter: brightness(1.1);
    }
} 