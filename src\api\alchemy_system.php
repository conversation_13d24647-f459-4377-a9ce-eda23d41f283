<?php
/**
 * 炼丹系统API
 * 处理炼丹相关的所有操作
 */

// 错误处理设置
ini_set('display_errors', 0);
error_reporting(E_ALL);

// 设置JSON响应头
header('Content-Type: application/json; charset=utf-8');

try {
    // 引入全局配置和函数库
    require_once __DIR__ . '/../includes/functions.php';

    // 检查维护模式
    if (function_exists('isMaintenanceMode') && isMaintenanceMode()) {
        echo json_encode([
            'success' => false,
            'message' => '游戏正在维护中，请稍后再试',
            'maintenance' => true
        ]);
        exit;
    }

    // 记录API调用（如果开启了调试）
    if (defined('DEBUG_LOG_API_CALLS') && DEBUG_LOG_API_CALLS && function_exists('writeLog')) {
        writeLog("API调用: alchemy_system.php", 'DEBUG', 'api.log');
    }

    // 处理OPTIONS请求
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        exit(0);
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => '系统初始化失败: ' . $e->getMessage()
    ]);
    exit;
}

try {
    // 获取数据库连接
    if (function_exists('getDatabaseConnection')) {
        $db = getDatabase();
    } else {
        // 向后兼容
        require_once __DIR__ . '/../config/database.php';
        $db = getDatabase();
    }

    if (!$db) {
        if (function_exists('writeLog')) {
            writeLog("炼丹系统: 数据库连接失败", 'ERROR', 'database.log');
        }
        throw new Exception('数据库连接失败');
    }
    
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    
    switch ($action) {
        case 'get_furnaces':
            getFurnaces($db);
            break;
            
        case 'get_materials':
            getMaterials($db);
            break;
            
        case 'get_recipes':
            getRecipes($db);
            break;
            
        case 'get_character':
            getCharacter($db);
            break;
            
        case 'start_crafting':
            startCrafting($db);
            break;
            
        case 'learn_recipe':
            learnRecipe($db);
            break;
            
        case 'get_available_recipes':
            getAvailableRecipes($db);
            break;
            
        case 'get_user_furnaces':
            getUserFurnaces($db);
            break;
            
        default:
            throw new Exception('无效的操作');
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'error_code' => 'SYSTEM_ERROR'
    ]);
}

/**
 * 获取当前用户ID的辅助函数
 * @return int|null 用户ID或null
 */
function getCurrentUserId() {
    // 启动会话（如果还没有启动）
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }

    return $_SESSION['user_id'] ?? null;
}

// 通用材料解析函数 - 简化版（所有配方都是键值对格式）
function parseMaterials($materialsJson, $db) {
    if (empty($materialsJson)) {
        return [];
    }
    
    $materials = json_decode($materialsJson, true);
    if (!$materials || !is_array($materials)) {
        return [];
    }
    
    $materialList = [];
    
    // 现在只有一种格式：{"materialId": quantity}
    foreach ($materials as $materialId => $quantity) {
        // 获取材料名称
        $materialSql = "SELECT item_name FROM game_items WHERE id = ?";
        $materialStmt = $db->prepare($materialSql);
        $materialStmt->execute([$materialId]);
        $materialData = $materialStmt->fetch(PDO::FETCH_ASSOC);
        
        if ($materialData) {
            $materialList[] = [
                'id' => (int)$materialId,
                'name' => $materialData['item_name'],
                'quantity' => (int)$quantity,
                'display' => $materialData['item_name'] . " x" . $quantity
            ];
        }
    }
    
    return $materialList;
}

// 获取丹炉列表
function getFurnaces($db) {
    try {
        $sql = "SELECT id, item_name as name, sell_price as price, 
                       0 as success_bonus, 0 as output_bonus, 
                       CONCAT('需要', realm_requirement, '级') as unlock_requirement, 
                       description
                FROM game_items 
                WHERE item_name LIKE '%丹炉%' AND is_active = 1
                ORDER BY sell_price ASC";
        
        $stmt = $db->prepare($sql);
        $stmt->execute();
        
        $furnaces = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $bonus = 0;
            $multiple_output_chance = 0;
            $max_output_multiplier = 1;
            $special_effect = "无特效";
            
            if (strpos($row['name'], '下品') !== false) {
                $bonus = 5;
                $multiple_output_chance = 10;
                $max_output_multiplier = 2;
                $special_effect = "10%几率产出2个丹药";
            } elseif (strpos($row['name'], '中品') !== false) {
                $bonus = 10;
                $multiple_output_chance = 15;
                $max_output_multiplier = 3;
                $special_effect = "15%几率产出2-3个丹药";
            } elseif (strpos($row['name'], '上品') !== false) {
                $bonus = 15;
                $multiple_output_chance = 20;
                $max_output_multiplier = 4;
                $special_effect = "20%几率产出2-4个丹药";
            } elseif (strpos($row['name'], '极品') !== false) {
                $bonus = 20;
                $multiple_output_chance = 25;
                $max_output_multiplier = 5;
                $special_effect = "25%几率产出2-5个丹药";
            } elseif (strpos($row['name'], '神品') !== false) {
                $bonus = 30;
                $multiple_output_chance = 35;
                $max_output_multiplier = 6;
                $special_effect = "35%几率产出2-6个丹药";
            }
            
            $furnaces[] = [
                'id' => (int)$row['id'],
                'name' => $row['name'],
                'price' => (int)$row['price'],
                'success_bonus' => $bonus,
                'output_bonus' => 0,
                'unlock_requirement' => $row['unlock_requirement'] ? $row['unlock_requirement'] : '无要求',
                'description' => $row['description'] ? $row['description'] : '',
                'multiple_output_chance' => $multiple_output_chance,  // 🆕 复数产出几率
                'max_output_multiplier' => $max_output_multiplier,    // 🆕 最大产出倍数
                'special_effect' => $special_effect                   // 🆕 特效描述
            ];
        }
        
        echo json_encode([
            'success' => true,
            'data' => $furnaces,
            'count' => count($furnaces)
        ]);
        
    } catch (Exception $e) {
        throw new Exception('获取丹炉数据失败: ' . $e->getMessage());
    }
}

// 获取材料列表
function getMaterials($db) {
    try {
        $user_id = getCurrentUserId();
        
        if (!$user_id) {
            // 如果未登录，返回示例材料数据
            $sql = "SELECT id, item_name as name, description
                    FROM game_items 
                    WHERE item_type = 'material'
                    ORDER BY id ASC LIMIT 20";
            
            $stmt = $db->prepare($sql);
            $stmt->execute();
            
            $materials = [];
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $materials[] = [
                    'id' => (int)$row['id'],
                    'name' => $row['name'] ? $row['name'] : "材料" . $row['id'],
                    'description' => $row['description'] ? $row['description'] : '',
                    'count' => rand(0, 50) // 模拟数据
                ];
            }
        } else {
            // 已登录用户的材料查询 - 使用game_items表
            $sql = "SELECT g.id, g.item_name as name, g.description, 
                           COALESCE(ui.quantity, 0) as count
                    FROM game_items g
                    LEFT JOIN user_inventories ui ON g.id = ui.item_id 
                        AND ui.character_id = (SELECT id FROM characters WHERE user_id = ?) 
                        AND ui.item_type = 'material'
                    WHERE g.item_type = 'material'
                    ORDER BY g.id ASC LIMIT 20";
            
            $stmt = $db->prepare($sql);
            $stmt->execute([$user_id]);
            
            $materials = [];
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $materials[] = [
                    'id' => (int)$row['id'],
                    'name' => $row['name'] ? $row['name'] : "材料" . $row['id'],
                    'description' => $row['description'] ? $row['description'] : '',
                    'count' => (int)$row['count']
                ];
            }
        }
        
        echo json_encode([
            'success' => true,
            'data' => $materials,
            'count' => count($materials),
            'message' => '材料数据来源：game_items表'
        ]);
        
    } catch (Exception $e) {
        throw new Exception('获取材料数据失败: ' . $e->getMessage());
    }
}

// 获取丹方列表 - 修改为显示所有已学会的丹方，无境界限制
function getRecipes($db) {
    try {
        $user_id = getCurrentUserId();
        
        if (!$user_id) {
            // 未登录时返回示例数据
            echo json_encode([
                'success' => true,
                'data' => [
                    [
                        'id' => 1,
                        'name' => '示例丹方',
                        'description' => '一种基础丹药的配方',
                        'materials' => ['灵芝草 x1'],
                        'realm_requirement' => '无限制',
                        'required_level' => 1,
                        'level' => 1,
                        'base_success_rate' => 50,
                        'craft_time' => 3
                    ]
                ],
                'count' => 1
            ]);
            return;
        }
        
        // 获取角色ID
        $characterSql = "SELECT id FROM characters WHERE user_id = ? LIMIT 1";
        $stmt = $db->prepare($characterSql);
        $stmt->execute([$user_id]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$character) {
            throw new Exception('角色信息不存在');
        }
        
        $character_id = $character['id'];
        
        // 获取已学会的丹方（无境界限制）
        $sql = "SELECT r.id, r.recipe_name as name, r.description, r.materials, 
                       r.required_level, r.success_rate as base_success_rate, 
                       3 as craft_time, r.result_item_id,
                       g.item_name as pill_name
                FROM user_learned_recipes ulr
                INNER JOIN crafting_recipes r ON ulr.recipe_id = r.id
                INNER JOIN game_items g ON r.result_item_id = g.id
                WHERE ulr.character_id = ? AND r.is_active = 1
                ORDER BY r.required_level ASC, r.id ASC";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([$character_id]);
        
        $recipes = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            // 使用通用材料解析函数
            $materialList = parseMaterials($row['materials'], $db);
            
            // 🆕 检查材料充足性
            $materialDisplayList = [];
            $materialDetailsList = [];
            $canCraft = true; // 是否可以炼制
            
            foreach ($materialList as $material) {
                // 查询玩家拥有的材料数量
                $ownedSql = "SELECT COALESCE(SUM(quantity), 0) as owned_count 
                           FROM user_inventories 
                           WHERE character_id = ? AND item_id = ? AND item_type = 'material'";
                $ownedStmt = $db->prepare($ownedSql);
                $ownedStmt->execute([$character_id, $material['id']]);
                $ownedData = $ownedStmt->fetch(PDO::FETCH_ASSOC);
                $ownedCount = (int)$ownedData['owned_count'];
                
                $requiredCount = $material['quantity'];
                $isSufficient = $ownedCount >= $requiredCount;
                
                if (!$isSufficient) {
                    $canCraft = false;
                }
                
                // 构建显示文本
                $displayText = $material['name'] . " x" . $requiredCount;
                $materialDisplayList[] = $displayText;
                
                // 详细材料信息
                $materialDetailsList[] = [
                    'id' => $material['id'],
                    'name' => $material['name'],
                    'required' => $requiredCount,
                    'owned' => $ownedCount,
                    'sufficient' => $isSufficient,
                    'display' => $displayText
                ];
            }
            
            $recipes[] = [
                'id' => (int)$row['id'],
                'name' => $row['name'],
                'description' => $row['description'] ?: '',
                'materials' => $materialDisplayList, // 保持兼容性
                'material_details' => $materialDetailsList, // 🆕 详细材料信息
                'realm_requirement' => '无限制', // 取消境界限制
                'required_level' => (int)$row['required_level'],
                'level' => (int)$row['required_level'],
                'base_success_rate' => (int)$row['base_success_rate'], // 基础成功率
                'craft_time' => 3, // 固定3秒炼制时间
                'result_item_id' => (int)$row['result_item_id'],
                'pill_name' => $row['pill_name'],
                'can_craft' => $canCraft // 🆕 是否可以炼制
            ];
        }
        
        echo json_encode([
            'success' => true,
            'data' => $recipes,
            'count' => count($recipes),
            'message' => '已学会的丹方列表（无境界限制）'
        ]);
        
    } catch (Exception $e) {
        throw new Exception('获取丹方数据失败: ' . $e->getMessage());
    }
}

// 获取角色信息 - 修复版本
function getCharacter($db) {
    try {
        $user_id = getCurrentUserId();
        if (!$user_id) {
            echo json_encode([
                'success' => true,
                'data' => [
                    'id' => 0,
                    'username' => '游客',
                    'level' => 1,
                    'current_stamina' => 100,
                    'max_stamina' => 100,
                    'spirit_stones' => 1000,
                    'current_realm' => '练气期一阶',
                    'realm_level' => 1
                ]
            ]);
            return;
        }
        
        // 获取角色基础信息
        $sql = "SELECT c.id, u.username, c.character_name, c.realm_id, c.realm_progress,
                       c.current_hp, c.current_mp, u.spirit_stones, u.gold
                FROM characters c 
                INNER JOIN users u ON c.user_id = u.id
                WHERE c.user_id = ? LIMIT 1";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([$user_id]);
        
        $character = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$character) {
            throw new Exception('角色数据不存在');
        }
        
        // 获取境界信息
        $realmSql = "SELECT realm_name, realm_level FROM realm_levels WHERE id = ?";
        $stmt = $db->prepare($realmSql);
        $stmt->execute([$character['realm_id']]);
        $realm = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $realm_name = $realm ? $realm['realm_name'] : '练气期一阶';
        $realm_level = $realm ? $realm['realm_level'] : 1;
        
        echo json_encode([
            'success' => true,
            'data' => [
                'id' => (int)$character['id'],
                'username' => $character['character_name'] ?: $character['username'],
                'current_hp' => (int)$character['current_hp'],
                'current_mp' => (int)$character['current_mp'],
                'current_stamina' => 100, // 默认体力
                'max_stamina' => 100,
                'spirit_stones' => (int)$character['spirit_stones'],
                'gold' => (int)$character['gold'],
                'current_realm' => $realm_name,
                'realm_level' => $realm_level,
                'realm_progress' => (float)$character['realm_progress']
            ]
        ]);
        
    } catch (Exception $e) {
        throw new Exception('获取角色数据失败: ' . $e->getMessage());
    }
}

// 开始炼制
function startCrafting($db) {
    try {
        // 引入背包工具函数
        require_once __DIR__ . '/../includes/inventory_utils.php';
        
        $user_id = getCurrentUserId();
        if (!$user_id) {
            throw new Exception('请先登录');
        }
        
        $input = json_decode(file_get_contents('php://input'), true);
        $recipe_id = (int)(isset($input['recipe_id']) ? $input['recipe_id'] : 0);
        $furnace_id = (int)(isset($input['furnace_id']) ? $input['furnace_id'] : 0);
        
        if (!$recipe_id || !$furnace_id) {
            throw new Exception('参数不完整');
        }
        
        // 获取丹方信息
        $sql = "SELECT r.*, g.item_name as pill_name, g.realm_requirement
                FROM crafting_recipes r
                INNER JOIN game_items g ON r.result_item_id = g.id
                WHERE r.id = ? AND r.is_active = 1";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([$recipe_id]);
        $recipe = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$recipe) {
            throw new Exception('丹方不存在或已失效');
        }
        
        // 获取丹炉信息
        $sql = "SELECT * FROM game_items WHERE id = ? AND item_name LIKE '%丹炉%'";
        $stmt = $db->prepare($sql);
        $stmt->execute([$furnace_id]);
        $furnace = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$furnace) {
            throw new Exception('丹炉不存在');
        }
        
        // 计算丹炉成功率加成和复数产出几率
        $furnace_bonus = 0;
        $multiple_output_chance = 0; // 🆕 复数产出几率
        $max_output_multiplier = 1;  // 🆕 最大产出倍数
        
        if (strpos($furnace['item_name'], '下品') !== false) {
            $furnace_bonus = 5;
            $multiple_output_chance = 10; // 10% 几率产出2个
            $max_output_multiplier = 2;
        } elseif (strpos($furnace['item_name'], '中品') !== false) {
            $furnace_bonus = 10;
            $multiple_output_chance = 15; // 15% 几率产出2-3个
            $max_output_multiplier = 3;
        } elseif (strpos($furnace['item_name'], '上品') !== false) {
            $furnace_bonus = 15;
            $multiple_output_chance = 20; // 20% 几率产出2-4个
            $max_output_multiplier = 4;
        } elseif (strpos($furnace['item_name'], '极品') !== false) {
            $furnace_bonus = 20;
            $multiple_output_chance = 25; // 25% 几率产出2-5个
            $max_output_multiplier = 5;
        } elseif (strpos($furnace['item_name'], '神品') !== false) {
            $furnace_bonus = 30;
            $multiple_output_chance = 35; // 35% 几率产出2-6个
            $max_output_multiplier = 6;
        }
        
        // 最终成功率 = 丹方基础成功率 + 丹炉加成
        $final_success_rate = (int)$recipe['success_rate'] + $furnace_bonus;
        $final_success_rate = min($final_success_rate, 100); // 最大不超过100%
        
        // 检查材料是否充足
        $materialList = parseMaterials($recipe['materials'], $db);
        if (!empty($materialList)) {
            // 获取角色ID
            $characterSql = "SELECT id FROM characters WHERE user_id = ? LIMIT 1";
            $stmt = $db->prepare($characterSql);
            $stmt->execute([$user_id]);
            $character = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$character) {
                throw new Exception('角色信息不存在');
            }
            
            $character_id = $character['id'];
            
            // 检查每种材料的数量
            foreach ($materialList as $materialInfo) {
                $materialId = $materialInfo['id'];
                $requiredQuantity = $materialInfo['quantity'];
                
                $materialSql = "SELECT quantity FROM user_inventories 
                               WHERE character_id = ? AND item_id = ? AND item_type = 'material'";
                $stmt = $db->prepare($materialSql);
                $stmt->execute([$character_id, $materialId]);
                $inventory = $stmt->fetch(PDO::FETCH_ASSOC);
                
                $currentQuantity = $inventory ? (int)$inventory['quantity'] : 0;
                
                if ($currentQuantity < $requiredQuantity) {
                    throw new Exception("材料不足：{$materialInfo['name']} (需要{$requiredQuantity}个，拥有{$currentQuantity}个)");
                }
            }
        }
        
        // 计算炼制成功
        $isSuccess = (rand(1, 100) <= $final_success_rate);
        
        // 扣除材料
        if (!empty($materialList)) {
            foreach ($materialList as $materialInfo) {
                $materialId = $materialInfo['id'];
                $requiredQuantity = $materialInfo['quantity'];
                
                $updateSql = "UPDATE user_inventories 
                             SET quantity = quantity - ? 
                             WHERE character_id = ? AND item_id = ? AND item_type = 'material'";
                $stmt = $db->prepare($updateSql);
                $stmt->execute([$requiredQuantity, $character_id, $materialId]);
            }
        }
        
        $result_message = '';
        $actual_output_count = 1; // 🆕 实际产出数量
        $finalItemNames = []; // 🆕 初始化产出物品名称数组
        
        if ($isSuccess) {
            // 🆕 计算复数产出
            $output_count = 1;
            if ($multiple_output_chance > 0 && rand(1, 100) <= $multiple_output_chance) {
                // 触发复数产出，随机2到最大倍数
                $output_count = rand(2, $max_output_multiplier);
            }
            $actual_output_count = $output_count;
            
            // 🆕 渡劫丹和养魂丹品质随机系统
            $finalItemIds = [];
            $finalItemNames = [];
            
            for ($i = 0; $i < $output_count; $i++) {
                $randomItemId = getRandomQualityItemId($db, $recipe['result_item_id'], $recipe['recipe_name']);
                $finalItemIds[] = $randomItemId['item_id'];
                $finalItemNames[] = $randomItemId['item_name'];
            }
            
            // 炼制成功，添加随机品质的丹药到背包
            foreach ($finalItemIds as $itemId) {
                // 🆕 获取物品的堆叠信息
                $stmt = $db->prepare("SELECT max_stack, is_stackable FROM game_items WHERE id = ?");
                $stmt->execute([$itemId]);
                $gameItem = $stmt->fetch(PDO::FETCH_ASSOC);
                
                $maxStack = $gameItem ? intval($gameItem['max_stack']) : 99;
                $isStackable = $gameItem ? $gameItem['is_stackable'] : true;
                
                // 🆕 智能堆叠逻辑 - 参考商店购买的处理方式
                if ($isStackable && $maxStack > 1) {
                    // 1. 先尝试填充现有的未满堆叠位置
                    $stmt = $db->prepare("
                        SELECT id, quantity FROM user_inventories 
                        WHERE character_id = ? AND item_id = ? AND item_type = 'consumable' AND quantity < ?
                        ORDER BY quantity DESC LIMIT 1
                    ");
                    $stmt->execute([$character_id, $itemId, $maxStack]);
                    $existingSlot = $stmt->fetch(PDO::FETCH_ASSOC);
                    
                    if ($existingSlot) {
                        // 更新现有槽位数量
                        $newQuantity = $existingSlot['quantity'] + 1;
                        if ($newQuantity <= $maxStack) {
                            $stmt = $db->prepare("
                                UPDATE user_inventories 
                                SET quantity = ?, updated_at = NOW()
                                WHERE id = ?
                            ");
                            $stmt->execute([$newQuantity, $existingSlot['id']]);
                        } else {
                            // 如果现有槽位已满，创建新槽位
                            $sortWeight = calculateSortWeight($db, $character_id, 'consumable');
                            
                            $stmt = $db->prepare("
                                INSERT INTO user_inventories (character_id, item_id, item_type, quantity, obtained_time, obtained_source, sort_weight) 
                                VALUES (?, ?, 'consumable', 1, NOW(), 'alchemy_crafting', ?)
                            ");
                            $stmt->execute([$character_id, $itemId, $sortWeight]);
                        }
                    } else {
                        // 2. 没有现有槽位，创建新的背包位置
                        $sortWeight = calculateSortWeight($db, $character_id, 'consumable');
                        
                        $stmt = $db->prepare("
                            INSERT INTO user_inventories (character_id, item_id, item_type, quantity, obtained_time, obtained_source, sort_weight) 
                            VALUES (?, ?, 'consumable', 1, NOW(), 'alchemy_crafting', ?)
                        ");
                        $stmt->execute([$character_id, $itemId, $sortWeight]);
                    }
                } else {
                    // 不可堆叠物品，直接创建新记录
                    $sortWeight = calculateSortWeight($db, $character_id, 'consumable');
                    
                    $stmt = $db->prepare("
                        INSERT INTO user_inventories (character_id, item_id, item_type, quantity, obtained_time, obtained_source, sort_weight) 
                        VALUES (?, ?, 'consumable', 1, NOW(), 'alchemy_crafting', ?)
                    ");
                    $stmt->execute([$character_id, $itemId, $sortWeight]);
                }
            }
            
            // 🆕 根据产出数量和品质显示不同消息
            if ($output_count > 1) {
                $itemsText = implode('、', array_unique($finalItemNames));
                $result_message = "炼制大成功！触发了{$furnace['item_name']}的特效，获得了 {$output_count} 个丹药：{$itemsText}！";
            } else {
                $result_message = "炼制成功！获得了 {$finalItemNames[0]}";
            }
        } else {
            $result_message = "炼制失败！材料已消耗";
        }
        
        echo json_encode([
            'success' => true,
            'is_crafting_success' => $isSuccess,
            'message' => $result_message,
            'data' => [
                'recipe_name' => $recipe['recipe_name'],
                'crafting_time' => 3, // 固定3秒
                'base_success_rate' => (int)$recipe['success_rate'],
                'furnace_bonus' => $furnace_bonus,
                'final_success_rate' => $final_success_rate,
                'furnace_name' => $furnace['item_name'],
                'multiple_output_chance' => $multiple_output_chance, // 🆕 复数产出几率
                'max_output_multiplier' => $max_output_multiplier,   // 🆕 最大产出倍数
                'actual_output_count' => $actual_output_count,       // 🆕 实际产出数量
                'is_multiple_output' => $actual_output_count > 1,    // 🆕 是否触发复数产出
                'output_items' => $isSuccess ? $finalItemNames : [], // 🆕 实际产出的物品名称
                'has_quality_random' => (strpos($recipe['recipe_name'], '渡劫丹') !== false || strpos($recipe['recipe_name'], '养魂丹') !== false) // 🆕 是否有品质随机
            ]
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

// 学习丹方
function learnRecipe($db) {
    try {
        $user_id = getCurrentUserId();
        if (!$user_id) {
            throw new Exception('请先登录');
        }
        
        // 获取角色ID
        $characterSql = "SELECT id FROM characters WHERE user_id = ? LIMIT 1";
        $stmt = $db->prepare($characterSql);
        $stmt->execute([$user_id]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$character) {
            throw new Exception('角色信息不存在');
        }
        
        $character_id = $character['id'];
        
        // 获取POST数据
        $input = json_decode(file_get_contents('php://input'), true);
        $recipe_id = (int)(isset($input['recipe_id']) ? $input['recipe_id'] : 0);
        $learn_source = isset($input['learn_source']) ? $input['learn_source'] : '手动学习';
        
        if (!$recipe_id) {
            throw new Exception('丹方ID不能为空');
        }
        
        // 检查丹方是否存在
        $recipeSql = "SELECT r.*, g.item_name as pill_name 
                      FROM crafting_recipes r 
                      INNER JOIN game_items g ON r.result_item_id = g.id 
                      WHERE r.id = ? AND r.is_active = 1";
        $stmt = $db->prepare($recipeSql);
        $stmt->execute([$recipe_id]);
        $recipe = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$recipe) {
            throw new Exception('丹方不存在或已失效');
        }
        
        // 检查是否已经学会了这个丹方
        $learnedSql = "SELECT id FROM user_learned_recipes WHERE character_id = ? AND recipe_id = ?";
        $stmt = $db->prepare($learnedSql);
        $stmt->execute([$character_id, $recipe_id]);
        $learned = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($learned) {
            throw new Exception('您已经学会了这个丹方');
        }
        
        // 学习丹方
        $insertSql = "INSERT INTO user_learned_recipes (character_id, recipe_id, learned_source) VALUES (?, ?, ?)";
        $stmt = $db->prepare($insertSql);
        $result = $stmt->execute([$character_id, $recipe_id, $learn_source]);
        
        if ($result) {
            echo json_encode([
                'success' => true,
                'message' => "成功学会了 {$recipe['recipe_name']} 丹方",
                'data' => [
                    'recipe_id' => $recipe_id,
                    'recipe_name' => $recipe['recipe_name'],
                    'learned_source' => $learn_source
                ]
            ]);
        } else {
            throw new Exception('学习丹方失败');
        }
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

// 获取可学习的丹方列表
function getAvailableRecipes($db) {
    try {
        $user_id = getCurrentUserId();
        if (!$user_id) {
            throw new Exception('请先登录');
        }
        
        // 获取角色ID
        $characterSql = "SELECT id FROM characters WHERE user_id = ? LIMIT 1";
        $stmt = $db->prepare($characterSql);
        $stmt->execute([$user_id]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$character) {
            throw new Exception('角色信息不存在');
        }
        
        $character_id = $character['id'];
        
        // 查询所有可用丹方中未学会的
        $sql = "SELECT r.id, r.recipe_name as name, r.required_level, g.item_name as pill_name, g.description as pill_description
                FROM crafting_recipes r
                INNER JOIN game_items g ON r.result_item_id = g.id
                LEFT JOIN user_learned_recipes ulr ON r.id = ulr.recipe_id AND ulr.character_id = ?
                WHERE r.is_active = 1 AND ulr.id IS NULL
                ORDER BY r.required_level ASC, r.recipe_name ASC";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([$character_id]);
        
        $available_recipes = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $available_recipes[] = [
                'id' => (int)$row['id'],
                'recipe_name' => $row['name'],
                'pill_name' => $row['pill_name'],
                'pill_description' => $row['pill_description'] ? $row['pill_description'] : '',
                'required_level' => (int)$row['required_level'],
                'can_learn' => true // 后续可以根据等级等条件判断
            ];
        }
        
        echo json_encode([
            'success' => true,
            'data' => $available_recipes,
            'count' => count($available_recipes),
            'message' => '获取可学习丹方成功'
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

// 获取用户拥有的丹炉列表
function getUserFurnaces($db) {
    try {
        $user_id = getCurrentUserId();
        
        if (!$user_id) {
            // 如果未登录，返回示例丹炉数据
            $sql = "SELECT id, item_name as name, sell_price as price, description
                    FROM game_items 
                    WHERE item_name LIKE '%丹炉%' AND is_active = 1
                    ORDER BY sell_price ASC LIMIT 3";
            
            $stmt = $db->prepare($sql);
            $stmt->execute();
            
            $furnaces = [];
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $bonus = 0;
                $multiple_output_chance = 0;
                $max_output_multiplier = 1;
                $special_effect = "无特效";
                
                if (strpos($row['name'], '下品') !== false) {
                    $bonus = 5;
                    $multiple_output_chance = 10;
                    $max_output_multiplier = 2;
                    $special_effect = "10%几率产出2个丹药";
                } elseif (strpos($row['name'], '中品') !== false) {
                    $bonus = 10;
                    $multiple_output_chance = 15;
                    $max_output_multiplier = 3;
                    $special_effect = "15%几率产出2-3个丹药";
                } elseif (strpos($row['name'], '上品') !== false) {
                    $bonus = 15;
                    $multiple_output_chance = 20;
                    $max_output_multiplier = 4;
                    $special_effect = "20%几率产出2-4个丹药";
                } elseif (strpos($row['name'], '极品') !== false) {
                    $bonus = 20;
                    $multiple_output_chance = 25;
                    $max_output_multiplier = 5;
                    $special_effect = "25%几率产出2-5个丹药";
                } elseif (strpos($row['name'], '神品') !== false) {
                    $bonus = 30;
                    $multiple_output_chance = 35;
                    $max_output_multiplier = 6;
                    $special_effect = "35%几率产出2-6个丹药";
                }
                
                $furnaces[] = [
                    'id' => (int)$row['id'],
                    'name' => $row['name'],
                    'price' => (int)$row['price'],
                    'success_bonus' => $bonus,
                    'output_bonus' => 0,
                    'description' => $row['description'] ? $row['description'] : '',
                    'quantity' => 1, // 示例数据
                    'multiple_output_chance' => $multiple_output_chance,  // 🆕 复数产出几率
                    'max_output_multiplier' => $max_output_multiplier,    // 🆕 最大产出倍数
                    'special_effect' => $special_effect                   // 🆕 特效描述
                ];
            }
        } else {
            // 已登录用户 - 查询背包中的丹炉
            $characterSql = "SELECT id FROM characters WHERE user_id = ? LIMIT 1";
            $stmt = $db->prepare($characterSql);
            $stmt->execute([$user_id]);
            $character = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$character) {
                throw new Exception('角色信息不存在');
            }
            
            $character_id = $character['id'];
            
            $sql = "SELECT ui.quantity, gi.id, gi.item_name as name, gi.sell_price as price, gi.description
                    FROM user_inventories ui
                    INNER JOIN game_items gi ON ui.item_id = gi.id
                    WHERE ui.character_id = ? AND gi.item_name LIKE '%丹炉%' AND ui.quantity > 0
                    ORDER BY gi.sell_price ASC";
            
            $stmt = $db->prepare($sql);
            $stmt->execute([$character_id]);
            
            $furnaces = [];
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $bonus = 0;
                $multiple_output_chance = 0;
                $max_output_multiplier = 1;
                $special_effect = "无特效";
                
                if (strpos($row['name'], '下品') !== false) {
                    $bonus = 5;
                    $multiple_output_chance = 10;
                    $max_output_multiplier = 2;
                    $special_effect = "10%几率产出2个丹药";
                } elseif (strpos($row['name'], '中品') !== false) {
                    $bonus = 10;
                    $multiple_output_chance = 15;
                    $max_output_multiplier = 3;
                    $special_effect = "15%几率产出2-3个丹药";
                } elseif (strpos($row['name'], '上品') !== false) {
                    $bonus = 15;
                    $multiple_output_chance = 20;
                    $max_output_multiplier = 4;
                    $special_effect = "20%几率产出2-4个丹药";
                } elseif (strpos($row['name'], '极品') !== false) {
                    $bonus = 20;
                    $multiple_output_chance = 25;
                    $max_output_multiplier = 5;
                    $special_effect = "25%几率产出2-5个丹药";
                } elseif (strpos($row['name'], '神品') !== false) {
                    $bonus = 30;
                    $multiple_output_chance = 35;
                    $max_output_multiplier = 6;
                    $special_effect = "35%几率产出2-6个丹药";
                }
                
                $furnaces[] = [
                    'id' => (int)$row['id'],
                    'name' => $row['name'],
                    'price' => (int)$row['price'],
                    'success_bonus' => $bonus,
                    'output_bonus' => 0,
                    'description' => $row['description'] ? $row['description'] : '',
                    'quantity' => (int)$row['quantity'],
                    'multiple_output_chance' => $multiple_output_chance,  // 🆕 复数产出几率
                    'max_output_multiplier' => $max_output_multiplier,    // 🆕 最大产出倍数
                    'special_effect' => $special_effect                   // 🆕 特效描述
                ];
            }
        }
        
        echo json_encode([
            'success' => true,
            'data' => $furnaces,
            'count' => count($furnaces),
            'message' => $user_id ? '获取用户丹炉成功' : '获取示例丹炉成功'
        ]);
        
    } catch (Exception $e) {
        throw new Exception('获取用户丹炉失败: ' . $e->getMessage());
    }
}

// 🆕 获取随机品质的丹药ID
function getRandomQualityItemId($pdo, $baseItemId, $recipeName) {
    // 检查是否为渡劫丹或养魂丹
    if (strpos($recipeName, '渡劫丹') === false && strpos($recipeName, '养魂丹') === false) {
        // 非渡劫丹/养魂丹，返回原始物品
        $stmt = $pdo->prepare("SELECT id, item_name FROM game_items WHERE id = ?");
        $stmt->execute([$baseItemId]);
        $item = $stmt->fetch(PDO::FETCH_ASSOC);
        return [
            'item_id' => $baseItemId,
            'item_name' => $item['item_name']
        ];
    }
    
    // 从配方名称中提取境界信息
    $realmName = '';
    $pillType = '';
    
    if (strpos($recipeName, '渡劫丹') !== false) {
        $pillType = '渡劫丹';
        $realmName = str_replace('渡劫丹', '', $recipeName);
    } elseif (strpos($recipeName, '养魂丹') !== false) {
        $pillType = '养魂丹';
        $realmName = str_replace('养魂丹', '', $recipeName);
    }
    
    // 品质权重系统 - 品质越低概率越高
    $qualityWeights = [
        '下品' => 40,    // 40% 几率
        '中品' => 30,    // 30% 几率  
        '上品' => 20,    // 20% 几率
        '极品' => 10     // 10% 几率
    ];
    
    // 根据权重随机选择品质
    $randomNum = rand(1, 100);
    $currentWeight = 0;
    $selectedQuality = '下品'; // 默认下品
    
    foreach ($qualityWeights as $quality => $weight) {
        $currentWeight += $weight;
        if ($randomNum <= $currentWeight) {
            $selectedQuality = $quality;
            break;
        }
    }
    
    // 查找对应品质的丹药
    $searchPattern = $realmName . $pillType . '（' . $selectedQuality . '）';
    $stmt = $pdo->prepare("SELECT id, item_name FROM game_items WHERE item_name = ?");
    $stmt->execute([$searchPattern]);
    $qualityItem = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($qualityItem) {
        return [
            'item_id' => (int)$qualityItem['id'],
            'item_name' => $qualityItem['item_name']
        ];
    } else {
        // 如果找不到对应品质，返回原始物品
        $stmt = $pdo->prepare("SELECT id, item_name FROM game_items WHERE id = ?");
        $stmt->execute([$baseItemId]);
        $item = $stmt->fetch(PDO::FETCH_ASSOC);
        return [
            'item_id' => $baseItemId,
            'item_name' => $item['item_name']
        ];
    }
}

// 删除重复的getCurrentUserId函数，使用setting.php中的版本
?> 