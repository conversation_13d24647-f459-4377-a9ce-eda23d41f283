/**
 * 金系技能模块 - 金针暴雨
 * 适用于 item_skills 表中的 animation_model = 'jinzhenbayu'
 */

// 金系技能 - 金针暴雨
class JinZhenBaYuSkill extends BaseSkill {
    async execute(skillData, weaponImage) {
        // 使用数据库中的真实技能名称，而不是硬编码
        const skillName = skillData?.skillName || skillData?.displayName || '金针暴雨'; // 提供默认值作为后备
        await this.showSkillShout(skillName);
        
        // 执行金针暴雨动画，传递武器图片
        await this.createJinZhenBaYuAnimation(weaponImage);
    }
    
    async createJinZhenBaYuAnimation(weaponImage) {
        // 🔧 动态判断位置映射
        let casterPos, targetPos;
        
        if (this.isEnemySkill) {
            // 敌方技能：敌人是施法者，玩家是目标
            casterPos = this.getCharacterPosition(false); // 敌人位置
            targetPos = this.getCharacterPosition(true);  // 玩家位置
        } else {
            // 我方技能：玩家是施法者，敌人是目标
            casterPos = this.getCharacterPosition(true);  // 玩家位置
            targetPos = this.getCharacterPosition(false); // 敌人位置
        }
        
        // 创建动画容器
        const container = document.createElement('div');
        container.className = 'jinzhenbayu-container';
        this.effectsContainer.appendChild(container);
        
        // 使用动态位置数据
        const startX = casterPos.x;
        const startY = casterPos.y;
        const endX = targetPos.x;
        const endY = targetPos.y;
        
        try {
            // === 第一阶段：蓄力 - 金属元素汇聚 ===
            await this.createChargePhase(container, startX, startY, weaponImage);
            
            // === 第二阶段：发射 - 金针暴雨降临 ===
            await this.createRainPhase(container, startX, startY, endX, endY);
            
            // === 第三阶段：击中 - 金属风暴爆发 ===
            await this.createImpactPhase(container, endX, endY);
            
        } finally {
            // 清理容器
            setTimeout(() => {
                if (container && container.parentNode) {
                    container.parentNode.removeChild(container);
                }
            }, 100);
        }
    }
    
    // 蓄力阶段：金属元素汇聚
    async createChargePhase(container, startX, startY, weaponImage) {
        // 创建金属魔法阵
        const metalCircle = document.createElement('div');
        metalCircle.className = 'jinzhenbayu-magic-circle';
        metalCircle.style.left = `${startX}px`;
        metalCircle.style.top = `${startY}px`;
        container.appendChild(metalCircle);
        
        // 创建内圈金属符文
        const innerRunes = document.createElement('div');
        innerRunes.className = 'jinzhenbayu-inner-runes';
        innerRunes.style.left = `${startX}px`;
        innerRunes.style.top = `${startY}px`;
        container.appendChild(innerRunes);
        
        // 创建外圈锋利符文
        const outerRunes = document.createElement('div');
        outerRunes.className = 'jinzhenbayu-outer-runes';
        outerRunes.style.left = `${startX}px`;
        outerRunes.style.top = `${startY}px`;
        container.appendChild(outerRunes);
        
        // 创建武器图片在中心旋转
        if (weaponImage) {
            const weaponSprite = document.createElement('div');
            weaponSprite.className = 'jinzhenbayu-weapon-sprite';
            weaponSprite.style.left = `${startX}px`;
            weaponSprite.style.top = `${startY}px`;
            
            // 添加武器图片背景
            this.addWeaponImage(weaponSprite, weaponImage);
            // 🗡️ 动态调整武器图片角度
            const weaponImg = weaponSprite.querySelector('.weapon-image');
            if (weaponImg) {
                weaponImg.style.transform = `rotate(${this.calculateInitialSwordRotation()}deg)`;
            }
            
            container.appendChild(weaponSprite);
        }
        
        // 创建蓄力能量核心
        const energyCore = document.createElement('div');
        energyCore.className = 'jinzhenbayu-energy-core';
        energyCore.style.left = `${startX}px`;
        energyCore.style.top = `${startY}px`;
        container.appendChild(energyCore);
        
        // 创建金属粒子汇聚效果（增加到40个）
        for (let i = 0; i < 40; i++) {
            const metalParticle = document.createElement('div');
            metalParticle.className = 'jinzhenbayu-charge-metal';
            metalParticle.style.left = `${startX}px`;
            metalParticle.style.top = `${startY}px`;
            
            const angle = Math.random() * Math.PI * 2;
            const radius = 25 + Math.random() * 45;
            const moveX = Math.cos(angle) * radius;
            const moveY = Math.sin(angle) * radius;
            
            metalParticle.style.setProperty('--chargeX', `${moveX}px`);
            metalParticle.style.setProperty('--chargeY', `${moveY}px`);
            metalParticle.style.animationDelay = `${Math.random() * 1.0}s`;
            
            container.appendChild(metalParticle);
        }
        
        // 创建环绕的金光闪烁
        for (let i = 0; i < 20; i++) {
            const angle = (i / 20) * Math.PI * 2;
            const radius = 80;
            const glintX = startX + Math.cos(angle) * radius;
            const glintY = startY + Math.sin(angle) * radius;
            
            const glint = document.createElement('div');
            glint.className = 'jinzhenbayu-charge-glint';
            glint.style.left = `${glintX}px`;
            glint.style.top = `${glintY}px`;
            glint.style.animationDelay = `${i * 0.04}s`;
            container.appendChild(glint);
        }
        
        // 创建金属能量波纹
        for (let i = 0; i < 7; i++) {
            const ripple = document.createElement('div');
            ripple.className = 'jinzhenbayu-energy-ripple';
            ripple.style.left = `${startX}px`;
            ripple.style.top = `${startY}px`;
            ripple.style.animationDelay = `${i * 0.1}s`;
            container.appendChild(ripple);
        }
        
        // 创建锋利气息效果
        for (let i = 0; i < 15; i++) {
            const sharpness = document.createElement('div');
            sharpness.className = 'jinzhenbayu-sharpness';
            sharpness.style.left = `${startX + (Math.random() - 0.5) * 50}px`;
            sharpness.style.top = `${startY + (Math.random() - 0.5) * 50}px`;
            sharpness.style.animationDelay = `${Math.random() * 0.8}s`;
            container.appendChild(sharpness);
        }
        
        // 创建金属共鸣音效视觉
        for (let i = 0; i < 8; i++) {
            const resonance = document.createElement('div');
            resonance.className = 'jinzhenbayu-resonance';
            resonance.style.left = `${startX}px`;
            resonance.style.top = `${startY}px`;
            resonance.style.animationDelay = `${i * 0.15}s`;
            container.appendChild(resonance);
        }
        
        // 等待蓄力完成
        await this.wait(1200);
        
        // 移除蓄力效果
        metalCircle.remove();
        innerRunes.remove();
        outerRunes.remove();
        energyCore.remove();
        document.querySelectorAll('.jinzhenbayu-charge-metal').forEach(m => m.remove());
        document.querySelectorAll('.jinzhenbayu-charge-glint').forEach(g => g.remove());
        document.querySelectorAll('.jinzhenbayu-energy-ripple').forEach(r => r.remove());
        document.querySelectorAll('.jinzhenbayu-sharpness').forEach(s => s.remove());
        document.querySelectorAll('.jinzhenbayu-resonance').forEach(r => r.remove());
        document.querySelectorAll('.jinzhenbayu-weapon-sprite').forEach(w => w.remove());
    }
    
    // 发射阶段：金针暴雨降临
    async createRainPhase(container, startX, startY, endX, endY) {
        // 创建预发射金属震颤
        const metalVibration = document.createElement('div');
        metalVibration.className = 'jinzhenbayu-metal-vibration';
        metalVibration.style.left = `${startX}px`;
        metalVibration.style.top = `${startY}px`;
        container.appendChild(metalVibration);
        
        await this.wait(300);
        
        // 创建天空中的金针云团
        const needleCloud = document.createElement('div');
        needleCloud.className = 'jinzhenbayu-needle-cloud';
        needleCloud.style.left = `${endX}px`;
        needleCloud.style.top = `${endY - 150}px`;
        container.appendChild(needleCloud);
        
        // 创建金针暴雨效果（分三波）
        const waves = [
            { count: 15, delay: 0, duration: 0.8 },
            { count: 20, delay: 0.3, duration: 1.0 },
            { count: 25, delay: 0.6, duration: 1.2 }
        ];
        
        const allNeedles = [];
        
        waves.forEach((wave, waveIndex) => {
            setTimeout(() => {
                for (let i = 0; i < wave.count; i++) {
                    const needle = document.createElement('div');
                    needle.className = `jinzhenbayu-needle-wave-${waveIndex + 1}`;
                    
                    // 随机分布在目标周围
                    const offsetX = (Math.random() - 0.5) * 120;
                    const offsetY = (Math.random() - 0.5) * 80;
                    const needleStartX = endX + offsetX;
                    const needleStartY = endY - 200 + offsetY;
                    
                    needle.style.left = `${needleStartX}px`;
                    needle.style.top = `${needleStartY}px`;
                    needle.style.setProperty('--fallDistance', `${200 + Math.random() * 50}px`);
                    needle.style.setProperty('--fallDuration', `${wave.duration}s`);
                    needle.style.animationDelay = `${i * 0.03}s`;
                    
                    container.appendChild(needle);
                    allNeedles.push(needle);
                }
            }, wave.delay * 1000);
        });
        
        // 创建金针轨迹效果
        const trailInterval = setInterval(() => {
            const trail = document.createElement('div');
            trail.className = 'jinzhenbayu-needle-trail';
            trail.style.left = `${endX + (Math.random() - 0.5) * 100}px`;
            trail.style.top = `${endY - 180 + Math.random() * 60}px`;
            container.appendChild(trail);
            
            setTimeout(() => trail.remove(), 600);
        }, 100);
        
        // 创建金光闪烁效果
        const glintInterval = setInterval(() => {
            const glint = document.createElement('div');
            glint.className = 'jinzhenbayu-falling-glint';
            glint.style.left = `${endX + (Math.random() - 0.5) * 80}px`;
            glint.style.top = `${endY - 160 + Math.random() * 40}px`;
            container.appendChild(glint);
            
            setTimeout(() => glint.remove(), 400);
        }, 80);
        
        await this.wait(700);
        
        clearInterval(trailInterval);
        clearInterval(glintInterval);
        
        // 移除发射效果
        metalVibration.remove();
        needleCloud.remove();
        allNeedles.forEach(needle => needle.remove());
        document.querySelectorAll('.jinzhenbayu-needle-trail').forEach(t => t.remove());
        document.querySelectorAll('.jinzhenbayu-falling-glint').forEach(g => g.remove());
    }
    
    // 击中阶段：金属风暴爆发
    async createImpactPhase(container, endX, endY) {
        // 第一阶段：瞬间金属闪光
        const impactFlash = document.createElement('div');
        impactFlash.className = 'jinzhenbayu-impact-flash';
        impactFlash.style.left = `${endX}px`;
        impactFlash.style.top = `${endY}px`;
        container.appendChild(impactFlash);
        
        // 第二阶段：金属风暴核心
        const stormCore = document.createElement('div');
        stormCore.className = 'jinzhenbayu-storm-core';
        stormCore.style.left = `${endX}px`;
        stormCore.style.top = `${endY}px`;
        container.appendChild(stormCore);
        
        // 第三阶段：多层冲击波
        setTimeout(() => {
            for (let i = 0; i < 6; i++) {
                const shockwave = document.createElement('div');
                shockwave.className = 'jinzhenbayu-impact-shockwave';
                shockwave.style.left = `${endX}px`;
                shockwave.style.top = `${endY}px`;
                shockwave.style.animationDelay = `${i * 0.08}s`;
                container.appendChild(shockwave);
            }
        }, 100);
        
        // 第四阶段：金属碎片爆发
        setTimeout(() => {
            for (let i = 0; i < 50; i++) {
                const fragment = document.createElement('div');
                fragment.className = 'jinzhenbayu-metal-fragment';
                fragment.style.left = `${endX}px`;
                fragment.style.top = `${endY}px`;
                
                const angle = (i / 50) * Math.PI * 2;
                const distance = 20 + Math.random() * 80;
                const velocity = 0.8 + Math.random() * 0.6;
                
                fragment.style.setProperty('--fragmentAngle', `${angle * 180 / Math.PI}deg`);
                fragment.style.setProperty('--fragmentDistance', `${distance}px`);
                fragment.style.setProperty('--fragmentVelocity', velocity);
                fragment.style.animationDelay = `${i * 0.01}s`;
                
                container.appendChild(fragment);
            }
        }, 200);
        
        // 第五阶段：切割线效果
        setTimeout(() => {
            for (let i = 0; i < 12; i++) {
                const cutLine = document.createElement('div');
                cutLine.className = 'jinzhenbayu-cut-line';
                cutLine.style.left = `${endX}px`;
                cutLine.style.top = `${endY}px`;
                
                const angle = (i / 12) * Math.PI * 2;
                const length = 60 + Math.random() * 40;
                
                cutLine.style.setProperty('--cutAngle', `${angle * 180 / Math.PI}deg`);
                cutLine.style.setProperty('--cutLength', `${length}px`);
                cutLine.style.animationDelay = `${i * 0.02}s`;
                
                container.appendChild(cutLine);
            }
        }, 300);
        
        // 第六阶段：金属粉尘云
        setTimeout(() => {
            for (let i = 0; i < 18; i++) {
                const dust = document.createElement('div');
                dust.className = 'jinzhenbayu-metal-dust';
                dust.style.left = `${endX + (Math.random() - 0.5) * 120}px`;
                dust.style.top = `${endY + (Math.random() - 0.5) * 120}px`;
                dust.style.animationDelay = `${i * 0.06}s`;
                container.appendChild(dust);
            }
        }, 400);
        
        // 第七阶段：金光余韵
        setTimeout(() => {
            for (let i = 0; i < 10; i++) {
                const afterglow = document.createElement('div');
                afterglow.className = 'jinzhenbayu-afterglow';
                afterglow.style.left = `${endX + (Math.random() - 0.5) * 60}px`;
                afterglow.style.top = `${endY + (Math.random() - 0.5) * 60}px`;
                afterglow.style.animationDelay = `${i * 0.12}s`;
                container.appendChild(afterglow);
            }
        }, 600);
        
        // 第八阶段：金属共鸣余震
        setTimeout(() => {
            for (let i = 0; i < 6; i++) {
                const resonance = document.createElement('div');
                resonance.className = 'jinzhenbayu-impact-resonance';
                resonance.style.left = `${endX}px`;
                resonance.style.top = `${endY}px`;
                resonance.style.animationDelay = `${i * 0.2}s`;
                container.appendChild(resonance);
            }
        }, 800);
        
        // 击中特效
        this.createHitEffect(endX, endY, true);
        
        // 🔧 修复：给被攻击者添加金属切割效果
        const targetSelector = !this.isEnemySkill ? '.enemy .character-sprite' : '.player .character-sprite';
        const targetSprite = document.querySelector(targetSelector);
        if (targetSprite) {
            targetSprite.style.animation = 'metal-hit 2.0s ease-out, metal-shake 0.1s ease-in-out 15';
        }
        
        await this.wait(800);
        
        // 🔧 修复：清理击中效果
        if (targetSprite) {
            targetSprite.style.animation = '';
        }
    }
    // 🗡️ 动态计算剑的初始旋转角度
    calculateInitialSwordRotation() {
        // 敌方技能：剑尖向下（指向玩家），我方技能：剑尖向上（指向敌人）
        return this.isEnemySkill ? 0 : 180;
    }
}

// 导出技能类
window.MetalSkills = { JinZhenBaYuSkill }; 