<?php
/**
 * 奇遇系统API
 * 
 * 功能包括：
 * 1. 奇遇值累积系统
 * 2. 奇遇事件触发
 * 3. 智能奖励分配
 * 4. 奇遇状态查询
 * 5. 奇遇历史记录
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/auth.php';

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    $db = getDatabase();
    
    // 验证用户身份
    $userId = check_auth();
    if (!$userId) {
        throw new Exception('用户未登录或会话已过期');
    }
    
    // 获取角色ID
    $stmt = $db->prepare("SELECT id FROM characters WHERE user_id = ? LIMIT 1");
    $stmt->execute([$userId]);
    $character = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$character) {
        throw new Exception('角色不存在');
    }
    
    $characterId = $character['id'];
    
    // 获取操作类型
    $action = isset($_POST['action']) ? $_POST['action'] : (isset($_GET['action']) ? $_GET['action'] : '');
    
    switch ($action) {
        case 'add_adventure_value':
            echo json_encode(addAdventureValue($db, $characterId));
            break;
            
        case 'get_adventure_status':
            echo json_encode(getAdventureStatus($db, $characterId));
            break;
            
        case 'get_adventure_history':
            echo json_encode(getAdventureHistory($db, $characterId));
            break;
            
        case 'trigger_adventure_event':
            echo json_encode(triggerAdventureEvent($db, $characterId));
            break;
            
        default:
            throw new Exception('未知的操作类型');
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * 增加奇遇值（战斗胜利后调用）
 */
function addAdventureValue($db, $characterId) {
    try {
        $db->beginTransaction();
        
        // 随机增加1-3点奇遇值
        $addValue = mt_rand(1, 3);
        
        // 获取当前奇遇记录
        $stmt = $db->prepare("
            SELECT current_adventure_value, total_adventure_count 
            FROM user_adventure_records 
            WHERE character_id = ?
        ");
        $stmt->execute([$characterId]);
        $record = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$record) {
            // 如果没有记录，创建新记录
            $stmt = $db->prepare("
                INSERT INTO user_adventure_records (character_id, current_adventure_value, total_adventure_count)
                VALUES (?, ?, 0)
            ");
            $stmt->execute([$characterId, $addValue]);
            $newValue = $addValue;
        } else {
            $newValue = $record['current_adventure_value'] + $addValue;
            
            // 更新奇遇值
            $stmt = $db->prepare("
                UPDATE user_adventure_records 
                SET current_adventure_value = ?, updated_at = CURRENT_TIMESTAMP
                WHERE character_id = ?
            ");
            $stmt->execute([$newValue, $characterId]);
        }
        
        $db->commit();
        
        // 检查是否达到触发条件（1000点）
        $shouldTrigger = $newValue >= 1000;
        
        return [
            'success' => true,
            'added_value' => $addValue,
            'current_value' => $newValue,
            'should_trigger' => $shouldTrigger,
            'message' => "奇遇值增加 {$addValue} 点，当前 {$newValue}/1000"
        ];
        
    } catch (Exception $e) {
        $db->rollBack();
        return [
            'success' => false,
            'message' => '增加奇遇值失败: ' . $e->getMessage()
        ];
    }
}

/**
 * 获取奇遇状态
 */
function getAdventureStatus($db, $characterId) {
    try {
        $stmt = $db->prepare("
            SELECT current_adventure_value, total_adventure_count, last_adventure_time
            FROM user_adventure_records 
            WHERE character_id = ?
        ");
        $stmt->execute([$characterId]);
        $record = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$record) {
            return [
                'success' => true,
                'current_value' => 0,
                'total_count' => 0,
                'last_time' => null,
                'progress_percent' => 0
            ];
        }
        
        $progressPercent = min(100, ($record['current_adventure_value'] / 1000) * 100);
        
        return [
            'success' => true,
            'current_value' => intval($record['current_adventure_value']),
            'total_count' => intval($record['total_adventure_count']),
            'last_time' => $record['last_adventure_time'],
            'progress_percent' => round($progressPercent, 2) // 百分比保留2位小数
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => '获取奇遇状态失败: ' . $e->getMessage()
        ];
    }
}

/**
 * 获取奇遇历史记录
 */
function getAdventureHistory($db, $characterId) {
    try {
        $stmt = $db->prepare("
            SELECT event_id, map_id, adventure_value_before, adventure_value_after, 
                   rewards_received, trigger_time
            FROM adventure_trigger_logs 
            WHERE character_id = ? 
            ORDER BY trigger_time DESC 
            LIMIT 10
        ");
        $stmt->execute([$characterId]);
        $history = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 处理奖励数据
        foreach ($history as &$record) {
            if ($record['rewards_received']) {
                $record['rewards_received'] = json_decode($record['rewards_received'], true);
            }
        }
        
        return [
            'success' => true,
            'history' => $history
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => '获取奇遇历史失败: ' . $e->getMessage()
        ];
    }
}

/**
 * 触发奇遇事件
 */
function triggerAdventureEvent($db, $characterId) {
    try {
        $db->beginTransaction();
        
        // 获取当前奇遇值和角色信息（包括user_id）
        $stmt = $db->prepare("
            SELECT uar.current_adventure_value, c.realm_id, c.pickup_settings, c.user_id
            FROM user_adventure_records uar
            JOIN characters c ON uar.character_id = c.id
            WHERE uar.character_id = ?
        ");
        $stmt->execute([$characterId]);
        $record = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$record || $record['current_adventure_value'] < 1000) {
            throw new Exception('奇遇值不足，无法触发奇遇事件');
        }
        
        // 获取当前挂机地图
        $currentMapId = getCurrentHangupMap($db, $characterId);
        
        // 获取符合条件的奇遇事件
        $availableEvents = getAvailableAdventureEvents($db, $record['realm_id'], $currentMapId);
        
        if (empty($availableEvents)) {
            throw new Exception('当前没有可触发的奇遇事件');
        }
        
        // 随机选择一个事件
        $selectedEvent = $availableEvents[array_rand($availableEvents)];
        
        // 生成奖励
        $rewards = generateAdventureRewards($db, $selectedEvent, $characterId, $currentMapId, $record['realm_id']);
        
        // 重置奇遇值
        $stmt = $db->prepare("
            UPDATE user_adventure_records 
            SET current_adventure_value = 0, 
                total_adventure_count = total_adventure_count + 1,
                last_adventure_time = CURRENT_TIMESTAMP
            WHERE character_id = ?
        ");
        $stmt->execute([$characterId]);
        
        // 记录触发日志
        $stmt = $db->prepare("
            INSERT INTO adventure_trigger_logs 
            (user_id, character_id, event_id, map_id, adventure_value_before, adventure_value_after, rewards_received)
            VALUES (?, ?, ?, ?, ?, 0, ?)
        ");
        $stmt->execute([
            $record['user_id'],
            $characterId, 
            $selectedEvent['id'], 
            $currentMapId, 
            $record['current_adventure_value'],
            json_encode($rewards, JSON_UNESCAPED_UNICODE)
        ]);
        
        // 发放奖励到背包
        $rewardResult = distributeRewards($db, $characterId, $rewards);
        
        $db->commit();
        
        return [
            'success' => true,
            'event_name' => $selectedEvent['event_name'],
            'event_description' => $selectedEvent['description'],
            'rewards' => $rewards,
            'reward_result' => $rewardResult,
            'message' => "触发奇遇事件：{$selectedEvent['event_name']}"
        ];
        
    } catch (Exception $e) {
        $db->rollBack();
        return [
            'success' => false,
            'message' => '触发奇遇事件失败: ' . $e->getMessage()
        ];
    }
}

/**
 * 获取当前挂机地图
 */
function getCurrentHangupMap($db, $characterId) {
    // 这里需要根据实际的挂机系统获取当前地图
    // 暂时返回默认值，后续需要集成实际的挂机系统
    return 1; // 默认太乙峰
}

/**
 * 获取可用的奇遇事件
 */
function getAvailableAdventureEvents($db, $realmId, $mapId) {
    try {
        $stmt = $db->prepare("
            SELECT * FROM adventure_events 
            WHERE is_active = 1 
            AND (min_map_level <= ? OR min_map_level IS NULL)
            AND (max_map_level >= ? OR max_map_level IS NULL)
            ORDER BY probability DESC
        ");
        $stmt->execute([$mapId, $mapId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        error_log("获取奇遇事件失败: " . $e->getMessage());
        return [];
    }
}

/**
 * 生成奇遇奖励（智能分配）
 */
function generateAdventureRewards($db, $event, $characterId, $mapId, $realmId) {
    $rewards = [];
    
    try {
        switch ($event['event_type']) {
            case 'treasure_discovery':
                $rewards = generateTreasureRewards($db, $mapId);
                break;
                
            case 'equipment_find':
                $rewards = generateEquipmentRewards($db, $realmId);
                break;
                
            case 'skill_fragment':
                $rewards = generateSkillFragmentRewards($db);
                break;
                
            case 'precious_pills':
                $rewards = generatePillRewards($db, $characterId, $realmId);
                break;
                
            case 'spirit_stone_treasure':
                $rewards = generateSpiritStoneRewards($db, $mapId);
                break;
                
            case 'recipe_inheritance':
                $rewards = generateRecipeRewards($db, $characterId);
                break;
                
            case 'secret_realm_discovery':
                $rewards = generateSecretRealmRewards($db, $mapId);
                break;
                
            default:
                $rewards = generateDefaultRewards($db, $mapId);
                break;
        }
        
    } catch (Exception $e) {
        error_log("生成奇遇奖励失败: " . $e->getMessage());
        $rewards = generateDefaultRewards($db, $mapId);
    }
    
    return $rewards;
}

/**
 * 生成天材地宝奖励
 */
function generateTreasureRewards($db, $mapId) {
    try {
        // 根据地图等级获取对应的天材地宝
        $mapLevel = getMapLevel($mapId);
        $treasureLevel = min(3, max(1, $mapLevel)); // 限制在1-3级
        
        $stmt = $db->prepare("
            SELECT gi.id, gi.item_name, gi.item_type, gi.icon_image
            FROM game_items gi
            WHERE gi.item_type = 'material' 
            AND gi.item_subtype = 'spiritual_material'
            AND JSON_EXTRACT(gi.special_effects, '$.spiritual_root_boost.value') = ?
            ORDER BY RAND()
            LIMIT 3
        ");
        $stmt->execute([$treasureLevel]);
        $treasures = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $rewards = [];
        foreach ($treasures as $treasure) {
            $rewards[] = [
                'type' => 'item',
                'item_id' => $treasure['id'],
                'item_name' => $treasure['item_name'],
                'quantity' => mt_rand(1, 2),
                'icon_image' => $treasure['icon_image']
            ];
        }
        
        return $rewards;
        
    } catch (Exception $e) {
        error_log("生成天材地宝奖励失败: " . $e->getMessage());
        return [];
    }
}

/**
 * 生成丹药奖励（智能分配）
 */
function generatePillRewards($db, $characterId, $realmId) {
    try {
        $rewards = [];
        
        // 获取角色当前境界信息
        $currentRealmLevel = getCurrentRealmLevel($db, $realmId);
        
        // 优先级1: 符合境界的养魂丹和渡劫丹
        $highPriorityPills = getRealmSpecificPills($db, $currentRealmLevel);
        
        // 优先级2: 未满丹毒限制的属性丹
        $attributePills = getAvailableAttributePills($db, $characterId);
        
        // 合并可用丹药
        $availablePills = array_merge($highPriorityPills, $attributePills);
        
        if (!empty($availablePills)) {
            $selectedPill = $availablePills[array_rand($availablePills)];
            $rewards[] = [
                'type' => 'item',
                'item_id' => $selectedPill['id'],
                'item_name' => $selectedPill['item_name'],
                'quantity' => mt_rand(1, 3),
                'icon_image' => $selectedPill['icon_image']
            ];
        }
        
        return $rewards;
        
    } catch (Exception $e) {
        error_log("生成丹药奖励失败: " . $e->getMessage());
        return [];
    }
}

/**
 * 生成灵石奖励（合理数量）
 */
function generateSpiritStoneRewards($db, $mapId) {
    try {
        // 获取地图的基础灵石产出
        $baseSpiritStone = getMapBaseSpiritStone($db, $mapId);
        
        // 按50-100倍计算奖励
        $multiplier = mt_rand(50, 100);
        $rewardAmount = $baseSpiritStone * $multiplier;
        
        return [
            [
                'type' => 'spirit_stone',
                'amount' => $rewardAmount,
                'description' => "发现灵石宝藏，获得 {$rewardAmount} 灵石"
            ]
        ];
        
    } catch (Exception $e) {
        error_log("生成灵石奖励失败: " . $e->getMessage());
        return [];
    }
}

/**
 * 发放奖励到背包
 */
function distributeRewards($db, $characterId, $rewards) {
    $results = [];
    
    foreach ($rewards as $reward) {
        try {
            if ($reward['type'] === 'item') {
                // 添加物品到背包
                $stmt = $db->prepare("
                    INSERT INTO user_inventories (character_id, item_id, quantity, obtained_source)
                    VALUES (?, ?, ?, 'adventure_reward')
                    ON DUPLICATE KEY UPDATE quantity = quantity + VALUES(quantity)
                ");
                $stmt->execute([$characterId, $reward['item_id'], $reward['quantity']]);
                
                $results[] = [
                    'success' => true,
                    'type' => 'item',
                    'message' => "获得 {$reward['item_name']} x{$reward['quantity']}"
                ];
                
            } elseif ($reward['type'] === 'spirit_stone') {
                // 添加灵石
                $stmt = $db->prepare("
                    UPDATE characters 
                    SET cultivation_points = cultivation_points + ?
                    WHERE id = ?
                ");
                $stmt->execute([$reward['amount'], $characterId]);
                
                $results[] = [
                    'success' => true,
                    'type' => 'spirit_stone',
                    'message' => "获得 {$reward['amount']} 灵石"
                ];
            }
            
        } catch (Exception $e) {
            $results[] = [
                'success' => false,
                'message' => '发放奖励失败: ' . $e->getMessage()
            ];
        }
    }
    
    return $results;
}

// 辅助函数
function getMapLevel($mapId) {
    $mapLevels = [1 => 1, 2 => 1, 3 => 2, 4 => 2, 5 => 2, 6 => 3, 7 => 3, 8 => 3];
    return isset($mapLevels[$mapId]) ? $mapLevels[$mapId] : 1;
}

function getCurrentRealmLevel($db, $realmId) {
    // 这里需要根据实际的境界系统计算境界等级
    return $realmId; // 简化处理
}

function getRealmSpecificPills($db, $realmLevel) {
    // 获取符合境界的养魂丹和渡劫丹
    try {
        $stmt = $db->prepare("
            SELECT id, item_name, icon_image
            FROM game_items 
            WHERE item_type = 'consumable' 
            AND (item_name LIKE '%养魂丹%' OR item_name LIKE '%渡劫丹%')
            AND is_active = 1
            LIMIT 5
        ");
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        return [];
    }
}

function getAvailableAttributePills($db, $characterId) {
    // 获取未满丹毒限制的属性丹
    try {
        $stmt = $db->prepare("
            SELECT id, item_name, icon_image
            FROM game_items 
            WHERE item_type = 'consumable' 
            AND item_name LIKE '%属性丹%'
            AND is_active = 1
            LIMIT 5
        ");
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        return [];
    }
}

function getMapBaseSpiritStone($db, $mapId) {
    // 获取地图基础灵石产出
    $baseSpiritStones = [1 => 10, 2 => 15, 3 => 30, 4 => 45, 5 => 60, 6 => 100, 7 => 150, 8 => 200];
    return isset($baseSpiritStones[$mapId]) ? $baseSpiritStones[$mapId] : 10;
}

function generateEquipmentRewards($db, $realmId) {
    try {
        // 获取境界等级信息
        $stmt = $db->prepare("SELECT level FROM realm_levels WHERE id = ?");
        $stmt->execute([$realmId]);
        $realmInfo = $stmt->fetch(PDO::FETCH_ASSOC);
        $realmLevel = $realmInfo ? $realmInfo['level'] : 5;
        
        // 随机选择装备类型
        $itemTypes = ['weapon', 'armor', 'accessory'];
        $selectedType = $itemTypes[array_rand($itemTypes)];
        
        // 根据境界等级选择合适的装备（允许±10级的范围）
        $minLevel = max(1, $realmLevel - 10);
        $maxLevel = $realmLevel + 10;
        
        $stmt = $db->prepare("
            SELECT id, item_name, item_type, realm_requirement 
            FROM game_items 
            WHERE item_type = ? 
            AND realm_requirement BETWEEN ? AND ?
            ORDER BY ABS(realm_requirement - ?) ASC, RAND()
            LIMIT 1
        ");
        $stmt->execute([$selectedType, $minLevel, $maxLevel, $realmLevel]);
        $equipment = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // 如果没找到合适境界的装备，扩大搜索范围
        if (!$equipment) {
            $stmt = $db->prepare("
                SELECT id, item_name, item_type, realm_requirement 
                FROM game_items 
                WHERE item_type = ? 
                AND realm_requirement <= ?
                ORDER BY realm_requirement DESC, RAND()
                LIMIT 1
            ");
            $stmt->execute([$selectedType, $realmLevel + 20]);
            $equipment = $stmt->fetch(PDO::FETCH_ASSOC);
        }
        
        // 如果还是没找到，使用任意装备
        if (!$equipment) {
            $stmt = $db->prepare("
                SELECT id, item_name, item_type 
                FROM game_items 
                WHERE item_type = ? 
                ORDER BY RAND() 
                LIMIT 1
            ");
            $stmt->execute([$selectedType]);
            $equipment = $stmt->fetch(PDO::FETCH_ASSOC);
        }
        
        if ($equipment) {
            return [
                [
                    'type' => 'equipment',
                    'item_id' => $equipment['id'],
                    'item_name' => $equipment['item_name'],
                    'quality' => 'legendary', // 传说品质
                    'realm_requirement' => $equipment['realm_requirement'],
                    'description' => "获得传说品质装备：{$equipment['item_name']}（境界要求：{$equipment['realm_requirement']}级）"
                ]
            ];
        }
        
        return [];
        
    } catch (Exception $e) {
        error_log("生成装备奖励失败: " . $e->getMessage());
        return [];
    }
}

function generateSkillFragmentRewards($db) {
    // 生成功法碎片奖励
    try {
        $stmt = $db->prepare("
            SELECT id, item_name, icon_image
            FROM game_items 
            WHERE item_subtype = 'technique_fragment'
            ORDER BY RAND()
            LIMIT 1
        ");
        $stmt->execute();
        $fragment = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($fragment) {
            return [
                [
                    'type' => 'item',
                    'item_id' => $fragment['id'],
                    'item_name' => $fragment['item_name'],
                    'quantity' => mt_rand(1, 3),
                    'icon_image' => $fragment['icon_image']
                ]
            ];
        }
        
        return [];
    } catch (Exception $e) {
        return [];
    }
}

function generateRecipeRewards($db, $characterId) {
    // 生成丹方奖励的简化实现
    return [];
}

function generateSecretRealmRewards($db, $mapId) {
    // 生成秘境钥匙奖励
    try {
        $stmt = $db->prepare("
            SELECT id, item_name, icon_image
            FROM game_items 
            WHERE item_subtype = 'secret_key'
            ORDER BY RAND()
            LIMIT 1
        ");
        $stmt->execute();
        $key = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($key) {
            return [
                [
                    'type' => 'item',
                    'item_id' => $key['id'],
                    'item_name' => $key['item_name'],
                    'quantity' => 1,
                    'icon_image' => $key['icon_image']
                ]
            ];
        }
        
        return [];
    } catch (Exception $e) {
        return [];
    }
}

function generateDefaultRewards($db, $mapId) {
    // 默认奖励
    return [
        [
            'type' => 'spirit_stone',
            'amount' => mt_rand(100, 500),
            'description' => '获得少量灵石'
        ]
    ];
}
?> 