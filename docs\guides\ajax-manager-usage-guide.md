# AJAX管理器使用指南

## 📖 简介

项目已经提供了一个统一的AJAX管理系统，让所有表单提交和异步请求体验更加流畅和统一。

**好消息：你的项目已经基本实现了AJAX提交！** 这个管理器是为了进一步优化用户体验。

## 🎯 核心特性

### ✅ 已有功能
- **统一加载状态**: 全局加载遮罩，用户体验更好
- **自动重试机制**: 网络问题时自动重试3次
- **友好错误提示**: 根据错误类型显示对应消息
- **网络状态监控**: 自动检测网络连接状态
- **请求超时处理**: 30秒超时保护
- **移动端适配**: 响应式设计支持

### 🔧 便捷方法
```javascript
// GET请求
const data = await ajaxManager.get('/api/user_info.php');

// POST请求
const result = await ajaxManager.post('/api/login.php', {
    username: 'test',
    password: '123456'
});

// 自定义配置
const response = await ajaxManager.request({
    url: '/api/custom.php',
    method: 'POST',
    body: { data: 'value' },
    loadingText: '自定义加载文字...',
    retryAttempts: 5,
    timeout: 60000
});
```

## 📋 使用方式

### 方式1: 自动表单AJAX化（推荐新表单）

对于新创建的表单，只需添加 `data-ajax="true"` 属性：

```html
<form data-ajax="true" action="../src/api/example.php" method="POST" data-loading="提交中...">
    <input type="text" name="username" required>
    <input type="password" name="password" required>
    <button type="submit">登录</button>
</form>

<script>
// 监听成功事件
document.querySelector('form').addEventListener('ajaxSuccess', (e) => {
    const { result, form } = e.detail;
    if (result.success) {
        console.log('表单提交成功！');
        // 处理成功逻辑
    }
});

// 监听失败事件
document.querySelector('form').addEventListener('ajaxError', (e) => {
    const { error, form } = e.detail;
    console.log('表单提交失败:', error.message);
    // 处理失败逻辑
});
</script>
```

### 方式2: 手动使用AJAX管理器

在现有代码中替换fetch调用：

```javascript
// 原来的代码
fetch('../src/api/login.php', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
})
.then(response => response.json())
.then(result => {
    if (result.success) {
        // 成功处理
    }
})
.catch(error => {
    console.error('错误:', error);
});

// 使用AJAX管理器的代码
try {
    const result = await ajaxManager.post('../src/api/login.php', data, {
        loadingText: '登录中...'
    });
    
    if (result.success) {
        // 成功处理 - 自动显示加载状态和错误处理
    }
} catch (error) {
    // 错误已经自动显示给用户，这里可以做额外处理
    console.log('登录失败:', error.message);
}
```

### 方式3: 兼容性方法

为了保持现有代码兼容，提供了 `unifiedFetch` 方法：

```javascript
// 可以直接替换现有的fetch调用
const result = await unifiedFetch('../src/api/login.php', {
    method: 'POST',
    body: { username: 'test', password: '123456' },
    loadingText: '登录中...'
});
```

## 🎨 配置选项

### 全局配置
```javascript
// 修改全局配置
ajaxManager.globalConfig = {
    timeout: 30000,        // 请求超时时间（毫秒）
    retryAttempts: 3,      // 重试次数
    retryDelay: 1000,      // 重试延迟（毫秒）
    showLoading: true,     // 是否显示加载状态
    autoHideError: true    // 是否自动隐藏错误消息
};
```

### 单次请求配置
```javascript
const result = await ajaxManager.request({
    url: '/api/example.php',
    method: 'POST',
    body: { data: 'value' },
    
    // 显示配置
    loadingText: '自定义加载文字...',
    showLoading: true,
    showError: true,
    autoHideError: false,
    
    // 重试配置
    retryAttempts: 5,
    retryDelay: 2000,
    timeout: 60000,
    
    // 请求头配置
    headers: {
        'Custom-Header': 'value'
    }
});
```

## 📱 移动端适配

AJAX管理器已经包含完整的移动端适配：

- 加载遮罩在小屏幕上自动调整大小
- 错误提示适配移动端显示
- 触摸友好的交互设计

## 🔧 高级功能

### 取消请求
```javascript
// 取消所有进行中的请求
ajaxManager.cancelAllRequests();
```

### 网络状态监控
```javascript
// 自动监控网络状态
// 网络断开时自动显示提示
// 网络恢复时自动隐藏提示
```

### 错误类型处理
管理器会根据不同错误类型显示对应消息：

- **网络断开**: "网络连接已断开，请检查网络后重试"
- **请求超时**: "请求超时，请稍后重试"
- **服务器错误**: "服务器内部错误，请稍后重试"
- **权限不足**: "权限不足，请重新登录"
- **资源不存在**: "请求的资源不存在"

## 🚀 项目集成建议

### 1. 在主布局中引入
```html
<!-- 在所有页面的头部引入 -->
<script src="assets/js/ajax-manager.js"></script>
```

### 2. 现有代码迁移策略

**无需立即修改所有代码**，可以按以下优先级逐步迁移：

1. **高频使用的功能**：登录、注册、修炼等核心功能
2. **新开发的功能**：直接使用AJAX管理器
3. **问题较多的功能**：经常出现网络错误的地方
4. **用户体验关键的功能**：需要更好加载状态的地方

### 3. 保持现有功能完整性

```javascript
// 现有的showMessage函数会被自动使用
// 如果页面已有错误处理，AJAX管理器会优先使用
if (window.showMessage) {
    // 使用现有的消息系统
} else {
    // 使用内置的消息系统
}
```

## 📊 性能优势

### 对比传统方式：

| 功能 | 传统fetch | AJAX管理器 |
|------|-----------|------------|
| 加载状态 | 手动实现 | ✅ 自动处理 |
| 错误处理 | 每次编写 | ✅ 统一处理 |
| 重试机制 | 无 | ✅ 自动重试 |
| 网络监控 | 无 | ✅ 自动监控 |
| 移动端适配 | 手动处理 | ✅ 自动适配 |
| 请求取消 | 复杂实现 | ✅ 一键取消 |

## 🔍 调试和日志

管理器提供详细的调试信息：

```javascript
// 控制台会显示详细日志
// 🚀 AJAX管理器初始化完成
// 📡 AJAX请求失败: Error message
// 🔄 重试请求 (剩余次数: 2)
// 🌐 网络已连接
// 🔌 网络已断开
// ❌ 已取消所有进行中的请求
```

## 💡 最佳实践

### 1. 错误处理
```javascript
try {
    const result = await ajaxManager.post('/api/action.php', data);
    // 成功处理
} catch (error) {
    // 错误已自动显示给用户
    // 这里只需要处理业务逻辑相关的错误处理
    if (error.message.includes('权限')) {
        // 跳转到登录页面
        window.location.href = 'login.html';
    }
}
```

### 2. 表单处理
```javascript
// 推荐：使用事件监听
form.addEventListener('ajaxSuccess', (e) => {
    const result = e.detail.result;
    if (result.success) {
        // 成功后的业务逻辑
        form.reset(); // 重置表单
        updateUI(result.data); // 更新界面
    }
});
```

### 3. 加载文字
```javascript
// 提供有意义的加载文字
await ajaxManager.post('/api/login.php', data, {
    loadingText: '正在验证账号...'
});

await ajaxManager.post('/api/upload.php', formData, {
    loadingText: '正在上传文件...'
});
```

## 🎯 总结

AJAX管理器让你的项目拥有：

- ✅ **统一的用户体验**：所有请求都有一致的加载和错误提示
- ✅ **更好的错误处理**：自动重试和友好的错误消息
- ✅ **更少的代码**：不需要每个请求都写相同的处理逻辑
- ✅ **更好的维护性**：统一管理所有异步请求
- ✅ **完全兼容**：不影响现有功能，可以逐步迁移

**记住：你的项目已经基本实现了AJAX提交，这个管理器只是让体验更上一层楼！** 🎉 