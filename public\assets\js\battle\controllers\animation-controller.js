/**
 * 一念修仙 - 动画控制器
 * 负责管理技能喊话、击中特效、角色动画等
 * 与技能系统配合，提供统一的动画管理
 */

class BattleAnimationController {
    constructor() {
        this.isShoutVisible = false;
        this.shoutQueue = [];
        this.activeAnimations = new Set();
        this.animationCleanupTasks = [];
        
        console.log('🎬 动画控制器初始化完成');
    }
    
    /**
     * 显示技能喊话
     */
    async showSkillShout(skillName, isEnemy = false) {
        // 将喊话加入队列
        this.shoutQueue.push({ skillName, isEnemy });
        
        // 如果当前没有显示喊话，立即显示
        if (!this.isShoutVisible) {
            await this.processShoutQueue();
        }
    }
    
    /**
     * 处理喊话队列
     */
    async processShoutQueue() {
        if (this.shoutQueue.length === 0) {
            this.isShoutVisible = false;
            return;
        }
        
        this.isShoutVisible = true;
        const { skillName, isEnemy } = this.shoutQueue.shift();
        
        try {
            await this.displaySkillShout(skillName, isEnemy);
        } catch (error) {
            console.error('❌ 显示技能喊话失败:', error);
        }
        
        // 继续处理队列中的下一个喊话
        this.processShoutQueue();
    }
    
    /**
     * 显示单个技能喊话
     */
    displaySkillShout(skillName, isEnemy = false) {
        return new Promise((resolve) => {
            // 获取技能喊话文本
            const shoutText = this.getSkillShoutText(skillName);
            
            // 创建喊话元素
            const shoutElement = document.createElement('div');
            shoutElement.className = `skill-shout ${isEnemy ? 'enemy-shout' : 'player-shout'}`;
            shoutElement.textContent = shoutText;
            
            // 设置基础样式
            Object.assign(shoutElement.style, {
                position: 'absolute',
                fontSize: '16px',
                fontWeight: 'bold',
                color: isEnemy ? '#ff6b6b' : '#4ecdc4',
                textShadow: '2px 2px 4px rgba(0, 0, 0, 0.8)',
                zIndex: '1001',
                pointerEvents: 'none',
                opacity: '0',
                transform: 'scale(0.8)',
                transition: 'all 0.3s ease',
                padding: '8px 12px',
                borderRadius: '6px',
                backgroundColor: 'rgba(0, 0, 0, 0.7)',
                border: `2px solid ${isEnemy ? '#ff6b6b' : '#4ecdc4'}`,
                whiteSpace: 'nowrap'
            });
            
            // 获取角色位置并设置喊话位置
            this.positionShoutElement(shoutElement, isEnemy);
            
            // 添加到效果容器
            const effectsContainer = document.querySelector('.effects-container');
            if (effectsContainer) {
                effectsContainer.appendChild(shoutElement);
                
                // 动画显示
                setTimeout(() => {
                    shoutElement.style.opacity = '1';
                    shoutElement.style.transform = 'scale(1)';
                }, 50);
                
                // 自动隐藏
                setTimeout(() => {
                    shoutElement.style.opacity = '0';
                    shoutElement.style.transform = 'scale(0.8) translateY(-20px)';
                    
                    setTimeout(() => {
                        if (shoutElement.parentNode) {
                            shoutElement.parentNode.removeChild(shoutElement);
                        }
                        resolve();
                    }, 300);
                }, 1500);
                
                console.log(`💬 显示技能喊话: ${shoutText} (${isEnemy ? '敌人' : '玩家'})`);
            } else {
                console.warn('⚠️ 效果容器未找到，无法显示技能喊话');
                resolve();
            }
        });
    }
    
    /**
     * 设置喊话元素位置
     */
    positionShoutElement(shoutElement, isEnemy) {
        try {
            const characterSelector = isEnemy ? '.character.enemy' : '.character.player';
            const character = document.querySelector(characterSelector);
            
            if (character) {
                const characterRect = character.getBoundingClientRect();
                const battleContainer = document.querySelector('.battle-container');
                const containerRect = battleContainer.getBoundingClientRect();
                
                // 计算相对于战斗容器的位置
                const relativeX = characterRect.left - containerRect.left;
                const relativeY = characterRect.top - containerRect.top;
                
                // 设置喊话位置（在角色上方）
                shoutElement.style.left = `${relativeX + characterRect.width / 2}px`;
                shoutElement.style.top = `${relativeY - 40}px`;
                shoutElement.style.transform = 'translateX(-50%) scale(0.8)';
            } else {
                // 备用位置
                shoutElement.style.left = isEnemy ? '75%' : '25%';
                shoutElement.style.top = '30%';
                shoutElement.style.transform = 'translateX(-50%) scale(0.8)';
            }
        } catch (error) {
            console.error('❌ 设置喊话位置失败:', error);
            // 使用默认位置
            shoutElement.style.left = isEnemy ? '75%' : '25%';
            shoutElement.style.top = '30%';
            shoutElement.style.transform = 'translateX(-50%) scale(0.8)';
        }
    }
    
    /**
     * 获取技能喊话文本
     */
    getSkillShoutText(skillName) {
        const shoutTexts = {
            // 剑类技能
            '剑气外放！': '御剑飞行，斩妖除魔！',
            '万剑诀': '万剑归宗，诛邪灭恶！',
            '巨剑术': '巨剑开天，破碎虚空！',
            '剑气纵横': '剑气如虹，纵横天地！',
            '剑意凌云': '剑意冲霄，凌云破空！',
            
            // 雷法技能
            '掌心雷': '雷霆万钧，掌心生雷！',
            '天雷滚滚': '天雷滚滚，雷动九霄！',
            '雷电风暴': '雷电风暴，毁天灭地！',
            '九霄神雷': '九霄神雷，诛魔灭妖！',
            '雷霆万钧': '雷霆万钧，震慑群魔！',
            
            // 火法技能
            '火球术': '烈火焚天，火球灭敌！',
            '烈焰冲击': '烈焰冲击，焚烧一切！',
            '火海滔天': '火海滔天，炼狱降临！',
            '凤凰火羽': '凤凰火羽，涅槃重生！',
            '炎龙咆哮': '炎龙咆哮，焚山煮海！',
            
            // 冰法技能
            '冰锥术': '寒冰刺骨，冰锥穿心！',
            '玄冰剑': '玄冰化剑，冰封万物！',
            '冰霜风暴': '冰霜风暴，冻结万物！',
            '极地寒流': '极地寒流，冰封千里！',
            '冰龙吐息': '冰龙吐息，寒气逼人！',
            '万年玄冰': '万年玄冰，永恒冰封！',
            
            // 土法技能
            '土刺术': '大地之怒，土刺冲天！',
            '岩石风暴': '岩石风暴，山崩地裂！',
            '地动山摇': '地动山摇，撼天动地！',
            '巨石碾压': '巨石碾压，粉身碎骨！',
            '大地守护': '大地守护，坚如磐石！',
            
            // 风法技能
            '风刃术': '疾风如刃，切割一切！',
            '龙卷风暴': '龙卷风暴，席卷天地！',
            '风神之怒': '风神之怒，狂风肆虐！',
            '疾风步': '疾风步，身如鬼魅！',
            '风起云涌': '风起云涌，变幻莫测！',
            
            // 木法技能
            '藤蔓缠绕': '藤蔓缠绕，束缚敌人！',
            '荆棘丛生': '荆棘丛生，刺痛入骨！',
            '生命之树': '生命之树，治愈万物！',
            '森林守护': '森林守护，自然之力！',
            '花开花落': '花开花落，生死轮回！',
            
            // 水法技能
            '水球术': '水球术，波涛汹涌！',
            '水龙卷': '水龙卷，席卷一切！',
            '治疗术': '治疗术，回春妙手！',
            '水盾术': '水盾术，柔能克刚！',
            '海啸冲击': '海啸冲击，排山倒海！',
            
            // 光明技能
            '圣光术': '圣光普照，驱散黑暗！',
            '光明审判': '光明审判，正义之光！',
            '治愈之光': '治愈之光，生命复苏！',
            '净化术': '净化术，洗涤罪恶！',
            '光明护盾': '光明护盾，神圣守护！',
            
            // 黑暗技能
            '暗影箭': '暗影箭，穿透灵魂！',
            '黑暗诅咒': '黑暗诅咒，堕入深渊！',
            '死亡凝视': '死亡凝视，灵魂颤栗！',
            '暗影分身': '暗影分身，虚实难辨！',
            '黑暗吞噬': '黑暗吞噬，万物归虚！'
        };
        
        return shoutTexts[skillName] || `${skillName}！`;
    }
    
    /**
     * 创建击中特效
     */
    createHitEffect(x, y, isPlayerAttack = true, effectType = 'normal') {
        try {
            const hitEffect = document.createElement('div');
            hitEffect.className = `hit-effect hit-effect-${effectType}`;
            
            // 设置基础样式
            Object.assign(hitEffect.style, {
                position: 'absolute',
                left: `${x}px`,
                top: `${y}px`,
                width: '60px',
                height: '60px',
                borderRadius: '50%',
                pointerEvents: 'none',
                zIndex: '1002',
                transform: 'translate(-50%, -50%) scale(0)',
                opacity: '1'
            });
            
            // 根据效果类型设置不同的样式
            this.setHitEffectStyle(hitEffect, effectType, isPlayerAttack);
            
            // 添加到效果容器
            const effectsContainer = document.querySelector('.effects-container');
            if (effectsContainer) {
                effectsContainer.appendChild(hitEffect);
                
                // 注册动画
                const animationId = this.registerAnimation();
                
                // 执行击中动画
                this.animateHitEffect(hitEffect, effectType).then(() => {
                    // 清理动画
                    if (hitEffect.parentNode) {
                        hitEffect.parentNode.removeChild(hitEffect);
                    }
                    this.unregisterAnimation(animationId);
                });
                
                console.log(`💥 创建击中特效: ${effectType} at (${x}, ${y})`);
            }
        } catch (error) {
            console.error('❌ 创建击中特效失败:', error);
        }
    }
    
    /**
     * 设置击中特效样式
     */
    setHitEffectStyle(hitEffect, effectType, isPlayerAttack) {
        const effectStyles = {
            normal: {
                background: 'radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,215,0,0.6) 50%, transparent 100%)',
                boxShadow: '0 0 20px rgba(255,215,0,0.8)'
            },
            critical: {
                background: 'radial-gradient(circle, rgba(255,100,100,0.9) 0%, rgba(255,50,50,0.7) 50%, transparent 100%)',
                boxShadow: '0 0 30px rgba(255,50,50,1)'
            },
            magic: {
                background: 'radial-gradient(circle, rgba(100,150,255,0.9) 0%, rgba(50,100,255,0.7) 50%, transparent 100%)',
                boxShadow: '0 0 25px rgba(100,150,255,0.9)'
            },
            fire: {
                background: 'radial-gradient(circle, rgba(255,150,50,0.9) 0%, rgba(255,100,0,0.7) 50%, transparent 100%)',
                boxShadow: '0 0 25px rgba(255,100,0,0.9)'
            },
            ice: {
                background: 'radial-gradient(circle, rgba(150,200,255,0.9) 0%, rgba(100,150,255,0.7) 50%, transparent 100%)',
                boxShadow: '0 0 25px rgba(150,200,255,0.9)'
            },
            lightning: {
                background: 'radial-gradient(circle, rgba(255,255,150,0.9) 0%, rgba(255,255,0,0.7) 50%, transparent 100%)',
                boxShadow: '0 0 30px rgba(255,255,0,1)'
            }
        };
        
        const style = effectStyles[effectType] || effectStyles.normal;
        Object.assign(hitEffect.style, style);
    }
    
    /**
     * 执行击中动画
     */
    animateHitEffect(hitEffect, effectType) {
        return new Promise((resolve) => {
            // 第一阶段：快速放大
            hitEffect.style.transition = 'transform 0.1s ease-out, opacity 0.1s ease-out';
            hitEffect.style.transform = 'translate(-50%, -50%) scale(1.2)';
            
            setTimeout(() => {
                // 第二阶段：震动效果
                hitEffect.style.transition = 'transform 0.05s ease-in-out';
                hitEffect.style.transform = 'translate(-50%, -50%) scale(1) rotate(5deg)';
                
                setTimeout(() => {
                    hitEffect.style.transform = 'translate(-50%, -50%) scale(1) rotate(-5deg)';
                    
                    setTimeout(() => {
                        hitEffect.style.transform = 'translate(-50%, -50%) scale(1) rotate(0deg)';
                        
                        setTimeout(() => {
                            // 第三阶段：消失
                            hitEffect.style.transition = 'transform 0.3s ease-in, opacity 0.3s ease-in';
                            hitEffect.style.transform = 'translate(-50%, -50%) scale(0)';
                            hitEffect.style.opacity = '0';
                            
                            setTimeout(resolve, 300);
                        }, 100);
                    }, 50);
                }, 50);
            }, 100);
        });
    }
    
    /**
     * 创建角色受击动画
     */
    createCharacterHitAnimation(character, isEnemy = false) {
        try {
            if (!character || !character.element) {
                console.warn('⚠️ 角色元素无效，跳过受击动画');
                return;
            }
            
            const characterElement = character.element;
            const originalTransform = characterElement.style.transform || '';
            
            // 注册动画
            const animationId = this.registerAnimation();
            
            // 受击震动动画
            characterElement.style.transition = 'transform 0.1s ease-in-out';
            characterElement.style.transform = originalTransform + ' translateX(-5px)';
            
            setTimeout(() => {
                characterElement.style.transform = originalTransform + ' translateX(5px)';
                
                setTimeout(() => {
                    characterElement.style.transform = originalTransform + ' translateX(-3px)';
                    
                    setTimeout(() => {
                        characterElement.style.transform = originalTransform + ' translateX(3px)';
                        
                        setTimeout(() => {
                            characterElement.style.transform = originalTransform;
                            characterElement.style.transition = '';
                            this.unregisterAnimation(animationId);
                        }, 50);
                    }, 50);
                }, 50);
            }, 50);
            
            console.log(`🎭 创建角色受击动画: ${isEnemy ? '敌人' : '玩家'}`);
        } catch (error) {
            console.error('❌ 创建角色受击动画失败:', error);
        }
    }
    
    /**
     * 注册动画（用于跟踪活动动画）
     */
    registerAnimation() {
        const animationId = Date.now() + Math.random();
        this.activeAnimations.add(animationId);
        return animationId;
    }
    
    /**
     * 注销动画
     */
    unregisterAnimation(animationId) {
        this.activeAnimations.delete(animationId);
    }
    
    /**
     * 清理所有动画
     */
    clearAllAnimations() {
        try {
            // 清理喊话队列
            this.shoutQueue = [];
            this.isShoutVisible = false;
            
            // 清理活动动画
            this.activeAnimations.clear();
            
            // 清理DOM中的动画元素
            const effectsContainer = document.querySelector('.effects-container');
            if (effectsContainer) {
                const animationElements = effectsContainer.querySelectorAll('.skill-shout, .hit-effect');
                animationElements.forEach(element => {
                    if (element.parentNode) {
                        element.parentNode.removeChild(element);
                    }
                });
            }
            
            // 执行清理任务
            this.animationCleanupTasks.forEach(task => {
                try {
                    task();
                } catch (error) {
                    console.error('❌ 执行动画清理任务失败:', error);
                }
            });
            this.animationCleanupTasks = [];
            
            console.log('🧹 清理所有动画完成');
        } catch (error) {
            console.error('❌ 清理动画失败:', error);
        }
    }
    
    /**
     * 添加清理任务
     */
    addCleanupTask(task) {
        if (typeof task === 'function') {
            this.animationCleanupTasks.push(task);
        }
    }
    
    /**
     * 获取动画状态信息
     */
    getAnimationState() {
        return {
            isShoutVisible: this.isShoutVisible,
            shoutQueueLength: this.shoutQueue.length,
            activeAnimationsCount: this.activeAnimations.size,
            cleanupTasksCount: this.animationCleanupTasks.length
        };
    }
    
    /**
     * 暂停所有动画
     */
    pauseAllAnimations() {
        try {
            const effectsContainer = document.querySelector('.effects-container');
            if (effectsContainer) {
                effectsContainer.style.animationPlayState = 'paused';
            }
            console.log('⏸️ 暂停所有动画');
        } catch (error) {
            console.error('❌ 暂停动画失败:', error);
        }
    }
    
    /**
     * 恢复所有动画
     */
    resumeAllAnimations() {
        try {
            const effectsContainer = document.querySelector('.effects-container');
            if (effectsContainer) {
                effectsContainer.style.animationPlayState = 'running';
            }
            console.log('▶️ 恢复所有动画');
        } catch (error) {
            console.error('❌ 恢复动画失败:', error);
        }
    }
}

// 导出动画控制器类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BattleAnimationController;
} else {
    window.BattleAnimationController = BattleAnimationController;
} 