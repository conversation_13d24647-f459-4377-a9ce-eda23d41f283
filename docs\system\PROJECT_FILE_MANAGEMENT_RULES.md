# 📁 一念修仙项目文件管理规范

*制定时间: 2025年6月17日 17:45*
*适用版本: v1.0+*

## 🎯 文件管理原则

### 📍 核心理念
- **目录清晰**: 每个文件都有明确的归属目录
- **用途明确**: 文件名和位置能清楚表达其用途
- **及时清理**: 临时文件、调试文件不滞留
- **版本控制**: 重要文件变更都有记录

## 📂 根目录文件规范

### ✅ 允许的根目录文件
```
yinian/
├── README.md                    # 项目主说明文档
├── DATABASE_STRUCTURE.md       # 数据库结构文档  
├── PROJECT_STATUS.md           # 项目状态文档
├── GAME_DEVELOPMENT_DOCS.md    # 游戏开发文档
├── .cursorrules                # Cursor IDE配置
├── index.php                   # 项目入口文件
├── docs/                       # 文档目录
├── src/                        # 后端源码
├── public/                     # 前端资源
├── scripts/                    # 脚本工具
├── sql/                        # SQL脚本
├── tools/                      # 工具文件
├── logs/                       # 日志文件
├── database/                   # 数据库相关
├── admin/                      # 管理后台
├── data/                       # 数据备份
├── backend/                    # 后端支持
├── archive/                    # 归档文件
└── .cursor/                    # IDE配置
```

### 🚫 禁止的根目录文件
- 调试文件 (`debug_*.php`, `test_*.html`)
- 临时结果文件 (`*_result.txt`, `*_backup.json`)
- 修复脚本 (`fix_*.php`)
- 临时测试页面
- 任何带 `-temp`, `-backup`, `-copy` 后缀的文件

## 📚 docs/ 目录结构规范

### 🏗️ 标准目录分类
```
docs/
├── README.md                   # 文档中心导航
├── system/                     # 系统架构文档
│   ├── PROJECT_DIRECTORY_STRUCTURE.md
│   ├── ARCHITECTURE_ANALYSIS.md
│   ├── PROJECT_OVERVIEW.md
│   ├── DOCUMENTATION_INDEX.md
│   └── spirit_root_system.md
├── guides/                     # 开发指南
│   ├── auth_integration_guide.md
│   ├── ajax-manager-usage-guide.md
│   ├── 战斗系统控制台屏蔽指南.md
│   ├── 技能开发指南_v3.0.md
│   ├── 新技能模型添加完整指南.md
│   └── 怪物技能设计开发指南.md
├── completed/                  # 已完成项目
│   ├── 战斗系统模块化完成总结.md
│   ├── 掉落系统重构完成报告.md
│   ├── AI_SYSTEM_FINAL_STATUS.md
│   └── ...
├── planning/                   # 开发计划
│   ├── 秘境系统开发计划.md
│   ├── 奇遇系统和灵根系统完善执行计划.md
│   └── ...
├── archived/                   # 归档文档
│   ├── 历练地图境界掉落系统设计方案.md
│   └── ...
├── 技能开发模板/              # 开发模板
├── refactoring/               # 重构文档
└── development/               # 开发文档
```

### 📝 文档分类标准

#### system/ - 系统架构类
- 项目整体架构设计
- 系统核心功能说明
- 数据库设计文档
- 项目概览和目录结构

#### guides/ - 开发指南类  
- 功能开发操作指南
- 最佳实践说明
- 工具使用手册
- 问题解决方案

#### completed/ - 已完成类
- 系统重构完成报告
- 功能实现总结
- 优化完成记录
- 修复完成报告

#### planning/ - 规划计划类
- 未来功能开发计划
- 系统优化计划
- 测试计划
- 执行指南

#### archived/ - 归档类
- 过时的设计方案
- 已不再使用的文档
- 历史版本记录
- 废弃功能说明

## 🧹 文件清理规范

### 🗑️ 定期清理项目
**每周执行清理检查:**
1. 删除根目录下的临时文件
2. 移动过时文档到 `archived/` 目录
3. 将完成的计划移到 `completed/` 目录
4. 清理空目录和重复文件

### 🚨 立即清理的文件
- 以 `debug_`, `test_`, `fix_` 开头的临时文件
- 以 `_result.txt`, `_backup.json` 结尾的结果文件
- 任何包含 `temp`, `backup`, `copy` 的文件名
- 空文件和损坏文件

### ✅ 清理检查清单
- [ ] 根目录是否有临时文件
- [ ] docs目录结构是否规范
- [ ] 是否有重复或过时文档
- [ ] 文件命名是否符合规范
- [ ] 归档文档是否及时移动

## 🔄 文件生命周期管理

### 📋 新文件创建流程
1. **确定用途**: 明确文件的用途和目标用户
2. **选择位置**: 根据分类标准选择合适目录
3. **命名规范**: 使用清晰、描述性的文件名
4. **更新索引**: 在相关README中添加文档说明

### 📊 文件状态转换
```
Planning → Development → Completed → Archived
   ↓           ↓           ↓          ↓
planning/   guides/   completed/  archived/
```

### 🗄️ 归档规则
- **完成项目**: 计划类文档移到 `completed/`
- **过时设计**: 不再使用的设计移到 `archived/`
- **废弃功能**: 已删除功能的文档移到 `archived/`
- **版本升级**: 旧版本文档移到 `archived/`

## 📈 项目成长适应

### 🔧 目录扩展规则
当项目需要新的文档类型时:
1. 在 `docs/` 下创建新的分类目录
2. 更新 `docs/README.md` 说明新目录用途
3. 制定该目录的文件管理规则
4. 更新本文档的目录结构说明

### 📝 命名约定
- **目录名**: 使用英文小写，单词用下划线分隔
- **文件名**: 中文用中文，英文用英文，避免混用
- **版本标记**: 使用 `v1.0`, `v2.0` 等版本号
- **状态标记**: 使用 `_draft`, `_final` 等状态后缀

## 🛡️ 文件保护规则

### 🔒 重要文件保护
- 核心配置文件定期备份
- 重要文档变更前先备份
- 数据库相关文件特别保护
- 生产环境文件严格权限控制

### ⚠️ 危险操作警告
- **批量删除**: 需要仔细确认文件列表
- **目录移动**: 检查是否有其他文件引用
- **重命名**: 确保没有硬编码的文件路径
- **权限修改**: 避免过度开放文件权限

## 📊 监控和维护

### 📈 定期检查
- **每周**: 根目录文件清理检查
- **每月**: docs目录结构优化
- **每季度**: 整体文件架构评估
- **每年**: 归档策略评估

### 📋 检查工具
- 使用脚本自动检查临时文件
- 定期统计各目录文件数量
- 监控大文件和重复文件
- 检查断链和无效引用

---

## 📞 问题反馈

发现文件管理问题或有改进建议时:
1. 立即清理发现的问题文件
2. 在项目群组或Issue中反馈
3. 提出改进建议和解决方案
4. 更新相应的管理规范

---

*本规范将根据项目发展持续优化更新*  
*最近更新: 2025年6月17日 17:45* 