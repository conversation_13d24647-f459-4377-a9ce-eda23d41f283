// 境界系统工具类
class RealmSystem {
    // 人界境界
    static HUMAN_REALMS = [
        '开光', '灵虚', '辟谷', '心动', '元化', 
        '元婴', '离合', '空冥', '寂灭', '大乘',
        '渡劫', '凡仙', '地仙', '天仙'
    ];

    // 仙界境界
    static IMMORTAL_REALMS = [
        '真仙', '太乙真仙', '太乙金仙', '太乙玄仙', 
        '大罗真仙', '大罗金仙', '大罗玄仙', '准圣',
        '教主', '混元', '混元金仙', '混元至仙',
        '天道', '鸿蒙至元'
    ];

    // 小境界名称
    static SUB_REALM_NAMES = [
        '一阶', '二阶', '三阶', '四阶',
        '五阶', '六阶', '七阶', '八阶',
        '九阶', '大圆满'
    ];

    /**
     * 将数字等级转换为境界描述
     * @param {number} level 数字等级
     * @param {boolean} showSubRealm 是否显示小境界
     * @returns {string} 境界描述
     */
    static getLevelRealm(level, showSubRealm = true) {
        if (!level || level < 1) return '凡人';
        
        // 计算大境界和小境界
        const realmLevel = Math.ceil(level / 10); // 每10级一个大境界
        const subRealmLevel = level % 10 || 10; // 1-10的小境界
        
        // 确定是人界还是仙界
        let realm;
        if (realmLevel <= 14) {
            // 人界境界
            realm = this.HUMAN_REALMS[realmLevel - 1];
        } else {
            // 仙界境界
            const immortalRealmIndex = realmLevel - 15;
            if (immortalRealmIndex >= this.IMMORTAL_REALMS.length) {
                realm = this.IMMORTAL_REALMS[this.IMMORTAL_REALMS.length - 1];
            } else {
                realm = this.IMMORTAL_REALMS[immortalRealmIndex];
            }
        }

        // 是否显示小境界
        if (showSubRealm) {
            const subRealm = this.SUB_REALM_NAMES[subRealmLevel - 1];
            return `${realm}期 ${subRealm}`;
        }
        
        return `${realm}期`;
    }

    /**
     * 获取境界的简短描述
     * @param {number} level 数字等级
     * @returns {string} 简短境界描述
     */
    static getShortRealm(level) {
        return this.getLevelRealm(level, false);
    }

    /**
     * 获取完整的境界描述（包含具体数值）
     * @param {number} level 数字等级
     * @returns {string} 完整境界描述
     */
    static getFullRealm(level) {
        return `${this.getLevelRealm(level)} (${level}级)`;
    }

    /**
     * 获取境界图标
     * @param {number} level 数字等级
     * @returns {string} 境界对应的图标
     */
    static getRealmIcon(level) {
        const realmLevel = Math.ceil(level / 10);
        if (realmLevel <= 14) {
            // 根据境界等级返回不同图标
            const realmIcons = {
                1: '🔵', // 开光
                2: '🟣', // 灵虚  
                3: '🟡', // 辟谷
                4: '🟢', // 心动
                5: '🟤', // 元化
                6: '⚪', // 元婴
                7: '🔴', // 离合
                8: '⭐', // 空冥
                9: '🌟', // 寂灭
                10: '💫', // 大乘
                11: '⚡', // 渡劫
                12: '🌙', // 凡仙
                13: '🌞', // 地仙
                14: '🌈', // 天仙
            };
            
            // 凡界使用对应图标,仙界统一使用✨
            return realmIcons[realmLevel] || '✨';
        }
    }

    /**
     * 判断是否达到突破条件
     * @param {number} level 当前等级
     * @param {number} exp 当前经验
     * @param {number} requiredExp 需要经验
     * @returns {boolean} 是否可以突破
     */
    static canBreakthrough(level, exp, requiredExp) {
        return exp >= requiredExp;
    }

    /**
     * 获取境界颜色
     * @param {number} level 数字等级
     * @returns {string} 颜色代码
     */
    static getRealmColor(level) {
        const realmLevel = Math.ceil(level / 10);
        if (realmLevel <= 14) {
            // 人界颜色
            const colors = [
                '#ffffff', // 开光
                '#c0c0c0', // 灵虚
                '#ffb6c1', // 辟谷
                '#87ceeb', // 心动
                '#9370db', // 元化
                '#4169e1', // 元婴
                '#9932cc', // 离合
                '#8a2be2', // 空冥
                '#4b0082', // 寂灭
                '#800080', // 大乘
                '#ff4500', // 渡劫
                '#ffd700', // 凡仙
                '#daa520', // 地仙
                '#ffa500'  // 天仙
            ];
            return colors[realmLevel - 1] || colors[0];
        } else {
            // 仙界颜色
            return '#00ffff';
        }
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = RealmSystem;
} 