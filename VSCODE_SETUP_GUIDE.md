# 一念修仙项目 VSCode 开发环境配置指南

## 🎯 配置状态

✅ **所有配置已完成并验证通过！**

根据配置检查结果，您的开发环境已经完全配置好，包括：
- PHP 7.4.3 环境正常
- 数据库连接成功 (yn_game, 44个表)
- 所有必要目录存在
- VSCode配置文件完整
- 目录权限正确

## 🔧 已配置的功能

### 1. 核心开发工具
- **PHP Intelephense**: 智能代码补全和语法检查
- **PHP Debug**: 断点调试支持
- **SQLTools**: 数据库管理和查询
- **Live Server**: 实时预览
- **REST Client**: API测试

### 2. 代码质量工具
- **Prettier**: 自动代码格式化
- **ESLint**: JavaScript代码检查
- **PHP CodeSniffer**: PHP代码规范检查

### 3. 项目专用配置
- **数据库连接**: 已配置yn_game数据库连接
- **API测试**: 预设15个常用API测试用例
- **代码片段**: PHP和JavaScript游戏开发专用片段
- **SQL查询**: 常用数据库查询模板

## 🚀 使用指南

### 数据库管理
1. 按 `Ctrl+Shift+P` 打开命令面板
2. 输入 `SQLTools: Connect` 选择"一念修仙-主数据库"
3. 使用 `.vscode/sql_queries.sql` 中的预设查询

### API测试
1. 打开 `.vscode/api_test.http` 文件
2. 点击请求上方的 "Send Request" 按钮
3. 查看响应结果

### PHP调试
1. 在代码中设置断点
2. 按 `F5` 启动调试
3. 选择 "Listen for Xdebug" 配置

### 代码片段使用
在PHP文件中输入：
- `api-response` - 标准API响应格式
- `db-connect` - 数据库连接代码
- `game-api` - 完整游戏API模板
- `session-check` - 用户登录检查

在JavaScript文件中输入：
- `api-call` - 游戏API调用模板
- `config-check` - 配置检查
- `update-data` - 数据更新函数

## 📋 快捷键

### 常用操作
- `Ctrl+Shift+P`: 命令面板
- `Ctrl+P`: 快速打开文件
- `F5`: 启动调试
- `Ctrl+F5`: 运行不调试
- `Ctrl+Shift+F`: 全局搜索

### 代码编辑
- `Alt+Shift+F`: 格式化代码
- `Ctrl+/`: 切换注释
- `F12`: 跳转到定义
- `Shift+F12`: 查找所有引用

### 数据库操作
- `Ctrl+Shift+P` → `SQLTools: Connect`: 连接数据库
- `Ctrl+Shift+P` → `SQLTools: Run Query`: 执行查询

## 🛠️ 自定义配置

### 修改数据库连接
编辑 `.vscode/settings.json` 中的 `sqltools.connections` 部分

### 添加新的代码片段
编辑 `.vscode/snippets/php.json` 或 `.vscode/snippets/javascript.json`

### 修改API测试
编辑 `.vscode/api_test.http` 添加新的测试用例

## 🔍 故障排除

### 如果PHP智能提示不工作
1. 检查 `.vscode/settings.json` 中的PHP路径
2. 重启VSCode
3. 按 `Ctrl+Shift+P` → `PHP: Restart Language Server`

### 如果数据库连接失败
1. 确认PHPStudy Pro正在运行
2. 检查数据库用户名密码
3. 运行 `php check_vscode_config.php` 检查配置

### 如果调试不工作
1. 确认Xdebug已安装并配置
2. 检查 `.vscode/launch.json` 中的端口设置
3. 确认PHP配置中启用了Xdebug

## 📈 性能优化建议

1. **排除不必要的文件**: 已配置排除 `temp_*`, `test_*`, `debug_*` 文件
2. **自动保存**: 已启用1秒延迟自动保存
3. **智能提示**: 已配置包含路径优化

## 🎮 游戏开发专用功能

### API路径标准化
使用配置的API路径格式：
```javascript
window.GameConfig ? window.GameConfig.getApiUrl('endpoint') : '../src/api/endpoint'
```

### 数据库操作模板
使用预设的SQL查询模板进行常见操作：
- 用户和角色查询
- 装备和背包管理
- 战斗记录分析
- 系统统计报告

### 调试工具
- `check_vscode_config.php`: 环境配置检查
- API测试文件: 完整的接口测试覆盖
- SQL查询模板: 常用数据库操作

---

**配置完成时间**: 2025年6月28日  
**环境状态**: ✅ 完全就绪  
**下一步**: 开始愉快的游戏开发！
