# 🎮 一念修仙 - 网页修仙类游戏

## 📖 项目简介

一念修仙是一个基于PHP + MySQL + JavaScript开发的网页修仙类游戏，提供完整的修仙体验，包括修炼系统、战斗系统、装备系统、炼丹系统等丰富功能。

## ✨ 最新更新 (2025年6月17日)

### 🗺️ 地图进度修为系统 - 重大功能更新
- **新增机制**: 每通关1关增加1点基础修为
- **计算公式**: 地图进度修为 = max(0, 最高通关关卡数 - 1)
- **系统整合**: 基础修为 = 功法基础修为 + 地图进度修为
- **激励效果**: 鼓励玩家积极冒险，修炼与冒险完美联动

## 🎯 核心功能

### 🏔️ 修炼系统
- **280个境界等级**: 从练气期到鸿蒙至元期的完整修炼体系
- **功法学习**: 支持多功法学习，功法碎片合成机制
- **地图进度修为**: ✨ 冒险进度直接提升修炼效率
- **离线修炼**: 支持48小时离线挂机修炼

### ⚔️ 战斗系统
- **回合制战斗**: 策略性战斗，支持技能、普攻、防御
- **169个武器技能**: 剑法、法术等多样化技能系统
- **奇遇系统**: 战斗胜利累积奇遇值，触发神秘事件

### 🎒 装备系统
- **1001件装备**: 剑修/法修分化，涵盖武器、护甲、饰品等多种类型
- **品质系统**: 普通、稀有、史诗、传说、神话五个品质
- **随机属性**: 动态属性生成，装备个性化

### 🗺️ 冒险系统
- **8个历练地图**: 1015个关卡的庞大冒险世界
- **多样化怪物**: 丰富的怪物生态系统，每关卡独特配置
- **掉落系统**: 智能掉落算法，装备品质动态生成

## 📊 项目规模 (2025年6月25日统计)

- **前端页面**: 18个 (14个主页面 + 4个管理页面)
- **API接口**: 31个
- **装备总数**: 1001件
- **武器技能**: 169个
- **境界等级**: 280个
- **地图关卡**: 1015个
- **地图总数**: 8个
- **奇遇物品**: 96个
- **数据库表**: 44个

## 🛠️ 技术架构

### 后端技术栈
- **PHP**: 7.4.3nts
- **MySQL**: 5.7+
- **Web服务器**: Nginx/Apache

### 前端技术栈
- **HTML5 + CSS3 + JavaScript**: 原生技术栈
- **响应式设计**: 支持桌面和移动端
- **PWA支持**: 离线访问能力

### 数据库设计
- **数据库**: yn_game
- **字符集**: utf8mb4
- **引擎**: InnoDB
- **表结构**: 44个表的完整游戏系统

## 🎮 游戏特色

### 🌟 五行灵根系统
- **天赋属性**: 金木水火土五行灵根影响角色天赋
- **天材地宝**: 85个天材地宝可提升对应灵根
- **品质分级**: 废灵根到极品灵根的五级品质系统

### 🎲 奇遇系统
- **神秘事件**: 7种奇遇事件类型
- **智能奖励**: 根据角色状态智能分配奖励
- **稀有物品**: 功法碎片、秘境钥匙等珍贵物品

### 🧪 炼丹系统
- **丹药炼制**: 属性丹、渡劫丹、养魂丹等
- **材料收集**: 怪物掉落材料的完整生态

## 📋 系统完成度

- **核心功能**: 98% 完成
- **用户系统**: 100% ✅
- **修炼系统**: 100% ✅ (含地图进度修为)
- **战斗系统**: 100% ✅
- **装备系统**: 100% ✅
- **冒险系统**: 100% ✅
- **奇遇系统**: 100% ✅
- **秘境系统**: 待开发 🔄

## 🚀 快速开始

1. **环境要求**: PHP 7.4+ + MySQL 5.7+ + Web服务器
2. **数据库配置**:
   - 创建数据库 `yn_game`
   - 配置用户 `ynxx` 并授权
   - 修改 `src/config/database.php` 中的连接信息
3. **文件部署**: 上传项目文件到web目录
4. **访问游戏**: 打开 `public/index.html` 开始游戏

---

**最后更新**: 2025年6月25日
**版本状态**: v1.0 (生产就绪)
**项目完成度**: 98%