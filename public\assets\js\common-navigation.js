// 通用底部导航栏组件
function createBottomNavigation() {
    const nav = document.createElement('div');
    nav.className = 'bottom-navigation';
    nav.innerHTML = `
        <a href="attributes.html" class="nav-btn" data-page="attributes">
            <span class="nav-btn-icon">📊</span>
            <span class="nav-btn-text">属性</span>
        </a>
        <a href="equipment_integrated.html" class="nav-btn" data-page="equipment">
            <span class="nav-btn-icon">💍</span>
            <span class="nav-btn-text">纳戒</span>
        </a>
        <a href="game.html" class="nav-btn" data-page="home">
            <span class="nav-btn-icon">🏠</span>
            <span class="nav-btn-text">主页</span>
        </a>
        <a href="adventure.html" class="nav-btn" data-page="map">
            <span class="nav-btn-icon">⚔️</span>
            <span class="nav-btn-text">历练</span>
        </a>
        <a href="settings.html" class="nav-btn" data-page="settings">
            <span class="nav-btn-icon">⚙️</span>
            <span class="nav-btn-text">设置</span>
        </a>
    `;

    // 设置当前页面的激活状态
    const currentPage = getCurrentPage();
    const activeButton = nav.querySelector(`[data-page="${currentPage}"]`);
    if (activeButton) {
        activeButton.classList.add('active');
    }

    // 添加事件监听 - 🔧 修改：只有在确实是同一个页面时才阻止跳转
    nav.querySelectorAll('.nav-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            const targetPage = this.getAttribute('data-page');
            const currentPageExact = getCurrentPageExact(); // 使用更精确的页面检测
            
            // 🔧 修改：只有在确实是同一个页面时才阻止跳转
            if (targetPage === currentPageExact) {
                e.preventDefault();
                showMessage('当前已在该页面', 'info');
            }
            // 🔧 对于其他情况（如在子页面点击主页），允许正常跳转
        });
    });

    return nav;
}

// 获取当前页面标识（用于导航高亮显示）
function getCurrentPage() {
    const path = window.location.pathname;
    if (path.includes('index.html')) return 'index';
    if (path.includes('game.html')) return 'home';
    if (path.includes('equipment_integrated.html')) return 'equipment';
    if (path.includes('attributes.html')) return 'attributes';
    if (path.includes('adventure.html')) return 'map';
    if (path.includes('battle.html')) return 'map';
    if (path.includes('settings.html')) return 'settings';
    // 🔧 修改：对于子页面（炼丹、修炼、商店等），显示主页高亮，但不阻止跳转
    return 'home';
}

// 🔧 新增：获取精确的当前页面标识（用于跳转判断）
function getCurrentPageExact() {
    const path = window.location.pathname;
    
    // 🔧 新增：检测页面中是否有活跃的弹窗或详情界面
    const hasActiveModal = isModalActive();
    
    // 基础页面检测
    if (path.includes('index.html')) {
        return hasActiveModal ? 'index-modal' : 'index';
    }
    if (path.includes('game.html')) {
        return hasActiveModal ? 'home-modal' : 'home';
    }
    if (path.includes('equipment_integrated.html')) {
        return hasActiveModal ? 'equipment-modal' : 'equipment';
    }
    if (path.includes('attributes.html')) {
        return hasActiveModal ? 'attributes-modal' : 'attributes';
    }
    if (path.includes('adventure.html')) {
        return hasActiveModal ? 'map-modal' : 'map';
    }
    if (path.includes('battle.html')) {
        return hasActiveModal ? 'map-modal' : 'map';
    }
    if (path.includes('settings.html')) {
        return hasActiveModal ? 'settings-modal' : 'settings';
    }
    
    // 🔧 关键修改：对于子页面，返回特殊标识而不是'home'
    if (path.includes('cultivation.html')) return 'cultivation';
    if (path.includes('alchemy.html')) return 'alchemy';
    if (path.includes('shop.html')) return 'shop';
    if (path.includes('market.html')) return 'market';
    if (path.includes('blackmarket.html')) return 'blackmarket';
    
    // 其他未明确识别的页面返回当前路径，确保不会误判
    return path;
}

// 🔧 新增：检测当前页面是否有活跃的弹窗或详情界面
function isModalActive() {
    // 检测常见的弹窗选择器
    const modalSelectors = [
        // 通用弹窗
        '.modal:not([style*="display: none"])',
        '.popup:not([style*="display: none"])',
        '.overlay:not([style*="display: none"])',
        
        // 属性页面弹窗
        '.attribute-detail-modal:not([style*="display: none"])',
        '.attribute-popup-overlay:not([style*="display: none"])',
        
        // 装备页面弹窗
        '.avatar-modal:not([style*="display: none"])',
        '.recycle-confirm-popup:not([style*="display: none"])',
        '#item-detail-popup:not([style*="display: none"])',
        '.popup-overlay:not([style*="display: none"])',
        
        // 修炼页面弹窗
        '.technique-detail-modal:not([style*="display: none"])',
        '.pill-detail-modal:not([style*="display: none"])',
        
        // 炼丹页面弹窗
        '.recipe-detail-modal:not([style*="display: none"])',
        '.furnace-detail-modal:not([style*="display: none"])',
        
        // 商店页面弹窗
        '.item-preview-modal:not([style*="display: none"])',
        '.purchase-confirm-modal:not([style*="display: none"])',
        
        // 其他可能的弹窗
        '[class*="modal"]:not([style*="display: none"])',
        '[class*="popup"]:not([style*="display: none"])',
        '[class*="detail"]:not([style*="display: none"])',
        '[id*="modal"]:not([style*="display: none"])',
        '[id*="popup"]:not([style*="display: none"])'
    ];
    
    // 检查是否有任何弹窗是可见的
    for (const selector of modalSelectors) {
        try {
            const elements = document.querySelectorAll(selector);
            for (const element of elements) {
                // 检查元素是否真正可见
                if (element && element.offsetParent !== null) {
                    // 对于简化的选择器，额外检查display样式
                    if (!selector.includes(':not(')) {
                        const computedStyle = window.getComputedStyle(element);
                        if (computedStyle.display === 'none' || computedStyle.visibility === 'hidden') {
                            continue; // 跳过不可见的元素
                        }
                    }
                    console.log('检测到活跃弹窗:', selector, element);
                    return true;
                }
                // 对于有visibility检查的选择器，检查display样式
                if (selector.includes(':not(')) {
                const computedStyle = window.getComputedStyle(element);
                if (computedStyle.display !== 'none' && computedStyle.visibility !== 'hidden') {
                    console.log('检测到活跃弹窗（样式检查）:', selector, element);
                    return true;
                    }
                }
            }
        } catch (e) {
            // 忽略选择器错误，继续检查下一个
            console.warn('选择器检查失败:', selector, e.message);
            continue;
        }
    }
    
    return false;
}

// 显示消息提示
function showMessage(text, type) {
    const existingMessage = document.querySelector('.message');
    if (existingMessage) {
        existingMessage.remove();
    }
    
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        padding: 12px 20px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: bold;
        z-index: 1000;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        backdrop-filter: blur(10px);
        background: linear-gradient(135deg, #2196F3, #1976D2);
        color: white;
        border: 1px solid #2196F3;
    `;
    messageDiv.textContent = text;
    document.body.appendChild(messageDiv);
    
    setTimeout(() => {
        messageDiv.remove();
    }, 2000);
}

// 在页面加载完成后初始化底部导航
document.addEventListener('DOMContentLoaded', function() {
    const container = document.querySelector('.bottom-navigation');
    if (container) {
        container.replaceWith(createBottomNavigation());
    } else {
        document.body.appendChild(createBottomNavigation());
    }
}); 