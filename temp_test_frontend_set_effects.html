<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>套装特殊效果前端测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .test-button:hover {
            transform: scale(1.05);
        }
        .test-button.danger {
            background: linear-gradient(135deg, #f44336, #d32f2f);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .log-container {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background: #f8f9fa;
            font-family: monospace;
            font-size: 12px;
        }
        .shield-test {
            border: 2px solid #4da6ff;
            background: rgba(77, 166, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <h1>🛡️ 套装特殊效果前端测试</h1>
    
    <div class="test-section">
        <h2>📋 测试说明</h2>
        <p>此页面用于测试套装特殊效果在前端的实际工作情况。</p>
        <div class="info status">
            <strong>后端测试结果显示：</strong>
            <ul>
                <li>✅ 角色装备了6件星衣道尊套装</li>
                <li>✅ 6件套效果：战斗开始时获得最大生命值20%的护盾</li>
                <li>✅ 4件套效果：受到攻击时有20%概率反弹30%伤害</li>
                <li>✅ 战斗API正确返回套装效果数据</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>🔧 测试工具</h2>
        
        <button class="test-button" onclick="testBattleAPI()">
            📊 测试战斗API数据
        </button>
        
        <button class="test-button" onclick="openBattlePage()">
            ⚔️ 打开战斗页面
        </button>
        
        <button class="test-button" onclick="simulateSetEffects()">
            🧪 模拟套装效果
        </button>
        
        <button class="test-button danger" onclick="clearLogs()">
            🗑️ 清空日志
        </button>
        
        <div id="testResults"></div>
    </div>

    <div class="test-section shield-test">
        <h2>🛡️ 护盾系统测试重点</h2>
        <div class="warning status">
            <strong>需要检查的关键点：</strong>
            <ol>
                <li><strong>战斗初始化</strong>：控制台是否显示"🔥 套装效果触发检查 - 战斗开始时"</li>
                <li><strong>护盾计算</strong>：控制台是否显示"🛡️ 护盾效果: +XXX (总护盾: XXX)"</li>
                <li><strong>UI显示</strong>：HP条旁边是否显示蓝色"🛡️XXX"护盾值</li>
                <li><strong>伤害吸收</strong>：受到攻击时护盾是否正确减少</li>
                <li><strong>视觉效果</strong>：护盾吸收时是否显示蓝色动画效果</li>
                <li><strong>反弹效果</strong>：受到攻击时是否有20%概率触发反弹</li>
            </ol>
        </div>
    </div>

    <div class="test-section">
        <h2>📝 实时日志</h2>
        <div id="logContainer" class="log-container">
            <div>等待测试开始...</div>
        </div>
    </div>

    <script>
        let logContainer = document.getElementById('logContainer');
        let testResults = document.getElementById('testResults');

        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span style="color: #666;">[${timestamp}]</span> ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function showResult(message, type = 'info') {
            const resultElement = document.createElement('div');
            resultElement.className = `status ${type}`;
            resultElement.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            testResults.appendChild(resultElement);
            testResults.scrollTop = testResults.scrollHeight;
        }

        async function testBattleAPI() {
            try {
                addLog('🔄 正在测试战斗API...', 'info');
                showResult('🔄 正在测试战斗API数据获取...', 'info');
                
                const response = await fetch('../src/api/battle_unified.php?action=init_battle&map_id=1&map_code=kunlun&stage_number=1');
                const result = await response.json();
                
                if (result.success && result.data && result.data.player_data) {
                    const playerData = result.data.player_data;
                    addLog('✅ 战斗API调用成功');
                    showResult('✅ 战斗API调用成功', 'success');
                    
                    if (playerData.set_special_effects && playerData.set_special_effects.length > 0) {
                        addLog(`📊 找到${playerData.set_special_effects.length}个套装特殊效果:`);
                        showResult(`📊 找到${playerData.set_special_effects.length}个套装特殊效果`, 'success');
                        
                        playerData.set_special_effects.forEach((effect, index) => {
                            addLog(`  ${index + 1}. ${effect.set_name}: ${effect.effect}`);
                            
                            // 检查护盾效果
                            if (effect.effect.includes('护盾')) {
                                addLog(`🛡️ 发现护盾效果: ${effect.effect}`, 'success');
                                showResult(`🛡️ 发现护盾效果: ${effect.effect}`, 'success');
                            }
                            
                            // 检查反弹效果
                            if (effect.effect.includes('反弹')) {
                                addLog(`⚡ 发现反弹效果: ${effect.effect}`, 'success');
                                showResult(`⚡ 发现反弹效果: ${effect.effect}`, 'success');
                            }
                        });
                    } else {
                        addLog('❌ 没有找到套装特殊效果数据', 'error');
                        showResult('❌ 没有找到套装特殊效果数据', 'error');
                    }
                    
                    // 显示详细数据
                    const details = document.createElement('pre');
                    details.textContent = JSON.stringify(playerData.set_special_effects || [], null, 2);
                    testResults.appendChild(details);
                    
                } else {
                    addLog('❌ 战斗API调用失败', 'error');
                    showResult(`❌ 战斗API调用失败: ${result.message || '未知错误'}`, 'error');
                }
            } catch (error) {
                addLog(`❌ 请求失败: ${error.message}`, 'error');
                showResult(`❌ 请求失败: ${error.message}`, 'error');
            }
        }

        function openBattlePage() {
            addLog('🚀 正在打开战斗页面...');
            showResult('🚀 正在打开战斗页面进行实际测试...', 'info');
            
            // 提示用户观察要点
            showResult('📋 请在战斗页面中观察以下要点：', 'warning');
            showResult('1. 打开浏览器开发者工具(F12) → Console标签页', 'info');
            showResult('2. 观察战斗开始时的套装效果日志', 'info');
            showResult('3. 检查HP条旁边是否显示护盾值', 'info');
            showResult('4. 测试受到攻击时护盾吸收效果', 'info');
            
            window.open('battle.html?map_code=kunlun&stage=1', '_blank');
        }

        function simulateSetEffects() {
            addLog('🧪 开始模拟套装效果...');
            showResult('🧪 模拟套装效果触发情况...', 'info');
            
            // 模拟护盾效果
            const maxHp = 10000; // 假设最大生命值
            const shieldAmount = Math.floor(maxHp * 0.2); // 20%护盾
            addLog(`🛡️ 模拟护盾效果: 最大生命值${maxHp} → 护盾${shieldAmount}`);
            showResult(`🛡️ 护盾效果模拟: ${shieldAmount}点护盾`, 'success');
            
            // 模拟反弹效果
            let reflectCount = 0;
            const testAttacks = 20;
            for (let i = 0; i < testAttacks; i++) {
                if (Math.random() < 0.2) { // 20%概率
                    reflectCount++;
                }
            }
            const reflectRate = (reflectCount / testAttacks * 100).toFixed(1);
            addLog(`⚡ 模拟反弹效果: ${testAttacks}次攻击中触发${reflectCount}次 (${reflectRate}%)`);
            showResult(`⚡ 反弹效果模拟: ${reflectCount}/${testAttacks}次触发 (${reflectRate}%)`, 'info');
            
            if (reflectRate >= 15 && reflectRate <= 25) {
                showResult('✅ 反弹概率在合理范围内 (15%-25%)', 'success');
            } else {
                showResult('⚠️ 反弹概率可能需要调整', 'warning');
            }
        }

        function clearLogs() {
            logContainer.innerHTML = '<div>日志已清空，等待新的测试...</div>';
            testResults.innerHTML = '';
            addLog('🗑️ 日志已清空');
        }

        // 页面加载时的初始化
        window.onload = function() {
            addLog('📋 套装特殊效果测试页面已加载');
            showResult('📋 测试页面已准备就绪，请开始测试', 'info');
            
            // 自动运行API测试
            setTimeout(() => {
                testBattleAPI();
            }, 1000);
        };
    </script>
</body>
</html>
