# 📚 一念修仙项目文档中心

*最新更新时间: 2025年6月17日*

## 📁 文档目录结构

### 🏗️ system/ - 系统架构文档
核心系统设计和项目结构文档
- `PROJECT_DIRECTORY_STRUCTURE.md` - 项目目录结构说明
- `DOCUMENTATION_INDEX.md` - 文档索引
- `ARCHITECTURE_ANALYSIS.md` - 架构分析
- `PROJECT_OVERVIEW.md` - 项目概览
- `spirit_root_system.md` - 灵根系统设计

### 📖 guides/ - 开发指南
开发过程中的操作指南和最佳实践
- `auth_integration_guide.md` - 认证集成指南
- `ajax-manager-usage-guide.md` - AJAX管理器使用指南
- `战斗系统控制台屏蔽指南.md` - 战斗系统调试指南
- `技能开发指南_v3.0.md` - 技能开发指南
- `新技能模型添加完整指南.md` - 新技能模型添加指南
- `怪物技能设计开发指南.md` - 怪物技能设计开发指南

### ✅ completed/ - 已完成项目
已完成的系统重构和优化报告
- `战斗系统模块化完成总结.md` - 战斗系统模块化总结
- `掉落系统重构完成报告.md` - 掉落系统重构报告
- `战斗系统分拆优化报告.md` - 战斗系统分拆优化报告
- `AI_SYSTEM_FINAL_STATUS.md` - AI系统最终状态
- `技能系统v3.0更新总结.md` - 技能系统更新总结
- `装备属性计算系统重构说明.md` - 装备属性计算重构说明
- `cultivation_fixes.md` - 修炼系统修复记录
- `battle_drops_fix_summary.md` - 战斗掉落修复总结

### 📋 planning/ - 开发计划
未来系统开发计划和执行指南
- `秘境系统开发计划.md` - 秘境系统开发计划
- `奇遇系统和灵根系统完善执行计划.md` - 奇遇系统完善计划
- `第六阶段系统集成测试计划.md` - 系统集成测试计划
- `战斗系统优化计划.md` - 战斗系统优化计划
- `怪物系统重构执行指南.md` - 怪物系统重构指南
- `AI_SYSTEM_IMPLEMENTATION_PLAN.md` - AI系统实施计划
- `mobile_optimization_plan.md` - 移动端优化计划

### 🗄️ archived/ - 归档文档
过时的设计方案和已不再使用的文档
- `历练地图境界掉落系统设计方案.md` - 历练地图设计方案（已实现）
- `怪物系统重构完整方案-修正版.md` - 怪物系统重构方案（已实现）
- `AI_SYSTEM_BALANCED_SUMMARY.md` - AI系统平衡化总结（已实现）
- `字段名统一执行计划.md` - 字段名统一计划（已实现）

### 🛠️ 其他目录
- `技能开发模板/` - 技能开发模板文件
- `guides/` - 开发指南（详见上文）
- `refactoring/` - 重构相关文档
- `development/` - 开发相关文档

## 🎯 文档使用指南

### 📝 新开发者入门
1. 先阅读 `system/PROJECT_OVERVIEW.md` 了解项目概况
2. 查看 `system/ARCHITECTURE_ANALYSIS.md` 理解系统架构  
3. 根据需要查看 `guides/` 目录下的相关开发指南

### 🔧 系统开发
- 开发新功能前查看 `planning/` 目录下的相关计划
- 开发过程中参考 `guides/` 目录下的操作指南
- 完成后更新 `completed/` 目录下的相关报告

### 📚 文档维护规则
1. **新增文档**: 根据内容类型放入相应目录
2. **已完成项目**: 将计划文档移动到 `completed/` 目录
3. **过时文档**: 移动到 `archived/` 目录保留历史记录
4. **保持更新**: 及时更新文档索引和说明

---

*项目文档结构于 2025年6月17日 17:45 重新整理* 