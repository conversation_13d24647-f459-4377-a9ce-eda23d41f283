<?php
/**
 * 临时检查套装特殊效果数据结构
 */

try {
    $pdo = new PDO('mysql:host=127.0.0.1;port=3306;dbname=yn_game;charset=utf8mb4', 'root', 'mjlxz159');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== 检查套装特殊效果数据结构 ===\n\n";
    
    // 获取所有激活的套装
    $stmt = $pdo->prepare("SELECT set_name, effects FROM game_item_sets WHERE status = 'active' LIMIT 5");
    $stmt->execute();
    $sets = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach($sets as $set) {
        echo "套装: {$set['set_name']}\n";
        echo "原始JSON: {$set['effects']}\n";
        
        $effects = json_decode($set['effects'], true);
        if ($effects) {
            echo "解析后结构:\n";
            
            // 检查各个件套的特殊效果
            foreach(['two_piece', 'four_piece', 'six_piece'] as $piece_type) {
                if (isset($effects[$piece_type])) {
                    echo "  {$piece_type}:\n";
                    foreach($effects[$piece_type] as $key => $value) {
                        if ($key === 'special_effect') {
                            echo "    特殊效果: {$value}\n";
                        } else {
                            echo "    {$key}: {$value}\n";
                        }
                    }
                }
            }
        } else {
            echo "JSON解析失败\n";
        }
        
        echo "\n=================\n\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
?>
