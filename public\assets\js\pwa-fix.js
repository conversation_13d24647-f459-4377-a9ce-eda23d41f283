/**
 * PWA模式底部白边强力修复脚本
 * 专门解决iOS添加到主屏幕后的白条问题
 */

(function() {
    'use strict';
    
    // 检测PWA模式
    function isPWAMode() {
        return window.matchMedia('(display-mode: standalone)').matches ||
               window.navigator.standalone ||
               document.referrer.includes('android-app://');
    }
    
    // 检测iOS设备
    function isIOS() {
        return /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
    }
    
    // 强力修复底部白边
    function forceFixBottomGap() {
        if (!isPWAMode()) return;
        
        console.log('🔧 PWA模式检测到，开始强力修复底部白边...');
        console.log('📱 设备信息:', {
            userAgent: navigator.userAgent,
            innerHeight: window.innerHeight,
            outerHeight: window.outerHeight,
            screenHeight: screen.height,
            isIOS: isIOS()
        });
        
        // 移除现有的修复样式
        const existingStyle = document.getElementById('pwa-fix-styles');
        if (existingStyle) {
            existingStyle.remove();
        }
        
        // 立即设置viewport
        const viewport = document.querySelector('meta[name="viewport"]');
        if (viewport) {
            viewport.setAttribute('content', 
                'width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover'
            );
        }
        
        // 获取真实的视口高度
        const realViewportHeight = window.innerHeight;
        const screenWidth = window.innerWidth;
        
        // 创建强制样式
        const style = document.createElement('style');
        style.id = 'pwa-fix-styles';
        style.textContent = `
            /* PWA模式强制修复样式 - 专门针对iOS全屏模式 */
            * {
                -webkit-touch-callout: none !important;
                -webkit-user-select: none !important;
                -webkit-tap-highlight-color: transparent !important;
            }
            
            html {
                width: 100% !important;
                height: 100vh !important;
                height: ${realViewportHeight}px !important;
                margin: 0 !important;
                padding: 0 !important;
                overflow: hidden !important;
                box-sizing: border-box !important;
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                bottom: 0 !important;
            }
            
            body {
                width: 100% !important;
                height: 100vh !important;
                height: ${realViewportHeight}px !important;
                margin: 0 !important;
                padding: 0 !important;
                overflow: hidden !important;
                box-sizing: border-box !important;
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                bottom: 0 !important;
                background-size: cover !important;
            }
            
            .main-container, .menu-container {
                width: 100% !important;
                height: 100vh !important;
                height: ${realViewportHeight}px !important;
                margin: 0 !important;
                padding: 20px 20px 20px 20px !important;
                box-sizing: border-box !important;
                overflow-y: auto !important;
                -webkit-overflow-scrolling: touch !important;
                position: relative !important;
            }
            
            /* 首页特殊处理 */
            .menu-container {
                display: flex !important;
                flex-direction: column !important;
                justify-content: center !important;
                align-items: center !important;
                text-align: center !important;
                padding: 30px 20px 30px 20px !important;
            }
            
            /* 游戏页面特殊处理 */
            .game-interface .main-container {
                padding: 20px 10px 90px 10px !important;
            }
            
            .bottom-navigation {
                position: fixed !important;
                bottom: 0 !important;
                left: 0 !important;
                right: 0 !important;
                width: 100% !important;
                height: 70px !important;
                margin: 0 !important;
                padding: 0 !important;
                padding-bottom: env(safe-area-inset-bottom, 0px) !important;
                box-sizing: border-box !important;
                z-index: 9999 !important;
            }
            
            /* iOS特殊处理 */
            ${isIOS() ? `
            @supports (-webkit-touch-callout: none) {
                html, body {
                    height: ${realViewportHeight}px !important;
                    max-height: ${realViewportHeight}px !important;
                    min-height: ${realViewportHeight}px !important;
                }
                
                .main-container, .menu-container {
                    height: ${realViewportHeight}px !important;
                    max-height: ${realViewportHeight}px !important;
                    min-height: ${realViewportHeight}px !important;
                }
            }` : ''}
            
            /* 小屏设备适配 */
            @media (max-width: 414px) {
                .menu-container {
                    padding: 20px 15px 20px 15px !important;
                }
                
                .game-title {
                    font-size: 36px !important;
                    margin-bottom: 15px !important;
                }
                
                .game-subtitle {
                    font-size: 16px !important;
                    margin-bottom: 25px !important;
                }
                
                .menu-button {
                    padding: 12px 30px !important;
                    font-size: 16px !important;
                    min-width: 180px !important;
                }
            }
            
            @media (max-width: 375px) {
                .menu-container {
                    padding: 15px 10px 15px 10px !important;
                }
                
                .game-interface .main-container {
                    padding: 15px 8px 80px 8px !important;
                }
            }
            
            @media (max-width: 320px) {
                .menu-container {
                    padding: 10px 8px 10px 8px !important;
                }
                
                .game-title {
                    font-size: 28px !important;
                }
                
                .game-subtitle {
                    font-size: 14px !important;
                }
                
                .menu-button {
                    padding: 10px 20px !important;
                    font-size: 14px !important;
                    min-width: 160px !important;
                }
            }
        `;
        
        // 确保样式在head的最后，具有最高优先级
        document.head.appendChild(style);
        
        // 强制设置body样式（直接操作DOM）
        document.documentElement.style.height = realViewportHeight + 'px';
        document.documentElement.style.maxHeight = realViewportHeight + 'px';
        document.documentElement.style.overflow = 'hidden';
        document.documentElement.style.position = 'fixed';
        document.documentElement.style.top = '0';
        document.documentElement.style.left = '0';
        document.documentElement.style.right = '0';
        document.documentElement.style.bottom = '0';
        
        document.body.style.height = realViewportHeight + 'px';
        document.body.style.maxHeight = realViewportHeight + 'px';
        document.body.style.overflow = 'hidden';
        document.body.style.position = 'fixed';
        document.body.style.top = '0';
        document.body.style.left = '0';
        document.body.style.right = '0';
        document.body.style.bottom = '0';
        document.body.style.margin = '0';
        document.body.style.padding = '0';
        
        console.log('✅ PWA强制修复样式已应用');
        console.log('📐 应用的高度:', realViewportHeight + 'px');
        
        // 延迟再次确认修复
        setTimeout(() => {
            document.documentElement.style.height = realViewportHeight + 'px';
            document.body.style.height = realViewportHeight + 'px';
            console.log('🔄 二次确认修复完成');
        }, 100);
    }
    
    // 监听尺寸变化，更新修复
    function updateFixOnResize() {
        if (!isPWAMode()) return;
        
        console.log('📱 检测到尺寸变化，重新应用修复');
        setTimeout(forceFixBottomGap, 50);
    }
    
    // 强制阻止页面滚动（iOS PWA特殊处理）
    function preventScroll() {
        if (!isPWAMode()) return;
        
        // 只阻止页面级别的滚动，不阻止内容滚动
        let startY = 0;
        let startX = 0;
        
        document.addEventListener('touchstart', function(e) {
            startY = e.touches[0].pageY;
            startX = e.touches[0].pageX;
        }, { passive: true });
        
        document.addEventListener('touchmove', function(e) {
            const currentY = e.touches[0].pageY;
            const currentX = e.touches[0].pageX;
            const deltaY = currentY - startY;
            const deltaX = currentX - startX;
            
            // 检查是否在可滚动容器内
            let target = e.target;
            let isInScrollableContainer = false;
            let scrollContainer = null;
            
            // 向上遍历DOM树查找可滚动容器
            while (target && target !== document.body && target !== document.documentElement) {
                const computedStyle = window.getComputedStyle(target);
                const overflowY = computedStyle.overflowY;
                const overflowX = computedStyle.overflowX;
                
                // 如果目标元素或其父元素是可滚动的
                if (overflowY === 'auto' || overflowY === 'scroll' || 
                    overflowX === 'auto' || overflowX === 'scroll' ||
                    target.classList.contains('main-container') ||
                    target.classList.contains('attributes-section') ||
                    target.classList.contains('attribute-panel') ||
                    target.classList.contains('modal-content') ||
                    target.classList.contains('attribute-detail-modal') ||
                    target.classList.contains('shop-content') ||
                    target.classList.contains('character-creation') ||
                    target.classList.contains('techniques-container')) {
                    isInScrollableContainer = true;
                    scrollContainer = target;
                    break;
                }
                target = target.parentElement;
            }
            
            // 如果在可滚动容器内，检查是否已经滚动到边界
            if (isInScrollableContainer && scrollContainer) {
                const scrollTop = scrollContainer.scrollTop;
                const scrollHeight = scrollContainer.scrollHeight;
                const clientHeight = scrollContainer.clientHeight;
                
                // 只有当容器确实可以滚动时才允许滚动
                if (scrollHeight > clientHeight) {
                    // 在顶部向上滑动或在底部向下滑动时阻止
                    if ((scrollTop <= 0 && deltaY > 0) || 
                        (scrollTop + clientHeight >= scrollHeight && deltaY < 0)) {
                        e.preventDefault();
                        console.log('🚫 阻止边界弹性滚动');
                    } else {
                        // 允许容器内的正常滚动
                        console.log('✅ 允许容器滚动:', scrollContainer.className || scrollContainer.tagName);
                        return;
                    }
                } else {
                    // 容器内容不够多，不需要滚动，阻止触摸
                    e.preventDefault();
                    console.log('🚫 容器无需滚动，阻止触摸');
                }
            } else {
                // 阻止页面级别的滚动（弹性滚动）
                e.preventDefault();
                console.log('🚫 阻止页面级滚动');
            }
        }, { passive: false });
        
        // 阻止页面的scroll事件（防止地址栏隐藏导致的布局变化）
        document.addEventListener('scroll', function(e) {
            window.scrollTo(0, 0);
        });
        
        console.log('🚫 已设置智能滚动控制');
    }
    
    // 初始化函数
    function init() {
        console.log('🚀 PWA修复脚本初始化');
        console.log('🔍 PWA模式:', isPWAMode());
        console.log('📱 iOS设备:', isIOS());
        
        if (isPWAMode()) {
            // 立即执行修复
            forceFixBottomGap();
            
            // 阻止滚动
            preventScroll();
            
            // 监听窗口变化
            window.addEventListener('resize', updateFixOnResize);
            window.addEventListener('orientationchange', () => {
                console.log('📱 屏幕方向改变，延迟修复');
                setTimeout(updateFixOnResize, 300);
            });
            
            // DOM加载完成后再次修复
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => {
                    setTimeout(forceFixBottomGap, 100);
                });
            } else {
                setTimeout(forceFixBottomGap, 100);
            }
        }
    }
    
    // 导出调试函数
    window.PWAFix = {
        isPWA: isPWAMode,
        isIOS: isIOS,
        fix: forceFixBottomGap,
        update: updateFixOnResize,
        info: function() {
            console.log('📊 PWA修复信息:', {
                isPWA: isPWAMode(),
                isIOS: isIOS(),
                innerHeight: window.innerHeight,
                outerHeight: window.outerHeight,
                screenHeight: screen.height,
                documentHeight: document.documentElement.clientHeight,
                bodyHeight: document.body.clientHeight
            });
        }
    };
    
    // 立即初始化
    init();
    
})(); 