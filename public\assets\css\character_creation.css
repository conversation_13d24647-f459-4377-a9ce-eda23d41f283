.story-container {
    position: relative;
    width: 100%;
    height: 100vh;
    height: calc(var(--vh, 1vh) * 100);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
    box-sizing: border-box;
}

.story-panel {
    max-width: 648px;
    width: calc(100% - 20px);
    height: calc(100vh - 20px);
    height: calc(var(--vh, 1vh) * 100 - 20px);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border: 2px solid rgba(212, 175, 55, 0.3);
    border-radius: 20px;
    backdrop-filter: blur(15px);
    box-shadow: 
        0 0 30px rgba(255, 215, 0, 0.2),
        inset 0 0 20px rgba(255, 255, 255, 0.1);
    position: relative;
    margin: 10px;
    overflow: hidden; /* 确保内容不会溢出 */
    -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
}

/* PWA全屏模式专用样式 - 确保完全填充屏幕 */
@media (display-mode: standalone) {
    html, body {
        height: 100vh !important;
        height: calc(var(--vh, 1vh) * 100) !important;
        margin: 0 !important;
        padding: 0 !important;
        overflow: hidden !important;
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
    }
    
    .story-container {
        width: 100vw !important;
        height: 100vh !important;
        height: calc(var(--vh, 1vh) * 100) !important;
        padding: 0 !important;
        margin: 0 !important;
    }
    
    .story-panel {
        width: 100vw !important;
        height: 100vh !important;
        height: calc(var(--vh, 1vh) * 100) !important;
        min-height: 100vh !important;
        min-height: calc(var(--vh, 1vh) * 100) !important;
        max-height: 100vh !important;
        max-height: calc(var(--vh, 1vh) * 100) !important;
        margin: 0 !important;
        padding: 0 !important;
        border-radius: 0 !important;
        border: none !important;
        background: rgba(255, 255, 255, 0.05) !important;
        overflow-y: auto !important;
        -webkit-overflow-scrolling: touch !important;
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        max-width: none !important;
    }
    
    .story-content {
        padding: 40px 20px !important;
        min-height: 100vh !important;
        min-height: calc(var(--vh, 1vh) * 100) !important;
    }
    
    .character-creation {
        padding: 40px 20px !important;
        min-height: 100vh !important;
        min-height: calc(var(--vh, 1vh) * 100) !important;
    }
}

/* 小屏设备PWA模式适配 */
@media (display-mode: standalone) and (max-width: 414px) {
    .story-content {
        padding: 30px 15px !important;
    }
    
    .character-creation {
        padding: 30px 15px !important;
    }
    
    .creation-title {
        font-size: 18px !important;
        margin-bottom: 10px !important;
    }
    
    .character-name-input {
        width: 220px !important;
        padding: 8px 12px !important;
        font-size: 14px !important;
    }
    
    .avatar-preview {
        width: 70px !important;
        height: 70px !important;
    }
    
    .spirit-button {
        width: 26px !important;
        height: 26px !important;
        font-size: 9px !important;
        top: -3px !important;
        right: -3px !important;
    }
    
    .avatar-grid {
        grid-template-columns: repeat(5, 1fr) !important;
        gap: 8px !important;
        max-width: 320px !important;
        padding: 10px !important;
    }
    
    .avatar-option {
        width: 45px !important;
        height: 45px !important;
    }
    
    .spirit-allocation-content {
        width: 95% !important;
        padding: 20px 15px !important;
        max-height: 85vh !important;
    }
}

@media (display-mode: standalone) and (max-width: 375px) {
    .story-content {
        padding: 25px 12px !important;
    }
    
    .character-creation {
        padding: 25px 12px !important;
    }
    
    .creation-title {
        font-size: 16px !important;
    }
    
    .character-name-input {
        width: 200px !important;
        padding: 8px 10px !important;
    }
    
    .avatar-preview {
        width: 65px !important;
        height: 65px !important;
    }
    
    .spirit-button {
        width: 24px !important;
        height: 24px !important;
        font-size: 8px !important;
    }
    
    .avatar-grid {
        grid-template-columns: repeat(4, 1fr) !important;
        max-width: 280px !important;
    }
}

@media (display-mode: standalone) and (max-width: 320px) {
    .story-content {
        padding: 20px 10px !important;
    }
    
    .character-creation {
        padding: 20px 10px !important;
    }
    
    .creation-title {
        font-size: 14px !important;
    }
    
    .input-label {
        font-size: 12px !important;
    }
    
    .character-name-input {
        width: 180px !important;
        padding: 6px 8px !important;
        font-size: 13px !important;
    }
    
    .avatar-preview {
        width: 60px !important;
        height: 60px !important;
    }
    
    .spirit-button {
        width: 22px !important;
        height: 22px !important;
        font-size: 7px !important;
    }
    
    .avatar-grid {
        grid-template-columns: repeat(4, 1fr) !important;
        gap: 6px !important;
        max-width: 240px !important;
        padding: 8px !important;
    }
    
    .avatar-option {
        width: 40px !important;
        height: 40px !important;
    }
    
    .create-button {
        padding: 8px 20px !important;
        font-size: 14px !important;
    }
    
    .spirit-allocation-content {
        width: 98% !important;
        padding: 15px 10px !important;
        max-height: 90vh !important;
    }
    
    .spirit-allocation-title {
        font-size: 16px !important;
    }
    
    .spirit-btn {
        width: 26px !important;
        height: 26px !important;
    }
    
    .preset-btn {
        padding: 6px 10px !important;
        font-size: 11px !important;
    }
}

@media (max-width: 768px) {
    .story-container {
        padding: 5px;
    }
    
    .story-panel {
        margin: 5px;
        width: calc(100% - 10px);
        height: calc(100vh - 10px);
        height: calc(var(--vh, 1vh) * 100 - 10px);
    }
    
    .story-content {
        padding: 12px;
    }
    
    .character-creation {
        padding: 12px;
    }
    
    .avatar-preview {
        width: 70px;
        height: 70px;
    }
    
    .spirit-button {
        width: 26px;
        height: 26px;
        font-size: 9px;
    }
    
    .spirit-allocation-content {
        width: 95%;
        max-width: none;
        margin: 20px auto;
        max-height: 80vh;
    }
}

@media (max-width: 375px) {
    .story-container {
        padding: 2px;
    }
    
    .story-panel {
        margin: 2px;
        width: calc(100% - 4px);
        height: calc(100vh - 4px);
        height: calc(var(--vh, 1vh) * 100 - 4px);
    }
    
    .story-content {
        padding: 10px;
    }
    
    .character-creation {
        padding: 10px;
    }
    
    .creation-title {
        font-size: 18px;
    }
    
    .character-name-input {
        width: 220px;
        padding: 8px 10px;
    }
    
    .avatar-preview {
        width: 65px;
        height: 65px;
    }
    
    .spirit-button {
        width: 24px;
        height: 24px;
        font-size: 8px;
    }
}

.story-content {
    padding: 20px;
    text-align: center;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.story-paragraph {
    display: none;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
    margin-bottom: 15px;
    width: 100%;
    max-width: 500px;
}

.story-paragraph.active {
    display: block;
    opacity: 1;
}

.story-image {
    width: 100%;
    max-width: 350px;
    height: auto;
    border-radius: 15px;
    margin-bottom: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.story-text {
    font-size: 12px;
    line-height: 1.4;
    color: #7fffd4;
    text-shadow: 0 0 8px rgba(127, 255, 212, 0.3);
    margin-bottom: 10px;
}

.character-creation {
    display: none;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
    text-align: center;
    padding: 15px;
    height: 100%;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
}

.character-creation.active {
    display: flex;
    opacity: 1;
    flex-direction: column;
    justify-content: flex-start; /* 从顶部开始 */
    align-items: center; /* 水平居中 */
    min-height: 100%; /* 确保最小高度 */
}

.creation-title {
    font-size: 20px;
    font-weight: bold;
    color: #ffd700;
    text-shadow: 
        0 0 20px rgba(255, 215, 0, 0.8),
        2px 2px 4px rgba(0, 0, 0, 0.8);
    margin-bottom: 15px;
    animation: title-glow 2s ease-in-out infinite alternate;
}

@keyframes title-glow {
    from {
        text-shadow: 
            0 0 20px rgba(255, 215, 0, 0.8),
            2px 2px 4px rgba(0, 0, 0, 0.8);
    }
    to {
        text-shadow: 
            0 0 30px rgba(255, 215, 0, 1),
            0 0 40px rgba(255, 215, 0, 0.6),
            2px 2px 4px rgba(0, 0, 0, 0.8);
    }
}

.input-group {
    margin-bottom: 15px;
}

.input-label {
    display: block;
    font-size: 14px;
    color: #d4af37;
    margin-bottom: 8px;
    text-shadow: 0 0 8px rgba(212, 175, 55, 0.5);
}

.character-name-input {
    width: 260px;
    padding: 10px 14px;
    font-size: 14px;
    border: 2px solid rgba(212, 175, 55, 0.5);
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    text-align: center;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.character-name-input:focus {
    outline: none;
    border-color: #ffd700;
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.4);
}

.character-name-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

/* 头像选择样式 */
.avatar-selection {
    margin-bottom: 15px;
}

.avatar-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 10px;
    max-width: 380px;
    margin: 0 auto;
    padding: 12px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.avatar-option {
    position: relative;
    width: 50px;
    height: 50px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    overflow: hidden;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.avatar-option:hover {
    border-color: rgba(255, 215, 0, 0.6);
    transform: scale(1.1);
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
}

.avatar-option.selected {
    border-color: #ffd700;
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
    transform: scale(1.1);
}

.avatar-preview {
    width: 80px;
    height: 80px;            
    margin: 0 auto 15px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;           
    background-image: url('assets/images/char/huaishang.png'); /* 默认头像 */
    position: relative;
}

/* 灵根按钮样式 */
.spirit-button {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 30px;
    height: 30px;
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #1a1a2e;
    border: 2px solid rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    font-size: 10px;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
    transition: all 0.3s ease;
    animation: spirit-breathe 2s ease-in-out infinite;
    box-shadow: 
        0 0 10px rgba(255, 215, 0, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.spirit-button:hover {
    transform: scale(1.1);
    animation-duration: 1s;
}

/* 呼吸灯动画 */
@keyframes spirit-breathe {
    0%, 100% {
        box-shadow: 
            0 0 10px rgba(255, 215, 0, 0.5),
            0 0 20px rgba(255, 215, 0, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        transform: scale(1);
    }
    50% {
        box-shadow: 
            0 0 20px rgba(255, 215, 0, 0.8),
            0 0 30px rgba(255, 215, 0, 0.5),
            0 0 40px rgba(255, 215, 0, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        transform: scale(1.05);
    }
}

.create-button {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #1a1a2e;
    border: none;
    padding: 12px 30px;
    border-radius: 10px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 
        0 4px 15px rgba(255, 215, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    margin-top: 15px;
}

.create-button:hover {
    transform: translateY(-3px);
    box-shadow: 
        0 6px 20px rgba(255, 215, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.create-button:active {
    transform: translateY(0);
    box-shadow: 
        0 2px 10px rgba(255, 215, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.create-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.click-prompt {
    position: fixed;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px 20px;
    border-radius: 20px;
    font-size: 14px;
    opacity: 0;
    transition: opacity 0.5s ease;
    backdrop-filter: blur(10px);
}

.click-prompt.show {
    opacity: 1;
}

.loading {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 20px 40px;
    border-radius: 15px;
    font-size: 16px;
    backdrop-filter: blur(10px);
    z-index: 1000;
}

.error-message {
    color: #ff6b6b;
    font-size: 14px;
    margin-top: 10px;
    text-shadow: 0 0 8px rgba(255, 107, 107, 0.3);
}

/* 灵根分配系统样式 - 改为弹窗 */
.spirit-allocation-modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.spirit-allocation-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, rgba(26, 26, 46, 0.95), rgba(15, 52, 96, 0.95));
    border: 2px solid rgba(212, 175, 55, 0.6);
    border-radius: 15px;
    padding: 25px;
    max-width: 90%;
    width: 450px;
    max-height: 80vh;
    overflow-y: auto;
    backdrop-filter: blur(15px);
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.3);
}

.spirit-allocation-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(212, 175, 55, 0.3);
}

.spirit-allocation-title {
    font-size: 20px;
    color: #ffd700;
    font-weight: bold;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.spirit-allocation-close {
    background: none;
    border: none;
    color: #fff;
    font-size: 24px;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.spirit-allocation-close:hover {
    opacity: 1;
}

.spirit-allocation {
    background: transparent;
    border-radius: 0;
    padding: 0;
    margin: 0;
    backdrop-filter: none;
    border: none;
}

.spirit-info {
    text-align: center;
    margin-bottom: 20px;
}

.total-points {
    font-size: 16px;
    color: #ffd700;
    font-weight: bold;
    margin-bottom: 5px;
}

.allocation-hint {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
}

.spirit-controls {
    margin-bottom: 20px;
}

.spirit-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.spirit-row:last-child {
    border-bottom: none;
}

.spirit-icon {
    font-size: 20px;
    width: 30px;
    text-align: center;
}

.spirit-name {
    flex: 1;
    font-size: 14px;
    color: #fff;
    margin-left: 10px;
}

.spirit-control {
    display: flex;
    align-items: center;
    gap: 8px;
}

.spirit-btn {
    width: 30px;
    height: 30px;
    border: 1px solid rgba(212, 175, 55, 0.5);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.spirit-btn:hover {
    background: rgba(212, 175, 55, 0.3);
    border-color: #ffd700;
}

.spirit-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
}

.spirit-value {
    min-width: 30px;
    text-align: center;
    font-weight: bold;
    color: #ffd700;
}

.spirit-quality {
    min-width: 40px;
    font-size: 12px;
    text-align: center;
    font-weight: bold;
}

.preset-buttons {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.preset-btn {
    padding: 8px 16px;
    border: 1px solid rgba(212, 175, 55, 0.5);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.preset-btn:hover {
    background: rgba(212, 175, 55, 0.3);
    border-color: #ffd700;
}

/* 灵根说明模态框样式 */
.spirit-modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.spirit-modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, rgba(26, 26, 46, 0.95), rgba(15, 52, 96, 0.95));
    border: 2px solid rgba(212, 175, 55, 0.6);
    border-radius: 15px;
    padding: 25px;
    max-width: 90%;
    width: 400px;
    max-height: 80vh;
    overflow-y: auto;
    backdrop-filter: blur(15px);
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.3);
}

.spirit-modal-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(212, 175, 55, 0.3);
}

.spirit-modal-icon {
    font-size: 32px;
}

.spirit-modal-title {
    font-size: 20px;
    color: #ffd700;
    font-weight: bold;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.spirit-modal-close {
    position: absolute;
    top: 15px;
    right: 20px;
    background: none;
    border: none;
    color: #fff;
    font-size: 24px;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.spirit-modal-close:hover {
    opacity: 1;
}

.spirit-description {
    color: #7fffd4;
    line-height: 1.6;
    margin-bottom: 20px;
    text-shadow: 0 0 8px rgba(127, 255, 212, 0.3);
}

.spirit-effects {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
}

.spirit-effects-title {
    color: #d4af37;
    font-weight: bold;
    margin-bottom: 10px;
    font-size: 16px;
}

.spirit-effect {
    color: #87ceeb;
    margin-bottom: 8px;
    font-size: 14px;
}

.spirit-recommendations {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1));
    border: 1px solid rgba(255, 215, 0, 0.3);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
}

.spirit-recommendations-title {
    color: #ffd700;
    font-weight: bold;
    margin-bottom: 10px;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.recommendation-item {
    color: #fff;
    margin-bottom: 8px;
    font-size: 14px;
    padding-left: 15px;
    position: relative;
}

.recommendation-item:before {
    content: "⭐";
    position: absolute;
    left: 0;
    color: #ffd700;
}

.spirit-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-top: 15px;
}

.stat-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 10px;
    text-align: center;
}

.stat-label {
    color: #d4af37;
    font-size: 12px;
    margin-bottom: 5px;
}

.stat-value {
    color: #fff;
    font-weight: bold;
    font-size: 14px;
}

/* 灵根行点击效果 */
.spirit-row {
    cursor: pointer;
    border-radius: 8px;
    transition: background-color 0.3s ease;
}

.spirit-row:hover {
    background: rgba(255, 255, 255, 0.05);
}