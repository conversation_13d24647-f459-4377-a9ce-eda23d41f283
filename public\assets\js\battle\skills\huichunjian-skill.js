/**
 * 回春剑技能模块 - 独立文件
 * 木系/剑类混合技能：绿色魔法阵蓄力 + 旋转飞剑攻击 + 藤蔓穿刺效果
 */

/**
 * 回春剑技能类 - 木系/剑类混合技能
 * 绿色魔法阵中心旋转飞剑 + 飞剑甩飞攻击 + 藤蔓穿刺击中效果
 */
class HuiChunJianSkill extends BaseSkill {
    constructor(battleSystem) {
        super(battleSystem);
        this.skillName = '回春剑';
        this.elementType = 'wood-sword';
        
        // v2.0新增：技能实例管理
        this.animationContainers = new Set();
        this.activeTimers = new Set();
    }

    async execute(skillData, weaponImage) {
        try {
            console.log(`🌿⚔️ 回春剑技能开始执行`);
            
            // 必须调用技能喊话
            // 🔧 修复：使用真实技能名称而不是动画名称
            const skillName = skillData?.skillName || skillData?.displayName || this.skillName || '回春剑';
            await this.showSkillShout(skillName);
            
            // 调用具体的技能动画方法
            await this.createHuiChunJian(weaponImage);
            
        } catch (error) {
            console.error(`❌ ${this.skillName} 执行失败:`, error);
            this.handleError(error, 'execute');
        }
    }
    
    async createHuiChunJian(weaponImage) {
        console.log(`🌿⚔️ createHuiChunJian 开始，isEnemySkill: ${this.isEnemySkill}`);
        // 🔧 修复：动态判断位置映射
        // 根据技能使用者动态确定施法者和目标位置
        let casterPos, targetPos;
        
        if (this.isEnemySkill) {
            // 敌方技能：敌人是施法者，玩家是目标
            casterPos = this.getCharacterPosition(false); // 敌人位置
            targetPos = this.getCharacterPosition(true);  // 玩家位置
        } else {
            // 我方技能：玩家是施法者，敌人是目标
            casterPos = this.getCharacterPosition(true);  // 玩家位置
            targetPos = this.getCharacterPosition(false); // 敌人位置
        }
        
        console.log(`🌿⚔️ 回春剑位置计算 - 敌方技能:${this.isEnemySkill}, 施法者:`, casterPos, '目标:', targetPos);
        
        // 创建技能动画容器
        const container = this.createElement('huichunjian-container', {
            style: {
                position: 'absolute',
                top: '0',
                left: '0',
                width: '100%',
                height: '100%',
                pointerEvents: 'none',
                zIndex: 1000
            }
        });
        
        // v2.0新增：容器管理
        this.animationContainers.add(container);
        this.effectsContainer.appendChild(container);
        
        try {
            // 第一阶段：绿色魔法阵中间旋转飞剑，逐渐变大（1.5秒）
            await this.createGreenCircleCharge(container, casterPos, weaponImage);
            
            // 第二阶段：魔法阵中的飞剑甩飞旋转攻击（1.0秒）
            await this.createSwordSpinAttack(container, casterPos, targetPos, weaponImage);
            
            // 第三阶段：8方向武器穿刺 + 藤蔓环绕效果（参考藤蔓技能）
            await this.createVinePierceEffect(container, targetPos, weaponImage);
            
        } finally {
            // v2.0优化：安全清理机制
            this.safeCleanupContainer(container);
        }
    }
    
    // 第一阶段：绿色魔法阵中间旋转飞剑，逐渐变大
    async createGreenCircleCharge(container, casterPos, weaponImage) {
        console.log(`🌿 回春剑第一阶段：绿色魔法阵蓄力 + 旋转飞剑`);
        
        // === 创建绿色魔法阵 ===
        const magicCircle = this.createElement('huichunjian-magic-circle', {
            style: {
                position: 'absolute',
                left: casterPos.x + 'px',
                top: casterPos.y + 'px',
                transform: 'translate(-50%, -50%)'
            }
        });
        container.appendChild(magicCircle);
        
        // 创建内圈符文
        const innerRunes = this.createElement('huichunjian-inner-runes', {
            style: {
                position: 'absolute',
                left: casterPos.x + 'px',
                top: casterPos.y + 'px',
                transform: 'translate(-50%, -50%)'
            }
        });
        container.appendChild(innerRunes);
        
        // 创建外圈符文
        const outerRunes = this.createElement('huichunjian-outer-runes', {
            style: {
                position: 'absolute',
                left: casterPos.x + 'px',
                top: casterPos.y + 'px',
                transform: 'translate(-50%, -50%)'
            }
        });
        container.appendChild(outerRunes);
        
        // === 在魔法阵中心创建旋转飞剑 ===
        const centerSword = this.createElement('huichunjian-center-sword', {
            style: {
                position: 'absolute',
                left: casterPos.x + 'px',
                top: casterPos.y + 'px',
                transform: 'translate(-50%, -50%)',
                opacity: '0',
                zIndex: '1001'
            }
        });
        
        // 添加武器图片
        if (weaponImage) {
            this.addWeaponImage(centerSword, weaponImage);
            // 🗡️ 动态调整武器图片角度
            const weaponImg = centerSword.querySelector('.weapon-image');
            if (weaponImg) {
                weaponImg.style.transform = `rotate(${this.calculateInitialSwordRotation()}deg)`;
            }
            console.log(`🌿 为中心飞剑添加武器图片: ${weaponImage}`);
        }
        
        container.appendChild(centerSword);
        
        // 创建蓄力能量核心
        const energyCore = this.createElement('huichunjian-energy-core', {
            style: {
                position: 'absolute',
                left: casterPos.x + 'px',
                top: casterPos.y + 'px',
                transform: 'translate(-50%, -50%)'
            }
        });
        container.appendChild(energyCore);
        
        // 创建绿色能量粒子汇聚
        for (let i = 0; i < 20; i++) {
            const particle = this.createElement('huichunjian-charge-particle', {
                style: {
                    position: 'absolute',
                    left: casterPos.x + 'px',
                    top: casterPos.y + 'px',
                    transform: 'translate(-50%, -50%)',
                    animationDelay: `${Math.random() * 1.0}s`
                }
            });
            
            const angle = Math.random() * Math.PI * 2;
            const radius = 15 + Math.random() * 35;
            const moveX = Math.cos(angle) * radius;
            const moveY = Math.sin(angle) * radius;
            
            particle.style.setProperty('--chargeX', `${moveX}px`);
            particle.style.setProperty('--chargeY', `${moveY}px`);
            
            container.appendChild(particle);
        }
        
        // 等待蓄力完成（1.5秒）
        console.log(`🌿 等待1200毫秒蓄力完成...`);
        await this.wait(1200);
        
        // 清理蓄力特效，保留飞剑用于下一阶段
        this.safeRemoveElement(magicCircle);
        this.safeRemoveElement(innerRunes);
        this.safeRemoveElement(outerRunes);
        this.safeRemoveElement(energyCore);
        
        // 清理粒子
        container.querySelectorAll('.huichunjian-charge-particle').forEach(particle => {
            this.safeRemoveElement(particle);
        });
        
        console.log(`✅ 回春剑第一阶段完成，返回中心飞剑`);
        return centerSword;
    }
    
    // 第二阶段：魔法阵中的飞剑甩飞旋转攻击
    async createSwordSpinAttack(container, casterPos, targetPos, weaponImage) {
        console.log(`⚔️ 回春剑第二阶段：飞剑直线攻击`);
        
        // 获取第一阶段的飞剑（应该还在容器中）
        const centerSword = container.querySelector('.huichunjian-center-sword');
        if (!centerSword) {
            console.error('❌ 找不到中心飞剑元素');
            return;
        }
        
        // 计算飞行角度和轨迹
        const deltaX = targetPos.x - casterPos.x;
        const deltaY = targetPos.y - casterPos.y;
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
        let flyAngle = Math.atan2(deltaY, deltaX) * 180 / Math.PI - 90; // 剑尖指向目标
        
        // 🔧 动态判断：角度计算已经能够正确处理双向攻击
        console.log(`🌿⚔️ 回春剑技能角度计算: ${flyAngle}度 (isEnemySkill: ${this.isEnemySkill})`);
        console.log(`🌿⚔️ 攻击向量: (${deltaX}, ${deltaY})`);
        
        console.log(`🌿⚔️ 回春剑飞行计算:`, {
            角度: flyAngle,
            距离: distance
        });
        
        // === 设置飞剑飞行参数 ===
        centerSword.style.setProperty('--startX', `${casterPos.x}px`);
        centerSword.style.setProperty('--startY', `${casterPos.y}px`);
        centerSword.style.setProperty('--targetX', `${targetPos.x}px`);
        centerSword.style.setProperty('--targetY', `${targetPos.y}px`);
        centerSword.style.setProperty('--flyAngle', `${flyAngle}deg`);
        
        // 启动飞剑直线攻击动画（剑尖朝向敌人，不旋转）
        centerSword.style.opacity = '1'; // 显示飞剑
        centerSword.style.animation = 'huichunjian-sword-fly 1.0s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards';
        
        // === 创建飞行轨迹粒子 ===
        const trailInterval = setInterval(() => {
            if (!centerSword.parentNode) {
                clearInterval(trailInterval);
                return;
            }
            
            const trail = this.createElement('huichunjian-flight-trail', {
                style: {
                    position: 'absolute',
                    left: casterPos.x + 'px',
                    top: casterPos.y + 'px',
                    transform: 'translate(-50%, -50%)'
                }
            });
            
            // 设置轨迹跟随参数
            trail.style.setProperty('--startX', `${casterPos.x}px`);
            trail.style.setProperty('--startY', `${casterPos.y}px`);
            trail.style.setProperty('--targetX', `${targetPos.x}px`);
            trail.style.setProperty('--targetY', `${targetPos.y}px`);
            trail.style.setProperty('--trailOpacity', `${0.3 + Math.random() * 0.5}`);
            
            container.appendChild(trail);
            
            // 清理轨迹粒子
            setTimeout(() => {
                this.safeRemoveElement(trail);
            }, 1200);
        }, 80);
        
        // 等待飞行攻击完成（1.0秒）
        console.log(`⚔️ 等待800毫秒飞行攻击完成...`);
        await this.wait(800);
        
        // 清理轨迹间隔器
        clearInterval(trailInterval);
        
        // 清理飞剑
        this.safeRemoveElement(centerSword);
        
        console.log(`✅ 回春剑第二阶段完成`);
    }
    
    // 第三阶段：8方向武器穿刺 + 藤蔓环绕效果（参考藤蔓技能）
    async createVinePierceEffect(container, targetPos, weaponImage) {
        console.log(`🌿 回春剑第三阶段：8方向武器穿刺 + 藤蔓环绕效果`);
        
        // === 同时触发：光圈扩散 + 击中特效 + 武器穿刺 ===
        
        // 1. 绿色光圈扩散效果
        const impactRipple = this.createElement('huichunjian-impact-ripple', {
            style: {
                position: 'absolute',
                left: targetPos.x + 'px',
                top: targetPos.y + 'px',
                transform: 'translate(-50%, -50%)',
                zIndex: '1000'
            }
        });
        container.appendChild(impactRipple);
        
        // 2. 扩散粒子
        const impactParticles = [];
        for (let i = 0; i < 20; i++) {
            const particle = this.createElement('huichunjian-impact-particle', {
                style: {
                    position: 'absolute',
                    left: targetPos.x + 'px',
                    top: targetPos.y + 'px',
                    transform: 'translate(-50%, -50%)',
                    animationDelay: `${Math.random() * 0.2}s`
                }
            });
            
            const angle = (i / 20) * Math.PI * 2;
            const distance = 60 + Math.random() * 40;
            const moveX = Math.cos(angle) * distance;
            const moveY = Math.sin(angle) * distance;
            
            particle.style.setProperty('--impactX', `${moveX}px`);
            particle.style.setProperty('--impactY', `${moveY}px`);
            
            container.appendChild(particle);
            impactParticles.push(particle);
        }
        
        // 3. 标准击中特效
        this.createHitEffect(targetPos.x, targetPos.y, true);
        
        // 4. 🔧 修复：8方向武器穿刺 - 简化定位逻辑
        const weaponCount = 8;
        const startRadius = 80; // 起始半径
        const weapons = [];
        
        if (weaponImage) {
            for (let i = 0; i < weaponCount; i++) {
                const angle = (i / weaponCount) * Math.PI * 2;
                const startX = targetPos.x + Math.cos(angle) * startRadius;
                const startY = targetPos.y + Math.sin(angle) * startRadius;
                
                const weapon = this.createElement('huichunjian-circle-weapon', {
                    style: {
                        position: 'absolute',
                        left: startX + 'px',
                        top: startY + 'px',
                        transform: 'translate(-50%, -50%)',
                        zIndex: '1001',
                        animationDelay: `${i * 0.03}s`
                    }
                });
                
                // 添加武器图片
                this.addWeaponImage(weapon, weaponImage);
                
                // 🔧 修复：使用简单的CSS变量设置穿刺目标
                weapon.style.setProperty('--startX', startX + 'px');
                weapon.style.setProperty('--startY', startY + 'px');
                weapon.style.setProperty('--targetX', targetPos.x + 'px');
                weapon.style.setProperty('--targetY', targetPos.y + 'px');
                
                // 🔧 修复：计算武器角度指向中心
                const centerAngle = Math.atan2(targetPos.y - startY, targetPos.x - startX);
                const angleDegrees = centerAngle * 180 / Math.PI;
                weapon.style.setProperty('--weaponAngle', `${angleDegrees + 270}deg`);
                
                container.appendChild(weapon);
                weapons.push(weapon);
            }
        }
        
        // 5. 中心爆炸效果
        const explosion = this.createElement('huichunjian-center-explosion', {
            style: {
                position: 'absolute',
                left: targetPos.x + 'px',
                top: targetPos.y + 'px',
                transform: 'translate(-50%, -50%)',
                zIndex: '1000'
            }
        });
        container.appendChild(explosion);
        
        // 6. 爆炸粒子
        const explosionParticles = [];
        for (let i = 0; i < 15; i++) {
            const particle = this.createElement('huichunjian-explosion-particle', {
                style: {
                    position: 'absolute',
                    left: targetPos.x + 'px',
                    top: targetPos.y + 'px',
                    transform: 'translate(-50%, -50%)',
                    animationDelay: `${Math.random() * 0.2}s`
                }
            });
            
            const angle = Math.random() * Math.PI * 2;
            const distance = 40 + Math.random() * 80;
            const moveX = Math.cos(angle) * distance;
            const moveY = Math.sin(angle) * distance;
            
            particle.style.setProperty('--explodeX', `${moveX}px`);
            particle.style.setProperty('--explodeY', `${moveY}px`);
            
            container.appendChild(particle);
            explosionParticles.push(particle);
        }
        
        // 7. 延迟创建藤蔓环绕效果
        setTimeout(() => {
            for (let i = 0; i < 12; i++) {
                const vine = this.createElement('huichunjian-impact-vine', {
                    style: {
                        position: 'absolute',
                        left: targetPos.x + 'px',
                        top: targetPos.y + 'px',
                        transform: 'translate(-50%, -50%)',
                        animationDelay: `${i * 0.08}s`,
                        zIndex: '999'
                    }
                });
                
                const vineAngle = (i / 12) * Math.PI * 2;
                vine.style.setProperty('--vineAngle', `${vineAngle * 180 / Math.PI}deg`);
                
                container.appendChild(vine);
            }
        }, 200);
        
        // 8. 给被攻击者添加穿刺震动效果
        const targetSelector = !this.isEnemySkill ? '.enemy .character-sprite' : '.player .character-sprite';
        const targetSprite = document.querySelector(targetSelector);
        if (targetSprite) {
            targetSprite.style.animation = '';
            targetSprite.offsetHeight;
            targetSprite.style.animation = 'huichunjian-pierce-struck 1.0s ease-out';
        }
        
        // 等待穿刺效果完成
        await this.wait(800);
        
        // 清理所有元素
        this.cleanupBindPhaseElements(impactRipple, impactParticles, weapons, explosion, explosionParticles);
        
        // 恢复目标动画
        if (targetSprite) {
            targetSprite.style.animation = '';
        }
        
        console.log(`✅ 回春剑第三阶段完成`);
    }
    
    // 清理击中阶段的所有元素
    cleanupBindPhaseElements(impactRipple, impactParticles, weapons, explosion, explosionParticles) {
        // 清理光圈
        if (impactRipple && impactRipple.parentNode) {
            impactRipple.parentNode.removeChild(impactRipple);
        }
        
        // 清理扩散粒子
        impactParticles.forEach(particle => {
            if (particle && particle.parentNode) {
                particle.parentNode.removeChild(particle);
            }
        });
        
        // 清理武器
        weapons.forEach(weapon => {
            if (weapon && weapon.parentNode) {
                weapon.parentNode.removeChild(weapon);
            }
        });
        
        // 清理爆炸效果
        if (explosion && explosion.parentNode) {
            explosion.parentNode.removeChild(explosion);
        }
        
        // 清理爆炸粒子
        explosionParticles.forEach(particle => {
            if (particle && particle.parentNode) {
                particle.parentNode.removeChild(particle);
            }
        });
    }
    
    // 🗡️ 动态计算剑的初始旋转角度
    calculateInitialSwordRotation() {
        // 敌方技能：剑尖向下（指向玩家），我方技能：剑尖向上（指向敌人）
        return this.isEnemySkill ? 180 : 0;
    }

    // v2.0新增：安全移除元素
    safeRemoveElement(element) {
        if (element && element.parentNode) {
            try {
                element.parentNode.removeChild(element);
            } catch (error) {
                console.warn('移除元素时出错:', error);
            }
        }
    }
    
    // v2.0新增：安全清理容器
    safeCleanupContainer(container) {
        const timer = setTimeout(() => {
            this.animationContainers.delete(container);
            this.safeRemoveElement(container);
        }, 100);
        this.activeTimers.add(timer);
    }
    
    // v2.0新增：清理所有资源
    cleanup() {
        // 清理定时器
        this.activeTimers.forEach(timer => {
            clearTimeout(timer);
        });
        this.activeTimers.clear();
        
        // 清理动画容器
        this.animationContainers.forEach(container => {
            this.safeRemoveElement(container);
        });
        this.animationContainers.clear();
        
        console.log(`🧹 ${this.skillName} 资源清理完成`);
    }
    
    // v2.0新增：错误处理
    handleError(error, context) {
        console.error(`❌ ${this.skillName} 在 ${context} 阶段发生错误:`, error);
        
        // 尝试清理资源
        this.cleanup();
        
        // 可以在这里添加错误报告到调试面板
        if (window.debugPanel) {
            window.debugPanel.addError(`${this.skillName}-${context}`, error.message);
        }
    }
}

// 导出技能类（必须按此格式）
window.HuichunjianSkills = { HuiChunJianSkill };
