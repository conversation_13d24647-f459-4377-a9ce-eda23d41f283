<?php
/**
 * 一念修仙项目监控和日志系统
 * 实时监控系统状态，记录关键指标和异常
 */

require_once __DIR__ . '/setting.php';

class MonitoringSystem {
    private $pdo;
    private $logFile;
    private $alertThresholds;
    
    public function __construct() {
        $this->pdo = getDatabaseConnection();
        $this->logFile = LOGS_DIR . '/monitoring.log';
        
        // 设置告警阈值
        $this->alertThresholds = [
            'response_time' => 1000,    // 响应时间超过1秒告警
            'error_rate' => 5,          // 错误率超过5%告警
            'memory_usage' => 80,       // 内存使用超过80%告警
            'disk_usage' => 90,         // 磁盘使用超过90%告警
            'db_connections' => 50      // 数据库连接超过50个告警
        ];
        
        $this->initMonitoringTables();
    }
    
    /**
     * 系统健康检查
     */
    public function healthCheck() {
        $health = [
            'timestamp' => date('Y-m-d H:i:s'),
            'status' => 'healthy',
            'checks' => []
        ];
        
        // 1. 数据库连接检查
        $dbCheck = $this->checkDatabase();
        $health['checks']['database'] = $dbCheck;
        
        // 2. 磁盘空间检查
        $diskCheck = $this->checkDiskSpace();
        $health['checks']['disk'] = $diskCheck;
        
        // 3. 内存使用检查
        $memoryCheck = $this->checkMemoryUsage();
        $health['checks']['memory'] = $memoryCheck;
        
        // 4. 日志文件检查
        $logCheck = $this->checkLogFiles();
        $health['checks']['logs'] = $logCheck;
        
        // 5. API响应时间检查
        $apiCheck = $this->checkApiResponse();
        $health['checks']['api'] = $apiCheck;
        
        // 确定整体状态
        $hasErrors = false;
        foreach ($health['checks'] as $check) {
            if ($check['status'] !== 'ok') {
                $hasErrors = true;
                break;
            }
        }
        
        $health['status'] = $hasErrors ? 'warning' : 'healthy';
        
        // 记录监控数据
        $this->logMonitoringData($health);
        
        return $health;
    }
    
    /**
     * 检查数据库状态
     */
    private function checkDatabase() {
        try {
            $startTime = microtime(true);
            
            // 测试连接
            $stmt = $this->pdo->query("SELECT 1");
            $result = $stmt->fetch();
            
            $responseTime = (microtime(true) - $startTime) * 1000;
            
            // 检查连接数
            $stmt = $this->pdo->query("SHOW STATUS LIKE 'Threads_connected'");
            $connections = $stmt->fetch(PDO::FETCH_ASSOC)['Value'];
            
            // 检查表状态
            $stmt = $this->pdo->query("SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = '" . DB_NAME . "'");
            $tableCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            $status = 'ok';
            $message = '数据库运行正常';
            
            if ($responseTime > $this->alertThresholds['response_time']) {
                $status = 'warning';
                $message = '数据库响应时间过长';
            }
            
            if ($connections > $this->alertThresholds['db_connections']) {
                $status = 'error';
                $message = '数据库连接数过多';
            }
            
            return [
                'status' => $status,
                'message' => $message,
                'metrics' => [
                    'response_time' => round($responseTime, 2),
                    'connections' => $connections,
                    'tables' => $tableCount
                ]
            ];
            
        } catch (Exception $e) {
            return [
                'status' => 'error',
                'message' => '数据库连接失败: ' . $e->getMessage(),
                'metrics' => []
            ];
        }
    }
    
    /**
     * 检查磁盘空间
     */
    private function checkDiskSpace() {
        $totalSpace = disk_total_space('.');
        $freeSpace = disk_free_space('.');
        $usedSpace = $totalSpace - $freeSpace;
        $usagePercent = ($usedSpace / $totalSpace) * 100;
        
        $status = 'ok';
        $message = '磁盘空间充足';
        
        if ($usagePercent > $this->alertThresholds['disk_usage']) {
            $status = 'error';
            $message = '磁盘空间不足';
        } elseif ($usagePercent > 80) {
            $status = 'warning';
            $message = '磁盘空间使用较高';
        }
        
        return [
            'status' => $status,
            'message' => $message,
            'metrics' => [
                'total_space' => $this->formatBytes($totalSpace),
                'free_space' => $this->formatBytes($freeSpace),
                'used_percent' => round($usagePercent, 2)
            ]
        ];
    }
    
    /**
     * 检查内存使用
     */
    private function checkMemoryUsage() {
        $memoryUsage = memory_get_usage(true);
        $memoryPeak = memory_get_peak_usage(true);
        $memoryLimit = ini_get('memory_limit');
        
        // 转换内存限制为字节
        $memoryLimitBytes = $this->parseMemoryLimit($memoryLimit);
        $usagePercent = ($memoryPeak / $memoryLimitBytes) * 100;
        
        $status = 'ok';
        $message = '内存使用正常';
        
        if ($usagePercent > $this->alertThresholds['memory_usage']) {
            $status = 'warning';
            $message = '内存使用较高';
        }
        
        return [
            'status' => $status,
            'message' => $message,
            'metrics' => [
                'current_usage' => $this->formatBytes($memoryUsage),
                'peak_usage' => $this->formatBytes($memoryPeak),
                'limit' => $memoryLimit,
                'usage_percent' => round($usagePercent, 2)
            ]
        ];
    }
    
    /**
     * 检查日志文件
     */
    private function checkLogFiles() {
        $logFiles = [
            LOGS_DIR . '/php_errors.log',
            LOGS_DIR . '/game.log',
            LOGS_DIR . '/monitoring.log'
        ];
        
        $status = 'ok';
        $message = '日志系统正常';
        $metrics = [];
        
        foreach ($logFiles as $logFile) {
            $filename = basename($logFile);
            
            if (file_exists($logFile)) {
                $size = filesize($logFile);
                $metrics[$filename] = [
                    'exists' => true,
                    'size' => $this->formatBytes($size),
                    'writable' => is_writable($logFile)
                ];
                
                // 检查日志文件大小
                if ($size > LOG_MAX_SIZE) {
                    $status = 'warning';
                    $message = '部分日志文件过大';
                }
            } else {
                $metrics[$filename] = [
                    'exists' => false,
                    'size' => '0 B',
                    'writable' => false
                ];
            }
        }
        
        return [
            'status' => $status,
            'message' => $message,
            'metrics' => $metrics
        ];
    }
    
    /**
     * 检查API响应时间
     */
    private function checkApiResponse() {
        $testEndpoints = [
            'user_info.php',
            'cultivation.php?action=get_attributes'
        ];
        
        $totalTime = 0;
        $successCount = 0;
        $metrics = [];
        
        foreach ($testEndpoints as $endpoint) {
            $startTime = microtime(true);
            
            // 模拟内部API调用
            $url = "http://localhost/yinian/src/api/" . $endpoint;
            $context = stream_context_create([
                'http' => [
                    'method' => 'GET',
                    'timeout' => 5,
                    'header' => "User-Agent: Monitoring System\r\n"
                ]
            ]);
            
            $result = @file_get_contents($url, false, $context);
            $responseTime = (microtime(true) - $startTime) * 1000;
            
            $metrics[basename($endpoint, '.php')] = [
                'response_time' => round($responseTime, 2),
                'success' => $result !== false
            ];
            
            if ($result !== false) {
                $totalTime += $responseTime;
                $successCount++;
            }
        }
        
        $avgResponseTime = $successCount > 0 ? $totalTime / $successCount : 0;
        
        $status = 'ok';
        $message = 'API响应正常';
        
        if ($avgResponseTime > $this->alertThresholds['response_time']) {
            $status = 'warning';
            $message = 'API响应时间较长';
        }
        
        if ($successCount === 0) {
            $status = 'error';
            $message = 'API无法访问';
        }
        
        return [
            'status' => $status,
            'message' => $message,
            'metrics' => array_merge($metrics, [
                'avg_response_time' => round($avgResponseTime, 2),
                'success_rate' => round(($successCount / count($testEndpoints)) * 100, 2)
            ])
        ];
    }
    
    /**
     * 记录监控数据
     */
    private function logMonitoringData($health) {
        // 写入日志文件
        $logEntry = [
            'timestamp' => $health['timestamp'],
            'status' => $health['status'],
            'summary' => $this->generateSummary($health)
        ];
        
        $logLine = json_encode($logEntry, JSON_UNESCAPED_UNICODE) . "\n";
        file_put_contents($this->logFile, $logLine, FILE_APPEND | LOCK_EX);
        
        // 写入数据库
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO monitoring_logs (status, data, created_at)
                VALUES (?, ?, NOW())
            ");
            $stmt->execute([
                $health['status'],
                json_encode($health, JSON_UNESCAPED_UNICODE)
            ]);
        } catch (Exception $e) {
            // 忽略数据库写入错误
        }
    }
    
    /**
     * 生成监控摘要
     */
    private function generateSummary($health) {
        $summary = [];
        
        foreach ($health['checks'] as $checkName => $check) {
            if ($check['status'] !== 'ok') {
                $summary[] = "$checkName: {$check['message']}";
            }
        }
        
        return empty($summary) ? '系统运行正常' : implode('; ', $summary);
    }
    
    /**
     * 初始化监控表
     */
    private function initMonitoringTables() {
        try {
            $sql = "
                CREATE TABLE IF NOT EXISTS monitoring_logs (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    status VARCHAR(20) NOT NULL,
                    data JSON,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_status (status),
                    INDEX idx_created_at (created_at)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            ";
            $this->pdo->exec($sql);
        } catch (Exception $e) {
            // 忽略表创建错误
        }
    }
    
    /**
     * 解析内存限制
     */
    private function parseMemoryLimit($limit) {
        $limit = trim($limit);
        $last = strtolower($limit[strlen($limit)-1]);
        $limit = (int) $limit;
        
        switch($last) {
            case 'g': $limit *= 1024;
            case 'm': $limit *= 1024;
            case 'k': $limit *= 1024;
        }
        
        return $limit;
    }
    
    /**
     * 格式化字节数
     */
    private function formatBytes($size) {
        $units = ['B', 'KB', 'MB', 'GB'];
        $unit = 0;
        
        while ($size >= 1024 && $unit < count($units) - 1) {
            $size /= 1024;
            $unit++;
        }
        
        return round($size, 2) . ' ' . $units[$unit];
    }
}

// 命令行接口
if (php_sapi_name() === 'cli') {
    echo "=== 一念修仙监控系统 ===\n\n";
    
    $monitor = new MonitoringSystem();
    $health = $monitor->healthCheck();
    
    echo "系统状态: " . strtoupper($health['status']) . "\n";
    echo "检查时间: " . $health['timestamp'] . "\n\n";
    
    foreach ($health['checks'] as $checkName => $check) {
        $statusIcon = $check['status'] === 'ok' ? '✅' : ($check['status'] === 'warning' ? '⚠️' : '❌');
        echo "$statusIcon $checkName: {$check['message']}\n";
        
        if (!empty($check['metrics'])) {
            foreach ($check['metrics'] as $metric => $value) {
                if (is_array($value)) {
                    echo "   $metric: " . json_encode($value, JSON_UNESCAPED_UNICODE) . "\n";
                } else {
                    echo "   $metric: $value\n";
                }
            }
        }
        echo "\n";
    }
}

?>
