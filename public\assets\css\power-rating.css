/* 战力系统样式 - 一念修仙项目 */

/* 战力值显示 - 幻彩设计 */
.power-rating-display {
    background: linear-gradient(135deg, #ff6b6b, #ffa726, #ffeb3b, #66bb6a, #42a5f5, #ab47bc);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: powerGradient 3s ease infinite;
    font-weight: bold;
    font-size: 14px;
    margin: 8px 0;
    text-align: center;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

/* 幻彩动画 */
@keyframes powerGradient {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* 装备详情中的战力显示 */
.equipment-power-display {
    text-align: center;
    margin: 10px 0;
    font-size: 16px;
    font-weight: bold;
    
    /* 更强烈的幻彩效果 */
    background: linear-gradient(45deg, 
        #ffd700,    /* 金色 */
        #ff69b4,    /* 粉色 */
        #8a2be2,    /* 紫色 */
        #00bfff,    /* 天蓝 */
        #32cd32     /* 绿色 */
    );
    background-size: 400% 400%;
    
    /* 文字渐变效果 */
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    
    /* 兼容性回退 */
    color: #e67e22;
    
    animation: equipmentPowerGlow 2s ease-in-out infinite;
    
    padding: 8px 12px;
    border-radius: 8px;
    border: 2px solid transparent;
    background-origin: border-box;
    position: relative;
}

/* 兼容性处理：如果不支持background-clip: text */
@supports not (-webkit-background-clip: text) {
    .equipment-power-display {
        color: #e67e22;
        background: rgba(230, 126, 34, 0.1);
        text-shadow: 0 0 10px rgba(230, 126, 34, 0.8);
    }
}

@keyframes equipmentPowerGlow {
    0%, 100% {
        background-position: 0% 50%;
        border-color: rgba(255, 215, 0, 0.6);
    }
    50% {
        background-position: 100% 50%;
        border-color: rgba(138, 43, 226, 0.6);
    }
}

/* 装备升级箭头 */
.power-upgrade-arrow {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    z-index: 10;
    color: white;
    
    /* 默认样式 - 更好装备（绿色上箭头） */
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    box-shadow: 
        0 2px 4px rgba(0,0,0,0.3),
        0 0 8px rgba(39, 174, 96, 0.5);
    
    /* 动画效果 */
    animation: upgradeArrowPulse 2s ease-in-out infinite;
    
    /* 兼容性 */
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* 更差装备箭头样式 */
.power-upgrade-arrow.worse {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    box-shadow: 
        0 2px 4px rgba(0,0,0,0.3),
        0 0 8px rgba(231, 76, 60, 0.5);
}

@keyframes upgradeArrowPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.9;
    }
    50% {
        transform: scale(1.1);
        opacity: 1;
    }
}

/* 武器槽位箭头 */
.weapon-slot-arrow {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
    z-index: 10;
    color: white;
    
    /* 更弱武器槽位（红色下箭头） */
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    box-shadow: 
        0 2px 4px rgba(0,0,0,0.3),
        0 0 8px rgba(231, 76, 60, 0.5);
    
    animation: weaponArrowPulse 2s ease-in-out infinite;
}

@keyframes weaponArrowPulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.15);
    }
}

/* 🔥 新增：装备比较箭头样式 */
.power-arrow {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: bold;
    color: #fff;
    z-index: 15;
    pointer-events: none;
    text-shadow: 0 1px 3px rgba(0,0,0,0.5);
    animation: arrowPulse 2s ease-in-out infinite;
}

/* 绿色上升箭头 */
.power-arrow.better {
    color: #00ff00;
    text-shadow: 0 0 8px #00ff00, 0 1px 3px rgba(0,0,0,0.5);
}

/* 红色下降箭头 */
.power-arrow.worse {
    color: #ff4444;
    text-shadow: 0 0 8px #ff4444, 0 1px 3px rgba(0,0,0,0.5);
}

/* 箭头脉冲动画 */
@keyframes arrowPulse {
    0%, 100% { 
        transform: scale(1);
        opacity: 0.8;
    }
    50% { 
        transform: scale(1.2);
        opacity: 1;
    }
}

/* 武器槽位对比图标 */
.power-comparison-icon {
    position: absolute;
    top: 5px;
    right: 5px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;
    border-radius: 50%;
    z-index: 10;
    text-shadow: 0 1px 2px rgba(0,0,0,0.5);
}

.power-comparison-icon.better {
    color: #00ff00;
    text-shadow: 0 0 6px #00ff00, 0 1px 2px rgba(0,0,0,0.5);
}

.power-comparison-icon.worse {
    color: #ff4444;
    text-shadow: 0 0 6px #ff4444, 0 1px 2px rgba(0,0,0,0.5);
}

.power-comparison-icon.equal {
    color: #ffaa00;
    text-shadow: 0 0 6px #ffaa00, 0 1px 2px rgba(0,0,0,0.5);
}

/* 武器槽位选择弹窗中的图例 */
.power-legend {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin: 10px 0;
    font-size: 12px;
    color: #ccc;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 4px;
}

/* 武器槽位选择的额外样式 */
.weapon-slot-option {
    position: relative;
    overflow: visible;
}

/* 威胁等级显示 */
.threat-level-display {
    font-size: 14px;
    margin: 5px 0;
    font-weight: bold;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .power-rating-display {
        font-size: 12px;
        margin: 6px 0;
    }
    
    .power-arrow {
        width: 20px;
        height: 20px;
        font-size: 14px;
        bottom: 1px;
        right: 1px;
    }
    
    .power-comparison-icon {
        width: 18px;
        height: 18px;
        font-size: 12px;
        top: 3px;
        right: 3px;
    }
    
    .power-legend {
        gap: 15px;
        font-size: 11px;
    }
}

/* 极小屏幕适配 */
@media (max-width: 480px) {
    .power-rating-display {
        font-size: 11px;
        padding: 2px 4px;
    }
    
    .equipment-power-display {
        font-size: 13px;
        padding: 4px 6px;
    }
    
    .power-upgrade-arrow {
        width: 16px;
        height: 16px;
        font-size: 9px;
    }
    
    .weapon-slot-arrow {
        width: 14px;
        height: 14px;
        font-size: 8px;
    }
}

/* 高对比度支持 */
@media (prefers-contrast: high) {
    .power-rating-display {
        background: #000;
        color: #fff;
        border: 2px solid #fff;
        text-shadow: none;
    }
    
    .equipment-power-display {
        background: #000;
        color: #fff;
        border: 2px solid #fff;
        -webkit-text-fill-color: #fff;
    }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
    .power-rating-display,
    .equipment-power-display,
    .power-upgrade-arrow,
    .weapon-slot-arrow {
        animation: none;
    }
    
    .power-rating-display {
        background-position: 0% 50%;
    }
    
    .equipment-power-display {
        background-position: 0% 50%;
    }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
    .power-rating-display {
        border-color: rgba(230, 126, 34, 0.6);
        background: linear-gradient(45deg, 
            rgba(255, 215, 0, 0.3),
            rgba(255, 105, 180, 0.3),
            rgba(138, 43, 226, 0.3),
            rgba(0, 191, 255, 0.3),
            rgba(50, 205, 50, 0.3)
        );
    }
    
    .threat-level-display {
        background: rgba(255, 255, 255, 0.1);
    }
}

/* iOS Safari特殊处理 */
@supports (-webkit-touch-callout: none) {
    .power-rating-display,
    .equipment-power-display {
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
    }
}

/* 确保箭头在各种背景下可见 */
.power-upgrade-arrow,
.weapon-slot-arrow {
    box-sizing: border-box;
    text-align: center;
    line-height: 1;
}

/* 防止箭头被遮挡 */
.inventory-item {
    position: relative;
    overflow: visible;
}

.weapon-slot {
    position: relative;
    overflow: visible;
}

/* 战力值不同等级的颜色 */
.power-rating-display.low {
    color: #95a5a6;
    background: rgba(149, 165, 166, 0.1);
}

.power-rating-display.medium {
    color: #3498db;
    background: rgba(52, 152, 219, 0.1);
}

.power-rating-display.high {
    color: #e67e22;
    background: rgba(230, 126, 34, 0.1);
}

.power-rating-display.very-high {
    color: #9b59b6;
    background: rgba(155, 89, 182, 0.1);
}

.power-rating-display.extreme {
    color: #e74c3c;
    background: rgba(231, 76, 60, 0.1);
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

/* 在物品详情弹窗中的战力显示 */
.item-detail-popup .power-rating-display {
    margin: 8px 0;
    font-size: 16px;
    padding: 4px 8px;
}

/* 在属性页面中的战力显示 */
.attributes-container .power-rating-display {
    font-size: 18px;
    padding: 8px 12px;
    margin: 10px 0;
    text-align: center;
    border: 2px solid rgba(230, 126, 34, 0.3);
}

/* 在背包中的升级箭头特殊效果 */
.inventory-item:hover .power-upgrade-arrow {
    animation-duration: 1s;
    transform: scale(1.2);
}

/* 战力对比提示（如果需要） */
.power-comparison-hint {
    position: absolute;
    bottom: -25px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.power-upgrade-arrow:hover + .power-comparison-hint,
.power-upgrade-arrow:hover .power-comparison-hint {
    opacity: 1;
} 