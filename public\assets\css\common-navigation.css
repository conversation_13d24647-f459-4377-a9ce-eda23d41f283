/* 底部导航栏样式 */
.bottom-navigation {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 70px;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.7));
    border-top: 2px solid rgba(212, 175, 55, 0.5);
    display: flex;
    justify-content: space-around;
    align-items: center;
    z-index: 100;
    backdrop-filter: blur(10px);
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);
    margin: 0;
    padding: 0;
    padding-bottom: env(safe-area-inset-bottom, 0px);
    box-sizing: border-box;
}

/* 🔧 PWA模式特殊处理 */
@media (display-mode: standalone) {
    .bottom-navigation {
        bottom: 0 !important;
        height: calc(70px + env(safe-area-inset-bottom, 0px));
        padding-bottom: env(safe-area-inset-bottom, 0px);
    }
}

/* 🔧 移动端浏览器地址栏隐藏后的适配 */
@supports (-webkit-touch-callout: none) {
    /* iOS Safari */
    .bottom-navigation {
        bottom: 0;
        padding-bottom: env(safe-area-inset-bottom, 0px);
    }
}

.nav-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    color: #fff;
    cursor: pointer;
    padding: 8px 16px;
    border-radius: 10px;
    transition: all 0.3s ease;
    min-width: 60px;
    text-decoration: none;
}

.nav-btn:hover {
    background: rgba(212, 175, 55, 0.2);
    transform: translateY(-2px);
    color: #fff;
}

.nav-btn:active {
    transform: scale(0.95);
}

.nav-btn.active {
    background: rgba(212, 175, 55, 0.3);
    color: #d4af37;
}

.nav-btn-icon {
    font-size: 20px;
    margin-bottom: 3px;
}

.nav-btn-text {
    font-size: 11px;
    color: #bdc3c7;
}

.nav-btn.active .nav-btn-text {
    color: #d4af37;
}

/* 响应式优化 */
@media (max-width: 360px) {
    .bottom-navigation {
        height: 65px;
    }
    
    .nav-btn {
        padding: 6px 12px;
    }
    
    .nav-btn-icon {
        font-size: 18px;
    }
    
    .nav-btn-text {
        font-size: 10px;
    }
}

/* 🔧 超小屏设备优化 */
@media (max-width: 320px) {
    .bottom-navigation {
        height: 60px;
    }
    
    .nav-btn {
        padding: 4px 8px;
        min-width: 50px;
    }
    
    .nav-btn-icon {
        font-size: 16px;
        margin-bottom: 2px;
    }
    
    .nav-btn-text {
        font-size: 9px;
    }
} 