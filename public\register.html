<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover">

    <!-- 🔧 项目配置文件 - 必须早期加载 -->
    <script src="assets/js/config.js"></script>

    <!-- 新增HBuilder X优化meta标签 -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="format-detection" content="telephone=no">
    <!-- PWA支持 -->
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#d4af37">       
    <!-- 原有CSS保持不变 -->
    <link rel="stylesheet" href="assets/css/global.css">
    <link rel="stylesheet" href="assets/css/register.css">
    <!-- 移动端CSS已删除 -->
    <title>一念战斗 - 注册账号</title>
</head>
<body>
    <div class="register-container">
        <h1 class="register-title">注册账号</h1>
        
        <div id="message" class="message" style="display: none;"></div>
        <div id="loading" class="loading">注册中...</div>
        
        <form id="registerForm">
            <div class="form-group">
                <label for="username" class="form-label">用户名</label>
                <input type="text" id="username" name="username" class="form-input" placeholder="请输入用户名（3-20个字符）" required>
            </div>
            
            <div class="form-group">
                <label for="email" class="form-label">邮箱</label>
                <input type="email" id="email" name="email" class="form-input" placeholder="请输入邮箱地址" required>
            </div>
            
            <div class="form-group">
                <label for="password" class="form-label">密码</label>
                <input type="password" id="password" name="password" class="form-input" placeholder="请输入密码（至少6位）" required>
            </div>
            
            <div class="form-group">
                <label for="confirmPassword" class="form-label">确认密码</label>
                <input type="password" id="confirmPassword" name="confirmPassword" class="form-input" placeholder="请再次输入密码" required>
            </div>
            
            <button type="submit" id="registerBtn" class="register-button">注册账号</button>
        </form>
        
        <div class="links">
            <a href="login.html">已有账号？点击登录</a>
            <a href="index.html">返回主页</a>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('registerForm');
            const messageDiv = document.getElementById('message');
            const loadingDiv = document.getElementById('loading');
            const registerBtn = document.getElementById('registerBtn');
            
            // 获取CSRF令牌
            let csrfToken = '';
            fetch(window.GameConfig ? window.GameConfig.getApiUrl('csrf.php') : '../src/api/csrf.php')
                .then(response => response.json())
                .then(data => {
                    csrfToken = data.token;
                })
                .catch(err => console.error('获取CSRF令牌失败:', err));
            
            function showMessage(text, type) {
                messageDiv.textContent = text;
                messageDiv.className = `message ${type}`;
                messageDiv.style.display = 'block';
            }
            
            function hideMessage() {
                messageDiv.style.display = 'none';
            }
            
            function showLoading() {
                loadingDiv.style.display = 'block';
                registerBtn.disabled = true;
            }
            
            function hideLoading() {
                loadingDiv.style.display = 'none';
                registerBtn.disabled = false;
            }
            
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                hideMessage();
                
                const formData = new FormData(form);
                const data = {
                    username: formData.get('username'),
                    email: formData.get('email'),
                    password: formData.get('password'),
                    confirmPassword: formData.get('confirmPassword'),
                    csrf_token: csrfToken
                };
                
                // 前端验证
                if (data.password !== data.confirmPassword) {
                    showMessage('两次输入的密码不一致', 'error');
                    return;
                }
                
                if (data.password.length < 6) {
                    showMessage('密码至少需要6位字符', 'error');
                    return;
                }
                
                showLoading();

                fetch(window.GameConfig ? window.GameConfig.getApiUrl('register.php') : '../src/api/register.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                })
                .then(response => response.json())
                .then(result => {
                    hideLoading();
                    
                    if (result.success) {
                        showMessage(result.message, 'success');
                        const username = data.username;
                        setTimeout(() => {
                            window.location.href = `login.html?username=${encodeURIComponent(username)}`;
                        }, 2000);
                    } else {
                        showMessage(result.message, 'error');
                    }
                })
                .catch(error => {
                    hideLoading();
                    console.error('注册失败:', error);
                    showMessage('注册失败，请稍后重试', 'error');
                });
            });
        });
    </script>
</body>
</html> 