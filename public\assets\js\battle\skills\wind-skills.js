/**
 * 风系技能模块 - 风刃术
 * 适用于 item_skills 表中的 animation_model = 'fengrensu'
 */

// 风系技能 - 风刃术
class FengRenSuSkill extends BaseSkill {
    async execute(skillData, weaponImage) {
        // 使用数据库中的真实技能名称，而不是硬编码
        const skillName = skillData?.skillName || skillData?.displayName || '风刃术'; // 提供默认值作为后备
        await this.showSkillShout(skillName);
        
        // 执行风刃术动画，传递武器图片
        await this.createFengRenSuAnimation(weaponImage);
    }
    
    async createFengRenSuAnimation(weaponImage) {
        // 🔧 动态判断位置映射
        let casterPos, targetPos;
        
        if (this.isEnemySkill) {
            // 敌方技能：敌人是施法者，玩家是目标
            casterPos = this.getCharacterPosition(false); // 敌人位置
            targetPos = this.getCharacterPosition(true);  // 玩家位置
        } else {
            // 我方技能：玩家是施法者，敌人是目标
            casterPos = this.getCharacterPosition(true);  // 玩家位置
            targetPos = this.getCharacterPosition(false); // 敌人位置
        }
        
        // 创建动画容器
        const container = document.createElement('div');
        container.className = 'fengrensu-container';
        container.style.position = 'absolute';
        container.style.top = '0';
        container.style.left = '0';
        container.style.width = '100%';
        container.style.height = '100%';
        container.style.pointerEvents = 'none';
        container.style.zIndex = '1000';
        
        this.effectsContainer.appendChild(container);
        
        try {
            // === 第一阶段：蓄力 - 风元素汇聚 ===
            await this.createChargePhase(container, casterPos.x, casterPos.y, weaponImage);
            
            // === 第二阶段：发射 - 快速风刃穿透 ===
            await this.createBladeStrikePhase(container, casterPos.x, casterPos.y, targetPos.x, targetPos.y);
            
        } finally {
            // 清理容器
            setTimeout(() => {
                if (container && container.parentNode) {
                    container.parentNode.removeChild(container);
                }
            }, 100);
        }
    }
    
    // 蓄力阶段：风元素汇聚（简化版）
    async createChargePhase(container, startX, startY, weaponImage) {
        // 创建风系魔法阵
        const windCircle = document.createElement('div');
        windCircle.className = 'fengrensu-magic-circle';
        windCircle.style.left = `${startX}px`;
        windCircle.style.top = `${startY}px`;
        container.appendChild(windCircle);
        
        // 创建武器图片在中心旋转
        if (weaponImage) {
            const weaponSprite = document.createElement('div');
            weaponSprite.className = 'fengrensu-weapon-sprite';
            weaponSprite.style.left = `${startX}px`;
            weaponSprite.style.top = `${startY}px`;
            
            // 添加武器图片背景
            this.addWeaponImage(weaponSprite, weaponImage);
            // 🗡️ 动态调整武器图片角度
            const weaponImg = weaponSprite.querySelector('.weapon-image');
            if (weaponImg) {
                weaponImg.style.transform = `rotate(${this.calculateInitialSwordRotation()}deg)`;
            }
            
            container.appendChild(weaponSprite);
        }
        
        // 创建蓄力能量核心
        const energyCore = document.createElement('div');
        energyCore.className = 'fengrensu-energy-core';
        energyCore.style.left = `${startX}px`;
        energyCore.style.top = `${startY}px`;
        container.appendChild(energyCore);
        
        // 创建风元素粒子汇聚效果（减少数量，增加速度感）
        for (let i = 0; i < 20; i++) {
            const windParticle = document.createElement('div');
            windParticle.className = 'fengrensu-charge-wind';
            windParticle.style.left = `${startX}px`;
            windParticle.style.top = `${startY}px`;
            
            const angle = Math.random() * Math.PI * 2;
            const radius = 15 + Math.random() * 35;
            const moveX = Math.cos(angle) * radius;
            const moveY = Math.sin(angle) * radius;
            
            windParticle.style.setProperty('--chargeX', `${moveX}px`);
            windParticle.style.setProperty('--chargeY', `${moveY}px`);
            windParticle.style.animationDelay = `${Math.random() * 0.6}s`;
            
            container.appendChild(windParticle);
        }
        
        // 创建环绕的气流螺旋（减少数量）
        for (let i = 0; i < 12; i++) {
            const angle = (i / 12) * Math.PI * 2;
            const radius = 50;
            const spiralX = startX + Math.cos(angle) * radius;
            const spiralY = startY + Math.sin(angle) * radius;
            
            const spiral = document.createElement('div');
            spiral.className = 'fengrensu-charge-spiral';
            spiral.style.left = `${spiralX}px`;
            spiral.style.top = `${spiralY}px`;
            spiral.style.animationDelay = `${i * 0.03}s`;
            container.appendChild(spiral);
        }
        
        // 创建风压波纹（减少数量）
        for (let i = 0; i < 3; i++) {
            const ripple = document.createElement('div');
            ripple.className = 'fengrensu-energy-ripple';
            ripple.style.left = `${startX}px`;
            ripple.style.top = `${startY}px`;
            ripple.style.animationDelay = `${i * 0.15}s`;
            container.appendChild(ripple);
        }
        
        // 等待蓄力完成（缩短时间）
        await this.wait(600);
        
        // 移除蓄力效果
        windCircle.remove();
        energyCore.remove();
        document.querySelectorAll('.fengrensu-charge-wind').forEach(w => w.remove());
        document.querySelectorAll('.fengrensu-charge-spiral').forEach(s => s.remove());
        document.querySelectorAll('.fengrensu-energy-ripple').forEach(r => r.remove());
        document.querySelectorAll('.fengrensu-weapon-sprite').forEach(w => w.remove());
    }
    
    // 发射阶段：快速风刃穿透
    async createBladeStrikePhase(container, startX, startY, endX, endY) {
        // 预发射气流爆发
        const preBurst = document.createElement('div');
        preBurst.className = 'fengrensu-pre-burst';
        preBurst.style.left = `${startX}px`;
        preBurst.style.top = `${startY}px`;
        container.appendChild(preBurst);
        
        await this.wait(100);
        
        // 计算方向和距离
        const deltaX = endX - startX;
        const deltaY = endY - startY;
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
        const angle = Math.atan2(deltaY, deltaX);
        
        // 计算穿透后的最终位置（继续飞行一段距离）
        const penetrateDistance = 150;
        const finalX = endX + Math.cos(angle) * penetrateDistance;
        const finalY = endY + Math.sin(angle) * penetrateDistance;
        
        // 创建主风刃（横向，类似飞剑但是横向的）
        const mainBlade = document.createElement('div');
        mainBlade.className = 'fengrensu-main-blade';
        mainBlade.style.left = `${startX}px`;
        mainBlade.style.top = `${startY}px`;
        
        // 设置风刃的旋转角度（横向）
        mainBlade.style.setProperty('--bladeAngle', `${angle * 180 / Math.PI}deg`);
        mainBlade.style.setProperty('--targetX', `${finalX - startX}px`);
        mainBlade.style.setProperty('--targetY', `${finalY - startY}px`);
        mainBlade.style.setProperty('--hitX', `${endX - startX}px`);
        mainBlade.style.setProperty('--hitY', `${endY - startY}px`);
        
        container.appendChild(mainBlade);
        
        // 创建两个副风刃（上下偏移）
        for (let i = 0; i < 2; i++) {
            const subBlade = document.createElement('div');
            subBlade.className = 'fengrensu-sub-blade';
            
            // 计算垂直偏移
            const perpAngle = angle + Math.PI / 2;
            const offset = (i === 0 ? -20 : 20); // 上下偏移
            const offsetX = Math.cos(perpAngle) * offset;
            const offsetY = Math.sin(perpAngle) * offset;
            
            subBlade.style.left = `${startX + offsetX}px`;
            subBlade.style.top = `${startY + offsetY}px`;
            subBlade.style.setProperty('--bladeAngle', `${angle * 180 / Math.PI}deg`);
            subBlade.style.setProperty('--targetX', `${finalX - startX - offsetX}px`);
            subBlade.style.setProperty('--targetY', `${finalY - startY - offsetY}px`);
            subBlade.style.animationDelay = `${i * 0.05}s`;
            
            container.appendChild(subBlade);
        }
        
        // 创建风刃拖尾效果
        for (let i = 0; i < 6; i++) {
            const trail = document.createElement('div');
            trail.className = 'fengrensu-blade-trail';
            trail.style.left = `${startX}px`;
            trail.style.top = `${startY}px`;
            trail.style.setProperty('--bladeAngle', `${angle * 180 / Math.PI}deg`);
            trail.style.setProperty('--targetX', `${finalX - startX}px`);
            trail.style.setProperty('--targetY', `${finalY - startY}px`);
            trail.style.setProperty('--trailIndex', i);
            trail.style.animationDelay = `${i * 0.02}s`;
            
            container.appendChild(trail);
        }
        
        // 创建气流切割线
        for (let i = 0; i < 4; i++) {
            const cutLine = document.createElement('div');
            cutLine.className = 'fengrensu-cut-line';
            cutLine.style.left = `${startX}px`;
            cutLine.style.top = `${startY}px`;
            cutLine.style.setProperty('--bladeAngle', `${angle * 180 / Math.PI}deg`);
            cutLine.style.setProperty('--targetX', `${finalX - startX}px`);
            cutLine.style.setProperty('--targetY', `${finalY - startY}px`);
            cutLine.style.setProperty('--lineIndex', i);
            cutLine.style.animationDelay = `${i * 0.03}s`;
            
            container.appendChild(cutLine);
        }
        
        // 计算击中时间（与CSS动画同步）
        // 主风刃动画1.2秒，在75%时到达敌人位置
        const hitTime = 1.2 * 0.75; // 0.9秒
        
        // 在风刃击中敌人时创建穿透特效
        setTimeout(() => {
            this.createPenetrateEffect(container, endX, endY);
        }, hitTime * 1000);
        
        // 等待风刃完全穿透（主风刃动画完成 + 额外0.5秒）
        await this.wait((1.2 + 0.5) * 1000);
        
        // 移除发射效果
        preBurst.remove();
        mainBlade.remove();
        document.querySelectorAll('.fengrensu-sub-blade').forEach(b => b.remove());
        document.querySelectorAll('.fengrensu-blade-trail').forEach(t => t.remove());
        document.querySelectorAll('.fengrensu-cut-line').forEach(c => c.remove());
    }
    
    // 穿透效果：简单的切割特效
    createPenetrateEffect(container, hitX, hitY) {
        // 创建穿透闪光
        const penetrateFlash = document.createElement('div');
        penetrateFlash.className = 'fengrensu-penetrate-flash';
        penetrateFlash.style.left = `${hitX}px`;
        penetrateFlash.style.top = `${hitY}px`;
        container.appendChild(penetrateFlash);
        
        // 创建切割痕迹
        for (let i = 0; i < 3; i++) {
            const cutMark = document.createElement('div');
            cutMark.className = 'fengrensu-cut-mark';
            cutMark.style.left = `${hitX}px`;
            cutMark.style.top = `${hitY}px`;
            cutMark.style.setProperty('--markAngle', `${(i - 1) * 15}deg`);
            cutMark.style.animationDelay = `${i * 0.02}s`;
            container.appendChild(cutMark);
        }
        
        // 创建风压扩散（简化版）
        for (let i = 0; i < 8; i++) {
            const windBurst = document.createElement('div');
            windBurst.className = 'fengrensu-wind-burst-simple';
            windBurst.style.left = `${hitX}px`;
            windBurst.style.top = `${hitY}px`;
            
            const angle = (i / 8) * Math.PI * 2;
            const distance = 30 + Math.random() * 20;
            windBurst.style.setProperty('--burstAngle', `${angle * 180 / Math.PI}deg`);
            windBurst.style.setProperty('--burstDistance', `${distance}px`);
            windBurst.style.animationDelay = `${i * 0.01}s`;
            
            container.appendChild(windBurst);
        }
        
        // 击中特效
        this.createHitEffect(hitX, hitY, true);
        
        // 🔧 修复：给被攻击者添加切割效果
        const targetSelector = !this.isEnemySkill ? '.enemy .character-sprite' : '.player .character-sprite';
        const targetSprite = document.querySelector(targetSelector);
        if (targetSprite) {
            targetSprite.style.animation = 'wind-cut 0.8s ease-out, wind-shake 0.1s ease-in-out 3';
            
            // 恢复目标动画
            setTimeout(() => {
                if (targetSprite) {
                    targetSprite.style.animation = '';
                }
            }, 800);
        }
        
        // 清理穿透效果
        setTimeout(() => {
            penetrateFlash.remove();
            document.querySelectorAll('.fengrensu-cut-mark').forEach(m => m.remove());
            document.querySelectorAll('.fengrensu-wind-burst-simple').forEach(b => b.remove());
        }, 600);
    }

    // 🗡️ 动态计算剑的初始旋转角度
    calculateInitialSwordRotation() {
        // 敌方技能：剑尖向下（指向玩家），我方技能：剑尖向上（指向敌人）
        return this.isEnemySkill ? 0 : 180;
    }
}

// 导出技能类
window.WindSkills = { FengRenSuSkill }; 