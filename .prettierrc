{"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 100, "tabWidth": 4, "useTabs": false, "bracketSpacing": true, "arrowParens": "avoid", "endOfLine": "lf", "htmlWhitespaceSensitivity": "css", "insertPragma": false, "jsxBracketSameLine": false, "jsxSingleQuote": true, "proseWrap": "preserve", "quoteProps": "as-needed", "requirePragma": false, "vueIndentScriptAndStyle": false, "embeddedLanguageFormatting": "auto", "overrides": [{"files": "*.php", "options": {"parser": "php", "phpVersion": "7.4", "printWidth": 120, "tabWidth": 4}}, {"files": "*.html", "options": {"parser": "html", "printWidth": 120, "tabWidth": 4}}, {"files": "*.css", "options": {"parser": "css", "printWidth": 100, "tabWidth": 4}}]}