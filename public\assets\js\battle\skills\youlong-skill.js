/**
 * 游龙剑技能模块
 * 对应 animation_model = 'youlong'
 */

/**
 * 游龙剑技能类
 * 继承BaseSkill，遵循v2.0架构规范
 */
class YouLongJianSkill extends BaseSkill {
    constructor(battleSystem) {
        super(battleSystem);
        this.skillName = '游龙剑';
        this.elementType = 'sword';
        
        // v2.0新增：技能实例管理
        this.animationContainers = new Set();
        this.activeTimers = new Set();
    }

    async execute(skillData, weaponImage) {
        try {
            // 🔧 修复：使用真实技能名称而不是动画名称
            const skillName = skillData?.skillName || skillData?.displayName || this.skillName || '游龙剑';
            
            // 必须调用技能喊话
            await this.showSkillShout(skillName);
            
            // 调用具体的技能动画方法
            await this.createYouLong<PERSON>ian(weaponImage);
            
        } catch (error) {
            console.error(`❌ ${this.skillName} 执行失败:`, error);
            // v2.0新增：错误处理机制
            this.handleError(error, 'execute');
        }
    }
    
    async createYou<PERSON>ong<PERSON><PERSON>(weaponImage) {
        // 🔧 动态判断位置映射
        let casterPos, targetPos;
        
        if (this.isEnemySkill) {
            // 敌方技能：敌人是施法者，玩家是目标
            casterPos = this.getCharacterPosition(false); // 敌人位置
            targetPos = this.getCharacterPosition(true);  // 玩家位置
        } else {
            // 我方技能：玩家是施法者，敌人是目标
            casterPos = this.getCharacterPosition(true);  // 玩家位置
            targetPos = this.getCharacterPosition(false); // 敌人位置
        }
        
        console.log(`🐉 游龙剑位置计算:`, {
            起点: {x: casterPos.x, y: casterPos.y},
            终点: {x: targetPos.x, y: targetPos.y}
        });
        
        // 创建技能动画容器
        const container = this.createElement('youlong-container', {
            style: {
                position: 'absolute',
                top: '0',
                left: '0',
                width: '100%',
                height: '100%',
                pointerEvents: 'none',
                zIndex: 1000
            }
        });
        
        // v2.0新增：容器管理
        this.animationContainers.add(container);
        this.effectsContainer.appendChild(container);
        
        try {
            // 第一阶段：巨剑开场旋转效果，小剑按时机出现（顺时针，1.5秒）
            const generatedSwords = await this.createRotatingPhase(container, casterPos, weaponImage);
            
            // 第二阶段：6把小剑蛇形前进飞向敌人（1.2秒）
            await this.createSnakeMovement(generatedSwords, casterPos, targetPos);
            
            // 受击后技能立即结束，不再执行穿透效果
            console.log(`🐉 游龙剑技能完成，受击后立即结束`);
            
        } finally {
            // v2.0优化：安全清理机制
            this.safeCleanupContainer(container);
        }
    }
    
    // 第一阶段：巨剑开场旋转效果（顺时针），小剑按时机出现
    async createRotatingPhase(container, casterPos, weaponImage) {
        console.log(`🔄 游龙剑旋转阶段开始`);
        
        // 创建主剑（中心旋转，参考巨剑术但改为顺时针）
        const mainSword = this.createRotatingSword(weaponImage, casterPos, 0, true);
        container.appendChild(mainSword);
        
        // 创建2个残影效果
        const shadow1 = this.createRotatingSword(weaponImage, casterPos, 120, false);
        shadow1.style.opacity = '0.3';
        shadow1.style.filter = 'blur(1px)';
        shadow1.style.zIndex = '199';
        container.appendChild(shadow1);
        
        const shadow2 = this.createRotatingSword(weaponImage, casterPos, 240, false);
        shadow2.style.opacity = '0.2';
        shadow2.style.filter = 'blur(2px)';
        shadow2.style.zIndex = '198';
        container.appendChild(shadow2);
        
        // 开始快速连续旋转，持续到所有小剑出现完毕
        mainSword.style.animation = 'youlong-main-rotate 1.5s linear forwards';
        
        // 残影延迟启动，形成拖尾效果
        setTimeout(() => {
            shadow1.style.animation = 'youlong-shadow-rotate 1.5s linear forwards';
        }, 100);
        
        setTimeout(() => {
            shadow2.style.animation = 'youlong-shadow-rotate 1.5s linear forwards';
        }, 200);
        
        // 在旋转过程中按时机生成6把小剑
        const generatedSwords = [];
        const radius = 60; // 小剑生成半径
        
        // 6把剑按6分之1圆周时机出现（每250ms一把）
        for (let i = 0; i < 6; i++) {
            setTimeout(() => {
                // 计算小剑出现位置（按顺时针，6等分圆周）
                const angle = i * 60; // 0, 60, 120, 180, 240, 300度
                const radian = (angle * Math.PI) / 180;
                const swordX = casterPos.x + radius * Math.cos(radian);
                const swordY = casterPos.y + radius * Math.sin(radian);
                
                console.log(`⚔️ 第${i+1}把小剑出现，角度${angle}度，位置:`, {x: swordX, y: swordY});
                
                // 创建小剑并直接添加到generated数组
                const sword = this.createDragonSword(weaponImage, swordX, swordY, i);
                
                // 修复剑尖方向 - 让剑尖朝上（+180度翻转）
                sword.style.setProperty('--initialAngle', '180deg');
                
                container.appendChild(sword);
                generatedSwords.push({
                    element: sword,
                    index: i,
                    angle: radian,
                    startX: swordX,
                    startY: swordY
                });
            }, i * 250); // 每250ms出现一把剑
        }
        
        // 等待旋转和小剑生成完成
        await this.wait(1500);
        
        // 移除旋转剑
        this.safeRemoveElement(mainSword);
        this.safeRemoveElement(shadow1);
        this.safeRemoveElement(shadow2);
        
        console.log(`✅ 游龙剑旋转阶段完成，生成了${generatedSwords.length}把小剑`);
        
        // 返回生成的小剑数组，供下一阶段使用
        return generatedSwords;
    }
    

    
    // 真正的蛇形游动 - 6把剑形成连续的蛇身游向敌人
    async createSnakeMovement(dragonSwords, startPos, targetPos) {
        console.log(`🐍 游龙剑真正蛇形游动开始，${dragonSwords.length}把剑组成蛇身`);
        
        // 等待所有小剑生成完成
        await this.wait(300);
        
        // 计算蛇的游动路径参数
        const totalDistance = Math.sqrt(Math.pow(targetPos.x - startPos.x, 2) + Math.pow(targetPos.y - startPos.y, 2));
        const segments = 20; // 蛇形路径分段数
        const waveAmplitude = 80; // 蛇形波浪振幅（加大一倍：40 -> 80）
        const waveFrequency = 2; // 波浪频率
        
        // 为每把剑计算完整的蛇形路径
        dragonSwords.forEach((swordData, index) => {
            const sword = swordData.element;
            const swordOrder = index; // 剑在蛇身中的位置（0=蛇头，5=蛇尾）
            
            // 计算这把剑在蛇身中的相对位置
            const snakePosition = swordOrder / (dragonSwords.length - 1); // 0-1之间
            
            // 计算蛇形路径上的多个关键点
            const pathPoints = [];
            for (let i = 0; i <= segments; i++) {
                const progress = i / segments;
                
                // 基础直线路径
                const baseX = startPos.x + (targetPos.x - startPos.x) * progress;
                const baseY = startPos.y + (targetPos.y - startPos.y) * progress;
                
                // 蛇形波浪偏移（垂直于前进方向）
                const wavePhase = progress * waveFrequency * Math.PI * 2 - snakePosition * Math.PI * 0.5;
                const waveOffset = Math.sin(wavePhase) * waveAmplitude * (1 - progress * 0.2);
                
                // 计算垂直于前进方向的偏移
                const direction = Math.atan2(targetPos.y - startPos.y, targetPos.x - startPos.x);
                const perpDirection = direction + Math.PI / 2;
                const offsetX = Math.cos(perpDirection) * waveOffset;
                const offsetY = Math.sin(perpDirection) * waveOffset;
                
                pathPoints.push({
                    x: baseX + offsetX,
                    y: baseY + offsetY,
                    progress: progress
                });
            }
            
            // 为这把剑设置多个关键路径点
            const quarterPoint = pathPoints[Math.floor(segments * 0.25)];
            const halfPoint = pathPoints[Math.floor(segments * 0.5)];
            const threeQuarterPoint = pathPoints[Math.floor(segments * 0.75)];
            const endPoint = pathPoints[segments];
            
            // 计算穿透点
            const penetrateDistance = 120;
            const finalDirection = Math.atan2(endPoint.y - startPos.y, endPoint.x - startPos.x);
            const penetrateX = endPoint.x + Math.cos(finalDirection) * penetrateDistance;
            const penetrateY = endPoint.y + Math.sin(finalDirection) * penetrateDistance;
            
            // 设置CSS变量 - 完整的蛇形路径
            sword.style.setProperty('--startX', `${swordData.startX}px`);
            sword.style.setProperty('--startY', `${swordData.startY}px`);
            sword.style.setProperty('--quarter1X', `${quarterPoint.x}px`);
            sword.style.setProperty('--quarter1Y', `${quarterPoint.y}px`);
            sword.style.setProperty('--midX', `${halfPoint.x}px`);
            sword.style.setProperty('--midY', `${halfPoint.y}px`);
            sword.style.setProperty('--quarter3X', `${threeQuarterPoint.x}px`);
            sword.style.setProperty('--quarter3Y', `${threeQuarterPoint.y}px`);
            sword.style.setProperty('--targetX', `${endPoint.x}px`);
            sword.style.setProperty('--targetY', `${endPoint.y}px`);
            sword.style.setProperty('--penetrateX', `${penetrateX}px`);
            sword.style.setProperty('--penetrateY', `${penetrateY}px`);
            
            // 修复剑尖方向 - 飞行时也保持剑尖朝上（+180度翻转）
            sword.style.setProperty('--angle', '180deg');
            
            console.log(`🐉 第${index+1}把剑（蛇身位置${snakePosition.toFixed(2)}）:`, {
                起点: {x: swordData.startX, y: swordData.startY},
                路径点: [quarterPoint, halfPoint, threeQuarterPoint, endPoint],
                穿透点: {x: penetrateX, y: penetrateY},
                方向: '剑尖朝上(180度)'
            });
            
            // 蛇身连续游动 - 延迟很小，形成连续效果
            const delay = swordOrder * 50; // 减小延迟，让蛇身更连贯
            
            setTimeout(() => {
                // 蛇形游动到目标
                sword.style.animation = 'youlong-continuous-snake 1.0s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards';
                
                // 击中敌人时触发受击效果，但小剑继续穿透
                setTimeout(() => {
                    // 击中效果只在第一把剑击中时触发
                    if (swordOrder === 0) {
                        // 使用万剑诀相同的受击特效方法
                        if (this.battleSystem && this.battleSystem.createSwordHitEffect) {
                            this.battleSystem.createSwordHitEffect(targetPos.x, targetPos.y, true);
                        } else {
                            // 后备方案：使用BaseSkill的方法
                            this.createHitEffect(targetPos.x, targetPos.y, true);
                        }
                        
                        // 为敌人图片添加专用晃动动画
                        this.addEnemyShakeAnimation();
                    }
                }, 600); // 600ms击中时机
                
                // 击中敌人后从击中位置笔直向上飞行消失，按剑的顺序逐个飞出
                setTimeout(() => {
                    // 计算向上飞行终点（从击中位置笔直向上150px）
                    const flyUpDistance = 150;
                    const flyUpX = targetPos.x; // X坐标保持不变
                    const flyUpY = targetPos.y - flyUpDistance; // 向上飞行
                    
                    // 设置向上飞行终点CSS变量
                    sword.style.setProperty('--flyUpX', `${flyUpX}px`);
                    sword.style.setProperty('--flyUpY', `${flyUpY}px`);
                    
                    sword.style.animation = 'youlong-fly-up-exit 0.4s ease-out forwards';
                    console.log(`🐉 第${swordOrder + 1}把剑从击中位置向上飞行消失，终点: (${flyUpX.toFixed(1)}, ${flyUpY.toFixed(1)})`);
                }, 900 + swordOrder * 50); // 900ms开始向上飞出，每把剑间隔50ms
            }, delay);
        });
        
        // 等待所有剑完成向上飞行消失
        const maxDelay = (dragonSwords.length - 1) * 50;
        const lastSwordFlyTime = 900 + maxDelay + 400; // 最后一把剑开始向上飞行时间 + 飞行持续时间
        await this.wait(lastSwordFlyTime);
        
        console.log(`✅ 游龙剑蛇身游动完成`);
    }
    
    // 第三阶段：清理和特效（穿透已在第二阶段处理）
    async createPenetrateEffect(container, targetPos, dragonSwords) {
        console.log(`⚔️ 游龙剑清理阶段开始`);
        
        // 创建额外的游龙特效
        await this.createDragonImpactEffect(container, targetPos);
        
        // 等待所有剑完成穿透（最后一把剑的延迟 + 游动时间 + 向上飞出时间）
        const maxDelay = (dragonSwords.length - 1) * 50; // 最后一把剑的延迟
        const totalTime = maxDelay + 1000 + 600; // 延迟 + 游动(1.0s) + 向上飞出(0.6s)
        await this.wait(totalTime);
        
        // 清理所有剑
        dragonSwords.forEach(swordData => {
            this.safeRemoveElement(swordData.element);
        });
        
        console.log(`✅ 游龙剑清理阶段完成`);
    }
    
    // 创建游龙撞击特效
    async createDragonImpactEffect(container, targetPos) {
        // 龙形冲击波
        const dragonShockwave = this.createElement('youlong-dragon-shockwave', {
            style: {
                position: 'absolute',
                left: `${targetPos.x}px`,
                top: `${targetPos.y}px`,
                width: '80px',
                height: '80px',
                transform: 'translate(-50%, -50%)',
                background: 'radial-gradient(circle, rgba(255, 215, 0, 0.8) 0%, rgba(255, 140, 0, 0.6) 50%, transparent 100%)',
                borderRadius: '50%',
                animation: 'youlong-shockwave 0.4s ease-out forwards'
            }
        });
        container.appendChild(dragonShockwave);
        
        // 金色粒子爆散
        for (let i = 0; i < 12; i++) {
            const particle = this.createElement('youlong-impact-particle', {
                style: {
                    position: 'absolute',
                    left: `${targetPos.x}px`,
                    top: `${targetPos.y}px`,
                    width: '4px',
                    height: '4px',
                    background: 'radial-gradient(circle, #fff 0%, #ffd700 100%)',
                    borderRadius: '50%',
                    transform: 'translate(-50%, -50%)'
                }
            });
            
            const angle = (i / 12) * Math.PI * 2;
            const distance = 60 + Math.random() * 40;
            const moveX = Math.cos(angle) * distance;
            const moveY = Math.sin(angle) * distance;
            
            particle.style.setProperty('--particleX', `${moveX}px`);
            particle.style.setProperty('--particleY', `${moveY}px`);
            particle.style.animation = 'youlong-particle-scatter 0.6s ease-out forwards';
            
            container.appendChild(particle);
        }
        
        await this.wait(200);
        
        // 清理特效
        this.safeRemoveElement(dragonShockwave);
        document.querySelectorAll('.youlong-impact-particle').forEach(p => this.safeRemoveElement(p));
    }
    
    // 创建旋转剑（参考巨剑术）
    createRotatingSword(weaponImage, centerPos, startAngle, isMain) {
        const sword = this.createElement('youlong-rotating-sword', {
            style: {
                position: 'absolute',
                left: `${centerPos.x}px`,
                top: `${centerPos.y}px`,
                width: '40px',
                height: '80px',
                transform: `translate(-50%, -100%) scaleY(-1) rotate(${startAngle}deg)`,
                transformOrigin: 'center bottom'
            }
        });
        
        // 🔧 修复武器图片处理 - 避免双重发光效果
        if (weaponImage) {
            this.addWeaponImage(sword, weaponImage);
            // 🗡️ 动态调整武器图片角度
            const weaponImg = sword.querySelector('.weapon-image');
            if (weaponImg) {
                weaponImg.style.transform = `rotate(${this.calculateInitialSwordRotation()}deg)`;
            }
            // 使用武器图片时不给容器添加光晕，只对武器图片应用光晕
        } else {
            // 使用背景图片时才添加光晕类
            sword.classList.add('background-glow');
            sword.style.backgroundImage = `url('assets/images/battle_sword.png')`;
            sword.style.backgroundSize = 'contain';
            sword.style.backgroundRepeat = 'no-repeat';
            sword.style.backgroundPosition = 'center';
        }
        
        return sword;
    }
    
    // 🔧 修复：为被攻击者添加游龙剑专用晃动动画
    addEnemyShakeAnimation() {
        // 🔧 修复：动态判断被攻击目标的选择器
        let targetSelectors = [];
        if (!this.isEnemySkill) {
            // 我方技能攻击敌人
            targetSelectors = [
                '.enemy .character-sprite',
                '.enemy img',
                '.enemy .enemy-image',
                '.enemy .character-image',
                '.enemy-character',
                '.enemy'
            ];
        } else {
            // 敌方技能攻击玩家
            targetSelectors = [
                '.player .character-sprite',
                '.player img',
                '.player .player-image',
                '.player .character-image',
                '.player-character',
                '.player'
            ];
        }
        
        let targetElement = null;
        for (const selector of targetSelectors) {
            targetElement = document.querySelector(selector);
            if (targetElement) {
                console.log(`🎯 找到目标元素: ${selector}`);
                break;
            }
        }
        
        if (targetElement) {
            // 清除之前的动画
            targetElement.style.animation = '';
            
            // 强制重绘
            targetElement.offsetHeight;
            
            // 添加游龙剑专用晃动动画
            targetElement.style.animation = 'youlong-enemy-hit-shake 0.3s ease-in-out';
            
            console.log(`⚔️ 游龙剑：为${!this.isEnemySkill ? '敌人' : '玩家'}添加晃动动画`);
            
            // 动画结束后清理
            setTimeout(() => {
                if (targetElement) {
                    targetElement.style.animation = '';
                }
            }, 300);
        } else {
            console.warn(`⚠️ 游龙剑：未找到目标元素，无法添加晃动动画`);
        }
    }

    // 创建游龙剑（6把小剑）
    createDragonSword(weaponImage, startX, startY, index) {
        const sword = this.createElement('youlong-dragon-sword', {
            style: {
                position: 'absolute',
                left: `${startX}px`,
                top: `${startY}px`,
                width: '30px',
                height: '60px',
                transform: 'translate(-50%, -50%)',
                opacity: '0'
            }
        });
        
        // 🔧 修复武器图片处理 - 避免双重发光效果
        if (weaponImage) {
            this.addWeaponImage(sword, weaponImage);
            // 🗡️ 动态调整武器图片角度
            const weaponImg = sword.querySelector('.weapon-image');
            if (weaponImg) {
                weaponImg.style.transform = `rotate(${this.calculateInitialSwordRotation()}deg)`;
            }
            // 使用武器图片时不给容器添加光晕，只对武器图片应用光晕
        } else {
            // 使用背景图片时才添加光晕类
            sword.classList.add('background-glow');
            sword.style.backgroundImage = `url('assets/images/battle_sword.png')`;
            sword.style.backgroundSize = 'contain';
            sword.style.backgroundRepeat = 'no-repeat';
            sword.style.backgroundPosition = 'center';
        }
        
        // 小剑生成动画
        sword.style.animation = `youlong-sword-appear 0.3s ease-out ${index * 0.05}s forwards`;
        
        return sword;
    }

    // v2.0新增：安全清理方法
    safeRemoveElement(element) {
        if (element && element.parentNode) {
            try {
                element.parentNode.removeChild(element);
            } catch (error) {
                console.warn(`⚠️ 元素移除失败:`, error);
            }
        }
    }

    safeCleanupContainer(container) {
        if (container) {
            this.animationContainers.delete(container);
            
            // 延迟清理，确保动画完成
            const timer = setTimeout(() => {
                this.safeRemoveElement(container);
                this.activeTimers.delete(timer);
            }, 100);
            
            this.activeTimers.add(timer);
        }
    }

    // v2.0新增：技能实例清理方法
    cleanup() {
        // 清理所有容器
        this.animationContainers.forEach(container => {
            this.safeRemoveElement(container);
        });
        this.animationContainers.clear();

        // 清理所有定时器
        this.activeTimers.forEach(timer => {
            clearTimeout(timer);
        });
        this.activeTimers.clear();

        console.log(`✅ ${this.skillName} 实例已清理`);
    }

    // 🗡️ 动态计算剑的初始旋转角度
    calculateInitialSwordRotation() {
        // 敌方技能：剑尖向下（指向玩家），我方技能：剑尖向上（指向敌人）
        return this.isEnemySkill ? 180 : 0;
    }

    // v2.0新增：错误处理
    handleError(error, context) {
        const errorInfo = {
            skill: this.skillName,
            context: context,
            error: error.message,
            timestamp: Date.now()
        };

        // 上报错误到调试面板
        if (window.BattleDebugPanel) {
            window.BattleDebugPanel.addLog('error', `${this.skillName} ${context} 失败`, errorInfo);
        }
    }
}

// 导出技能类（必须按此格式）
window.YouLongJianSkills = { YouLongJianSkill };