<?php
require_once __DIR__ . '/../includes/functions.php';
setJsonResponse();

$action = isset($_GET['action']) ? $_GET['action'] : (isset($_POST['action']) ? $_POST['action'] : '');

if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => '请先登录']);
    exit;
}

$userId = $_SESSION['user_id'];

switch ($action) {
    case 'get_spirit_inventory':
        getSpiritInventory($userId);
        break;
    case 'get_all_spirits':
        getAllSpirits();
        break;
    case 'bind_spirit':
        bindSpirit($userId);
        break;
    case 'unbind_spirit':
        unbindSpirit($userId);
        break;
    case 'get_equipment_spirits':
        getEquipmentSpirits($userId);
        break;
    case 'spirit_synthesis':
        spiritSynthesis($userId);
        break;
    default:
        echo json_encode(['success' => false, 'message' => '未知操作']);
}

// 获取用户器灵库存
function getSpiritInventory($userId) {
    try {
        $pdo = getDatabase();
        $stmt = $pdo->prepare("
            SELECT usi.*, ss.name, ss.quality, ss.position, ss.attribute_bonus,
                   ss.special_effect, ss.drop_source, ss.image_url
            FROM user_spirit_inventory usi
            JOIN spirit_souls ss ON usi.spirit_id = ss.id
            WHERE usi.user_id = ?
            ORDER BY 
                CASE ss.quality 
                    WHEN 'legendary' THEN 5
                    WHEN 'epic' THEN 4
                    WHEN 'rare' THEN 3
                    WHEN 'uncommon' THEN 2
                    WHEN 'common' THEN 1
                END DESC, ss.name
        ");
        $stmt->execute([$userId]);
        $spirits = $stmt->fetchAll();
        
        // 解析属性加成JSON
        foreach ($spirits as &$spirit) {
            $spirit['attribute_bonus_parsed'] = json_decode($spirit['attribute_bonus'], true);
        }
        
        echo json_encode(['success' => true, 'spirits' => $spirits]);
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => '获取器灵库存失败: ' . $e->getMessage()]);
    }
}

// 获取所有器灵信息
function getAllSpirits() {
    try {
        $pdo = getDatabase();
        $stmt = $pdo->prepare("
            SELECT * FROM spirit_souls 
            ORDER BY 
                CASE quality 
                    WHEN 'legendary' THEN 5
                    WHEN 'epic' THEN 4
                    WHEN 'rare' THEN 3
                    WHEN 'uncommon' THEN 2
                    WHEN 'common' THEN 1
                END DESC, name
        ");
        $stmt->execute();
        $spirits = $stmt->fetchAll();
        
        // 解析属性加成JSON
        foreach ($spirits as &$spirit) {
            $spirit['attribute_bonus_parsed'] = json_decode($spirit['attribute_bonus'], true);
        }
        
        echo json_encode(['success' => true, 'spirits' => $spirits]);
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => '获取器灵信息失败: ' . $e->getMessage()]);
    }
}

// 器灵附魂到装备
function bindSpirit($userId) {
    $inventoryItemId = isset($_POST['inventory_item_id']) ? intval($_POST['inventory_item_id']) : 0;
    $spiritId = isset($_POST['spirit_id']) ? intval($_POST['spirit_id']) : 0;
    
    if (!$inventoryItemId || !$spiritId) {
        echo json_encode(['success' => false, 'message' => '参数错误']);
        return;
    }
    
    try {
        $pdo = getDatabase();
        $pdo->beginTransaction();
        
        // 检查装备是否属于用户
        $stmt = $pdo->prepare("
            SELECT ui.*, ei.name as equipment_name, et.slot_type
            FROM user_inventory ui
            JOIN equipment_items ei ON ui.item_id = ei.id
            JOIN equipment_types et ON ei.type_id = et.id
            WHERE ui.id = ? AND ui.user_id = ?
        ");
        $stmt->execute([$inventoryItemId, $userId]);
        $equipment = $stmt->fetch();
        
        if (!$equipment) {
            throw new Exception('装备不存在或不属于您');
        }
        
        // 检查器灵是否属于用户
        $stmt = $pdo->prepare("
            SELECT usi.*, ss.name as spirit_name, ss.quality, ss.position
            FROM user_spirit_inventory usi
            JOIN spirit_souls ss ON usi.spirit_id = ss.id
            WHERE usi.spirit_id = ? AND usi.user_id = ? AND usi.quantity > 0
        ");
        $stmt->execute([$spiritId, $userId]);
        $spirit = $stmt->fetch();
        
        if (!$spirit) {
            throw new Exception('器灵不存在或数量不足');
        }
        
        // 检查器灵位置匹配
        $equipmentCategory = getEquipmentCategory($equipment['slot_type']);
        if ($spirit['position'] != $equipmentCategory) {
            throw new Exception('器灵位置不匹配，该器灵只能附魂到' . $spirit['position'] . '类装备');
        }
        
        // 检查装备是否已经附魂
        $stmt = $pdo->prepare("SELECT id FROM equipment_spirit_binding WHERE inventory_item_id = ?");
        $stmt->execute([$inventoryItemId]);
        $existingBinding = $stmt->fetch();
        
        if ($existingBinding) {
            throw new Exception('该装备已经附魂，请先解除附魂');
        }
        
        // 创建附魂记录
        $stmt = $pdo->prepare("INSERT INTO equipment_spirit_binding (inventory_item_id, spirit_id) VALUES (?, ?)");
        $stmt->execute([$inventoryItemId, $spiritId]);
        
        // 减少器灵数量
        $stmt = $pdo->prepare("UPDATE user_spirit_inventory SET quantity = quantity - 1 WHERE spirit_id = ? AND user_id = ?");
        $stmt->execute([$spiritId, $userId]);
        
        // 清理数量为0的记录
        $stmt = $pdo->prepare("DELETE FROM user_spirit_inventory WHERE quantity <= 0");
        $stmt->execute();
        
        $pdo->commit();
        echo json_encode([
            'success' => true, 
            'message' => "成功将 {$spirit['spirit_name']} 附魂到 {$equipment['equipment_name']}"
        ]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

// 解除器灵附魂
function unbindSpirit($userId) {
    $inventoryItemId = isset($_POST['inventory_item_id']) ? intval($_POST['inventory_item_id']) : 0;
    
    if (!$inventoryItemId) {
        echo json_encode(['success' => false, 'message' => '缺少装备ID']);
        return;
    }
    
    try {
        $pdo = getDatabase();
        $pdo->beginTransaction();
        
        // 检查装备和附魂记录
        $stmt = $pdo->prepare("
            SELECT esb.*, ui.user_id, ei.name as equipment_name, ss.name as spirit_name
            FROM equipment_spirit_binding esb
            JOIN user_inventory ui ON esb.inventory_item_id = ui.id
            JOIN equipment_items ei ON ui.item_id = ei.id
            JOIN spirit_souls ss ON esb.spirit_id = ss.id
            WHERE esb.inventory_item_id = ? AND ui.user_id = ?
        ");
        $stmt->execute([$inventoryItemId, $userId]);
        $binding = $stmt->fetch();
        
        if (!$binding) {
            throw new Exception('附魂记录不存在或装备不属于您');
        }
        
        // 删除附魂记录
        $stmt = $pdo->prepare("DELETE FROM equipment_spirit_binding WHERE inventory_item_id = ?");
        $stmt->execute([$inventoryItemId]);
        
        // 返还器灵到库存
        $stmt = $pdo->prepare("
            INSERT INTO user_spirit_inventory (user_id, spirit_id, quantity) 
            VALUES (?, ?, 1)
            ON DUPLICATE KEY UPDATE quantity = quantity + 1
        ");
        $stmt->execute([$userId, $binding['spirit_id']]);
        
        $pdo->commit();
        echo json_encode([
            'success' => true, 
            'message' => "成功解除 {$binding['equipment_name']} 的附魂，{$binding['spirit_name']} 已返还"
        ]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

// 获取装备附魂信息
function getEquipmentSpirits($userId) {
    try {
        $pdo = getDatabase();
        $stmt = $pdo->prepare("
            SELECT esb.*, ui.id as inventory_item_id, ei.name as equipment_name,
                   ss.name as spirit_name, ss.quality, ss.attribute_bonus, ss.special_effect
            FROM equipment_spirit_binding esb
            JOIN user_inventory ui ON esb.inventory_item_id = ui.id
            JOIN equipment_items ei ON ui.item_id = ei.id
            JOIN spirit_souls ss ON esb.spirit_id = ss.id
            WHERE ui.user_id = ?
            ORDER BY ei.name
        ");
        $stmt->execute([$userId]);
        $bindings = $stmt->fetchAll();
        
        // 解析属性加成JSON
        foreach ($bindings as &$binding) {
            $binding['attribute_bonus_parsed'] = json_decode($binding['attribute_bonus'], true);
        }
        
        echo json_encode(['success' => true, 'bindings' => $bindings]);
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => '获取装备附魂信息失败: ' . $e->getMessage()]);
    }
}

// 器灵合成
function spiritSynthesis($userId) {
    $spiritId = isset($_POST['spirit_id']) ? intval($_POST['spirit_id']) : 0;
    $quantity = isset($_POST['quantity']) ? intval($_POST['quantity']) : 0;
    
    if (!$spiritId || $quantity < 3) {
        echo json_encode(['success' => false, 'message' => '合成需要至少3个同种器灵']);
        return;
    }
    
    try {
        $pdo = getDatabase();
        $pdo->beginTransaction();
        
        // 检查用户器灵数量
        $stmt = $pdo->prepare("
            SELECT usi.*, ss.name, ss.quality, ss.position
            FROM user_spirit_inventory usi
            JOIN spirit_souls ss ON usi.spirit_id = ss.id
            WHERE usi.spirit_id = ? AND usi.user_id = ? AND usi.quantity >= ?
        ");
        $stmt->execute([$spiritId, $userId, $quantity]);
        $userSpirit = $stmt->fetch();
        
        if (!$userSpirit) {
            throw new Exception('器灵数量不足');
        }
        
        // 计算合成成功率 (品质越高成功率越低)
        $successRates = [
            'common' => 80,
            'uncommon' => 65,
            'rare' => 50,
            'epic' => 35,
            'legendary' => 20
        ];
        
        $successRate = isset($successRates[$userSpirit['quality']]) ? $successRates[$userSpirit['quality']] : 50;
        $success = (rand(1, 100) <= $successRate);
        
        // 扣除合成材料
        $stmt = $pdo->prepare("UPDATE user_spirit_inventory SET quantity = quantity - ? WHERE spirit_id = ? AND user_id = ?");
        $stmt->execute([$quantity, $spiritId, $userId]);
        
        if ($success) {
            // 合成成功，获得高品质器灵
            $newQuality = upgradeQuality($userSpirit['quality']);
            
            // 查找同位置更高品质的器灵
            $stmt = $pdo->prepare("
                SELECT * FROM spirit_souls 
                WHERE position = ? AND quality = ? 
                ORDER BY RAND() LIMIT 1
            ");
            $stmt->execute([$userSpirit['position'], $newQuality]);
            $newSpirit = $stmt->fetch();
            
            if ($newSpirit) {
                // 添加新器灵到库存
                $stmt = $pdo->prepare("
                    INSERT INTO user_spirit_inventory (user_id, spirit_id, quantity) 
                    VALUES (?, ?, 1)
                    ON DUPLICATE KEY UPDATE quantity = quantity + 1
                ");
                $stmt->execute([$userId, $newSpirit['id']]);
                
                $message = "合成成功！获得 {$newSpirit['name']}";
            } else {
                $message = "合成成功，但没有找到合适的高品质器灵";
            }
        } else {
            $message = "合成失败，器灵已消耗";
        }
        
        // 清理数量为0的记录
        $stmt = $pdo->prepare("DELETE FROM user_spirit_inventory WHERE quantity <= 0");
        $stmt->execute();
        
        $pdo->commit();
        echo json_encode(['success' => $success, 'message' => $message]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

// 辅助函数：获取装备分类
function getEquipmentCategory($slotType) {
    $weaponTypes = ['weapon', 'sword', 'fan'];
    $armorTypes = ['armor', 'chest', 'helmet', 'pants', 'boots'];
    $accessoryTypes = ['accessory', 'ring', 'necklace', 'bracers', 'pendant', 'belt'];
    
    if (in_array($slotType, $weaponTypes)) {
        return 'weapon';
    } elseif (in_array($slotType, $armorTypes)) {
        return 'armor';
    } elseif (in_array($slotType, $accessoryTypes)) {
        return 'accessory';
    }
    
    return 'accessory'; // 默认
}

// 辅助函数：升级品质
function upgradeQuality($currentQuality) {
    $qualities = ['common', 'uncommon', 'rare', 'epic', 'legendary'];
    $currentIndex = array_search($currentQuality, $qualities);
    
    if ($currentIndex !== false && $currentIndex < count($qualities) - 1) {
        return $qualities[$currentIndex + 1];
    }
    
    return $currentQuality;
}
?> 