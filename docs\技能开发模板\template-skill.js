// ========================================
// 技能JavaScript模板 (v3.0)
// 使用说明：复制此文件，替换所有"Template"为实际技能英文名
// 重要：类名必须使用英文，不能使用中文
// ========================================

class TemplateSkill extends BaseSkill {
    async execute(skillData, weaponImage) {
        console.log('🎯 模板技能开始执行，isEnemySkill:', this.isEnemySkill);
        console.log('🎯 技能数据:', skillData);
        console.log('🎯 武器图片:', weaponImage);
        
        // 显示技能喊话
        await this.showSkillShout(skillData.skillName || '模板技能');
        console.log('🎯 技能喊话完成');
        
        // 执行技能动画
        await this.createTemplateAnimation(weaponImage);
        console.log('🎯 模板技能执行完成');
    }
    
    async createTemplateAnimation(weaponImage) {
        console.log('🎯 createTemplateAnimation 开始，武器图片:', weaponImage);
        
        // 获取施法者和目标位置
        const casterPos = this.getCharacterPosition(this.isEnemySkill);
        const targetPos = this.getCharacterPosition(!this.isEnemySkill);
        console.log('🎯 位置计算完成:', { casterPos, targetPos });
        
        // 创建动画容器
        const container = this.createElement('template-container', {
            style: {
                position: 'absolute',
                top: '0',
                left: '0',
                width: '100%',
                height: '100%',
                pointerEvents: 'none',
                zIndex: 1000
            }
        });
        
        this.effectsContainer.appendChild(container);
        console.log('🎯 动画容器创建完成:', container);
        
        try {
            // 第一阶段：蓄力准备
            console.log('🎯 开始第一阶段：蓄力准备');
            await this.createChargePhase(container, casterPos, weaponImage);
            console.log('🎯 第一阶段完成');
            
            // 第二阶段：攻击发射
            console.log('🎯 开始第二阶段：攻击发射');
            await this.createAttackPhase(container, casterPos, targetPos, weaponImage);
            console.log('🎯 第二阶段完成');
            
            // 第三阶段：击中效果
            console.log('🎯 开始第三阶段：击中效果');
            await this.createHitPhase(container, targetPos);
            console.log('🎯 第三阶段完成');
            
        } finally {
            // 清理动画容器
            setTimeout(() => {
                if (container && container.parentNode) {
                    container.parentNode.removeChild(container);
                }
                console.log('🎯 动画容器已清理');
            }, 100);
        }
    }
    
    // 🔥 第一阶段：蓄力准备动画
    async createChargePhase(container, casterPos, weaponImage) {
        console.log('🎯 createChargePhase 开始');
        
        // 创建蓄力核心
        const chargeCore = this.createElement('template-charge-core', {
            style: {
                position: 'absolute',
                left: casterPos.x + 'px',
                top: casterPos.y + 'px',
                transform: 'translate(-50%, -50%)'
            }
        });
        container.appendChild(chargeCore);
        console.log('🎯 蓄力核心已创建:', chargeCore);
        
        // 如果有武器，显示武器发光效果
        if (weaponImage) {
            const weaponSprite = this.createElement('template-weapon-sprite', {
                style: {
                    position: 'absolute',
                    left: casterPos.x + 'px',
                    top: casterPos.y + 'px',
                    transform: 'translate(-50%, -50%)'
                }
            });
            this.addWeaponImage(weaponSprite, weaponImage);
            container.appendChild(weaponSprite);
            console.log('🎯 武器发光效果已创建:', weaponSprite);
        }
        
        // 创建蓄力粒子效果
        console.log('🎯 开始创建蓄力粒子');
        for (let i = 0; i < 8; i++) {
            const angle = (i * 45) * Math.PI / 180;
            const radius = 40 + Math.random() * 20;
            const particleX = Math.cos(angle) * radius;
            const particleY = Math.sin(angle) * radius;
            
            const particle = this.createElement('template-charge-particle', {
                style: {
                    position: 'absolute',
                    left: casterPos.x + 'px',
                    top: casterPos.y + 'px',
                    transform: 'translate(-50%, -50%)'
                },
                cssVariables: {
                    '--particleX': particleX + 'px',
                    '--particleY': particleY + 'px'
                }
            });
            
            container.appendChild(particle);
        }
        console.log('🎯 8个蓄力粒子已创建');
        
        // 等待蓄力动画完成
        console.log('🎯 等待1000ms蓄力动画完成...');
        await this.wait(1000);
        
        // 清理第一阶段元素
        const phaseElements = container.querySelectorAll(
            '.template-charge-core, .template-weapon-sprite, .template-charge-particle'
        );
        console.log('🎯 清理第一阶段元素，数量:', phaseElements.length);
        phaseElements.forEach(element => {
            if (element && element.parentNode) {
                element.parentNode.removeChild(element);
            }
        });
        console.log('🎯 第一阶段元素清理完成');
    }
    
    // 🔥 第二阶段：攻击发射动画
    async createAttackPhase(container, casterPos, targetPos, weaponImage) {
        console.log('🎯 createAttackPhase 开始');
        
        // 计算角度和距离
        const distance = Math.sqrt(
            Math.pow(targetPos.x - casterPos.x, 2) + 
            Math.pow(targetPos.y - casterPos.y, 2)
        );
        const angle = Math.atan2(
            targetPos.y - casterPos.y, 
            targetPos.x - casterPos.x
        ) * 180 / Math.PI;
        
        console.log('🎯 计算结果:', { 
            distance, 
            angle, 
            casterPos: { x: casterPos.x, y: casterPos.y }, 
            targetPos: { x: targetPos.x, y: targetPos.y },
            deltaX: targetPos.x - casterPos.x,
            deltaY: targetPos.y - casterPos.y
        });
        
        // 创建主攻击弹道
        const projectile = this.createElement('template-projectile', {
            style: {
                position: 'absolute',
                left: casterPos.x + 'px',
                top: casterPos.y + 'px',
                transform: `translate(-50%, -50%) rotate(${angle}deg)`
            },
            cssVariables: {
                '--startX': casterPos.x + 'px',
                '--startY': casterPos.y + 'px',
                '--targetX': targetPos.x + 'px',
                '--targetY': targetPos.y + 'px',
                '--angle': angle + 'deg'
            }
        });
        
        // 如果有武器图片，添加到弹道上
        if (weaponImage) {
            this.addWeaponImage(projectile, weaponImage);
            console.log('🎯 为弹道添加武器图片:', weaponImage);
        }
        
        container.appendChild(projectile);
        console.log('🎯 主攻击弹道已创建:', projectile);
        
        // 创建拖尾效果
        const trail = this.createElement('template-trail', {
            style: {
                position: 'absolute',
                left: casterPos.x + 'px',
                top: casterPos.y + 'px',
                transform: `translate(-50%, -50%) rotate(${angle}deg)`
            },
            cssVariables: {
                '--startX': casterPos.x + 'px',
                '--startY': casterPos.y + 'px',
                '--targetX': (targetPos.x - casterPos.x) + 'px',
                '--targetY': (targetPos.y - casterPos.y) + 'px',
                '--angle': angle + 'deg'
            }
        });
        
        container.appendChild(trail);
        console.log('🎯 拖尾效果已创建:', trail);
        
        // 计算飞行时间（根据距离动态调整）
        const flightTime = Math.min(Math.max(distance / 400, 0.8), 1.5);
        console.log('🎯 等待飞行时间:', flightTime * 1000, 'ms');
        await this.wait(flightTime * 1000);
        
        // 清理第二阶段元素
        setTimeout(() => {
            const elements = [projectile, trail];
            elements.forEach(element => {
                if (element && element.parentNode) {
                    element.parentNode.removeChild(element);
                }
            });
            console.log('🎯 第二阶段元素已清理');
        }, 200);
        console.log('🎯 第二阶段清理定时器已设置');
    }
    
    // 🔥 第三阶段：击中效果动画
    async createHitPhase(container, targetPos) {
        console.log('🎯 createHitPhase 开始，目标位置:', targetPos);
        
        // 创建击中特效
        this.createHitEffect(targetPos.x, targetPos.y, !this.isEnemySkill);
        console.log('🎯 击中特效已创建');
        
        // 创建爆炸效果
        const explosion = this.createElement('template-explosion', {
            style: {
                position: 'absolute',
                left: targetPos.x + 'px',
                top: targetPos.y + 'px',
                transform: 'translate(-50%, -50%)'
            }
        });
        container.appendChild(explosion);
        console.log('🎯 爆炸效果已创建:', explosion);
        
        // 创建冲击波效果
        const shockwave = this.createElement('template-shockwave', {
            style: {
                position: 'absolute',
                left: targetPos.x + 'px',
                top: targetPos.y + 'px',
                transform: 'translate(-50%, -50%)'
            }
        });
        container.appendChild(shockwave);
        console.log('🎯 冲击波效果已创建:', shockwave);
        
        // 创建散射粒子
        console.log('🎯 开始创建12个散射粒子');
        for (let i = 0; i < 12; i++) {
            const angle = (i * 30) * Math.PI / 180;
            const scatterDistance = 30 + Math.random() * 40;
            
            const scatterParticle = this.createElement('template-scatter', {
                style: {
                    position: 'absolute',
                    left: targetPos.x + 'px',
                    top: targetPos.y + 'px',
                    transform: 'translate(-50%, -50%)'
                },
                cssVariables: {
                    '--scatterX': Math.cos(angle) * scatterDistance + 'px',
                    '--scatterY': Math.sin(angle) * scatterDistance + 'px'
                }
            });
            
            container.appendChild(scatterParticle);
        }
        console.log('🎯 12个散射粒子已创建');
        
        // 等待击中效果完成
        console.log('🎯 等待800ms击中效果完成...');
        await this.wait(800);
        
        // 清理第三阶段元素
        const phaseElements = container.querySelectorAll(
            '.template-explosion, .template-shockwave, .template-scatter'
        );
        console.log('🎯 清理第三阶段元素，数量:', phaseElements.length);
        phaseElements.forEach(element => {
            if (element && element.parentNode) {
                element.parentNode.removeChild(element);
            }
        });
        console.log('🎯 第三阶段元素清理完成');
    }
}

// 导出技能类
window.TemplateSkills = { 模板Skill };

// ========================================
// 使用说明：
// 1. 复制此文件为新技能名-skill.js
// 2. 替换所有"模板"/"Template"为实际技能名
// 3. 修改技能效果的颜色、大小、时机等
// 4. 在skill-loader.js中注册技能
// 5. 在script.js中添加映射
// 6. 创建对应的CSS动画文件
// ======================================== 