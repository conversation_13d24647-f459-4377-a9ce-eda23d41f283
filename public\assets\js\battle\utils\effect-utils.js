/**
 * 视觉效果工具类
 * 提供战斗特效和视觉效果相关的方法
 */
class BattleEffectUtils {
    
    /**
     * 创建飞剑击中特效
     * @param {number} x X坐标
     * @param {number} y Y坐标
     * @param {boolean} isPlayerAttack 是否为玩家攻击
     * @param {HTMLElement} effectsContainer 特效容器
     */
    static createSwordHitEffect(x, y, isPlayerAttack, effectsContainer) {
        if (!effectsContainer) {
            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.error('effect-utils', '特效容器不存在，无法创建击中特效');
            }
            return;
        }

        // 创建击中闪光
        const hitFlash = document.createElement('div');
        hitFlash.className = 'sword-hit-flash';
        hitFlash.style.left = `${x}px`;
        hitFlash.style.top = `${y}px`;
        effectsContainer.appendChild(hitFlash);

        // 创建击中粒子
        for (let i = 0; i < 12; i++) {
            const particle = document.createElement('div');
            particle.className = 'sword-hit-particle';
            particle.style.left = `${x}px`;
            particle.style.top = `${y}px`;
            
            const angle = (i / 12) * Math.PI * 2;
            const distance = 30 + Math.random() * 40;
            const particleX = Math.cos(angle) * distance;
            const particleY = Math.sin(angle) * distance;
            
            particle.style.setProperty('--particleX', `${particleX}px`);
            particle.style.setProperty('--particleY', `${particleY}px`);
            particle.style.animationDelay = `${Math.random() * 0.1}s`;
            
            effectsContainer.appendChild(particle);
            
            setTimeout(() => particle.remove(), 600);
        }

        // 创建冲击波
        const shockwave = document.createElement('div');
        shockwave.className = 'sword-hit-shockwave';
        shockwave.style.left = `${x}px`;
        shockwave.style.top = `${y}px`;
        effectsContainer.appendChild(shockwave);

        // 如果是玩家攻击，给敌人添加受击效果
        if (isPlayerAttack) {
            const enemySprite = document.querySelector('.enemy .character-sprite');
            if (enemySprite) {
                // 检查是否已经在播放万剑诀受击动画
                const currentAnimation = enemySprite.style.animation;
                if (currentAnimation.includes('sword-struck')) {
                    // 如果已经在受击，增强震动效果
                    enemySprite.style.animation = 'sword-struck 0.4s ease-out, sword-hit-shake 0.2s ease-out';
                } else {
                    // 开始万剑诀受击动画
                    enemySprite.style.animation = 'sword-struck 0.4s ease-out';
                    // 0.8秒后重置动画
                    setTimeout(() => {
                        if (enemySprite.style.animation.includes('sword-struck')) {
                            enemySprite.style.animation = '';
                        }
                    }, 400);
                }
            }
        } else {
            // 如果是敌人攻击，给玩家添加受击效果
            const playerSprite = document.querySelector('.player .character-sprite');
            if (playerSprite) {
                playerSprite.style.animation = 'sword-hit-shake 0.8s ease-out forwards';
                setTimeout(() => {
                    playerSprite.style.animation = '';
                }, 800);
            }
        }

        // 清理特效
        setTimeout(() => {
            if (hitFlash.parentNode) hitFlash.remove();
            if (shockwave.parentNode) shockwave.remove();
        }, 500);
    }

    /**
     * 创建伤害数字显示
     * @param {number} damage 伤害值
     * @param {number} x X坐标
     * @param {number} y Y坐标
     * @param {boolean} isCritical 是否暴击
     * @param {boolean} isMiss 是否未命中
     * @param {HTMLElement} effectsContainer 特效容器
     */
    static createDamageNumber(damage, x, y, isCritical = false, isMiss = false, effectsContainer) {
        if (!effectsContainer) {
            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.error('effect-utils', '特效容器不存在，无法创建伤害数字');
            }
            return;
        }

        const damageElement = document.createElement('div');
        damageElement.className = 'damage-number';
        
        if (isMiss) {
            damageElement.textContent = 'MISS';
            damageElement.classList.add('miss');
        } else {
            damageElement.textContent = damage;
            if (isCritical) {
                damageElement.classList.add('critical');
            }
        }
        
        damageElement.style.left = `${x}px`;
        damageElement.style.top = `${y}px`;
        
        effectsContainer.appendChild(damageElement);
        
        // 自动清理
        setTimeout(() => {
            if (damageElement.parentNode) {
                damageElement.remove();
            }
        }, isCritical ? 1800 : 1500);
    }

    /**
     * 创建治疗数字显示
     * @param {number} heal 治疗值
     * @param {number} x X坐标
     * @param {number} y Y坐标
     * @param {HTMLElement} effectsContainer 特效容器
     */
    static createHealNumber(heal, x, y, effectsContainer) {
        if (!effectsContainer) {
            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.error('effect-utils', '特效容器不存在，无法创建治疗数字');
            }
            return;
        }

        const healElement = document.createElement('div');
        healElement.className = 'heal-number';
        healElement.textContent = `+${heal}`;
        healElement.style.left = `${x}px`;
        healElement.style.top = `${y}px`;
        healElement.style.color = '#4CAF50';
        healElement.style.fontWeight = 'bold';
        
        effectsContainer.appendChild(healElement);
        
        // 自动清理
        setTimeout(() => {
            if (healElement.parentNode) {
                healElement.remove();
            }
        }, 1500);
    }

    /**
     * 创建状态效果图标
     * @param {string} statusType 状态类型
     * @param {number} duration 持续时间
     * @param {HTMLElement} targetElement 目标元素
     */
    static createStatusEffect(statusType, duration, targetElement) {
        if (!targetElement) {
            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.error('effect-utils', '目标元素不存在，无法创建状态效果');
            }
            return;
        }

        const statusIcon = document.createElement('div');
        statusIcon.className = `status-effect status-${statusType}`;
        statusIcon.dataset.statusType = statusType;
        
        // 根据状态类型设置图标
        const statusIcons = {
            'poison': '☠️',
            'burn': '🔥',
            'freeze': '❄️',
            'stun': '💫',
            'shield': '🛡️',
            'buff': '✨'
        };
        
        statusIcon.textContent = statusIcons[statusType] || '⚡';
        
        targetElement.appendChild(statusIcon);
        
        // 自动移除
        setTimeout(() => {
            if (statusIcon.parentNode) {
                statusIcon.remove();
            }
        }, duration);
    }

    /**
     * 创建粒子爆炸效果
     * @param {number} x X坐标
     * @param {number} y Y坐标
     * @param {string} color 粒子颜色
     * @param {number} particleCount 粒子数量
     * @param {HTMLElement} effectsContainer 特效容器
     */
    static createParticleExplosion(x, y, color = '#FFD700', particleCount = 20, effectsContainer) {
        if (!effectsContainer) {
            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.error('effect-utils', '特效容器不存在，无法创建粒子爆炸效果');
            }
            return;
        }

        for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement('div');
            particle.className = 'explosion-particle';
            particle.style.position = 'absolute';
            particle.style.left = `${x}px`;
            particle.style.top = `${y}px`;
            particle.style.width = '4px';
            particle.style.height = '4px';
            particle.style.backgroundColor = color;
            particle.style.borderRadius = '50%';
            particle.style.pointerEvents = 'none';
            
            const angle = (i / particleCount) * Math.PI * 2;
            const distance = 50 + Math.random() * 100;
            const endX = Math.cos(angle) * distance;
            const endY = Math.sin(angle) * distance;
            
            particle.style.setProperty('--endX', `${endX}px`);
            particle.style.setProperty('--endY', `${endY}px`);
            particle.style.animation = 'particle-explosion 1s ease-out forwards';
            
            effectsContainer.appendChild(particle);
            
            setTimeout(() => {
                if (particle.parentNode) {
                    particle.remove();
                }
            }, 1000);
        }
    }

    /**
     * 创建屏幕震动效果
     * @param {number} intensity 震动强度
     * @param {number} duration 持续时间
     * @param {HTMLElement} targetElement 目标元素
     */
    static createScreenShake(intensity = 5, duration = 300, targetElement) {
        if (!targetElement) {
            targetElement = document.querySelector('.battle-container') || document.body;
        }

        const originalTransform = targetElement.style.transform;
        
        const startTime = Date.now();
        const shake = () => {
            const elapsed = Date.now() - startTime;
            
            if (elapsed < duration) {
                const x = (Math.random() - 0.5) * intensity;
                const y = (Math.random() - 0.5) * intensity;
                targetElement.style.transform = `translate(${x}px, ${y}px)`;
                requestAnimationFrame(shake);
            } else {
                targetElement.style.transform = originalTransform;
            }
        };
        
        shake();
    }

    /**
     * 创建光晕效果
     * @param {HTMLElement} element 目标元素
     * @param {string} color 光晕颜色
     * @param {number} duration 持续时间
     */
    static createGlowEffect(element, color = '#FFD700', duration = 1000) {
        if (!element) {
            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.error('effect-utils', '目标元素不存在，无法创建光晕效果');
            }
            return;
        }

        const originalBoxShadow = element.style.boxShadow;
        element.style.boxShadow = `0 0 20px ${color}, 0 0 40px ${color}`;
        element.style.transition = 'box-shadow 0.3s ease';
        
        setTimeout(() => {
            element.style.boxShadow = originalBoxShadow;
        }, duration);
    }
}

// 添加CSS动画样式（如果不存在）
if (!document.querySelector('#battle-effect-styles')) {
    const styleElement = document.createElement('style');
    styleElement.id = 'battle-effect-styles';
    styleElement.textContent = `
        @keyframes particle-explosion {
            0% {
                transform: translate(0, 0);
                opacity: 1;
                scale: 1;
            }
            100% {
                transform: translate(var(--endX), var(--endY));
                opacity: 0;
                scale: 0.5;
            }
        }
        
        .status-effect {
            position: absolute;
            font-size: 16px;
            animation: status-float 2s ease-in-out infinite;
            pointer-events: none;
            z-index: 1000;
        }
        
        @keyframes status-float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        .heal-number {
            position: absolute;
            font-size: 20px;
            font-weight: bold;
            color: #4CAF50;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 1000;
            animation: heal-float 1.5s ease-out forwards;
            transform: translateX(-50%);
        }
        
        @keyframes heal-float {
            0% {
                opacity: 1;
                transform: translateX(-50%) translateY(0) scale(1);
            }
            30% {
                transform: translateX(-50%) translateY(-20px) scale(1.1);
            }
            100% {
                opacity: 0;
                transform: translateX(-50%) translateY(-60px) scale(0.8);
            }
        }
    `;
    document.head.appendChild(styleElement);
}

// 全局导出工具类
window.BattleEffectUtils = BattleEffectUtils;

if (window.BattleDebugConfig) {
    window.BattleDebugConfig.log('effect-utils', '✨ 视觉效果工具模块已加载');
} else {
    console.log('✨ 视觉效果工具模块已加载');
} 