<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>套装效果直接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .test-button:hover {
            transform: scale(1.05);
        }
        .test-button.primary {
            background: linear-gradient(135deg, #2196F3, #1976D2);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        .log-container {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background: #f8f9fa;
            font-family: monospace;
            font-size: 12px;
        }
        .shield-display {
            border: 2px solid #4da6ff;
            background: rgba(77, 166, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            text-align: center;
        }
        .shield-value {
            color: #4da6ff;
            font-weight: bold;
            text-shadow: 0 0 4px #4da6ff;
            font-size: 24px;
        }
    </style>
</head>
<body>
    <h1>🛡️ 套装效果直接测试</h1>
    
    <div class="test-section">
        <h2>📋 测试说明</h2>
        <div class="info status">
            <strong>本测试直接模拟套装效果处理逻辑，绕过战斗系统初始化问题</strong>
            <ul>
                <li>✅ 直接创建模拟的套装效果数据</li>
                <li>🔧 测试护盾效果的正则表达式匹配</li>
                <li>🛡️ 验证护盾值计算逻辑</li>
                <li>🎨 测试UI显示效果</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>🧪 直接测试工具</h2>
        
        <button class="test-button primary" onclick="testRegexMatching()">
            🔍 1. 测试正则表达式匹配
        </button>
        
        <button class="test-button primary" onclick="testShieldCalculation()">
            🛡️ 2. 测试护盾计算
        </button>
        
        <button class="test-button primary" onclick="testSetEffectProcessing()">
            ⚔️ 3. 测试套装效果处理
        </button>
        
        <button class="test-button" onclick="openBattlePageWithDebug()">
            🚀 4. 打开战斗页面(调试)
        </button>
        
        <div id="testResults"></div>
    </div>

    <div class="test-section">
        <h2>🛡️ 护盾效果演示</h2>
        <div class="shield-display">
            <div>玩家生命值: <span id="demo-hp">10687/10687</span></div>
            <div>护盾值: <span id="demo-shield" class="shield-value">🛡️0</span></div>
            <div style="margin-top: 15px;">
                <button class="test-button" onclick="simulateShieldEffect()">模拟护盾效果</button>
                <button class="test-button" onclick="resetShieldDemo()">重置</button>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>📝 实时日志</h2>
        <div id="logContainer" class="log-container">
            <div>等待测试开始...</div>
        </div>
    </div>

    <script>
        let logContainer = document.getElementById('logContainer');
        let testResults = document.getElementById('testResults');
        let demoHp = 10687;
        let demoMaxHp = 10687;
        let demoShield = 0;

        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span style="color: #666;">[${timestamp}]</span> ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function showResult(message, type = 'info') {
            const resultElement = document.createElement('div');
            resultElement.className = `status ${type}`;
            resultElement.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            testResults.appendChild(resultElement);
            testResults.scrollTop = testResults.scrollHeight;
        }

        function testRegexMatching() {
            addLog('🔍 开始测试正则表达式匹配...', 'info');
            showResult('🔍 测试步骤1：正则表达式匹配验证...', 'info');
            
            // 测试不同的护盾效果文本格式
            const testTexts = [
                "战斗开始时获得最大生命值20%的护盾",
                "战斗开始时获得20%的护盾",
                "获得最大生命值20%的护盾",
                "战斗开始时获得护盾",
                "受到攻击时有20%概率反弹30%伤害"
            ];
            
            testTexts.forEach((text, index) => {
                addLog(`📝 测试文本 ${index + 1}: "${text}"`);
                
                // 测试护盾条件匹配
                const hasStart = text.includes('战斗开始时获得');
                const hasShield = text.includes('护盾');
                const matchesCondition = hasStart && hasShield;
                
                addLog(`  - 包含"战斗开始时获得": ${hasStart}`);
                addLog(`  - 包含"护盾": ${hasShield}`);
                addLog(`  - 条件匹配: ${matchesCondition}`);
                
                if (matchesCondition) {
                    // 测试正则表达式匹配
                    const percentMatch = text.match(/(\d+)%的护盾/) || text.match(/最大生命值(\d+)%的护盾/);
                    addLog(`  - 正则匹配结果: ${percentMatch ? percentMatch[1] + '%' : '无匹配'}`);
                    
                    if (percentMatch) {
                        const shieldPercent = parseInt(percentMatch[1]);
                        showResult(`✅ 文本${index + 1}匹配成功: ${shieldPercent}%护盾`, 'success');
                    } else {
                        showResult(`❌ 文本${index + 1}正则匹配失败`, 'error');
                    }
                } else {
                    addLog(`  - 跳过：不是护盾效果`);
                }
                
                addLog(''); // 空行分隔
            });
        }

        function testShieldCalculation() {
            addLog('🛡️ 开始测试护盾计算...', 'info');
            showResult('🛡️ 测试步骤2：护盾值计算验证...', 'info');
            
            const testCases = [
                { maxHp: 10687, percent: 20, expected: Math.floor(10687 * 0.2) },
                { maxHp: 8500, percent: 20, expected: Math.floor(8500 * 0.2) },
                { maxHp: 5000, percent: 15, expected: Math.floor(5000 * 0.15) },
                { maxHp: 12000, percent: 25, expected: Math.floor(12000 * 0.25) }
            ];
            
            testCases.forEach((testCase, index) => {
                const shieldAmount = Math.floor(testCase.maxHp * testCase.percent / 100);
                const isCorrect = shieldAmount === testCase.expected;
                
                addLog(`📊 测试用例 ${index + 1}:`);
                addLog(`  - 最大生命值: ${testCase.maxHp}`);
                addLog(`  - 护盾百分比: ${testCase.percent}%`);
                addLog(`  - 计算结果: ${shieldAmount}`);
                addLog(`  - 预期结果: ${testCase.expected}`);
                addLog(`  - 计算正确: ${isCorrect ? '✅' : '❌'}`);
                
                if (isCorrect) {
                    showResult(`✅ 计算用例${index + 1}正确: ${testCase.maxHp} × ${testCase.percent}% = ${shieldAmount}`, 'success');
                } else {
                    showResult(`❌ 计算用例${index + 1}错误: 期望${testCase.expected}，实际${shieldAmount}`, 'error');
                }
            });
        }

        function testSetEffectProcessing() {
            addLog('⚔️ 开始测试套装效果处理...', 'info');
            showResult('⚔️ 测试步骤3：完整套装效果处理流程...', 'info');
            
            // 模拟套装效果数据
            const mockSetEffects = [
                {
                    set_name: "星衣道尊",
                    effect: "受到攻击时有20%概率反弹30%伤害"
                },
                {
                    set_name: "星衣道尊", 
                    effect: "战斗开始时获得最大生命值20%的护盾"
                }
            ];
            
            // 模拟玩家数据
            const mockPlayer = {
                max_hp: 10687,
                maxHp: 10687, // 兼容性别名
                shield: 0
            };
            
            addLog('📊 模拟数据:');
            addLog(`  - 套装效果数量: ${mockSetEffects.length}`);
            addLog(`  - 玩家最大生命值: ${mockPlayer.max_hp}`);
            
            // 处理每个套装效果
            mockSetEffects.forEach((setEffect, index) => {
                const effect = setEffect.effect;
                addLog(`🔍 处理套装效果 ${index + 1}: ${setEffect.set_name} - ${effect}`);
                
                // 护盾效果处理
                if (effect.includes('战斗开始时获得') && effect.includes('护盾')) {
                    addLog('✅ 护盾效果条件匹配，开始处理...');
                    
                    const percentMatch = effect.match(/(\d+)%的护盾/) || effect.match(/最大生命值(\d+)%的护盾/);
                    
                    if (percentMatch) {
                        const shieldPercent = parseInt(percentMatch[1]) / 100;
                        const shieldAmount = Math.floor(mockPlayer.max_hp * shieldPercent);
                        
                        addLog(`🛡️ 护盾计算: ${mockPlayer.max_hp} × ${shieldPercent} = ${shieldAmount}`);
                        
                        // 添加护盾
                        mockPlayer.shield = (mockPlayer.shield || 0) + shieldAmount;
                        
                        addLog(`🛡️ 护盾效果: +${shieldAmount} (总护盾: ${mockPlayer.shield})`);
                        showResult(`🛡️ ${setEffect.set_name}套装效果：获得${shieldAmount}点护盾！`, 'success');
                        
                        // 更新演示显示
                        demoShield = mockPlayer.shield;
                        updateShieldDemo();
                        
                    } else {
                        addLog('❌ 护盾效果正则表达式匹配失败');
                        showResult('❌ 护盾效果正则匹配失败', 'error');
                    }
                } else {
                    addLog('ℹ️ 非护盾效果，跳过处理');
                }
            });
            
            showResult(`✅ 套装效果处理完成，最终护盾值: ${mockPlayer.shield}`, 'success');
        }

        function simulateShieldEffect() {
            const shieldAmount = Math.floor(demoMaxHp * 0.2);
            demoShield = shieldAmount;
            updateShieldDemo();
            addLog(`🛡️ 模拟护盾效果: 获得${shieldAmount}点护盾`);
            showResult(`🛡️ 护盾效果激活: ${shieldAmount}点护盾`, 'success');
        }

        function resetShieldDemo() {
            demoShield = 0;
            updateShieldDemo();
            addLog('🔄 护盾演示重置');
        }

        function updateShieldDemo() {
            document.getElementById('demo-hp').textContent = `${demoHp}/${demoMaxHp}`;
            document.getElementById('demo-shield').textContent = demoShield > 0 ? `🛡️${demoShield}` : '🛡️0';
            document.getElementById('demo-shield').style.opacity = demoShield > 0 ? '1' : '0.3';
        }

        function openBattlePageWithDebug() {
            addLog('🚀 打开战斗页面进行实际验证...');
            showResult('🚀 请在战斗页面控制台中观察调试信息', 'info');
            
            // 在新窗口中打开战斗页面
            window.open('battle.html?map_code=kunlun&stage=1', '_blank');
        }

        // 页面加载时的初始化
        window.onload = function() {
            addLog('📋 套装效果直接测试页面已加载');
            showResult('📋 测试页面已准备就绪，请按顺序执行测试步骤', 'info');
            updateShieldDemo();
        };
    </script>
</body>
</html>
