/**
 * 一念修仙 - 战斗计算器
 * 处理命中、闪避、暴击、抗暴等战斗公式
 */

class BattleCombatCalculator {
    constructor() {
        // 战斗常数配置
        this.COMBAT_CONSTANTS = {
            // 命中率基础值（85%）
            BASE_HIT_RATE: 85,
            
            // 暴击率基础值（5点）
            BASE_CRITICAL_BONUS: 5,
            
            // 闪避率基础值（5%）
            BASE_DODGE_RATE: 5,
            
            // 抗暴率基础值（0%）
            BASE_CRITICAL_RESISTANCE: 0,
            
            // 暴击伤害倍率（默认1.5倍）
            CRITICAL_DAMAGE_MULTIPLIER: 1.5,
            
            // 命中率影响因子
            HIT_CALCULATION_FACTOR: 0.8,
            
            // 随机伤害浮动范围（±20%）
            DAMAGE_VARIANCE: 0.2,
            
            // 最小伤害保障
            MIN_DAMAGE: 1
        };
        
        console.log('🎯 战斗计算器初始化完成');
    }
    
    /**
     * 🎯 计算命中判定
     * @param {Object} attacker - 攻击者数据
     * @param {Object} defender - 防御者数据
     * @returns {Object} 命中结果 {isHit: boolean, hitRate: number, dodge_bonus: number}
     */
    calculateHitCheck(attacker, defender) {
        // 获取攻击者命中率（数值型，如85表示85点命中）
        const attackerHitRate = parseFloat(attacker.accuracy_bonus) || this.COMBAT_CONSTANTS.BASE_HIT_RATE;
        
        // 获取防御者闪避率（数值型，如15表示15点闪避）
        const defenderDodgeRate = parseFloat(defender.dodge_bonus) || this.COMBAT_CONSTANTS.BASE_DODGE_RATE;
        
        // 命中公式：命中率 - 闪避率 * 命中因子
        // 例：攻击者85命中 vs 防御者15闪避 = 85 - 15 * 0.8 = 85 - 12 = 73% 命中率
        const finalHitRate = attackerHitRate - (defenderDodgeRate * this.COMBAT_CONSTANTS.HIT_CALCULATION_FACTOR);
        
        // 命中率限制在5%-95%之间，避免绝对命中或绝对闪避
        const clampedHitRate = Math.max(5, Math.min(95, finalHitRate));
        
        // 随机数判定（1-100）
        const roll = Math.random() * 100;
        const isHit = roll <= clampedHitRate;
        
        console.log(`🎯 命中判定: 攻击者命中${attackerHitRate} vs 防御者闪避${defenderDodgeRate} = 最终命中率${clampedHitRate.toFixed(1)}% | 骰子${roll.toFixed(1)} | ${isHit ? '命中' : 'MISS'}`);
        
        return {
            isHit,
            hitRate: clampedHitRate,
            dodge_bonus: defenderDodgeRate,
            roll: roll,
            rawHitRate: finalHitRate
        };
    }
    
    /**
     * 💥 计算暴击判定
     * @param {Object} attacker - 攻击者数据
     * @param {Object} defender - 防御者数据
             * @returns {Object} 暴击结果 {isCritical: boolean, critical_bonus: number, resistanceRate: number} // 🔧 修复：统一使用critical_bonus
     */
    calculateCriticalCheck(attacker, defender) {
        // 获取攻击者暴击率（数值型，如25表示25点暴击）
        const attackerCriticalRate = parseFloat(attacker.critical_bonus) || this.COMBAT_CONSTANTS.BASE_CRITICAL_BONUS;
        
        // 获取防御者抗暴率（数值型，如10表示10点抗暴）
        const defenderCriticalResistance = parseFloat(defender.critical_resistance) || this.COMBAT_CONSTANTS.BASE_CRITICAL_RESISTANCE;
        
        // 暴击公式：暴击率 - 抗暴率，结果转换为百分比
        // 例：攻击者25暴击 vs 防御者10抗暴 = 25 - 10 = 15% 暴击率
        const finalCriticalRate = attackerCriticalRate - defenderCriticalResistance;
        
        // 暴击率限制在0%-50%之间，避免过高的暴击率
        const clampedCriticalRate = Math.max(0, Math.min(50, finalCriticalRate));
        
        // 随机数判定（1-100）
        const roll = Math.random() * 100;
        const isCritical = roll <= clampedCriticalRate;
        
        console.log(`💥 暴击判定: 攻击者暴击${attackerCriticalRate} vs 防御者抗暴${defenderCriticalResistance} = 最终暴击率${clampedCriticalRate.toFixed(1)}% | 骰子${roll.toFixed(1)} | ${isCritical ? '暴击！' : '普通'}`);
        
        return {
            isCritical,
            critical_bonus: clampedCriticalRate, // 🔧 修复：统一使用critical_bonus
            critical_resistance: defenderCriticalResistance,
            roll: roll,
            rawCriticalRate: finalCriticalRate
        };
    }
    
    /**
     * ⚔️ 计算最终伤害
     * @param {number} attackPower - 攻击力
     * @param {number} defense - 防御力
     * @param {boolean} isCritical - 是否暴击
     * @param {Object} options - 额外选项
     * @returns {Object} 伤害结果 {damage: number, isCritical: boolean, damageType: string}
     */
    calculateFinalDamage(attackPower, defense, isCritical = false, options = {}) {
        // 确保数值类型
        const attack = parseFloat(attackPower) || 0;
        const def = parseFloat(defense) || 0;
        
        // 基础伤害 = 攻击力 - 防御力（最小为1）
        let baseDamage = Math.max(this.COMBAT_CONSTANTS.MIN_DAMAGE, attack - def);
        
        // 暴击伤害计算
        if (isCritical) {
            // 🎯 统一：使用角色的暴击伤害属性，统一小数格式
            let criticalMultiplier = this.COMBAT_CONSTANTS.CRITICAL_DAMAGE_MULTIPLIER; // 默认1.5倍
            
            // 如果有传递角色暴击伤害属性，则使用它
            if (options.attacker && options.attacker.critical_damage) {
                // 🔧 修复：统一使用critical_damage字段，小数格式直接加1转为倍率
                criticalMultiplier = 1 + parseFloat(options.attacker.critical_damage);
            } else if (options.attacker && options.attacker.criticalDamage) {
                // 兼容性：如果是百分比格式，先转换
                const criticalDamageValue = parseFloat(options.attacker.criticalDamage);
                if (criticalDamageValue > 1) {
                    // 百分比格式：150 -> 1.5倍
                    criticalMultiplier = criticalDamageValue / 100;
                } else {
                    // 小数格式：0.5 -> 1.5倍
                    criticalMultiplier = 1 + criticalDamageValue;
                }
            } else if (options.criticalMultiplier) {
                criticalMultiplier = options.criticalMultiplier;
            }
            
            baseDamage = Math.floor(baseDamage * criticalMultiplier);
            console.log(`💥 暴击伤害: 基础伤害 × ${criticalMultiplier.toFixed(2)} = ${baseDamage}`);
        }
        
        // 🔧 修改：移除随机伤害浮动，使伤害更可预测
        const damage = Math.max(this.COMBAT_CONSTANTS.MIN_DAMAGE, baseDamage);
        
        console.log(`⚔️ 伤害计算: 攻击${attack} - 防御${def} = 基础${Math.max(1, attack - def)} ${isCritical ? `× 暴击${this.COMBAT_CONSTANTS.CRITICAL_DAMAGE_MULTIPLIER}` : ''} = 最终${damage}`);
        
        return {
            damage,
            isCritical,
            baseDamage: Math.max(this.COMBAT_CONSTANTS.MIN_DAMAGE, attack - def),
            damageType: isCritical ? 'critical' : 'normal'
        };
    }
    
    /**
     * 🎲 完整的战斗伤害计算流程
     * @param {Object} attacker - 攻击者数据
     * @param {Object} defender - 防御者数据  
     * @param {number} skillPower - 技能威力
     * @param {Object} options - 额外选项 {skillType: 'physical'|'magic', weaponType: 'sword'|'fan', etc.}
     * @returns {Object} 完整战斗结果
     */
    calculateBattleResult(attacker, defender, skillPower, options = {}) {
        const battleResult = {
            // 命中判定
            hitCheck: null,
            // 暴击判定  
            criticalCheck: null,
            // 伤害结果
            damageResult: null,
            // 最终结果
            finalDamage: 0,
            isMiss: false,
            isCritical: false,
            battleLog: []
        };
        
        // 第一步：命中判定
        battleResult.hitCheck = this.calculateHitCheck(attacker, defender);
        battleResult.battleLog.push(`命中判定: ${battleResult.hitCheck.hitRate.toFixed(1)}% (${battleResult.hitCheck.isHit ? '命中' : 'MISS'})`);
        
        // 如果未命中，直接返回
        if (!battleResult.hitCheck.isHit) {
            battleResult.isMiss = true;
            battleResult.finalDamage = 0;
            battleResult.battleLog.push('攻击未命中，造成0点伤害');
            return battleResult;
        }
        
        // 第二步：暴击判定
        battleResult.criticalCheck = this.calculateCriticalCheck(attacker, defender);
        battleResult.isCritical = battleResult.criticalCheck.isCritical;
        battleResult.battleLog.push(`暴击判定: ${battleResult.criticalCheck.critical_bonus.toFixed(1)}% (${battleResult.isCritical ? '暴击！' : '普通'})`); // 🔧 修复：统一使用critical_bonus
        
        // 🔧 修改：第三步：根据技能类型选择正确的攻击力和防御力
        const weaponType = options.weaponType || 'sword';
        const skillType = options.skillType || (weaponType === 'fan' ? 'magic' : 'physical');
        
        let attackPower, defense, attackType;
        
        if (skillType === 'magic' || weaponType === 'fan') {
                    // 法术技能：使用法术攻击力 vs 法术防御力（优先使用immortal_attack）
        attackPower = skillPower || attacker.immortal_attack || attacker.attack || 0;
        defense = defender.immortal_defense || defender.defense || 0;
            attackType = '法术';
        } else {
            // 物理技能：使用物理攻击力 vs 物理防御力
                    attackPower = skillPower || attacker.physical_attack || attacker.attack || 0;
        defense = defender.physical_defense || defender.defense || 0;
            attackType = '物理';
        }
        
        console.log(`🎯 技能类型判断: weaponType=${weaponType}, skillType=${skillType}, attackType=${attackType}`);
        console.log(`📊 攻防数值: ${attackType}攻击力=${attackPower}, ${attackType}防御力=${defense}`);
        
        // 🔥 新增：分解五行伤害信息
        const elementalInfo = this.getElementalDamageInfo(skillPower, attackPower, options);
        
        battleResult.damageResult = this.calculateFinalDamage(
            attackPower, 
            defense, 
            battleResult.isCritical,
            { ...options, attackType, attacker }
        );
        
        battleResult.finalDamage = battleResult.damageResult.damage;
        battleResult.attackType = attackType;
        battleResult.elementalInfo = elementalInfo; // 🔥 新增：五行伤害信息
        
        // 🔥 增强：包含五行伤害的战斗日志
        if (elementalInfo.hasElementalDamage) {
            battleResult.battleLog.push(`五行${elementalInfo.elementType}伤害: +${elementalInfo.elementalDamage}点`);
        }
        battleResult.battleLog.push(`最终伤害: ${battleResult.finalDamage}点 ${attackType}伤害 ${battleResult.isCritical ? '(暴击)' : ''}`);
        
        console.log('🎲 完整战斗结果:', battleResult);
        
        return battleResult;
    }
    
    /**
     * 🛡️ 获取格式化的战斗属性显示
     * @param {Object} characterData - 角色数据
     * @returns {Object} 格式化的属性数据
     */
    getFormattedBattleStats(characterData) {
        return {
            accuracy_bonus: Math.floor(parseFloat(characterData.accuracy_bonus) || this.COMBAT_CONSTANTS.BASE_HIT_RATE),
            dodge_bonus: Math.floor(parseFloat(characterData.dodge_bonus) || this.COMBAT_CONSTANTS.BASE_DODGE_RATE),
            critical_bonus: Math.floor(parseFloat(characterData.critical_bonus) || this.COMBAT_CONSTANTS.BASE_CRITICAL_BONUS), // 🔧 修复：统一使用critical_bonus
            critical_resistance: Math.floor(parseFloat(characterData.critical_resistance) || this.COMBAT_CONSTANTS.BASE_CRITICAL_RESISTANCE)
        };
    }
    
    /**
     * 📊 获取战斗统计信息
     * @param {Array} battleHistory - 战斗历史记录
     * @returns {Object} 统计数据
     */
    getBattleStatistics(battleHistory) {
        if (!battleHistory || battleHistory.length === 0) {
            return {
                totalAttacks: 0,
                hitCount: 0,
                missCount: 0,
                criticalCount: 0,
                hitRate: 0,
                criticalRate: 0,
                averageDamage: 0
            };
        }
        
        const stats = {
            totalAttacks: battleHistory.length,
            hitCount: 0,
            missCount: 0,
            criticalCount: 0,
            totalDamage: 0
        };
        
        battleHistory.forEach(battle => {
            if (battle.isMiss) {
                stats.missCount++;
            } else {
                stats.hitCount++;
                stats.totalDamage += battle.finalDamage;
                
                if (battle.isCritical) {
                    stats.criticalCount++;
                }
            }
        });
        
        stats.hitRate = stats.totalAttacks > 0 ? (stats.hitCount / stats.totalAttacks * 100) : 0;
        stats.criticalRate = stats.hitCount > 0 ? (stats.criticalCount / stats.hitCount * 100) : 0;
        stats.averageDamage = stats.hitCount > 0 ? (stats.totalDamage / stats.hitCount) : 0;
        
        return stats;
    }
    
    /**
     * 🌟 获取五行伤害信息
     * @param {number} skillPower - 技能威力（可能包含五行伤害）
     * @param {number} basePower - 基础攻击力（不含五行）
     * @param {Object} options - 选项参数
     * @returns {Object} 五行伤害信息
     */
    getElementalDamageInfo(skillPower, basePower, options = {}) {
        try {
            const skillData = options.skillData || {};
            const elementType = skillData.elementType || skillData.element_type || 'neutral';
            
            // 如果技能威力大于基础攻击力，说明有五行加成
            const elementalDamage = Math.max(0, skillPower - basePower);
            const hasElementalDamage = elementalDamage > 0 && elementType !== 'neutral';
            
            const elementNames = {
                'metal': '金',
                'wood': '木', 
                'water': '水',
                'fire': '火',
                'earth': '土'
            };
            
            return {
                hasElementalDamage,
                elementType: elementNames[elementType] || elementType,
                elementalDamage,
                skillPower,
                basePower
            };
            
        } catch (error) {
            console.error('获取五行伤害信息失败:', error);
            return {
                hasElementalDamage: false,
                elementType: '无',
                elementalDamage: 0
            };
        }
    }
} 