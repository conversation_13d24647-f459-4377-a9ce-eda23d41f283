# 🗂️ 一念修仙项目 - 完整目录结构

> **基于实际文件验证的准确目录结构** | 更新时间: 2024年12月19日

## 📊 项目规模概览

- **前端页面**: 17个HTML页面
- **后端API**: 40个PHP接口文件
- **样式文件**: 21个CSS文件
- **脚本文件**: 6个核心JS文件
- **总代码量**: 约1000KB

## 📁 项目根目录

```
yinian/
├── 📄 核心文件
│   ├── index.php                    # 项目入口文件（跳转到public/）
│   ├── README.md                    # 项目说明文档
│   ├── PROJECT_STATUS.md            # 项目状态总结
│   ├── ARCHITECTURE_ANALYSIS.md     # 架构技术分析
│   ├── PROJECT_OVERVIEW.md          # 项目概览
│   └── PROJECT_DIRECTORY_STRUCTURE.md # 本文档
├── 🎮 前端目录
│   └── public/                      # 前端页面和静态资源
├── 🔧 后端目录
│   └── src/                         # 后端PHP核心代码
├── 🗄️ 数据目录
│   ├── database/                    # 数据库脚本
│   ├── data/                        # 数据备份
│   └── sql/                         # SQL脚本
├── 🛠️ 支持目录
│   ├── backend/                     # 后端支持系统
│   ├── scripts/                     # 各种脚本工具
│   ├── tools/                       # 开发工具
│   ├── tests/                       # 测试文件
│   ├── logs/                        # 日志文件
│   ├── includes/                    # 公共包含文件
│   └── admin/                       # 管理工具
└── 📚 文档目录
    └── archive/                     # 历史文档归档
```

## 🎨 public/ - 前端页面和静态资源

### 📱 游戏页面文件 (17个)

```
public/
├── 🏠 核心入口页面
│   ├── index.html                   # 游戏主入口页面
│   ├── game.html                    # 主游戏界面
│   ├── login.html                   # 用户登录页面
│   ├── register.html                # 用户注册页面
│   └── character_creation.html      # 角色创建页面
├── 🎮 核心游戏功能
│   ├── cultivation.html             # 修炼系统界面 (117KB)
│   ├── attributes.html              # 角色属性界面 (82KB)
│   ├── equipment_integrated.html    # 装备管理界面

│   ├── battle.html                  # 战斗系统界面
│   └── adventure.html               # 历练冒险界面
├── 🧪 辅助功能系统
│   ├── alchemy.html                 # 炼丹系统界面
│   ├── spirit_system.html           # 器灵系统界面
│   ├── spirit_root.html             # 灵根系统界面

│   └── shop.html                    # 商店系统界面
├── ⚙️ 设置管理
│   └── settings.html                # 游戏设置页面
└── 📱 PWA配置
    └── manifest.json                # PWA应用配置
```

### 🎨 assets/ - 静态资源目录

#### 🎨 assets/css/ - 样式文件 (21个)

```
assets/css/
├── 🌐 全局样式
│   ├── global.css (1.7KB)           # 全局基础样式
│   └── common-navigation.css (5.7KB) # 统一导航样式
├── 🎮 页面样式 (按功能模块)
│   ├── attributes.css (61KB)        # 属性系统样式
│   ├── cultivation.css (52KB)       # 修炼系统样式
│   ├── equipment-integrated.css (52KB) # 装备系统样式
│   ├── game.css (27KB)              # 主界面样式
│   ├── alchemy.css (21KB)           # 炼丹系统样式
│   ├── spirit_system.css (18KB)     # 器灵系统样式

│   ├── shop.css (16KB)              # 商店系统样式
│   ├── battle.css (15KB)            # 战斗系统样式
│   ├── adventure.css (12KB)         # 冒险系统样式
│   ├── character_creation.css (12KB) # 角色创建样式
│   ├── spirit_root.css (6.0KB)      # 灵根系统样式

│   ├── login.css (4.8KB)            # 登录页面样式
│   ├── register.css (4.3KB)         # 注册页面样式
│   └── settings.css (3.1KB)         # 设置页面样式
├── 🧩 组件样式
│   ├── item-detail-popup.css (12KB) # 物品详情弹窗
│   └── character-equipment-component.css (8.5KB) # 装备组件
└── 📁 专门目录
    ├── battle/                      # 战斗相关样式
    │   └── battle-ui.css
    ├── components/                  # 组件样式
    └── mobile/                      # 移动端样式 (已清理)
```

#### 🛠️ assets/js/ - JavaScript文件 (6个核心)

```
assets/js/
├── 🔐 全局服务
│   └── auth-check.js (23KB)         # 全局认证检查服务
├── 🧩 通用组件
│   ├── common-navigation.js (8KB)   # 统一导航组件
│   ├── item-detail-popup.js (68KB)  # 物品详情弹窗组件
│   ├── component-loader.js (2.5KB)  # 组件加载器
│   └── realm-system.js (5KB)        # 境界系统组件
├── 🎮 核心业务逻辑
│   └── equipment-integrated.js (86KB) # 装备系统核心逻辑
└── 📁 专门目录
    ├── battle/                      # 战斗相关JS
    │   └── skills/                  # 技能动画模块
    └── loading/                     # 加载相关JS
```

#### 🖼️ assets/images/ - 图片资源

```
assets/images/
├── 🌄 界面背景
│   ├── game_bg.jpg                  # 主游戏背景
│   ├── battle-bg.jpg                # 战斗背景
│   └── first_login_*.jpg            # 首次登录背景
├── 👤 角色图片
│   ├── char/                        # 主要角色 (150+ 角色头像)
│   ├── char/vip/                    # VIP角色
│   └── char/other/                  # 其他角色
├── 🎒 装备物品图片 (200+ 装备图标)
├── 🔊 音效文件
│   └── sound/
└── 🧩 组件资源
    └── components/
        └── character-equipment.html  # 角色装备组件
```

## 🔧 src/ - 后端PHP核心代码

### 🌐 src/api/ - API接口层 (40个文件)

```
src/api/
├── 👤 用户系统 API (8个)
│   ├── login.php (6.9KB)            # 用户登录接口
│   ├── register.php (2.0KB)         # 用户注册接口
│   ├── logout.php (499B)            # 用户登出接口
│   ├── user_info.php (8.8KB)        # 获取用户信息
│   ├── create_character.php (31KB)  # 角色创建接口
│   ├── rename_character.php (16KB)  # 角色改名接口
│   ├── check_session.php (563B)     # 会话验证接口
│   └── auth_status.php (10KB)       # 认证状态检查
├── 🏔️ 修炼系统 API (6个)
│   ├── cultivation.php (100KB) 🌟   # 修炼系统核心接口
│   ├── spirit_root.php (5.2KB)      # 灵根系统
│   ├── spirit_root_simple.php (2.4KB) # 简化灵根接口
│   ├── five_elements_spiritual_root.php (20KB) # 五行灵根系统

├── ⚔️ 战斗系统 API (4个)
│   ├── battle_unified.php (51KB)    # 统一战斗系统
│   ├── battle_drops_unified.php (106KB) 🌟 # 战斗掉落系统
│   ├── battle_stage_info.php (9.3KB) # 战斗关卡信息
│   └── monster_ai_system.php (9.0KB) # 怪物AI系统
├── 🎒 装备系统 API (5个)
│   ├── equipment_integrated.php (157KB) 🌟 # 装备综合管理

│   ├── equipment_quality_system.php (13KB) # 装备品质系统
│   ├── fix_character_equipment.php (6.5KB) # 装备修复工具
│   └── fix_character_equipment_direct.php (7.8KB) # 直接装备修复
├── 🗺️ 冒险系统 API (3个)
│   ├── adventure_maps.php (18KB)    # 地图冒险系统

│   └── dungeons.php (12KB)          # 副本系统
├── 🧪 辅助系统 API (5个)
│   ├── alchemy_system.php (41KB)    # 炼丹系统
│   ├── spirit_system.php (14KB)     # 器灵系统
│   ├── redeem_code.php (12KB)       # 兑换码系统
│   ├── shop.php (16KB)              # 商城系统
│   └── shop_backup.php (37KB)       # 商城备份版本
├── 🔧 工具和调试 API (6个)
│   ├── db_structure_check.php (2.3KB) # 数据库结构检查
│   ├── debug_equipment.php (4.5KB)  # 装备调试工具
│   ├── debug_equipment_direct.php (6.8KB) # 直接装备调试
│   ├── cleanup_test_character.php (10KB) # 测试角色清理
│   ├── test_simple.php (2.4KB)      # 简单测试
│   └── csrf.php (133B)              # CSRF防护
├── 📊 其他功能 API (1个)
│   └── leaderboard.php (409B)       # 排行榜系统
├── 🛡️ 安全配置
│   └── .htaccess (440B)             # Apache访问控制
└── 👨‍💼 管理后台
    └── admin/                       # 管理相关API文件
```

### 📖 src/includes/ - 公共库文件

```
src/includes/
├── functions.php                    # 通用函数库
└── (其他公共库文件)
```

### ⚙️ src/config/ - 配置文件

```
src/config/
├── database.php                     # 数据库配置
└── (其他配置文件)
```

### 🛠️ src/utils/ - 工具类

```
src/utils/
└── (各种工具类文件)
```

## 🗄️ 数据相关目录

### 📋 database/ - 数据库脚本

```
database/
├── schema.sql                       # 数据库结构定义
├── data.sql                         # 基础数据导入
├── init.sql                         # 数据库初始化
├── check_table_structure.php       # 表结构检查
├── test_db_connection.php           # 数据库连接测试
└── (其他数据库工具脚本)
```

### 💾 data/ - 数据备份

```
data/
├── backups/                         # 数据备份文件
├── exports/                         # 数据导出文件
└── imports/                         # 数据导入文件
```

### 📜 sql/ - SQL脚本

```
sql/
├── updates/                         # 数据库更新脚本
├── migrations/                      # 数据迁移脚本
└── procedures/                      # 存储过程
```

## 🏢 backend/ - 后端支持系统

### 👨‍💼 backend/admin/ - 后台管理

```
backend/admin/
├── index.html                       # 管理后台首页
├── login.html                       # 管理员登录
├── api/                             # 管理API
└── sql/                             # SQL脚本
```

### 📜 backend/scripts/ - 后端脚本

```
backend/scripts/
├── init_database.php               # 数据库初始化
├── data_migration/                  # 数据迁移脚本
└── maintenance/                     # 维护脚本
```

## 🔧 支持目录

### 📜 scripts/ - 各种脚本工具

```
scripts/
├── deploy/                          # 部署脚本
├── backup/                          # 备份脚本
└── maintenance/                     # 维护脚本
```

### 🛠️ tools/ - 开发工具

```
tools/
├── generators/                      # 代码生成器
├── validators/                      # 验证工具
└── analyzers/                       # 分析工具
```

### 🧪 tests/ - 测试文件

```
tests/
├── unit/                            # 单元测试
├── integration/                     # 集成测试
└── api/                             # API测试
```

### 📝 logs/ - 日志文件

```
logs/
├── error/                           # 错误日志
├── access/                          # 访问日志
└── debug/                           # 调试日志
```

### 📦 includes/ - 公共包含文件

```
includes/
├── common/                          # 通用包含文件
├── config/                          # 配置包含文件
└── utilities/                       # 工具包含文件
```

### 👨‍💼 admin/ - 管理工具

```
admin/
├── tools/                           # 管理工具
├── reports/                         # 报告生成
└── maintenance/                     # 维护功能
```

## 📚 archive/ - 历史文档归档

```
archive/
├── docs/                            # 旧版文档
│   ├── system/
│   │   └── GAME_DEVELOPMENT_DOCS.md # 旧版游戏开发文档
│   └── database/
│       └── DATABASE_SCHEMA.md       # 旧版数据库文档
├── backups/                         # 文档备份
└── migrations/                      # 迁移记录
```

## 📊 目录规模统计

| 目录 | 文件数 | 总大小 | 主要内容 |
|------|--------|--------|----------|
| public/ | 18个 | ~500KB | 前端页面和静态资源 |
| src/api/ | 40个 | ~800KB | 后端API接口 |
| assets/css/ | 21个 | ~300KB | 样式文件 |
| assets/js/ | 6个 | ~200KB | JavaScript文件 |
| assets/images/ | 350+ | ~50MB | 图片资源 |

## 🎯 目录管理规则

### 📝 文件命名规范
- **API文件**: 使用下划线命名 (`battle_drops_unified.php`)
- **前端页面**: 使用下划线命名 (`character_creation.html`)
- **CSS文件**: 使用连字符命名 (`common-navigation.css`)
- **JavaScript文件**: 使用连字符命名 (`item-detail-popup.js`)

### 🗂️ 目录组织原则
- **功能模块化**: 按游戏功能模块组织文件
- **前后端分离**: 前端文件在public/，后端文件在src/
- **资源分类**: 静态资源按类型分目录存放
- **文档归档**: 历史文档统一归档到archive/

### 🔄 维护规则
- **定期清理**: 删除无用的临时文件和测试文件
- **文档更新**: 目录结构变化时及时更新本文档
- **版本控制**: 重要目录变更要做版本记录
- **权限管理**: 关键目录设置适当的访问权限

---

**文档维护**: 本文档基于实际文件验证，每次目录结构变更后需同步更新。  
**更新频率**: 建议每月检查一次目录结构的准确性。  
**联系维护**: 如发现文档与实际不符，请及时更新此文档。 