/**
 * 🔧 战斗系统调试配置
 * 统一管理所有控制台输出的开关
 * 
 * 🚨 生产环境配置说明：
 * - MASTER_DEBUG = false：完全屏蔽所有调试信息
 * - ENABLE_CONSOLE_SUPPRESSOR = true：启用智能控制台屏蔽器
 * - PRODUCTION_MODE = true：生产模式，用户完全无需手动操作
 */
class BattleDebugConfig {
    constructor() {
        // ===== 🚨 生产环境核心配置 =====
        
        // 主控制开关 - 现在由全局开关控制
        this.MASTER_DEBUG = window.GLOBAL_DEBUG_SETTINGS?.ENABLE_DEBUG || false;
        
        // 🔇 自动屏蔽器开关 - 由全局开关控制
        this.ENABLE_CONSOLE_SUPPRESSOR = !window.GLOBAL_DEBUG_SETTINGS?.ENABLE_DEBUG;
        
        // 🏭 生产模式 - 与全局开关相反
        this.PRODUCTION_MODE = !window.GLOBAL_DEBUG_SETTINGS?.ENABLE_DEBUG;
        
        // ===== 错误处理配置 =====
        
        // 错误日志开关（生产环境建议保持开启以便排查问题）
        this.ENABLE_ERROR_LOGS = true;
        
        // 警告日志开关（生产环境建议关闭）
        this.ENABLE_WARNING_LOGS = false;
        
        // 信息日志开关（生产环境建议关闭）
        this.ENABLE_INFO_LOGS = false;
        
        // ===== 🔧 开发者调试开关（生产环境无效） =====
        
        // 细分调试开关（仅当 MASTER_DEBUG = true 时生效）
        this.DEBUG_BATTLE_MANAGER = false;
        this.DEBUG_SKILL_SYSTEM = false;
        this.DEBUG_UI_MANAGER = false;
        this.DEBUG_FLOW_MANAGER = false;
        this.DEBUG_REWARD_MANAGER = false;
        this.DEBUG_VICTORY_PANEL = false;
        this.DEBUG_AUTO_BATTLE = false;
        this.DEBUG_COMBAT_CALCULATOR = false;
        this.DEBUG_WEAPON_UTILS = false;
        this.DEBUG_MEMORY_MANAGER = false;
        this.DEBUG_ERROR_HANDLER = false;
        this.DEBUG_EFFECT_UTILS = false;
        this.DEBUG_ITEM_UTILS = false;
        this.DEBUG_DATA_UTILS = false;
        this.DEBUG_MANAGER_REGISTRY = false;
        this.DEBUG_NORMAL_ATTACK = false;
        this.DEBUG_XUANFENGZHAN = false;
        this.DEBUG_YOULONG = false;
        this.DEBUG_WOOD_SKILLS = false;
        this.DEBUG_DEBUG_PANEL = false;
        
        // 性能调试开关
        this.DEBUG_PERFORMANCE = false;
        this.DEBUG_API_CALLS = false;
        this.DEBUG_IMAGE_LOADING = false;
    }
    
    /**
     * 检查是否启用调试
     * @param {string} category 调试类别
     * @returns {boolean}
     */
    isDebugEnabled(category) {
        // 🏭 生产模式下完全屏蔽所有调试信息
        if (this.PRODUCTION_MODE) return false;
        
        // 主开关关闭时屏蔽所有调试信息
        if (!this.MASTER_DEBUG) return false;
        
        const categoryMap = {
            'battle-manager': this.DEBUG_BATTLE_MANAGER,
            'skill-system': this.DEBUG_SKILL_SYSTEM,
            'ui-manager': this.DEBUG_UI_MANAGER,
            'flow-manager': this.DEBUG_FLOW_MANAGER,
            'reward-manager': this.DEBUG_REWARD_MANAGER,
            'victory-panel': this.DEBUG_VICTORY_PANEL,
            'auto-battle': this.DEBUG_AUTO_BATTLE,
            'combat-calculator': this.DEBUG_COMBAT_CALCULATOR,
            'weapon-utils': this.DEBUG_WEAPON_UTILS,
            'memory-manager': this.DEBUG_MEMORY_MANAGER,
            'error-handler': this.DEBUG_ERROR_HANDLER,
            'effect-utils': this.DEBUG_EFFECT_UTILS,
            'item-utils': this.DEBUG_ITEM_UTILS,
            'data-utils': this.DEBUG_DATA_UTILS,
            'manager-registry': this.DEBUG_MANAGER_REGISTRY,
            'normal-attack': this.DEBUG_NORMAL_ATTACK,
            'xuanfengzhan': this.DEBUG_XUANFENGZHAN,
            'youlong': this.DEBUG_YOULONG,
            'wood-skills': this.DEBUG_WOOD_SKILLS,
            'debug-panel': this.DEBUG_DEBUG_PANEL,
            'performance': this.DEBUG_PERFORMANCE,
            'api-calls': this.DEBUG_API_CALLS,
            'image-loading': this.DEBUG_IMAGE_LOADING
        };
        
        return categoryMap[category] || false;
    }
    
    /**
     * 🏭 生产模式检查 - 用于屏蔽器判断是否启用
     * @returns {boolean}
     */
    isProductionMode() {
        return this.PRODUCTION_MODE;
    }
    
    /**
     * 🔇 检查是否应该启用控制台屏蔽器
     * @returns {boolean}
     */
    shouldSuppressConsole() {
        return this.ENABLE_CONSOLE_SUPPRESSOR && this.PRODUCTION_MODE;
    }
    
    /**
     * 安全的console.log
     * @param {string} category 调试类别
     * @param {...any} args 参数
     */
    log(category, ...args) {
        if (this.isDebugEnabled(category)) {
            console.log(...args);
        }
    }
    
    /**
     * 安全的console.warn
     * @param {string} category 调试类别
     * @param {...any} args 参数
     */
    warn(category, ...args) {
        // 🏭 生产模式下只显示错误级别的警告
        if (this.PRODUCTION_MODE) {
            if (this.ENABLE_ERROR_LOGS && category === 'error') {
                console.warn(...args);
            }
            return;
        }
        
        if (this.isDebugEnabled(category) || this.ENABLE_WARNING_LOGS) {
            console.warn(...args);
        }
    }
    
    /**
     * 安全的console.error
     * @param {string} category 调试类别
     * @param {...any} args 参数
     */
    error(category, ...args) {
        // 🏭 生产模式下始终显示错误信息（确保安全）
        if (this.PRODUCTION_MODE) {
            if (this.ENABLE_ERROR_LOGS) {
                console.error(...args);
            }
            return;
        }
        
        if (this.isDebugEnabled(category) || this.ENABLE_ERROR_LOGS) {
            console.error(...args);
        }
    }
    
    /**
     * 安全的console.info
     * @param {string} category 调试类别
     * @param {...any} args 参数
     */
    info(category, ...args) {
        // 🏭 生产模式下屏蔽所有信息日志
        if (this.PRODUCTION_MODE) return;
        
        if (this.isDebugEnabled(category) || this.ENABLE_INFO_LOGS) {
            console.info(...args);
        }
    }
    
    /**
     * 开发者快速切换调试模式
     * @param {boolean} enabled 是否启用
     */
    setMasterDebug(enabled) {
        this.MASTER_DEBUG = enabled;
        console.log(`🔧 战斗系统调试模式: ${enabled ? '已启用' : '已禁用'}`);
    }
    
    /**
     * 批量设置调试类别
     * @param {Object} settings 设置对象
     */
    setBatchDebug(settings) {
        Object.keys(settings).forEach(key => {
            if (this.hasOwnProperty(key)) {
                this[key] = settings[key];
            }
        });
        console.log('🔧 批量调试设置已更新:', settings);
    }
}

// 创建全局实例
window.BattleDebugConfig = new BattleDebugConfig();

// 为开发者提供便捷方法
window.enableBattleDebug = () => window.BattleDebugConfig.setMasterDebug(true);
window.disableBattleDebug = () => window.BattleDebugConfig.setMasterDebug(false);

// 模块加载完成（仅在开发模式下显示）
if (window.BattleDebugConfig && window.BattleDebugConfig.isDebugEnabled('debug-panel')) {
    console.log('战斗系统调试配置已加载');
} 