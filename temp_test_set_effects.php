<?php
/**
 * 临时测试套装特殊效果数据传递
 */

try {
    $pdo = new PDO('mysql:host=127.0.0.1;port=3306;dbname=yn_game;charset=utf8mb4', 'root', 'mjlxz159');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 包含必要的函数
    require_once __DIR__ . '/src/includes/functions.php';
    
    echo "=== 测试套装特殊效果数据传递 ===\n\n";
    
    // 获取一个有4件套或以上的角色ID
    $stmt = $pdo->prepare("
        SELECT ce.character_id, c.character_name, COUNT(*) as pieces_count
        FROM character_equipment ce
        JOIN characters c ON ce.character_id = c.id
        JOIN game_items gi ON ce.item_id = gi.id
        WHERE gi.set_id IS NOT NULL
        GROUP BY ce.character_id, c.character_name, gi.set_id
        HAVING COUNT(*) >= 4
        LIMIT 1
    ");
    $stmt->execute();
    $character = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$character) {
        echo "没有找到装备套装的角色\n";
        exit;
    }
    
    $characterId = $character['character_id'];
    $characterName = $character['character_name'];
    
    echo "测试角色: {$characterName} (ID: {$characterId})\n\n";
    
    // 测试getCharacterSetBonus函数
    $setBonus = getCharacterSetBonus($pdo, $characterId);
    
    echo "套装属性加成:\n";
    foreach($setBonus as $key => $value) {
        if ($key === 'special_effects') {
            echo "  特殊效果:\n";
            if (is_array($value) && count($value) > 0) {
                foreach($value as $effect) {
                    echo "    - 套装: {$effect['set_name']}\n";
                    echo "      件数: {$effect['pieces']}\n";
                    echo "      效果: {$effect['effect']}\n";
                }
            } else {
                echo "    无特殊效果\n";
            }
        } else {
            echo "  {$key}: {$value}\n";
        }
    }
    
    echo "\n=== 模拟API返回数据 ===\n";
    
    // 模拟API返回的数据结构
    $apiData = [
        'success' => true,
        'set_special_effects' => $setBonus['special_effects'] ?? [],
        'attributes' => [
            'physical_attack' => 1000,
            'hp' => 5000
        ]
    ];
    
    echo "API返回的套装特殊效果数据:\n";
    echo json_encode($apiData['set_special_effects'], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    
    echo "\n\n=== JavaScript效果匹配测试 ===\n";
    
    // 测试JavaScript中的效果匹配逻辑
    foreach($setBonus['special_effects'] as $effect) {
        $effectText = $effect['effect'];
        echo "效果文本: {$effectText}\n";
        
        // 测试概率提取
        if (preg_match('/有(\d+)%概率/', $effectText, $matches)) {
            echo "  提取概率: {$matches[1]}%\n";
        }
        
        // 测试反弹伤害匹配
        if (strpos($effectText, '反弹') !== false && strpos($effectText, '伤害') !== false) {
            if (preg_match('/反弹(\d+)%伤害/', $effectText, $matches) || 
                preg_match('/反弹(\d+)%/', $effectText, $matches)) {
                echo "  反弹百分比: {$matches[1]}%\n";
            }
        }
        
        // 测试护盾效果匹配
        if (strpos($effectText, '战斗开始时获得') !== false && strpos($effectText, '护盾') !== false) {
            if (preg_match('/(\d+)%的护盾/', $effectText, $matches)) {
                echo "  护盾百分比: {$matches[1]}%\n";
            }
        }
        
        echo "\n";
    }

    echo "\n=== 查看所有套装特殊效果类型 ===\n";

    // 获取所有套装的特殊效果
    $stmt = $pdo->prepare("SELECT set_name, effects FROM game_item_sets WHERE status = 'active'");
    $stmt->execute();
    $sets = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $allEffects = [];
    foreach($sets as $set) {
        $effects = json_decode($set['effects'], true);
        if ($effects) {
            foreach(['four_piece', 'six_piece'] as $piece_type) {
                if (isset($effects[$piece_type]['special_effect'])) {
                    $allEffects[] = $effects[$piece_type]['special_effect'];
                }
            }
        }
    }

    $uniqueEffects = array_unique($allEffects);
    echo "所有套装特殊效果类型:\n";
    foreach($uniqueEffects as $effect) {
        echo "- {$effect}\n";
    }

} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
?>
