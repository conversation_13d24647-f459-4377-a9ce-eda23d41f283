/**
 * 🎛️ 全局调试开关控制中心
 * 
 * 您只需要修改这个文件中的开关，就能控制整个项目所有页面的控制台输出
 * 包括战斗系统、修炼系统、装备系统等所有模块
 * 
 * 使用方法：
 * - 设置 ENABLE_DEBUG = false 屏蔽所有调试信息
 * - 设置 ENABLE_DEBUG = true 显示所有调试信息
 * 
 * <AUTHOR> Assistant
 * @date 2024年12月
 */

// 🎯 全局调试总开关 - 您只需要修改这一个设置
window.GLOBAL_DEBUG_SETTINGS = {
    
    // ==========================================
    // 🎛️ 主控开关区域 (您只需要修改这里)
    // ==========================================
    
    /**
     * 🔧 全局调试开关
     * true  = 显示所有调试信息 (开发模式)
     * false = 屏蔽所有调试信息 (生产模式)
     */
    ENABLE_DEBUG: true,  // 👈 修改这里控制整个项目
    
    /**
     * 🎯 模块开关 (可选的细粒度控制)
     * 如果 ENABLE_DEBUG = false，这些设置会被忽略
     * 如果 ENABLE_DEBUG = true，可以分别控制各模块
     */
    MODULES: {
        BATTLE: true,      // 战斗系统
        CULTIVATION: true, // 修炼系统
        EQUIPMENT: true,   // 装备系统
        ALCHEMY: true,     // 炼丹系统
        SHOP: true,        // 商城系统
        AUTH: true,        // 登录系统
        API: true,         // API调用
        UI: true,          // 界面系统
        COMMON: true       // 通用功能
    },
    
    // ==========================================
    // 🔧 系统设置区域 (一般不需要修改)
    // ==========================================
    
    /**
     * 🛡️ 安全设置
     */
    ALWAYS_SHOW_ERRORS: true,  // 错误信息始终显示，确保安全
    
    /**
     * 🎯 过滤关键词 (自动屏蔽包含这些词的日志)
     */
    FILTER_KEYWORDS: [
        // 中文游戏关键词
        '战斗', '修炼', '装备', '炼丹', '技能', '境界', '灵根', '法力', '体力',
        '攻击', '防御', '暴击', '闪避', '命中', '伤害', '治疗', '回复', '武器',
        '护具', '丹药', '材料', '背包', '道具', '品质', '等级', '经验', '金币',
        '物品', '槽位', '耐久', '图片', '渲染', '过滤', '检查', '匹配', '已装备',
        '玩家', '敌人', '怪物', '角色', '生命值', '魔法值', '当前轮次', '游戏结束',
        '胜利', '失败', '掉落', '奖励', '地图', '关卡', '头像', '设置', '获取',
        '初始化', '管理器', '系统', '模式', '状态', '数据', '信息', '元素',
        '文件名', '路径', '转换', '处理', '计算', '分析', '结果', '配置',
        
        // 英文游戏关键词
        'battle', 'skill', 'weapon', 'equipment', 'cultivation', 'alchemy',
        'attack', 'defense', 'hp', 'mp', 'level', 'exp', 'damage', 'heal',
        'critical', 'dodge', 'accuracy', 'realm', 'spirit', 'immortal',
        'inventory', 'item', 'rarity', 'equipped', 'slot', 'durability',
        'player', 'enemy', 'monster', 'character', 'avatar', 'victory',
        'defeat', 'reward', 'drop', 'stage', 'map', 'progress', 'manager',
        'system', 'data', 'config', 'status', 'state', 'element', 'path',
        'filename', 'convert', 'process', 'calculate', 'analyze', 'result',
        
        // 技术调试关键词
        'API', 'ajax', 'fetch', 'request', 'response', 'data', 'json',
        'loading', 'cache', 'storage', 'init', 'update', 'render',
        'calculate', 'validate', 'process', 'success', 'fail',
        'image_url', 'icon_image', 'getItemImageUrl', 'renderEquipped',
        'container', 'position', 'rect', 'exists', 'create', 'destroy',
        'memory', 'register', 'initialize', 'complete', 'start', 'end',
        'http', '200', 'ok', '404', 'not found', 'get', 'post', 'manifest',
        'icon-144x144', 'resource size', 'typo', 'error while trying',
        'domrect', 'getboundingclientrect', 'flex', 'display', 'none', 'block',
        
        // PWA和系统相关
        'PWA', 'iOS', 'favicon', 'manifest', 'auth-check', 'pwa-fix',
        '修复脚本', '登录状态', '设备', '模式', '检查模块', '已加载',
        '已初始化', '已创建', '已完成', '已关联', '已恢复', '挂机',
        
        // 特殊标识符和调试标记
        '🔧', '🎯', '✅', '❌', '⚠️', '🔍', '🚀', '💎', '⭐', '🌟','✨', '💥',
        '📊', '🎮', '🔄', '🔥', '💰', '🏆', '⚡', '🛡️', '🗡️', '💊', '❄️', '🔐', '🌿', '📂',
        '🖼️', '🎒', '⚔️', '🧠', '🔗', '🎉', '📄', '📡', '🤖', '🏗️', '🎁', '🌩️',
        '💙', '🌊', '💚', '💛', '💜', '❤️', '💪', '🦶', '👁️', '🔮', '🔥', '🌍', 
        '🔘',  '🧹 ', '📖', '🐉', '🐍', '🐲', '🐯', '🐮', '🐷', '🐸', '🐵', '🐔', 
        '🐶', '🐱', '🐭', '🐹', '🐰', '🐻', '🐼', '🐨', 
        '喊话', '技能', '攻击', '防御', '暴击', '闪避', '命中', '伤害', '治疗',
        '回复', '武器', '护具', '丹药', '材料', '背包', '道具', '品质', '等级',
        '经验', '金币', '物品', '槽位', '耐久', '图片', '渲染', '过滤', '检查',
        '匹配', '已装备', '动画类型', '使用缓存数据', '基础属性类型转换后',
        '=== 最终属性计算结果 ===', '=== 完整玩家数据 ===', 'DOM元素状态',
        '开始获取地图信息', '设置地图名称为', '文件名转换', '路径处理',
        '提取的敌人文件名', '最终头像路径', '敌人原始数据', '响应状态',
        '响应头', '响应文本长度', '游戏覆盖层元素', '最终计算结果',
        '存在:', '类型:', '(类型:', '获得:', '金币:', '银币:', '值:', 
        '- action:', '- map_code:', '- gold_gained:', '- silver_gained:',
        'recycle_gold:', 'gold_gained:', 'silver_gained:', 'recycle_warning',
        'goldreward', 'rewardssection', 'recyclwarning', 'localStorag',
        'isAutoBattleMode:', '倒计时元素:', '下一层按钮:', '挂机按钮:', '退出按钮:',
        'char/', 'equi/', 'enemy/', 'assets/images/', '.png', 'huaishang', 'shanghou',
        'taiyifeng', '太乙峰', 'user_progress', '野生山猴', 'normal', 'feijian',
        'huichunjian', '类型转换后:', '计算结果:', 'gold_reward', 'silver_reward',
        'effectscontainer存在:', '玩家位置:', '敌人位置:', '容器位置:', '当前轮次:',
        '动画类型:', '使用的animationType:', '是否敌人:', '完整路径，直接使用:',
        '提取的敌人文件名:', '最终头像路径:', '设置头像:', '角色类型:', '敌人生命值:',
        '非挂机模式，等待用户操作', '恢复挂机按钮状态：开始挂机', '胜利面板中恢复挂机状态:',
        
        // 系统模块名
        'BattleSystem', 'CultivationManager', 'EquipmentManager',
        'AlchemySystem', 'ShopManager', 'AuthManager', 'UIManager',
        'battle-manager', 'skill-loader', 'image-path', 'memory-manager',
        'renderInventory', 'renderEquippedItems', 'selectItem',
        'MemoryManager', 'RewardManager', 'VictoryPanelManager',
        'ImagePathManager', 'DataManager', 'StateMachine', 'AutoBattleManager',
        'dataManager', 'uiManager', 'stateMachine', 'rewardManager',
        'victoryPanelManager', 'memoryManager', 'imagePathManager',
        'autoBattleManager', 'battleContainer', 'effectsContainer'
    ]
};

// ==========================================
// 🚀 自动执行系统 (不需要修改)
// ==========================================

(function initGlobalDebugSwitch() {
    
    // 备份原始console方法
    const originalConsole = {
        log: console.log,
        warn: console.warn,
        info: console.info,
        debug: console.debug,
        trace: console.trace
    };
    
    /**
     * 检查是否应该屏蔽日志
     */
    function shouldSuppressLog(args) {
        if (window.GLOBAL_DEBUG_SETTINGS.ENABLE_DEBUG) {
            return false; // 调试开启，不屏蔽
        }
        
        // 转换参数为字符串进行检查
        const message = args.join(' ').toString().toLowerCase();
        
        // 检查是否包含过滤关键词
        return window.GLOBAL_DEBUG_SETTINGS.FILTER_KEYWORDS.some(keyword => 
            message.includes(keyword.toLowerCase())
        );
    }
    
    /**
     * 创建过滤后的console方法
     */
    function createFilteredConsoleMethod(originalMethod, methodName) {
        return function(...args) {
            if (shouldSuppressLog(args)) {
                // 屏蔽包含关键词的日志
                return;
            }
            // 不包含关键词或调试开启，正常输出
            originalMethod.apply(console, args);
        };
    }
    
    // 替换console方法
    if (!window.GLOBAL_DEBUG_SETTINGS.ENABLE_DEBUG) {
        console.log = createFilteredConsoleMethod(originalConsole.log, 'log');
        console.warn = createFilteredConsoleMethod(originalConsole.warn, 'warn');
        console.info = createFilteredConsoleMethod(originalConsole.info, 'info');
        console.debug = createFilteredConsoleMethod(originalConsole.debug, 'debug');
        console.trace = createFilteredConsoleMethod(originalConsole.trace, 'trace');
        
        // 保持错误信息始终显示
        if (window.GLOBAL_DEBUG_SETTINGS.ALWAYS_SHOW_ERRORS) {
            // console.error 不被替换
        }
    }
    
    // 存储原始方法供恢复使用
    window.GLOBAL_DEBUG_SETTINGS._original = originalConsole;
    
    // 提供便捷的调试函数
    window.debugLog = function(...args) {
        if (window.GLOBAL_DEBUG_SETTINGS.ENABLE_DEBUG) {
            originalConsole.log(...args);
        }
    };
    
    window.debugWarn = function(...args) {
        if (window.GLOBAL_DEBUG_SETTINGS.ENABLE_DEBUG) {
            originalConsole.warn(...args);
        }
    };
    
    window.debugError = function(...args) {
        // 错误始终显示
        originalConsole.error(...args);
    };
    
    // 模块化调试函数
    window.debugModule = function(module, ...args) {
        if (window.GLOBAL_DEBUG_SETTINGS.ENABLE_DEBUG && 
            window.GLOBAL_DEBUG_SETTINGS.MODULES[module.toUpperCase()]) {
            originalConsole.log(`[${module.toUpperCase()}]`, ...args);
        }
    };
    
    // 🔧 兼容性支持：为战斗系统提供旧API的兼容方法
    window.enableBattleConsole = function() {
        console.log('🔧 启用战斗控制台 (通过全局调试开关)');
        // 临时启用调试模式
        window.GLOBAL_DEBUG_SETTINGS.ENABLE_DEBUG = true;
        console.log('✅ 战斗控制台已启用');
    };
    
    window.disableBattleConsole = function() {
        console.log('🔧 禁用战斗控制台 (通过全局调试开关)');
        // 临时禁用调试模式
        window.GLOBAL_DEBUG_SETTINGS.ENABLE_DEBUG = false;
        console.log('🔇 战斗控制台已禁用');
    };
    
    window.showConsoleStats = function() {
        console.log('📊 全局调试开关状态:');
        console.log('  🎛️ 调试模式:', window.GLOBAL_DEBUG_SETTINGS.ENABLE_DEBUG ? '开启' : '关闭');
        console.log('  🔍 过滤关键词数量:', window.GLOBAL_DEBUG_SETTINGS.FILTER_KEYWORDS.length);
        console.log('  📋 模块状态:', window.GLOBAL_DEBUG_SETTINGS.MODULES);
    };
    
    window.showRecentLogs = function(count = 10) {
        console.log('📝 全局调试开关使用说明:');
        console.log('  - 修改 ENABLE_DEBUG 控制调试输出');
        console.log('  - 设置为 true 显示所有调试信息');
        console.log('  - 设置为 false 屏蔽游戏相关调试信息');
    };
    
})();

// ==========================================
// 🎮 状态显示 (开发时可用)
// ==========================================

// 在开发模式下显示当前设置状态
if (window.GLOBAL_DEBUG_SETTINGS.ENABLE_DEBUG) {
    setTimeout(() => {
        console.log('🎛️ 全局调试开关状态:');
        console.log('  📊 调试模式:', window.GLOBAL_DEBUG_SETTINGS.ENABLE_DEBUG ? '开启' : '关闭');
        console.log('  🛡️ 错误显示:', window.GLOBAL_DEBUG_SETTINGS.ALWAYS_SHOW_ERRORS ? '开启' : '关闭');
        console.log('  🎯 过滤关键词数量:', window.GLOBAL_DEBUG_SETTINGS.FILTER_KEYWORDS.length);
        console.log('  📋 模块状态:', window.GLOBAL_DEBUG_SETTINGS.MODULES);
    }, 100);
}

/**
 * 🎯 使用说明:
 * 
 * 1. 生产环境使用:
 *    设置 ENABLE_DEBUG: false
 *    所有游戏相关调试信息将被自动屏蔽
 * 
 * 2. 开发环境使用:
 *    设置 ENABLE_DEBUG: true  
 *    所有调试信息正常显示
 * 
 * 3. 模块控制:
 *    可以在 MODULES 中单独控制各模块的调试输出
 * 
 * 4. 立即生效:
 *    修改设置后刷新页面即可生效，无需其他操作
 */ 