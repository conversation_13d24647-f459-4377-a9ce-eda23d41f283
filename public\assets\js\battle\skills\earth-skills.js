/**
 * 土系技能模块 - 岩石突刺
 * 适用于 item_skills 表中的 animation_model = 'yanshituci'
 */

// 土系技能 - 岩石突刺
class YanShiTuCiSkill extends BaseSkill {
    async execute(skillData, weaponImage) {
        // 使用数据库中的真实技能名称，而不是硬编码
        const skillName = skillData?.skillName || skillData?.displayName || '岩石突刺'; // 提供默认值作为后备
        await this.showSkillShout(skillName);
        
        // 执行岩石突刺动画，传递武器图片
        await this.createYanShiTuCiAnimation(weaponImage);
    }
    
    async createYanShiTuCiAnimation(weaponImage) {
        // 🔧 动态判断位置映射
        let casterPos, targetPos;
        
        if (this.isEnemySkill) {
            // 敌方技能：敌人是施法者，玩家是目标
            casterPos = this.getCharacterPosition(false); // 敌人位置
            targetPos = this.getCharacterPosition(true);  // 玩家位置
        } else {
            // 我方技能：玩家是施法者，敌人是目标
            casterPos = this.getCharacterPosition(true);  // 玩家位置
            targetPos = this.getCharacterPosition(false); // 敌人位置
        }
        
        // 创建动画容器
        const container = document.createElement('div');
        container.className = 'yanshituci-container';
        this.effectsContainer.appendChild(container);
        
        // 使用动态位置数据
        const startX = casterPos.x;
        const startY = casterPos.y;
        const endX = targetPos.x;
        const endY = targetPos.y;
        
        try {
            // === 第一阶段：蓄力 - 大地之力汇聚 ===
            await this.createChargePhase(container, startX, startY, weaponImage);
            
            // === 第二阶段：发射 - 地裂石出 ===
            await this.createEruptionPhase(container, startX, startY, endX, endY);
            
            // === 第三阶段：击中 - 碎石飞溅和震荡波 ===
            await this.createImpactPhase(container, endX, endY);
            
        } finally {
            // 清理容器
            setTimeout(() => {
                if (container && container.parentNode) {
                    container.parentNode.removeChild(container);
                }
            }, 100);
        }
    }
    
    // 蓄力阶段：大地之力汇聚
    async createChargePhase(container, startX, startY, weaponImage) {
        // 创建大地魔法阵
        const earthCircle = document.createElement('div');
        earthCircle.className = 'yanshituci-magic-circle';
        earthCircle.style.left = `${startX}px`;
        earthCircle.style.top = `${startY}px`;
        container.appendChild(earthCircle);
        
        // 创建内圈岩石符文
        const innerRunes = document.createElement('div');
        innerRunes.className = 'yanshituci-inner-runes';
        innerRunes.style.left = `${startX}px`;
        innerRunes.style.top = `${startY}px`;
        container.appendChild(innerRunes);
        
        // 创建外圈地脉符文
        const outerRunes = document.createElement('div');
        outerRunes.className = 'yanshituci-outer-runes';
        outerRunes.style.left = `${startX}px`;
        outerRunes.style.top = `${startY}px`;
        container.appendChild(outerRunes);
        
        // 创建武器图片在中心旋转
        if (weaponImage) {
            const weaponSprite = document.createElement('div');
            weaponSprite.className = 'yanshituci-weapon-sprite';
            weaponSprite.style.left = `${startX}px`;
            weaponSprite.style.top = `${startY}px`;
            
            // 添加武器图片背景
            this.addWeaponImage(weaponSprite, weaponImage);
            // 🗡️ 动态调整武器图片角度
            const weaponImg = weaponSprite.querySelector('.weapon-image');
            if (weaponImg) {
                weaponImg.style.transform = `rotate(${this.calculateInitialSwordRotation()}deg)`;
            }
            
            container.appendChild(weaponSprite);
        }
        
        // 创建蓄力能量核心
        const energyCore = document.createElement('div');
        energyCore.className = 'yanshituci-energy-core';
        energyCore.style.left = `${startX}px`;
        energyCore.style.top = `${startY}px`;
        container.appendChild(energyCore);
        
        // 创建土石聚集粒子（增加到35个）
        for (let i = 0; i < 35; i++) {
            const rockParticle = document.createElement('div');
            rockParticle.className = 'yanshituci-charge-rock';
            rockParticle.style.left = `${startX}px`;
            rockParticle.style.top = `${startY}px`;
            
            const angle = Math.random() * Math.PI * 2;
            const radius = 20 + Math.random() * 40;
            const moveX = Math.cos(angle) * radius;
            const moveY = Math.sin(angle) * radius;
            
            rockParticle.style.setProperty('--chargeX', `${moveX}px`);
            rockParticle.style.setProperty('--chargeY', `${moveY}px`);
            rockParticle.style.animationDelay = `${Math.random() * 1.0}s`;
            
            container.appendChild(rockParticle);
        }
        
        // 创建环绕的地脉震动
        for (let i = 0; i < 18; i++) {
            const angle = (i / 18) * Math.PI * 2;
            const radius = 70;
            const trembleX = startX + Math.cos(angle) * radius;
            const trembleY = startY + Math.sin(angle) * radius;
            
            const tremble = document.createElement('div');
            tremble.className = 'yanshituci-charge-tremble';
            tremble.style.left = `${trembleX}px`;
            tremble.style.top = `${trembleY}px`;
            tremble.style.animationDelay = `${i * 0.05}s`;
            container.appendChild(tremble);
        }
        
        // 创建大地能量波纹
        for (let i = 0; i < 6; i++) {
            const ripple = document.createElement('div');
            ripple.className = 'yanshituci-energy-ripple';
            ripple.style.left = `${startX}px`;
            ripple.style.top = `${startY}px`;
            ripple.style.animationDelay = `${i * 0.12}s`;
            container.appendChild(ripple);
        }
        
        // 创建地面裂纹预兆
        for (let i = 0; i < 12; i++) {
            const crack = document.createElement('div');
            crack.className = 'yanshituci-charge-crack';
            crack.style.left = `${startX + (Math.random() - 0.5) * 60}px`;
            crack.style.top = `${startY + (Math.random() - 0.5) * 60}px`;
            crack.style.animationDelay = `${Math.random() * 0.8}s`;
            container.appendChild(crack);
        }
        
        // 创建尘土飞扬效果
        for (let i = 0; i < 10; i++) {
            const dust = document.createElement('div');
            dust.className = 'yanshituci-dust';
            dust.style.left = `${startX + (Math.random() - 0.5) * 50}px`;
            dust.style.top = `${startY + (Math.random() - 0.5) * 50}px`;
            dust.style.animationDelay = `${Math.random() * 0.9}s`;
            container.appendChild(dust);
        }
        
        // 等待蓄力完成
        await this.wait(1100);
        
        // 移除蓄力效果
        earthCircle.remove();
        innerRunes.remove();
        outerRunes.remove();
        energyCore.remove();
        document.querySelectorAll('.yanshituci-charge-rock').forEach(r => r.remove());
        document.querySelectorAll('.yanshituci-charge-tremble').forEach(t => t.remove());
        document.querySelectorAll('.yanshituci-energy-ripple').forEach(r => r.remove());
        document.querySelectorAll('.yanshituci-charge-crack').forEach(c => c.remove());
        document.querySelectorAll('.yanshituci-dust').forEach(d => d.remove());
        document.querySelectorAll('.yanshituci-weapon-sprite').forEach(w => w.remove());
    }
    
    // 发射阶段：地裂石出
    async createEruptionPhase(container, startX, startY, endX, endY) {
        // 创建预发射地面震动
        const groundQuake = document.createElement('div');
        groundQuake.className = 'yanshituci-ground-quake';
        groundQuake.style.left = `${startX}px`;
        groundQuake.style.top = `${startY}px`;
        container.appendChild(groundQuake);
        
        await this.wait(250);
        
        // 计算路径上的尖刺数量和位置
        const spikeCount = 8;
        const spikes = [];
        
        // 创建从施法者到目标的路径上的多个尖刺，更错落有致
        for (let i = 0; i < spikeCount; i++) {
            const progress = i / (spikeCount - 1);
            // 增加随机偏移，让路径更自然
            const randomOffset = (Math.random() - 0.5) * 30; // ±15px随机偏移
            const spikeX = startX + (endX - startX) * progress + randomOffset;
            const spikeY = startY + (endY - startY) * progress + randomOffset;
            
            // 地面裂缝（增强版）- 增加随机延迟
            const crack = document.createElement('div');
            crack.className = 'yanshituci-major-crack';
            crack.style.left = `${spikeX}px`;
            crack.style.top = `${spikeY}px`;
            crack.style.animationDelay = `${i * 0.06 + Math.random() * 0.04}s`; // 0.06±0.04s
            container.appendChild(crack);
            
            // 地面震动波 - 也增加随机延迟
            const shockRipple = document.createElement('div');
            shockRipple.className = 'yanshituci-shock-ripple';
            shockRipple.style.left = `${spikeX}px`;
            shockRipple.style.top = `${spikeY}px`;
            shockRipple.style.animationDelay = `${i * 0.06 + Math.random() * 0.04}s`;
            container.appendChild(shockRipple);
            
            // 岩石尖刺（多层次）- 更随机的时间
            const baseDelay = i * 60 + Math.random() * 40 + 120; // 更随机的基础延迟
            setTimeout(() => {
                // 主尖刺 - 高度更随机
                const mainSpike = document.createElement('div');
                mainSpike.className = 'yanshituci-main-spike';
                mainSpike.style.left = `${spikeX}px`;
                mainSpike.style.top = `${spikeY}px`;
                mainSpike.style.setProperty('--spikeHeight', `${70 + Math.random() * 50}px`); // 更大的随机范围
                
                // 🔧 修复：动态调整尖刺方向
                if (this.isEnemySkill) {
                    // 敌方技能：尖刺向下指向玩家
                    mainSpike.style.transform = 'translate(-50%, 0%) scaleY(-1)';
                } else {
                    // 我方技能：尖刺向上指向敌人  
                    mainSpike.style.transform = 'translate(-50%, -100%)';
                }
                
                container.appendChild(mainSpike);
                spikes.push(mainSpike);
                
                // 副尖刺（围绕主尖刺）- 数量和位置更随机
                const subSpikeCount = 2 + Math.floor(Math.random() * 3); // 2-4个副尖刺
                for (let j = 0; j < subSpikeCount; j++) {
                    const subSpike = document.createElement('div');
                    subSpike.className = 'yanshituci-sub-spike';
                    const offsetAngle = (j * (360 / subSpikeCount) + Math.random() * 45) * Math.PI / 180; // 更随机的角度
                    const offsetRadius = 10 + Math.random() * 15; // 更随机的半径
                    subSpike.style.left = `${spikeX + Math.cos(offsetAngle) * offsetRadius}px`;
                    subSpike.style.top = `${spikeY + Math.sin(offsetAngle) * offsetRadius}px`;
                    subSpike.style.setProperty('--spikeHeight', `${30 + Math.random() * 30}px`);
                    subSpike.style.animationDelay = `${j * 0.03 + Math.random() * 0.06}s`; // 更随机的延迟
                    
                    // 🔧 修复：动态调整副尖刺方向
                    if (this.isEnemySkill) {
                        // 敌方技能：尖刺向下指向玩家
                        subSpike.style.transform = 'translate(-50%, 0%) scaleY(-1)';
                    } else {
                        // 我方技能：尖刺向上指向敌人  
                        subSpike.style.transform = 'translate(-50%, -100%)';
                    }
                    
                    container.appendChild(subSpike);
                    spikes.push(subSpike);
                }
                
                // 碎石飞溅 - 数量和方向更随机
                const debrisCount = 6 + Math.floor(Math.random() * 6); // 6-11个碎石
                for (let k = 0; k < debrisCount; k++) {
                    const debris = document.createElement('div');
                    debris.className = 'yanshituci-eruption-debris';
                    debris.style.left = `${spikeX}px`;
                    debris.style.top = `${spikeY}px`;
                    
                    const debrisAngle = Math.random() * 360;
                    const debrisDistance = 15 + Math.random() * 50; // 更大的散布范围
                    debris.style.setProperty('--debrisAngle', `${debrisAngle}deg`);
                    debris.style.setProperty('--debrisDistance', `${debrisDistance}px`);
                    debris.style.animationDelay = `${k * 0.015 + Math.random() * 0.03}s`; // 更随机的延迟
                    
                    container.appendChild(debris);
                }
            }, baseDelay);
        }
        
        await this.wait(1000);
        
        // 移除发射效果
        groundQuake.remove();
        document.querySelectorAll('.yanshituci-major-crack').forEach(c => c.remove());
        document.querySelectorAll('.yanshituci-shock-ripple').forEach(r => r.remove());
        document.querySelectorAll('.yanshituci-eruption-debris').forEach(d => d.remove());
        
        // 尖刺逐渐沉入地下
        setTimeout(() => {
            spikes.forEach((spike, index) => {
                spike.style.animation = 'yanshituci-spike-sink 0.8s ease-in forwards';
                spike.style.animationDelay = `${index * 0.05}s`;
            });
        }, 500);
    }
    
    // 击中阶段：碎石飞溅和震荡波
    async createImpactPhase(container, endX, endY) {
        // 第一阶段：瞬间地震冲击
        const impactQuake = document.createElement('div');
        impactQuake.className = 'yanshituci-impact-quake';
        impactQuake.style.left = `${endX}px`;
        impactQuake.style.top = `${endY}px`;
        container.appendChild(impactQuake);
        
        // 第二阶段：巨石爆发核心
        const rockCore = document.createElement('div');
        rockCore.className = 'yanshituci-rock-core';
        rockCore.style.left = `${endX}px`;
        rockCore.style.top = `${endY}px`;
        container.appendChild(rockCore);
        
        // 第三阶段：多层震荡波
        setTimeout(() => {
            for (let i = 0; i < 5; i++) {
                const shockwave = document.createElement('div');
                shockwave.className = 'yanshituci-impact-shockwave';
                shockwave.style.left = `${endX}px`;
                shockwave.style.top = `${endY}px`;
                shockwave.style.animationDelay = `${i * 0.1}s`;
                container.appendChild(shockwave);
            }
        }, 100);
        
        // 第四阶段：放射状碎石爆发
        setTimeout(() => {
            for (let i = 0; i < 40; i++) {
                const debris = document.createElement('div');
                debris.className = 'yanshituci-impact-debris';
                debris.style.left = `${endX}px`;
                debris.style.top = `${endY}px`;
                
                const angle = (i / 40) * Math.PI * 2;
                const distance = 25 + Math.random() * 70;
                const height = 15 + Math.random() * 40;
                
                debris.style.setProperty('--debrisAngle', `${angle * 180 / Math.PI}deg`);
                debris.style.setProperty('--debrisDistance', `${distance}px`);
                debris.style.setProperty('--debrisHeight', `${height}px`);
                debris.style.animationDelay = `${i * 0.015}s`;
                
                container.appendChild(debris);
            }
        }, 200);
        
        // 第五阶段：地面裂纹扩散
        setTimeout(() => {
            for (let i = 0; i < 16; i++) {
                const crack = document.createElement('div');
                crack.className = 'yanshituci-impact-crack';
                crack.style.left = `${endX}px`;
                crack.style.top = `${endY}px`;
                
                const angle = (i / 16) * Math.PI * 2;
                const length = 40 + Math.random() * 30;
                
                crack.style.setProperty('--crackAngle', `${angle * 180 / Math.PI}deg`);
                crack.style.setProperty('--crackLength', `${length}px`);
                crack.style.animationDelay = `${i * 0.03}s`;
                
                container.appendChild(crack);
            }
        }, 300);
        
        // 第六阶段：尘土云雾
        setTimeout(() => {
            for (let i = 0; i < 15; i++) {
                const dustCloud = document.createElement('div');
                dustCloud.className = 'yanshituci-impact-dust';
                dustCloud.style.left = `${endX + (Math.random() - 0.5) * 100}px`;
                dustCloud.style.top = `${endY + (Math.random() - 0.5) * 100}px`;
                dustCloud.style.animationDelay = `${i * 0.08}s`;
                container.appendChild(dustCloud);
            }
        }, 400);
        
        // 第七阶段：余震效果
        setTimeout(() => {
            for (let i = 0; i < 8; i++) {
                const aftershock = document.createElement('div');
                aftershock.className = 'yanshituci-aftershock';
                aftershock.style.left = `${endX + (Math.random() - 0.5) * 80}px`;
                aftershock.style.top = `${endY + (Math.random() - 0.5) * 80}px`;
                aftershock.style.animationDelay = `${i * 0.15}s`;
                container.appendChild(aftershock);
            }
        }, 600);
        
        // 击中特效
        this.createHitEffect(endX, endY, true);
        
        // 🔧 修复：给被攻击者添加地震冲击效果
        const targetSelector = !this.isEnemySkill ? '.enemy .character-sprite' : '.player .character-sprite';
        const targetSprite = document.querySelector(targetSelector);
        if (targetSprite) {
            targetSprite.style.animation = 'earth-hit 1.8s ease-out, earth-shake 0.2s ease-in-out 10';
        }
        
        await this.wait(1500);
        
        // 🔧 修复：清理击中效果
        if (targetSprite) {
            targetSprite.style.animation = '';
        }
    }
    // 🗡️ 动态计算剑的初始旋转角度
    calculateInitialSwordRotation() {
        // 敌方技能：剑尖向下（指向玩家），我方技能：剑尖向上（指向敌人）
        return this.isEnemySkill ? 0 : 180;
    }
}

// 导出技能类
window.EarthSkills = { YanShiTuCiSkill }; 