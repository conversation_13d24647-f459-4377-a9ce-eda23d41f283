{"Game API Call": {"prefix": "api-call", "body": ["fetch(window.GameConfig ? window.GameConfig.getApiUrl('${1:endpoint}') : '../src/api/${1:endpoint}', {", "    method: '${2|GET,POST|}',", "    headers: {", "        'Content-Type': 'application/json'", "    },", "    body: ${3:JSON.stringify(data)}", "})", ".then(response => response.json())", ".then(data => {", "    if (data.success) {", "        ${4:// 成功处理}", "    } else {", "        console.error('API错误:', data.message);", "        ${5:// 错误处理}", "    }", "})", ".catch(error => {", "    console.error('请求失败:', error);", "});"], "description": "游戏API调用模板"}, "Game Config Check": {"prefix": "config-check", "body": ["if (!window.GameConfig) {", "    console.error('GameConfig未加载');", "    return;", "}"], "description": "游戏配置检查"}, "DOM Ready": {"prefix": "dom-ready", "body": ["document.addEventListener('DOMContentLoaded', function() {", "    ${1:// 页面加载完成后执行}", "});"], "description": "DOM加载完成事件"}, "Show Message": {"prefix": "show-msg", "body": ["function showMessage(message, type = 'info') {", "    const messageDiv = document.createElement('div');", "    messageDiv.className = `message message-\\${type}`;", "    messageDiv.textContent = message;", "    document.body.appendChild(messageDiv);", "    ", "    setTimeout(() => {", "        messageDiv.remove();", "    }, 3000);", "}"], "description": "显示消息提示"}, "Update UI Element": {"prefix": "update-ui", "body": ["const element = document.getElementById('${1:elementId}');", "if (element) {", "    element.${2|textContent,innerHTML,value|} = ${3:value};", "}"], "description": "更新UI元素"}, "Game Data Update": {"prefix": "update-data", "body": ["// 更新${1:数据类型}", "function update${1/(.*)/${1:/capitalize}/}(data) {", "    if (!data) return;", "    ", "    ${2:// 更新逻辑}", "}"], "description": "游戏数据更新函数"}}