{"aiAssistant": {"name": "一念修仙开发助手", "version": "1.0.0", "description": "专为一念修仙Web游戏项目定制的AI开发助手", "capabilities": {"codeGeneration": true, "debugging": true, "testing": true, "documentation": true, "refactoring": true, "security": true, "performance": true}, "projectContext": {"gameType": "修仙RPG", "techStack": ["PHP 7.4+", "MySQL", "HTML5", "CSS3", "JavaScript ES6+"], "framework": "原生PHP + 自定义框架", "database": "yn_game", "environment": "PHPStudy Pro + Windows"}, "codePatterns": {"apiResponse": {"template": "header('Content-Type: application/json'); echo json_encode(['success' => $success, 'message' => $message, 'data' => $data]);", "description": "标准API响应格式"}, "dbConnection": {"template": "require_once __DIR__ . '/../config/database.php'; $pdo = getDatabase();", "description": "标准数据库连接"}, "sessionCheck": {"template": "session_start(); if (!isset($_SESSION['user_id'])) { echo json_encode(['success' => false, 'message' => '请先登录']); exit; }", "description": "用户登录检查"}, "gameApiCall": {"template": "fetch(window.GameConfig ? window.GameConfig.getApiUrl('endpoint') : '../src/api/endpoint')", "description": "游戏API调用"}}, "commonTasks": {"createApi": {"steps": ["创建PHP API文件", "添加数据库连接", "实现业务逻辑", "添加错误处理", "创建测试用例"]}, "addGameFeature": {"steps": ["设计数据库表结构", "创建后端API接口", "实现前端界面", "添加游戏逻辑", "测试功能完整性"]}, "debugIssue": {"steps": ["检查错误日志", "验证数据库连接", "测试API响应", "检查前端调用", "分析数据流"]}}, "securityChecks": {"sqlInjection": "使用参数化查询", "xss": "过滤用户输入", "csrf": "验证CSRF令牌", "authentication": "检查用户登录状态", "authorization": "验证用户权限"}, "performanceOptimization": {"database": ["使用索引优化查询", "避免N+1查询问题", "使用连接池", "缓存频繁查询结果"], "frontend": ["压缩静态资源", "使用CDN", "延迟加载图片", "缓存API响应"], "backend": ["使用OPcache", "优化PHP配置", "减少文件I/O", "使用内存缓存"]}, "testingStrategy": {"unit": "测试单个函数和方法", "integration": "测试API接口完整性", "functional": "测试游戏功能流程", "security": "测试安全漏洞", "performance": "测试性能瓶颈"}, "documentationTemplates": {"api": {"format": "markdown", "sections": ["描述", "参数", "返回值", "示例", "错误码"]}, "function": {"format": "phpDoc", "sections": ["描述", "参数", "返回值", "异常", "示例"]}, "feature": {"format": "markdown", "sections": ["功能描述", "使用方法", "技术实现", "注意事项"]}}, "shortcuts": {"generateApi": "Ctrl+Shift+A", "runTests": "Ctrl+Shift+T", "debugMode": "Ctrl+Shift+D", "securityScan": "Ctrl+Shift+S", "performanceCheck": "Ctrl+Shift+P"}}}