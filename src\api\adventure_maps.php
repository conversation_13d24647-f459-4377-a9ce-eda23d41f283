<?php
/**
 * 一念修仙 - 历练地图API
 * 提供地图数据、关卡信息和用户进度
 */

// 禁用错误显示，确保JSON响应纯净
ini_set('display_errors', 0);
error_reporting(E_ALL);

// 允许跨域请求
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, POST");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");
    
// 引入全局配置和函数库
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/auth.php';

// 检查维护模式
if (isMaintenanceMode()) {
    echo json_encode([
        'success' => false,
        'message' => '游戏正在维护中，请稍后再试',
        'maintenance' => true
    ]);
    exit;
}

// 记录API调用（如果开启了调试）
if (DEBUG_LOG_API_CALLS) {
    writeLog("API调用: adventure_maps.php", 'DEBUG', 'api.log');
}

// 创建数据库连接
$pdo = getDatabase();
if (!$pdo) {
    echo json_encode(['success' => false, 'message' => '数据库连接失败']);
    exit;
}

// 检查用户登录状态
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => '请先登录']);
    exit;
}

// 获取当前用户和角色信息
$userId = $_SESSION['user_id'];
$characterId = get_character_id();

if (!$characterId) {
    echo json_encode(['success' => false, 'message' => '角色信息不存在']);
    exit;
}

// 获取请求动作
$action = isset($_GET['action']) ? $_GET['action'] : 'get_maps';

// 根据请求动作处理不同的API请求
switch ($action) {
    case 'get_maps':
        // 获取所有地图数据
        getMaps($pdo, $characterId, isset($_GET['map_type']) ? $_GET['map_type'] : 'all');
        break;

    case 'get_map_detail':
        // 获取特定地图的详细信息和关卡
        $mapId = isset($_GET['map_id']) ? intval($_GET['map_id']) : 0;
        getMapDetail($pdo, $characterId, $mapId);
        break;

    case 'get_user_progress':
        // 获取用户在特定地图的进度
        $mapCode = isset($_GET['map_code']) ? $_GET['map_code'] : '';
        getUserProgress($pdo, $characterId, $mapCode);
        break;

    case 'get_stage_info':
        // 获取特定关卡的信息
        $mapId = isset($_GET['map_id']) ? intval($_GET['map_id']) : 0;
        $stageNumber = isset($_GET['stage_number']) ? intval($_GET['stage_number']) : 0;
        getStageInfo($pdo, $characterId, $mapId, $stageNumber);
        break;

    case 'update_user_progress':
        // 更新用户地图进度
        $mapId = isset($_GET['map_id']) ? intval($_GET['map_id']) : 0;
        $currentStage = isset($_GET['current_stage']) ? intval($_GET['current_stage']) : 1;
        updateUserProgress($pdo, $characterId, $mapId, $currentStage);
            break;
            
        default:
        echo json_encode(['success' => false, 'message' => '无效的请求']);
}

/**
 * 获取所有地图数据
 */
function getMaps($pdo, $characterId, $mapType = 'all') {
    // 准备SQL语句，根据mapType过滤
    if ($mapType == 'all') {
        $sql = "SELECT gm.* FROM game_maps gm WHERE gm.is_active = 1 ORDER BY gm.sort_order ASC, gm.realm_requirement ASC";
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
    } else {
        $sql = "SELECT gm.* FROM game_maps gm WHERE gm.is_active = 1 AND gm.map_type = ? ORDER BY gm.sort_order ASC, gm.realm_requirement ASC";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$mapType]);
    }

    $maps = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 分类存储不同类型的地图
    $normalMaps = [];
    $dungeonMaps = [];
    $specialMaps = [];
    $pvpMaps = [];

    foreach ($maps as $row) {
        // 获取用户在此地图的进度
        $progressSql = "SELECT * FROM user_map_progress WHERE character_id = ? AND map_id = ?";
        $progressStmt = $pdo->prepare($progressSql);
        $progressStmt->execute([$characterId, $row['id']]);
        $progress = $progressStmt->fetch(PDO::FETCH_ASSOC);

        // 检查地图是否已解锁
        $isUnlocked = checkMapUnlockStatus($pdo, $characterId, $row);
        
        // 组装地图数据
        $map = [
            'id' => $row['id'],
            'map_name' => $row['map_name'],
            'map_code' => $row['map_code'],
            'map_type' => $row['map_type'],
            'level_requirement' => $row['level_requirement'],
            'realm_requirement' => $row['realm_requirement'],
            'description' => $row['description'],
            'background_image' => $row['background_image'],
            'map_icon' => $row['map_icon'],
            'entry_cost' => $row['entry_cost'],
            'daily_limit' => $row['daily_limit'],
            'max_stages' => $row['max_stages'],
            'sort_order' => $row['sort_order'],
            'is_unlocked' => $isUnlocked,
            'current_stage' => $progress ? $progress['current_stage'] : 1,
            'max_stage_reached' => $progress ? $progress['max_stage_reached'] : 0,
            'total_battles' => $progress ? $progress['total_battles'] : 0,
            'total_victories' => $progress ? $progress['total_victories'] : 0,
            'daily_entries' => $progress ? $progress['daily_entries'] : 0
        ];
        
        // 根据地图类型分类
        switch ($row['map_type']) {
            case 'normal':
                $normalMaps[] = $map;
                break;
            case 'dungeon':
                $dungeonMaps[] = $map;
                break;
            case 'special':
                $specialMaps[] = $map;
                break;
            case 'pvp':
                $pvpMaps[] = $map;
                break;
            default:
                $normalMaps[] = $map; // 默认放入普通地图
        }
    }
    
    // 返回分类后的地图数据
    echo json_encode([
        'success' => true, 
        'maps' => [
            'normal_maps' => $normalMaps,
            'dungeons' => $dungeonMaps,
            'special_maps' => $specialMaps,
            'pvp_maps' => $pvpMaps
        ]
    ]);
}

/**
 * 获取特定地图的详细信息和关卡
 */
function getMapDetail($pdo, $characterId, $mapId) {
    if ($mapId <= 0) {
        echo json_encode(['success' => false, 'message' => '无效的地图ID']);
        return;
    }

    // 获取地图详情
    $sql = "SELECT * FROM game_maps WHERE id = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$mapId]);
    $map = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$map) {
        echo json_encode(['success' => false, 'message' => '地图不存在']);
        return;
    }

    // 获取用户在此地图的进度
    $sql = "SELECT * FROM user_map_progress WHERE character_id = ? AND map_id = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$characterId, $mapId]);
    $progress = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$progress) {
        // 创建新的进度记录
        $sql = "INSERT INTO user_map_progress (character_id, map_id, map_code, current_stage, max_stage_reached)
                VALUES (?, ?, ?, 1, 0)";
        $mapCode = $map['map_code'];
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$characterId, $mapId, $mapCode]);
        
        $progress = [
            'character_id' => $characterId,
            'map_id' => $mapId,
                'current_stage' => 1,
            'max_stage_reached' => 0
        ];
    }

    // 获取地图关卡
    $sql = "SELECT ms.*
            FROM map_stages ms
            WHERE ms.map_id = ?
            ORDER BY ms.stage_number ASC";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$mapId]);
    $stages = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 检查地图是否已解锁
    $isUnlocked = checkMapUnlockStatus($pdo, $characterId, $map);
        
    echo json_encode([
        'success' => true,
        'map' => $map, 
        'progress' => $progress, 
        'stages' => $stages,
        'is_unlocked' => $isUnlocked
    ]);
}

/**
 * 获取用户在特定地图的进度
 */
function getUserProgress($pdo, $characterId, $mapCode) {
    if (empty($mapCode)) {
        echo json_encode(['success' => false, 'message' => '无效的地图代码']);
        return;
    }

    // 先获取地图ID
    $sql = "SELECT id FROM game_maps WHERE map_code = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$mapCode]);
    $mapRow = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$mapRow) {
        echo json_encode(['success' => false, 'message' => '地图不存在']);
        return;
    }

    $mapId = $mapRow['id'];

    // 获取用户进度
    $sql = "SELECT * FROM user_map_progress WHERE character_id = ? AND map_id = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$characterId, $mapId]);
    $progress = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($progress) {
        echo json_encode(['success' => true, 'progress' => $progress]);
    } else {
        // 如果没有进度记录，返回默认值
        echo json_encode([
            'success' => true,
            'progress' => [
                'current_stage' => 1,
                'max_stage_reached' => 0,
                'total_battles' => 0,
                'total_victories' => 0
            ]
        ]);
    }
}

/**
 * 获取特定关卡的信息
 */
function getStageInfo($pdo, $characterId, $mapId, $stageNumber) {
    if ($mapId <= 0 || $stageNumber <= 0) {
        echo json_encode(['success' => false, 'message' => '无效的地图或关卡ID']);
        return;
    }

    // 获取关卡信息
    $sql = "SELECT ms.*
            FROM map_stages ms
            WHERE ms.map_id = ? AND ms.stage_number = ?";

    $stmt = $pdo->prepare($sql);
    $stmt->execute([$mapId, $stageNumber]);
    $stageInfo = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$stageInfo) {
        echo json_encode(['success' => false, 'message' => '关卡不存在']);
        return;
    }

    // 获取地图信息
    $sql = "SELECT * FROM game_maps WHERE id = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$mapId]);
    $mapInfo = $stmt->fetch(PDO::FETCH_ASSOC);

    // 检查用户是否可以进入该关卡
    $canEnter = false;
    $sql = "SELECT max_stage_reached FROM user_map_progress WHERE character_id = ? AND map_id = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$characterId, $mapId]);
    $progress = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($progress) {
        $canEnter = $stageNumber <= $progress['max_stage_reached'] || $stageNumber == 1;
    } else {
        // 如果没有进度记录，只能进入第一关
        $canEnter = $stageNumber == 1;
    }

    // 检查地图是否已解锁
    $isMapUnlocked = checkMapUnlockStatus($pdo, $characterId, $mapInfo);
                
                echo json_encode([
                    'success' => true,
        'stage' => $stageInfo,
                    'map' => $mapInfo,
        'can_enter' => $canEnter && $isMapUnlocked
    ]);
}

/**
 * 更新用户地图进度
 */
function updateUserProgress($pdo, $characterId, $mapId, $currentStage) {
    if ($mapId <= 0 || $currentStage <= 0) {
        echo json_encode(['success' => false, 'message' => '无效的地图或关卡ID']);
        return;
    }

    // 获取地图信息
    $sql = "SELECT * FROM game_maps WHERE id = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$mapId]);
    $map = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$map) {
        echo json_encode(['success' => false, 'message' => '地图不存在']);
        return;
    }

    // 检查关卡是否存在
    $sql = "SELECT COUNT(*) as count FROM map_stages WHERE map_id = ? AND stage_number = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$mapId, $currentStage]);
    $stageCount = $stmt->fetchColumn();

    if ($stageCount == 0) {
        echo json_encode(['success' => false, 'message' => '关卡不存在']);
        return;
    }

    // 获取用户进度
    $sql = "SELECT * FROM user_map_progress WHERE character_id = ? AND map_id = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$characterId, $mapId]);
    $progress = $stmt->fetch(PDO::FETCH_ASSOC);

    $mapCode = $map['map_code'];

    if ($progress) {
        // 更新最高关卡记录
        $maxStageReached = max($progress['max_stage_reached'], $currentStage);

        // 更新进度
        $sql = "UPDATE user_map_progress
                SET current_stage = ?, max_stage_reached = ?, last_played_at = NOW()
                WHERE character_id = ? AND map_id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$currentStage, $maxStageReached, $characterId, $mapId]);
    } else {
        // 创建新的进度记录
        $sql = "INSERT INTO user_map_progress
                (character_id, map_id, map_code, current_stage, max_stage_reached, last_played_at)
                VALUES (?, ?, ?, ?, ?, NOW())";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$characterId, $mapId, $mapCode, $currentStage, $currentStage]);
    }
        
        echo json_encode([
            'success' => true,
        'message' => '进度更新成功',
        'current_stage' => $currentStage
    ]);
}

/**
 * 检查地图是否已解锁
 */
function checkMapUnlockStatus($pdo, $characterId, $map) {
    // 第一张地图默认解锁
    if ($map['id'] == 1 || $map['map_code'] == 'kunlun') {
        return true;
    }

    // 检查境界等级需求
    if ($map['realm_requirement'] > 1) {
        $sql = "SELECT realm_id FROM characters WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$characterId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($character) {
            if ($character['realm_id'] < $map['realm_requirement']) {
                return false;
            }
        } else {
            return false;
        }
    }
    
    // 地图类型特殊处理
    switch ($map['map_type']) {
        case 'normal':
            // 普通地图需要完成前一张地图
            $prevMapCode = '';
            
            if ($map['map_code'] == 'donghai') {
                $prevMapCode = 'kunlun';
                $requiredStage = 60; // 🔧 修复：太乙峰实际关卡数
            } else if ($map['map_code'] == 'jiuyou') {
                $prevMapCode = 'donghai';
                $requiredStage = 90; // 🔧 修复：碧水寒潭实际关卡数
            } else if ($map['map_code'] == 'santiansan') {
                $prevMapCode = 'jiuyou';
                $requiredStage = 120; // 🔧 修复：赤焰谷实际关卡数
            }
            
            if (!empty($prevMapCode)) {
                return checkPreviousMapProgress($pdo, $characterId, $prevMapCode, $requiredStage);
            }
            break;

        case 'dungeon':
            // 秘境探索需要达到特定条件
            if ($map['map_code'] == 'huolong') {
                // 火龙洞窟需要昆仑山脉达到50关
                return checkPreviousMapProgress($pdo, $characterId, 'kunlun', 50);
            } else if ($map['map_code'] == 'hanbing') {
                // 寒冰峡谷需要东海龙宫达到50关
                return checkPreviousMapProgress($pdo, $characterId, 'donghai', 50);
            }
            break;
    }
    
    // 默认解锁
    return true;
}

/**
 * 检查前置地图进度
 */
function checkPreviousMapProgress($pdo, $characterId, $mapCode, $requiredStage) {
    // 获取地图ID
    $sql = "SELECT id FROM game_maps WHERE map_code = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$mapCode]);
    $map = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$map) {
        return false;
    }

    $mapId = $map['id'];

    // 检查用户进度
    $sql = "SELECT max_stage_reached FROM user_map_progress WHERE character_id = ? AND map_id = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$characterId, $mapId]);
    $progress = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$progress) {
        return false;
    }

    return $progress['max_stage_reached'] >= $requiredStage;
}
?>