

        /* 主要内容区域 */
        .main-container {
            padding: 8px 8px 80px 8px;
            height: 100vh;
            position: relative;
            z-index: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        /* 顶部标题栏 - 紧凑设计 */
        .header {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.12), rgba(255, 255, 255, 0.04));
            border-radius: 12px;
            padding: 8px 12px;
            margin-bottom: 8px;
            border: 1px solid rgba(212, 175, 55, 0.4);
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 40px;
        }

        .header-title {
            font-size: 16px;
            font-weight: bold;
            color: #d4af37;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .back-btn {
            background: rgba(212, 175, 55, 0.2);
            border: 1px solid rgba(212, 175, 55, 0.5);
            border-radius: 8px;
            color: #d4af37;
            padding: 4px 8px;
            font-size: 12px;
            cursor: pointer;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 3px;
        }

        /* 炼丹界面主体 - 垂直布局 */
        .alchemy-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 8px;
            overflow: hidden;
            max-height: calc(100vh - 140px);
        }

        /* 丹方选择区域 - 占满整个空间 */
        .recipe-section {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.03));
            border-radius: 12px;
            padding: 8px;
            border: 1px solid rgba(212, 175, 55, 0.3);
            backdrop-filter: blur(8px);
            flex: 1;
            display: flex;
            flex-direction: column;
            max-height: calc(100vh - 200px);
            min-height: 250px;
        }

        .section-title {
            font-size: 13px;
            font-weight: bold;
            color: #d4af37;
            margin-bottom: 6px;
            text-align: center;
        }

        .recipe-tabs {
            display: flex;
            gap: 4px;
            margin-bottom: 8px;
        }

        .recipe-tab {
            background: rgba(0, 0, 0, 0.4);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            padding: 4px 6px;
            font-size: 10px;
            color: #bdc3c7;
            cursor: pointer;
            transition: all 0.3s ease;
            flex: 1;
            text-align: center;
        }

        .recipe-tab.active {
            background: rgba(212, 175, 55, 0.3);
            border-color: #d4af37;
            color: #d4af37;
        }

        .recipe-list {
            flex: 1;
            overflow-y: auto;
            max-height: 50vh;
            min-height: 200px;
            padding-right: 4px;
        }

        .recipe-item {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid transparent;
            border-radius: 8px;
            padding: 8px;
            margin-bottom: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .recipe-item:hover {
            border-color: rgba(212, 175, 55, 0.5);
        }

        .recipe-item.selected {
            border-color: #d4af37;
            background: rgba(212, 175, 55, 0.2);
        }

        .recipe-name {
            font-size: 14px;
            color: #fff;
            margin-bottom: 4px;
            font-weight: bold;
        }

        .recipe-description {
            font-size: 11px;
            color: #bdc3c7;
            margin-bottom: 3px;
        }

        .recipe-requirements {
            font-size: 10px;
            color: #4CAF50;
        }

        /* 🆕 材料充足性样式 */
        .material-sufficient {
            color: #4CAF50; /* 绿色 - 材料充足 */
        }

        .material-insufficient {
            color: #f44336; /* 红色 - 材料不足 */
        }

        .recipe-item.insufficient-materials {
            opacity: 0.7;
            border: 1px solid rgba(244, 67, 84, 0.3);
        }

        .recipe-item.insufficient-materials .recipe-name {
            color: #ffcccb;
        }

        /* 底部炼制按钮 */
        .craft-button-container {
            padding: 8px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.03));
            border-radius: 12px;
            border: 1px solid rgba(212, 175, 55, 0.3);
            backdrop-filter: blur(8px);
        }

        .craft-button {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            border: none;
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 16px;
            font-weight: bold;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }

        .craft-button:hover {
            background: linear-gradient(135deg, #45a049, #4CAF50);
            transform: translateY(-1px);
        }

        .craft-button:disabled {
            background: linear-gradient(135deg, #666, #555);
            cursor: not-allowed;
            transform: none;
        }

        /* 丹炉选择弹窗 */
        .furnace-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            backdrop-filter: blur(5px);
        }

        .furnace-modal-content {
            background: linear-gradient(135deg, rgba(26, 58, 92, 0.95), rgba(45, 89, 132, 0.95));
            border-radius: 15px;
            padding: 20px;
            max-width: 90%;
            max-height: 80%;
            border: 2px solid rgba(212, 175, 55, 0.5);
            backdrop-filter: blur(10px);
            overflow-y: auto;
        }

        .furnace-modal-header {
            text-align: center;
            margin-bottom: 15px;
            color: #d4af37;
            font-size: 16px;
            font-weight: bold;
        }

        .furnace-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 8px;
            margin-bottom: 15px;
        }

        .furnace-item {
            background: rgba(0, 0, 0, 0.4);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .furnace-item:hover {
            border-color: rgba(212, 175, 55, 0.5);
            background: rgba(212, 175, 55, 0.2);
        }

        .furnace-item.selected {
            border-color: #d4af37;
            background: rgba(212, 175, 55, 0.3);
            box-shadow: 0 0 8px rgba(212, 175, 55, 0.4);
        }

        .furnace-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .furnace-icon {
            font-size: 20px;
        }

        .furnace-details {
            display: flex;
            flex-direction: column;
        }

        .furnace-name {
            font-size: 12px;
            color: #fff;
            font-weight: bold;
            margin-bottom: 2px;
        }

        .furnace-bonus {
            font-size: 10px;
            color: #4CAF50;
        }

        .furnace-quantity {
            font-size: 10px;
            color: #bdc3c7;
        }

        .furnace-stats {
            text-align: right;
            font-size: 10px;
        }

        .furnace-modal-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .modal-button {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            border: none;
            border-radius: 8px;
            padding: 8px 16px;
            font-size: 12px;
            font-weight: bold;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .modal-button.cancel {
            background: linear-gradient(135deg, #666, #555);
        }

        .modal-button:hover {
            transform: translateY(-1px);
        }

        .modal-button:disabled {
            background: linear-gradient(135deg, #666, #555);
            cursor: not-allowed;
            transform: none;
        }

        /* 炼制详情显示 */
        .crafting-details {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 8px;
            margin-top: 10px;
            font-size: 10px;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
        }

        .detail-row:last-child {
            margin-bottom: 0;
        }

        /* 炼制遮罩层 */
        .crafting-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 999;
            display: none;
        }

        /* 炼制背景图片 */
        .crafting-background {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 300px;
            height: 300px;
            background-image: url('../images/cuiti_11.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            z-index: 1000;
            display: none;
            opacity: 0.6;
        }

        /* 炼制进度条 */
        .crafting-progress {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: linear-gradient(135deg, rgba(26, 58, 92, 0.95), rgba(45, 89, 132, 0.95));
            border-radius: 15px;
            padding: 20px;
            border: 2px solid rgba(212, 175, 55, 0.5);
            backdrop-filter: blur(10px);
            z-index: 1001;
            display: none;
            min-width: 250px;
        }

        .progress-bar {
            width: 100%;
            height: 12px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 6px;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 8px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-text {
            text-align: center;
            font-size: 12px;
            color: #d4af37;
            font-weight: bold;
        }

        /* 滚动条样式 */
        .recipe-list::-webkit-scrollbar {
            width: 6px;
        }

        .recipe-list::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 3px;
        }

        .recipe-list::-webkit-scrollbar-thumb {
            background: rgba(212, 175, 55, 0.5);
            border-radius: 3px;
            transition: background 0.3s ease;
        }

        .recipe-list::-webkit-scrollbar-thumb:hover {
            background: rgba(212, 175, 55, 0.7);
        }

        /* 消息提示样式 */
        .message {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 10000;
            animation: messageSlideIn 0.3s ease-out;
        }

        .message.success {
            background: rgba(76, 175, 80, 0.9);
        }

        .message.success-multiple {
            background: linear-gradient(45deg, rgba(212, 175, 55, 0.9), rgba(255, 215, 0, 0.9));
            box-shadow: 0 0 20px rgba(212, 175, 55, 0.5);
            font-weight: bold;
        }

        .message.error {
            background: rgba(244, 67, 54, 0.9);
        }

        .message.info {
            background: rgba(33, 150, 243, 0.9);
        }

        /* 🆕 复数产出特效样式 */
        .multiple-output-effect {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 10001;
            pointer-events: none;
        }

        .effect-text {
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            color: #d4af37;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            margin-bottom: 10px;
            animation: effectTextPulse 2s ease-in-out;
        }

        .effect-particles {
            position: relative;
            width: 200px;
            height: 100px;
        }

        .particle {
            position: absolute;
            font-size: 20px;
            animation: particleFloat 3s ease-out forwards;
        }

        .particle:nth-child(1) { animation-delay: 0s; left: 90px; top: 40px; }
        .particle:nth-child(2) { animation-delay: 0.2s; left: 110px; top: 45px; }
        .particle:nth-child(3) { animation-delay: 0.4s; left: 80px; top: 50px; }
        .particle:nth-child(4) { animation-delay: 0.6s; left: 120px; top: 35px; }
        .particle:nth-child(5) { animation-delay: 0.8s; left: 100px; top: 55px; }

        @keyframes effectTextPulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.2); opacity: 0.8; }
        }

        @keyframes particleFloat {
            0% {
                opacity: 1;
                transform: translate(0, 0) scale(1);
            }
            25% {
                opacity: 0.8;
                transform: translate(-10px, -20px) scale(1.2);
            }
            50% {
                opacity: 0.6;
                transform: translate(15px, -40px) scale(1.3);
            }
            100% {
                opacity: 0;
                transform: translate(-5px, -80px) scale(1.5);
            }
        }

        /* 横屏适配 */
        @media (orientation: landscape) and (max-height: 500px) {
            .main-container {
                padding: 4px 4px 60px 4px;
            }
            
            .header {
                height: 32px;
                padding: 4px 8px;
                margin-bottom: 4px;
            }
            
            .header-title {
                font-size: 14px;
            }
            
            .alchemy-container {
                gap: 4px;
                max-height: calc(100vh - 100px);
            }
            
            .recipe-section {
                max-height: calc(100vh - 160px);
                min-height: 180px;
            }
            
            .recipe-list {
                max-height: 35vh;
                min-height: 150px;
            }
        }

        /* 超小屏幕优化 */
        @media (max-width: 360px) {
            .main-container {
                padding: 6px 6px 75px 6px;
            }
            
            .recipe-list {
                max-height: 45vh;
                min-height: 180px;
            }
            
            .recipe-section {
                max-height: calc(100vh - 180px);
            }
        }