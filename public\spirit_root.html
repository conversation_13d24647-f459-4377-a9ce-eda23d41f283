<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover">
    
    <!-- 🎛️ 全局调试开关 - 必须最早加载 -->
    <script src="assets/js/global-debug-switch.js"></script>

    <!-- 🔧 项目配置文件 - 必须早期加载 -->
    <script src="assets/js/config.js"></script>

    <!-- 新增HBuilder X优化meta标签 -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="format-detection" content="telephone=no">
    <!-- PWA支持 -->
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#d4af37">
    
    <title>一念修仙 - 灵根系统</title>
    <link rel="stylesheet" href="assets/css/global.css">
    <link rel="stylesheet" href="assets/css/spirit_root.css">
    <link rel="stylesheet" href="assets/css/spiritual-material-usage.css">

    <!-- 引入通用导航样式 -->
    <link rel="stylesheet" href="assets/css/common-navigation.css">
    <!-- 🔑 全局登录检查系统 -->
    <script src="assets/js/auth-check.js"></script>

    <!-- 🎵 全局音乐管理器 -->
    <script src="assets/js/global-music-manager.js"></script>

    <!-- 移动端CSS已删除 -->

</head>
<body>
    <div class="main-container">
        <!-- 页面标题 -->
        <div class="page-title">
            <h1>灵根系统</h1>
            <div class="page-subtitle">修炼之根本，悟道之基础</div>
        </div>

        <!-- ✨ 整合的五行相生相克图 -->
        <div class="wuxing-diagram">
            <!-- 灵根总览信息 -->
            <div class="spirit-overview-compact">
                <div class="overview-item">
                    <span class="overview-label">总值:</span>
                    <span class="overview-value" id="totalSpiritValue">-</span>
                </div>
                <div class="overview-item">
                    <span class="overview-label">资质:</span>
                    <span class="overview-value" id="spiritQuality">-</span>
                </div>
            </div>
            
            <div class="wuxing-container">
                <!-- 中心标题 -->
                <div class="wuxing-center">
                    <div class="center-title">五行灵根</div>
                </div>                
               
                <!-- 五行元素按五角星排列 -->
                <!-- 火（正上方） -->
                <div class="wuxing-element fire" data-element="fire">
                    <div class="element-icon">🔥</div>
                    <div class="element-name">火</div>
                    <div class="element-value" id="fireValue">-</div>
                </div>
                
                <!-- 土（右上） -->
                <div class="wuxing-element earth" data-element="earth">
                    <div class="element-icon">🗻</div>
                    <div class="element-name">土</div>
                    <div class="element-value" id="earthValue">-</div>
                </div>
                
                <!-- 金（右下） -->
                <div class="wuxing-element metal" data-element="metal">
                    <div class="element-icon">⚔️</div>
                    <div class="element-name">金</div>
                    <div class="element-value" id="metalValue">-</div>
                </div>
                
                <!-- 水（左下） -->
                <div class="wuxing-element water" data-element="water">
                    <div class="element-icon">💧</div>
                    <div class="element-name">水</div>
                    <div class="element-value" id="waterValue">-</div>
                </div>
                
                <!-- 木（左上） -->
                <div class="wuxing-element wood" data-element="wood">
                    <div class="element-icon">🌲</div>
                    <div class="element-name">木</div>
                    <div class="element-value" id="woodValue">-</div>
                </div>
            </div>
            
            <!-- 灵根操作面板 -->
            <div class="spirit-actions-panel">
                <button class="action-button reset-btn" onclick="resetSpirits()">
                    <span class="button-icon">🔄</span>
                    <span>重置灵根</span>
                </button>
                <button class="action-button info-btn" onclick="showSpiritIntro()">
                    <span class="button-icon">📖</span>
                    <span>灵根介绍</span>
                </button>
                <button class="action-button enhance-btn" onclick="enhanceSpirits()">
                    <span class="button-icon">⬆️</span>
                    <span>提升灵根</span>
                </button>
            </div>
        </div>

        <!-- 灵根详情弹窗 -->
        <div class="spirit-modal" id="spiritModal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="modalTitle">灵根详情</h3>
                    <button class="modal-close" onclick="closeSpiritModal()">&times;</button>
                </div>
                <div class="modal-body" id="modalBody">
                    <!-- 动态内容 -->
                </div>
            </div>
        </div>

        <!-- 重置灵根确认弹窗 -->
        <div class="confirm-modal" id="resetModal" style="display: none;">
            <div class="confirm-content">
                <div class="confirm-header">
                    <h3>重置灵根</h3>
                    <button class="modal-close" onclick="closeResetModal()">&times;</button>
                </div>
                <div class="confirm-body">
                    <p>使用洗灵液随机洗掉1-9点灵根，洗掉的灵根点数将转化为灵根点数供手动分配。</p>
                    
                    <div class="wash-quality-section">
                        <h4>选择洗灵液品质</h4>
                        <div class="quality-options">
                            <label class="quality-option">
                                <input type="radio" name="washQuality" value="1" checked>
                                <span>一品洗灵液 (洗掉1-3点)</span>
                            </label>
                            <label class="quality-option">
                                <input type="radio" name="washQuality" value="2">
                                <span>二品洗灵液 (洗掉2-4点)</span>
                            </label>
                            <label class="quality-option">
                                <input type="radio" name="washQuality" value="3">
                                <span>三品洗灵液 (洗掉3-5点)</span>
                            </label>
                            <label class="quality-option">
                                <input type="radio" name="washQuality" value="4">
                                <span>四品洗灵液 (洗掉4-6点)</span>
                            </label>
                            <label class="quality-option">
                                <input type="radio" name="washQuality" value="5">
                                <span>五品洗灵液 (洗掉5-7点)</span>
                            </label>
                            <label class="quality-option">
                                <input type="radio" name="washQuality" value="6">
                                <span>六品洗灵液 (洗掉6-8点)</span>
                            </label>
                            <label class="quality-option">
                                <input type="radio" name="washQuality" value="7">
                                <span>七品洗灵液 (洗掉7-9点)</span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="lock-section">
                        <h4>锁定灵根 <span class="lock-tip">(每锁定1个灵根多消耗1瓶洗灵液)</span></h4>
                        <div class="lock-options">
                            <label class="lock-option">
                                <input type="checkbox" id="lockFire" value="fire">
                                <span class="element-label fire-label">🔥 火灵根</span>
                            </label>
                            <label class="lock-option">
                                <input type="checkbox" id="lockEarth" value="earth">
                                <span class="element-label earth-label">🗻 土灵根</span>
                            </label>
                            <label class="lock-option">
                                <input type="checkbox" id="lockMetal" value="metal">
                                <span class="element-label metal-label">⚔️ 金灵根</span>
                            </label>
                            <label class="lock-option">
                                <input type="checkbox" id="lockWater" value="water">
                                <span class="element-label water-label">💧 水灵根</span>
                            </label>
                            <label class="lock-option">
                                <input type="checkbox" id="lockWood" value="wood">
                                <span class="element-label wood-label">🌲 木灵根</span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="cost-summary">
                        <p>消耗洗灵液数量：<span id="costCount">1</span>瓶</p>
                    </div>
                    
                    <p style="color: #e74c3c; font-size: 12px;">注意：此操作不可逆转！</p>
                    <div class="confirm-buttons">
                        <button class="confirm-btn cancel" onclick="closeResetModal()">取消</button>
                        <button class="confirm-btn confirm" onclick="confirmReset()">确认洗灵根</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 天材地宝使用弹窗 -->
        <div class="spiritual-material-modal" id="spiritualMaterialModal" style="display: none;">
            <div class="spiritual-material-content">
                <div class="spiritual-material-header">
                    <h3>天材地宝 - 灵根提升</h3>
                    <button class="modal-close" onclick="closeSpiritualMaterialModal()">&times;</button>
                </div>
                
                <div class="spiritual-material-body">
                    <!-- 分类标签 -->
                    <div class="material-category-tabs">
                        <button class="category-tab active" data-element="all">全部</button>
                        <button class="category-tab" data-element="metal">金系</button>
                        <button class="category-tab" data-element="wood">木系</button>
                        <button class="category-tab" data-element="water">水系</button>
                        <button class="category-tab" data-element="fire">火系</button>
                        <button class="category-tab" data-element="earth">土系</button>
                    </div>
                    
                    <!-- 天材地宝列表 - 可滚动区域 -->
                    <div class="material-list-container">
                        <div class="material-list" id="materialList">
                            <div class="loading-materials">
                                <div class="loading-spinner"></div>
                                <span>加载天材地宝中...</span>
                            </div>
                        </div>
                        </div>
                        </div>
                
                <!-- 使用控制面板 - 固定在底部 -->
                <div class="usage-controls-footer" id="usageControls" style="display: none;">
                    <div class="usage-title">使用天材地宝</div>
                    
                    <div class="selected-material-info" id="selectedMaterialInfo">
                        <!-- 动态内容 -->
                        </div>
                    
                    <div class="usage-actions">
                        <div class="quantity-selector">
                            <button class="quantity-btn" id="decreaseBtn" onclick="changeQuantity(-1)">-</button>
                            <input type="number" class="quantity-input" id="quantityInput" value="1" min="1" max="10" onchange="validateQuantity()">
                            <button class="quantity-btn" id="increaseBtn" onclick="changeQuantity(1)">+</button>
                        </div>
                        
                        <button class="use-material-btn" id="useMaterialBtn" onclick="useSpiritualMaterial()">
                            使用天材地宝
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航栏会由 common-navigation.js 自动插入 -->
    
    <script src="assets/js/common-navigation.js"></script>
    <script>
        // 全局变量
        let spiritRootData = null;

        // 页面加载
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('灵根系统页面加载...');
            
            try {
                await loadSpiritRootData();
                renderSpiritRoots();
                addElementInteraction();
            } catch (error) {
                console.error('页面加载失败:', error);
                showMessage('加载失败，请刷新页面', 'error');
            }
        });

        // 加载灵根数据
        async function loadSpiritRootData() {
            try {
                console.log('开始加载灵根数据...');
                const apiUrl = window.GameConfig ? window.GameConfig.getApiUrl('spirit_root.php?action=get_spiritual_materials') : '../src/api/spirit_root.php?action=get_spiritual_materials';
                console.log('API URL:', apiUrl);

                const response = await fetch(apiUrl);
                console.log('Response status:', response.status);
                console.log('Response ok:', response.ok);
                
                if (!response.ok) {
                    throw new Error(`HTTP错误! status: ${response.status} - ${response.statusText}`);
                }
                
                const text = await response.text();
                console.log('Raw response text:', text);
                
                let data;
                try {
                    data = JSON.parse(text);
                } catch (parseError) {
                    console.error('JSON解析失败:', parseError);
                    console.log('原始响应内容:', text);
                    throw new Error(`JSON解析失败: ${parseError.message}`);
                }
                
                console.log('解析后的数据:', data);
                
                if (data.success) {
                    spiritRootData = data.data;
                    console.log('灵根数据加载成功:', spiritRootData);
                } else {
                    throw new Error(data.message || '获取灵根数据失败');
                }
            } catch (error) {
                console.error('加载灵根数据失败:', error);
                throw error;
            }
        }

        // 渲染灵根界面
        function renderSpiritRoots() {
            if (!spiritRootData) return;

            // 更新总值和品质
            document.getElementById('totalSpiritValue').textContent = spiritRootData.total_value;
            document.getElementById('spiritQuality').textContent = spiritRootData.quality;

            // 设置品质颜色
            const qualityElement = document.getElementById('spiritQuality');
            qualityElement.style.color = getSpiritQualityColor(spiritRootData.quality);

            // 更新五行数值
            document.getElementById('metalValue').textContent = spiritRootData.roots.metal || 0;
            document.getElementById('woodValue').textContent = spiritRootData.roots.wood || 0;
            document.getElementById('waterValue').textContent = spiritRootData.roots.water || 0;
            document.getElementById('fireValue').textContent = spiritRootData.roots.fire || 0;
            document.getElementById('earthValue').textContent = spiritRootData.roots.earth || 0;
        }

        // 添加元素交互
        function addElementInteraction() {
            const elements = document.querySelectorAll('.wuxing-element');
            elements.forEach(element => {
                element.addEventListener('click', function() {
                    const elementType = this.dataset.element;
                    showElementModal(elementType);
                });
            });
        }

        // 显示元素详情弹窗
        function showElementModal(elementType) {
            const elementNames = {
                'metal': '金灵根',
                'wood': '木灵根', 
                'water': '水灵根',
                'fire': '火灵根',
                'earth': '土灵根'
            };

            const elementDescs = {
                'metal': '金灵根主杀伐，擅锋锐之术，增强物理攻击力和暴击率。',
                'wood': '木灵根主生机，善恢复之道，增强生命上限和法力回复。',
                'water': '水灵根主柔韧，精防御之法，增强法术防御和韧性。',
                'fire': '火灵根主爆发，重伤害输出，增强法术攻击力和伤害。',
                'earth': '土灵根主厚重，固根基之术，增强物理防御和生命值。'
            };

            const value = spiritRootData.roots[elementType] || 0;
            
            document.getElementById('modalTitle').textContent = elementNames[elementType];
            document.getElementById('modalBody').innerHTML = `
                <div class="element-detail">
                    <div class="detail-row">
                        <span class="detail-label">当前等级：</span>
                        <span class="detail-value">${value}</span>
                        </div>
                    <div class="detail-desc">
                        <p>${elementDescs[elementType]}</p>
                    </div>
                    <div class="element-enhance">
                        <h4>手动加点</h4>
                        <p>消耗灵根点数提升该灵根（灵根点数通过洗灵根获得）：</p>
                        <div class="enhance-controls">
                            <button class="enhance-point-btn" onclick="addSpiritPoint('${elementType}', 1)">+1 (消耗1灵根点)</button>
                            <button class="enhance-point-btn" onclick="addSpiritPoint('${elementType}', 5)">+5 (消耗5灵根点)</button>
                            <button class="enhance-point-btn" onclick="addSpiritPoint('${elementType}', 10)">+10 (消耗10灵根点)</button>
                        </div>
                    </div>
                    </div>
                `;
                
            document.getElementById('spiritModal').style.display = 'flex';
        }

        // 关闭灵根详情弹窗
        function closeSpiritModal() {
            document.getElementById('spiritModal').style.display = 'none';
        }

        // 重置灵根
        function resetSpirits() {
            document.getElementById('resetModal').style.display = 'flex';
            // 重置选择状态
            document.querySelector('input[name="washQuality"][value="1"]').checked = true;
            document.querySelectorAll('.lock-option input[type="checkbox"]').forEach(cb => cb.checked = false);
            updateCostCount();
            
            // 添加监听器
            document.querySelectorAll('input[name="washQuality"]').forEach(radio => {
                radio.addEventListener('change', updateCostCount);
            });
            document.querySelectorAll('.lock-option input[type="checkbox"]').forEach(checkbox => {
                checkbox.addEventListener('change', updateCostCount);
            });
        }

        // 更新消耗数量
        function updateCostCount() {
            let baseCost = 1;
            let lockedCount = document.querySelectorAll('.lock-option input[type="checkbox"]:checked').length;
            let totalCost = baseCost + lockedCount;
            document.getElementById('costCount').textContent = totalCost;
        }

        // 关闭重置确认弹窗
        function closeResetModal() {
            document.getElementById('resetModal').style.display = 'none';
        }

        // 确认重置
        async function confirmReset() {
            try {
                showMessage('功能开发中，敬请期待...', 'info');
                closeResetModal();
            } catch (error) {
                showMessage('重置失败：' + error.message, 'error');
            }
        }

        // 灵根介绍
        function showSpiritIntro() {
            document.getElementById('modalTitle').textContent = '灵根介绍';
            document.getElementById('modalBody').innerHTML = `
                <div class="spirit-intro">
                    <h4>灵根作用</h4>
                    <p>灵根是修仙者的天赋根基，决定了对五行法术的亲和力和修炼速度。</p>
                    
                    <h4>五行伤害加成</h4>
                    <ul>
                        <li><strong>金灵根</strong>：增加金系技能附加伤害，公式：金灵根值 × 1.2</li>
                        <li><strong>木灵根</strong>：增加木系技能附加伤害，公式：木灵根值 × 1.2</li>
                        <li><strong>水灵根</strong>：增加水系技能附加伤害，公式：水灵根值 × 1.2</li>
                        <li><strong>火灵根</strong>：增加火系技能附加伤害，公式：火灵根值 × 1.2</li>
                        <li><strong>土灵根</strong>：增加土系技能附加伤害，公式：土灵根值 × 1.2</li>
                    </ul>
                    
                    <h4>五行相克关系</h4>
                    <p><strong>相生</strong>：金生水，水生木，木生火，火生土，土生金</p>
                    <p><strong>相克</strong>：金克木，木克土，土克水，水克火，火克金</p>
                    
                    <h4>资质等级</h4>
                    <p>根据灵根总值划分资质：</p>
                    <ul>
                        <li>凡品：总值 0-100</li>
                        <li>下品：总值 101-150</li>
                        <li>中品：总值 151-300</li>
                        <li>上品：总值 301-500</li>
                        <li>极品：总值 501+</li>
                    </ul>
                </div>
            `;
            document.getElementById('spiritModal').style.display = 'flex';
        }

        // 天材地宝使用系统变量
        let spiritualMaterials = [];
        let usageRecords = [];
        let selectedMaterial = null;
        let currentCategory = 'all';

        // 提升灵根
        async function enhanceSpirits() {
            document.getElementById('spiritualMaterialModal').style.display = 'flex';
            await loadSpiritualMaterials();
            setupCategoryTabs();
        }

        // 关闭天材地宝使用弹窗
        function closeSpiritualMaterialModal() {
            document.getElementById('spiritualMaterialModal').style.display = 'none';
            selectedMaterial = null;
            document.getElementById('usageControls').style.display = 'none';
        }

        // 加载天材地宝列表
        async function loadSpiritualMaterials() {
            try {
                const response = await fetch(window.GameConfig ? window.GameConfig.getApiUrl('spiritual_material_usage.php?action=get_spiritual_materials') : '../src/api/spiritual_material_usage.php?action=get_spiritual_materials');
                const data = await response.json();
                
                if (data.success) {
                    spiritualMaterials = data.data.materials || [];
                    usageRecords = data.data.usage_records || [];
                    renderMaterialList();
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                console.error('加载天材地宝失败:', error);
                document.getElementById('materialList').innerHTML = `
                    <div class="empty-materials">
                        <div class="empty-materials-icon">❌</div>
                        <div class="empty-materials-text">加载失败</div>
                        <div class="empty-materials-hint">${error.message}</div>
                    </div>
                `;
            }
        }

        // 渲染天材地宝列表
        function renderMaterialList() {
            const materialList = document.getElementById('materialList');
            
            if (spiritualMaterials.length === 0) {
                materialList.innerHTML = `
                    <div class="empty-materials">
                        <div class="empty-materials-icon">📦</div>
                        <div class="empty-materials-text">暂无天材地宝</div>
                        <div class="empty-materials-hint">通过奇遇事件可以获得天材地宝</div>
                    </div>
                `;
                return;
            }
            
            // 过滤材料
            let filteredMaterials = spiritualMaterials;
            if (currentCategory !== 'all') {
                filteredMaterials = spiritualMaterials.filter(material => 
                    material.boost_type === currentCategory
                );
            }
            
            if (filteredMaterials.length === 0) {
                materialList.innerHTML = `
                    <div class="empty-materials">
                        <div class="empty-materials-icon">🔍</div>
                        <div class="empty-materials-text">该分类暂无天材地宝</div>
                    </div>
                `;
                return;
            }
            
            materialList.innerHTML = filteredMaterials.map(material => {
                const elementName = getElementName(material.boost_type);
                const elementIcon = getElementIcon(material.boost_type);
                const rarityClass = `rarity-${material.rarity}`;
                const elementClass = `element-${material.boost_type}`;
                
                // 获取使用记录
                const usageRecord = usageRecords.find(record => record.item_code === material.item_code);
                const usedCount = usageRecord ? usageRecord.usage_count : 0;
                const maxUsage = 10;
                const canUse = usedCount < maxUsage;
                
                return `
                    <div class="material-item ${rarityClass} ${!canUse ? 'disabled' : ''}" onclick="selectMaterial(${material.inventory_id})">
                        <div class="material-header">
                            <div class="material-icon">${elementIcon}</div>
                            <div class="material-info">
                                <div class="material-name">${material.item_name}</div>
                                <div class="material-quantity">数量: ${material.quantity}</div>
                            </div>
                        </div>
                        <div class="material-effect ${elementClass}">
                            提升${elementName}灵根 +${material.boost_value}点
                        </div>
                        <div class="material-usage ${!canUse ? 'usage-full' : ''}">
                            已使用: ${usedCount}/${maxUsage}
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 设置分类标签
        function setupCategoryTabs() {
            const tabs = document.querySelectorAll('.category-tab');
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // 移除所有active类
                    tabs.forEach(t => t.classList.remove('active'));
                    // 添加active类到当前标签
                    this.classList.add('active');
                    // 更新当前分类
                    currentCategory = this.dataset.element;
                    // 重新渲染列表
                    renderMaterialList();
                    // 隐藏使用控制面板
                    document.getElementById('usageControls').style.display = 'none';
                    selectedMaterial = null;
                });
            });
        }

        // 选择天材地宝
        function selectMaterial(inventoryId) {
            // 确保类型匹配 - API返回的是字符串类型
            selectedMaterial = spiritualMaterials.find(m => m.inventory_id == inventoryId);
            
            if (!selectedMaterial) return;
            
            // 检查是否已经使用满
            const usageRecord = usageRecords.find(record => record.item_code === selectedMaterial.item_code);
            const usedCount = usageRecord ? usageRecord.usage_count : 0;
            const maxUsage = 10;
            
            if (usedCount >= maxUsage) {
                showMessage('该天材地宝已使用满10个，无法继续使用', 'error');
                return;
            }
            
            // 更新选中状态
            document.querySelectorAll('.material-item').forEach(item => {
                item.classList.remove('selected');
            });
            event.target.closest('.material-item').classList.add('selected');
            
            // 显示使用控制面板
            updateSelectedMaterialInfo();
            document.getElementById('usageControls').style.display = 'block';
            
            // 重置数量
            document.getElementById('quantityInput').value = 1;
            validateQuantity();
        }

        // 更新选中天材地宝信息
        function updateSelectedMaterialInfo() {
            if (!selectedMaterial) return;
            
            const elementIcon = getElementIcon(selectedMaterial.boost_type);
            
            // 获取使用记录
            const usageRecord = usageRecords.find(record => record.item_code === selectedMaterial.item_code);
            const usedCount = usageRecord ? usageRecord.usage_count : 0;
            const maxUsage = 10;
            const remainingUsage = maxUsage - usedCount;
            
            document.getElementById('selectedMaterialInfo').innerHTML = `
                <div class="selected-material-name">
                    ${elementIcon} ${selectedMaterial.item_name} (拥有${selectedMaterial.quantity}个)
                </div>
                <div class="selected-material-limit">
                    已使用: ${usedCount}/${maxUsage} | 剩余可用: ${remainingUsage}个
                </div>
            `;
        }

        // 改变数量
        function changeQuantity(delta) {
            const input = document.getElementById('quantityInput');
            let newValue = parseInt(input.value) + delta;
            
            if (newValue < 1) newValue = 1;
            if (selectedMaterial && newValue > selectedMaterial.quantity) {
                newValue = selectedMaterial.quantity;
            }
            if (newValue > 10) newValue = 10;
            
            input.value = newValue;
            validateQuantity();
        }

        // 验证数量
        function validateQuantity() {
            const input = document.getElementById('quantityInput');
            const decreaseBtn = document.getElementById('decreaseBtn');
            const increaseBtn = document.getElementById('increaseBtn');
            const useMaterialBtn = document.getElementById('useMaterialBtn');
            
            let value = parseInt(input.value);
            if (isNaN(value) || value < 1) {
                value = 1;
                input.value = 1;
            }
            
            if (selectedMaterial) {
                // 获取使用记录
                const usageRecord = usageRecords.find(record => record.item_code === selectedMaterial.item_code);
                const usedCount = usageRecord ? usageRecord.usage_count : 0;
                const maxUsage = 10;
                const remainingUsage = maxUsage - usedCount;
                
                // 限制数量不能超过背包数量和剩余可用数量
                const maxAllowed = Math.min(selectedMaterial.quantity, remainingUsage);
                
                if (value > maxAllowed) {
                    value = maxAllowed;
                    input.value = value;
                }
                
                decreaseBtn.disabled = value <= 1;
                increaseBtn.disabled = value >= maxAllowed;
                useMaterialBtn.disabled = value <= 0 || value > maxAllowed || remainingUsage <= 0;
                
                // 如果剩余可用数量为0，显示提示
                if (remainingUsage <= 0) {
                    useMaterialBtn.textContent = '已达使用上限';
                } else {
                    useMaterialBtn.textContent = '使用天材地宝';
                }
            }
        }

        // 使用天材地宝
        async function useSpiritualMaterial() {
            const quantity = parseInt(document.getElementById('quantityInput').value);
            
            if (!selectedMaterial || quantity <= 0) {
                showMessage('请选择有效的天材地宝和数量', 'error');
                return;
            }
            
            // 🔧 修复：立即禁用按钮，防止连点
                const useMaterialBtn = document.getElementById('useMaterialBtn');
            if (useMaterialBtn.disabled) {
                return; // 如果按钮已禁用，直接返回
            }
                useMaterialBtn.disabled = true;
                useMaterialBtn.textContent = '使用中...';
                
            try {
                const response = await fetch(window.GameConfig ? window.GameConfig.getApiUrl('spiritual_material_usage.php') : '../src/api/spiritual_material_usage.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `action=use_material&inventory_id=${selectedMaterial.inventory_id}&quantity=${quantity}`
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showMessage(data.message, 'success');
                    
                    // 更新灵根数据 - 重新加载完整的灵根数据
                    await loadSpiritRootData();
                    renderSpiritRoots();
                    
                    // 重新加载天材地宝列表和使用记录
                    await loadSpiritualMaterials();
                    
                    // 如果当前选中的天材地宝还存在，重新选中并更新信息
                    const updatedMaterial = spiritualMaterials.find(m => m.inventory_id == selectedMaterial.inventory_id);
                    if (updatedMaterial) {
                        selectedMaterial = updatedMaterial;
                        updateSelectedMaterialInfo();
                        validateQuantity();
                    } else {
                        // 如果天材地宝已用完，关闭控制面板
                        document.getElementById('usageControls').style.display = 'none';
                        selectedMaterial = null;
                    }
                    
                } else {
                    throw new Error(data.message);
                }
                
            } catch (error) {
                console.error('使用天材地宝失败:', error);
                showMessage('使用失败：' + error.message, 'error');
            } finally {
                // 🔧 修复：确保按钮状态恢复，但要检查是否还有可用数量
                if (selectedMaterial && selectedMaterial.quantity > 0) {
                useMaterialBtn.disabled = false;
                useMaterialBtn.textContent = '使用天材地宝';
                } else {
                    // 如果没有可用数量，保持禁用状态
                    useMaterialBtn.disabled = true;
                    useMaterialBtn.textContent = '数量不足';
                }
            }
        }

        // 获取元素图标
        function getElementIcon(elementType) {
            const icons = {
                'metal': '⚔️',
                'wood': '🌲',
                'water': '💧',
                'fire': '🔥',
                'earth': '🗻'
            };
            return icons[elementType] || '💎';
        }

        // 获取元素中文名称（前端版本）
        function getElementName(elementType) {
            const names = {
                'metal': '金',
                'wood': '木',
                'water': '水',
                'fire': '火',
                'earth': '土'
            };
            return names[elementType] || elementType;
        }

        // 手动加点
        async function addSpiritPoint(elementType, points) {
            try {
                showMessage(`手动加点功能开发中...`, 'info');
                closeSpiritModal();
            } catch (error) {
                showMessage('加点失败：' + error.message, 'error');
            }
        }

        // 获取灵根品质颜色
        function getSpiritQualityColor(quality) {
            const colors = {
                '废灵根': '#808080',
                '下品灵根': '#FFFFFF',
                '中品灵根': '#32CD32',
                '上品灵根': '#1E90FF',
                '极品灵根': '#9370DB',
                '变异灵根': '#FFD700'
            };
            return colors[quality] || '#FFFFFF';
        }

        // 显示消息
        function showMessage(message, type = 'info') {
            // 移除已存在的消息
            const existingMessage = document.querySelector('.message');
            if (existingMessage) {
                existingMessage.remove();
            }

            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = message;
            
            document.body.appendChild(messageDiv);
            
            // 3秒后自动消失
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.remove();
                }
            }, 3000);
        }
    </script>
</body>
</html> 