       
        .menu-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 40px;
            border: 2px solid rgba(255, 215, 0, 0.3);
            box-shadow: 
                0 0 30px rgba(255, 215, 0, 0.2),
                inset 0 0 20px rgba(255, 255, 255, 0.1);
            max-width: 648px;
            margin: 50px auto;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        
        /* PWA全屏模式专用样式 - 确保完全填充屏幕 */
        @media (display-mode: standalone) {
            html, body {
                height: 100vh !important;
                height: calc(var(--vh, 1vh) * 100) !important;
                margin: 0 !important;
                padding: 0 !important;
                overflow: hidden !important;
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                bottom: 0 !important;
            }
            
            .menu-container {
                width: 100vw !important;
                height: 100vh !important;
                height: calc(var(--vh, 1vh) * 100) !important;
                min-height: 100vh !important;
                min-height: calc(var(--vh, 1vh) * 100) !important;
                max-height: 100vh !important;
                max-height: calc(var(--vh, 1vh) * 100) !important;
                margin: 0 !important;
                padding: 40px 20px !important;
                border-radius: 0 !important;
                border: none !important;
                background: rgba(255, 255, 255, 0.05) !important;
                overflow-y: auto !important;
                -webkit-overflow-scrolling: touch !important;
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                bottom: 0 !important;
                display: flex !important;
                flex-direction: column !important;
                justify-content: center !important;
                align-items: center !important;
            }
            
            /* 确保子元素不会导致额外的高度 */
            .game-title {
                margin-top: 0 !important;
                margin-bottom: 20px !important;
            }
            
            .game-subtitle {
                margin-bottom: 30px !important;
            }
            
            .menu-buttons {
                gap: 15px !important;
            }
            
            .user-buttons {
                margin-top: 20px !important;
                margin-bottom: 0 !important;
            }
            
            .version-info {
                position: absolute !important;
                bottom: 15px !important;
                right: 15px !important;
            }
        }
        
        /* 小屏设备PWA模式适配 */
        @media (display-mode: standalone) and (max-width: 414px) {
            .menu-container {
                padding: 30px 15px !important;
            }
            
            .game-title {
                font-size: 36px !important;
                margin-bottom: 15px !important;
            }
            
            .game-subtitle {
                font-size: 16px !important;
                margin-bottom: 25px !important;
            }
            
            .menu-button {
                padding: 12px 30px !important;
                font-size: 16px !important;
                min-width: 180px !important;
            }
        }
        
        @media (display-mode: standalone) and (max-width: 375px) {
            .menu-container {
                padding: 25px 12px !important;
            }
            
            .game-title {
                font-size: 32px !important;
            }
            
            .menu-button {
                min-width: 160px !important;
            }
        }
        
        @media (display-mode: standalone) and (max-width: 320px) {
            .menu-container {
                padding: 20px 10px !important;
            }
            
            .game-title {
                font-size: 28px !important;
            }
            
            .game-subtitle {
                font-size: 14px !important;
            }
            
            .menu-button {
                padding: 10px 20px !important;
                font-size: 14px !important;
                min-width: 150px !important;
            }
            
            .user-button {
                padding: 10px 20px !important;
                font-size: 13px !important;
            }
        }
        
        @media (max-width: 768px) {
            .menu-container {
                margin: 20px;
                padding: 30px 20px;
                min-height: calc(var(--vh, 1vh) * 100 - 40px);
                position: relative;
                top: auto;
                left: auto;
                transform: none;
            }
        }
        
        @media (max-width: 375px) {
            .menu-container {
                margin: 10px;
                padding: 25px 15px;
                min-height: calc(var(--vh, 1vh) * 100 - 20px);
            }
        }
        
        .game-title {
            font-size: 48px;
            font-weight: bold;
            color: #ffd700;
            text-shadow: 
                0 0 20px rgba(255, 215, 0, 0.8),
                2px 2px 4px rgba(0, 0, 0, 0.8);
            margin-bottom: 20px;
            animation: title-glow 2s ease-in-out infinite alternate;
        }
        
        @keyframes title-glow {
            from {
                text-shadow: 
                    0 0 20px rgba(255, 215, 0, 0.8),
                    2px 2px 4px rgba(0, 0, 0, 0.8);
            }
            to {
                text-shadow: 
                    0 0 30px rgba(255, 215, 0, 1),
                    0 0 40px rgba(255, 215, 0, 0.6),
                    2px 2px 4px rgba(0, 0, 0, 0.8);
            }
        }
        
        .game-subtitle {
            font-size: 18px;
            color: #ffffff;
            margin-bottom: 40px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        }
        
        .menu-buttons {
            display: flex;
            flex-direction: column;
            gap: 20px;
            align-items: center;
        }
        
        .menu-button {
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            color: #1a1a2e;
            border: none;
            padding: 15px 40px;
            border-radius: 10px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            min-width: 200px;
            box-shadow: 
                0 4px 15px rgba(255, 215, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }
        
        .menu-button:hover {
            transform: translateY(-3px);
            box-shadow: 
                0 6px 20px rgba(255, 215, 0, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }
        
        .menu-button:active {
            transform: translateY(0);
            box-shadow: 
                0 2px 10px rgba(255, 215, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }
        
        .user-buttons {
            display: flex;
            gap: 15px;
            margin-top: 30px;
            justify-content: center;
        }
        
        .user-button {
            background: linear-gradient(135deg, #666, #888);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            box-shadow: 
                0 2px 10px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }
        
        .user-button:hover {
            background: linear-gradient(135deg, #777, #999);
            transform: translateY(-2px);
            box-shadow: 
                0 4px 15px rgba(0, 0, 0, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }
        
        .user-button.primary {
            background: linear-gradient(135deg, #4caf50, #66bb6a);
        }
        
        .user-button.primary:hover {
            background: linear-gradient(135deg, #66bb6a, #81c784);
        }
        
        .version-info {
            position: absolute;
            bottom: 20px;
            right: 20px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
        }
        
        .user-status {
            position: absolute;
            top: 20px;
            right: 20px;
            font-size: 14px;
            color: #ffd700;
            display: none;
        }