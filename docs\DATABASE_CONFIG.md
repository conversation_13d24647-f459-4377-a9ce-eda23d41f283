# 数据库配置说明

## 概述

一念修仙项目使用单一数据库 `yn_game` 存储所有游戏数据，包括用户数据、游戏内容和管理数据。

## 数据库信息

- **数据库名**: `yn_game`
- **字符集**: `utf8mb4`
- **引擎**: `InnoDB`
- **表数量**: 44个

## 配置文件

### 主配置文件
**位置**: `src/config/database.php`

这是项目的核心数据库配置文件，定义了以下常量：
- `DB_HOST`: 数据库主机地址
- `DB_NAME`: 数据库名称
- `DB_USER`: 数据库用户名
- `DB_PASS`: 数据库密码
- `DB_CHARSET`: 字符集

### 使用方式

所有API文件都应该通过以下方式引入数据库配置：

```php
require_once __DIR__ . '/../config/database.php';

// 使用PDO连接
$pdo = getDatabase();

// 或使用mysqli连接
$conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
```

## 数据库结构

### 核心表分类

1. **用户系统** (3个表)
   - `users` - 用户主表
   - `user_server_roles` - 用户服务器角色
   - `login_logs` - 登录日志

2. **角色系统** (4个表)
   - `characters` - 角色主表
   - `character_equipment` - 角色装备
   - `character_growth_logs` - 角色成长日志
   - `character_name_changes` - 角色改名记录

3. **物品系统** (6个表)
   - `game_items` - 游戏物品总表
   - `game_item_sets` - 装备套装
   - `item_skills` - 物品技能
   - `user_inventories` - 用户背包
   - `shop_items` - 商店物品
   - `crafting_recipes` - 制作配方

4. **地图冒险** (6个表)
   - `game_maps` - 游戏地图
   - `map_stages` - 地图关卡
   - `map_drop_configs` - 掉落配置
   - `user_map_progress` - 用户地图进度
   - `battle_records` - 战斗记录
   - `adventure_events` - 奇遇事件

5. **竞技系统** (3个表)
   - `immortal_arena_ranks` - 竞技场段位
   - `immortal_arena_records` - 竞技场记录
   - `immortal_arena_match_pool` - 匹配池

6. **管理系统** (2个表)
   - `admin_users` - 管理员
   - `admin_logs` - 管理日志

7. **其他系统** (20个表)
   - 包括境界、精灵、成就、副本等各种游戏功能表

## 部署说明

### 1. 创建数据库
```sql
CREATE DATABASE yn_game CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 2. 创建用户并授权
```sql
CREATE USER 'ynxx'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON yn_game.* TO 'ynxx'@'localhost';
FLUSH PRIVILEGES;
```

### 3. 修改配置文件
编辑 `src/config/database.php`，更新数据库连接信息：
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'yn_game');
define('DB_USER', 'ynxx');
define('DB_PASS', 'your_password');
define('DB_CHARSET', 'utf8mb4');
```

### 4. 导入数据结构
如果有SQL文件，导入数据库结构和初始数据。

## 注意事项

1. **统一配置**: 所有文件都应使用 `src/config/database.php` 中的配置
2. **字符集**: 确保使用 `utf8mb4` 字符集以支持emoji和特殊字符
3. **权限**: 确保数据库用户有足够的权限进行增删改查操作
4. **备份**: 定期备份数据库，特别是在更新前
5. **安全**: 生产环境中使用强密码，并限制数据库访问权限

## 故障排除

### 常见问题

1. **连接失败**: 检查数据库服务是否启动，用户名密码是否正确
2. **字符集问题**: 确保数据库、表和连接都使用 utf8mb4
3. **权限不足**: 确保数据库用户有足够的操作权限
4. **表不存在**: 确保已正确导入数据库结构

### 调试工具

项目提供了以下调试脚本：
- `check_databases.php` - 检查数据库连接
- `analyze_database.php` - 分析数据库结构
- `table_comparison.php` - 对比表结构

---

**最后更新**: 2025年6月25日
