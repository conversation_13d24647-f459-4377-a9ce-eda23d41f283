/**
 * 技能加载器 - 动态加载和管理技能模块
 * 支持按需加载JavaScript和CSS文件
 */
class SkillLoader {
    constructor() {
        this.loadedSkills = new Map(); // 已加载的技能模块
        this.loadedCSS = new Set(); // 已加载的CSS文件
        this.skillInstances = new Map(); // 🔥 新增：技能实例缓存
        this.battleSystem = null; // 战斗系统引用
        
        // 技能映射表 - 将技能名称映射到对应的类和模块
        this.skillMapping = {
            // 剑类技能 - 分开为独立模块
            '剑气外放！': { module: 'feijian-skill', class: 'FeiJianSkill', css: 'feijian-animations' },
            '万剑诀': { module: 'wanjianjue-skill', class: 'WanJianJueSkill', css: 'wanjianjue-animations' },
            '巨剑术': { module: 'jujian-skill', class: 'JuJianSkill', css: 'jujian-animations' },
            
            // 雷法技能
            '掌心雷': { module: 'lightning-skills', class: 'ZhangXinLeiSkill', css: 'lightning-animations' },
            '雷剑': { module: 'lightning-skills', class: 'LeiJianSkill', css: 'lightning-animations' },
            
            // 火法技能
            '火球术': { module: 'fire-skills', class: 'HuoQiuShuSkill', css: 'fire-animations' },
            '火流星': { module: 'fire-skills', class: 'HuoLiuXingSkill', css: 'huoliuxing-animations' },
            
            // 木系技能  
            '藤蔓': { module: 'wood-skills', class: 'TengManSkill', css: 'wood-animations' },
            '藤蔓缠绕': { module: 'wood-skills', class: 'TengManSkill', css: 'wood-animations' },
            '回春剑': { module: 'huichunjian-skill', class: 'HuiChunJianSkill', css: 'huichunjian-animations' },
            
            // 水系技能
            '水龙卷': { module: 'water-skills', class: 'ShuiLongJuanSkill', css: 'water-animations' },
            
            // 土系技能
            '岩石突刺': { module: 'earth-skills', class: 'YanShiTuCiSkill', css: 'earth-animations' },
            
            // 金系技能
            '金针暴雨': { module: 'metal-skills', class: 'JinZhenBaYuSkill', css: 'metal-animations' },
            
            // 冰系技能
            '冰锥术': { module: 'ice-skills', class: 'BingZhuiShuSkill', css: 'ice-animations' },
            '玄冰剑': { module: 'ice-skills', class: 'XuanBingJianSkill', css: 'ice-animations' },
            
            // 风系技能
            '风刃术': { module: 'wind-skills', class: 'FengRenSuSkill', css: 'wind-animations' },
            
            // 剑类技能 - 游龙剑
            '游龙剑': { module: 'youlong-skill', class: 'YouLongJianSkill', css: 'youlong-animations' },
            
            // 独立技能文件
            '横斩': { module: 'hengzhan-skill', class: 'HengZhanSkill', css: 'hengzhan-animations' },
            
            // 基础攻击技能
            '普通攻击': { module: 'normal-attack-skill', class: 'NormalAttackSkill', css: 'normal-attack-animations' },
        };
    }

    /**
     * 设置战斗系统引用
     * @param {BattleSystem} battleSystem 战斗系统实例
     */
    setBattleSystem(battleSystem) {
        this.battleSystem = battleSystem;
        // 🔥 新增：更新已缓存的技能实例的战斗系统引用
        this.skillInstances.forEach(instance => {
            if (instance && typeof instance.setBattleSystem === 'function') {
                instance.setBattleSystem(battleSystem);
            }
        });
    }

    /**
     * 动态加载JavaScript模块
     * @param {string} moduleName 模块名称
     * @returns {Promise} 加载完成的Promise
     */
    async loadJSModule(moduleName) {
        if (this.loadedSkills.has(moduleName)) {
            return this.loadedSkills.get(moduleName);
        }

        const script = document.createElement('script');
        script.src = `assets/js/battle/skills/${moduleName}.js`;
        
        const loadPromise = new Promise((resolve, reject) => {
            script.onload = () => {
                console.log(`✅ 技能模块加载成功: ${moduleName}`);
                resolve();
            };
            script.onerror = () => {
                console.error(`❌ 技能模块加载失败: ${moduleName}`);
                reject(new Error(`Failed to load skill module: ${moduleName}`));
            };
        });

        document.head.appendChild(script);
        await loadPromise;
        
        this.loadedSkills.set(moduleName, true);
        return true;
    }

    /**
     * 动态加载CSS文件
     * @param {string} cssName CSS文件名称
     * @returns {Promise} 加载完成的Promise
     */
    async loadCSSFile(cssName) {
        if (this.loadedCSS.has(cssName)) {
            return true;
        }

        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = `assets/css/battle/skills/${cssName}.css`;
        
        const loadPromise = new Promise((resolve, reject) => {
            link.onload = () => {
                console.log(`✅ 技能样式加载成功: ${cssName}`);
                resolve();
            };
            link.onerror = () => {
                console.warn(`⚠️ 技能样式加载失败，使用默认样式: ${cssName}`);
                // CSS加载失败不阻塞执行，使用现有的样式
                resolve();
            };
        });

        document.head.appendChild(link);
        await loadPromise;
        
        this.loadedCSS.add(cssName);
        return true;
    }

    /**
     * 预加载基础模块（BaseSkill类和基础动画样式）
     */
    async preloadBaseModules() {
        try {
            // BaseSkill类已在HTML中静态加载，无需动态加载
            // 检查BaseSkill是否可用
            if (typeof BaseSkill === 'undefined') {
                console.error('❌ BaseSkill类未加载！请检查HTML中的base-skill.js引用');
                return;
            }
            
            // 只加载基础动画样式
            await this.loadCSSFile('base-animations');
            
            console.log('✅ 基础技能模块预加载完成');
        } catch (error) {
            console.error('❌ 基础技能模块预加载失败:', error);
        }
    }

    /**
     * 根据技能名称获取技能配置
     * @param {string} skillName 技能名称
     * @returns {Object|null} 技能配置
     */
    getSkillConfig(skillName) {
        // 优先使用统一配置
        if (window.SkillConfig) {
            return window.SkillConfig.getSkillConfig(skillName);
        }
        // 降级使用本地配置
        return this.skillMapping[skillName] || null;
    }

    /**
     * 创建技能实例（带缓存）
     * @param {string} skillName 技能名称
     * @param {boolean} isEnemySkill 是否为怪物技能
     * @returns {BaseSkill|null} 技能实例
     */
    createSkillInstance(skillName, isEnemySkill = false) {
        // 🔥 新增：优先使用缓存的实例（区分玩家和怪物技能）
        const cacheKey = isEnemySkill ? `enemy_${skillName}` : skillName;
        if (this.skillInstances.has(cacheKey)) {
            const cachedInstance = this.skillInstances.get(cacheKey);
            if (cachedInstance) {
                // 🔧 重要修复：确保缓存实例的isEnemySkill属性正确
                cachedInstance.isEnemySkill = isEnemySkill;
                if (window.BattleDebugConfig) {
                    window.BattleDebugConfig.log('skill-loader', `✅ 使用缓存的技能实例: ${skillName} (isEnemy: ${isEnemySkill})`);
                }
                return cachedInstance;
            }
        }

        const config = this.getSkillConfig(skillName);
        if (!config) {
            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.log('skill-loader', `⚠️ 未找到技能配置: ${skillName}`);
            }
            return null;
        }

        try {
            // 根据模块类型获取技能类
            let SkillClass = null;
            
            // 独立的剑类技能模块
            if (config.module === 'feijian-skill') {
                if (window.FeiJianSkills && window.FeiJianSkills[config.class]) {
                    SkillClass = window.FeiJianSkills[config.class];
                } else {
                    console.error(`❌ FeiJianSkills 模块未加载或类不存在: ${config.class}`);
                }
            } else if (config.module === 'wanjianjue-skill') {
                if (window.WanJianJueSkills && window.WanJianJueSkills[config.class]) {
                    SkillClass = window.WanJianJueSkills[config.class];
                } else {
                    console.error(`❌ WanJianJueSkills 模块未加载或类不存在: ${config.class}`);
                }
            } else if (config.module === 'jujian-skill') {
                if (window.JuJianSkills && window.JuJianSkills[config.class]) {
                    SkillClass = window.JuJianSkills[config.class];
                } else {
                    console.error(`❌ JuJianSkills 模块未加载或类不存在: ${config.class}`);
                }
            } else if (config.module === 'sword-skills') {
                // 保留对原有剑类技能模块的兼容性
                if (window.SwordSkills && window.SwordSkills[config.class]) {
                    SkillClass = window.SwordSkills[config.class];
                } else {
                    console.error(`❌ SwordSkills 模块未加载或类不存在: ${config.class}`);
                }
            } else if (config.module === 'lightning-skills') {
                if (window.LightningSkills && window.LightningSkills[config.class]) {
                    SkillClass = window.LightningSkills[config.class];
                } else {
                    console.error(`❌ LightningSkills 模块未加载或类不存在: ${config.class}`);
                }
            } else if (config.module === 'fire-skills') {
                if (window.FireSkills && window.FireSkills[config.class]) {
                    SkillClass = window.FireSkills[config.class];
                } else {
                    console.error(`❌ FireSkills 模块未加载或类不存在: ${config.class}`);
                }
            } else if (config.module === 'wood-skills') {
                if (window.WoodSkills && window.WoodSkills[config.class]) {
                    SkillClass = window.WoodSkills[config.class];
                } else {
                    console.error(`❌ WoodSkills 模块未加载或类不存在: ${config.class}`);
                }
            } else if (config.module === 'water-skills') {
                if (window.WaterSkills && window.WaterSkills[config.class]) {
                    SkillClass = window.WaterSkills[config.class];
                } else {
                    console.error(`❌ WaterSkills 模块未加载或类不存在: ${config.class}`);
                }
            } else if (config.module === 'earth-skills') {
                if (window.EarthSkills && window.EarthSkills[config.class]) {
                    SkillClass = window.EarthSkills[config.class];
                } else {
                    console.error(`❌ EarthSkills 模块未加载或类不存在: ${config.class}`);
                }
            } else if (config.module === 'metal-skills') {
                if (window.MetalSkills && window.MetalSkills[config.class]) {
                    SkillClass = window.MetalSkills[config.class];
                } else {
                    console.error(`❌ MetalSkills 模块未加载或类不存在: ${config.class}`);
                }
            } else if (config.module === 'ice-skills') {
                if (window.IceSkills && window.IceSkills[config.class]) {
                    SkillClass = window.IceSkills[config.class];
                } else {
                    console.error(`❌ IceSkills 模块未加载或类不存在: ${config.class}`);
                }
            } else if (config.module === 'wind-skills') {
                if (window.WindSkills && window.WindSkills[config.class]) {
                    SkillClass = window.WindSkills[config.class];
                } else {
                    console.error(`❌ WindSkills 模块未加载或类不存在: ${config.class}`);
                }
            } else if (config.module === 'youlong-skill') {
                if (window.YouLongJianSkills && window.YouLongJianSkills[config.class]) {
                    SkillClass = window.YouLongJianSkills[config.class];
                } else {
                    console.error(`❌ YouLongJianSkills 模块未加载或类不存在: ${config.class}`);
                }
            } else if (config.module === 'hengzhan-skill') {
                if (window.SwordSkills && window.SwordSkills[config.class]) {
                    SkillClass = window.SwordSkills[config.class];
                } else {
                    console.error(`❌ HengZhanSkill 模块未加载或类不存在: ${config.class}`, {
                        windowSwordSkills: window.SwordSkills,
                        availableClasses: window.SwordSkills ? Object.keys(window.SwordSkills) : 'undefined'
                    });
                }
            } else if (config.module === 'huichunjian-skill') {
                if (window.HuichunjianSkills && window.HuichunjianSkills[config.class]) {
                    SkillClass = window.HuichunjianSkills[config.class];
                } else {
                    console.error(`❌ HuichunjianSkills 模块未加载或类不存在: ${config.class}`, {
                        windowHuichunjianSkills: window.HuichunjianSkills,
                        availableClasses: window.HuichunjianSkills ? Object.keys(window.HuichunjianSkills) : 'undefined'
                    });
                }
            } else if (config.module === 'normal-attack-skill') {
                if (window.NormalAttackSkills && window.NormalAttackSkills[config.class]) {
                    SkillClass = window.NormalAttackSkills[config.class];
                    if (window.BattleDebugConfig) {
                        window.BattleDebugConfig.log('skill-loader', `✅ 找到普通攻击技能类: ${config.class}`);
                    }
                } else {
                    if (window.BattleDebugConfig) {
                        window.BattleDebugConfig.log('skill-loader', `❌ NormalAttackSkills 模块未加载或类不存在: ${config.class}`, {
                            windowNormalAttackSkills: window.NormalAttackSkills,
                            availableClasses: window.NormalAttackSkills ? Object.keys(window.NormalAttackSkills) : 'undefined'
                        });
                    }
                }
            }

            if (!SkillClass) {
                if (window.BattleDebugConfig) {
                    window.BattleDebugConfig.log('skill-loader', `❌ 技能类不存在: ${config.class}，模块: ${config.module}`);
                }
                return null;
            }

            const instance = new SkillClass(this.battleSystem, isEnemySkill);
            
            // 🔧 重要修复：确保新实例的isEnemySkill属性正确设置
            instance.isEnemySkill = isEnemySkill;
            
            // 🔥 新增：为怪物技能使用不同的缓存键，避免冲突
            const cacheKey = isEnemySkill ? `enemy_${skillName}` : skillName;
            this.skillInstances.set(cacheKey, instance);
            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.log('skill-loader', `✅ 创建并缓存技能实例: ${skillName} (isEnemy: ${isEnemySkill})，实例isEnemySkill: ${instance.isEnemySkill}`);
            }
            
            return instance;
        } catch (error) {
            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.log('skill-loader', `❌ 创建技能实例失败: ${skillName}`, error);
            }
            return null;
        }
    }

    /**
     * 执行技能动画
     * @param {string} skillName 技能名称
     * @param {Object} skillData 技能数据
     * @param {string} weaponImage 武器图片
     * @param {Object} effectsContainer 效果容器
     * @param {Object} battleSystem 战斗系统实例
     * @param {boolean} isEnemySkill 是否为敌人技能（新增参数）
     * @returns {Promise} 技能执行完成的Promise
     */
    async executeSkill(skillName, skillData, weaponImage, effectsContainer = null, battleSystem = null, isEnemySkill = null) {
        // 🔧 修复关键问题：如果skillName是animation_model，需要先转换为技能名称
        let actualSkillName = skillName;
        
        // 🔧 新增：优先使用SkillConfig进行动画模型到技能名称的转换
        if (window.SkillConfig) {
            // 🔧 修复：只有在动画模型映射表中确实存在时才转换
            if (window.SkillConfig.animationModelMapping.hasOwnProperty(skillName)) {
                const mappedSkillName = window.SkillConfig.animationModelMapping[skillName];
                actualSkillName = mappedSkillName;
                if (window.BattleDebugConfig) {
                    window.BattleDebugConfig.log('skill-loader', `🔄 动画模型转换: ${skillName} -> ${actualSkillName}`);
                }
            }
        }
        
        const config = this.getSkillConfig(actualSkillName);
        if (!config) {
            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.log('skill-loader', `⚠️ 未知技能，使用默认动画: ${skillName} (实际: ${actualSkillName})`);
            }
            return this.executeDefaultSkill(skillName, skillData, weaponImage);
        }

        try {
            // 并行加载模块和CSS
            await Promise.all([
                this.loadJSModule(config.module),
                this.loadCSSFile(config.css)
            ]);

            // 🔧 新增：等待模块完全加载（给浏览器一点时间执行模块代码）
            await new Promise(resolve => setTimeout(resolve, 50));

            // 🔧 检测是否为怪物技能 - 多重检查确保准确性
            let finalIsEnemySkill = false;
            
            // 优先使用显式传入的参数
            if (isEnemySkill !== null) {
                finalIsEnemySkill = isEnemySkill;
            } else if (skillData && (
                skillData.isEnemy === true || 
                skillData.isEnemySkill === true || 
                skillData.isMonsterSkill === true
            )) {
                finalIsEnemySkill = true;
            }
            
            // 🔧 强制输出技能执行信息
            console.log('🎯 SkillLoader执行技能:', {
                技能名称: skillName,
                是否怪物技能: finalIsEnemySkill,
                传入参数: isEnemySkill,
                技能数据: skillData,
                武器图片: weaponImage
            });
            
            // 🔧 更新战斗系统引用（如果传入了新的实例）
            if (battleSystem) {
                this.setBattleSystem(battleSystem);
            }
            
            // 创建技能实例并执行
            const skillInstance = this.createSkillInstance(actualSkillName, finalIsEnemySkill);
            if (!skillInstance) {
                throw new Error(`无法创建技能实例: ${actualSkillName}`);
            }

            console.log('🎯 技能实例创建成功:', {
                原始名称: skillName,
                实际技能名称: actualSkillName,
                实例类型: skillInstance.constructor.name,
                实例isEnemySkill: skillInstance.isEnemySkill
            });

            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.log('skill-loader', `🎯 执行技能: ${actualSkillName} (原始: ${skillName}) (怪物技能: ${finalIsEnemySkill})`);
                window.BattleDebugConfig.log('skill-loader', `🔧 技能实例类型: ${skillInstance.constructor.name}`);
                window.BattleDebugConfig.log('skill-loader', `🔧 实例的isEnemySkill值: ${skillInstance.isEnemySkill}`);
            }
            
            // 🔧 修复：确保skillData包含正确的skillName用于喊话
            const enhancedSkillData = { 
                ...skillData,
                displayName: skillData?.skillName || skillData?.displayName || (skillName === 'feijian' ? '剑气外放！' : skillName)  // 优先使用真实技能名称，对feijian特殊处理
            };
            
            await skillInstance.execute(enhancedSkillData, weaponImage);
            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.log('skill-loader', `✅ 技能执行完成: ${actualSkillName} (原始: ${skillName})`);
            }

        } catch (error) {
            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.log('skill-loader', `❌ 技能执行失败: ${actualSkillName} (原始: ${skillName})`, error);
            }
            // 回退到默认技能
            await this.executeDefaultSkill(skillName, skillData, weaponImage);
        }
    }

    /**
     * 执行默认技能动画（回退方案）
     * @param {string} skillName 技能名称
     * @param {Object} skillData 技能数据
     * @param {string} weaponImage 武器图片
     * @returns {Promise} 默认技能执行完成的Promise
     */
    async executeDefaultSkill(skillName, skillData, weaponImage) {
        console.log(`🔄 执行默认技能动画: ${skillName}`);
        
        // 使用原有的战斗系统方法作为默认实现
        if (this.battleSystem && typeof this.battleSystem.createSkillEffect === 'function') {
            await this.battleSystem.createSkillEffect(skillName, skillData, weaponImage);
        } else {
            // 简单的默认动画
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    }

    /**
     * 预加载指定技能列表
     * @param {Array} skillNames 技能名称数组
     * @returns {Promise} 预加载完成的Promise
     */
    async preloadSkills(skillNames) {
        const loadPromises = [];
        
        skillNames.forEach(skillName => {
            const config = this.getSkillConfig(skillName);
            if (config) {
                loadPromises.push(
                    Promise.all([
                        this.loadJSModule(config.module),
                        this.loadCSSFile(config.css)
                    ])
                );
            }
        });

        try {
            await Promise.all(loadPromises);
            console.log(`✅ 预加载技能完成: ${skillNames.join(', ')}`);
        } catch (error) {
            console.warn(`⚠️ 部分技能预加载失败:`, error);
        }
    }

    /**
     * 获取已加载的技能列表
     * @returns {Array} 已加载的技能模块名称
     */
    getLoadedSkills() {
        return Array.from(this.loadedSkills.keys());
    }

    /**
     * 获取已加载的CSS列表
     * @returns {Array} 已加载的CSS文件名称
     */
    getLoadedCSS() {
        return Array.from(this.loadedCSS);
    }

    /**
     * 🔥 新增：清理技能实例缓存
     * @param {string} skillName 要清理的技能名称，不传则清理所有
     */
    clearSkillCache(skillName = null) {
        if (skillName) {
            if (this.skillInstances.has(skillName)) {
                const instance = this.skillInstances.get(skillName);
                // 如果实例有清理方法，调用它
                if (instance && typeof instance.cleanup === 'function') {
                    instance.cleanup();
                }
                this.skillInstances.delete(skillName);
                console.log(`✅ 清理技能缓存: ${skillName}`);
            }
        } else {
            // 清理所有缓存
            this.skillInstances.forEach((instance, name) => {
                if (instance && typeof instance.cleanup === 'function') {
                    instance.cleanup();
                }
            });
            this.skillInstances.clear();
            console.log(`✅ 清理所有技能缓存`);
        }
    }

    /**
     * 🔥 新增：获取缓存统计信息
     * @returns {Object} 缓存统计
     */
    getCacheStats() {
        return {
            loadedModules: this.loadedSkills.size,
            loadedCSS: this.loadedCSS.size,
            cachedInstances: this.skillInstances.size,
            moduleList: Array.from(this.loadedSkills.keys()),
            cssList: Array.from(this.loadedCSS),
            instanceList: Array.from(this.skillInstances.keys())
        };
    }
}

// 导出技能加载器
window.SkillLoader = SkillLoader; 