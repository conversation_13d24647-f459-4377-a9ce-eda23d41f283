<?php
// 套装特殊效果修复验证脚本
session_start();
echo "开始验证套装特殊效果修复...\n";

// 数据库连接
$host = 'localhost';
$dbname = 'yinian';
$username = 'root';
$password = 'mjlxz159';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ 数据库连接成功\n";
} catch (PDOException $e) {
    die("❌ 数据库连接失败: " . $e->getMessage() . "\n");
}

// 获取测试用户
$stmt = $pdo->prepare("SELECT u.id as user_id, c.id as character_id FROM users u JOIN characters c ON u.id = c.user_id LIMIT 1");
$stmt->execute();
$testUser = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$testUser) {
    die("❌ 无可用的测试用户\n");
}

$user_id = $testUser['user_id'];
$character_id = $testUser['character_id'];
$_SESSION['user_id'] = $user_id;

echo "✅ 使用测试用户 (用户ID: $user_id, 角色ID: $character_id)\n";

// 1. 检查套装装备
echo "\n=== 1. 套装装备检查 ===\n";
$stmt = $pdo->prepare("
    SELECT gi.name, gi.set_name, gi.set_piece_type 
    FROM character_equipment ce 
    JOIN game_items gi ON ce.item_id = gi.id 
    WHERE ce.character_id = ? AND gi.set_name IS NOT NULL AND gi.set_name != ''
");
$stmt->execute([$character_id]);
$equipment = $stmt->fetchAll(PDO::FETCH_ASSOC);

if ($equipment) {
    echo "✅ 找到 " . count($equipment) . " 件套装装备\n";
    $setSummary = [];
    foreach ($equipment as $item) {
        $setName = $item['set_name'];
        $setSummary[$setName] = ($setSummary[$setName] ?? 0) + 1;
    }
    foreach ($setSummary as $setName => $count) {
        echo "  - $setName: $count 件\n";
    }
} else {
    echo "❌ 未找到套装装备\n";
}

// 2. 测试套装特殊效果函数
echo "\n=== 2. 套装特殊效果函数测试 ===\n";
require_once 'src/includes/functions.php';

$setBonus = getCharacterSetBonus($pdo, $character_id);

if (isset($setBonus['special_effects']) && !empty($setBonus['special_effects'])) {
    echo "✅ 套装特殊效果函数正常，找到 " . count($setBonus['special_effects']) . " 个效果\n";
    foreach ($setBonus['special_effects'] as $effect) {
        echo "  - {$effect['set_name']} ({$effect['pieces']}件套): {$effect['effect']}\n";
    }
} else {
    echo "❌ 套装特殊效果函数未返回数据\n";
}

// 3. 测试cultivation.php API
echo "\n=== 3. cultivation.php API测试 ===\n";
$_POST['action'] = 'get_attributes';
ob_start();
include 'src/api/cultivation.php';
$apiResponse = ob_get_clean();

$apiData = json_decode($apiResponse, true);
if ($apiData && $apiData['success']) {
    if (isset($apiData['attributes']['set_special_effects']) && !empty($apiData['attributes']['set_special_effects'])) {
        echo "✅ cultivation.php API 正确返回套装特殊效果数据\n";
        foreach ($apiData['attributes']['set_special_effects'] as $effect) {
            echo "  - {$effect['set_name']} ({$effect['pieces']}件套): {$effect['effect']}\n";
        }
    } else {
        echo "❌ cultivation.php API 未返回套装特殊效果数据\n";
    }
} else {
    echo "❌ cultivation.php API 调用失败\n";
}

// 4. 测试battle_unified.php API
echo "\n=== 4. battle_unified.php API测试 ===\n";
$_GET['action'] = 'init_battle';
$_GET['map_id'] = '1';
$_GET['map_code'] = 'qingshan';
$_GET['stage_number'] = '1';

ob_start();
include 'src/api/battle_unified.php';
$battleResponse = ob_get_clean();

$battleData = json_decode($battleResponse, true);
if ($battleData && $battleData['success']) {
    if (isset($battleData['data']['player_data']['set_special_effects']) && !empty($battleData['data']['player_data']['set_special_effects'])) {
        echo "✅ battle_unified.php API 正确返回套装特殊效果数据\n";
        foreach ($battleData['data']['player_data']['set_special_effects'] as $effect) {
            echo "  - {$effect['set_name']} ({$effect['pieces']}件套): {$effect['effect']}\n";
        }
    } else {
        echo "❌ battle_unified.php API 未返回套装特殊效果数据\n";
    }
} else {
    echo "❌ battle_unified.php API 调用失败\n";
}

// 5. 检查前端文件修复状态
echo "\n=== 5. 前端文件修复检查 ===\n";
$battleManagerFile = 'public/assets/js/battle/battle-manager.js';
if (file_exists($battleManagerFile)) {
    $content = file_get_contents($battleManagerFile);
    if (strpos($content, 'set_special_effects: attrs.set_special_effects || []') !== false) {
        echo "✅ battle-manager.js 已修复：包含set_special_effects字段\n";
    } else {
        echo "❌ battle-manager.js 未修复：缺少set_special_effects字段\n";
    }
    
    if (strpos($content, '套装特殊效果调试') !== false) {
        echo "✅ battle-manager.js 已添加调试信息\n";
    } else {
        echo "❌ battle-manager.js 缺少调试信息\n";
    }
} else {
    echo "❌ battle-manager.js 文件不存在\n";
}

$battleSystemFile = 'public/assets/js/battle/core/battle-system.js';
if (file_exists($battleSystemFile)) {
    $content = file_get_contents($battleSystemFile);
    if (strpos($content, '🔥 套装效果触发检查') !== false) {
        echo "✅ battle-system.js 已添加调试信息\n";
    } else {
        echo "❌ battle-system.js 缺少调试信息\n";
    }
} else {
    echo "❌ battle-system.js 文件不存在\n";
}

echo "\n=== 修复验证完成 ===\n";
echo "后端数据流测试完成，前端修复状态已检查\n";
?>
