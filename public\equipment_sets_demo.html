<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>装备套装系统 - 一念修仙</title>

    <!-- 🔧 项目配置文件 - 必须早期加载 -->
    <script src="assets/js/config.js"></script>

    <!-- 基础样式 -->
    <link rel="stylesheet" href="assets/css/global.css">
    <link rel="stylesheet" href="assets/css/equipment-sets.css">
    
    <!-- Font Awesome 图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: #fff;
            margin: 0;
            padding: 20px;
            font-family: "Microsoft YaHei", <PERSON><PERSON>, sans-serif;
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            border: 1px solid rgba(212, 177, 116, 0.3);
        }
        
        .demo-title {
            color: #d4b174;
            font-size: 28px;
            margin: 0 0 10px 0;
        }
        
        .demo-subtitle {
            color: #999;
            font-size: 14px;
            margin: 0;
        }
        
        .demo-controls {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .demo-btn {
            background: linear-gradient(45deg, #d4b174, #ffd700);
            color: #000;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: bold;
            font-size: 14px;
            transition: all 0.3s ease;
            min-width: 120px;
        }
        
        .demo-btn:hover {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
        }
        
        .demo-btn:disabled {
            background: #666;
            color: #999;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .demo-status {
            background: rgba(0, 0, 0, 0.4);
            border: 1px solid rgba(212, 177, 116, 0.3);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .status-title {
            color: #d4b174;
            font-size: 16px;
            margin: 0 0 15px 0;
            border-bottom: 1px solid rgba(212, 177, 116, 0.3);
            padding-bottom: 8px;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 4px;
        }
        
        .status-label {
            color: #ccc;
            font-size: 13px;
        }
        
        .status-value {
            color: #fff;
            font-weight: bold;
        }
        
        .status-value.success {
            color: #1eff00;
        }
        
        .status-value.error {
            color: #ff4757;
        }
        
        .loading-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            z-index: 9999;
            justify-content: center;
            align-items: center;
        }
        
        .loading-content {
            background: rgba(20, 20, 30, 0.9);
            border: 2px solid #d4b174;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            color: #fff;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #333;
            border-top: 3px solid #d4b174;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .message-display {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(212, 177, 116, 0.3);
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
            min-height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-style: italic;
        }

        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            .demo-controls {
                flex-direction: column;
                align-items: center;
            }
            
            .demo-btn {
                width: 100%;
                max-width: 280px;
            }
            
            .status-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- 演示头部 -->
    <div class="demo-header">
        <h1 class="demo-title">装备套装系统演示</h1>
        <p class="demo-subtitle">基于实际数据库结构 - 一念修仙</p>
    </div>

    <!-- 演示控制按钮 -->
    <div class="demo-controls">
        <button class="demo-btn" onclick="loadSets()">加载套装状态</button>
        <button class="demo-btn" onclick="loadAllSets()">查看所有套装</button>
        <button class="demo-btn" onclick="calculatePower()">计算套装战力</button>
        <button class="demo-btn" onclick="simulateEffect()">模拟套装效果</button>
        <button class="demo-btn" onclick="checkSystemStatus()">系统状态检查</button>
    </div>

    <!-- 系统状态显示 -->
    <div class="demo-status">
        <h3 class="status-title">系统状态</h3>
        <div class="status-grid">
            <div class="status-item">
                <span class="status-label">API连接</span>
                <span class="status-value" id="api-status">检查中...</span>
            </div>
            <div class="status-item">
                <span class="status-label">套装管理器</span>
                <span class="status-value" id="manager-status">初始化中...</span>
            </div>
            <div class="status-item">
                <span class="status-label">当前套装数</span>
                <span class="status-value" id="sets-count">-</span>
            </div>
            <div class="status-item">
                <span class="status-label">总战力加成</span>
                <span class="status-value" id="total-power">-</span>
            </div>
        </div>
    </div>

    <!-- 消息显示区域 -->
    <div class="message-display" id="message-display">
        点击上方按钮开始测试套装系统功能...
    </div>

    <!-- 套装容器 -->
    <div id="equipment-sets-container"></div>

    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <div>正在处理请求...</div>
        </div>
    </div>

    <!-- JavaScript引用 -->
    <script src="assets/js/equipment-set-manager.js"></script>
    
    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('manager-status', '已加载', 'success');
            checkApiConnection();
        });

        // 显示加载状态
        function showLoading() {
            document.getElementById('loading-overlay').style.display = 'flex';
        }

        // 隐藏加载状态
        function hideLoading() {
            document.getElementById('loading-overlay').style.display = 'none';
        }

        // 更新状态显示
        function updateStatus(id, text, type = '') {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = text;
                element.className = 'status-value' + (type ? ' ' + type : '');
            }
        }

        // 显示消息
        function showMessage(message, type = 'info') {
            const messageDiv = document.getElementById('message-display');
            messageDiv.innerHTML = `<span class="${type}">${message}</span>`;
            
            // 样式
            messageDiv.style.color = type === 'error' ? '#ff4757' : 
                                   type === 'success' ? '#1eff00' : '#ccc';
        }

        // 检查API连接
        async function checkApiConnection() {
            try {
                const response = await fetch(window.GameConfig ? window.GameConfig.getApiUrl('equipment_set_system.php?action=get_all_sets') : '../src/api/equipment_set_system.php?action=get_all_sets');
                const data = await response.json();
                
                if (data.success) {
                    updateStatus('api-status', '连接正常', 'success');
                    updateStatus('sets-count', data.total_count || 0);
                } else {
                    updateStatus('api-status', '连接异常', 'error');
                }
            } catch (error) {
                updateStatus('api-status', '连接失败', 'error');
                console.error('API连接检查失败:', error);
            }
        }

        // 加载套装状态
        async function loadSets() {
            showLoading();
            showMessage('正在加载套装状态...');
            
            try {
                await window.equipmentSetManager.loadCharacterSets();
                
                if (window.equipmentSetManager.currentSets) {
                    const count = window.equipmentSetManager.currentSets.length;
                    updateStatus('sets-count', count);
                    showMessage(`成功加载 ${count} 个套装`, 'success');
                } else {
                    showMessage('未找到激活的套装', 'info');
                }
            } catch (error) {
                showMessage('加载套装状态失败: ' + error.message, 'error');
                console.error('加载套装失败:', error);
            } finally {
                hideLoading();
            }
        }

        // 查看所有套装
        async function loadAllSets() {
            showLoading();
            showMessage('正在获取所有套装列表...');
            
            try {
                const data = await window.equipmentSetManager.loadAllSets();
                
                showMessage(`找到 ${data.total_count} 个可用套装`, 'success');
                console.log('所有套装:', data.sets);
                
                // 显示套装列表
                const setsInfo = data.sets.map(set => 
                    `${set.set_name} (${set.rarity}, 境界${set.realm_requirement})`
                ).join(', ');
                
                if (setsInfo) {
                    showMessage(`套装列表: ${setsInfo}`, 'info');
                }
            } catch (error) {
                showMessage('获取套装列表失败: ' + error.message, 'error');
            } finally {
                hideLoading();
            }
        }

        // 计算套装战力
        async function calculatePower() {
            showLoading();
            showMessage('正在计算套装战力...');
            
            try {
                const data = await window.equipmentSetManager.calculateSetPower();
                
                updateStatus('total-power', data.total_set_power.toLocaleString());
                showMessage(`套装总战力: ${data.total_set_power.toLocaleString()}`, 'success');
                
                console.log('战力详情:', data.breakdown);
            } catch (error) {
                showMessage('计算套装战力失败: ' + error.message, 'error');
            } finally {
                hideLoading();
            }
        }

        // 模拟套装效果
        async function simulateEffect() {
            showLoading();
            showMessage('正在模拟套装效果...');
            
            try {
                // 模拟一个套装的6件套效果
                const data = await window.equipmentSetManager.simulateSetEffect(77, 6);
                
                showMessage(`模拟结果: ${data.set_name} 6件套战力贡献 ${data.power_contribution}`, 'success');
                console.log('模拟效果详情:', data);
            } catch (error) {
                showMessage('模拟套装效果失败: ' + error.message, 'error');
            } finally {
                hideLoading();
            }
        }

        // 系统状态检查
        async function checkSystemStatus() {
            showLoading();
            showMessage('正在检查系统状态...');
            
            try {
                // 检查各个组件状态
                const checks = [
                    { name: 'API连接', check: checkApiConnection },
                    { name: '套装管理器', check: () => !!window.equipmentSetManager },
                    { name: '容器元素', check: () => !!document.getElementById('equipment-sets-container') }
                ];
                
                let allGood = true;
                const results = [];
                
                for (const check of checks) {
                    try {
                        if (typeof check.check === 'function') {
                            await check.check();
                        }
                        results.push(`${check.name}: ✓`);
                    } catch (error) {
                        results.push(`${check.name}: ✗`);
                        allGood = false;
                    }
                }
                
                if (allGood) {
                    showMessage('系统状态检查完成，所有组件正常', 'success');
                } else {
                    showMessage('系统状态检查发现问题: ' + results.join(', '), 'error');
                }
                
                console.log('系统检查结果:', results);
            } catch (error) {
                showMessage('系统状态检查失败: ' + error.message, 'error');
            } finally {
                hideLoading();
            }
        }

        // 全局错误处理
        window.addEventListener('error', function(event) {
            console.error('页面错误:', event.error);
            showMessage('页面发生错误，请查看控制台', 'error');
        });

        // 初始化提示
        showMessage('套装系统演示页面已加载，点击按钮开始测试各项功能', 'info');
    </script>
</body>
</html> 