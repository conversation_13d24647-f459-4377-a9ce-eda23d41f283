/**
 * 风系技能动画样式 - 风刃术
 * 对应 animation_model = 'fengrensu'
 */

/* 动画容器 */
.fengrensu-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1000;
}

/* === 蓄力阶段动画 === */

/* 风系魔法阵 */
.fengrensu-magic-circle {
    position: absolute;
    width: 100px;
    height: 100px;
    transform: translate(-50%, -50%);
    border: 3px solid rgba(144, 238, 144, 0.9);
    border-radius: 50%;
    background: radial-gradient(circle, rgba(144, 238, 144, 0.3) 0%, rgba(173, 255, 173, 0.1) 50%, transparent 100%);
    animation: fengrensu-magic-circle 0.6s ease-out;
    box-shadow: 0 0 30px rgba(144, 238, 144, 0.7);
}

@keyframes fengrensu-magic-circle {
    0% {
        transform: translate(-50%, -50%) scale(0) rotate(0deg);
        opacity: 0;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.2) rotate(180deg);
        opacity: 0.9;
    }
    100% {
        transform: translate(-50%, -50%) scale(1) rotate(360deg);
        opacity: 1;
    }
}

/* 内圈风旋符文 */
.fengrensu-inner-runes {
    position: absolute;
    width: 100px;
    height: 100px;
    transform: translate(-50%, -50%);
    border: 3px solid rgba(173, 255, 173, 0.9);
    border-radius: 50%;
    background: conic-gradient(from 0deg, 
        rgba(144, 238, 144, 0.7), 
        rgba(173, 255, 173, 0.5), 
        rgba(152, 251, 152, 0.3), 
        rgba(173, 255, 173, 0.5), 
        rgba(144, 238, 144, 0.7));
    animation: fengrensu-inner-runes 1.0s ease-out infinite;
}

@keyframes fengrensu-inner-runes {
    0%, 100% {
        transform: translate(-50%, -50%) scale(0.9) rotate(0deg);
        opacity: 0.8;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.2) rotate(180deg);
        opacity: 0.5;
    }
}

/* 外圈气流符文 */
.fengrensu-outer-runes {
    position: absolute;
    width: 160px;
    height: 160px;
    transform: translate(-50%, -50%);
    border: 2px solid rgba(152, 251, 152, 0.7);
    border-radius: 50%;
    background: conic-gradient(from 180deg, 
        rgba(173, 255, 173, 0.4), 
        rgba(152, 251, 152, 0.2), 
        rgba(240, 255, 240, 0.1), 
        rgba(152, 251, 152, 0.2), 
        rgba(173, 255, 173, 0.4));
    animation: fengrensu-outer-runes 1.8s ease-out infinite;
}

@keyframes fengrensu-outer-runes {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1) rotate(0deg);
        opacity: 0.7;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.3) rotate(-180deg);
        opacity: 0.4;
    }
}

/* 武器图片旋转 */
.fengrensu-weapon-sprite {
    position: absolute;
    width: 40px;
    height: 40px;
    transform: translate(-50%, -50%);
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    animation: fengrensu-weapon-rotate 0.6s linear;
    -webkit-filter: drop-shadow(0 0 10px rgba(144, 238, 144, 0.8));
    filter: drop-shadow(0 0 10px rgba(144, 238, 144, 0.8));
}

@keyframes fengrensu-weapon-rotate {
    0% { transform: translate(-50%, -50%) rotate(0deg) scale(1); }
    50% { transform: translate(-50%, -50%) rotate(180deg) scale(1.2); }
    100% { transform: translate(-50%, -50%) rotate(360deg) scale(1); }
}

/* 蓄力能量核心 */
.fengrensu-energy-core {
    position: absolute;
    width: 30px;
    height: 30px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, rgba(144, 238, 144, 0.8) 0%, rgba(173, 255, 173, 0.5) 50%, transparent 100%);
    border-radius: 50%;
    animation: fengrensu-energy-pulse 0.6s ease-in-out;
    box-shadow: 0 0 25px rgba(144, 238, 144, 0.8);
}

@keyframes fengrensu-energy-pulse {
    0% {
        transform: translate(-50%, -50%) scale(0.5);
        box-shadow: 0 0 20px rgba(144, 238, 144, 0.6);
    }
    100% {
        transform: translate(-50%, -50%) scale(1.3);
        box-shadow: 0 0 30px rgba(144, 238, 144, 1);
    }
}

/* 风元素粒子汇聚效果 */
.fengrensu-charge-wind {
    position: absolute;
    width: 5px;
    height: 5px;
    transform: translate(-50%, -50%);
    background: linear-gradient(45deg, rgba(144, 238, 144, 0.9), rgba(173, 255, 173, 0.7));
    border-radius: 50%;
    animation: fengrensu-wind-gather 0.6s ease-out forwards;
    box-shadow: 0 0 5px rgba(144, 238, 144, 0.6);
}

@keyframes fengrensu-wind-gather {
    0% {
        transform: translate(calc(-50% + var(--chargeX)), calc(-50% + var(--chargeY))) scale(0);
        opacity: 0;
    }
    50% {
        transform: translate(calc(-50% + var(--chargeX) * 0.5), calc(-50% + var(--chargeY) * 0.5)) scale(1.2);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(0.2);
        opacity: 0;
    }
}

/* 环绕气流螺旋 */
.fengrensu-charge-spiral {
    position: absolute;
    width: 8px;
    height: 8px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, rgba(173, 255, 173, 0.7), rgba(152, 251, 152, 0.3));
    border-radius: 50%;
    animation: fengrensu-spiral-move 0.6s ease-out forwards;
}

@keyframes fengrensu-spiral-move {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0.8;
    }
    100% {
        transform: translate(-50%, -50%) scale(0.1);
        opacity: 0;
    }
}

/* 风压波纹 */
.fengrensu-energy-ripple {
    position: absolute;
    width: 20px;
    height: 20px;
    transform: translate(-50%, -50%);
    border: 2px solid rgba(144, 238, 144, 0.6);
    border-radius: 50%;
    animation: fengrensu-ripple-expand 0.6s ease-out forwards;
}

@keyframes fengrensu-ripple-expand {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0.8;
        border-width: 2px;
    }
    100% {
        transform: translate(-50%, -50%) scale(8);
        opacity: 0;
        border-width: 1px;
    }
}

/* 气流弥漫效果 */
.fengrensu-airflow {
    position: absolute;
    width: 10px;
    height: 10px;
    transform: translate(-50%, -50%);
    background: rgba(152, 251, 152, 0.6);
    border-radius: 50%;
    animation: fengrensu-airflow-rise 1.4s ease-out forwards;
}

@keyframes fengrensu-airflow-rise {
    0% {
        transform: translate(-50%, -50%) scale(0.5);
        opacity: 0.6;
    }
    50% {
        transform: translate(-50%, calc(-50% - 25px)) scale(2);
        opacity: 0.4;
    }
    100% {
        transform: translate(-50%, calc(-50% - 50px)) scale(3.5);
        opacity: 0;
    }
}

/* === 发射阶段动画 === */

/* 预发射气流爆发 */
.fengrensu-pre-burst {
    position: absolute;
    width: 60px;
    height: 60px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, rgba(144, 238, 144, 0.6) 0%, rgba(173, 255, 173, 0.3) 50%, transparent 100%);
    border-radius: 50%;
    animation: fengrensu-pre-burst 0.1s ease-out;
    box-shadow: 0 0 40px rgba(144, 238, 144, 0.8);
}

@keyframes fengrensu-pre-burst {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0;
    }
    100% {
        transform: translate(-50%, -50%) scale(2);
        opacity: 0;
    }
}

/* 主风刃 - 使用图片的横向快速穿透 */
.fengrensu-main-blade {
    position: absolute;
    width: 100px;
    height: 100px;
    transform: translate(-50%, -50%) rotate(calc(var(--bladeAngle) + 90deg));
    background-image: url('../../../images/eff/2090_Loop.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    animation: fengrensu-main-blade-fly 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    -webkit-filter: drop-shadow(0 0 15px rgba(144, 238, 144, 0.9));
    filter: drop-shadow(0 0 15px rgba(144, 238, 144, 0.9)) 
            drop-shadow(0 0 30px rgba(173, 255, 173, 0.6));
}

@keyframes fengrensu-main-blade-fly {
    0% {
        transform: translate(-50%, -50%) rotate(calc(var(--bladeAngle) + 90deg)) scale(0.5);
        opacity: 0;
    }
    15% {
        transform: translate(-50%, -50%) rotate(calc(var(--bladeAngle) + 90deg)) scale(1.4);
        opacity: 1;
    }
    75% {
        transform: translate(calc(-50% + var(--hitX)), calc(-50% + var(--hitY))) rotate(calc(var(--bladeAngle) + 90deg)) scale(1.2);
        opacity: 1;
    }
    100% {
        transform: translate(calc(-50% + var(--targetX)), calc(-50% + var(--targetY))) rotate(calc(var(--bladeAngle) + 90deg)) scale(0.6);
        opacity: 0.3;
    }
}

/* 副风刃 - 使用图片的慢速跟随 */
.fengrensu-sub-blade {
    position: absolute;
    width: 80px;
    height: 80px;
    transform: translate(-50%, -50%) rotate(calc(var(--bladeAngle) + 90deg));
    background-image: url('../../../images/eff/2090_Loop.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    animation: fengrensu-sub-blade-fly 1.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    -webkit-filter: drop-shadow(0 0 10px rgba(152, 251, 152, 0.7)) brightness(0.8);
    filter: drop-shadow(0 0 10px rgba(152, 251, 152, 0.7)) brightness(0.8);
}

@keyframes fengrensu-sub-blade-fly {
    0% {
        transform: translate(-50%, -50%) rotate(calc(var(--bladeAngle) + 90deg)) scale(0.3);
        opacity: 0;
    }
    25% {
        transform: translate(-50%, -50%) rotate(calc(var(--bladeAngle) + 90deg)) scale(1.2);
        opacity: 0.8;
    }
    90% {
        transform: translate(calc(-50% + var(--targetX)), calc(-50% + var(--targetY))) rotate(calc(var(--bladeAngle) + 90deg)) scale(1.0);
        opacity: 0.6;
    }
    100% {
        transform: translate(calc(-50% + var(--targetX)), calc(-50% + var(--targetY))) rotate(calc(var(--bladeAngle) + 90deg)) scale(0.4);
        opacity: 0.2;
    }
}

/* 风刃拖尾效果 - 使用图片的拖尾 */
.fengrensu-blade-trail {
    position: absolute;
    width: calc(60px - var(--trailIndex) * 8px);
    height: calc(60px - var(--trailIndex) * 8px);
    transform: translate(-50%, -50%) rotate(calc(var(--bladeAngle) + 90deg));
    background-image: url('../../../images/eff/2090_Loop.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    animation: fengrensu-trail-fly 1.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    -webkit-filter: drop-shadow(0 0 8px rgba(173, 255, 173, calc(0.5 - var(--trailIndex) * 0.08)));
    filter: drop-shadow(0 0 8px rgba(173, 255, 173, calc(0.5 - var(--trailIndex) * 0.08))) 
            brightness(calc(0.7 - var(--trailIndex) * 0.1)) 
            blur(calc(1px + var(--trailIndex) * 0.5px));
}

@keyframes fengrensu-trail-fly {
    0% {
        transform: translate(-50%, -50%) rotate(calc(var(--bladeAngle) + 90deg)) scale(0);
        opacity: 0;
    }
    30% {
        transform: translate(-50%, -50%) rotate(calc(var(--bladeAngle) + 90deg)) scale(1.1);
        opacity: calc(0.8 - var(--trailIndex) * 0.1);
    }
    95% {
        transform: translate(calc(-50% + var(--targetX)), calc(-50% + var(--targetY))) rotate(calc(var(--bladeAngle) + 90deg)) scale(0.8);
        opacity: calc(0.4 - var(--trailIndex) * 0.05);
    }
    100% {
        transform: translate(calc(-50% + var(--targetX)), calc(-50% + var(--targetY))) rotate(calc(var(--bladeAngle) + 90deg)) scale(0.2);
        opacity: 0;
    }
}

/* 气流切割线 */
.fengrensu-cut-line {
    position: absolute;
    width: calc(100px + var(--lineIndex) * 20px);
    height: 2px;
    transform: translate(-50%, -50%) rotate(calc(var(--bladeAngle) + var(--lineIndex) * 5deg - 7.5deg));
    background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(144, 238, 144, calc(0.5 - var(--lineIndex) * 0.1)) 20%, 
        rgba(173, 255, 173, calc(0.7 - var(--lineIndex) * 0.1)) 50%, 
        rgba(144, 238, 144, calc(0.5 - var(--lineIndex) * 0.1)) 80%, 
        transparent 100%);
    animation: fengrensu-cut-line-fly 1.5s ease-out forwards;
    -webkit-filter: blur(0.5px);
    filter: blur(0.5px);
}

@keyframes fengrensu-cut-line-fly {
    0% {
        transform: translate(-50%, -50%) rotate(calc(var(--bladeAngle) + var(--lineIndex) * 5deg - 7.5deg)) scaleX(0);
        opacity: 0;
    }
    25% {
        transform: translate(-50%, -50%) rotate(calc(var(--bladeAngle) + var(--lineIndex) * 5deg - 7.5deg)) scaleX(1);
        opacity: calc(0.7 - var(--lineIndex) * 0.1);
    }
    95% {
        transform: translate(calc(-50% + var(--targetX)), calc(-50% + var(--targetY))) rotate(calc(var(--bladeAngle) + var(--lineIndex) * 5deg - 7.5deg)) scaleX(0.8);
        opacity: calc(0.3 - var(--lineIndex) * 0.05);
    }
    100% {
        transform: translate(calc(-50% + var(--targetX)), calc(-50% + var(--targetY))) rotate(calc(var(--bladeAngle) + var(--lineIndex) * 5deg - 7.5deg)) scaleX(0.5);
        opacity: 0;
    }
}

/* === 穿透效果动画 === */

/* 穿透闪光 */
.fengrensu-penetrate-flash {
    position: absolute;
    width: 100px;
    height: 100px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, rgba(144, 238, 144, 0.9) 0%, rgba(173, 255, 173, 0.6) 30%, transparent 70%);
    border-radius: 50%;
    animation: fengrensu-penetrate-flash 0.3s ease-out;
    box-shadow: 0 0 50px rgba(144, 238, 144, 0.8);
}

@keyframes fengrensu-penetrate-flash {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(3);
        opacity: 0;
    }
}

/* 切割痕迹 */
.fengrensu-cut-mark {
    position: absolute;
    width: 80px;
    height: 4px;
    transform: translate(-50%, -50%) rotate(var(--markAngle));
    background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(144, 238, 144, 0.8) 20%, 
        rgba(173, 255, 173, 1) 50%, 
        rgba(144, 238, 144, 0.8) 80%, 
        transparent 100%);
    animation: fengrensu-cut-mark 0.5s ease-out;
    -webkit-filter: blur(0.5px);
    filter: blur(0.5px);
}

@keyframes fengrensu-cut-mark {
    0% {
        transform: translate(-50%, -50%) rotate(var(--markAngle)) scaleX(0);
        opacity: 0;
    }
    30% {
        transform: translate(-50%, -50%) rotate(var(--markAngle)) scaleX(1.2);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) rotate(var(--markAngle)) scaleX(0.8);
        opacity: 0;
    }
}

/* 简化风压扩散 */
.fengrensu-wind-burst-simple {
    position: absolute;
    width: 8px;
    height: 8px;
    transform: translate(-50%, -50%) rotate(var(--burstAngle));
    background: radial-gradient(circle, rgba(173, 255, 173, 0.7), rgba(152, 251, 152, 0.3));
    border-radius: 50%;
    animation: fengrensu-wind-burst-simple 0.4s ease-out forwards;
}

@keyframes fengrensu-wind-burst-simple {
    0% {
        transform: translate(-50%, -50%) rotate(var(--burstAngle)) scale(0);
        opacity: 0.7;
    }
    100% {
        transform: translate(calc(-50% + cos(var(--burstAngle) * 3.14159 / 180) * var(--burstDistance)), calc(-50% + sin(var(--burstAngle) * 3.14159 / 180) * var(--burstDistance))) rotate(var(--burstAngle)) scale(2);
        opacity: 0;
    }
}

/* 敌人受击动画 */
@keyframes wind-cut {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-3px) rotate(-1deg); }
    75% { transform: translateX(3px) rotate(1deg); }
}

@keyframes wind-shake {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-2px); }
}

/* 移动端适配 */
@media (max-width: 768px) {
    .fengrensu-container {
        transform: scale(0.8);
    }
    
    .fengrensu-main-blade {
        width: 80px;
        height: 80px;
    }
    
    .fengrensu-sub-blade {
        width: 64px;
        height: 64px;
    }
    
    .fengrensu-penetrate-flash {
        width: 80px;
        height: 80px;
    }
} 