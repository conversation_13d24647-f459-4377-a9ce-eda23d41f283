// 核心战斗系统
// 注意：此文件依赖于其他模块，需要在HTML中正确引入

class BattleSystem {
    constructor() {
        // 🔧 修复：不在构造函数中立即初始化，避免重复加载
        this.isInitialized = false;
        this.isInitializing = false;

        // 初始化基本属性
        this.skillNames = {
            feijian: '剑气外放！',
            wanjianjue: '万剑诀！',
            zhangxinlei: '掌心雷！',
            huoqiushu: '火球术！',
            jujian: '巨剑术！',
        };

        // 🔧 新增：敌人技能名称
        this.enemySkillNames = [
            '妖爪撕裂！',
            '魔焰冲击！',
            '毒牙突刺！',
            '暗影斩击！',
            '雷霆一击！',
            '寒冰刺骨！',
            '烈火焚身！',
            '狂风呼啸！',
        ];

        // 🔧 修复：安全地查找DOM元素，避免null错误
        this.battleContainer = document.querySelector('.battle-container');
        this.effectsContainer = document.querySelector('.effects-container');
        this.attackCount = 0;
        this.isGameOver = false;

        // 🔧 新增：战斗时间追踪
        this.battleStartTime = null;
        this.battleEndTime = null;

        // 🎯 新增：从localStorage恢复挂机状态
        this.isAutoBattleMode = localStorage.getItem('autoBattleMode') === 'true';
        this.autoBattleCountdown = null;

        console.log('🤖 挂机状态恢复:', this.isAutoBattleMode ? '开启' : '关闭');

        // 🔧 修复：只有在battleContainer存在时才创建effectsContainer
        if (!this.effectsContainer && this.battleContainer) {
            this.effectsContainer = document.createElement('div');
            this.effectsContainer.className = 'effects-container';
            this.battleContainer.appendChild(this.effectsContainer);
        }

        console.log('BattleSystem构造完成，等待手动初始化');
        console.log('DOM元素状态:', {
            battleContainer: !!this.battleContainer,
            effectsContainer: !!this.effectsContainer,
        });
    }

    // 🔧 新增：手动初始化方法，避免重复初始化
    async initialize() {
        if (this.isInitialized) {
            console.log('战斗系统已初始化，跳过重复初始化');
            return true;
        }

        if (this.isInitializing) {
            console.log('战斗系统正在初始化中，等待完成...');
            // 🔧 修复：等待初始化完成而不是直接返回false
            return new Promise(resolve => {
                const checkInterval = setInterval(() => {
                    if (!this.isInitializing) {
                        clearInterval(checkInterval);
                        resolve(this.isInitialized);
                    }
                }, 100);
            });
        }

        this.isInitializing = true;

        try {
            const success = await this.initializeBattle();
            this.isInitialized = success;
            return success;
        } finally {
            this.isInitializing = false;
        }
    }

    async initializeBattle() {
        // 显示加载状态
        this.updateBattleStatus('正在加载战斗数据...');

        // 🔧 修复：检查是否已有数据管理器实例，避免重复创建
        if (!this.dataManager) {
            this.dataManager = new BattleDataManager();
        }

        const success = await this.dataManager.initializeBattleData();

        if (!success) {
            this.updateBattleStatus('加载战斗数据失败，使用默认配置');
        }

        // 更新区域信息显示
        this.updateAreaInfo();

        // 🔧 新增：设置战斗背景图片
        this.dataManager.setBattleBackground();

        // 获取最终玩家属性
        this.playerStats = this.dataManager.calculateFinalStats();

        // 🔥 修复：确保playerStats包含完整的MP数据
        const initialMp = this.playerStats.mp || this.playerStats.max_mp || 100;
        this.playerStats.max_mp = initialMp;
        this.playerStats.currentMp = initialMp;
        console.log('🔋 玩家MP初始化:', {
            max_mp: this.playerStats.max_mp,
            currentMp: this.playerStats.currentMp,
            originalMp: this.playerStats.mp,
        });

        // 先获取技能序列（在更新武器显示之前）
        this.skillSequence = this.dataManager.getBattleSkillSequence();

        console.log('=== 完整玩家数据 ===', this.playerStats);

        // 创建角色，正确传递头像数据
        this.player = new Character(document.querySelector('.player'), {
            name: this.playerStats.name || '修仙者',
            level: this.playerStats.level,
            max_hp: this.playerStats.max_hp,
            max_mp: this.playerStats.max_mp,
            currentMp: this.playerStats.currentMp,
            attack: this.playerStats.physical_attack,
            defense: this.playerStats.physical_defense,
            avatar: this.playerStats.character_avatar || this.playerStats.avatar_image,
        });

        // 🔥 修复：确保玩家角色对象的MP值与playerStats同步
        this.player.max_mp = this.playerStats.max_mp;
        this.player.currentMp = this.playerStats.currentMp;
        this.player.updateUI();

        // 🔧 修复：检查敌人数据是否存在
        if (!this.dataManager.enemyData) {
            console.error('❌ 敌人数据为空，无法初始化战斗');
            throw new Error('敌人数据加载失败');
        }

        // 敌人可能有avatar属性，确保传递
        const enemyData = {
            ...this.dataManager.enemyData,
            // 🔧 修复：正确处理敌人头像路径
            avatar:
                this.dataManager.enemyData.avatarImage ||
                this.dataManager.enemyData.modelImage ||
                this.dataManager.enemyData.avatar ||
                this.dataManager.enemyData.monster_avatar,
            // 🆕 新增：确保type属性正确传递
            type: this.dataManager.enemyData.type || 'normal',
        };

        this.enemy = new Character(document.querySelector('.enemy'), enemyData);

        // 现在更新武器显示（skillSequence和skillNames都已经初始化）
        this.updateWeaponDisplay();

        console.log('战斗配置:', {
            playerStats: this.playerStats,
            enemyData: this.dataManager.enemyData,
            weaponSlots: this.dataManager.weaponSlots,
            skillSequence: this.skillSequence,
        });

        // 🔧 新增：详细的敌人技能调试信息
        console.log('=== 敌人技能数据调试 ===');
        console.log('敌人原始数据:', this.dataManager.enemyData);
        console.log('敌人技能列表:', this.dataManager.enemyData?.skills);
        console.log('技能列表类型:', typeof this.dataManager.enemyData?.skills);
        console.log('技能数量:', this.dataManager.enemyData?.skills?.length || 0);
        if (
            this.dataManager.enemyData?.skills &&
            Array.isArray(this.dataManager.enemyData.skills)
        ) {
            this.dataManager.enemyData.skills.forEach((skill, index) => {
                console.log(`  技能${index + 1}: "${skill}" (类型: ${typeof skill})`);
            });
        }

        console.log('=== 详细战斗数据 ===');
        console.log('玩家基础攻击力:', this.playerStats.physical_attack);
        console.log(
            '玩家头像:',
            this.playerStats.character_avatar || this.playerStats.avatar_image
        );
        console.log('敌人生命值:', this.enemy.max_hp);
        console.log('敌人防御力:', this.enemy.defense);
        console.log('敌人头像:', enemyData.avatar);
        console.log('武器槽位详情:');
        this.skillSequence.forEach((skill, index) => {
            console.log(`  槽位${index + 1}:`, {
                hasWeapon: skill.hasWeapon,
                weaponName: skill.weaponName,
                weaponAttack: skill.weaponAttack,
                skillName: skill.skillName,
                damageMultiplier: skill.damageMultiplier,
            });
        });

        // 🔧 新增：应用战斗开始时的套装辅助效果
        this.applyBattleStartSetEffects();

        return true;
    }

    // 核心战斗方法
    async performSkillDamageCalculation(currentSkillData) {
        // 🔧 修复：移除重复的武器攻击计算，因为playerStats已包含所有装备加成
        // 根据武器类型选择正确的攻击力
        let baseDamage;
        if (currentSkillData.weaponType === 'fan') {
            baseDamage = this.playerStats.immortal_attack || 15;
        } else {
            baseDamage = this.playerStats.physical_attack || 20;
        }

        // 应用技能倍率
        let skillDamage = baseDamage * (currentSkillData.damageMultiplier || 1);

        // 🔧 新增：应用套装攻击特殊效果
        const attackEffectResult = this.applySetAttackEffects(skillDamage);
        skillDamage = attackEffectResult.damage;

        // 计算最终伤害（考虑防御）
        let finalDamage = Math.max(1, Math.floor(skillDamage * (1 - this.enemy.defense / 1000)));

        // 🔧 新增：应用破甲效果
        if (attackEffectResult.ignoreDefense) {
            finalDamage = Math.max(1, Math.floor(skillDamage)); // 无视防御
        }

        // 🔧 新增：应用敌人的防御套装特殊效果
        const enemyDefenseEffectResult = this.applyEnemySetDefenseEffects(finalDamage);
        finalDamage = enemyDefenseEffectResult.damage;

        // 保存敌人防御效果结果，供后续处理反击使用
        this.lastEnemyDefenseEffectResult = enemyDefenseEffectResult;

        console.log('伤害计算:', {
            baseDamage,
            weaponType: currentSkillData.weaponType,
            damageMultiplier: currentSkillData.damageMultiplier,
            enemyDefense: this.enemy.defense,
            finalDamage,
            setEffectsApplied: this.playerStats.set_special_effects?.length || 0,
            attackEffects: attackEffectResult,
        });

        return finalDamage;
    }

    // 🔧 新增：套装攻击特殊效果处理
    applySetAttackEffects(baseDamage) {
        if (
            !this.playerStats.set_special_effects ||
            this.playerStats.set_special_effects.length === 0
        ) {
            return { damage: baseDamage, ignoreDefense: false, extraAttacks: 0 };
        }

        let modifiedDamage = baseDamage;
        let ignoreDefense = false;
        let extraAttacks = 0;
        const triggeredEffects = [];

        for (const setEffect of this.playerStats.set_special_effects) {
            const effect = setEffect.effect;

            // 🗡️ 攻击类效果处理

            // 暴击伤害 / 必杀
            if (
                effect.includes('200%伤害') ||
                effect.includes('250%伤害') ||
                effect.includes('300%伤害') ||
                effect.includes('350%伤害') ||
                effect.includes('400%伤害')
            ) {
                const chance = this.extractChance(effect) || 15;
                if (Math.random() * 100 < chance) {
                    const multiplierMatch = effect.match(/(\d+)%伤害/);
                    if (multiplierMatch) {
                        const multiplier = parseInt(multiplierMatch[1]) / 100;
                        modifiedDamage *= multiplier;
                        triggeredEffects.push(
                            `${setEffect.set_name}: 暴击伤害${multiplierMatch[1]}%`
                        );
                        this.showSetEffectMessage(`${setEffect.set_name}套装效果：暴击伤害！`);
                    }
                }
            }

            // 连击
            if (effect.includes('连续攻击') || effect.includes('连击')) {
                const chance = this.extractChance(effect) || 10;
                if (Math.random() * 100 < chance) {
                    extraAttacks = 1; // 额外攻击1次
                    triggeredEffects.push(`${setEffect.set_name}: 连击`);
                    this.showSetEffectMessage(`${setEffect.set_name}套装效果：连击！`);
                }
            }

            // 破甲 / 无视防御
            if (
                effect.includes('无视') &&
                (effect.includes('防御') ||
                    effect.includes('仙术防御') ||
                    effect.includes('物理防御'))
            ) {
                const chance = this.extractChance(effect) || 20;
                if (Math.random() * 100 < chance) {
                    ignoreDefense = true;
                    triggeredEffects.push(`${setEffect.set_name}: 破甲`);
                    this.showSetEffectMessage(`${setEffect.set_name}套装效果：破甲！`);
                }
            }

            // 吸血
            if (effect.includes('转化为生命值') || effect.includes('吸血')) {
                const percentMatch = effect.match(/(\d+)%/);
                if (percentMatch) {
                    const healPercent = parseInt(percentMatch[1]) / 100;
                    const healAmount = Math.floor(modifiedDamage * healPercent);
                    this.player.currentHp = Math.min(
                        this.player.maxHp,
                        this.player.currentHp + healAmount
                    );
                    triggeredEffects.push(`${setEffect.set_name}: 吸血${healAmount}`);
                    this.showSetEffectMessage(`${setEffect.set_name}套装效果：吸血${healAmount}！`);
                    this.updatePlayerHpDisplay();
                }
            }
        }

        if (triggeredEffects.length > 0) {
            console.log('🔥 套装攻击效果触发:', triggeredEffects);
        }

        return {
            damage: modifiedDamage,
            ignoreDefense: ignoreDefense,
            extraAttacks: extraAttacks,
            triggeredEffects: triggeredEffects,
        };
    }

    // 🔧 新增：套装防御特殊效果处理
    applySetDefenseEffects(incomingDamage) {
        if (
            !this.playerStats.set_special_effects ||
            this.playerStats.set_special_effects.length === 0
        ) {
            return { damage: incomingDamage, counterDamage: 0 };
        }

        let modifiedDamage = incomingDamage;
        let counterDamage = 0;
        const triggeredEffects = [];

        for (const setEffect of this.playerStats.set_special_effects) {
            const effect = setEffect.effect;

            // 🛡️ 防御类效果处理

            // 格挡 / 减少伤害
            if (effect.includes('减少') && effect.includes('伤害')) {
                const chance = this.extractChance(effect) || 25;
                if (Math.random() * 100 < chance) {
                    const percentMatch = effect.match(/减少(\d+)%伤害/);
                    if (percentMatch) {
                        const reduction = parseInt(percentMatch[1]) / 100;
                        modifiedDamage = Math.floor(modifiedDamage * (1 - reduction));
                        triggeredEffects.push(`${setEffect.set_name}: 格挡减伤${percentMatch[1]}%`);
                        this.showSetEffectMessage(`${setEffect.set_name}套装效果：格挡！`);
                    }
                }
            }

            // 反击
            if (effect.includes('反弹') && effect.includes('伤害')) {
                const chance = this.extractChance(effect) || 20;
                if (Math.random() * 100 < chance) {
                    const percentMatch = effect.match(/反弹(\d+)%伤害/);
                    if (percentMatch) {
                        const reflectPercent = parseInt(percentMatch[1]) / 100;
                        counterDamage += Math.floor(incomingDamage * reflectPercent);
                        triggeredEffects.push(`${setEffect.set_name}: 反击${counterDamage}`);
                        this.showSetEffectMessage(`${setEffect.set_name}套装效果：反击！`);
                    }
                }
            }

            // 坚韧 / 免疫伤害
            if (effect.includes('免疫本次伤害')) {
                const chance = this.extractChance(effect) || 15;
                if (Math.random() * 100 < chance) {
                    modifiedDamage = 0;
                    triggeredEffects.push(`${setEffect.set_name}: 免疫伤害`);
                    this.showSetEffectMessage(`${setEffect.set_name}套装效果：免疫！`);
                }
            }

            // 荆棘 / 固定反伤
            if (effect.includes('固定') && effect.includes('点伤害')) {
                const damageMatch = effect.match(/固定(\d+)点伤害/);
                if (damageMatch) {
                    counterDamage += parseInt(damageMatch[1]);
                    triggeredEffects.push(`${setEffect.set_name}: 荆棘${damageMatch[1]}`);
                    this.showSetEffectMessage(`${setEffect.set_name}套装效果：荆棘！`);
                }
            }

            // 低血量减伤
            if (effect.includes('生命值低于') && effect.includes('受到伤害减少')) {
                const hpThresholdMatch = effect.match(/生命值低于(\d+)%/);
                const reductionMatch = effect.match(/减少(\d+)%/);
                if (hpThresholdMatch && reductionMatch) {
                    const threshold = parseInt(hpThresholdMatch[1]) / 100;
                    const currentHpPercent = this.player.currentHp / this.player.maxHp;
                    if (currentHpPercent < threshold) {
                        const reduction = parseInt(reductionMatch[1]) / 100;
                        modifiedDamage = Math.floor(modifiedDamage * (1 - reduction));
                        triggeredEffects.push(
                            `${setEffect.set_name}: 低血减伤${reductionMatch[1]}%`
                        );
                        this.showSetEffectMessage(`${setEffect.set_name}套装效果：不屈！`);
                    }
                }
            }
        }

        if (triggeredEffects.length > 0) {
            console.log('🛡️ 套装防御效果触发:', triggeredEffects);
        }

        return {
            damage: Math.max(0, modifiedDamage),
            counterDamage: counterDamage,
            triggeredEffects: triggeredEffects,
        };
    }

    // 🔧 新增：提取效果概率的辅助函数
    extractChance(effectText) {
        const chanceMatch = effectText.match(/有(\d+)%概率/);
        return chanceMatch ? parseInt(chanceMatch[1]) : null;
    }

    // 🔧 新增：显示套装特殊效果消息
    showSetEffectMessage(message) {
        // 在战斗日志中显示特殊效果消息
        const logElement = document.querySelector('.battle-log');
        if (logElement) {
            const effectMessage = document.createElement('div');
            effectMessage.className = 'battle-log-entry set-effect';
            effectMessage.style.color = '#ff6b35';
            effectMessage.style.fontWeight = 'bold';
            effectMessage.textContent = `✨ ${message}`;
            logElement.appendChild(effectMessage);
            logElement.scrollTop = logElement.scrollHeight;
        }

        // 在控制台输出
        console.log(`✨ 套装效果: ${message}`);
    }

    // 🔧 新增：更新玩家血量显示
    updatePlayerHpDisplay() {
        const hpElement = document.querySelector('.player-hp');
        if (hpElement) {
            hpElement.textContent = `${this.player.currentHp}/${this.player.maxHp}`;
        }

        const hpBarElement = document.querySelector('.player-hp-bar');
        if (hpBarElement) {
            const hpPercent = (this.player.currentHp / this.player.maxHp) * 100;
            hpBarElement.style.width = `${hpPercent}%`;
        }
    }

    // 🔧 新增：战斗开始时的套装辅助效果处理
    applyBattleStartSetEffects() {
        if (
            !this.playerStats.set_special_effects ||
            this.playerStats.set_special_effects.length === 0
        ) {
            return;
        }

        for (const setEffect of this.playerStats.set_special_effects) {
            const effect = setEffect.effect;

            // ✨ 辅助类效果处理

            // 护盾效果
            if (effect.includes('战斗开始时获得') && effect.includes('护盾')) {
                const percentMatch = effect.match(/(\d+)%的护盾/);
                if (percentMatch) {
                    const shieldPercent = parseInt(percentMatch[1]) / 100;
                    const shieldAmount = Math.floor(this.player.maxHp * shieldPercent);

                    // 为玩家添加护盾
                    this.player.shield = (this.player.shield || 0) + shieldAmount;
                    this.showSetEffectMessage(
                        `${setEffect.set_name}套装效果：获得${shieldAmount}点护盾！`
                    );
                    console.log(`🛡️ 护盾效果: +${shieldAmount} (总护盾: ${this.player.shield})`);
                }
            }

            // 狂暴效果 - 低血量时攻击力提升
            if (effect.includes('生命值低于') && effect.includes('攻击力提升')) {
                // 这个效果在战斗中动态检查，这里只是记录
                console.log(`⚡ 狂暴效果已激活: ${effect}`);
            }
        }
    }

    // 🔧 新增：每回合结束时的套装辅助效果处理
    applyTurnEndSetEffects() {
        if (
            !this.playerStats.set_special_effects ||
            this.playerStats.set_special_effects.length === 0
        ) {
            return;
        }

        for (const setEffect of this.playerStats.set_special_effects) {
            const effect = setEffect.effect;

            // 回复效果
            if (effect.includes('每回合结束时恢复') && effect.includes('生命值')) {
                const percentMatch = effect.match(/(\d+)%/);
                if (percentMatch) {
                    const healPercent = parseInt(percentMatch[1]) / 100;
                    const healAmount = Math.floor(this.player.maxHp * healPercent);
                    this.player.currentHp = Math.min(
                        this.player.maxHp,
                        this.player.currentHp + healAmount
                    );
                    this.showSetEffectMessage(
                        `${setEffect.set_name}套装效果：回复${healAmount}生命值！`
                    );
                    this.updatePlayerHpDisplay();
                    console.log(`💚 回复效果: +${healAmount}HP`);
                }
            }
        }
    }

    async enemyCounter() {
        if (this.isGameOver) return;

        // 随机选择敌人技能名称
        const randomSkillName =
            this.enemySkillNames[Math.floor(Math.random() * this.enemySkillNames.length)];

        // 显示技能喊话
        await this.showSkillShout(randomSkillName, true);

        // 计算敌人基础伤害
        let enemyDamage = Math.max(
            1,
            Math.floor(this.enemy.attack * (1 - this.playerStats.physical_defense / 1000))
        );

        // 🔧 新增：应用敌人的攻击套装特殊效果
        const enemyAttackEffectResult = this.applyEnemySetAttackEffects(enemyDamage);
        enemyDamage = enemyAttackEffectResult.damage;

        // 🔧 新增：应用玩家的防御套装特殊效果
        const defenseEffectResult = this.applySetDefenseEffects(enemyDamage);
        enemyDamage = defenseEffectResult.damage;

        // 应用伤害
        this.player.takeDamage(enemyDamage);

        // 🔧 新增：处理反击效果
        if (defenseEffectResult.counterDamage > 0) {
            this.enemy.takeDamage(defenseEffectResult.counterDamage);
            this.showSetEffectMessage(`反击造成${defenseEffectResult.counterDamage}点伤害！`);
        }

        // 🔧 新增：处理敌人攻击特殊效果（如连击）
        if (enemyAttackEffectResult.extraAttacks > 0) {
            for (let i = 0; i < enemyAttackEffectResult.extraAttacks; i++) {
                const extraDamage = Math.floor(enemyDamage * 0.5); // 连击伤害减半
                this.player.takeDamage(extraDamage);
                this.showSetEffectMessage(`敌人连击造成${extraDamage}点额外伤害！`);
            }
        }

        // 检查玩家是否死亡
        if (this.player.currentHp <= 0) {
            await this.gameOver('战斗失败！');
            return;
        }
    }

    async gameOver(message) {
        this.isGameOver = true;
        this.battleEndTime = Date.now();

        // 🏆 新增：设置胜负状态标识，便于竞技场结算获取
        this.isPlayerVictory =
            message.includes('玩家胜利') ||
            message.includes('论道胜利') ||
            message.includes('胜利');
        this.battleEnded = true;

        console.log('🔍 战斗结束状态设置:', {
            message: message,
            isPlayerVictory: this.isPlayerVictory,
            battleEnded: this.battleEnded,
        });

        // 停止自动战斗
        if (this.isAutoBattleMode) {
            this.stopAutoBattle();
        }

        // 显示战斗结果
        this.updateBattleStatus(message);

        // 记录战斗时间
        const battleDuration = (this.battleEndTime - this.battleStartTime) / 1000;
        console.log(`战斗结束，持续时间: ${battleDuration}秒`);

        return battleDuration;
    }

    // 战斗状态更新方法
    updateBattleStatus(message) {
        const statusElement = document.querySelector('.battle-status');
        if (statusElement) {
            statusElement.textContent = message;
        }
    }

    // 安全的数值转换方法
    safeParseInt(value) {
        const parsed = parseInt(value);
        return isNaN(parsed) ? 0 : parsed;
    }

    safeParseFloat(value) {
        const parsed = parseFloat(value);
        return isNaN(parsed) ? 0 : parsed;
    }

    // 品质倍率计算
    getRarityMultiplier(rarity) {
        const rarityMultipliers = {
            普通: 1,
            稀有: 1.5,
            史诗: 2,
            传说: 2.5,
            神话: 3,
        };
        return rarityMultipliers[rarity] || 1;
    }

    // 战斗结果处理
    async handleBattleEnd(isVictory, droppedItems = []) {
        // 停止自动战斗
        if (this.isAutoBattleMode) {
            this.stopAutoBattle();
        }

        // 计算战斗时间
        const battleDuration = await this.gameOver(isVictory ? '战斗胜利！' : '战斗失败！');

        // 处理战斗结果
        if (isVictory) {
            // 处理掉落物品
            if (droppedItems && droppedItems.length > 0) {
                await this.showVictoryPanel('战斗胜利！', droppedItems);
            } else {
                await this.showSimpleVictoryPanel('战斗胜利！', []);
            }
        }

        return {
            isVictory,
            battleDuration,
            droppedItems,
        };
    }

    // 自动战斗相关方法
    async startAutoBattle() {
        if (this.isGameOver) return;

        this.isAutoBattleMode = true;
        localStorage.setItem('autoBattleMode', 'true');

        // 开始自动战斗循环
        await this.autoBattle();
    }

    stopAutoBattle() {
        this.isAutoBattleMode = false;
        localStorage.setItem('autoBattleMode', 'false');

        if (this.autoBattleCountdown) {
            clearInterval(this.autoBattleCountdown);
            this.autoBattleCountdown = null;
        }
    }

    async autoBattle() {
        if (!this.isAutoBattleMode || this.isGameOver) return;

        try {
            // 检查是否有可用的技能
            if (!this.skillSequence || this.skillSequence.length === 0) {
                console.error('没有可用的技能');
                return;
            }

            // 随机选择一个技能
            const randomIndex = Math.floor(Math.random() * this.skillSequence.length);
            const currentSkillData = this.skillSequence[randomIndex];

            // 显示技能名称
            if (currentSkillData.skillName) {
                await this.showSkillShout(
                    this.skillNames[currentSkillData.skillName] || currentSkillData.skillName
                );
            }

            // 计算伤害
            const damage = await this.performSkillDamageCalculation(currentSkillData);

            // 应用伤害
            this.enemy.takeDamage(damage);

            // 🔧 新增：处理敌人的反击效果（从performSkillDamageCalculation获取）
            const enemyDefenseEffectResult = this.lastEnemyDefenseEffectResult;
            if (enemyDefenseEffectResult && enemyDefenseEffectResult.counterDamage > 0) {
                this.player.takeDamage(enemyDefenseEffectResult.counterDamage);
                this.showSetEffectMessage(
                    `敌人反击造成${enemyDefenseEffectResult.counterDamage}点伤害！`
                );
                this.updatePlayerHpDisplay();
            }

            // 检查敌人是否死亡
            if (this.enemy.currentHp <= 0) {
                await this.handleBattleEnd(true);
                return;
            }

            // 检查玩家是否因反击死亡
            if (this.player.currentHp <= 0) {
                await this.gameOver('战斗失败！');
                return;
            }

            // 敌人反击
            await this.enemyCounter();

            // 如果战斗未结束且自动战斗模式仍然开启，继续下一轮
            if (!this.isGameOver && this.isAutoBattleMode) {
                setTimeout(() => this.autoBattle(), 2000);
            }
        } catch (error) {
            console.error('自动战斗出错:', error);
            this.stopAutoBattle();
        }
    }

    // 技能显示相关方法
    async showSkillShout(skillName, isEnemy = false) {
        const shoutElement = document.createElement('div');
        shoutElement.className = `skill-shout ${isEnemy ? 'enemy-shout' : 'player-shout'}`;
        shoutElement.textContent = skillName;

        const container = isEnemy
            ? document.querySelector('.enemy-container')
            : document.querySelector('.player-container');

        if (container) {
            container.appendChild(shoutElement);

            // 动画结束后移除元素
            setTimeout(() => {
                if (shoutElement.parentNode) {
                    shoutElement.parentNode.removeChild(shoutElement);
                }
            }, 1000);

            // 等待动画完成
            await new Promise(resolve => setTimeout(resolve, 500));
        }
    }

    updateCurrentSkill(skillName) {
        const skillDisplay = document.querySelector('.current-skill');
        if (skillDisplay) {
            skillDisplay.textContent = `当前技能: ${skillName}`;
        }
    }

    // 区域信息更新
    updateAreaInfo() {
        const areaNameElement = document.querySelector('.area-name');
        const stageLevelElement = document.querySelector('.stage-level');

        if (areaNameElement && this.dataManager.areaData) {
            areaNameElement.textContent = this.dataManager.areaData.areaName || '未知区域';
        }

        if (stageLevelElement && this.dataManager.areaData) {
            stageLevelElement.textContent = `第${this.dataManager.areaData.stageLevel || 1}关`;
        }
    }

    // 武器显示更新
    updateWeaponDisplay() {
        const weaponContainer = document.querySelector('.weapon-slots');
        if (!weaponContainer) return;
        weaponContainer.innerHTML = '';
        this.skillSequence.forEach((skill, index) => {
            const slot = document.createElement('div');
            slot.className = 'weapon-slot' + (skill.hasWeapon ? ' has-weapon' : '');
            if (skill.hasWeapon) {
                const weaponInfo = document.createElement('div');
                weaponInfo.className = 'weapon-info';
                let durabilityText = '';
                if (
                    skill.durability === null ||
                    skill.maxDurability === null ||
                    skill.durability === undefined ||
                    skill.maxDurability === undefined
                ) {
                    durabilityText = '<span style="color:#f44336">异常</span>';
                } else {
                    durabilityText = `${skill.durability}/${skill.maxDurability}`;
                }
                weaponInfo.innerHTML = `
                    <div class="weapon-name">${skill.weaponName || '未知武器'}</div>
                    <div class="weapon-attack">攻击力: ${skill.weaponAttack || 0}</div>
                    <div class="weapon-durability">耐久: ${durabilityText}</div>
                `;
                slot.appendChild(weaponInfo);
                // === 新增：损坏武器样式 ===
                if (
                    skill.durability === null ||
                    skill.maxDurability === null ||
                    skill.durability === undefined ||
                    skill.maxDurability === undefined ||
                    skill.durability <= 0 ||
                    skill.maxDurability <= 0
                ) {
                    slot.classList.add('broken-weapon');
                }
            }
            weaponContainer.appendChild(slot);
        });
    }

    // 🔧 新增：敌人套装攻击特殊效果处理
    applyEnemySetAttackEffects(baseDamage) {
        if (!this.enemy.set_special_effects || this.enemy.set_special_effects.length === 0) {
            return { damage: baseDamage, ignoreDefense: false, extraAttacks: 0 };
        }

        let modifiedDamage = baseDamage;
        let ignoreDefense = false;
        let extraAttacks = 0;
        const triggeredEffects = [];

        for (const setEffect of this.enemy.set_special_effects) {
            const effect = setEffect.effect;

            // 🗡️ 攻击类效果处理

            // 暴击伤害 / 必杀
            if (effect.includes('暴击伤害') || effect.includes('必杀')) {
                const percentMatch = effect.match(/(\d+)%/);
                if (percentMatch && Math.random() < 0.15) {
                    // 15%触发概率
                    const multiplier = parseInt(percentMatch[1]) / 100;
                    modifiedDamage = Math.floor(modifiedDamage * (1 + multiplier));
                    triggeredEffects.push(`敌人${setEffect.set_name}暴击！`);
                }
            }

            // 连击
            if (effect.includes('连击') && Math.random() < 0.12) {
                // 12%触发概率
                extraAttacks = 1;
                triggeredEffects.push(`敌人${setEffect.set_name}连击！`);
            }

            // 破甲 / 无视防御
            if ((effect.includes('破甲') || effect.includes('无视防御')) && Math.random() < 0.1) {
                // 10%触发概率
                ignoreDefense = true;
                triggeredEffects.push(`敌人${setEffect.set_name}破甲！`);
            }

            // 吸血
            if (effect.includes('吸血') && Math.random() < 0.15) {
                // 15%触发概率
                const percentMatch = effect.match(/(\d+)%/);
                if (percentMatch) {
                    const healPercent = parseInt(percentMatch[1]) / 100;
                    const healAmount = Math.floor(modifiedDamage * healPercent);
                    this.enemy.currentHp = Math.min(
                        this.enemy.max_hp,
                        this.enemy.currentHp + healAmount
                    );
                    triggeredEffects.push(
                        `敌人${setEffect.set_name}吸血回复${healAmount}点生命值！`
                    );
                }
            }
        }

        // 显示触发的效果
        triggeredEffects.forEach(effect => this.showSetEffectMessage(effect));

        // 如果破甲，重新计算伤害（无视防御）
        if (ignoreDefense) {
            modifiedDamage = Math.max(1, Math.floor(this.enemy.attack));
        }

        console.log('🔥 敌人套装攻击效果:', {
            baseDamage,
            modifiedDamage,
            ignoreDefense,
            extraAttacks,
            setEffectsApplied: this.enemy.set_special_effects?.length || 0,
            triggeredEffects,
        });

        return { damage: modifiedDamage, ignoreDefense, extraAttacks };
    }

    // 🔧 新增：敌人套装防御特殊效果处理
    applyEnemySetDefenseEffects(incomingDamage) {
        if (!this.enemy.set_special_effects || this.enemy.set_special_effects.length === 0) {
            return { damage: incomingDamage, counterDamage: 0 };
        }

        let modifiedDamage = incomingDamage;
        let counterDamage = 0;
        const triggeredEffects = [];

        for (const setEffect of this.enemy.set_special_effects) {
            const effect = setEffect.effect;

            // 🛡️ 防御类效果处理

            // 格挡 / 减少伤害
            if (effect.includes('格挡') || (effect.includes('减少') && effect.includes('伤害'))) {
                const percentMatch = effect.match(/(\d+)%/);
                if (percentMatch && Math.random() < 0.2) {
                    // 20%触发概率
                    const reduction = parseInt(percentMatch[1]) / 100;
                    modifiedDamage = Math.floor(modifiedDamage * (1 - reduction));
                    triggeredEffects.push(`敌人${setEffect.set_name}格挡！`);
                }
            }

            // 反击
            if (effect.includes('反击') && Math.random() < 0.15) {
                // 15%触发概率
                const percentMatch = effect.match(/(\d+)%/);
                if (percentMatch) {
                    const counterPercent = parseInt(percentMatch[1]) / 100;
                    counterDamage = Math.floor(incomingDamage * counterPercent);
                } else {
                    counterDamage = Math.floor(this.enemy.attack * 0.5); // 默认50%攻击力反击
                }
                triggeredEffects.push(`敌人${setEffect.set_name}反击！`);
            }

            // 免疫 / 无效化
            if (effect.includes('免疫') && Math.random() < 0.08) {
                // 8%触发概率
                modifiedDamage = 0;
                triggeredEffects.push(`敌人${setEffect.set_name}免疫伤害！`);
            }

            // 荆棘 / 伤害反弹
            if (effect.includes('荆棘') || effect.includes('反弹')) {
                const percentMatch = effect.match(/(\d+)%/);
                if (percentMatch && Math.random() < 0.25) {
                    // 25%触发概率
                    const reflectPercent = parseInt(percentMatch[1]) / 100;
                    const reflectDamage = Math.floor(incomingDamage * reflectPercent);
                    counterDamage += reflectDamage;
                    triggeredEffects.push(
                        `敌人${setEffect.set_name}荆棘反弹${reflectDamage}点伤害！`
                    );
                }
            }
        }

        // 显示触发的效果
        triggeredEffects.forEach(effect => this.showSetEffectMessage(effect));

        console.log('🛡️ 敌人套装防御效果:', {
            incomingDamage,
            modifiedDamage,
            counterDamage,
            setEffectsApplied: this.enemy.set_special_effects?.length || 0,
            triggeredEffects,
        });

        return { damage: modifiedDamage, counterDamage };
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BattleSystem;
}
