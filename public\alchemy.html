<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <!-- 移动端适配核心meta标签 -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover">
    
    <!-- 🔧 PWA模式底部白边强力修复 - 必须最早执行 -->
    <script src="assets/js/pwa-fix.js"></script>
    
    <!-- 🎛️ 全局调试开关 - 必须最早加载 -->
    <script src="assets/js/global-debug-switch.js"></script>

    <!-- 🔧 项目配置文件 - 必须早期加载 -->
    <script src="assets/js/config.js"></script>

    <!-- WebView优化配置 -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="screen-orientation" content="portrait">
    <meta name="x5-orientation" content="portrait">
    <meta name="full-screen" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta name="browsermode" content="application">
    <meta name="x5-page-mode" content="app">
    <!-- 禁用长按菜单 -->
    <meta name="format-detection" content="telephone=no,email=no,address=no">
    <!-- 强制使用最新版本 -->
    <meta http-equiv="Cache-Control" content="no-siteapp">
    <meta http-equiv="Cache-Control" content="no-transform">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <!-- 启用硬件加速 -->
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <!-- iOS图标 -->
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/images/app-icon-180.png">
    <link rel="apple-touch-icon" sizes="167x167" href="/assets/images/app-icon-167.png">
    <link rel="apple-touch-icon" sizes="152x152" href="/assets/images/app-icon-152.png">
    <link rel="apple-touch-icon" sizes="120x120" href="/assets/images/app-icon-120.png">
    <!-- PWA支持 -->
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#d4af37">

    <title>一念修仙 - 炼丹系统</title>
    <!-- 🔧 新增：引入全局样式文件 -->
    <link rel="stylesheet" href="assets/css/global.css">
    <link rel="stylesheet" href="assets/css/alchemy.css">
    <!-- 引入通用导航样式 -->
    <link rel="stylesheet" href="assets/css/common-navigation.css">
    <!-- 🔧 视口高度修复脚本 -->
    <script src="assets/js/viewport-height-fix.js"></script>
    <!-- 🔑 全局登录检查系统 -->
    <script src="assets/js/auth-check.js"></script>

    <!-- 🎵 全局音乐管理器 -->
    <script src="assets/js/global-music-manager.js"></script>

</head>
<body>
    <div class="main-container">
        <!-- 顶部标题栏 -->
        <div class="header">
            <div class="header-title">
                <span>⚗️</span>
                炼丹系统
            </div>
            <a href="game.html" class="back-btn">
                <span>🔙</span>
                返回
            </a>
        </div>

        <!-- 炼丹界面主体 -->
        <div class="alchemy-container">
            <!-- 丹方选择区域 -->
            <div class="recipe-section">
                <div class="section-title">选择丹方</div>
                <div class="recipe-tabs">
                    <div class="recipe-tab active" data-type="all">全部</div>
                    <div class="recipe-tab" data-type="渡劫丹">渡劫</div>
                    <div class="recipe-tab" data-type="养魂丹">养魂</div>
                    <div class="recipe-tab" data-type="属性丹">属性</div>
                </div>
                <div class="recipe-list" id="recipeList">
                    <!-- 丹方列表由JavaScript生成 -->
                </div>
            </div>

            <!-- 底部炼制按钮 -->
            <div class="craft-button-container">
                <button class="craft-button" id="craftButton" onclick="selectFurnaceForCrafting()">
                    选择丹炉
                </button>
            </div>
        </div>
    </div>

    <!-- 丹炉选择弹窗 -->
    <div class="furnace-modal" id="furnaceModal">
        <div class="furnace-modal-content">
            <div class="furnace-modal-header">
                <div>🔥 选择丹炉</div>
                <div style="font-size: 12px; color: #bdc3c7; margin-top: 5px;" id="recipeInfo">
                    请选择用于炼制的丹炉
                </div>
            </div>
            
            <div class="furnace-grid" id="modalFurnaceGrid">
                <!-- 丹炉列表由JavaScript生成 -->
            </div>
            
            <!-- 炼制详情 -->
            <div class="crafting-details" id="craftingDetails" style="display: none;">
                <div class="detail-row">
                    <span>成功率:</span>
                    <span id="modalSuccessRate">--</span>
                </div>
                <div class="detail-row">
                    <span>炼制时间:</span>
                    <span id="modalCraftTime">--</span>
                </div>
                <div class="detail-row">
                    <span>消耗需求:</span>
                    <span id="modalStaminaCost">--</span>
                </div>
                <div class="detail-row">
                    <span>材料需求:</span>
                    <span id="modalMaterials">--</span>
                </div>
            </div>
            
            <div class="furnace-modal-buttons">
                <button class="modal-button cancel" onclick="closeFurnaceModal()">取消</button>
                <button class="modal-button" id="confirmCraftButton" onclick="confirmCrafting()" disabled>开始炼制</button>
            </div>
        </div>
    </div>

    <!-- 炼制遮罩层 -->
    <div class="crafting-overlay" id="craftingOverlay"></div>
    
    <!-- 炼制背景图片 -->
    <div class="crafting-background" id="craftingBackground"></div>
    
    <!-- 炼制进度弹窗 -->
    <div class="crafting-progress" id="craftingProgress">
        <div class="progress-text" style="margin-bottom: 10px;">炼制中...</div>
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>
        <div class="progress-text" id="progressText">准备中...</div>
    </div>

    <!-- 底部导航栏 -->
    <script src="assets/js/common-navigation.js"></script>
    
    <script>
        // 炼丹系统数据
        let alchemyData = {
            userFurnaces: [],
            materials: [],
            recipes: [],
            userCharacter: null
        };

        let selectedFurnace = null;
        let selectedRecipe = null;
        let currentTab = 'all';
        let isCrafting = false;

        // 页面加载
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 [炼丹] 炼丹系统界面加载...');
            
            // 浏览器兼容性检查
            try {
                // 测试模板字符串支持
                eval('`test`');
                // 测试箭头函数支持
                eval('() => {}');
                // 测试async/await支持
                eval('async function test() { await Promise.resolve(); }');
            } catch (e) {
                console.warn('⚠️ [炼丹] 浏览器可能不支持ES6语法，使用兼容模式');
                showMessage('浏览器版本较老，建议使用Chrome/Firefox最新版本', 'info');
            }
            
            loadAlchemyData().then(function() {
                initializeInterface();
                console.log('✅ [炼丹] 炼丹系统加载完成');
            }).catch(function(error) {
                console.error('❌ [炼丹] 加载失败:', error);
                showMessage('加载炼丹系统失败，请刷新页面', 'error');
            });
        });

        // 加载炼丹系统数据
        function loadAlchemyData() {
            console.log('🧪 [炼丹] 开始加载炼丹数据...');
            
            return new Promise(function(resolve, reject) {
                Promise.all([
                    // 加载丹方数据
                    fetch(window.GameConfig ? window.GameConfig.getApiUrl('alchemy_system.php?action=get_recipes') : '../src/api/alchemy_system.php?action=get_recipes').then(function(response) {
                        return response.json();
                    }),
                    // 加载角色数据
                    fetch(window.GameConfig ? window.GameConfig.getApiUrl('alchemy_system.php?action=get_character') : '../src/api/alchemy_system.php?action=get_character').then(function(response) {
                        return response.json();
                    })
                ]).then(function(results) {
                    var recipeData = results[0];
                    var characterData = results[1];

                    if (recipeData.success) {
                        alchemyData.recipes = recipeData.data;
                        console.log('✅ [炼丹] 丹方数据加载成功:', alchemyData.recipes.length, '个');
                    } else {
                        throw new Error('加载丹方数据失败: ' + recipeData.message);
                    }

                    if (characterData.success) {
                        alchemyData.userCharacter = characterData.data;
                        console.log('✅ [炼丹] 角色数据加载成功');
                    } else {
                        console.warn('⚠️ [炼丹] 角色数据加载失败:', characterData.message);
                        // 角色数据失败不阻止系统运行
                    }
                    
                    resolve();
                }).catch(function(error) {
                    console.error('❌ [炼丹] 数据加载失败:', error);
                    reject(error);
                });
            });
        }

        // 初始化界面
        function initializeInterface() {
            renderRecipes();
            setupTabs();
            updateCraftingInfo();
        }

        // 渲染丹方列表
        function renderRecipes() {
            const recipeList = document.getElementById('recipeList');
            recipeList.innerHTML = '';

            let filteredRecipes = alchemyData.recipes;
            
            if (currentTab !== 'all') {
                filteredRecipes = alchemyData.recipes.filter(recipe => {
                    if (currentTab === '属性丹') {
                        return recipe.pill_name.includes('腾龙丹') || 
                               recipe.pill_name.includes('罗刹丹') || 
                               recipe.pill_name.includes('血气丹') || 
                               recipe.pill_name.includes('虚灵丹') || 
                               recipe.pill_name.includes('游龙丹');
                    }
                    return recipe.pill_name.includes(currentTab);
                });
            }

            if (filteredRecipes.length === 0) {
                recipeList.innerHTML = '<div style="text-align: center; color: #999; font-size: 10px;">暂无丹方</div>';
                return;
            }

            filteredRecipes.forEach(recipe => {
                const recipeItem = document.createElement('div');
                recipeItem.className = 'recipe-item';
                recipeItem.dataset.recipeId = recipe.id;
                
                const isUnlocked = checkRecipeUnlocked(recipe);
                if (!isUnlocked) {
                    recipeItem.classList.add('locked');
                }
                
                // 🆕 材料不足时添加特殊样式
                if (!recipe.can_craft) {
                    recipeItem.classList.add('insufficient-materials');
                }

                // 🆕 构建材料显示文本，根据充足性显示不同颜色
                let materialsText = '';
                if (recipe.material_details && recipe.material_details.length > 0) {
                    const materialParts = recipe.material_details.map(material => {
                        const colorClass = material.sufficient ? 'material-sufficient' : 'material-insufficient';
                        return `<span class="${colorClass}">${material.name} x${material.required}(${material.owned})</span>`;
                    });
                    materialsText = ' | 材料: ' + materialParts.join(', ');
                } else if (recipe.materials && recipe.materials.length > 0) {
                    // 兼容旧格式
                    materialsText = ' | 材料: ' + recipe.materials.join(', ');
                }

                recipeItem.innerHTML = `
                    <div class="recipe-name">${recipe.name}</div>
                    <div class="recipe-description">${recipe.realm_requirement} | 炼制时间 3秒</div>
                    <div class="recipe-requirements">基础成功率 ${recipe.base_success_rate}%${materialsText}</div>
                `;

                if (isUnlocked) {
                    recipeItem.onclick = function() { selectRecipe(recipe); };
                } else {
                    recipeItem.onclick = function() { showMessage('需要' + (recipe.required_level || 0) + '级', 'info'); };
                }

                recipeList.appendChild(recipeItem);
            });
        }

        // 检查丹方是否解锁（已取消境界限制）
        function checkRecipeUnlocked(recipe) {
            // 取消所有境界限制，所有丹方都可以选择
            return true;
        }

        // 选择丹方
        function selectRecipe(recipe) {
            document.querySelectorAll('.recipe-item.selected').forEach(item => {
                item.classList.remove('selected');
            });

            const recipeItem = document.querySelector('[data-recipe-id="' + recipe.id + '"]');
            recipeItem.classList.add('selected');

            selectedRecipe = recipe;
            selectedFurnace = null; // 重置丹炉选择
            console.log('🧪 [炼丹] 选择丹方:', recipe.name);
            updateCraftingInfo();
        }

        // 设置标签页
        function setupTabs() {
            document.querySelectorAll('.recipe-tab').forEach(function(tab) {
                tab.onclick = function() {
                    document.querySelectorAll('.recipe-tab').forEach(function(t) { 
                        t.classList.remove('active'); 
                    });
                    tab.classList.add('active');
                    
                    currentTab = tab.dataset.type;
                    renderRecipes();
                };
            });
        }

        // 更新炼制信息
        function updateCraftingInfo() {
            const craftButtonEl = document.getElementById('craftButton');

            if (selectedRecipe) {
                // 🆕 检查材料是否充足
                if (selectedRecipe.can_craft === false) {
                    craftButtonEl.disabled = true;
                    craftButtonEl.textContent = '材料不足';
                    craftButtonEl.style.background = 'linear-gradient(135deg, #f44336, #d32f2f)';
                } else {
                    craftButtonEl.disabled = false;
                    craftButtonEl.textContent = selectedFurnace ? '开始炼制' : '选择丹炉';
                    craftButtonEl.style.background = 'linear-gradient(135deg, #4CAF50, #45a049)';
                }
            } else {
                craftButtonEl.disabled = true;
                craftButtonEl.textContent = '请先选择丹方';
                craftButtonEl.style.background = 'linear-gradient(135deg, #666, #555)';
            }
        }

        // 选择丹炉进行炼制
        function selectFurnaceForCrafting() {
            if (!selectedRecipe) {
                showMessage('请先选择丹方', 'info');
                return;
            }
            
            // 🆕 检查材料是否充足
            if (selectedRecipe.can_craft === false) {
                showMessage('材料不足，无法炼制', 'error');
                return;
            }
            
            if (selectedFurnace) {
                // 如果已选择丹炉，直接开始炼制
                startCrafting();
                return;
            }
            
            // 显示丹炉选择弹窗
            showFurnaceModal();
        }

        // 显示丹炉选择弹窗
        function showFurnaceModal() {
            console.log('🧪 [炼丹] 显示丹炉选择弹窗');
            
            const modal = document.getElementById('furnaceModal');
            const recipeInfo = document.getElementById('recipeInfo');
            
            let infoText = `炼制 ${selectedRecipe.name}`;
            if (selectedFurnace) {
                infoText += ` | 当前使用: ${selectedFurnace.name}`;
            }
            recipeInfo.textContent = infoText;
            
            // 加载用户丹炉数据
            loadUserFurnaces().then(function() {
                renderModalFurnaces();
                modal.style.display = 'flex';
            }).catch(function(error) {
                console.error('❌ [炼丹] 加载丹炉失败:', error);
                showMessage('加载丹炉失败: ' + error.message, 'error');
            });
        }

        // 加载用户丹炉数据
        function loadUserFurnaces() {
            return fetch(window.GameConfig ? window.GameConfig.getApiUrl('alchemy_system.php?action=get_user_furnaces') : '../src/api/alchemy_system.php?action=get_user_furnaces')
                .then(function(response) {
                    return response.json();
                })
                .then(function(data) {
                    if (data.success) {
                        alchemyData.userFurnaces = data.data;
                        console.log('✅ [炼丹] 用户丹炉加载成功:', alchemyData.userFurnaces.length, '个');
                    } else {
                        throw new Error(data.message);
                    }
                });
        }

        // 渲染弹窗中的丹炉列表
        function renderModalFurnaces() {
            const furnaceGrid = document.getElementById('modalFurnaceGrid');
            furnaceGrid.innerHTML = '';

            if (alchemyData.userFurnaces.length === 0) {
                furnaceGrid.innerHTML = '<div style="text-align: center; color: #999; font-size: 12px;">暂无可用丹炉</div>';
                return;
            }

            alchemyData.userFurnaces.forEach(furnace => {
                const furnaceItem = document.createElement('div');
                furnaceItem.className = 'furnace-item';
                furnaceItem.dataset.furnaceId = furnace.id;

                furnaceItem.innerHTML = `
                    <div class="furnace-info">
                        <div class="furnace-icon">🔥</div>
                        <div class="furnace-details">
                            <div class="furnace-name">${furnace.name}</div>
                            <div class="furnace-bonus">成功率 +${furnace.success_bonus}%</div>
                            ${furnace.special_effect && furnace.special_effect !== '无特效' ? 
                                `<div class="furnace-special-effect" style="color: #d4af37; font-size: 10px; margin-top: 2px;">✨ ${furnace.special_effect}</div>` : 
                                ''
                            }
                        </div>
                    </div>
                    <div class="furnace-stats">
                        <div class="furnace-quantity">数量: ${furnace.quantity}</div>
                    </div>
                `;

                furnaceItem.onclick = function() { selectModalFurnace(furnace); };
                furnaceGrid.appendChild(furnaceItem);
            });
        }

        // 在弹窗中选择丹炉
        function selectModalFurnace(furnace) {
            document.querySelectorAll('.furnace-item.selected').forEach(item => {
                item.classList.remove('selected');
            });

            const furnaceItem = document.querySelector('[data-furnace-id="' + furnace.id + '"]');
            furnaceItem.classList.add('selected');

            selectedFurnace = furnace;
            
            // 显示炼制详情
            updateModalCraftingDetails();
            
            // 启用确认按钮
            document.getElementById('confirmCraftButton').disabled = false;
        }

        // 更新弹窗中的炼制详情
        function updateModalCraftingDetails() {
            const detailsEl = document.getElementById('craftingDetails');
            const successRateEl = document.getElementById('modalSuccessRate');
            const craftTimeEl = document.getElementById('modalCraftTime');
            const staminaCostEl = document.getElementById('modalStaminaCost');
            const materialsEl = document.getElementById('modalMaterials');

            if (selectedRecipe && selectedFurnace) {
                // 计算最终成功率：丹方基础成功率 + 丹炉加成
                const totalSuccessRate = selectedRecipe.base_success_rate + selectedFurnace.success_bonus;
                const finalSuccessRate = Math.min(totalSuccessRate, 100); // 最大不超过100%
                
                successRateEl.textContent = `${finalSuccessRate}% (基础${selectedRecipe.base_success_rate}% + 丹炉${selectedFurnace.success_bonus}%)`;
                craftTimeEl.textContent = '3秒';
                staminaCostEl.textContent = '无消耗';
                
                // 🆕 显示材料需求，根据充足性显示不同颜色
                if (selectedRecipe.material_details && selectedRecipe.material_details.length > 0) {
                    const materialParts = selectedRecipe.material_details.map(material => {
                        const colorClass = material.sufficient ? 'material-sufficient' : 'material-insufficient';
                        return `<span class="${colorClass}">${material.name} x${material.required}(${material.owned})</span>`;
                    });
                    materialsEl.innerHTML = materialParts.join(', ');
                } else if (selectedRecipe.materials && selectedRecipe.materials.length > 0) {
                    materialsEl.textContent = selectedRecipe.materials.join(', ');
                } else {
                    materialsEl.textContent = '无';
                }
                
                detailsEl.style.display = 'block';
            } else {
                detailsEl.style.display = 'none';
            }
        }

        // 关闭丹炉选择弹窗
        function closeFurnaceModal() {
            const modal = document.getElementById('furnaceModal');
            modal.style.display = 'none';
            
            // 重置选择状态
            document.querySelectorAll('.furnace-item.selected').forEach(item => {
                item.classList.remove('selected');
            });
            document.getElementById('confirmCraftButton').disabled = true;
            document.getElementById('craftingDetails').style.display = 'none';
        }

        // 确认炼制
        function confirmCrafting() {
            if (!selectedRecipe || !selectedFurnace) {
                showMessage('请选择丹方和丹炉', 'error');
                return;
            }
            
            closeFurnaceModal();
            updateCraftingInfo();
            startCrafting();
        }

        // 检查是否可以炼制
        function checkCanCraft() {
            if (!selectedRecipe || !selectedFurnace) return false;
            
            // 检查材料是否充足
            if (selectedRecipe.materials && selectedRecipe.materials.length > 0) {
                // 简化材料检查 - 由后端负责详细检查
                return true;
            }
            
            return true;
        }

        // 开始炼制
        async function startCrafting() {
            if (!selectedRecipe || !selectedFurnace || isCrafting) return;
            
            console.log('🧪 [炼丹] 开始炼制:', selectedRecipe.name);
            
            try {
                isCrafting = true;
                updateCraftingInfo();
                showCraftingProgress();
                
                const response = await fetch(window.GameConfig ? window.GameConfig.getApiUrl('alchemy_system.php?action=start_crafting') : '../src/api/alchemy_system.php?action=start_crafting', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        recipe_id: selectedRecipe.id,
                        furnace_id: selectedFurnace.id
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    // 固定3秒炼制时间
                    await simulateCrafting(3);
                    
                    // 🆕 根据炼制结果显示不同的消息和特效
                    if (data.is_crafting_success) {
                        if (data.data.is_multiple_output) {
                            // 触发了复数产出特效
                            showMessage(`🌟 ${data.message}`, 'success-multiple');
                            showMultipleOutputEffect();
                        } else {
                            // 普通成功 - 检查是否有品质随机
                            if (data.data.has_quality_random && data.data.output_items.length > 0) {
                                const itemName = data.data.output_items[0];
                                let qualityIcon = '';
                                if (itemName.includes('极品')) qualityIcon = '🏆';
                                else if (itemName.includes('上品')) qualityIcon = '💎';
                                else if (itemName.includes('中品')) qualityIcon = '⚡';
                                else if (itemName.includes('下品')) qualityIcon = '🔸';
                                
                                showMessage(`${qualityIcon} ${data.message}`, 'success');
                            } else {
                                showMessage(data.message, 'success');
                            }
                        }
                        
                        // 在控制台显示详细信息
                        console.log('🧪 [炼丹] 炼制详情:', {
                            recipe: data.data.recipe_name,
                            furnace: data.data.furnace_name,
                            success_rate: data.data.final_success_rate + '%',
                            output_count: data.data.actual_output_count,
                            is_multiple: data.data.is_multiple_output,
                            output_items: data.data.output_items,
                            has_quality_random: data.data.has_quality_random
                        });
                    } else {
                        showMessage(data.message, 'error');
                    }
                    
                    // 重新加载数据
                    await loadAlchemyData();
                    renderRecipes();
                    
                } else {
                    throw new Error(data.message);
                }
                
            } catch (error) {
                console.error('❌ [炼丹] 炼制失败:', error);
                showMessage('炼制失败: ' + error.message, 'error');
            } finally {
                isCrafting = false;
                hideCraftingProgress();
                updateCraftingInfo();
            }
        }

        // 显示炼制进度
        function showCraftingProgress() {
            const overlayEl = document.getElementById('craftingOverlay');
            const backgroundEl = document.getElementById('craftingBackground');
            const progressEl = document.getElementById('craftingProgress');
            
            overlayEl.style.display = 'block';
            backgroundEl.style.display = 'block';
            progressEl.style.display = 'block';
        }

        // 隐藏炼制进度
        function hideCraftingProgress() {
            const overlayEl = document.getElementById('craftingOverlay');
            const backgroundEl = document.getElementById('craftingBackground');
            const progressEl = document.getElementById('craftingProgress');
            
            overlayEl.style.display = 'none';
            backgroundEl.style.display = 'none';
            progressEl.style.display = 'none';
            
            const progressFill = document.getElementById('progressFill');
            progressFill.style.width = '0%';
        }

        // 模拟炼制过程
        function simulateCrafting(duration) {
            return new Promise((resolve) => {
                const progressFill = document.getElementById('progressFill');
                const progressText = document.getElementById('progressText');
                
                let currentTime = 0;
                const interval = 100;
                
                const timer = setInterval(() => {
                    currentTime += interval;
                    const progress = (currentTime / (duration * 1000)) * 100;
                    
                    progressFill.style.width = Math.min(progress, 100) + '%';
                    progressText.textContent = '炼制中... ' + Math.floor((duration * 1000 - currentTime) / 1000) + '秒';
                    
                    if (currentTime >= duration * 1000) {
                        clearInterval(timer);
                        progressText.textContent = '炼制完成！';
                        resolve();
                    }
                }, interval);
            });
        }

        // 🆕 显示复数产出特效
        function showMultipleOutputEffect() {
            // 创建特效容器
            const effectContainer = document.createElement('div');
            effectContainer.className = 'multiple-output-effect';
            effectContainer.innerHTML = `
                <div class="effect-text">丹炉特效触发！</div>
                <div class="effect-particles">
                    <div class="particle">✨</div>
                    <div class="particle">🌟</div>
                    <div class="particle">💫</div>
                    <div class="particle">⭐</div>
                    <div class="particle">✨</div>
                </div>
            `;
            
            document.body.appendChild(effectContainer);
            
            // 3秒后移除特效
            setTimeout(() => {
                if (effectContainer.parentNode) {
                    effectContainer.parentNode.removeChild(effectContainer);
                }
            }, 3000);
        }

        // 显示消息提示
        function showMessage(text, type = 'info') {
            const existingMessage = document.querySelector('.message');
            if (existingMessage) {
                existingMessage.remove();
            }
            
            const message = document.createElement('div');
            message.className = 'message ' + type;
            message.textContent = text;
            document.body.appendChild(message);
            
            setTimeout(() => {
                if (message.parentNode) {
                    message.parentNode.removeChild(message);
                }
            }, 2500);
        }
    </script>
</body>
</html>