<?php
// 引入全局配置和函数库
require_once __DIR__ . '/../includes/functions.php';

// 检查维护模式
if (isMaintenanceMode()) {
    echo json_encode([
        'success' => false,
        'message' => '游戏正在维护中，请稍后再试',
        'maintenance' => true
    ]);
    exit;
}

// 记录API调用（如果开启了调试）
if (DEBUG_LOG_API_CALLS) {
    writeLog("API调用: redeem_code.php", 'DEBUG', 'api.log');
}

setJsonResponse();

// 获取输入数据
$input = json_decode(file_get_contents('php://input'), true);
$action = isset($input['action']) ? $input['action'] : (isset($_GET['action']) ? $_GET['action'] : 'redeem');

try {
    $pdo = getDatabase();
    
    if (!$pdo) {
        writeLog("兑换码系统: 数据库连接失败", 'ERROR', 'database.log');
        throw new Exception('数据库连接失败');
    }
    
    switch ($action) {
        case 'redeem':
            handleRedeemCode($pdo, $input);
            break;
            
        case 'create':
            handleCreateCode($pdo, $input);
            break;
            
        case 'list':
            handleListCodes($pdo);
            break;
            
        default:
            echo json_encode([
                'success' => false,
                'message' => '无效的操作'
            ]);
            break;
    }
    
} catch (Exception $e) {
    error_log("兑换码系统错误: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => '系统错误，请稍后重试'
    ]);
}

// 处理兑换码兑换
function handleRedeemCode($pdo, $input) {
    // 检查是否登录
    if (!isset($_SESSION['user_id'])) {
        echo json_encode([
            'success' => false,
            'message' => '请先登录'
        ]);
        return;
    }
    
    $code = trim(isset($input['code']) ? $input['code'] : '');
    
    if (empty($code)) {
        echo json_encode([
            'success' => false,
            'message' => '请输入兑换码'
        ]);
        return;
    }
    
    $user_id = $_SESSION['user_id'];
    
    try {
        $pdo->beginTransaction();
        
        // 查找兑换码 - 使用新字段名
        $stmt = $pdo->prepare("
            SELECT id, code_type, rewards, max_uses, used_count, valid_until, status
            FROM redeem_codes 
            WHERE code = ? AND status = 'active'
        ");
        $stmt->execute([$code]);
        $redeemCode = $stmt->fetch();
        
        if (!$redeemCode) {
            throw new Exception('兑换码不存在或已失效');
        }
        
        // 检查是否过期
        if ($redeemCode['valid_until'] && strtotime($redeemCode['valid_until']) < time()) {
            throw new Exception('兑换码已过期');
        }
        
        // 检查使用次数 - 使用新字段名
        if ($redeemCode['max_uses'] > 0 && $redeemCode['used_count'] >= $redeemCode['max_uses']) {
            throw new Exception('兑换码使用次数已达上限');
        }
        
        // 获取用户角色ID
        $stmt = $pdo->prepare("SELECT id FROM characters WHERE user_id = ? LIMIT 1");
        $stmt->execute([$user_id]);
        $character = $stmt->fetch();
        
        if (!$character) {
            throw new Exception('请先创建角色');
        }
        
        $character_id = $character['id'];
        
        // 检查用户是否已经使用过这个兑换码
            $stmt = $pdo->prepare("
                SELECT id FROM redeem_logs 
            WHERE user_id = ? AND code_id = ?
            ");
            $stmt->execute([$user_id, $redeemCode['id']]);
            
            if ($stmt->fetch()) {
                throw new Exception('您已经使用过这个兑换码');
        }
        
        // 解析奖励数据
        $rewardData = json_decode($redeemCode['rewards'], true);
        if (!$rewardData) {
            throw new Exception('兑换码奖励数据错误');
        }
        
        // 发放奖励
        $rewards = [];
        
        // 灵石
        if (isset($rewardData['spirit_stones']) && $rewardData['spirit_stones'] > 0) {
            $stmt = $pdo->prepare("UPDATE users SET spirit_stones = spirit_stones + ? WHERE id = ?");
            $stmt->execute([$rewardData['spirit_stones'], $user_id]);
            $rewards[] = $rewardData['spirit_stones'] . ' 灵石';
                }
        
        // 金币
                if (isset($rewardData['gold']) && $rewardData['gold'] > 0) {
            $stmt = $pdo->prepare("UPDATE users SET gold = gold + ? WHERE id = ?");
            $stmt->execute([$rewardData['gold'], $user_id]);
                    $rewards[] = $rewardData['gold'] . ' 金币';
                }
        
        // 物品奖励
        if (isset($rewardData['item_id']) && is_array($rewardData['item_id'])) {
            foreach ($rewardData['item_id'] as $item_id) {
                $quantity = isset($rewardData['item_quantity'][$item_id]) ? $rewardData['item_quantity'][$item_id] : 1;
                $item_name = giveItemToUser($pdo, $user_id, $character_id, $item_id, $quantity);
                $rewards[] = $quantity . 'x' . $item_name;
            }
        }
        
        // 构建奖励消息
        $rewardMessage = !empty($rewards) ? '获得 ' . implode('、', $rewards) : '获得神秘奖励';
        
        // 更新兑换码使用次数
        $stmt = $pdo->prepare("
            UPDATE redeem_codes 
            SET used_count = used_count + 1
            WHERE id = ?
        ");
        $stmt->execute([$redeemCode['id']]);
        
        // 记录兑换日志 - 添加缺失的code字段
        $stmt = $pdo->prepare("
            INSERT INTO redeem_logs (user_id, character_id, code_id, code, rewards_received, created_at)
            VALUES (?, ?, ?, ?, ?, NOW())
        ");
        $stmt->execute([
            $user_id,
            $character_id,
            $redeemCode['id'],
            $code,
            $redeemCode['rewards']
        ]);
        
        $pdo->commit();
        
        echo json_encode([
            'success' => true,
            'message' => '兑换成功！' . $rewardMessage
        ]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        echo json_encode([
            'success' => false,
            'message' => '兑换失败：' . $e->getMessage()
        ]);
    }
}

// 处理创建兑换码（管理员功能）
function handleCreateCode($pdo, $input) {
    // 这里可以添加管理员权限验证
    // if (!isAdmin()) { ... }
    
    $code = strtoupper(trim(isset($input['code']) ? $input['code'] : ''));
    $codeType = isset($input['code_type']) ? $input['code_type'] : 'general';
    $title = isset($input['title']) ? $input['title'] : $code;
    $description = isset($input['description']) ? $input['description'] : '';
    $rewardData = isset($input['reward_data']) ? $input['reward_data'] : [];
    $maxUses = intval(isset($input['max_uses']) ? $input['max_uses'] : 1);
    $validUntil = isset($input['valid_until']) ? $input['valid_until'] : null;
    
    if (empty($code)) {
        echo json_encode([
            'success' => false,
            'message' => '请输入兑换码'
        ]);
        return;
    }
    
    // 根据奖励类型构建奖励数据
    if (is_array($rewardData)) {
        $rewardData = json_encode($rewardData);
    } else {
        // 默认构建空的奖励数据
            $rewardData = json_encode(['description' => '空礼包']);
    }
    
    try {
        // 检查兑换码是否已存在
        $stmt = $pdo->prepare("SELECT id FROM redeem_codes WHERE code = ?");
        $stmt->execute([$code]);
        
        if ($stmt->fetch()) {
            throw new Exception('兑换码已存在');
        }
        
        // 创建兑换码 - 使用完整的字段
        $stmt = $pdo->prepare("
            INSERT INTO redeem_codes (code, code_type, title, description, rewards, 
                                    max_uses, used_count, valid_from, valid_until, 
                                    level_requirement, realm_requirement, user_limit, 
                                    status, created_by, created_at)
            VALUES (?, ?, ?, ?, ?, ?, 0, NOW(), ?, 1, NULL, 1, 'active', 1, NOW())
        ");
        
        $stmt->execute([
            $code,
            $codeType,
            $title,
            $description,
            $rewardData,
            $maxUses,
            $validUntil
        ]);
        
        echo json_encode([
            'success' => true,
            'message' => '兑换码创建成功',
            'code' => $code
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

// 处理查看兑换码列表（管理员功能）
function handleListCodes($pdo) {
    try {
        $stmt = $pdo->prepare("
            SELECT id, code, code_type, title, description, rewards, 
                   max_uses, used_count, valid_until, status, created_at
            FROM redeem_codes 
            ORDER BY created_at DESC
            LIMIT 50
        ");
        $stmt->execute();
        $codes = $stmt->fetchAll();
        
        echo json_encode([
            'success' => true,
            'codes' => $codes
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

// 更新用户资源
function updateUserResource($pdo, $user_id, $resource_type, $amount, $operation = '+') {
    // 根据资源类型选择对应的字段
    $field_map = [
        'gems' => 'spirit_stone',        // 灵石
        'gold' => 'gold',               // 金币
        'silver' => 'silver',           // 银两
        'spiritual_power' => 'spiritual_power',  // 灵气
        'immortal_jade' => 'immortal_jade',     // 仙玉
        'immortal_power' => 'immortal_power'    // 仙力
    ];
    
    $field = isset($field_map[$resource_type]) ? $field_map[$resource_type] : null;
    
    if (!$field) {
        throw new Exception('不支持的资源类型: ' . $resource_type);
    }
    
    // 检查字段是否存在于users表中
    $stmt = $pdo->prepare("SHOW COLUMNS FROM users LIKE ?");
    $stmt->execute([$field]);
    $columnExists = $stmt->fetch();
    
    if (!$columnExists) {
        throw new Exception("用户表中不存在字段: {$field}");
    }
    
    // 更新users表中的资源字段
        $operator = $operation === '+' ? '+' : '-';
        $stmt = $pdo->prepare("
            UPDATE users 
        SET {$field} = GREATEST(0, {$field} {$operator} ?)
            WHERE id = ?
        ");
        $stmt->execute([$amount, $user_id]);
    
    // 检查是否更新成功
    if ($stmt->rowCount() === 0) {
        throw new Exception('资源更新失败，用户不存在');
    }
}

// 发放物品到用户背包
function giveItemToUser($pdo, $user_id, $character_id, $item_id, $quantity = 1) {
    try {
        // 获取物品信息
        $stmt = $pdo->prepare("SELECT item_name, item_type FROM game_items WHERE id = ?");
        $stmt->execute([$item_id]);
        $item = $stmt->fetch();
        
        if (!$item) {
            throw new Exception('物品不存在: ' . $item_id);
        }
        
        $item_name = $item['item_name'];
        $item_type = $item['item_type'];
        
        // 检查用户背包是否已有此物品
        $stmt = $pdo->prepare("
            SELECT id, quantity FROM user_inventories 
            WHERE character_id = ? AND item_id = ?
        ");
        $stmt->execute([$character_id, $item_id]);
        $existingItem = $stmt->fetch();
        
        if ($existingItem) {
            // 增加现有物品数量
            $newQuantity = $existingItem['quantity'] + $quantity;
            $stmt = $pdo->prepare("
                UPDATE user_inventories 
                SET quantity = ?, updated_at = NOW() 
                WHERE id = ?
            ");
            $stmt->execute([$newQuantity, $existingItem['id']]);
        } else {
            // 添加新物品到背包 - 只使用实际存在的字段
            require_once __DIR__ . '/../includes/inventory_utils.php';
            $sortWeight = calculateSortWeight($pdo, $character_id, $item_type);
            
            $stmt = $pdo->prepare("
                INSERT INTO user_inventories (character_id, item_id, item_type, quantity, obtained_time, obtained_source, updated_at, sort_weight)
                VALUES (?, ?, ?, ?, NOW(), '兑换码奖励', NOW(), ?)
            ");
            $stmt->execute([$character_id, $item_id, $item_type, $quantity, $sortWeight]);
        }
        
        return $item_name;
        
    } catch (Exception $e) {
        throw new Exception('发放物品失败: ' . $e->getMessage());
    }
}

?> 