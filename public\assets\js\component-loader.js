/**
 * 组件加载器 - 用于加载公用HTML组件
 */

// 加载角色装备组件
async function loadCharacterEquipmentComponent(containerId) {
    try {
        const response = await fetch('assets/components/character-equipment.html');
        const html = await response.text();
        
        const container = document.getElementById(containerId);
        if (container) {
            // 先清空容器，防止重复加载
            container.innerHTML = '';
            
            // 然后设置新内容
            container.innerHTML = html;
            
            console.log('角色装备组件加载完成:', containerId);
            console.log('容器内武器槽位数量:', container.querySelectorAll('.weapon-slot').length);
            
            // 触发组件加载完成事件
            const event = new CustomEvent('characterEquipmentLoaded', {
                detail: { containerId: containerId }
            });
            document.dispatchEvent(event);
            
            return true;
        } else {
            console.error('找不到容器元素:', containerId);
            return false;
        }
    } catch (error) {
        console.error('加载角色装备组件失败:', error);
        return false;
    }
}

// 通用组件加载函数
async function loadComponent(componentPath, containerId) {
    try {
        const response = await fetch(componentPath);
        const html = await response.text();
        
        const container = document.getElementById(containerId);
        if (container) {
            // 先清空容器，防止重复加载
            container.innerHTML = '';
            
            // 然后设置新内容
            container.innerHTML = html;
            
            console.log('组件加载完成:', componentPath, '->', containerId);
            
            // 触发组件加载完成事件
            const event = new CustomEvent('componentLoaded', {
                detail: { 
                    componentPath: componentPath,
                    containerId: containerId 
                }
            });
            document.dispatchEvent(event);
            
            return true;
        } else {
            console.error('找不到容器元素:', containerId);
            return false;
        }
    } catch (error) {
        console.error('加载组件失败:', error);
        return false;
    }
}

// 导出函数
window.ComponentLoader = {
    loadCharacterEquipmentComponent,
    loadComponent
}; 