/**
 * 一念修仙 - UI控制器
 * 负责管理战斗界面的所有UI更新操作
 * 包括状态显示、武器显示、区域信息、消息提示等
 */

class BattleUIController {
    constructor() {
        this.isMessageVisible = false;
        this.messageQueue = [];
        this.currentHighlightedWeapon = -1;
        this.isInitialized = false;
        
        // DOM元素引用
        this.battleContainer = null;
        this.effectsContainer = null;
        this.battleStatus = null;
        this.currentSkill = null;
        this.areaName = null;
        this.stageLevel = null;
        this.weaponSlots = null;
        
        console.log('🎨 UI控制器初始化完成');
    }
    
    /**
     * 初始化UI
     */
    initialize() {
        if (this.isInitialized) return;
        
        // 初始化DOM元素引用
        this.battleContainer = document.querySelector('.battle-container');
        this.effectsContainer = document.querySelector('.effects-container');
        this.battleStatus = document.querySelector('.battle-status');
        this.currentSkill = document.querySelector('.current-skill');
        this.areaName = document.querySelector('.area-name');
        this.stageLevel = document.querySelector('.stage-level');
        this.weaponSlots = document.querySelector('.weapon-slots');
        
        // 创建effects容器（如果不存在）
        if (!this.effectsContainer && this.battleContainer) {
            this.effectsContainer = document.createElement('div');
            this.effectsContainer.className = 'effects-container';
            this.battleContainer.appendChild(this.effectsContainer);
        }
        
        this.isInitialized = true;
        console.log('🎨 UI控制器DOM元素初始化完成');
    }
    
    /**
     * 更新区域信息显示
     */
    updateAreaInfo(areaData) {
        if (this.areaName && areaData) {
            this.areaName.textContent = areaData.areaName || '未知区域';
        }
        
        if (this.stageLevel && areaData) {
            this.stageLevel.textContent = `第${areaData.stageLevel || 1}关`;
        }
    }
    
    /**
     * 更新战斗状态显示
     */
    updateBattleStatus(message) {
        if (this.battleStatus) {
            this.battleStatus.textContent = message;
        }
    }
    
    /**
     * 更新当前技能显示
     */
    updateCurrentSkill(skillName) {
        if (this.currentSkill) {
            this.currentSkill.textContent = `当前技能: ${skillName}`;
        }
    }
    
    /**
     * 更新武器显示
     */
    updateWeaponDisplay(skillSequence) {
        if (!this.weaponSlots) return;
        
        // 清空现有内容
        this.weaponSlots.innerHTML = '';
        
        // 创建武器槽位
        skillSequence.forEach((skill, index) => {
            const slot = document.createElement('div');
            slot.className = 'weapon-slot' + (skill.hasWeapon ? ' has-weapon' : '');
            
            if (skill.hasWeapon) {
                const weaponInfo = document.createElement('div');
                weaponInfo.className = 'weapon-info';
                weaponInfo.innerHTML = `
                    <div class="weapon-name">${skill.weaponName || '未知武器'}</div>
                    <div class="weapon-attack">攻击力: ${skill.weaponAttack || 0}</div>
                `;
                slot.appendChild(weaponInfo);
            }
            
            this.weaponSlots.appendChild(slot);
        });
    }
    
    /**
     * 显示技能喊话
     */
    async showSkillShout(skillName, isEnemy = false) {
        const shoutElement = document.createElement('div');
        shoutElement.className = `skill-shout ${isEnemy ? 'enemy-shout' : 'player-shout'}`;
        shoutElement.textContent = skillName;
        
        const container = isEnemy ? 
            document.querySelector('.enemy-container') : 
            document.querySelector('.player-container');
            
        if (container) {
            container.appendChild(shoutElement);
            
            // 动画结束后移除元素
            setTimeout(() => {
                if (shoutElement.parentNode) {
                    shoutElement.parentNode.removeChild(shoutElement);
                }
            }, 1000);
            
            // 等待动画完成
            await new Promise(resolve => setTimeout(resolve, 500));
        }
    }
    
    /**
     * 显示战斗胜利面板
     */
    async showVictoryPanel(message, droppedItems) {
        // 创建胜利面板容器
        const victoryOverlay = document.createElement('div');
        victoryOverlay.className = 'victory-overlay';
        
        // 创建胜利消息
        const victoryMessage = document.createElement('div');
        victoryMessage.className = 'victory-message';
        victoryMessage.textContent = message;
        victoryOverlay.appendChild(victoryMessage);
        
        // 创建掉落物品列表
        if (droppedItems && droppedItems.length > 0) {
            const dropList = document.createElement('div');
            dropList.className = 'drop-list';
            
            for (const item of droppedItems) {
                const itemElement = document.createElement('div');
                itemElement.className = 'dropped-item';
                itemElement.innerHTML = `
                    <div class="item-name">${item.name}</div>
                    <div class="item-quantity">数量: ${item.quantity || 1}</div>
                `;
                
                // 添加点击事件显示详细信息
                itemElement.addEventListener('click', () => {
                    this.showDropItemDetail(item);
                });
                
                dropList.appendChild(itemElement);
            }
            
            victoryOverlay.appendChild(dropList);
        }
        
        // 添加到页面
        document.body.appendChild(victoryOverlay);
        
        // 等待动画完成
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    /**
     * 显示掉落物品详情
     */
    showDropItemDetail(dropData) {
        const detailOverlay = document.createElement('div');
        detailOverlay.className = 'item-detail-overlay';
        
        const detailContent = document.createElement('div');
        detailContent.className = 'item-detail-content';
        
        // 构建物品详情HTML
        detailContent.innerHTML = `
            <div class="item-header">
                <div class="item-name ${dropData.rarity}-text">${dropData.name}</div>
                <div class="item-type">${dropData.type || '未知类型'}</div>
            </div>
            <div class="item-stats">
                ${this.constructItemDetailFromDrop(dropData)}
            </div>
            <div class="item-description">${dropData.description || ''}</div>
        `;
        
        // 添加关闭按钮
        const closeButton = document.createElement('button');
        closeButton.className = 'close-detail';
        closeButton.textContent = '关闭';
        closeButton.onclick = () => detailOverlay.remove();
        detailContent.appendChild(closeButton);
        
        detailOverlay.appendChild(detailContent);
        document.body.appendChild(detailOverlay);
    }
    
    /**
     * 构建物品详情
     */
    constructItemDetailFromDrop(dropData) {
        let detailHtml = '';
        
        // 添加基础属性
        if (dropData.attack) {
            detailHtml += `<div class="stat-line">攻击力: ${dropData.attack}</div>`;
        }
        if (dropData.defense) {
            detailHtml += `<div class="stat-line">防御力: ${dropData.defense}</div>`;
        }
        if (dropData.hp) {
            detailHtml += `<div class="stat-line">生命值: ${dropData.hp}</div>`;
        }
        if (dropData.mp) {
            detailHtml += `<div class="stat-line">法力值: ${dropData.mp}</div>`;
        }
        
        // 添加特殊属性
        if (dropData.specialAttributes) {
            dropData.specialAttributes.forEach(attr => {
                detailHtml += `<div class="stat-line special">${attr}</div>`;
            });
        }
        
        return detailHtml;
    }
    
    /**
     * 显示简单胜利面板（无掉落物品时使用）
     */
    async showSimpleVictoryPanel(message) {
        const simpleOverlay = document.createElement('div');
        simpleOverlay.className = 'simple-victory-overlay';
        simpleOverlay.textContent = message;
        
        document.body.appendChild(simpleOverlay);
        
        // 等待动画完成后移除
        setTimeout(() => {
            if (simpleOverlay.parentNode) {
                simpleOverlay.parentNode.removeChild(simpleOverlay);
            }
        }, 2000);
        
        await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    /**
     * 显示战斗消息
     */
    showBattleMessage(message, type = 'info', duration = 3000) {
        // 将消息加入队列
        this.messageQueue.push({ message, type, duration });
        
        // 如果当前没有显示消息，立即显示
        if (!this.isMessageVisible) {
            this.processMessageQueue();
        }
    }
    
    /**
     * 处理消息队列
     */
    async processMessageQueue() {
        if (this.messageQueue.length === 0) {
            this.isMessageVisible = false;
            return;
        }
        
        this.isMessageVisible = true;
        const { message, type, duration } = this.messageQueue.shift();
        
        try {
            await this.displayMessage(message, type, duration);
        } catch (error) {
            console.error('❌ 显示消息失败:', error);
        }
        
        // 继续处理队列中的下一条消息
        this.processMessageQueue();
    }
    
    /**
     * 显示单条消息
     */
    displayMessage(message, type, duration) {
        return new Promise((resolve) => {
            const messageDiv = document.createElement('div');
            messageDiv.className = `battle-message battle-message-${type}`;
            messageDiv.textContent = message;
            
            // 设置样式
            Object.assign(messageDiv.style, {
                position: 'fixed',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                padding: '12px 20px',
                borderRadius: '6px',
                color: 'white',
                fontSize: '14px',
                fontWeight: 'bold',
                zIndex: '10000',
                maxWidth: '80%',
                textAlign: 'center',
                boxShadow: '0 4px 12px rgba(0,0,0,0.3)',
                opacity: '0',
                transition: 'opacity 0.3s ease'
            });
            
            // 根据类型设置背景色
            const colors = {
                info: '#2196f3',
                success: '#4caf50',
                warning: '#ff9800',
                error: '#f44336'
            };
            messageDiv.style.backgroundColor = colors[type] || colors.info;
            
            document.body.appendChild(messageDiv);
            
            // 动画显示
            setTimeout(() => {
                messageDiv.style.opacity = '1';
            }, 100);
            
            // 自动隐藏
            setTimeout(() => {
                messageDiv.style.opacity = '0';
                setTimeout(() => {
                    if (messageDiv.parentNode) {
                        messageDiv.parentNode.removeChild(messageDiv);
                    }
                    resolve();
                }, 300);
            }, duration);
            
            console.log(`💬 显示${type}消息:`, message);
        });
    }
    
    /**
     * 显示加载状态
     */
    showLoader(text = '加载中...') {
        // 移除已存在的加载器
        this.hideLoader();
        
        const loaderDiv = document.createElement('div');
        loaderDiv.className = 'battle-loader-overlay';
        loaderDiv.innerHTML = `
            <div class="battle-loader">
                <div class="battle-loader-spinner"></div>
                <div class="battle-loader-text">${text}</div>
            </div>
        `;
        
        // 设置样式
        Object.assign(loaderDiv.style, {
            position: 'fixed',
            top: '0',
            left: '0',
            width: '100%',
            height: '100%',
            backgroundColor: 'rgba(0, 0, 0, 0.7)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: '10001',
            backdropFilter: 'blur(5px)'
        });
        
        document.body.appendChild(loaderDiv);
        console.log('⏳ 显示加载状态:', text);
    }
    
    /**
     * 隐藏加载状态
     */
    hideLoader() {
        const loader = document.querySelector('.battle-loader-overlay');
        if (loader) {
            loader.remove();
            console.log('✅ 隐藏加载状态');
        }
    }
    
    /**
     * 清除所有高亮
     */
    clearAllHighlights() {
        try {
            const allWeapons = document.querySelectorAll('.weapon-item');
            allWeapons.forEach(weapon => {
                weapon.classList.remove('current-weapon');
            });
            this.currentHighlightedWeapon = -1;
            
            console.log('🧹 清除所有武器高亮');
        } catch (error) {
            console.error('❌ 清除高亮失败:', error);
        }
    }
    
    /**
     * 重置UI状态
     */
    resetUIState() {
        try {
            this.clearAllHighlights();
            this.messageQueue = [];
            this.isMessageVisible = false;
            this.hideLoader();
            
            // 重置状态显示
            this.updateBattleStatus('准备战斗...');
            this.updateCurrentSkill('');
            
            console.log('🔄 UI状态已重置');
        } catch (error) {
            console.error('❌ 重置UI状态失败:', error);
        }
    }
    
    /**
     * 获取UI状态信息
     */
    getUIState() {
        return {
            currentHighlightedWeapon: this.currentHighlightedWeapon,
            messageQueueLength: this.messageQueue.length,
            isMessageVisible: this.isMessageVisible,
            hasLoader: !!document.querySelector('.battle-loader-overlay')
        };
    }
}

// 导出UI控制器类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BattleUIController;
} else {
    window.BattleUIController = BattleUIController;
} 