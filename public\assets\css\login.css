.login-container {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 40px;
    border: 2px solid rgba(255, 215, 0, 0.3);
    box-shadow: 
        0 0 30px rgba(255, 215, 0, 0.2),
        inset 0 0 20px rgba(255, 255, 255, 0.1);
    max-width: 648px;
    margin: 50px auto;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

/* PWA全屏模式专用样式 - 确保完全填充屏幕 */
@media (display-mode: standalone) {
    html, body {
        height: 100vh !important;
        height: calc(var(--vh, 1vh) * 100) !important;
        margin: 0 !important;
        padding: 0 !important;
        overflow: hidden !important;
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
    }
    
    .login-container {
        width: 100vw !important;
        height: 100vh !important;
        height: calc(var(--vh, 1vh) * 100) !important;
        min-height: 100vh !important;
        min-height: calc(var(--vh, 1vh) * 100) !important;
        max-height: 100vh !important;
        max-height: calc(var(--vh, 1vh) * 100) !important;
        margin: 0 !important;
        padding: 40px 20px !important;
        border-radius: 0 !important;
        border: none !important;
        background: rgba(255, 255, 255, 0.05) !important;
        overflow-y: auto !important;
        -webkit-overflow-scrolling: touch !important;
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        transform: none !important;
        display: flex !important;
        flex-direction: column !important;
        justify-content: center !important;
        align-items: center !important;
        max-width: none !important;
    }
    
    /* 确保子元素不会导致额外的高度 */
    .login-title {
        margin-top: 0 !important;
        margin-bottom: 20px !important;
    }
    
    .form-group {
        margin-bottom: 15px !important;
    }
    
    .login-button {
        margin-bottom: 15px !important;
    }
    
    .guest-button {
        margin-bottom: 10px !important;
    }
    
    .links {
        margin-top: 10px !important;
        margin-bottom: 0 !important;
    }
}

/* 小屏设备PWA模式适配 */
@media (display-mode: standalone) and (max-width: 414px) {
    .login-container {
        padding: 30px 15px !important;
    }
    
    .login-title {
        font-size: 28px !important;
        margin-bottom: 15px !important;
    }
    
    .form-input {
        padding: 10px 14px !important;
        font-size: 16px !important;
    }
    
    .login-button {
        padding: 12px !important;
        font-size: 16px !important;
    }
    
    .guest-button {
        padding: 10px !important;
        font-size: 14px !important;
    }
}

@media (display-mode: standalone) and (max-width: 375px) {
    .login-container {
        padding: 25px 12px !important;
    }
    
    .login-title {
        font-size: 26px !important;
    }
    
    .form-input {
        padding: 9px 12px !important;
    }
}

@media (display-mode: standalone) and (max-width: 320px) {
    .login-container {
        padding: 20px 10px !important;
    }
    
    .login-title {
        font-size: 24px !important;
    }
    
    .form-label {
        font-size: 14px !important;
    }
    
    .form-input {
        padding: 8px 10px !important;
        font-size: 14px !important;
    }
    
    .login-button {
        padding: 10px !important;
        font-size: 14px !important;
    }
    
    .guest-button {
        padding: 8px !important;
        font-size: 13px !important;
    }
    
    .links a {
        font-size: 12px !important;
    }
}

@media (max-width: 768px) {
    .login-container {
        margin: 20px;
        padding: 30px 20px;
        min-height: calc(var(--vh, 1vh) * 100 - 40px);
        position: relative;
        top: auto;
        left: auto;
        transform: none;
    }
}

@media (max-width: 375px) {
    .login-container {
        margin: 10px;
        padding: 25px 15px;
        min-height: calc(var(--vh, 1vh) * 100 - 20px);
    }
    
    .login-title {
        font-size: 28px;
    }
    
    .form-input {
        padding: 10px 12px;
        font-size: 14px;
    }
}

.login-title {
    text-align: center;
    font-size: 32px;
    font-weight: bold;
    color: #ffd700;
    text-shadow: 
        0 0 20px rgba(255, 215, 0, 0.8),
        2px 2px 4px rgba(0, 0, 0, 0.8);
    margin-bottom: 30px;
    animation: title-glow 2s ease-in-out infinite alternate;
}

@keyframes title-glow {
    from {
        text-shadow: 
            0 0 20px rgba(255, 215, 0, 0.8),
            2px 2px 4px rgba(0, 0, 0, 0.8);
    }
    to {
        text-shadow: 
            0 0 30px rgba(255, 215, 0, 1),
            0 0 40px rgba(255, 215, 0, 0.6),
            2px 2px 4px rgba(0, 0, 0, 0.8);
    }
}

.form-group {
    margin-bottom: 15px;
}

.form-label {
    display: block;
    margin-bottom: 6px;
    font-size: 14px;
    color: #ffffff;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.form-input {
    width: 100%;
    padding: 10px 14px;
    border: 2px solid rgba(255, 215, 0, 0.3);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 14px;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.form-input:focus {
    outline: none;
    border-color: #ffd700;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

.form-input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

/* 🆕 自动填充的输入框样式 */
.form-input.auto-filled {
    border-color: rgba(76, 175, 80, 0.5);
    background: rgba(76, 175, 80, 0.1);
    box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
}

.form-input.auto-filled::before {
    content: '✓ ';
    color: #4caf50;
}

.login-button {
    width: 100%;
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #1a1a2e;
    border: none;
    padding: 12px;
    border-radius: 10px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 
        0 4px 15px rgba(255, 215, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    margin-bottom: 15px;
}

.login-button:hover {
    transform: translateY(-2px);
    box-shadow: 
        0 6px 20px rgba(255, 215, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.login-button:active {
    transform: translateY(0);
}

.login-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.links {
    text-align: center;
}

.links a {
    color: #ffd700;
    text-decoration: none;
    margin: 0 8px;
    font-size: 13px;
    transition: color 0.3s ease;
}

.links a:hover {
    color: #ffed4e;
    text-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
}

.message {
    padding: 12px;
    border-radius: 8px;
    margin-bottom: 20px;
    text-align: center;
    font-weight: bold;
}

.message.success {
    background: rgba(76, 175, 80, 0.2);
    border: 1px solid rgba(76, 175, 80, 0.5);
    color: #4caf50;
}

.message.error {
    background: rgba(244, 67, 54, 0.2);
    border: 1px solid rgba(244, 67, 54, 0.5);
    color: #f44336;
}

.loading {
    display: none;
    text-align: center;
    color: #ffd700;
}

.loading::after {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 215, 0, 0.3);
    border-radius: 50%;
    border-top-color: #ffd700;
    animation: spin 1s ease-in-out infinite;
    margin-left: 10px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.guest-button {
    width: 100%;
    background: linear-gradient(135deg, #666, #888);
    color: white;
    border: none;
    padding: 10px;
    border-radius: 8px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 10px;
}

.guest-button:hover {
    background: linear-gradient(135deg, #777, #999);
    transform: translateY(-1px);
}

.clear-credentials-btn {
    background: none;
    border: none;
    color: #ffd700;
    font-size: 14px;
    cursor: pointer;
    transition: color 0.3s ease;
}

.clear-credentials-btn:hover {
    color: #ffed4e;
    text-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
}

/* 🔧 新增：复选框样式优化，确保移动端显示正常 */
input[type="checkbox"] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border: 2px solid rgba(255, 215, 0, 0.6);
    border-radius: 3px;
    background-color: rgba(255, 255, 255, 0.1);
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
    flex-shrink: 0; /* 防止缩放 */
    margin-right: 8px;
}

input[type="checkbox"]:checked {
    background-color: rgba(255, 215, 0, 0.8);
    border-color: rgba(255, 215, 0, 1);
}

input[type="checkbox"]:checked::after {
    content: '✓';
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    color: #000;
    font-size: 12px;
    font-weight: bold;
    line-height: 1;
}

input[type="checkbox"]:hover {
    border-color: rgba(255, 215, 0, 0.8);
    background-color: rgba(255, 255, 255, 0.15);
}

/* 🔧 新增：清除按钮样式 */
.clear-credentials-btn {
    background: rgba(255, 99, 99, 0.2);
    border: 1px solid rgba(255, 99, 99, 0.4);
    color: rgba(255, 99, 99, 0.8);
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
}

.clear-credentials-btn:hover {
    background: rgba(255, 99, 99, 0.3);
    border-color: rgba(255, 99, 99, 0.6);
    color: rgba(255, 99, 99, 1);
}