<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover">
    
    <!-- 🔧 PWA模式底部白边强力修复 - 必须最早执行 -->
    <script src="assets/js/pwa-fix.js"></script>
    
    <!-- 🎛️ 全局调试开关 - 必须最早加载 -->
    <script src="assets/js/global-debug-switch.js"></script>

    <!-- 🔧 项目配置文件 - 必须早期加载 -->
    <script src="assets/js/config.js"></script>

    <!-- 新增HBuilder X优化meta标签 -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="format-detection" content="telephone=no">
    <!-- PWA支持 -->
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#d4af37">
    
    <title>一念修仙 - 市场</title>
    <link rel="stylesheet" href="assets/css/global.css">
    <link rel="stylesheet" href="assets/css/shop.css">
    <link rel="stylesheet" href="assets/css/common-navigation.css">
    <!-- 移动端CSS已删除 -->
    
    <!-- 🔧 视口高度修复脚本 -->
    <script src="assets/js/viewport-height-fix.js"></script>
    
    <!-- 🔑 全局登录检查系统 -->
    <script src="assets/js/auth-check.js"></script>

    <!-- 🎵 全局音乐管理器 -->
    <script src="assets/js/global-music-manager.js"></script>

</head>
<body>
    <div class="main-container">
        <!-- 顶部标题栏 -->
        <div class="header">
            <div class="header-title">
                <span>🏪</span>
                市场
            </div>
            <a href="game.html" class="back-btn">
                <span>🔙</span>
                返回
            </a>
        </div>

        <!-- 商城分类导航 -->
        <div class="shop-categories" id="shopCategories">
            <!-- 分类按钮将通过JavaScript生成 -->
        </div>

        <!-- 用户资源显示 -->
        <div class="user-resources">
            <div class="resources-grid">
                <div class="resource-item">
                    <div class="resource-left">
                        <div class="resource-icon">💰</div>
                        <div class="resource-name">金币</div>
                    </div>
                    <div class="resource-value" id="goldCoins">1000</div>
                </div>
                <div class="resource-item">
                    <div class="resource-left">
                        <div class="resource-icon">🔹</div>
                        <div class="resource-name">灵石</div>
                    </div>
                    <div class="resource-value" id="spiritStones">50</div>
                </div>
            </div>
        </div>

        <!-- 商城内容区域 -->
        <div class="shop-content" id="shopContent">
            <div class="loading">正在加载商城数据...</div>
        </div>
    </div>

    <!-- 确认购买弹窗 -->
    <div class="purchase-modal" id="purchaseModal">
        <div class="purchase-modal-content">
            <div class="purchase-title">🛒 确认购买</div>
            
            <div class="purchase-item-info" id="purchaseItemInfo">
                <div class="purchase-item-name" id="purchaseItemName">物品名称</div>
                <div class="purchase-item-effect" id="purchaseItemEffect">物品效果</div>
            </div>
            
            <div class="purchase-cost" id="purchaseCost">
                消耗：<span id="purchaseCostValue">100</span> <span id="purchaseCostCurrency">灵石</span>
            </div>
            
            <div class="purchase-buttons">
                <button class="purchase-button purchase-cancel" onclick="closePurchaseModal()">取消</button>
                <button class="purchase-button purchase-confirm" onclick="confirmPurchase()">确认购买</button>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <script src="assets/js/common-navigation.js"></script>

    <script>
        // 全局变量
        let shopData = {
            categories: [],
            currentCategory: '',
            userResources: {},
            selectedItem: null
        };

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🏪 [商城系统] 页面加载完成，开始初始化...');
            loadShopData();
        });

        // 加载商城数据
        async function loadShopData() {
            try {
                console.log('📡 [API] 正在获取商城分类...');
                
                // 获取商城分类
                const categoriesResponse = await fetch(window.GameConfig ? window.GameConfig.getApiUrl('shop.php?action=get_shop_categories') : '../src/api/shop.php?action=get_shop_categories');
                const categoriesData = await categoriesResponse.json();
                
                if (categoriesData.success) {
                    shopData.categories = categoriesData.categories;
                    generateCategoryButtons();
                    
                    // 默认选择第一个分类
                    if (shopData.categories.length > 0) {
                        selectCategory(shopData.categories[0].id);
                    }
                } else {
                    throw new Error(categoriesData.message || '获取商城分类失败');
                }
                
                // 获取用户资源
                await loadUserResources();
                
            } catch (error) {
                console.error('❌ [商城] 加载商城数据失败:', error);
                showMessage('加载商城数据失败: ' + error.message, 'error');
            }
        }

        // 加载用户资源
        async function loadUserResources() {
            try {
                // 调用商城API获取用户资源
                const response = await fetch(window.GameConfig ? window.GameConfig.getApiUrl('shop.php?action=get_user_resources') : '../src/api/shop.php?action=get_user_resources');
                const data = await response.json();
                
                if (data.success && data.resources) {
                    shopData.userResources = data.resources;
                    updateResourcesDisplay();
                } else {
                    console.warn('获取用户资源失败:', data.message);
                    // 使用默认值
                    shopData.userResources = {
                        spirit_stones: 5000,
                        gold: 10000000
                    };
                    updateResourcesDisplay();
                }
            } catch (error) {
                console.error('❌ [资源] 获取用户资源失败:', error);
                // 使用默认值
                shopData.userResources = {
                    spirit_stones: 5000,
                    gold: 10000000
                };
                updateResourcesDisplay();
            }
        }

        // 生成分类按钮
        function generateCategoryButtons() {
            const categoriesContainer = document.getElementById('shopCategories');
            categoriesContainer.innerHTML = '';
            
            shopData.categories.forEach(category => {
                const categoryBtn = document.createElement('button');
                categoryBtn.className = 'category-btn';
                categoryBtn.onclick = () => selectCategory(category.id);
                
                categoryBtn.innerHTML = `
                    <div class="category-icon">${category.icon}</div>
                    <div class="category-name">${category.name}</div>
                    <div class="category-currency">${category.currency_name}</div>
                `;
                
                categoriesContainer.appendChild(categoryBtn);
            });
        }

        // 选择分类
        async function selectCategory(categoryId) {
            console.log('🏷️ [分类] 选择分类:', categoryId);
            
            // 更新按钮状态
            document.querySelectorAll('.category-btn').forEach((btn, index) => {
                if (shopData.categories[index].id === categoryId) {
                    btn.classList.add('active');
                } else {
                    btn.classList.remove('active');
                }
            });
            
            shopData.currentCategory = categoryId;
            
            // 加载分类商品
            await loadCategoryItems(categoryId);
        }

        // 加载分类商品
        async function loadCategoryItems(categoryId) {
            try {
                const shopContent = document.getElementById('shopContent');
                shopContent.innerHTML = '<div class="loading">正在加载商品...</div>';
                
                console.log('📦 [商品] 正在获取分类商品:', categoryId);
                
                // 将categoryId作为shop_type传递给API
                const response = await fetch(window.GameConfig ? window.GameConfig.getApiUrl(`shop.php?action=get_shop_items&shop_type=${categoryId}`) : `../src/api/shop.php?action=get_shop_items&shop_type=${categoryId}`);
                const data = await response.json();
                
                if (data.success) {
                    generateItemsList(data.items);
                } else {
                    throw new Error(data.message || '获取商品失败');
                }
                
            } catch (error) {
                console.error('❌ [商品] 加载商品失败:', error);
                const shopContent = document.getElementById('shopContent');
                shopContent.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">😞</div>
                        <div>加载商品失败</div>
                        <div style="font-size: 10px; margin-top: 5px;">${error.message}</div>
                    </div>
                `;
            }
        }

        // 生成商品列表
        function generateItemsList(itemsData) {
            const shopContent = document.getElementById('shopContent');
            shopContent.innerHTML = '';
            
            if (!itemsData || itemsData.length === 0) {
                shopContent.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">🛒</div>
                        <div>该分类暂无商品</div>
                    </div>
                `;
                return;
            }
            
            itemsData.forEach(categoryData => {
                const categorySection = document.createElement('div');
                categorySection.className = 'items-category';
                
                categorySection.innerHTML = `
                    <div class="category-header">
                        <div class="category-title">${categoryData.category_name}</div>
                    </div>
                    <div class="items-grid" id="category-${categoryData.category}">
                        <!-- 商品将在这里生成 -->
                    </div>
                `;
                
                shopContent.appendChild(categorySection);
                
                // 生成该分类的商品
                const itemsGrid = categorySection.querySelector('.items-grid');
                categoryData.items.forEach(item => {
                    // 检查是否已达到限购上限，如果是则跳过不显示
                    // 🔧 修复：确保数值比较而非字符串比较
                    const maxPurchase = parseInt(item.max_purchase) || 0;
                    const purchased = parseInt(item.purchased) || 0;
                    const isSoldOut = maxPurchase > 0 && purchased >= maxPurchase;
                    
                    if (isSoldOut) {
                        return; // 跳过已售罄的商品，不显示
                    }
                    
                    const itemCard = createItemCard(item);
                    itemsGrid.appendChild(itemCard);
                    
                    // 🆕 如果是材料类商品，自动加载拥有数量
                    if (item.supports_bulk === true) {
                        updateMaterialOwned(item.id);
                    }
                });
            });
        }

        // 创建商品卡片
        function createItemCard(item) {
            const itemCard = document.createElement('div');
            itemCard.className = 'item-card';
            
            // 注释：由于已达到限购上限的商品不会显示，这里不再需要检查售罄状态
            
            // 购买限制文本
            let purchaseLimitText = '';
            if (item.max_purchase > 0) {
                purchaseLimitText = `限购: ${item.purchased}/${item.max_purchase}`;
            } else {
                purchaseLimitText = '无限购买';
            }
            
            // 等级需求文本
            let levelReqText = '';
            if (item.level_requirement) {
                levelReqText = `<div class="item-level-req">需要等级: ${item.level_requirement}</div>`;
            }
            
            // 货币图标
            const currencyIcon = item.currency === 'spirit_stones' ? '🔹' : '💰';
            const currencyName = item.currency === 'spirit_stones' ? '灵石' : '金币';
            
            // 🆕 检查是否支持批量购买（材料类商品）
            const supportsBulk = item.supports_bulk === true;
            let purchaseSection = '';
            
            if (supportsBulk) {
                // 支持批量购买的商品 - 显示数量选择器
                purchaseSection = `
                    <div class="item-purchase bulk-purchase">
                        <div class="purchase-limit">${purchaseLimitText}</div>
                        <div class="material-owned" id="owned-${item.id}">
                            <span class="owned-label">当前拥有:</span>
                            <span class="owned-value">获取中...</span>
                        </div>
                        <div class="quantity-selector">
                            <button class="qty-btn" onclick="changeQuantity('${item.id}', -1)">-</button>
                            <input type="number" id="qty-${item.id}" value="1" min="1" max="999" class="qty-input" 
                                   onchange="updateQuantityInfo('${item.id}')">
                            <button class="qty-btn" onclick="changeQuantity('${item.id}', 1)">+</button>
                        </div>
                        <div class="quick-qty-buttons">
                            <button class="quick-qty-btn" onclick="setQuantity('${item.id}', 5)">5</button>
                            <button class="quick-qty-btn" onclick="setQuantity('${item.id}', 10)">10</button>
                            <button class="quick-qty-btn" onclick="setQuantity('${item.id}', 50)">50</button>
                            <button class="quick-qty-btn recommended-btn" id="rec-${item.id}" onclick="setRecommendedQuantity('${item.id}')">推荐</button>
                        </div>
                        <div class="quantity-hint" id="hint-${item.id}"></div>
                        <button class="purchase-btn bulk-btn" 
                                onclick="openBulkPurchaseModal('${item.id}', '${item.name}', '${item.effect}', ${item.price}, '${item.currency}')">
                            批量购买
                        </button>
                    </div>
                `;
            } else {
                // 普通商品 - 单个购买
                purchaseSection = `
                    <div class="item-purchase">
                        <div class="purchase-limit">${purchaseLimitText}</div>
                        <button class="purchase-btn" 
                                onclick="openPurchaseModal('${item.id}', '${item.name}', '${item.effect}', ${item.price}, '${item.currency}')">
                            购买
                        </button>
                    </div>
                `;
            }
            
            // 🔧 使用实际道具图片
            const itemImageUrl = item.image_url || item.icon_image || `assets/images/1100.png`;
            
            itemCard.innerHTML = `
                <div class="item-rarity rarity-${item.rarity}">${item.rarity}</div>
                <div class="item-header">
                    <div class="item-info">
                        <div class="item-name">${item.name}</div>
                        <div class="item-description">${item.description}</div>
                        <div class="item-effect">${item.effect}</div>
                        ${levelReqText}
                    </div>
                    <div class="item-icon">
                        <img src="${itemImageUrl}" alt="${item.name}" 
                             style="width: 40px; height: 40px; object-fit: cover; border-radius: 6px; border: 1px solid #d4af37;"
                             onerror="this.src='assets/images/1100.png'">
                    </div>
                </div>
                <div class="item-footer">
                    <div class="item-price">
                        <span class="price-value">${item.price}</span>
                        <span class="price-currency">${currencyIcon}${currencyName}</span>
                    </div>
                    ${purchaseSection}
                </div>
            `;
            
            return itemCard;
        }

        // 更新资源显示
        function updateResourcesDisplay() {
            const goldCoinsEl = document.getElementById('goldCoins');
            const spiritStonesEl = document.getElementById('spiritStones');
            
            if (goldCoinsEl) {
                goldCoinsEl.textContent = formatNumber(shopData.userResources.gold || 0);
            }
            
            if (spiritStonesEl) {
                spiritStonesEl.textContent = formatNumber(shopData.userResources.spirit_stones || 0);
            }
        }

        // 打开购买确认弹窗
        function openPurchaseModal(itemId, itemName, itemEffect, price, currency) {
            console.log('🛒 [购买] 打开购买弹窗:', itemId, itemName, price, currency);
            
            // 检查用户资源是否足够
            const userCurrency = currency === 'spirit_stones' ? shopData.userResources.spirit_stones : shopData.userResources.gold;
            const currencyName = currency === 'spirit_stones' ? '灵石' : '金币';
            const currencyIcon = currency === 'spirit_stones' ? '🔹' : '💰';
            
            if (userCurrency < price) {
                const shortfall = price - userCurrency;
                showMessage(`${currencyIcon}${currencyName}不足！还需要 ${shortfall} 个${currencyName}`, 'error');
                return;
            }
            
            shopData.selectedItem = {
                id: itemId,
                name: itemName,
                effect: itemEffect,
                price: price,
                currency: currency
            };
            
            // 更新弹窗内容
            document.getElementById('purchaseItemName').textContent = itemName;
            document.getElementById('purchaseItemEffect').textContent = itemEffect;
            document.getElementById('purchaseCostValue').textContent = price;
            
            document.getElementById('purchaseCostCurrency').textContent = currencyIcon + currencyName;
            
            // 显示弹窗
            document.getElementById('purchaseModal').style.display = 'flex';
        }

        // 关闭购买弹窗
        function closePurchaseModal() {
            document.getElementById('purchaseModal').style.display = 'none';
            shopData.selectedItem = null;
        }

        // 确认购买
        async function confirmPurchase() {
            if (!shopData.selectedItem) {
                showMessage('请选择要购买的物品', 'error');
                return;
            }
            
            try {
                console.log('💰 [购买] 确认购买:', shopData.selectedItem);
                
                // 🔧 修复：保存当前滚动位置
                const shopContent = document.getElementById('shopContent');
                const savedScrollTop = shopContent.scrollTop;
                console.log('📍 [滚动] 保存当前滚动位置:', savedScrollTop);
                
                const formData = new FormData();
                formData.append('action', 'purchase_item');
                formData.append('item_id', shopData.selectedItem.id);
                formData.append('quantity', shopData.selectedItem.quantity || '1');
                
                const response = await fetch(window.GameConfig ? window.GameConfig.getApiUrl('shop.php') : '../src/api/shop.php', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                // 先关闭弹窗，再显示结果消息
                closePurchaseModal();
                
                if (data.success) {
                    showMessage(data.message, 'success');
                    
                    // 🔧 修复：尝试精细更新，如果失败则使用完整重新加载
                    const usesFineUpdate = await updateAfterPurchase(data);
                    
                    if (!usesFineUpdate) {
                        // 精细更新失败，使用完整重新加载并保持滚动位置
                        console.log('📍 [更新] 精细更新不可用，使用完整重新加载');
                        
                        // 🔧 修复：重新加载当前分类商品，但保持滚动位置
                        await loadCategoryItems(shopData.currentCategory);
                        
                        // 🔧 修复：恢复滚动位置
                        setTimeout(() => {
                            const shopContentAfter = document.getElementById('shopContent');
                            if (shopContentAfter && savedScrollTop > 0) {
                                shopContentAfter.scrollTop = savedScrollTop;
                                console.log('📍 [滚动] 恢复滚动位置:', savedScrollTop);
                                
                                // 🔧 新增：如果滚动位置恢复失败，尝试平滑滚动
                                if (Math.abs(shopContentAfter.scrollTop - savedScrollTop) > 10) {
                                    console.log('📍 [滚动] 直接设置失败，尝试平滑滚动');
                                    shopContentAfter.scrollTo({
                                        top: savedScrollTop,
                                        behavior: 'smooth'
                                    });
                                }
                            }
                        }, 100); // 延迟100ms确保DOM更新完成
                    } else {
                        console.log('✅ [更新] 精细更新完成，无需重新加载列表');
                    }
                } else {
                    showMessage(data.message, 'error');
                }
                
            } catch (error) {
                console.error('❌ [购买] 购买失败:', error);
                closePurchaseModal();
                showMessage('购买失败: ' + error.message, 'error');
            }
        }
        
        // 🆕 批量购买相关函数
        
        // 材料信息缓存
        let materialInfoCache = {};
        
        // 获取材料信息 - 简化版本，不调用API
        async function getMaterialInfo(itemId) {
            if (materialInfoCache[itemId]) {
                return materialInfoCache[itemId];
            }
            
            // 返回默认的材料信息，不调用API
            const defaultInfo = {
                total_owned: 0,
                slot_count: 0,
                max_stack: 99,
                recommended_buy: 10
            };
            
            materialInfoCache[itemId] = defaultInfo;
            return defaultInfo;
        }
        
        // 更新材料拥有数量显示 - 简化版本
        async function updateMaterialOwned(itemId) {
            const ownedEl = document.querySelector(`#owned-${itemId} .owned-value`);
            
            if (ownedEl) {
                // 显示默认值，不显示具体拥有数量
                ownedEl.textContent = '0个';
                
                // 隐藏推荐按钮
                const recBtn = document.getElementById(`rec-${itemId}`);
                if (recBtn) {
                    recBtn.style.display = 'none';
                }
            }
        }
        
        // 设置推荐数量
        async function setRecommendedQuantity(itemId) {
            const info = await getMaterialInfo(itemId);
            if (info && info.recommended_buy > 0) {
                setQuantity(itemId, info.recommended_buy);
            }
        }
        
        // 更新数量信息和提示
        async function updateQuantityInfo(itemId) {
            const qtyInput = document.getElementById(`qty-${itemId}`);
            const hintEl = document.getElementById(`hint-${itemId}`);
            
            if (!qtyInput || !hintEl) return;
            
            const quantity = parseInt(qtyInput.value) || 1;
            const info = await getMaterialInfo(itemId);
            
            if (info) {
                const afterPurchase = info.total_owned + quantity;
                const newSlots = Math.ceil(afterPurchase / info.max_stack) - info.slot_count;
                
                let hintText = '';
                if (newSlots > 0) {
                    hintText = `购买后将占用 ${Math.ceil(afterPurchase / info.max_stack)} 个背包位置`;
                    if (newSlots > 0) {
                        hintText += ` (新增${newSlots}个)`;
                    }
                } else {
                    hintText = `购买后总共 ${afterPurchase} 个，占用 ${info.slot_count} 个位置`;
                }
                
                hintEl.textContent = hintText;
                hintEl.className = 'quantity-hint';
            }
        }
        
        // 调整商品数量
        function changeQuantity(itemId, delta) {
            const qtyInput = document.getElementById(`qty-${itemId}`);
            if (!qtyInput) return;
            
            let currentQty = parseInt(qtyInput.value) || 1;
            currentQty += delta;
            
            if (currentQty < 1) currentQty = 1;
            if (currentQty > 999) currentQty = 999;
            
            qtyInput.value = currentQty;
            updateQuantityInfo(itemId);
        }
        
        // 设置指定数量
        function setQuantity(itemId, quantity) {
            const qtyInput = document.getElementById(`qty-${itemId}`);
            if (!qtyInput) return;
            
            qtyInput.value = Math.max(1, Math.min(999, quantity));
            updateQuantityInfo(itemId);
        }
        
        // 打开批量购买弹窗
        function openBulkPurchaseModal(itemId, itemName, itemEffect, unitPrice, currency) {
            console.log('🛒 [批量购买] 打开批量购买弹窗:', itemId, itemName, unitPrice, currency);
            
            const qtyInput = document.getElementById(`qty-${itemId}`);
            const quantity = parseInt(qtyInput.value) || 1;
            const totalPrice = unitPrice * quantity;
            
            // 检查用户资源是否足够 (仅检查金币，移除错误的堆叠限制)
            const userCurrency = currency === 'spirit_stones' ? shopData.userResources.spirit_stones : shopData.userResources.gold;
            const currencyName = currency === 'spirit_stones' ? '灵石' : '金币';
            const currencyIcon = currency === 'spirit_stones' ? '🔹' : '💰';
            
            if (userCurrency < totalPrice) {
                const shortfall = totalPrice - userCurrency;
                showMessage(`${currencyIcon}${currencyName}不足！还需要 ${shortfall} 个${currencyName}`, 'error');
                return;
            }
            
            shopData.selectedItem = {
                id: itemId,
                name: itemName,
                effect: itemEffect,
                price: unitPrice,
                quantity: quantity,
                totalPrice: totalPrice,
                currency: currency
            };
            
            // 更新弹窗内容
            document.getElementById('purchaseItemName').textContent = `${itemName} x${quantity}`;
            document.getElementById('purchaseItemEffect').textContent = itemEffect;
            document.getElementById('purchaseCostValue').textContent = totalPrice;
            document.getElementById('purchaseCostCurrency').textContent = currencyIcon + currencyName;
            
            // 显示弹窗
            document.getElementById('purchaseModal').style.display = 'flex';
        }

        // 格式化数字显示
        function formatNumber(num) {
            if (num >= 100000000) {
                return (num / 100000000).toFixed(1) + '亿';
            } else if (num >= 10000) {
                return (num / 10000).toFixed(1) + '万';
            } else {
                return num.toString();
            }
        }

        // 显示消息提示
        function showMessage(text, type = 'info') {
            const existingMessage = document.querySelector('.message');
            if (existingMessage) {
                existingMessage.remove();
            }
            
            const message = document.createElement('div');
            message.className = `message ${type}`;
            message.textContent = text;
            document.body.appendChild(message);
            
            // 根据消息类型设置不同的显示时间
            let displayTime = 3000; // 默认3秒
            if (type === 'error') {
                displayTime = 4000; // 错误消息显示4秒
            } else if (type === 'success') {
                displayTime = 2500; // 成功消息显示2.5秒
            }
            
            setTimeout(() => {
                if (message.parentNode) {
                    message.style.opacity = '0';
                    message.style.transform = 'translate(-50%, -60%)';
                    setTimeout(() => {
                        if (message.parentNode) {
                            message.parentNode.removeChild(message);
                        }
                    }, 300);
                }
            }, displayTime);
        }

        // 点击弹窗外部关闭
        document.addEventListener('click', function(event) {
            const purchaseModal = document.getElementById('purchaseModal');
            
            if (event.target === purchaseModal) {
                closePurchaseModal();
            }
        });

        // ESC键关闭弹窗
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const purchaseModal = document.getElementById('purchaseModal');
                
                if (purchaseModal.style.display === 'flex') {
                    closePurchaseModal();
                }
            }
        });

        // 🔧 新增：更精细的商品状态更新方法
        function updateItemPurchaseStatus(itemId, newPurchasedCount, maxPurchase) {
            // 查找对应的商品卡片
            const itemCards = document.querySelectorAll('.item-card');
            
            itemCards.forEach(card => {
                const purchaseBtn = card.querySelector(`[onclick*="'${itemId}'"]`);
                if (purchaseBtn) {
                    // 找到了对应的商品卡片
                    const purchaseLimitEl = card.querySelector('.purchase-limit');
                    
                    if (purchaseLimitEl && maxPurchase > 0) {
                        // 更新限购显示
                        purchaseLimitEl.textContent = `限购: ${newPurchasedCount}/${maxPurchase}`;
                        
                        // 检查是否达到限购上限
                        if (newPurchasedCount >= maxPurchase) {
                            // 达到限购上限，隐藏整个商品卡片
                            card.style.transition = 'opacity 0.3s ease-out';
                            card.style.opacity = '0';
                            setTimeout(() => {
                                if (card.parentNode) {
                                    card.parentNode.removeChild(card);
                                }
                            }, 300);
                            console.log('🚫 [商品] 商品已达限购上限，已隐藏:', itemId);
                        }
                    }
                    
                    console.log('✅ [商品] 更新商品购买状态:', itemId, `${newPurchasedCount}/${maxPurchase}`);
                }
            });
        }

        // 🔧 新增：尝试精细更新而不是完整重新加载
        async function updateAfterPurchase(purchaseResult) {
            // 更新用户资源
            await loadUserResources();
            
            // 如果API返回了商品的新状态，尝试精细更新
            if (purchaseResult.item_status) {
                const { item_id, purchased_count, max_purchase } = purchaseResult.item_status;
                updateItemPurchaseStatus(item_id, purchased_count, max_purchase);
                console.log('✅ [更新] 使用精细更新模式');
                return true; // 表示使用了精细更新
            }
            
            return false; // 表示需要完整重新加载
        }
    </script>
</body>
</html> 