/**
 * 天材地宝使用界面样式
 * 用于灵根提升弹窗的样式定义
 * 作者：一念修仙前端开发团队
 * 日期：2024年12月19日
 */

/* 天材地宝使用弹窗 */
.spiritual-material-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: flex-start;
    z-index: 10000;
    backdrop-filter: blur(5px);
    padding: 20px 0 80px 0; /* 上20px，下80px（为底部导航留空间） */
}

.spiritual-material-content {
    background: linear-gradient(135deg, #2c1810 0%, #1a0f08 100%);
    border: 2px solid #d4af37;
    border-radius: 12px;
    width: 85%;
    max-width: 480px;
    height: calc(100vh - 100px); /* 总高度减去上下padding */
    max-height: 600px;
    box-shadow: 0 10px 30px rgba(212, 175, 55, 0.3);
    position: relative;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    margin-top: 20px; /* 确保从顶部padding开始 */
}

.spiritual-material-header {
    background: linear-gradient(135deg, #d4af37 0%, #b8941f 100%);
    color: #1a0f08;
    padding: 10px 16px;
    border-radius: 10px 10px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: bold;
    font-size: 15px;
    flex-shrink: 0;
}

.spiritual-material-body {
    flex: 1;
    padding: 12px 16px;
    color: #f4f1e8;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 天材地宝分类标签 */
.material-category-tabs {
    display: flex;
    margin-bottom: 12px;
    border-bottom: 1px solid #d4af37;
    flex-shrink: 0;
}

.category-tab {
    flex: 1;
    padding: 6px 4px;
    text-align: center;
    background: transparent;
    border: none;
    color: #d4af37;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 2px solid transparent;
    font-size: 12px;
}

.category-tab.active {
    background: rgba(212, 175, 55, 0.1);
    border-bottom-color: #d4af37;
    color: #f4f1e8;
}

.category-tab:hover {
    background: rgba(212, 175, 55, 0.05);
}

/* 天材地宝列表容器 - 可滚动 */
.material-list-container {
    flex: 1;
    overflow-y: auto;
    padding-right: 5px;
}

/* 天材地宝列表 */
.material-list {
    display: grid;
    grid-template-columns: 1fr;
    gap: 6px;
}

.material-item {
    background: linear-gradient(135deg, #3d2817 0%, #2a1a0f 100%);
    border: 1px solid #8b6914;
    border-radius: 6px;
    padding: 8px 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    min-height: 40px; /* 固定最小高度 */
}

.material-item:hover {
    border-color: #d4af37;
    box-shadow: 0 2px 8px rgba(212, 175, 55, 0.2);
    transform: translateY(-1px);
}

.material-item.selected {
    border-color: #d4af37;
    background: linear-gradient(135deg, #4a3220 0%, #332015 100%);
    box-shadow: 0 0 12px rgba(212, 175, 55, 0.3);
}

.material-header {
    display: flex;
    align-items: center;
    flex: 1;
}

.material-icon {
    width: 28px;
    height: 28px;
    border-radius: 4px;
    margin-right: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    background: linear-gradient(135deg, #d4af37 0%, #b8941f 100%);
    flex-shrink: 0;
}

.material-info {
    flex: 1;
    min-width: 0; /* 允许文字截断 */
}

.material-name {
    font-size: 13px;
    font-weight: bold;
    color: #f4f1e8;
    margin-bottom: 2px;
    line-height: 1.2;
}

.material-quantity {
    font-size: 10px;
    color: #d4af37;
    line-height: 1;
}

.material-effect {
    font-size: 11px;
    color: #a0a0a0;
    margin: 2px 0;
    line-height: 1.1;
}

.material-usage {
    font-size: 10px;
    color: #ff6b6b;
    background: rgba(255, 107, 107, 0.1);
    padding: 1px 4px;
    border-radius: 2px;
    display: inline-block;
    line-height: 1.2;
}

.material-usage.usage-full {
    color: #e74c3c;
    background: rgba(231, 76, 60, 0.2);
    font-weight: bold;
}

.material-item.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.material-item.disabled:hover {
    border-color: #8b6914;
    box-shadow: none;
    transform: none;
}

/* 使用控制面板 - 固定在底部 */
.usage-controls-footer {
    background: linear-gradient(135deg, #3d2817 0%, #2a1a0f 100%);
    border-top: 1px solid #d4af37;
    padding: 8px 12px;
    flex-shrink: 0;
}

.usage-title {
    font-size: 12px;
    font-weight: bold;
    color: #d4af37;
    margin-bottom: 6px;
    text-align: center;
}

.selected-material-info {
    background: rgba(212, 175, 55, 0.1);
    border-radius: 4px;
    padding: 6px 8px;
    margin-bottom: 8px;
}

.selected-material-name {
    font-size: 12px;
    font-weight: bold;
    color: #f4f1e8;
    margin-bottom: 2px;
    line-height: 1.2;
}

.selected-material-effect {
    font-size: 11px;
    color: #d4af37;
    margin-bottom: 2px;
    line-height: 1.1;
}

.selected-material-limit {
    font-size: 10px;
    color: #ff6b6b;
    line-height: 1.1;
}

.usage-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
}

.quantity-selector {
    display: flex;
    align-items: center;
    gap: 6px;
}

.quantity-btn {
    width: 24px;
    height: 24px;
    border: 1px solid #d4af37;
    background: rgba(212, 175, 55, 0.1);
    color: #d4af37;
    border-radius: 3px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
}

.quantity-btn:hover {
    background: rgba(212, 175, 55, 0.2);
    border-color: #f4f1e8;
}

.quantity-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: rgba(139, 105, 20, 0.1);
    border-color: #8b6914;
}

.quantity-input {
    width: 40px;
    height: 24px;
    text-align: center;
    border: 1px solid #d4af37;
    background: rgba(212, 175, 55, 0.1);
    color: #f4f1e8;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
}

.quantity-input:focus {
    outline: none;
    border-color: #f4f1e8;
    background: rgba(212, 175, 55, 0.2);
}

.use-material-btn {
    flex: 1;
    max-width: 120px;
    padding: 6px 12px;
    background: linear-gradient(135deg, #d4af37 0%, #b8941f 100%);
    color: #1a0f08;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.use-material-btn:hover {
    background: linear-gradient(135deg, #f4f1e8 0%, #d4af37 100%);
    transform: translateY(-1px);
}

.use-material-btn:disabled {
    background: rgba(139, 105, 20, 0.3);
    color: rgba(244, 241, 232, 0.5);
    cursor: not-allowed;
    transform: none;
}

/* 品质颜色 */
.rarity-common { border-left: 4px solid #ffffff; }
.rarity-uncommon { border-left: 4px solid #32cd32; }
.rarity-rare { border-left: 4px solid #1e90ff; }
.rarity-epic { border-left: 4px solid #9370db; }
.rarity-legendary { border-left: 4px solid #ffd700; }

/* 五行元素颜色 */
.element-metal { color: #c0c0c0; }
.element-wood { color: #32cd32; }
.element-water { color: #1e90ff; }
.element-fire { color: #ff4500; }
.element-earth { color: #daa520; }

/* 空状态 */
.empty-materials {
    text-align: center;
    padding: 40px 20px;
    color: #888;
}

.empty-materials-icon {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
}

.empty-materials-text {
    font-size: 16px;
    margin-bottom: 10px;
}

.empty-materials-hint {
    font-size: 14px;
    color: #666;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .spiritual-material-modal {
        padding: 15px 0 70px 0; /* 移动端：上15px，下70px */
    }
    
    .spiritual-material-content {
        width: 95%;
        height: calc(100vh - 85px); /* 移动端总高度减去上下padding */
        max-height: 500px;
        margin-top: 15px; /* 移动端从顶部padding开始 */
    }
    
    .spiritual-material-header {
        padding: 8px 12px;
        font-size: 14px;
    }
    
    .spiritual-material-body {
        padding: 10px 12px;
    }

    .material-list {
        gap: 4px;
    }

    .material-item {
        padding: 6px 8px;
        min-height: 36px;
    }
    
    .material-icon {
        width: 24px;
        height: 24px;
        font-size: 12px;
        margin-right: 6px;
    }

    .material-category-tabs {
        margin-bottom: 10px;
    }

    .category-tab {
        padding: 5px 6px;
        font-size: 11px;
    }

    .usage-controls-footer {
        padding: 6px 10px;
    }
    
    .usage-title {
        font-size: 11px;
        margin-bottom: 4px;
    }
    
    .selected-material-info {
        padding: 5px 6px;
        margin-bottom: 6px;
    }
    
    .usage-actions {
        flex-direction: column;
        gap: 6px;
    }
    
    .quantity-selector {
        justify-content: center;
    }
    
    .quantity-btn {
        width: 22px;
        height: 22px;
        font-size: 12px;
    }
    
    .quantity-input {
        width: 36px;
        height: 22px;
        font-size: 11px;
    }
    
    .use-material-btn {
        max-width: none;
        width: 100%;
        padding: 8px 12px;
        font-size: 11px;
    }
}

/* 加载动画 */
.loading-materials {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px;
    color: #d4af37;
}

.loading-spinner {
    width: 30px;
    height: 30px;
    border: 3px solid rgba(212, 175, 55, 0.3);
    border-top: 3px solid #d4af37;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 成功提示动画 */
.usage-success {
    background: rgba(46, 204, 113, 0.1);
    border: 1px solid #2ecc71;
    color: #2ecc71;
    padding: 15px;
    border-radius: 8px;
    margin-top: 15px;
    text-align: center;
    animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
} 