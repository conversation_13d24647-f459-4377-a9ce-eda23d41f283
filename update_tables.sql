-- 修改game_items表，添加set_id字段
ALTER TABLE game_items
ADD COLUMN set_id int(11) DEFAULT NULL COMMENT '套装ID，关联game_item_sets表' AFTER item_type,
ADD INDEX idx_set_id (set_id);

-- 删除旧的套装表结构
DROP TABLE IF EXISTS game_item_sets;

-- 创建新的套装表结构
CREATE TABLE game_item_sets (
    id int(11) NOT NULL AUTO_INCREMENT COMMENT '套装ID',
    set_name varchar(100) NOT NULL COMMENT '套装名称',
    set_description text DEFAULT NULL COMMENT '套装描述',
    set_icon varchar(200) DEFAULT NULL COMMENT '套装图标路径',
    min_pieces int(11) DEFAULT 2 COMMENT '最少激活件数',
    max_pieces int(11) DEFAULT 6 COMMENT '最多激活件数',
    set_bonus_2 json NOT NULL COMMENT '2件套效果',
    set_bonus_4 json NOT NULL COMMENT '4件套效果',
    set_bonus_6 json NOT NULL COMMENT '6件套效果',
    rarity enum('common','uncommon','rare','epic','legendary','mythic') DEFAULT 'common' COMMENT '套装品质',
    level_requirement int(11) DEFAULT 1 COMMENT '等级需求',
    is_active tinyint(1) DEFAULT 1 COMMENT '是否启用',
    created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    KEY idx_set_name (set_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='装备套装表'; 