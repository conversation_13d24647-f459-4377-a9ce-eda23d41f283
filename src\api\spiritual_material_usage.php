<?php
/**
 * 天材地宝使用系统API
 * 处理天材地宝的使用、灵根提升、使用限制检查等功能
 * 作者：一念修仙装备系统开发团队
 * 日期：2024年12月19日
 */

require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/five_elements_spiritual_root.php';

// 设置JSON响应头
setJsonResponse();

// 检查维护模式
if (isMaintenanceMode()) {
    echo json_encode([
        'success' => false,
        'message' => '游戏正在维护中，请稍后再试',
        'maintenance' => true
    ]);
    exit;
}

// 记录API调用（如果开启了调试）
if (DEBUG_LOG_API_CALLS) {
    writeLog("API调用: spiritual_material_usage.php", 'DEBUG', 'api.log');
}

try {
    // 检查用户是否登录
    if (!isLoggedIn()) {
        echo json_encode([
            'success' => false,
            'message' => '请先登录'
        ]);
        exit;
    }
    
    $user = getCurrentUser();
    if (!$user) {
        echo json_encode([
            'success' => false,
            'message' => '获取用户信息失败'
        ]);
        exit;
    }
    
    $userId = $user['id'];
    $characterId = $user['character_id'];
    
    if (!$characterId) {
        echo json_encode([
            'success' => false,
            'message' => '未找到角色信息，请先创建角色'
        ]);
        exit;
    }
    
    $action = isset($_GET['action']) ? $_GET['action'] : (isset($_POST['action']) ? $_POST['action'] : '');
    
    $pdo = getDatabase();
    
    switch ($action) {
        case 'get_spiritual_materials':
            getSpiritualMaterials($pdo, $characterId);
            break;
            
        case 'use_spiritual_material':
        case 'use_material':
            useSpiritualMaterial($pdo, $characterId, $_POST);
            break;
            
        case 'get_usage_records':
            getUsageRecords($pdo, $characterId);
            break;
            
        default:
            echo json_encode([
                'success' => false,
                'message' => '无效的操作'
            ]);
            break;
    }
    
} catch (Exception $e) {
    error_log("天材地宝使用API错误: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => '系统错误，请稍后重试'
    ]);
}

/**
 * 获取角色背包中的天材地宝
 */
function getSpiritualMaterials($pdo, $characterId) {
    try {
        // 获取背包中的天材地宝
        $stmt = $pdo->prepare("
            SELECT 
                ui.id as inventory_id,
                ui.quantity,
                gi.id as item_id,
                gi.item_name,
                gi.item_code,
                gi.description,
                gi.rarity,
                gi.icon_image,
                gi.special_effects
            FROM user_inventories ui
            JOIN game_items gi ON ui.item_id = gi.id
            WHERE ui.character_id = ? 
                AND gi.item_subtype = 'spiritual_material'
                AND ui.quantity > 0
            ORDER BY gi.rarity DESC, gi.item_name ASC
        ");
        $stmt->execute([$characterId]);
        $materials = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 解析天材地宝的特效信息
        foreach ($materials as &$material) {
            if ($material['special_effects']) {
                $effects = json_decode($material['special_effects'], true);
                if (isset($effects['spiritual_root_boost'])) {
                    $material['boost_type'] = $effects['spiritual_root_boost']['type'];
                    $material['boost_value'] = $effects['spiritual_root_boost']['value'];
                }
            }
        }
        
        // 获取使用记录
        $usageRecords = getUsageRecordsInternal($pdo, $characterId);
        
        echo json_encode([
            'success' => true,
            'message' => '获取天材地宝列表成功',
            'data' => [
                'materials' => $materials,
                'usage_records' => $usageRecords
            ]
        ]);
        
    } catch (Exception $e) {
        throw new Exception('获取天材地宝列表失败: ' . $e->getMessage());
    }
}

/**
 * 使用天材地宝提升灵根
 */
function useSpiritualMaterial($pdo, $characterId, $postData) {
    try {
        $inventoryId = intval(isset($postData['inventory_id']) ? $postData['inventory_id'] : 0);
        $quantity = intval(isset($postData['quantity']) ? $postData['quantity'] : 1);
        
        if ($inventoryId <= 0 || $quantity <= 0) {
            throw new Exception('参数错误');
        }
        
        $pdo->beginTransaction();
        
        // 获取物品信息
        $stmt = $pdo->prepare("
            SELECT 
                ui.quantity as current_quantity,
                gi.item_name,
                gi.item_code,
                gi.special_effects
            FROM user_inventories ui
            JOIN game_items gi ON ui.item_id = gi.id
            WHERE ui.id = ? AND ui.character_id = ?
        ");
        $stmt->execute([$inventoryId, $characterId]);
        $item = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$item) {
            throw new Exception('物品不存在或不属于您');
        }
        
        if ($item['current_quantity'] < $quantity) {
            throw new Exception('物品数量不足');
        }
        
        // 解析天材地宝效果
        $effects = json_decode($item['special_effects'], true);
        if (!isset($effects['spiritual_root_boost'])) {
            throw new Exception('该物品不是有效的天材地宝');
        }
        
        $boostType = $effects['spiritual_root_boost']['type'];
        $boostValue = $effects['spiritual_root_boost']['value'];
        
        // 检查使用限制
        $usageRecords = getUsageRecordsInternal($pdo, $characterId);
        $currentUsage = 0;
        
        foreach ($usageRecords as $record) {
            if ($record['item_code'] === $item['item_code']) {
                $currentUsage = intval($record['usage_count']);
                break;
            }
        }
        
        $maxUsage = 10; // 每种天材地宝最多使用10个
        if ($currentUsage + $quantity > $maxUsage) {
            $remaining = $maxUsage - $currentUsage;
            throw new Exception("该天材地宝最多使用{$maxUsage}个，您已使用{$currentUsage}个，最多还能使用{$remaining}个");
        }
        
        // 获取当前灵根数据
        $currentRoots = FiveElementsSpiritualRootSystem::loadRootsFromDatabase($pdo, $characterId);
        if (!$currentRoots) {
            throw new Exception('获取灵根数据失败');
        }
        
        // 计算提升后的灵根值
        $totalBoost = $boostValue * $quantity;
        $newValue = $currentRoots[$boostType]['value'] + $totalBoost;
        
        // 更新灵根数据
        $fieldName = $boostType . '_affinity';
        $stmt = $pdo->prepare("UPDATE characters SET {$fieldName} = ? WHERE id = ?");
        $stmt->execute([$newValue, $characterId]);
        
        // 扣除物品数量
        $stmt = $pdo->prepare("UPDATE user_inventories SET quantity = quantity - ? WHERE id = ?");
        $stmt->execute([$quantity, $inventoryId]);
        
        // 清理数量为0的物品
        $stmt = $pdo->prepare("DELETE FROM user_inventories WHERE id = ? AND quantity <= 0");
        $stmt->execute([$inventoryId]);
        
        // 更新使用记录
        updateUsageRecord($pdo, $characterId, $item['item_code'], $quantity);
        
        $pdo->commit();
        
        // 获取更新后的灵根数据
        $updatedRoots = FiveElementsSpiritualRootSystem::loadRootsFromDatabase($pdo, $characterId);
        
        echo json_encode([
            'success' => true,
            'message' => "成功使用{$quantity}个{$item['item_name']}，" . getElementName($boostType) . "灵根提升{$totalBoost}点",
            'data' => [
                'boost_type' => $boostType,
                'boost_value' => $totalBoost,
                'old_value' => $currentRoots[$boostType]['value'],
                'new_value' => $updatedRoots[$boostType]['value'],
                'updated_roots' => $updatedRoots
            ]
        ]);
        
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        throw new Exception('使用天材地宝失败: ' . $e->getMessage());
    }
}

/**
 * 获取使用记录
 */
function getUsageRecords($pdo, $characterId) {
    try {
        $records = getUsageRecordsInternal($pdo, $characterId);
        
        echo json_encode([
            'success' => true,
            'message' => '获取使用记录成功',
            'data' => $records
        ]);
        
    } catch (Exception $e) {
        throw new Exception('获取使用记录失败: ' . $e->getMessage());
    }
}

/**
 * 内部函数：获取使用记录
 */
function getUsageRecordsInternal($pdo, $characterId) {
    // 获取角色的使用记录
    $stmt = $pdo->prepare("SELECT spiritual_root_usage FROM characters WHERE id = ?");
    $stmt->execute([$characterId]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$result || !$result['spiritual_root_usage']) {
        return [];
    }
    
    $usageData = json_decode($result['spiritual_root_usage'], true);
    if (!$usageData) {
        return [];
    }
    
    $records = [];
    foreach ($usageData as $itemCode => $count) {
        $records[] = [
            'item_code' => $itemCode,
            'usage_count' => $count
        ];
    }
    
    return $records;
}

/**
 * 更新使用记录
 */
function updateUsageRecord($pdo, $characterId, $itemCode, $quantity) {
    // 获取当前使用记录
    $stmt = $pdo->prepare("SELECT spiritual_root_usage FROM characters WHERE id = ?");
    $stmt->execute([$characterId]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $usageData = [];
    if ($result && $result['spiritual_root_usage']) {
        $usageData = json_decode($result['spiritual_root_usage'], true);
        if (!$usageData) {
            $usageData = [];
        }
    }
    
    // 更新使用次数
    if (!isset($usageData[$itemCode])) {
        $usageData[$itemCode] = 0;
    }
    $usageData[$itemCode] += $quantity;
    
    // 保存更新后的记录
    $stmt = $pdo->prepare("UPDATE characters SET spiritual_root_usage = ? WHERE id = ?");
    $stmt->execute([json_encode($usageData), $characterId]);
}

/**
 * 获取元素中文名称
 */
function getElementName($elementType) {
    $names = [
        'metal' => '金',
        'wood' => '木',
        'water' => '水',
        'fire' => '火',
        'earth' => '土'
    ];
    
    return isset($names[$elementType]) ? $names[$elementType] : $elementType;
}

?> 