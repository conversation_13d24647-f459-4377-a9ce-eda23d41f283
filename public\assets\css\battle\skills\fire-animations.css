/**
 * 火法技能动画样式
 * 包含火球术等所有火法技能的动画效果
 */

/* 🔥 火球术技能样式 */
.fireball-container {
    position: absolute;
    pointer-events: none;
    z-index: 200;
}

/* 蓄力热浪效果 */
.fireball-heatwave {
    position: absolute;
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: radial-gradient(circle,
        transparent 30%,
        rgba(255, 100, 50, 0.3) 50%,
        rgba(255, 150, 100, 0.2) 70%,
        rgba(255, 200, 150, 0.1) 90%,
        transparent 100%
    );
    transform: translate(-50%, -50%);
    animation: fireball-heatwave-pulse 0.8s ease-out forwards;
    -webkit-filter: blur(3px);
    filter: blur(3px);
}

@keyframes fireball-heatwave-pulse {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0;
    }
    40% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
    80% {
        transform: translate(-50%, -50%) scale(1.8);
        opacity: 0.6;
    }
    100% {
        transform: translate(-50%, -50%) scale(2.5);
        opacity: 0;
    }
}

/* 蓄力容器样式 */
.fireball-charge-container {
    position: absolute;
    transform: translate(-50%, -50%);
    pointer-events: none;
    z-index: 195;
}

.fireball-charge-particle {
    position: absolute;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: radial-gradient(circle, 
        rgba(255, 100, 50, 1) 0%, 
        rgba(255, 150, 100, 0.8) 60%, 
        transparent 100%);
    animation: fireball-charge-particle-move 1.2s ease-out infinite;
    -webkit-filter: drop-shadow(0 0 8px rgba(255, 100, 50, 0.9));
    filter: drop-shadow(0 0 8px rgba(255, 100, 50, 0.9));
}

@keyframes fireball-charge-particle-move {
    0% {
        transform: translate(var(--chargeX), var(--chargeY)) scale(0.5);
        opacity: 0;
    }
    20% {
        transform: translate(calc(var(--chargeX) * 0.7), calc(var(--chargeY) * 0.7)) scale(1);
        opacity: 1;
    }
    80% {
        transform: translate(calc(var(--chargeX) * 0.2), calc(var(--chargeY) * 0.2)) scale(1.2);
        opacity: 0.8;
    }
    100% {
        transform: translate(0, 0) scale(0);
        opacity: 0;
    }
}

.fireball-charge-ring {
    position: absolute;
    width: 60px;
    height: 60px;
    border: 3px solid rgba(255, 100, 50, 0.8);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: fireball-charge-ring-expand 1.6s ease-out forwards;
    -webkit-filter: drop-shadow(0 0 15px rgba(255, 100, 50, 0.9));
    filter: drop-shadow(0 0 15px rgba(255, 100, 50, 0.9));
}

@keyframes fireball-charge-ring-expand {
    0% {
        transform: translate(-50%, -50%) scale(0) rotate(0deg);
        opacity: 0;
        border-color: rgba(255, 100, 50, 1);
    }
    30% {
        transform: translate(-50%, -50%) scale(0.8) rotate(150deg);
        opacity: 1;
        border-color: rgba(255, 150, 100, 0.9);
    }
    70% {
        transform: translate(-50%, -50%) scale(1.5) rotate(280deg);
        opacity: 0.7;
        border-color: rgba(255, 200, 150, 0.7);
    }
    100% {
        transform: translate(-50%, -50%) scale(2.2) rotate(360deg);
        opacity: 0;
        border-color: rgba(255, 220, 180, 0.4);
    }
}

/* 火球本体样式 - 完全对标原版 */
.fireball {
    position: absolute;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: radial-gradient(circle at 30% 30%,
        rgba(255, 255, 255, 1) 0%,
        rgba(255, 200, 100, 1) 15%,
        rgba(255, 150, 50, 1) 35%,
        rgba(255, 100, 30, 1) 60%,
        rgba(200, 50, 20, 0.9) 80%,
        rgba(150, 30, 10, 0.7) 100%
    );
    transform: translate(-50%, -50%);
    animation: fireball-core-pulse 0.3s ease-in-out infinite alternate;
    -webkit-filter: drop-shadow(0 0 15px rgba(255, 100, 50, 0.9)); 
    filter: drop-shadow(0 0 15px rgba(255, 100, 50, 0.9))
            drop-shadow(0 0 25px rgba(255, 150, 100, 0.6));
    
    /* 火焰尾迹效果 */
    box-shadow: 
        0 0 0 5px rgba(255, 150, 100, 0.6),
        0 0 0 10px rgba(255, 200, 150, 0.4),
        0 0 0 15px rgba(255, 220, 180, 0.2);
}

@keyframes fireball-core-pulse {
    0% {
        transform: translate(-50%, -50%) scale(1);
        -webkit-filter: drop-shadow(0 0 15px rgba(255, 100, 50, 0.9)); 
        filter: drop-shadow(0 0 15px rgba(255, 100, 50, 0.9))
                drop-shadow(0 0 25px rgba(255, 150, 100, 0.6));
    }
    100% {
        transform: translate(-50%, -50%) scale(1.1);
        -webkit-filter: drop-shadow(0 0 20px rgba(255, 100, 50, 1)); 
        filter: drop-shadow(0 0 20px rgba(255, 100, 50, 1))
                drop-shadow(0 0 35px rgba(255, 150, 100, 0.8));
    }
}

/* 火球蓄力动画 - 对标原版 */
@keyframes fireball-charge {
    0% {
        transform: translate(-50%, -50%) scale(0.2);
        opacity: 0.3;
        box-shadow: 0 0 5px #ff6b35;
    }
    25% {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 0.6;
        box-shadow: 
            0 0 20px #ff6b35,
            0 0 35px #ff4500,
            0 0 50px #dc143c;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0.8;
        box-shadow: 
            0 0 30px #ff6b35,
            0 0 50px #ff4500,
            0 0 70px #dc143c,
            0 0 90px #8b0000;
    }
    75% {
        transform: translate(-50%, -50%) scale(1.8);
        opacity: 0.9;
        box-shadow: 
            0 0 35px #ff6b35,
            0 0 55px #ff4500,
            0 0 75px #dc143c,
            0 0 95px #8b0000,
            inset 0 0 20px #ffff00;
    }
    100% {
        transform: translate(-50%, -50%) scale(2);
        opacity: 1;
        box-shadow: 
            0 0 40px #ff6b35,
            0 0 60px #ff4500,
            0 0 80px #dc143c,
            0 0 100px #8b0000,
            0 0 120px rgba(255, 107, 53, 0.5),
            inset 0 0 25px #ffff00;
    }
}

/* 火球发射动画 - 对标原版 */
@keyframes fireball-launch {
    0% {
        transform: translate(-50%, -50%) scale(2) translate(0, 0);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(1.5) translate(var(--targetX), var(--targetY));
        opacity: 1;
    }
}

/* 爆炸特效样式 - 完全对标原版 */
.explosion-flash {
    position: absolute;
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: radial-gradient(circle, 
        rgba(255, 255, 255, 1) 0%, 
        rgba(255, 255, 100, 0.9) 20%,
        rgba(255, 150, 50, 0.7) 40%,
        rgba(255, 100, 30, 0.5) 60%,
        transparent 100%);
    transform: translate(-50%, -50%);
    animation: explosion-flash-burst 0.3s ease-out forwards;
    -webkit-filter: blur(2px);
    filter: blur(2px);
    z-index: 210;
}

@keyframes explosion-flash-burst {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
        -webkit-filter: blur(0px) brightness(3);
        filter: blur(0px) brightness(3);
    }
    30% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
        -webkit-filter: blur(1px) brightness(2.5);
        filter: blur(1px) brightness(2.5);
    }
    100% {
        transform: translate(-50%, -50%) scale(2);
        opacity: 0;
        -webkit-filter: blur(4px) brightness(1);
        filter: blur(4px) brightness(1);
    }
}

.explosion-core {
    position: absolute;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: radial-gradient(circle, 
        #ffffff 0%, 
        #ffff00 15%, 
        #ff6b35 35%, 
        #ff4500 55%, 
        #dc143c 75%, 
        transparent 100%
    );
    transform: translate(-50%, -50%);
    animation: explosion-core 0.8s ease-out forwards;
    -webkit-filter: blur(2px);
    filter: blur(2px);
}

@keyframes explosion-core {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
        -webkit-filter: blur(0px) brightness(3);
        filter: blur(0px) brightness(3);
    }
    20% {
        transform: translate(-50%, -50%) scale(1.2);
        opacity: 1;
        -webkit-filter: blur(1px) brightness(2.5);
        filter: blur(1px) brightness(2.5);
    }
    40% {
        transform: translate(-50%, -50%) scale(2);
        opacity: 0.9;
        -webkit-filter: blur(2px) brightness(2);
        filter: blur(2px) brightness(2);
    }
    60% {
        transform: translate(-50%, -50%) scale(3);
        opacity: 0.7;
        -webkit-filter: blur(3px) brightness(1.5);
        filter: blur(3px) brightness(1.5);
    }
    80% {
        transform: translate(-50%, -50%) scale(4);
        opacity: 0.4;
        -webkit-filter: blur(4px) brightness(1);
        filter: blur(4px) brightness(1);
    }
    100% {
        transform: translate(-50%, -50%) scale(5);
        opacity: 0;
        -webkit-filter: blur(5px) brightness(0.5);
        filter: blur(5px) brightness(0.5);
    }
}

/* 冲击波效果 */
.explosion-shockwave {
    position: absolute;
    width: 80px;
    height: 80px;
    border: 3px solid rgba(255, 100, 50, 0.8);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: explosion-shockwave-expand 0.6s ease-out forwards;
    -webkit-filter: drop-shadow(0 0 15px rgba(255, 100, 50, 0.6));
    filter: drop-shadow(0 0 15px rgba(255, 100, 50, 0.6));
}

@keyframes explosion-shockwave-expand {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
        border-color: rgba(255, 100, 50, 1);
    }
    50% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0.8;
        border-color: rgba(255, 150, 100, 0.8);
    }
    100% {
        transform: translate(-50%, -50%) scale(3);
        opacity: 0;
        border-color: rgba(255, 200, 150, 0.3);
    }
}

/* 火焰环效果 */
.explosion-fire-ring {
    position: absolute;
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: conic-gradient(
        rgba(255, 100, 50, 0.8) 0deg,
        rgba(255, 150, 100, 0.6) 60deg,
        rgba(255, 200, 150, 0.4) 120deg,
        rgba(255, 100, 50, 0.8) 180deg,
        rgba(255, 150, 100, 0.6) 240deg,
        rgba(255, 200, 150, 0.4) 300deg,
        rgba(255, 100, 50, 0.8) 360deg
    );
    transform: translate(-50%, -50%);
    animation: explosion-fire-ring-expand 1.2s ease-out forwards;
    -webkit-filter: blur(3px);
    filter: blur(3px);
}

@keyframes explosion-fire-ring-expand {
    0% {
        transform: translate(-50%, -50%) scale(0) rotate(0deg);
        opacity: 1;
    }
    30% {
        transform: translate(-50%, -50%) scale(1) rotate(90deg);
        opacity: 0.9;
    }
    70% {
        transform: translate(-50%, -50%) scale(2) rotate(200deg);
        opacity: 0.5;
    }
    100% {
        transform: translate(-50%, -50%) scale(3.5) rotate(360deg);
        opacity: 0;
    }
}

/* 烟雾效果 */
.explosion-smoke {
    position: absolute;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: radial-gradient(circle,
        rgba(60, 60, 60, 0.8) 0%,
        rgba(80, 80, 80, 0.6) 30%,
        rgba(100, 100, 100, 0.4) 60%,
        rgba(120, 120, 120, 0.2) 80%,
        transparent 100%
    );
    transform: translate(-50%, -50%);
    animation: explosion-smoke-rise 2.5s ease-out forwards;
    -webkit-filter: blur(4px);
    filter: blur(4px);
}

@keyframes explosion-smoke-rise {
    0% {
        transform: translate(-50%, -50%) scale(0.3) rotate(0deg);
        opacity: 0;
    }
    20% {
        transform: translate(-50%, -50%) scale(0.8) rotate(45deg);
        opacity: 0.8;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.2) translateY(-40px) rotate(120deg);
        opacity: 0.6;
    }
    80% {
        transform: translate(-50%, -50%) scale(1.8) translateY(-80px) rotate(200deg);
        opacity: 0.3;
    }
    100% {
        transform: translate(-50%, -50%) scale(2.5) translateY(-120px) rotate(300deg);
        opacity: 0;
    }
}

/* 火花粒子效果 */
.fire-particle {
    position: absolute;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: radial-gradient(circle, #ffff00 0%, #ff6b35 50%, #dc143c 100%);
    box-shadow: 0 0 10px #ff6b35;
    -webkit-filter: drop-shadow(0 0 8px rgba(255, 100, 50, 0.9));
    filter: drop-shadow(0 0 8px rgba(255, 100, 50, 0.9));
}

@keyframes fire-particle {
    0% {
        transform: translate(0, 0) scale(1);
        opacity: 1;
    }
    100% {
        transform: translate(var(--moveX), var(--moveY)) scale(0);
        opacity: 0;
    }
}

/* 热浪扭曲效果 */
.explosion-heat-distortion {
    position: absolute;
    width: 150px;
    height: 150px;
    border-radius: 50%;
    background: radial-gradient(circle,
        transparent 40%,
        rgba(255, 100, 50, 0.1) 50%,
        rgba(255, 150, 100, 0.08) 70%,
        rgba(255, 200, 150, 0.05) 85%,
        transparent 100%
    );
    transform: translate(-50%, -50%);
    animation: heat-distortion-wave 1.5s ease-out forwards;
    -webkit-filter: blur(8px);
    filter: blur(8px);
}

@keyframes heat-distortion-wave {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0;
    }
    30% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
    70% {
        transform: translate(-50%, -50%) scale(2.5);
        opacity: 0.6;
    }
    100% {
        transform: translate(-50%, -50%) scale(4);
        opacity: 0;
    }
}

/* 角色受击动画 - 对标原版 */
@keyframes fire-hit {
    0%, 100% {
        transform: translateX(0) translateY(0);
        -webkit-filter: brightness(1) saturate(1);
        filter: brightness(1) saturate(1);
    }
    10% {
        transform: translateX(-4px) translateY(-3px);
        -webkit-filter: brightness(1.8) saturate(1.6) hue-rotate(15deg);
        filter: brightness(1.8) saturate(1.6) hue-rotate(15deg);
    }
    20% {
        transform: translateX(3px) translateY(2px);
        -webkit-filter: brightness(1.4) saturate(1.3) hue-rotate(10deg);
        filter: brightness(1.4) saturate(1.3) hue-rotate(10deg);
    }
    30% {
        transform: translateX(-3px) translateY(4px);
        -webkit-filter: brightness(2.2) saturate(1.8) hue-rotate(20deg);
        filter: brightness(2.2) saturate(1.8) hue-rotate(20deg);
    }
    40% {
        transform: translateX(4px) translateY(-2px);
        -webkit-filter: brightness(1.6) saturate(1.4) hue-rotate(12deg);
        filter: brightness(1.6) saturate(1.4) hue-rotate(12deg);
    }
    50% {
        transform: translateX(-2px) translateY(-4px);
        -webkit-filter: brightness(2.0) saturate(1.7) hue-rotate(18deg);
        filter: brightness(2.0) saturate(1.7) hue-rotate(18deg);
    }
    60% {
        transform: translateX(3px) translateY(3px);
        -webkit-filter: brightness(1.3) saturate(1.2) hue-rotate(8deg);
        filter: brightness(1.3) saturate(1.2) hue-rotate(8deg);
    }
    70% {
        transform: translateX(-2px) translateY(1px);
        -webkit-filter: brightness(1.7) saturate(1.5) hue-rotate(14deg);
        filter: brightness(1.7) saturate(1.5) hue-rotate(14deg);
    }
    80% {
        transform: translateX(1px) translateY(-1px);
        -webkit-filter: brightness(1.2) saturate(1.1) hue-rotate(6deg);
        filter: brightness(1.2) saturate(1.1) hue-rotate(6deg);
    }
    90% {
        transform: translateX(-1px) translateY(0px);
        -webkit-filter: brightness(1.4) saturate(1.3) hue-rotate(8deg);
        filter: brightness(1.4) saturate(1.3) hue-rotate(8deg);
    }
    95% {
        transform: translateX(0px) translateY(0px);
        -webkit-filter: brightness(1.1) saturate(1.05) hue-rotate(3deg);
        filter: brightness(1.1) saturate(1.05) hue-rotate(3deg);
    }
}

@keyframes fire-shake {
    0%, 100% { 
        transform: translateX(0) translateY(0) rotate(0deg); 
    }
    10% { 
        transform: translateX(-4px) translateY(-3px) rotate(-2deg); 
    }
    20% { 
        transform: translateX(3px) translateY(2px) rotate(1deg); 
    }
    30% { 
        transform: translateX(-3px) translateY(4px) rotate(-1deg); 
    }
    40% { 
        transform: translateX(4px) translateY(-2px) rotate(2deg); 
    }
    50% { 
        transform: translateX(-2px) translateY(-4px) rotate(-2deg); 
    }
    60% { 
        transform: translateX(3px) translateY(3px) rotate(1deg); 
    }
    70% { 
        transform: translateX(-4px) translateY(1px) rotate(-1deg); 
    }
    80% { 
        transform: translateX(2px) translateY(-3px) rotate(1deg); 
    }
    90% { 
        transform: translateX(-1px) translateY(2px) rotate(-1deg); 
    }
}

/* 移动端适配 */
@media (max-width: 480px) {
    .fireball-heatwave {
        width: 70px;
        height: 70px;
    }
    
    .fireball-charge-particle {
        width: 4px;
        height: 4px;
    }
    
    .fireball-charge-ring {
        width: 40px;
        height: 40px;
    }
    
    .fireball {
        width: 30px;
        height: 30px;
    }
    
    .explosion-flash {
        width: 80px;
        height: 80px;
    }
    
    .explosion-core {
        width: 40px;
        height: 40px;
    }
    
    .explosion-shockwave {
        width: 60px;
        height: 60px;
    }
    
    .explosion-fire-ring {
        width: 70px;
        height: 70px;
    }
    
    .explosion-smoke {
        width: 30px;
        height: 30px;
    }
    
    .explosion-heat-distortion {
        width: 100px;
        height: 100px;
    }
    
    .fire-particle {
        width: 4px;
        height: 4px;
    }
}

/* 🔥⚔️ 旋风斩技能样式 (巨剑旋转蓄力 + 直接击中 + 火焰爆炸) */

/* 技能容器 */
.xuanfengzhan-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1000;
    /* v2.0优化：启用硬件加速 */
    will-change: auto;
    transform: translateZ(0);
}

/* === 第一阶段：巨剑旋转蓄力样式 === */

/* 主旋转剑 */
.xuanfengzhan-main-sword {
    position: absolute;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(45deg, #ff6b35, #ff4500, #dc143c);
    transform: translate(-50%, -50%);
    animation: xuanfengzhan-spin-and-grow 2s linear forwards;
    z-index: 1001;
}

/* 武器图片发光效果（使用drop-shadow而不是box-shadow） */
.xuanfengzhan-main-sword[style*="background-image"] {
    background: none; /* 移除默认背景 */
    -webkit-filter: drop-shadow(0 0 15px rgba(255, 107, 53, 0.9)); 
    filter: drop-shadow(0 0 15px rgba(255, 107, 53, 0.9))
            drop-shadow(0 0 30px rgba(255, 69, 0, 0.7))
            drop-shadow(0 0 8px rgba(255, 255, 255, 0.5));
}

@keyframes xuanfengzhan-spin-and-grow {
    0% {
        transform: translate(-50%, -50%) rotate(0deg) scale(1);
        -webkit-filter: drop-shadow(0 0 10px rgba(255, 107, 53, 0.7));
        filter: drop-shadow(0 0 10px rgba(255, 107, 53, 0.7));
    }
    25% {
        transform: translate(-50%, -50%) rotate(-360deg) scale(2);
        -webkit-filter: drop-shadow(0 0 20px rgba(255, 107, 53, 0.8));
        filter: drop-shadow(0 0 20px rgba(255, 107, 53, 0.8));
    }
    50% {
        transform: translate(-50%, -50%) rotate(-720deg) scale(3.5);
        -webkit-filter: drop-shadow(0 0 30px rgba(255, 107, 53, 0.9));
        filter: drop-shadow(0 0 30px rgba(255, 107, 53, 0.9));
    }
    75% {
        transform: translate(-50%, -50%) rotate(-1080deg) scale(5);
        -webkit-filter: drop-shadow(0 0 40px rgba(255, 107, 53, 1));
        filter: drop-shadow(0 0 40px rgba(255, 107, 53, 1));
    }
    100% {
        width: var(--finalLength);
        height: var(--finalLength);
        transform: translate(-50%, -50%) rotate(-1440deg) scale(1);
        -webkit-filter: drop-shadow(0 0 50px rgba(255, 107, 53, 1)); 
        filter: drop-shadow(0 0 50px rgba(255, 107, 53, 1))
                drop-shadow(0 0 80px rgba(255, 69, 0, 0.8));
    }
}

/* 虚影跟随效果 */
.xuanfengzhan-sword-shadow {
    position: absolute;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(45deg, rgba(255, 107, 53, 0.5), rgba(255, 69, 0, 0.4), rgba(220, 20, 60, 0.3));
    transform: translate(-50%, -50%);
    animation: xuanfengzhan-shadow-follow 2s linear forwards;
    z-index: 1000;
}

/* 虚影武器图片效果 */
.xuanfengzhan-sword-shadow[style*="background-image"] {
    background: none;
    -webkit-filter: drop-shadow(0 0 10px rgba(255, 107, 53, 0.4)); 
    filter: drop-shadow(0 0 10px rgba(255, 107, 53, 0.4))
            drop-shadow(0 0 20px rgba(255, 69, 0, 0.3));
}

@keyframes xuanfengzhan-shadow-follow {
    0% {
        transform: translate(-50%, -50%) rotate(0deg) scale(0.8);
        opacity: 0.6;
    }
    25% {
        transform: translate(-50%, -50%) rotate(-300deg) scale(1.6);
        opacity: 0.5;
    }
    50% {
        transform: translate(-50%, -50%) rotate(-600deg) scale(2.8);
        opacity: 0.4;
    }
    75% {
        transform: translate(-50%, -50%) rotate(-900deg) scale(4);
        opacity: 0.3;
    }
    100% {
        width: var(--finalLength);
        height: var(--finalLength);
        transform: translate(-50%, -50%) rotate(-1200deg) scale(0.8);
        opacity: 0.2;
    }
}

/* 旋转能量场 */
.xuanfengzhan-energy-field {
    position: absolute;
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: conic-gradient(
        transparent 0deg,
        rgba(255, 107, 53, 0.3) 45deg,
        rgba(255, 69, 0, 0.5) 90deg,
        rgba(220, 20, 60, 0.4) 135deg,
        rgba(255, 107, 53, 0.3) 180deg,
        transparent 225deg,
        rgba(255, 69, 0, 0.2) 270deg,
        transparent 315deg,
        transparent 360deg
    );
    transform: translate(-50%, -50%);
    animation: xuanfengzhan-energy-spin 2s linear forwards;
    -webkit-filter: blur(2px);
    filter: blur(2px);
}

@keyframes xuanfengzhan-energy-spin {
    0% {
        transform: translate(-50%, -50%) rotate(0deg) scale(0.5);
        opacity: 0.4;
    }
    50% {
        transform: translate(-50%, -50%) rotate(720deg) scale(1.5);
        opacity: 0.8;
    }
    100% {
        transform: translate(-50%, -50%) rotate(1440deg) scale(2.5);
        opacity: 0.6;
    }
}

/* 火焰粒子环绕 */
.xuanfengzhan-charge-particle {
    position: absolute;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: radial-gradient(circle, #ff6b35 0%, #ff4500 50%, transparent 100%);
    animation: xuanfengzhan-particle-orbit 1.5s ease-in-out infinite;
    -webkit-filter: drop-shadow(0 0 6px rgba(255, 107, 53, 0.8));
    filter: drop-shadow(0 0 6px rgba(255, 107, 53, 0.8));
}

@keyframes xuanfengzhan-particle-orbit {
    0% {
        transform: translate(-50%, -50%) rotate(var(--orbitAngle)) 
                   translateX(var(--orbitRadius)) rotate(calc(-1 * var(--orbitAngle)));
        opacity: 0.3;
    }
    50% {
        transform: translate(-50%, -50%) rotate(calc(var(--orbitAngle) + 180deg)) 
                   translateX(calc(var(--orbitRadius) * 1.2)) rotate(calc(-1 * (var(--orbitAngle) + 180deg)));
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) rotate(calc(var(--orbitAngle) + 360deg)) 
                   translateX(var(--orbitRadius)) rotate(calc(-1 * (var(--orbitAngle) + 360deg)));
        opacity: 0.3;
    }
}

/* 蓄力冲击波 */
.xuanfengzhan-charge-shockwave {
    position: absolute;
    width: 60px;
    height: 60px;
    border: 2px solid rgba(255, 107, 53, 0.8);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: xuanfengzhan-shockwave-expand 1.6s ease-out forwards;
    -webkit-filter: drop-shadow(0 0 12px rgba(255, 107, 53, 0.6));
    filter: drop-shadow(0 0 12px rgba(255, 107, 53, 0.6));
}

@keyframes xuanfengzhan-shockwave-expand {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0;
        border-color: rgba(255, 107, 53, 1);
    }
    30% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
        border-color: rgba(255, 69, 0, 0.9);
    }
    70% {
        transform: translate(-50%, -50%) scale(2);
        opacity: 0.5;
        border-color: rgba(220, 20, 60, 0.6);
    }
    100% {
        transform: translate(-50%, -50%) scale(3.5);
        opacity: 0;
        border-color: rgba(255, 107, 53, 0.2);
    }
}

/* === 第二阶段：直接击中样式 === */

/* 直接击中动画（瞬间移动到目标位置） */
@keyframes xuanfengzhan-direct-strike {
    0% {
        left: var(--startX, 50%);
        top: var(--startY, 50%);
        transform: translate(-50%, -50%) rotate(var(--strikeAngle, 0deg)) scale(1);
        -webkit-filter: drop-shadow(0 0 30px rgba(255, 107, 53, 0.8));
        filter: drop-shadow(0 0 30px rgba(255, 107, 53, 0.8));
    }
    20% {
        left: var(--strikeX);
        top: var(--strikeY);
        transform: translate(-50%, -50%) rotate(var(--strikeAngle, 0deg)) scale(1.5);
        -webkit-filter: drop-shadow(0 0 50px rgba(255, 107, 53, 1)); 
        filter: drop-shadow(0 0 50px rgba(255, 107, 53, 1))
                drop-shadow(0 0 80px rgba(255, 69, 0, 0.8));
    }
    60% {
        left: var(--strikeX);
        top: var(--strikeY);
        transform: translate(-50%, -50%) rotate(calc(var(--strikeAngle, 0deg) + 90deg)) scale(1.3);
        -webkit-filter: drop-shadow(0 0 60px rgba(255, 107, 53, 1));
        filter: drop-shadow(0 0 60px rgba(255, 107, 53, 1));
    }
    100% {
        left: var(--strikeX);
        top: var(--strikeY);
        transform: translate(-50%, -50%) rotate(calc(var(--strikeAngle, 0deg) + 180deg)) scale(1.8);
        -webkit-filter: drop-shadow(0 0 40px rgba(255, 107, 53, 0.6));
        filter: drop-shadow(0 0 40px rgba(255, 107, 53, 0.6));
        opacity: 0.8;
    }
}

/* 击中冲击效果 */
.xuanfengzhan-strike-impact {
    position: absolute;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: radial-gradient(circle, 
        rgba(255, 255, 255, 1) 0%,
        rgba(255, 107, 53, 0.9) 30%,
        rgba(255, 69, 0, 0.7) 60%,
        transparent 100%
    );
    transform: translate(-50%, -50%);
    animation: xuanfengzhan-impact-burst 0.5s ease-out forwards;
    -webkit-filter: drop-shadow(0 0 20px rgba(255, 107, 53, 0.8));
    filter: drop-shadow(0 0 20px rgba(255, 107, 53, 0.8));
}

@keyframes xuanfengzhan-impact-burst {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
    }
    30% {
        transform: translate(-50%, -50%) scale(1.2);
        opacity: 0.9;
    }
    70% {
        transform: translate(-50%, -50%) scale(2);
        opacity: 0.5;
    }
    100% {
        transform: translate(-50%, -50%) scale(3);
        opacity: 0;
    }
}

/* 击中火花 */
.xuanfengzhan-strike-spark {
    position: absolute;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: radial-gradient(circle, #fff 0%, #ff6b35 50%, transparent 100%);
    animation: xuanfengzhan-spark-fly 0.4s ease-out forwards;
    -webkit-filter: drop-shadow(0 0 4px rgba(255, 107, 53, 0.8));
    filter: drop-shadow(0 0 4px rgba(255, 107, 53, 0.8));
}

@keyframes xuanfengzhan-spark-fly {
    0% {
        transform: translate(-50%, -50%) rotate(var(--sparkAngle)) 
                   translateX(0) rotate(calc(-1 * var(--sparkAngle))) scale(1);
        opacity: 1;
    }
    70% {
        transform: translate(-50%, -50%) rotate(var(--sparkAngle)) 
                   translateX(var(--sparkDistance)) rotate(calc(-1 * var(--sparkAngle))) scale(0.8);
        opacity: 0.6;
    }
    100% {
        transform: translate(-50%, -50%) rotate(var(--sparkAngle)) 
                   translateX(calc(var(--sparkDistance) * 1.5)) rotate(calc(-1 * var(--sparkAngle))) scale(0.3);
        opacity: 0;
    }
}

/* === 性能优化样式 === */

/* v2.0性能优化 */
.xuanfengzhan-main-sword,
.xuanfengzhan-sword-shadow,
.xuanfengzhan-energy-field,
.xuanfengzhan-charge-particle,
.xuanfengzhan-charge-shockwave,
.xuanfengzhan-strike-impact,
.xuanfengzhan-strike-spark {
    will-change: transform, opacity;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    /* 减少重绘 */
    contain: layout style paint;
}

/* === 移动端适配 === */
@media (max-width: 768px) {
    .xuanfengzhan-main-sword {
        width: 30px !important;
        height: 30px !important;
    }
    
    .xuanfengzhan-sword-shadow {
        width: 25px !important;
        height: 25px !important;
    }
    
    .xuanfengzhan-energy-field {
        width: 80px !important;
        height: 80px !important;
    }
    
    .xuanfengzhan-charge-particle {
        width: 6px !important;
        height: 6px !important;
    }
    
    .xuanfengzhan-strike-impact {
        width: 60px !important;
        height: 60px !important;
    }
}

/* v2.0新增：减少动画计算 */
@media (prefers-reduced-motion: reduce) {
    .xuanfengzhan-main-sword,
    .xuanfengzhan-sword-shadow,
    .xuanfengzhan-energy-field,
    .xuanfengzhan-charge-particle,
    .xuanfengzhan-charge-shockwave,
    .xuanfengzhan-strike-impact,
    .xuanfengzhan-strike-spark {
        animation-duration: 0.1s !important;
    }
} 