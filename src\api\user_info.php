<?php
// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 0); // 不在输出中显示错误，避免破坏JSON

// 引入依赖文件
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/equipment_stats_manager.php';

// 设置JSON响应头
setJsonResponse();

// 检查维护模式
if (isMaintenanceMode()) {
    echo json_encode([
        'success' => false,
        'message' => '游戏正在维护中，请稍后再试',
        'maintenance' => true
    ]);
    exit;
}

// 记录API调用（如果开启了调试）
if (DEBUG_LOG_API_CALLS) {
    writeLog("API调用: user_info.php", 'DEBUG', 'api.log');
}

// 设置详细的错误记录
if (isDebugMode()) {
    error_log("user_info.php 被调用 - 时间: " . date('Y-m-d H:i:s'));
    error_log("会话状态: " . (session_status() == PHP_SESSION_ACTIVE ? '活跃' : '未活跃'));
    error_log("会话ID: " . session_id());
}

if (!isLoggedIn()) {
    error_log("user_info.php: 用户未登录");
    echo json_encode([
        'success' => false, 
        'logged_in' => false,
        'message' => '用户未登录'
    ]);
    exit;
}

// 记录登录用户信息
error_log("user_info.php: 用户已登录，ID=" . $_SESSION['user_id'] . ", 用户名=" . (isset($_SESSION['username']) ? $_SESSION['username'] : '未知'));

// 获取用户信息前先记录会话中的角色信息
if (isset($_SESSION['character_id'])) {
    error_log("会话中存在角色信息 - 角色ID: " . $_SESSION['character_id'] . 
              ", 角色名: " . (isset($_SESSION['character_name']) ? $_SESSION['character_name'] : '未知'));
} else {
    error_log("会话中不存在角色信息，检查数据库中是否有角色");
}

// 使用getCurrentUser函数获取用户和角色信息 
try {
    $user = getCurrentUser();
    
    // 如果获取到了用户信息，记录更详细的日志
    if ($user) {
        error_log("getCurrentUser()成功返回用户信息 - 用户ID: " . $user['id']);
        
        // 检查角色信息是否完整
        if (isset($user['character_id'])) {
            error_log("用户信息包含角色ID: " . $user['character_id'] . 
                      ", 角色名: " . (isset($user['character_name']) ? $user['character_name'] : '未知'));
        } else {
            error_log("用户信息不包含角色ID，可能需要创建角色");
        }
    } else {
        error_log("getCurrentUser()返回null，尝试直接查询数据库");
    }
} catch (Exception $e) {
    error_log("getCurrentUser()出现异常: " . $e->getMessage());
    $user = null;
}

// 如果函数返回null但用户已登录，尝试直接从数据库获取用户和角色信息
if (!$user && isLoggedIn()) {
    error_log("尝试直接查询数据库获取用户和角色信息");
    
    try {
        $pdo = getDatabase();
        $userId = $_SESSION['user_id'];
        
        // 查询用户基本信息
        $userStmt = $pdo->prepare("
            SELECT id, username, email, nickname, status, spirit_stones, gold
            FROM users 
            WHERE id = ? AND status = 'active'
        ");
        $userStmt->execute([$userId]);
        $userData = $userStmt->fetch(PDO::FETCH_ASSOC);
        
        if ($userData) {
            error_log("从数据库直接查询到用户信息 - 用户ID: " . $userData['id']);
            
            // 查询角色信息 - 适应新的characters表结构
            $charStmt = $pdo->prepare("
                SELECT 
                    c.id as character_id, 
                    c.character_name,
                    c.realm_id, 
                    c.avatar_image,
                    c.current_hp,
                    c.current_mp,
                    c.physique, 
                    c.comprehension, 
                    c.constitution, 
                    c.spirit, 
                    c.agility,
                    r.realm_name, 
                    r.realm_level,
                    r.hp_multiplier,
                    r.mp_multiplier,
                    r.attack_multiplier,
                    r.defense_multiplier,
                    r.speed_multiplier
                FROM characters c
                LEFT JOIN realm_levels r ON c.realm_id = r.id
                WHERE c.user_id = ?
                LIMIT 1
            ");
            $charStmt->execute([$userId]);
            $charData = $charStmt->fetch(PDO::FETCH_ASSOC);
            
            if ($charData) {
                error_log("从数据库直接查询到角色信息 - 角色ID: " . $charData['character_id'] . 
                          ", 角色名: " . $charData['character_name']);
                
                // 修复会话中的角色信息
                $_SESSION['character_id'] = $charData['character_id'];
                $_SESSION['character_name'] = $charData['character_name'];
                $_SESSION['character_avatar'] = $charData['avatar_image'];
                $_SESSION['realm_id'] = $charData['realm_id'];
                $_SESSION['realm_name'] = $charData['realm_name'];
                $_SESSION['realm_level'] = $charData['realm_level'];
                
                error_log("已修复会话中的角色信息");
                
                // 合并用户和角色信息
                $user = array_merge($userData, $charData);
                
                // 计算实际属性（考虑境界加成）
                $hp_multiplier = isset($charData['hp_multiplier']) ? $charData['hp_multiplier'] : 1;
                $mp_multiplier = isset($charData['mp_multiplier']) ? $charData['mp_multiplier'] : 1;
                $attack_multiplier = isset($charData['attack_multiplier']) ? $charData['attack_multiplier'] : 1;
                $defense_multiplier = isset($charData['defense_multiplier']) ? $charData['defense_multiplier'] : 1;
                $speed_multiplier = isset($charData['speed_multiplier']) ? $charData['speed_multiplier'] : 1;
                
                $user['max_hp'] = round($charData['current_hp'] * $hp_multiplier);
                $user['max_mp'] = round($charData['current_mp'] * $mp_multiplier);
                
                // 🔧 更新：计算物理/法术攻击力和防御力（统一使用immortal字段）
                $user['physical_attack'] = round(($charData['physique'] + $charData['constitution']) * $attack_multiplier);
                $user['immortal_attack'] = round(($charData['spirit'] + $charData['comprehension']) * $attack_multiplier * 0.8);
                $user['physical_defense'] = round(($charData['physique'] + $charData['constitution']) * $defense_multiplier);
                $user['immortal_defense'] = round(($charData['spirit'] + $charData['comprehension']) * $defense_multiplier * 0.6);
                
                $user['speed'] = round($charData['agility'] * $speed_multiplier);
                
                // 🎯 新增：获取装备属性加成
                try {
                    // 🔧 重构：使用统一装备管理器
                    require_once __DIR__ . '/../includes/equipment_stats_manager.php';
                    
                    // 获取角色ID
                    $stmt = $pdo->prepare("SELECT id FROM characters WHERE user_id = ? LIMIT 1");
                    $stmt->execute([$userId]);
                    $character = $stmt->fetch(PDO::FETCH_ASSOC);
                    $characterId = $character ? $character['id'] : 0;
                    
                    $equipmentStats = EquipmentStatsManager::getEquipmentStats($pdo, $characterId);
                    $weaponStats = EquipmentStatsManager::getWeaponStats($pdo, $characterId);
                    
                    // 将装备属性加成添加到角色属性中
                    $user['equipment_bonus'] = [
                        'physical_attack' => (isset($equipmentStats['physical_attack']) ? $equipmentStats['physical_attack'] : 0) + (isset($weaponStats['physical_attack']) ? $weaponStats['physical_attack'] : 0),
                        'immortal_attack' => (isset($equipmentStats['immortal_attack']) ? $equipmentStats['immortal_attack'] : 0) + (isset($weaponStats['immortal_attack']) ? $weaponStats['immortal_attack'] : 0),
                        'physical_defense' => isset($equipmentStats['physical_defense']) ? $equipmentStats['physical_defense'] : 0,
                        'immortal_defense' => isset($equipmentStats['immortal_defense']) ? $equipmentStats['immortal_defense'] : 0,
                        'hp_bonus' => isset($equipmentStats['hp_bonus']) ? $equipmentStats['hp_bonus'] : 0,
                        'mp_bonus' => isset($equipmentStats['mp_bonus']) ? $equipmentStats['mp_bonus'] : 0,
                        'critical_bonus' => (isset($equipmentStats['critical_bonus']) ? $equipmentStats['critical_bonus'] : 0) + (isset($weaponStats['critical_bonus']) ? $weaponStats['critical_bonus'] : 0),
                        'critical_damage' => (isset($equipmentStats['critical_damage']) ? $equipmentStats['critical_damage'] : 0) + (isset($weaponStats['critical_damage']) ? $weaponStats['critical_damage'] : 0),
                        'accuracy' => (isset($equipmentStats['accuracy_bonus']) ? $equipmentStats['accuracy_bonus'] : 0) + (isset($weaponStats['accuracy_bonus']) ? $weaponStats['accuracy_bonus'] : 0),
                        'dodge_bonus' => isset($equipmentStats['dodge_bonus']) ? $equipmentStats['dodge_bonus'] : 0,
                        'speed_bonus' => isset($equipmentStats['speed_bonus']) ? $equipmentStats['speed_bonus'] : 0
                    ];
                    
                    // 将装备加成应用到最终属性中
                    $user['physical_attack'] += $user['equipment_bonus']['physical_attack'];
                    $user['immortal_attack'] += $user['equipment_bonus']['immortal_attack'];
                    $user['physical_defense'] += $user['equipment_bonus']['physical_defense'];
                    $user['immortal_defense'] += $user['equipment_bonus']['immortal_defense'];
                    $user['max_hp'] += $user['equipment_bonus']['hp_bonus'];
                    $user['max_mp'] += $user['equipment_bonus']['mp_bonus'];
                    
                    // 🎯 重要：添加装备属性到角色基础属性中（前端战斗系统需要）
                    $user['critical_bonus'] = (isset($user['equipment_bonus']['critical_bonus']) ? $user['equipment_bonus']['critical_bonus'] : 0) + 5;
                    $user['critical_damage'] = (isset($user['equipment_bonus']['critical_damage']) ? $user['equipment_bonus']['critical_damage'] : 0) + 1.5;
                    $user['accuracy_bonus'] = (isset($user['equipment_bonus']['accuracy']) ? $user['equipment_bonus']['accuracy'] : 0) + 95;
                    $user['dodge_bonus'] = (isset($user['equipment_bonus']['dodge_bonus']) ? $user['equipment_bonus']['dodge_bonus'] : 0) + 5;
                    
                    error_log("装备属性加成计算成功: " . json_encode($user['equipment_bonus']));
                    
                } catch (Exception $e) {
                    error_log("获取装备属性加成失败: " . $e->getMessage());
                    // 如果获取装备属性失败，设置默认值
                    $user['equipment_bonus'] = [
                        'physical_attack' => 0, 'immortal_attack' => 0, 'physical_defense' => 0, 'immortal_defense' => 0,
                        'hp_bonus' => 0, 'mp_bonus' => 0, 'critical_bonus' => 0,
                        'accuracy' => 0, 'dodge_bonus' => 0, 'speed_bonus' => 0
                    ];
                    $user['critical_bonus'] = 5;
                    $user['accuracy_bonus'] = 95;
                    $user['dodge_bonus'] = 5;
                }
                
                // 确保有角色名称和头像
                if (empty($user['character_name'])) {
                    $user['character_name'] = '原石道人'; // 默认角色名
                    error_log("角色名为空，设置默认名称: 原石道人");
                }
                
                if (empty($user['avatar_image'])) {
                    $user['avatar_image'] = 'assets/images/avatars/default_male.png'; // 默认头像
                    error_log("角色头像为空，设置默认头像路径");
                }
            } else {
                error_log("数据库中没有找到角色信息，可能需要创建角色");
                $user = $userData;
                
                // 设置need_create_character标志
                $_SESSION['need_create_character'] = true;
                $user['need_create_character'] = true;
                
                // 添加默认角色信息以防前端报错
                $user['character_name'] = '未创建角色';
                $user['avatar_image'] = 'assets/images/char/arxhz.png';
                
                error_log("添加了默认角色名和头像信息");
            }
        } else {
            error_log("无法从数据库获取用户信息，用户ID可能无效");
        }
    } catch (Exception $e) {
        error_log("直接查询数据库失败: " . $e->getMessage());
        error_log("错误发生在: " . $e->getFile() . " 第 " . $e->getLine() . " 行");
        error_log("堆栈跟踪: " . $e->getTraceAsString());
    }
}

if ($user) {
    // 检查响应中是否包含角色ID
    if (isset($user['character_id'])) {
        error_log("返回完整用户信息，包含角色ID: " . $user['character_id'] . 
                  ", 角色名: " . (isset($user['character_name']) ? $user['character_name'] : '未知'));
    } else {
        error_log("返回的用户信息不包含角色ID，可能需要创建角色");
    }
    
    echo json_encode([
        'success' => true,
        'logged_in' => true,
        'user' => $user
    ]);
} else {
    error_log("无法获取用户信息，即使尝试了直接查询数据库");
    echo json_encode([
        'success' => false, 
        'logged_in' => false,
        'message' => '获取用户信息失败'
    ]);
}
?> 