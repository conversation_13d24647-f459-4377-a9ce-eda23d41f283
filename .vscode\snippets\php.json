{"PHP API Response": {"prefix": "api-response", "body": ["header('Content-Type: application/json');", "echo json_encode([", "    'success' => ${1:true},", "    'message' => '${2:操作成功}',", "    'data' => ${3:null}", "]);"], "description": "标准API响应格式"}, "PHP Database Connection": {"prefix": "db-connect", "body": ["require_once __DIR__ . '/../config/database.php';", "", "try {", "    \\$pdo = getDatabase();", "    if (!\\$pdo) {", "        throw new Exception('数据库连接失败');", "    }", "} catch (Exception \\$e) {", "    error_log('数据库错误: ' . \\$e->getMessage());", "    echo json_encode(['success' => false, 'message' => '数据库连接失败']);", "    exit;", "}"], "description": "标准数据库连接代码"}, "PHP Session Check": {"prefix": "session-check", "body": ["session_start();", "", "if (!isset(\\$_SESSION['user_id'])) {", "    echo json_encode(['success' => false, 'message' => '请先登录']);", "    exit;", "}", "", "\\$user_id = \\$_SESSION['user_id'];"], "description": "用户登录状态检查"}, "PHP Game API Template": {"prefix": "game-api", "body": ["<?php", "require_once __DIR__ . '/../config/database.php';", "session_start();", "", "header('Content-Type: application/json');", "", "// 检查登录状态", "if (!isset(\\$_SESSION['user_id'])) {", "    echo json_encode(['success' => false, 'message' => '请先登录']);", "    exit;", "}", "", "\\$user_id = \\$_SESSION['user_id'];", "", "try {", "    \\$pdo = getDatabase();", "    if (!\\$pdo) {", "        throw new Exception('数据库连接失败');", "    }", "", "    // 处理不同的action", "    \\$action = \\$_GET['action'] ?? \\$_POST['action'] ?? '';", "", "    switch (\\$action) {", "        case '${1:action_name}':", "            ${2:// 处理逻辑}", "            break;", "        default:", "            echo json_encode(['success' => false, 'message' => '无效的操作']);", "            break;", "    }", "", "} catch (Exception \\$e) {", "    error_log('API错误: ' . \\$e->getMessage());", "    echo json_encode(['success' => false, 'message' => '服务器错误']);", "}"], "description": "游戏API模板"}, "PHP SQL Query": {"prefix": "sql-query", "body": ["\\$stmt = \\$pdo->prepare(\"${1:SELECT * FROM table WHERE id = ?}\");", "\\$stmt->execute([${2:\\$id}]);", "\\$result = \\$stmt->${3|fetch,fetchAll|}();"], "description": "参数化SQL查询"}, "PHP Error Log": {"prefix": "error-log", "body": ["error_log('${1:错误描述}: ' . ${2:\\$e->getMessage()});"], "description": "错误日志记录"}, "PHP Character Data": {"prefix": "char-data", "body": ["// 获取角色数据", "\\$stmt = \\$pdo->prepare(\"SELECT * FROM characters WHERE user_id = ? AND id = ?\");", "\\$stmt->execute([\\$user_id, \\$character_id]);", "\\$character = \\$stmt->fetch();", "", "if (!\\$character) {", "    echo json_encode(['success' => false, 'message' => '角色不存在']);", "    exit;", "}"], "description": "获取角色数据模板"}}