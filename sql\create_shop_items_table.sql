-- 创建商店商品配置表
-- 用于管理坊市和黑市的商品配置，确保商品与数据库一致性

CREATE TABLE `shop_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `item_id` int(11) NOT NULL COMMENT '物品ID，对应game_items表的id',
  `shop_key` varchar(50) NOT NULL COMMENT '商店内部标识符',
  `shop_type` enum('market','black_market') NOT NULL COMMENT '商店类型：坊市/黑市',
  `category` varchar(50) NOT NULL COMMENT '商品分类',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否上架销售',
  `max_purchase` int(11) DEFAULT 0 COMMENT '限购数量，0=无限购买',
  `level_requirement` int(11) DEFAULT NULL COMMENT '购买等级需求',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序权重',
  `special_tags` json DEFAULT NULL COMMENT '特殊标签，如新品、热销等',
  `purchase_note` text DEFAULT NULL COMMENT '购买备注说明',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_shop_key` (`shop_key`),
  FOREIGN KEY (`item_id`) REFERENCES `game_items` (`id`) ON DELETE CASCADE,
  KEY `idx_shop_type` (`shop_type`),
  KEY `idx_category` (`category`),
  KEY `idx_active` (`is_active`),
  KEY `idx_sort` (`sort_order`)
) ENGINE=InnoDB COMMENT='商店商品配置表';

-- 插入坊市商品数据（金币购买）
INSERT INTO `shop_items` (`item_id`, `shop_key`, `shop_type`, `category`, `max_purchase`, `level_requirement`, `sort_order`, `special_tags`, `purchase_note`) VALUES

-- 炼丹丹方 (限购1个，坊市)
(364, 'recipe_tenglong_1', 'market', 'alchemy_recipes', 1, 1, 100, '["炼丹"]', '学习后可炼制一阶腾龙丹'),
(365, 'recipe_luocha_1', 'market', 'alchemy_recipes', 1, 1, 101, '["炼丹"]', '学习后可炼制一阶罗刹丹'),
(366, 'recipe_xueqi_1', 'market', 'alchemy_recipes', 1, 1, 102, '["炼丹"]', '学习后可炼制一阶血气丹'),
(367, 'recipe_xuling_1', 'market', 'alchemy_recipes', 1, 1, 103, '["炼丹"]', '学习后可炼制一阶虚灵丹'),
(368, 'recipe_youlong_1', 'market', 'alchemy_recipes', 1, 1, 104, '["炼丹"]', '学习后可炼制一阶游龙丹'),

-- 炼丹工具 (限购1个，坊市)
(208, 'furnace_basic', 'market', 'furnaces', 1, NULL, 200, '["炼丹工具"]', '炼丹的基础工具，提升炼丹成功率'),

-- 炼丹材料 (无限购买，坊市)
(369, 'material_lingzhi', 'market', 'alchemy_materials', 0, NULL, 300, '["材料"]', '炼制属性丹的基础材料'),
(370, 'material_xueshen', 'market', 'alchemy_materials', 0, NULL, 301, '["材料"]', '炼制血气丹的主要材料'),
(371, 'material_longgu', 'market', 'alchemy_materials', 0, NULL, 302, '["材料"]', '炼制腾龙丹的主要材料'),
(372, 'material_hunjing', 'market', 'alchemy_materials', 0, NULL, 303, '["材料"]', '炼制虚灵丹的主要材料'),
(373, 'material_fengling', 'market', 'alchemy_materials', 0, NULL, 304, '["材料"]', '炼制游龙丹的主要材料'),
(374, 'material_zhihui', 'market', 'alchemy_materials', 0, NULL, 305, '["材料"]', '炼制罗刹丹的主要材料');

-- 注意：功法秘籍、基础丹药等其他商品需要先在game_items表中创建对应记录
-- 然后再在这里配置商店显示

-- 示例：如果要添加黑市商品（灵石购买），使用shop_type='black_market'
-- INSERT INTO `shop_items` (`item_id`, `shop_key`, `shop_type`, `category`, `max_purchase`, `level_requirement`, `sort_order`) VALUES
-- (999, 'rare_item_example', 'black_market', 'rare_items', 1, 30, 1000); 