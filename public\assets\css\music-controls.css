/* 音乐控制相关样式 */

/* 音乐状态指示器 */
.music-status-indicator {
    position: fixed;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: #fff;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 12px;
    z-index: 10000;
    transition: all 0.3s ease;
    opacity: 0;
    pointer-events: none;
}

.music-status-indicator.show {
    opacity: 1;
}

.music-status-indicator.background {
    background: linear-gradient(45deg, #4a90e2, #7b68ee);
}

.music-status-indicator.battle {
    background: linear-gradient(45deg, #ff6b6b, #ee5a5a);
}

.music-status-indicator.off {
    background: linear-gradient(45deg, #666, #999);
}

/* 音乐控制按钮 */
.music-control-button {
    position: fixed;
    bottom: 80px;
    right: 15px;
    width: 45px;
    height: 45px;
    background: linear-gradient(45deg, #d4af37, #f1c40f);
    border: none;
    border-radius: 50%;
    color: #fff;
    font-size: 18px;
    cursor: pointer;
    z-index: 9999;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(212, 175, 55, 0.4);
    display: none;
}

.music-control-button:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(212, 175, 55, 0.6);
}

.music-control-button:active {
    transform: scale(0.95);
}

.music-control-button.muted {
    background: linear-gradient(45deg, #666, #999);
    box-shadow: 0 4px 12px rgba(102, 102, 102, 0.4);
}

/* 移动端适配 */
@media (max-width: 768px) {
    .music-status-indicator {
        font-size: 11px;
        padding: 4px 8px;
    }
    
    .music-control-button {
        width: 40px;
        height: 40px;
        font-size: 16px;
        bottom: 70px;
        right: 10px;
    }
}

/* 音乐控制弹窗 */
.music-control-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10001;
    align-items: center;
    justify-content: center;
}

.music-control-content {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    border-radius: 15px;
    padding: 25px;
    max-width: 300px;
    width: 90%;
    text-align: center;
    border: 2px solid #d4af37;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.music-control-title {
    color: #d4af37;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 20px;
    text-shadow: 0 0 10px rgba(212, 175, 55, 0.5);
}

.music-control-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid rgba(212, 175, 55, 0.2);
    color: #fff;
}

.music-control-option:last-child {
    border-bottom: none;
}

.music-toggle-switch {
    position: relative;
    width: 50px;
    height: 25px;
    background: #333;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.music-toggle-switch.active {
    background: #d4af37;
}

.music-toggle-handle {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 21px;
    height: 21px;
    background: #fff;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.music-toggle-switch.active .music-toggle-handle {
    transform: translateX(25px);
}

.music-volume-control {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 15px 0;
}

.music-volume-slider {
    flex: 1;
    height: 5px;
    background: #333;
    border-radius: 5px;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

.music-volume-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 15px;
    height: 15px;
    background: #d4af37;
    border-radius: 50%;
    cursor: pointer;
}

.music-volume-slider::-moz-range-thumb {
    width: 15px;
    height: 15px;
    background: #d4af37;
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

.music-close-button {
    background: linear-gradient(45deg, #d4af37, #f1c40f);
    color: #fff;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    margin-top: 15px;
    transition: all 0.3s ease;
}

.music-close-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(212, 175, 55, 0.4);
}

/* 音乐加载动画 */
.music-loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #d4af37;
    border-radius: 50%;
    border-top-color: transparent;
    animation: musicSpin 1s ease-in-out infinite;
}

@keyframes musicSpin {
    to {
        transform: rotate(360deg);
    }
}

/* 音乐脉冲效果 */
.music-pulse {
    animation: musicPulse 2s ease-in-out infinite;
}

@keyframes musicPulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
} 