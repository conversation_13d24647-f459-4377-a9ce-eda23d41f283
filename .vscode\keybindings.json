[
    // 一念修仙项目专用快捷键配置
    
    // 数据库操作
    {
        "key": "ctrl+shift+d",
        "command": "sqltools.showRecords",
        "when": "editorTextFocus"
    },
    {
        "key": "ctrl+shift+q",
        "command": "sqltools.executeQuery",
        "when": "editorTextFocus && editorLangId == sql"
    },
    
    // API测试
    {
        "key": "ctrl+shift+t",
        "command": "rest-client.request",
        "when": "editorTextFocus && editorLangId == http"
    },
    
    // PHP调试
    {
        "key": "f9",
        "command": "editor.debug.action.toggleBreakpoint",
        "when": "editorTextFocus && editorLangId == php"
    },
    {
        "key": "ctrl+f5",
        "command": "workbench.action.debug.run",
        "when": "editorTextFocus && editorLangId == php"
    },
    
    // 代码格式化
    {
        "key": "ctrl+shift+f",
        "command": "editor.action.formatDocument",
        "when": "editorTextFocus"
    },
    
    // 快速打开常用文件
    {
        "key": "ctrl+shift+s",
        "command": "workbench.action.quickOpen",
        "args": "setting.php"
    },
    {
        "key": "ctrl+shift+c",
        "command": "workbench.action.quickOpen", 
        "args": "src/config/database.php"
    },
    
    // AI助手
    {
        "key": "ctrl+shift+a",
        "command": "github.copilot.generate",
        "when": "editorTextFocus"
    },
    {
        "key": "ctrl+shift+x",
        "command": "github.copilot.toggleInlineSuggestion",
        "when": "editorTextFocus"
    },
    
    // 终端操作
    {
        "key": "ctrl+shift+`",
        "command": "workbench.action.terminal.new"
    },
    {
        "key": "ctrl+shift+p",
        "command": "workbench.action.showCommands"
    },
    
    // 文件操作
    {
        "key": "ctrl+shift+n",
        "command": "explorer.newFile"
    },
    {
        "key": "ctrl+shift+m",
        "command": "explorer.newFolder"
    },
    
    // 搜索和替换
    {
        "key": "ctrl+shift+h",
        "command": "workbench.action.replaceInFiles"
    },
    {
        "key": "ctrl+shift+f",
        "command": "workbench.action.findInFiles"
    },
    
    // 代码导航
    {
        "key": "ctrl+shift+o",
        "command": "workbench.action.gotoSymbol"
    },
    {
        "key": "ctrl+shift+r",
        "command": "workbench.action.openRecent"
    },
    
    // 任务运行
    {
        "key": "ctrl+shift+b",
        "command": "workbench.action.tasks.build"
    },
    {
        "key": "ctrl+shift+u",
        "command": "workbench.action.tasks.runTask"
    },
    
    // Git操作
    {
        "key": "ctrl+shift+g",
        "command": "workbench.view.scm"
    },
    {
        "key": "ctrl+shift+enter",
        "command": "git.commitStaged"
    }
]
