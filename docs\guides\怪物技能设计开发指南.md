# 🐉 怪物技能设计开发指南

## 📋 文档说明
本文档记录了怪物技能开发的完整流程、技术规范、最佳实践和常见问题解决方案。基于普通攻击技能修复的成功经验编写，为后续怪物技能开发提供标准模板。

---

## 🎯 技能开发核心原则

### 1. 双向兼容性
- **必须支持**：玩家使用 + 怪物使用
- **参数传递**：通过 `isEnemySkill` 参数区分
- **位置计算**：自动适配施法者和目标位置
- **动画方向**：根据施法者类型自动调整

### 2. 模块化架构
- **独立文件**：每个技能一个JS文件 + 一个CSS文件
- **标准命名**：`技能名-skill.js` + `技能名-animations.css`
- **基类继承**：所有技能继承 `BaseSkill` 基类
- **功能封装**：蓄力→攻击→击中→清理的完整流程

---

## 🏗️ 技能文件结构

### JavaScript 文件模板
```javascript
// 技能名-skill.js
class 技能名Skill extends BaseSkill {
    async execute(skillData, weaponImage) {
        console.log('🌪️ 技能开始执行，isEnemySkill:', this.isEnemySkill);
        
        // 显示技能喊话
        await this.showSkillShout(skillData.skillName || '技能名');
        
        // 执行技能动画
        await this.create技能动画();
    }
    
    async create技能动画(weaponImage) {
        // 获取施法者和目标位置
        const casterPos = this.getCharacterPosition(this.isEnemySkill);
        const targetPos = this.getCharacterPosition(!this.isEnemySkill);
        
        // 创建动画容器
        const container = this.createElement('技能名-container', {
            style: {
                position: 'absolute',
                top: '0',
                left: '0',
                width: '100%',
                height: '100%',
                pointerEvents: 'none',
                zIndex: 1000
            }
        });
        
        this.effectsContainer.appendChild(container);
        
        try {
            // 第一阶段：蓄力准备
            await this.create蓄力Phase(container, casterPos, weaponImage);
            
            // 第二阶段：攻击发射
            await this.create攻击Phase(container, casterPos, targetPos, weaponImage);
            
            // 第三阶段：击中效果
            await this.create击中Phase(container, targetPos);
            
        } finally {
            // 清理动画容器
            setTimeout(() => {
                if (container && container.parentNode) {
                    container.parentNode.removeChild(container);
                }
            }, 100);
        }
    }
}

// 导出技能类
window.技能类型Skills = { 技能名Skill };
```

### CSS 文件模板
```css
/* 技能名-animations.css */

/* 动画容器 */
.技能名-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1000;
}

/* 第一阶段：蓄力动画 */
.技能名-charge-core {
    width: 30px;
    height: 30px;
    background: radial-gradient(circle, 特效颜色);
    border-radius: 50%;
    animation: chargeCore 1s ease-out;
}

@keyframes chargeCore {
    0% { opacity: 0; transform: translate(-50%, -50%) scale(0.2); }
    100% { opacity: 1; transform: translate(-50%, -50%) scale(1.5); }
}

/* 第二阶段：攻击飞行 - 关键修复点 */
.技能名-projectile {
    width: 60px;
    height: 8px;
    background: linear-gradient(90deg, 特效渐变);
    border-radius: 4px;
    animation: projectileFly 1s ease-out;
}

/* ✅ 正确的飞行动画实现 */
@keyframes projectileFly {
    0% {
        opacity: 0;
        left: var(--startX);
        top: var(--startY);
        transform: translate(-50%, -50%) rotate(var(--angle)) scale(0.4);
    }
    100% {
        opacity: 0.8;
        left: var(--targetX);
        top: var(--targetY);
        transform: translate(-50%, -50%) rotate(var(--angle)) scale(1.2);
    }
}

/* 第三阶段：击中爆炸 */
.技能名-explosion {
    width: 80px;
    height: 80px;
    background: radial-gradient(circle, 爆炸效果);
    border-radius: 50%;
    animation: explosionBurst 0.6s ease-out;
}

@keyframes explosionBurst {
    0% { opacity: 0; transform: translate(-50%, -50%) scale(0.3); }
    50% { opacity: 1; transform: translate(-50%, -50%) scale(1.5); }
    100% { opacity: 0; transform: translate(-50%, -50%) scale(2); }
}

/* 移动端适配 */
@media (max-width: 768px) {
    .技能名-projectile { width: 45px; height: 6px; }
    .技能名-explosion { width: 60px; height: 60px; }
}
```

---

## 🎨 CSS动画最佳实践

### ✅ 正确的位置移动实现
```css
/* 推荐方式：使用 left/top 属性变化 */
@keyframes correctMovement {
    0% {
        left: var(--startX);
        top: var(--startY);
        transform: translate(-50%, -50%) rotate(var(--angle));
    }
    100% {
        left: var(--targetX);
        top: var(--targetY);
        transform: translate(-50%, -50%) rotate(var(--angle));
    }
}
```

### ❌ 错误的位置移动实现
```css
/* 避免方式：复杂的 transform 嵌套 */
@keyframes wrongMovement {
    100% {
        transform: translate(-50%, -50%) rotate(var(--angle)) 
                  translate(calc(var(--targetX) - var(--startX)), 
                           calc(var(--targetY) - var(--startY)));
    }
}
```

### 🎯 CSS变量设置规范
```javascript
// JavaScript中的正确设置
const element = this.createElement('projectile-class', {
    style: {
        position: 'absolute',
        left: casterPos.x + 'px',
        top: casterPos.y + 'px',
        transform: `translate(-50%, -50%) rotate(${angle}deg)`
    },
    cssVariables: {
        '--startX': casterPos.x + 'px',
        '--startY': casterPos.y + 'px',
        '--targetX': targetPos.x + 'px',
        '--targetY': targetPos.y + 'px',
        '--angle': angle + 'deg'
    }
});
```

---

## 🔧 技术实现细节

### 1. 位置计算
```javascript
// 自动获取施法者和目标位置
const casterPos = this.getCharacterPosition(this.isEnemySkill);
const targetPos = this.getCharacterPosition(!this.isEnemySkill);

// 计算角度和距离
const distance = Math.sqrt(
    Math.pow(targetPos.x - casterPos.x, 2) + 
    Math.pow(targetPos.y - casterPos.y, 2)
);
const angle = Math.atan2(
    targetPos.y - casterPos.y, 
    targetPos.x - casterPos.x
) * 180 / Math.PI;
```

### 2. 武器图片支持
```javascript
// 如果有武器图片，添加到攻击元素上
if (weaponImage) {
    this.addWeaponImage(projectileElement, weaponImage);
}
```

### 3. 击中特效调用
```javascript
// 在目标位置创建击中特效
this.createHitEffect(targetPos.x, targetPos.y, !this.isEnemySkill);
```

### 4. 安全的元素清理
```javascript
// 使用 try-finally 确保清理
try {
    // 动画逻辑
} finally {
    setTimeout(() => {
        if (container && container.parentNode) {
            container.parentNode.removeChild(container);
        }
    }, 100);
}
```

---

## 📋 技能注册流程

### 1. skill-loader.js 中注册
```javascript
// 在 skillMapping 对象中添加
skillMapping = {
    // ... 现有技能 ...
    '技能名称': { 
        module: '技能类型-skills', 
        class: '技能名Skill', 
        css: '技能类型-animations' 
    }
};
```

### 2. script.js 中映射
```javascript
// 在 skillNameMapping 中添加动画模型映射
const skillNameMapping = {
    // ... 现有映射 ...
    '动画模型参数': '技能名称'
};
```

### 3. 数据库配置
确保 `item_skills` 表中：
- `skill_name` 字段与技能名称一致
- `animation_model` 字段与 skillNameMapping 的键一致

---

## 🎮 怪物AI技能调用

### 怪物技能数据结构
```php
// 怪物数据中的技能配置
$monster_data = [
    'skills' => ['普通攻击', '火球术', '冰锥术'], // 技能列表
    'skill_weights' => [60, 25, 15], // 技能权重
    'ai_type' => 'balanced' // AI类型：conservative/balanced/aggressive
];
```

### AI决策系统
- **保守型AI**：优先使用低消耗技能
- **均衡型AI**：平衡使用各种技能
- **激进型AI**：偏好高伤害技能

---

## 🎨 视觉效果设计指南

### 1. 颜色系统
- **普通攻击**：蓝色系 (#87CEEB, #4682B4, #1E90FF)
- **火系技能**：红橙色系 (#FF4500, #FF6347, #DC143C)
- **冰系技能**：青蓝色系 (#00CED1, #20B2AA, #008B8B)
- **雷系技能**：紫电色系 (#9370DB, #8A2BE2, #4B0082)
- **土系技能**：棕黄色系 (#D2691E, #CD853F, #A0522D)

### 2. 特效层次
- **蓄力阶段**：粒子汇聚、能量积累、武器发光
- **发射阶段**：弹道轨迹、拖尾效果、中途特效
- **击中阶段**：爆炸冲击、震荡波纹、目标受击
- **余韵阶段**：烟雾散射、能量消散、环境影响

### 3. 动画时机
- **蓄力时间**：0.5-1.5秒（根据技能威力调整）
- **飞行时间**：0.6-1.2秒（根据距离动态计算）
- **击中时间**：0.4-0.8秒（爆炸和冲击效果）
- **总时长**：控制在2-4秒内，保持战斗节奏

---

## 🐛 常见问题解决

### 1. 飞行路径错误
**症状**：技能不从施法者飞向目标
**原因**：CSS动画中使用了错误的transform嵌套
**解决**：使用left/top属性变化 + 简单的transform旋转

### 2. 角度计算偏差
**症状**：技能朝向不正确
**原因**：角度计算公式或角度单位错误
**解决**：使用标准的atan2函数，注意弧度转角度

### 3. 元素清理不完整
**症状**：DOM元素累积，影响性能
**原因**：异常情况下清理代码未执行
**解决**：使用try-finally确保清理，设置多重安全机制

### 4. 移动端适配问题
**症状**：手机上显示异常
**原因**：CSS尺寸和定位不适合小屏幕
**解决**：添加@media查询，调整移动端样式

---

## 🔍 调试技巧

### 1. 控制台日志
```javascript
console.log('🌪️ 位置计算:', { distance, angle, casterPos, targetPos });
console.log('🌪️ CSS变量:', { '--startX': startX, '--targetX': targetX });
console.log('🌪️ 元素创建:', element);
```

### 2. 分阶段测试
1. 先测试位置获取是否正确
2. 再测试CSS变量设置是否正确
3. 然后测试每个动画阶段
4. 最后测试完整流程

### 3. 浏览器开发者工具
- **元素面板**：查看CSS变量值和样式应用
- **动画面板**：观察关键帧动画执行
- **性能面板**：检查动画性能和内存使用

---

## 📚 参考示例

### 完整的普通攻击技能
技能已完成，参考文件：
- `public/assets/js/battle/skills/normal-attack-skill.js`
- `public/assets/css/battle/skills/normal-attack-animations.css`

### 技能特点
- ✅ 双向兼容（玩家+怪物）
- ✅ 三阶段动画（蓄力+飞行+爆炸）
- ✅ 正确的飞行路径
- ✅ 完整的清理机制
- ✅ 移动端适配

---

## 🚀 快速开发流程

### 新技能开发步骤
1. **复制模板**：使用本文档的模板文件
2. **修改命名**：替换技能名称和CSS类名
3. **设计动画**：定义蓄力、攻击、击中三阶段
4. **实现逻辑**：编写JavaScript动画控制
5. **编写样式**：创建CSS关键帧动画
6. **注册技能**：在skill-loader.js和script.js中注册
7. **测试验证**：测试玩家使用和怪物使用
8. **优化调整**：根据效果调整参数和样式

### 检查清单
- [ ] 继承BaseSkill基类
- [ ] 支持isEnemySkill参数
- [ ] 正确调用showSkillShout
- [ ] 使用getCharacterPosition获取位置
- [ ] 支持武器图片显示
- [ ] 调用createHitEffect
- [ ] 实现完整清理机制
- [ ] 在skill-loader.js中注册
- [ ] 在script.js中映射
- [ ] 包含移动端适配

---

**文档版本**：v1.0  
**创建日期**：2024年12月19日  
**基于经验**：普通攻击技能修复成功案例  
**适用范围**：所有怪物技能开发项目 