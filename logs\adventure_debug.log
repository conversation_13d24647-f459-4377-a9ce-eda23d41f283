[2025-06-01 14:21:24] 收到API请求 - Data: {"method":"POST","query":[],"content_type":"application\/json"}
[2025-06-01 14:21:27] 收到API请求 - Data: {"method":"GET","query":[],"content_type":""}
[2025-06-01 14:26:18] 收到API请求 - Data: {"method":"POST","query":[],"content_type":"application\/json"}
[2025-06-01 14:26:18] 认证服务文件不存在 - Data: {"path":"F:\\phpstudy_pro\\WWW\\yinian\\src\\api\/..\/services\/auth_service.php","fallback":"使用会话直接验证"}
[2025-06-01 14:26:18] 数据库连接失败 - Data: "Access denied for user ''@'localhost' (using password: NO)"
[2025-06-01 15:13:10] 收到API请求 - Data: {"method":"POST","query":[],"content_type":"application\/json"}
[2025-06-01 15:13:10] 认证服务文件不存在 - Data: {"path":"F:\\phpstudy_pro\\WWW\\yinian\\src\\api\/..\/services\/auth_service.php","fallback":"使用会话直接验证"}
[2025-06-01 15:13:10] 数据库连接失败 - Data: "Access denied for user ''@'localhost' (using password: NO)"
[2025-06-01 15:13:39] 收到API请求 - Data: {"method":"POST","query":[],"content_type":"application\/json"}
[2025-06-01 15:13:39] 认证服务文件不存在 - Data: {"path":"F:\\phpstudy_pro\\WWW\\yinian\\src\\api\/..\/services\/auth_service.php","fallback":"使用会话直接验证"}
[2025-06-01 15:13:39] 数据库连接失败 - Data: "Access denied for user ''@'localhost' (using password: NO)"
