<?php
/**
 * 临时配置验证工具
 * 用于验证修复后的API文件配置调用是否正常工作
 * 测试完成后请删除此文件
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 配置文件调用验证工具</h1>";
echo "<p>测试时间: " . date('Y-m-d H:i:s') . "</p>";

// 测试1: 验证setting.php配置加载
echo "<h2>1. 测试setting.php配置加载</h2>";
try {
    require_once __DIR__ . '/setting.php';
    echo "✅ setting.php 加载成功<br>";
    
    // 验证关键常量
    $constants = [
        'GAME_NAME' => GAME_NAME,
        'PROJECT_ROOT' => PROJECT_ROOT,
        'API_PATH' => API_PATH,
        'ASSETS_PATH' => ASSETS_PATH,
        'DB_HOST' => DB_HOST,
        'DB_NAME' => DB_NAME
    ];
    
    echo "<h3>关键常量验证:</h3>";
    foreach ($constants as $name => $value) {
        echo "✅ {$name}: {$value}<br>";
    }
    
} catch (Exception $e) {
    echo "❌ setting.php 加载失败: " . $e->getMessage() . "<br>";
}

// 测试2: 验证functions.php集成
echo "<h2>2. 测试functions.php集成</h2>";
try {
    require_once __DIR__ . '/src/includes/functions.php';
    echo "✅ functions.php 加载成功<br>";
    
    // 测试数据库连接函数
    if (function_exists('getDatabaseConnection')) {
        echo "✅ getDatabaseConnection() 函数存在<br>";
        
        $pdo = getDatabaseConnection();
        if ($pdo) {
            echo "✅ 数据库连接成功<br>";
        } else {
            echo "❌ 数据库连接失败<br>";
        }
    } else {
        echo "❌ getDatabaseConnection() 函数不存在<br>";
    }
    
    // 测试其他关键函数
    $functions = [
        'isMaintenanceMode',
        'isLoggedIn',
        'writeLog',
        'getApiPath',
        'getAssetUrl'
    ];
    
    echo "<h3>关键函数验证:</h3>";
    foreach ($functions as $func) {
        if (function_exists($func)) {
            echo "✅ {$func}() 函数存在<br>";
        } else {
            echo "❌ {$func}() 函数不存在<br>";
        }
    }
    
} catch (Exception $e) {
    echo "❌ functions.php 加载失败: " . $e->getMessage() . "<br>";
}

// 测试3: 验证修复的API文件
echo "<h2>3. 测试修复的API文件配置引入</h2>";

$fixedFiles = [
    'src/api/equipment_pickup_settings.php',
    'src/api/equipment_set_system.php', 
    'src/api/spiritual_material_usage.php',
    'src/api/update_map_progress.php',
    'src/api/adventure_maps.php'
];

foreach ($fixedFiles as $file) {
    echo "<h3>测试 {$file}:</h3>";
    
    if (file_exists($file)) {
        echo "✅ 文件存在<br>";
        
        // 检查文件内容
        $content = file_get_contents($file);
        
        // 检查是否使用了新的配置方式
        if (strpos($content, "require_once __DIR__ . '/../includes/functions.php'") !== false) {
            echo "✅ 使用新的配置引入方式<br>";
        } else {
            echo "⚠️ 可能仍使用旧的配置引入方式<br>";
        }
        
        // 检查是否还有旧的配置引用
        if (strpos($content, "require_once __DIR__ . '/../config/database.php'") !== false) {
            echo "❌ 仍包含旧的database.php引用<br>";
        } else {
            echo "✅ 已移除旧的database.php引用<br>";
        }
        
        // 检查维护模式检查
        if (strpos($content, 'isMaintenanceMode()') !== false) {
            echo "✅ 包含维护模式检查<br>";
        } else {
            echo "⚠️ 缺少维护模式检查<br>";
        }
        
        // 检查API调用日志
        if (strpos($content, 'DEBUG_LOG_API_CALLS') !== false) {
            echo "✅ 包含API调用日志<br>";
        } else {
            echo "⚠️ 缺少API调用日志<br>";
        }
        
    } else {
        echo "❌ 文件不存在<br>";
    }
    echo "<br>";
}

// 测试4: 验证config.js可访问性
echo "<h2>4. 测试config.js可访问性</h2>";
$configJsPath = 'public/assets/js/config.js';
if (file_exists($configJsPath)) {
    echo "✅ config.js 文件存在<br>";
    
    $content = file_get_contents($configJsPath);
    
    // 检查关键配置
    if (strpos($content, "API_BASE_URL: '/yinian/src/api/'") !== false) {
        echo "✅ API_BASE_URL 配置正确<br>";
    } else {
        echo "❌ API_BASE_URL 配置错误<br>";
    }
    
    if (strpos($content, "ASSETS_BASE_URL: '/yinian/public/assets/'") !== false) {
        echo "✅ ASSETS_BASE_URL 配置正确<br>";
    } else {
        echo "❌ ASSETS_BASE_URL 配置错误<br>";
    }
    
} else {
    echo "❌ config.js 文件不存在<br>";
}

echo "<h2>5. 总结</h2>";
echo "<p>✅ 配置验证完成</p>";
echo "<p>⚠️ 请在验证完成后删除此临时文件</p>";
echo "<p>📝 如发现问题，请根据上述检查结果进行相应修复</p>";

?>
