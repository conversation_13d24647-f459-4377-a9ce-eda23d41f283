<?php
/**
 * 升仙大会竞技系统数据库初始化脚本
 * 执行时间：2025年6月17日
 */

// 引入数据库配置
require_once '../src/config/database.php';

try {
    echo "🚀 开始初始化升仙大会竞技系统数据库...\n\n";
    
    $pdo = getDatabase();
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 读取SQL文件内容
    $sqlFile = 'immortal_arena_database.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL文件不存在: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    if ($sql === false) {
        throw new Exception("无法读取SQL文件: $sqlFile");
    }
    
    echo "📖 SQL文件读取成功，开始执行...\n";
    
    // 将SQL语句按分号分割
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    $executedCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $statement) {
        if (empty($statement) || strpos(trim($statement), '--') === 0 || strpos(trim($statement), 'SELECT') === 0) {
            continue; // 跳过空语句、注释和查询语句
        }
        
        try {
            $pdo->exec($statement);
            $executedCount++;
            
            // 显示执行的语句类型
            if (strpos($statement, 'CREATE TABLE') !== false) {
                preg_match('/CREATE TABLE.*?`(.*?)`/', $statement, $matches);
                $tableName = isset($matches[1]) ? $matches[1] : '未知表';
                echo "✅ 创建表: $tableName\n";
            } elseif (strpos($statement, 'ALTER TABLE') !== false) {
                preg_match('/ALTER TABLE `(.*?)`/', $statement, $matches);
                $tableName = isset($matches[1]) ? $matches[1] : '未知表';
                echo "🔧 修改表: $tableName\n";
            } elseif (strpos($statement, 'CREATE INDEX') !== false) {
                preg_match('/CREATE INDEX.*?`(.*?)`/', $statement, $matches);
                $indexName = isset($matches[1]) ? $matches[1] : '未知索引';
                echo "📊 创建索引: $indexName\n";
            } elseif (strpos($statement, 'INSERT') !== false) {
                echo "📝 插入数据\n";
            } elseif (strpos($statement, 'UPDATE') !== false) {
                echo "🔄 更新数据\n";
            }
            
        } catch (PDOException $e) {
            $errorCount++;
            echo "❌ 执行失败: " . substr($statement, 0, 50) . "...\n";
            echo "   错误信息: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n📊 执行完成统计:\n";
    echo "   ✅ 成功执行: $executedCount 条语句\n";
    echo "   ❌ 执行失败: $errorCount 条语句\n\n";
    
    // 验证结果
    echo "🔍 验证创建结果:\n";
    
    // 检查表是否创建成功
    $tables = ['immortal_arena_records', 'immortal_arena_match_pool', 'immortal_arena_ranks'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "✅ 表 $table 创建成功\n";
        } else {
            echo "❌ 表 $table 创建失败\n";
        }
    }
    
    // 检查characters表新增字段
    echo "\n🔧 检查characters表新增字段:\n";
    $stmt = $pdo->query("SHOW COLUMNS FROM characters LIKE 'arena_%'");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($columns) > 0) {
        foreach ($columns as $column) {
            echo "✅ 字段 {$column['Field']} 添加成功 ({$column['Type']})\n";
        }
    } else {
        echo "❌ 未发现arena_相关字段\n";
    }
    
    // 检查段位配置数据
    echo "\n🏆 检查段位配置数据:\n";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM immortal_arena_ranks");
    $rankCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "✅ 段位配置数据: $rankCount 条记录\n";
    
    if ($rankCount > 0) {
        echo "\n🎯 段位配置详情:\n";
        $stmt = $pdo->query("SELECT rank_level, rank_name, min_dao_power, max_dao_power FROM immortal_arena_ranks ORDER BY rank_level");
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $maxPower = $row['max_dao_power'] ? $row['max_dao_power'] : '无上限';
            echo "   等级{$row['rank_level']}: {$row['rank_name']} ({$row['min_dao_power']} - $maxPower 道行值)\n";
        }
    }
    
    echo "\n🎉 升仙大会竞技系统数据库初始化完成！\n";
    echo "📋 系统组件:\n";
    echo "   🗃️  竞技记录表 (immortal_arena_records)\n";
    echo "   🔄 匹配池表 (immortal_arena_match_pool)\n";
    echo "   🏆 段位配置表 (immortal_arena_ranks)\n";
    echo "   📊 角色扩展字段 (characters.arena_*)\n";
    echo "   📈 性能优化索引\n";
    echo "\n✨ 可以开始下一阶段开发了！\n";
    
} catch (Exception $e) {
    echo "💥 严重错误: " . $e->getMessage() . "\n";
    echo "📍 请检查数据库连接和SQL语法\n";
    exit(1);
}
?> 