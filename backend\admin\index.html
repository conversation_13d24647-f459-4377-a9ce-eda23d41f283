<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>一念修仙 - 游戏管理后台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 250px;
            background: rgba(0, 21, 41, 0.9);
            backdrop-filter: blur(10px);
            height: 100vh;
            padding: 20px 0;
            color: #fff;
            position: fixed;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 0 20px;
            margin-bottom: 30px;
            text-align: center;
        }

        .sidebar-header h1 {
            font-size: 20px;
            color: #d4af37;
            margin-bottom: 5px;
        }

        .sidebar-header p {
            font-size: 12px;
            color: #8c8c8c;
        }

        .menu-section {
            margin-bottom: 20px;
        }

        .menu-section-title {
            padding: 10px 20px;
            font-size: 12px;
            color: #8c8c8c;
            text-transform: uppercase;
            border-bottom: 1px solid #1f1f1f;
            margin-bottom: 10px;
        }

        .menu-item {
            padding: 12px 20px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 12px;
            border-left: 3px solid transparent;
        }

        .menu-item:hover {
            background: rgba(24, 144, 255, 0.1);
            border-left-color: #1890ff;
        }

        .menu-item.active {
            background: rgba(24, 144, 255, 0.2);
            border-left-color: #1890ff;
        }

        .menu-item .icon {
            font-size: 16px;
            width: 20px;
        }

        /* 主内容区域样式 */
        .main-content {
            margin-left: 250px;
            padding: 20px;
            flex: 1;
            width: calc(100% - 250px);
        }

        .content-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .content-header h2 {
            margin: 0;
            color: #333;
            font-size: 24px;
        }

        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .stats-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s;
        }

        .stats-card:hover {
            transform: translateY(-5px);
        }

        .stats-card .number {
            font-size: 32px;
            font-weight: bold;
            color: #1890ff;
            margin-bottom: 5px;
        }

        .stats-card .label {
            color: #666;
            font-size: 14px;
        }

        .content-body {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        /* 工具栏样式 */
        .toolbar {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-box {
            flex: 1;
            min-width: 200px;
            max-width: 300px;
        }

        .search-box input {
            width: 100%;
            padding: 10px 15px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            font-size: 14px;
        }

        /* 表格容器样式 */
        .table-container {
            width: 100%;
            overflow-x: auto;
            overflow-y: visible;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            background: #fff;
            margin-top: 20px;
        }

        /* 表格样式 */
        .data-table {
            width: 100%;
            min-width: 800px; /* 设置最小宽度确保字段完整显示 */
            border-collapse: collapse;
            margin: 0;
            background: #fff;
        }

        .data-table th,
        .data-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
            white-space: nowrap; /* 防止文字换行 */
        }

        .data-table th {
            background: #fafafa;
            font-weight: 600;
            color: #333;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .data-table tr:hover {
            background: #f5f5f5;
        }

        /* 自定义滚动条样式 */
        .table-container::-webkit-scrollbar {
            height: 8px;
        }

        .table-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .table-container::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .table-container::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* 操作按钮样式优化 */
        .action-buttons {
            display: flex;
            gap: 5px;
            flex-wrap: nowrap;
        }

        .action-buttons .btn {
            padding: 6px 12px;
            font-size: 12px;
            min-width: auto;
            white-space: nowrap;
        }

        /* 字段值显示优化 */
        .field-value {
            max-width: 150px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: block;
            min-height: 20px;
        }

        .field-value.long-text {
            max-width: 200px;
        }

        .field-value:hover {
            overflow: visible;
            white-space: normal;
            background: #fff;
            position: relative;
            z-index: 5;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            padding: 8px;
            border-radius: 4px;
        }

        /* 分页样式 */
        .pagination {
            margin-top: 20px;
            display: flex;
            justify-content: center;
            gap: 5px;
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            background: #fff;
            cursor: pointer;
            border-radius: 4px;
        }

        .pagination button:hover {
            background: #f0f0f0;
        }

        .pagination button.active {
            background: #1890ff;
            color: #fff;
            border-color: #1890ff;
        }

        /* 按钮样式 */
        .btn {
            padding: 10px 20px;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 14px;
            font-weight: 500;
        }

        .btn-primary {
            background: #1890ff;
            color: #fff;
        }

        .btn-primary:hover {
            background: #40a9ff;
        }

        .btn-danger {
            background: #ff4d4f;
            color: #fff;
        }

        .btn-danger:hover {
            background: #ff7875;
        }

        .btn-warning {
            background: #faad14;
            color: #fff;
        }

        .btn-warning:hover {
            background: #ffc53d;
        }

        .btn-success {
            background: #52c41a;
            color: #fff;
        }

        .btn-success:hover {
            background: #73d13d;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.6);
            z-index: 1000;
            backdrop-filter: blur(5px);
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 30px;
            border-radius: 12px;
            min-width: 400px;
            max-width: 90vw;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #f0f0f0;
        }

        .modal-header h3 {
            margin: 0;
            color: #333;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            font-size: 14px;
        }

        .form-group textarea {
            height: 100px;
            resize: vertical;
        }

        .modal-footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #f0f0f0;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
            color: #666;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 200px;
            }
            
            .main-content {
                margin-left: 200px;
                width: calc(100% - 200px);
                padding: 10px;
            }
            
            .content-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
                padding: 15px;
            }
            
            .content-header h2 {
                font-size: 20px;
            }
            
            .toolbar {
                flex-direction: column;
                align-items: stretch;
            }
            
            .search-box {
                max-width: none;
                margin-bottom: 10px;
            }
            
            .stats-cards {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 15px;
            }
            
            .stats-card {
                padding: 15px;
            }
            
            .stats-card .number {
                font-size: 24px;
            }
            
            /* 移动端表格优化 */
            .table-container {
                margin-top: 15px;
                border-radius: 6px;
            }
            
            .data-table {
                min-width: 600px; /* 移动端最小宽度 */
            }
            
            .data-table th,
            .data-table td {
                padding: 8px 10px;
                font-size: 13px;
            }
            
            .action-buttons .btn {
                padding: 4px 8px;
                font-size: 11px;
            }
            
            .field-value {
                max-width: 100px;
            }
            
            .field-value.long-text {
                max-width: 120px;
            }
            
            /* 移动端内联编辑优化 */
            .editable-cell:hover {
                border: 1px solid #1890ff;
            }
            
            .edit-hint {
                display: none; /* 移动端隐藏提示文字 */
            }
            
            .inline-input,
            .inline-select,
            .inline-textarea {
                font-size: 14px; /* 移动端字体稍大 */
                min-height: 35px; /* 移动端触摸目标更大 */
            }
        }

        @media (max-width: 480px) {
            .sidebar {
                width: 180px;
            }
            
            .main-content {
                margin-left: 180px;
                width: calc(100% - 180px);
                padding: 5px;
            }
            
            .content-header {
                padding: 10px;
            }
            
            .content-header h2 {
                font-size: 18px;
            }
            
            .user-info {
                font-size: 12px;
            }
            
            .stats-cards {
                grid-template-columns: repeat(2, 1fr);
                gap: 10px;
            }
            
            .stats-card {
                padding: 10px;
            }
            
            .stats-card .number {
                font-size: 20px;
            }
            
            .stats-card .label {
                font-size: 12px;
            }
            
            .data-table {
                min-width: 500px;
            }
            
            .data-table th,
            .data-table td {
                padding: 6px 8px;
                font-size: 12px;
            }
            
            .field-value {
                max-width: 80px;
            }
        }

        /* 内联编辑样式 */
        .editable-cell {
            cursor: pointer;
            position: relative;
            transition: background-color 0.2s;
        }

        .editable-cell:hover {
            background-color: #f0f9ff !important;
            border: 1px dashed #1890ff;
        }

        .editable-cell.editing {
            background-color: #fff !important;
            padding: 0 !important;
        }

        .inline-input {
            width: 100%;
            padding: 8px;
            border: 2px solid #1890ff;
            border-radius: 4px;
            font-size: 13px;
            outline: none;
            background: #fff;
            min-height: 30px;
        }

        .inline-select {
            width: 100%;
            padding: 8px;
            border: 2px solid #1890ff;
            border-radius: 4px;
            font-size: 13px;
            outline: none;
            background: #fff;
            min-height: 30px;
        }

        .inline-textarea {
            width: 100%;
            padding: 8px;
            border: 2px solid #1890ff;
            border-radius: 4px;
            font-size: 13px;
            outline: none;
            background: #fff;
            min-height: 60px;
            resize: vertical;
        }

        .edit-hint {
            position: absolute;
            top: 50%;
            right: 5px;
            transform: translateY(-50%);
            font-size: 10px;
            color: #999;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.2s;
        }

        .editable-cell:hover .edit-hint {
            opacity: 1;
        }

        .save-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #52c41a;
            color: white;
            padding: 10px 20px;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            z-index: 2000;
            display: none;
        }
    </style>
</head>
<body>
    <!-- 侧边导航栏 -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h1>一念修仙</h1>
            <p>游戏管理后台</p>
        </div>
        
        <div class="menu-section">
            <div class="menu-section-title">数据总览</div>
            <div class="menu-item active" onclick="switchContent('dashboard')">
                <span class="icon">📊</span>
                <span>仪表盘</span>
            </div>
        </div>

        <div class="menu-section">
            <div class="menu-section-title">玩家管理</div>
            <div class="menu-item" onclick="switchContent('users')">
                <span class="icon">👥</span>
                <span>玩家账号</span>
            </div>
            <div class="menu-item" onclick="switchContent('user_attributes')">
                <span class="icon">💪</span>
                <span>玩家属性</span>
            </div>
            <div class="menu-item" onclick="switchContent('user_map_progress')">
                <span class="icon">🗺️</span>
                <span>历练进度</span>
            </div>
            <div class="menu-item" onclick="switchContent('unified_inventory')">
                <span class="icon">🎒</span>
                <span>玩家背包</span>
            </div>
        </div>

        <div class="menu-section">
            <div class="menu-section-title">装备系统</div>
            <div class="menu-item" onclick="switchContent('user_weapon_slots')">
                <span class="icon">⚔️</span>
                <span>装备武器</span>
            </div>
            <div class="menu-item" onclick="switchContent('user_equipped_items')">
                <span class="icon">🛡️</span>
                <span>装备防具</span>
            </div>
        </div>

        <div class="menu-section">
            <div class="menu-section-title">物品掉落</div>
            <div class="menu-item" onclick="switchContent('drops')">
                <span class="icon">🎁</span>
                <span>掉落物品</span>
            </div>
            <div class="menu-item" onclick="switchContent('map_drops')">
                <span class="icon">💎</span>
                <span>地图掉落</span>
            </div>
        </div>

        <div class="menu-section">
            <div class="menu-section-title">地图世界</div>
            <div class="menu-item" onclick="switchContent('maps')">
                <span class="icon">🗺️</span>
                <span>历练地图</span>
            </div>
            <div class="menu-item" onclick="switchContent('monsters')">
                <span class="icon">👹</span>
                <span>怪物数据</span>
            </div>
            <div class="menu-item" onclick="switchContent('map_monsters')">
                <span class="icon">🎯</span>
                <span>地图怪物</span>
            </div>
            <div class="menu-item" onclick="switchContent('dungeons')">
                <span class="icon">🏰</span>
                <span>秘境管理</span>
            </div>
        </div>

        <div class="menu-section">
            <div class="menu-section-title">游戏配置</div>
            <div class="menu-item" onclick="switchContent('realms')">
                <span class="icon">⭐</span>
                <span>境界等级</span>
            </div>
        </div>

        <div class="menu-section">
            <div class="menu-section-title">运营管理</div>
            <div class="menu-item" onclick="openRedeemCodeManager()">
                <span class="icon">🎫</span>
                <span>兑换码管理</span>
            </div>
            <div class="menu-item" onclick="openUpdateLogManager()">
                <span class="icon">📝</span>
                <span>更新日志</span>
            </div>
        </div>

        <div class="menu-section">
            <div class="menu-section-title">系统日志</div>
            <div class="menu-item" onclick="switchContent('login_logs')">
                <span class="icon">📋</span>
                <span>登录记录</span>
            </div>
            <div class="menu-item" onclick="logout()" style="margin-top: 20px; border-top: 1px solid #1f1f1f; padding-top: 20px;">
                <span class="icon">🚪</span>
                <span>退出登录</span>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
        <div class="content-header">
            <h2 id="pageTitle">数据总览</h2>
            <div class="user-info">
                <span>欢迎，管理员</span>
                <span id="currentTime"></span>
            </div>
        </div>

        <!-- 仪表盘 -->
        <div id="dashboardContent">
            <div class="stats-cards">
                <div class="stats-card">
                    <div class="number" id="usersCount">0</div>
                    <div class="label">注册玩家</div>
                </div>
                <div class="stats-card">
                    <div class="number" id="itemsCount">0</div>
                    <div class="label">掉落物品</div>
                </div>
                <div class="stats-card">
                    <div class="number" id="monstersCount">0</div>
                    <div class="label">游戏怪物</div>
                </div>
                <div class="stats-card">
                    <div class="number" id="mapsCount">0</div>
                    <div class="label">历练地图</div>
                </div>
                <div class="stats-card">
                    <div class="number" id="dungeonsCount">0</div>
                    <div class="label">秘境数量</div>
                </div>
                <div class="stats-card">
                    <div class="number" id="realmsCount">0</div>
                    <div class="label">境界等级</div>
                </div>
            </div>
        </div>

        <!-- 数据内容 -->
        <div class="content-body" id="dataContent" style="display: none;">
            <div class="toolbar">
                <div class="search-box">
                    <input type="text" id="searchInput" placeholder="搜索..." onkeyup="handleSearch()">
                </div>
                <button class="btn btn-primary" onclick="showAddModal()">添加新项</button>
                <button class="btn btn-success" onclick="refreshList()">刷新</button>
                <span style="color: #666; font-size: 12px; margin-left: 10px;">💡 提示：点击表格数据可直接编辑，回车保存，ESC取消</span>
            </div>
            
            <div id="loadingDiv" class="loading">加载中...</div>
            
            <div class="table-container" id="tableContainer" style="display: none;">
                <table class="data-table" id="dataTable">
                    <thead id="tableHead">
                        <!-- 表头将动态生成 -->
                    </thead>
                    <tbody id="tableBody">
                        <!-- 数据将动态加载 -->
                    </tbody>
                </table>
            </div>
            
            <div class="pagination" id="pagination"></div>
        </div>
    </div>

    <!-- 保存提示 -->
    <div id="saveIndicator" class="save-indicator">
        保存成功！
    </div>

    <!-- 添加/编辑模态框 -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">添加项目</h3>
            </div>
            <form id="editForm">
                <input type="hidden" id="editId">
                <div id="formFields">
                    <!-- 表单字段将动态生成 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-warning" onclick="closeModal()">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 确认删除模态框 -->
    <div id="deleteModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>确认删除</h3>
            </div>
            <p>确定要删除这条记录吗？此操作不可恢复。</p>
            <div class="modal-footer">
                <button class="btn btn-warning" onclick="closeDeleteModal()">取消</button>
                <button class="btn btn-danger" onclick="confirmDelete()">删除</button>
            </div>
        </div>
    </div>

    <script>
        let currentType = 'dashboard';
        let currentPage = 1;
        let currentLimit = 20;
        let deleteId = null;
        let searchTimeout = null;

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            checkLoginStatus();
            updateTime();
            setInterval(updateTime, 1000);
            loadStats();
        });

        // 更新时间显示
        function updateTime() {
            const now = new Date();
            document.getElementById('currentTime').textContent = now.toLocaleString('zh-CN');
        }

        // 检查登录状态
        async function checkLoginStatus() {
            try {
                const response = await fetch('api/admin_api.php?action=check_login');
                const result = await response.json();
                
                if (!result.success) {
                    window.location.href = 'login.html';
                }
            } catch (error) {
                console.error('Error:', error);
                window.location.href = 'login.html';
            }
        }

        // 加载统计数据
        async function loadStats() {
            try {
                const response = await fetch('api/admin_api.php?action=stats');
                const result = await response.json();
                
                if (result.success) {
                    document.getElementById('usersCount').textContent = result.data.users || 0;
                    document.getElementById('itemsCount').textContent = result.data.items || 0;
                    document.getElementById('monstersCount').textContent = result.data.monsters || 0;
                    document.getElementById('mapsCount').textContent = result.data.maps || 0;
                    document.getElementById('dungeonsCount').textContent = result.data.dungeons || 0;
                    document.getElementById('realmsCount').textContent = result.data.realms || 0;
                }
            } catch (error) {
                console.error('Error loading stats:', error);
            }
        }

        // 切换内容
        function switchContent(type) {
            currentType = type;
            currentPage = 1;
            
            // 更新菜单激活状态
            document.querySelectorAll('.menu-item').forEach(item => {
                item.classList.remove('active');
            });
            event.currentTarget.classList.add('active');

            // 更新页面标题
            const titles = {
                'dashboard': '数据总览',
                'users': '玩家管理',
                'user_attributes': '玩家属性',
                'user_map_progress': '历练进度',
                'unified_inventory': '玩家背包',
                'user_weapon_slots': '装备武器',
                'user_equipped_items': '装备防具',
                'drops': '掉落物品',
                'map_drops': '地图掉落',
                'maps': '历练地图',
                'monsters': '怪物数据',
                'map_monsters': '地图怪物',
                'dungeons': '秘境管理',
                'realms': '境界等级',
                'login_logs': '登录记录'
            };
            
            document.getElementById('pageTitle').textContent = titles[type] || '管理';
            
            // 显示对应内容
            if (type === 'dashboard') {
                document.getElementById('dashboardContent').style.display = 'block';
                document.getElementById('dataContent').style.display = 'none';
                loadStats();
            } else {
                document.getElementById('dashboardContent').style.display = 'none';
                document.getElementById('dataContent').style.display = 'block';
                loadData(type);
            }
        }

        // 加载数据
        async function loadData(type, page = 1, search = '') {
            try {
                document.getElementById('loadingDiv').style.display = 'block';
                document.getElementById('tableContainer').style.display = 'none';
                
                const url = `api/admin_api.php?action=list&type=${type}&page=${page}&limit=${currentLimit}&search=${encodeURIComponent(search)}`;
                const response = await fetch(url);
                const result = await response.json();
                
                if (result.success) {
                    updateTable(result.data, type);
                    updatePagination(result.pagination);
                    document.getElementById('loadingDiv').style.display = 'none';
                    document.getElementById('tableContainer').style.display = 'block';
                } else {
                    alert('加载数据失败：' + result.message);
                    document.getElementById('loadingDiv').style.display = 'none';
                }
            } catch (error) {
                console.error('Error:', error);
                alert('加载数据失败');
                document.getElementById('loadingDiv').style.display = 'none';
            }
        }

        // 更新表格
        function updateTable(data, type) {
            const tableHead = document.getElementById('tableHead');
            const tableBody = document.getElementById('tableBody');
            
            // 清空现有内容
            tableHead.innerHTML = '';
            tableBody.innerHTML = '';
            
            // 生成表头
            if (data.length > 0) {
                const headers = Object.keys(data[0]);
                const headerRow = document.createElement('tr');
                
                // 添加数据列头
                headers.forEach(header => {
                    const th = document.createElement('th');
                    th.textContent = getFieldName(header);
                    headerRow.appendChild(th);
                });
                
                // 添加操作列头
                const actionTh = document.createElement('th');
                actionTh.textContent = '操作';
                actionTh.style.minWidth = '120px';
                headerRow.appendChild(actionTh);
                
                tableHead.appendChild(headerRow);
                
                // 生成表格数据
                data.forEach(item => {
                    const row = document.createElement('tr');
                    
                    // 添加数据列
                    headers.forEach(header => {
                        const td = document.createElement('td');
                        
                        // 如果是ID字段或只读字段，不允许编辑
                        if (header === 'id' || isReadOnlyField(header)) {
                            const span = document.createElement('span');
                            span.className = 'field-value';
                            span.textContent = formatFieldValue(header, item[header]);
                            td.appendChild(span);
                        } else {
                            // 可编辑字段
                            td.className = 'editable-cell';
                            td.setAttribute('data-field', header);
                            td.setAttribute('data-id', item.id);
                            td.setAttribute('data-type', currentType);
                            
                            const span = document.createElement('span');
                            span.className = `field-value ${isLongTextField(header) ? 'long-text' : ''}`;
                            span.textContent = formatFieldValue(header, item[header]);
                            td.appendChild(span);
                            
                            // 添加编辑提示
                            const hint = document.createElement('span');
                            hint.className = 'edit-hint';
                            hint.textContent = '点击编辑';
                            td.appendChild(hint);
                            
                            // 添加点击事件
                            td.addEventListener('click', function(e) {
                                e.stopPropagation();
                                startInlineEdit(this, item[header], header);
                            });
                        }
                        
                        row.appendChild(td);
                    });
                    
                    // 添加操作列
                    const actionTd = document.createElement('td');
                    actionTd.innerHTML = `
                        <div class="action-buttons">
                            <button class="btn btn-warning" onclick="showEditModal(${item.id})" title="编辑">编辑</button>
                            <button class="btn btn-danger" onclick="showDeleteModal(${item.id})" title="删除">删除</button>
                        </div>
                    `;
                    row.appendChild(actionTd);
                    
                    tableBody.appendChild(row);
                });
            } else {
                // 没有数据时显示提示
                const emptyRow = document.createElement('tr');
                const emptyCell = document.createElement('td');
                emptyCell.colSpan = 100;
                emptyCell.textContent = '暂无数据';
                emptyCell.style.textAlign = 'center';
                emptyCell.style.padding = '50px';
                emptyCell.style.color = '#999';
                emptyRow.appendChild(emptyCell);
                tableBody.appendChild(emptyRow);
            }
        }

        // 判断是否为长文本字段
        function isLongTextField(field) {
            const longTextFields = ['description', 'content', 'details', 'remark', 'note'];
            return longTextFields.includes(field.toLowerCase());
        }

        // 获取字段中文名称
        function getFieldName(field) {
            const fieldNames = {
                'id': 'ID',
                'user_id': '用户ID',
                'username': '用户名',
                'email': '邮箱',
                'password': '密码',
                'name': '名称',
                'type': '类型',
                'quality': '品质',
                'level': '等级',
                'level_requirement': '等级要求',
                'hp': '生命值',
                'mp': '法力值',
                'attack': '攻击力',
                'defense': '防御力',
                'speed': '速度',
                'exp': '经验值',
                'exp_requirement': '所需经验',
                'description': '描述',
                'difficulty': '难度',
                'map_id': '地图ID',
                'monster_id': '怪物ID',
                'drop_id': '掉落物ID',
                'item_id': '物品ID',
                'probability': '概率',
                'spawn_rate': '刷新率',
                'min_quantity': '最小数量',
                'max_quantity': '最大数量',
                'created_at': '创建时间',
                'updated_at': '更新时间',
                'last_login': '最后登录',
                'login_time': '登录时间',
                'ip_address': 'IP地址',
                'status': '状态',
                'realm_id': '境界ID',
                'weapon_id': '武器ID',
                'equipment_id': '装备ID',
                'progress': '进度',
                'completion_time': '完成时间'
            };
            return fieldNames[field] || field;
        }

        // 格式化字段值
        function formatFieldValue(field, value) {
            if (value === null || value === undefined) return '-';
            
            // 更精确的时间字段判断
            const timeFields = ['created_at', 'updated_at', 'last_login', 'login_time', 'completion_time'];
            if (timeFields.includes(field) && value && value !== '0000-00-00 00:00:00') {
                const date = new Date(value);
                if (!isNaN(date.getTime())) {
                    return date.toLocaleString('zh-CN');
                }
            }
            
            if (field === 'probability' || field === 'spawn_rate') {
                return value + '%';
            }
            
            if (typeof value === 'boolean') {
                return value ? '是' : '否';
            }
            
            if (typeof value === 'string') {
                // 限制显示长度，防止表格变形
                if (value.length > 30) {
                    return value.substring(0, 30) + '...';
                }
            }
            
            return value;
        }

        // 更新分页
        function updatePagination(pagination) {
            const paginationDiv = document.getElementById('pagination');
            if (!pagination || pagination.pages <= 1) {
                paginationDiv.innerHTML = '';
                return;
            }
            
            let html = '';
            
            // 上一页
            if (pagination.page > 1) {
                html += `<button onclick="changePage(${pagination.page - 1})">上一页</button>`;
            }
            
            // 页码
            for (let i = 1; i <= pagination.pages; i++) {
                if (i === pagination.page) {
                    html += `<button class="active">${i}</button>`;
                } else {
                    html += `<button onclick="changePage(${i})">${i}</button>`;
                }
            }
            
            // 下一页
            if (pagination.page < pagination.pages) {
                html += `<button onclick="changePage(${pagination.page + 1})">下一页</button>`;
            }
            
            paginationDiv.innerHTML = html;
        }

        // 切换页面
        function changePage(page) {
            currentPage = page;
            const search = document.getElementById('searchInput').value;
            loadData(currentType, page, search);
        }

        // 搜索处理
        function handleSearch() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                currentPage = 1;
                const search = document.getElementById('searchInput').value;
                loadData(currentType, 1, search);
            }, 500);
        }

        // 刷新列表
        function refreshList() {
            loadData(currentType, currentPage);
        }

        // 显示添加模态框
        function showAddModal() {
            document.getElementById('modalTitle').textContent = '添加' + document.getElementById('pageTitle').textContent;
            document.getElementById('editId').value = '';
            document.getElementById('editForm').reset();
            generateFormFields();
            document.getElementById('editModal').style.display = 'block';
        }

        // 显示编辑模态框
        async function showEditModal(id) {
            try {
                const response = await fetch(`api/admin_api.php?action=get&type=${currentType}&id=${id}`);
                const result = await response.json();
                
                if (result.success) {
                    document.getElementById('modalTitle').textContent = '编辑' + document.getElementById('pageTitle').textContent;
                    document.getElementById('editId').value = id;
                    generateFormFields(result.data);
                    document.getElementById('editModal').style.display = 'block';
                } else {
                    alert('加载数据失败：' + result.message);
                }
            } catch (error) {
                console.error('Error:', error);
                alert('加载数据失败');
            }
        }

        // 生成表单字段
        function generateFormFields(data = {}) {
            const formFields = document.getElementById('formFields');
            const fields = getFormFields(currentType);
            
            formFields.innerHTML = fields.map(field => {
                const value = data[field.name] || '';
                
                if (field.type === 'select') {
                    return `
                        <div class="form-group">
                            <label for="${field.name}">${field.label}：</label>
                            <select id="${field.name}" name="${field.name}" ${field.required ? 'required' : ''}>
                                ${field.options.map(option => 
                                    `<option value="${option.value}" ${value === option.value ? 'selected' : ''}>${option.label}</option>`
                                ).join('')}
                            </select>
                        </div>
                    `;
                } else if (field.type === 'textarea') {
                    return `
                        <div class="form-group">
                            <label for="${field.name}">${field.label}：</label>
                            <textarea id="${field.name}" name="${field.name}" ${field.required ? 'required' : ''}>${value}</textarea>
                        </div>
                    `;
                } else {
                    return `
                        <div class="form-group">
                            <label for="${field.name}">${field.label}：</label>
                            <input type="${field.type}" id="${field.name}" name="${field.name}" value="${value}" ${field.required ? 'required' : ''} ${field.min ? 'min="' + field.min + '"' : ''} ${field.max ? 'max="' + field.max + '"' : ''}>
                        </div>
                    `;
                }
            }).join('');
        }

        // 获取表单字段配置
        function getFormFields(type) {
            const commonFields = {
                users: [
                    { name: 'username', label: '用户名', type: 'text', required: true },
                    { name: 'password', label: '密码', type: 'password', required: true },
                    { name: 'email', label: '邮箱', type: 'email' }
                ],
                drops: [
                    { name: 'name', label: '物品名称', type: 'text', required: true },
                    { name: 'type', label: '物品类型', type: 'select', required: true, options: [
                        { value: 'weapon', label: '武器' },
                        { value: 'armor', label: '防具' },
                        { value: 'consumable', label: '消耗品' },
                        { value: 'material', label: '材料' },
                        { value: 'treasure', label: '宝物' }
                    ]},
                    { name: 'quality', label: '品质', type: 'number', required: true, min: 1, max: 10 },
                    { name: 'description', label: '描述', type: 'textarea' }
                ],
                monsters: [
                    { name: 'name', label: '怪物名称', type: 'text', required: true },
                    { name: 'level', label: '等级', type: 'number', required: true, min: 1 },
                    { name: 'hp', label: '生命值', type: 'number', required: true, min: 1 },
                    { name: 'attack', label: '攻击力', type: 'number', required: true, min: 1 },
                    { name: 'defense', label: '防御力', type: 'number', required: true, min: 0 },
                    { name: 'speed', label: '速度', type: 'number', required: true, min: 1 },
                    { name: 'description', label: '描述', type: 'textarea' }
                ],
                maps: [
                    { name: 'name', label: '地图名称', type: 'text', required: true },
                    { name: 'level_requirement', label: '等级要求', type: 'number', required: true, min: 1 },
                    { name: 'description', label: '地图描述', type: 'textarea' }
                ],
                dungeons: [
                    { name: 'name', label: '秘境名称', type: 'text', required: true },
                    { name: 'level_requirement', label: '等级要求', type: 'number', required: true, min: 1 },
                    { name: 'difficulty', label: '难度', type: 'select', required: true, options: [
                        { value: 'easy', label: '简单' },
                        { value: 'normal', label: '普通' },
                        { value: 'hard', label: '困难' },
                        { value: 'hell', label: '地狱' }
                    ]},
                    { name: 'description', label: '秘境描述', type: 'textarea' }
                ],
                realms: [
                    { name: 'name', label: '境界名称', type: 'text', required: true },
                    { name: 'level', label: '境界等级', type: 'number', required: true, min: 1 },
                    { name: 'exp_requirement', label: '所需经验', type: 'number', required: true, min: 0 },
                    { name: 'description', label: '境界描述', type: 'textarea' }
                ],
                map_drops: [
                    { name: 'map_id', label: '地图ID', type: 'number', required: true, min: 1 },
                    { name: 'drop_id', label: '掉落物ID', type: 'number', required: true, min: 1 },
                    { name: 'probability', label: '掉落概率', type: 'number', required: true, min: 0, max: 100 },
                    { name: 'min_quantity', label: '最小数量', type: 'number', required: true, min: 1 },
                    { name: 'max_quantity', label: '最大数量', type: 'number', required: true, min: 1 }
                ],
                map_monsters: [
                    { name: 'map_id', label: '地图ID', type: 'number', required: true, min: 1 },
                    { name: 'monster_id', label: '怪物ID', type: 'number', required: true, min: 1 },
                    { name: 'spawn_rate', label: '刷新率', type: 'number', required: true, min: 0, max: 100 }
                ],
                user_attributes: [
                    { name: 'user_id', label: '用户ID', type: 'number', required: true, min: 1 },
                    { name: 'level', label: '等级', type: 'number', required: true, min: 1 },
                    { name: 'exp', label: '经验值', type: 'number', required: true, min: 0 },
                    { name: 'hp', label: '生命值', type: 'number', required: true, min: 1 },
                    { name: 'mp', label: '法力值', type: 'number', required: true, min: 0 },
                    { name: 'attack', label: '攻击力', type: 'number', required: true, min: 1 },
                    { name: 'defense', label: '防御力', type: 'number', required: true, min: 0 }
                ]
            };
            
            return commonFields[type] || [
                { name: 'name', label: '名称', type: 'text', required: true },
                { name: 'description', label: '描述', type: 'textarea' }
            ];
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('editModal').style.display = 'none';
        }

        // 显示删除确认框
        function showDeleteModal(id) {
            deleteId = id;
            document.getElementById('deleteModal').style.display = 'block';
        }

        // 关闭删除确认框
        function closeDeleteModal() {
            deleteId = null;
            document.getElementById('deleteModal').style.display = 'none';
        }

        // 确认删除
        async function confirmDelete() {
            if (!deleteId) return;
            
            try {
                const response = await fetch(`api/admin_api.php?action=delete&type=${currentType}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `id=${deleteId}`
                });
                
                const result = await response.json();
                
                if (result.success) {
                    closeDeleteModal();
                    loadData(currentType, currentPage);
                } else {
                    alert('删除失败：' + result.message);
                }
            } catch (error) {
                console.error('Error:', error);
                alert('删除失败');
            }
        }

        // 处理表单提交
        document.getElementById('editForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = {};
            
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }
            
            const id = document.getElementById('editId').value;
            if (id) data.id = id;
            
            try {
                const response = await fetch(`api/admin_api.php?action=${id ? 'edit' : 'add'}&type=${currentType}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    closeModal();
                    loadData(currentType, currentPage);
                    loadStats(); // 更新统计数据
                } else {
                    alert((id ? '更新' : '添加') + '失败：' + result.message);
                }
            } catch (error) {
                console.error('Error:', error);
                alert((id ? '更新' : '添加') + '失败');
            }
        });

        // 退出登录
        async function logout() {
            if (confirm('确定要退出登录吗？')) {
                try {
                    const response = await fetch('api/admin_auth.php?action=logout');
                    const result = await response.json();
                    
                    if (result.success) {
                        window.location.href = 'login.html';
                    } else {
                        alert('退出失败：' + result.message);
                    }
                } catch (error) {
                    console.error('Error:', error);
                    alert('退出失败，请稍后重试');
                }
            }
        }

        // 判断是否为只读字段
        function isReadOnlyField(field) {
            const readOnlyFields = ['created_at', 'updated_at', 'last_login', 'login_time'];
            return readOnlyFields.includes(field);
        }

        // 开始内联编辑
        function startInlineEdit(cell, currentValue, fieldName) {
            // 如果已经在编辑中，不重复开始
            if (cell.classList.contains('editing')) {
                return;
            }
            
            // 添加编辑状态
            cell.classList.add('editing');
            
            // 获取字段类型和配置
            const fieldConfig = getFieldConfig(fieldName, currentType);
            const originalValue = currentValue || '';
            
            // 创建编辑元素
            let editElement;
            
            if (fieldConfig.type === 'select') {
                editElement = document.createElement('select');
                editElement.className = 'inline-select';
                
                fieldConfig.options.forEach(option => {
                    const optionElement = document.createElement('option');
                    optionElement.value = option.value;
                    optionElement.textContent = option.label;
                    if (option.value == originalValue) {
                        optionElement.selected = true;
                    }
                    editElement.appendChild(optionElement);
                });
            } else if (fieldConfig.type === 'textarea' || isLongTextField(fieldName)) {
                editElement = document.createElement('textarea');
                editElement.className = 'inline-textarea';
                editElement.value = originalValue;
            } else {
                editElement = document.createElement('input');
                editElement.className = 'inline-input';
                editElement.type = fieldConfig.type || 'text';
                editElement.value = originalValue;
                
                if (fieldConfig.min !== undefined) editElement.min = fieldConfig.min;
                if (fieldConfig.max !== undefined) editElement.max = fieldConfig.max;
            }
            
            // 清空单元格内容并添加编辑元素
            cell.innerHTML = '';
            cell.appendChild(editElement);
            
            // 聚焦并选中内容
            editElement.focus();
            if (editElement.select) editElement.select();
            
            // 保存函数
            const saveEdit = async () => {
                const newValue = editElement.value;
                
                // 如果值没有改变，直接取消编辑
                if (newValue == originalValue) {
                    cancelEdit();
                    return;
                }
                
                try {
                    // 显示保存中状态
                    editElement.disabled = true;
                    editElement.style.opacity = '0.6';
                    
                    // 发送AJAX请求
                    const id = cell.getAttribute('data-id');
                    const type = cell.getAttribute('data-type');
                    
                    const updateData = {
                        id: id,
                        [fieldName]: newValue
                    };
                    
                    const response = await fetch(`api/admin_api.php?action=edit&type=${type}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(updateData)
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        // 更新显示
                        cell.classList.remove('editing');
                        cell.innerHTML = `
                            <span class="field-value ${isLongTextField(fieldName) ? 'long-text' : ''}">${formatFieldValue(fieldName, newValue)}</span>
                            <span class="edit-hint">点击编辑</span>
                        `;
                        
                        // 重新绑定点击事件
                        cell.addEventListener('click', function(e) {
                            e.stopPropagation();
                            startInlineEdit(this, newValue, fieldName);
                        });
                        
                        // 显示保存成功提示
                        showSaveIndicator();
                        
                        // 更新统计数据
                        loadStats();
                    } else {
                        alert('保存失败：' + result.message);
                        cancelEdit();
                    }
                } catch (error) {
                    console.error('Error:', error);
                    alert('保存失败，请稍后重试');
                    cancelEdit();
                }
            };
            
            // 取消编辑函数
            const cancelEdit = () => {
                cell.classList.remove('editing');
                cell.innerHTML = `
                    <span class="field-value ${isLongTextField(fieldName) ? 'long-text' : ''}">${formatFieldValue(fieldName, originalValue)}</span>
                    <span class="edit-hint">点击编辑</span>
                `;
                
                // 重新绑定点击事件
                cell.addEventListener('click', function(e) {
                    e.stopPropagation();
                    startInlineEdit(this, originalValue, fieldName);
                });
            };
            
            // 绑定键盘事件
            editElement.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    saveEdit();
                } else if (e.key === 'Escape') {
                    e.preventDefault();
                    cancelEdit();
                }
            });
            
            // 绑定失焦事件（对于下拉框和输入框）
            if (fieldConfig.type !== 'textarea' && !isLongTextField(fieldName)) {
                editElement.addEventListener('blur', saveEdit);
            } else {
                // 对于文本域，只在点击外部时保存
                editElement.addEventListener('blur', function() {
                    setTimeout(() => {
                        if (!cell.contains(document.activeElement)) {
                            saveEdit();
                        }
                    }, 100);
                });
            }
        }

        // 获取字段配置
        function getFieldConfig(fieldName, type) {
            const allFields = getFormFields(type);
            const fieldConfig = allFields.find(field => field.name === fieldName);
            
            if (fieldConfig) {
                return fieldConfig;
            }
            
            // 默认配置
            const timeFields = ['created_at', 'updated_at', 'last_login', 'login_time', 'completion_time'];
            if (timeFields.includes(fieldName)) {
                return { type: 'datetime-local' };
            } else if (['level', 'hp', 'mp', 'attack', 'defense', 'speed', 'exp', 'probability', 'quantity', 'id', 'user_id', 'map_id', 'monster_id', 'drop_id', 'item_id', 'weapon_id', 'equipment_id', 'realm_id'].some(num => fieldName.includes(num)) || fieldName.includes('physical_') || fieldName.includes('immortal_') || fieldName.includes('critical_') || fieldName.includes('hit_') || fieldName.includes('dodge_') || fieldName.includes('_rate') || fieldName.includes('strength') || fieldName.includes('intelligence') || fieldName.includes('agility') || fieldName.includes('vitality')) {
                return { type: 'number', min: 0 };
            } else if (fieldName === 'email') {
                return { type: 'email' };
            } else if (fieldName === 'password') {
                return { type: 'password' };
            } else if (['description', 'content', 'details', 'remark', 'note'].includes(fieldName.toLowerCase())) {
                return { type: 'textarea' };
            } else {
                return { type: 'text' };
            }
        }

        // 显示保存成功提示
        function showSaveIndicator() {
            const indicator = document.getElementById('saveIndicator');
            indicator.style.display = 'block';
            setTimeout(() => {
                indicator.style.display = 'none';
            }, 2000);
        }

        // 打开兑换码管理器
        function openRedeemCodeManager() {
            window.open('../../public/pages/admin_redeem_codes.html', '_blank');
        }

        // 打开更新日志管理器
        function openUpdateLogManager() {
            // 直接跳转到一个独立的更新日志管理页面
            window.open('../../public/pages/admin_update_logs.html', '_blank');
        }
    </script>
</body>
</html> 