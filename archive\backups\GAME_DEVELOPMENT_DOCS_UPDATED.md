# 🎮 一念修仙游戏开发完整文档

## 📖 项目概述

一念修仙是一个基于PHP + MySQL + JavaScript的网页修仙类游戏，支持用户注册登录、角色创建、修炼系统、战斗系统、装备系统、炼丹系统、精灵系统等丰富功能。

### 🛠️ 技术环境
- **PHP**: 7.43nts
- **MySQL**: 5.7.2  
- **Nginx**: 1.52.2
- **前端**: HTML5 + CSS3 + JavaScript (ES6+)
- **数据库**: yn_game (39个表)

### 📊 项目规模
- **前端页面**: 25个
- **API接口**: 40个
- **装备总数**: 336件
- **武器技能**: 168个
- **境界等级**: 280个
- **地图关卡**: 1120个
- **怪物类型**: 104种

## 📁 完整项目结构

### 📌 根目录文件
```
yinian/
├── index.php                    # 项目入口文件
├── show_tables.php             # 数据库表结构查看工具
├── manage_item_stack_limits.php # 物品堆叠管理工具
├── check_current_stack_settings.php # 堆叠设置检查
├── README.md                   # 项目概览文档
├── GAME_DEVELOPMENT_DOCS.md   # 游戏开发完整指南
├── DATABASE_SCHEMA.md         # 数据库结构详细说明
├── PROJECT_DOCUMENTS_INDEX.md # 项目文档索引
├── project_status_tracker.md  # 项目状态跟踪
└── .cursorrules               # Cursor开发规则
```

### 🎨 public/ - 前端页面 (25个页面)

#### 🏠 主要游戏页面
| 页面 | 文件名 | 功能说明 |
|------|--------|----------|
| 项目首页 | `index.html` | 游戏入口和介绍 |
| 用户登录 | `login.html` | 用户登录界面 |
| 用户注册 | `register.html` | 用户注册界面 |
| 主游戏界面 | `game.html` | 游戏主界面 |
| 角色创建 | `character_creation.html` | 角色创建和五行灵根生成 |
| 首次登录 | `firstLogin.html` | 新用户引导 |

#### 🎮 游戏功能页面
| 页面 | 文件名 | 功能说明 |
|------|--------|----------|
| 修炼系统 | `cultivation.html` | 修炼、突破、功法学习 |
| 战斗系统 | `battle.html` | 回合制战斗界面 |
| 冒险地图 | `adventure.html` | 8个历练地图 |
| 装备管理 | `equipment_integrated.html` | 装备穿戴、属性查看 |
| 武器库 | `weapon_arsenal.html` | 武器管理和技能 |
| 炼丹系统 | `alchemy.html` | 丹药炼制 |
| 精灵系统 | `spirit_system.html` | 精灵收集和培养 |
| 商城系统 | `shop.html` | 坊市和黑市 |
| 角色属性 | `attributes.html` | 属性查看和分配 |
| 背包管理 | `inventory.html` | 物品管理 |
| 游戏设置 | `settings.html` | 系统设置 |

#### 🔧 调试和测试页面
| 页面 | 文件名 | 功能说明 |
|------|--------|----------|
| API测试 | `test_api_web.html` | 接口测试工具 |
| 数据库测试 | `db_test.html` | 数据库连接测试 |
| 地图测试 | `map.html` | 地图功能测试 |
| 调试页面 | `debug_*.html` | 各种调试工具 |

### 🔧 src/ - 后端PHP核心代码

#### 🌐 src/api/ - API接口层 (40个文件)

##### 👤 用户系统 API
| 文件 | 功能 | 说明 |
|------|------|------|
| `login.php` | 用户登录 | 账号密码验证 |
| `register.php` | 用户注册 | 新用户注册 |
| `logout.php` | 用户登出 | 会话清理 |
| `user_info.php` | 用户信息 | 获取用户详情 |
| `create_character.php` | 角色创建 | 创建角色和五行灵根 |
| `rename_character.php` | 角色改名 | 角色名称修改 |
| `check_session.php` | 会话验证 | 登录状态检查 |

##### 🏔️ 修炼系统 API
| 文件 | 功能 | 说明 |
|------|------|------|
| `cultivation.php` | 修炼系统核心 | 修炼、突破、功法学习 |

**主要功能接口**:
- `get_cultivation_info` - 获取修炼信息
- `circulation` - 周天运行
- `auto_cultivate` - 自动修炼
- `breakthrough` - 境界突破
- `learn_technique` - 学习功法
- `get_attributes` - 获取属性

##### ⚔️ 战斗系统 API
| 文件 | 功能 | 说明 |
|------|------|------|
| `battle_unified.php` | 统一战斗系统 | 回合制战斗核心 |
| `battle_drops_unified.php` | 战斗掉落系统 | 掉落计算和分配 |
| `battle_stage_info.php` | 关卡信息 | 获取关卡详情 |

##### 🎒 装备系统 API
| 文件 | 功能 | 说明 |
|------|------|------|
| `equipment_integrated.php` | 装备综合管理 | 装备穿戴、属性计算 |
| `weapon_arsenal.php` | 武器库系统 | 武器管理和技能 |
| `weapon_skills_config.php` | 武器技能配置 | 技能数据管理 |

##### 🗺️ 冒险系统 API
| 文件 | 功能 | 说明 |
|------|------|------|
| `adventure_maps.php` | 地图冒险系统 | 8个历练地图 |
| `dungeons.php` | 副本系统 | 副本挑战 |

##### 🧪 其他系统 API
| 文件 | 功能 | 说明 |
|------|------|------|
| `alchemy_system.php` | 炼丹系统 | 丹药炼制 |
| `spirit_system.php` | 精灵系统 | 精灵收集培养 |
| `redeem_code.php` | 兑换码系统 | 礼包兑换 |
| `shop.php` | 商城系统 | 坊市和黑市 |
| `leaderboard.php` | 排行榜系统 | 各类排行榜 |

## 🎯 核心系统详解

### 🏔️ 修炼系统
- **境界系统**: 280个境界等级 (28大境界 × 10小层次)
- **功法系统**: JSON格式存储，支持多功法学习
- **属性丹系统**: 5种属性丹，每种9个阶数，每阶最多使用20个
- **五行灵根**: 金木水火土五种灵根，影响对应属性伤害

### ⚔️ 战斗系统
- **回合制战斗**: 支持技能、普攻、防御等操作
- **掉落系统**: 品质随机生成（普通60%、稀有25%、史诗12%、传说3%）
- **AI模式**: 保守型、均衡型、激进型三种AI战斗模式
- **安全验证**: 三阶段验证策略，防止作弊

### 🎒 装备系统
- **装备总数**: 336件装备 (ID: 568-903)
- **职业分化**: 剑修/法修装备体系，物理/法术攻防分离
- **品质系统**: 普通、稀有、史诗、传说、神话五个品质等级
- **技能系统**: 168个武器技能，境界越高威力越强

### 🗺️ 地图系统
- **8个历练地图**: 太乙峰、碧水寒潭、赤焰谷、幽冥鬼域、青云仙山、星辰古战场、混元虚空、洪荒秘境
- **关卡总数**: 1120个关卡 (每地图140关)
- **怪物类型**: 104种不同怪物
- **难度递进**: 怪物难度连贯递增

### 🌟 五行灵根系统
- **灵根生成**: 角色创建时自动生成五行灵根
- **品质等级**: 废灵根、下品、中品、上品、极品五个品质等级
- **属性加成**: 影响对应基础属性 (筋骨、悟性、体魄、神魂、身法)
- **战斗加成**: 五行相克关系影响战斗伤害

### 🎒 物品堆叠规则 (✨ 最新更新)
| 物品类型 | 堆叠上限 | 说明 |
|----------|----------|------|
| **材料类** | **999** | 属性丹材料、炼丹材料、渡劫材料等 |
| **消耗品类** | **99** | 丹药、药品等可使用物品 |
| **装备类** | **1** | 武器、防具、饰品等装备 |
| **丹炉类** | **99** | 各品质丹炉 |
| **特殊物品** | **1** | 丹方、功法等学习类物品 |

## 🔐 安全验证系统

### 🛡️ 三阶段验证策略
1. **第一阶段 - 基础验证** (✅已完成)
   - 数值上限检查 (经验≤50,000, 金币≤10,000, 掉落≤30)
   - 战斗时间验证 (2秒-30分钟)
   - 高品质物品监控
   - 完整日志记录

2. **第二阶段 - 中级验证** (🔄计划中)
   - 属性合理性验证
   - 战斗结果验证
   - 时间序列验证
   - 掉落合理性验证

3. **第三阶段 - 完整验证** (📋远期计划)
   - 服务器端战斗引擎
   - 加密验证机制
   - 实时监控系统

## 📈 项目状态 (2024年12月19日)

### ✅ 已完成系统 (95% 完成)
- **用户系统**: 注册、登录、角色创建、会话管理 (100%)
- **修炼系统**: 修炼、突破、功法、丹药辅助 (100%)
- **装备系统**: 336件装备，14个境界完整覆盖 (100%)
- **战斗系统**: 回合制战斗、技能、掉落系统 (95%)
- **冒险系统**: 8个地图，1120个关卡 (100%)
- **五行灵根系统**: 角色天赋属性系统 (100%)
- **炼丹系统**: 丹药炼制、材料管理 (100%)
- **精灵系统**: 精灵收集、技能、进阶 (100%)
- **兑换码系统**: 礼包兑换、类型管理 (100%)
- **商城系统**: 坊市黑市、功法购买 (100%)

### 🔧 维护工具
- **数据库管理**: `show_tables.php`, `check_table_structure.php`
- **物品管理**: `manage_item_stack_limits.php`, `check_current_stack_settings.php`
- **背包整理**: `fix_inventory_standalone.php`, `final_fix_organize.php`
- **API测试**: `test_api_web.html`, `debug_*.php`

## 🚀 性能优化

### 🔍 数据库优化
- **索引策略**: 为常用查询字段添加索引
- **查询优化**: 避免N+1查询，使用JOIN优化
- **缓存策略**: 对频繁查询的数据进行缓存
- **分页处理**: 大数据量查询必须分页

### 💾 代码优化
- **函数复用**: 公共功能提取到functions.php
- **内存管理**: 及时释放大对象和数组
- **错误处理**: 统一的错误处理机制
- **代码规范**: 遵循PSR标准和团队约定

## 🔄 最新更新记录

### 📅 2024年12月19日更新
- ✅ **物理/法术攻防分离**: 340个物品属性迁移完成
- ✅ **材料堆叠优化**: 材料类物品堆叠上限调整为999
- ✅ **五行灵根系统**: 角色创建时自动生成五行灵根
- ✅ **神魂损伤机制**: 渡劫失败后的神魂修复系统

### 📅 2024年12月18日更新
- ✅ **属性丹系统**: 丹毒机制实现
- ✅ **背包整理**: 智能堆叠和背包扩展
- ✅ **装备品质**: 品质系统重构

### 📅 2024年12月17日更新
- ✅ **武器技能**: 168个武器技能完善
- ✅ **耐久系统**: 装备耐久度机制优化

---

*文档最后更新：2024年12月19日*  
*基于实际项目状态：39个数据表，40个API文件，25个前端页面，336件装备* 