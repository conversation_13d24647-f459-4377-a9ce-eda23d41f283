# 一念修仙 API 测试文件
# 使用 REST Client 扩展进行API测试

### 变量定义
@baseUrl = http://localhost/yinian
@apiUrl = {{baseUrl}}/src/api

### 1. 用户登录测试
POST {{apiUrl}}/login.php
Content-Type: application/x-www-form-urlencoded

username=testuser&password=testpass

### 2. 获取用户信息
GET {{apiUrl}}/user_info.php

### 3. 获取角色信息
GET {{apiUrl}}/cultivation.php?action=get_attributes

### 4. 修炼系统测试
POST {{apiUrl}}/cultivation.php
Content-Type: application/x-www-form-urlencoded

action=cultivate

### 5. 装备系统测试
GET {{apiUrl}}/equipment_integrated.php?action=get_user_stats

### 6. 商店系统测试
GET {{apiUrl}}/shop.php?action=get_items

### 7. 竞技场系统测试
GET {{apiUrl}}/immortal_arena.php?action=get_arena_info

### 8. 炼丹系统测试
GET {{apiUrl}}/alchemy_system.php?action=get_recipes

### 9. 战斗系统测试
POST {{apiUrl}}/battle_unified.php
Content-Type: application/json

{
    "action": "start_battle",
    "enemy_type": "monster",
    "enemy_id": 1
}

### 10. 登出测试
POST {{apiUrl}}/logout.php

### 11. 注册测试
POST {{apiUrl}}/register.php
Content-Type: application/x-www-form-urlencoded

username=newuser&password=newpass&email=<EMAIL>

### 12. 创建角色测试
POST {{apiUrl}}/create_character.php
Content-Type: application/json

{
    "character_name": "测试角色",
    "spiritual_root": "金"
}

### 13. 获取排行榜
GET {{apiUrl}}/leaderboard.php?type=level

### 14. 冒险地图测试
GET {{apiUrl}}/adventure_maps.php?action=get_maps

### 15. 技能系统测试
GET {{apiUrl}}/technique_fragment_synthesis.php?action=get_fragments
