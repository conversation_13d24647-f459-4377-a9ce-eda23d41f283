/**
 * 冰系技能动画样式 - 冰锥术
 * 对应 animation_model = 'bingzhuishu'
 */

/* 动画容器 */
.bingzhuishu-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1000;
}

/* === 蓄力阶段动画 === */

/* 寒冰魔法阵 */
.bingzhuishu-magic-circle {
    position: absolute;
    width: 135px;
    height: 135px;
    transform: translate(-50%, -50%);
    border: 4px solid rgba(173, 216, 230, 0.9);
    border-radius: 50%;
    background: radial-gradient(circle, rgba(173, 216, 230, 0.4) 0%, rgba(135, 206, 235, 0.2) 50%, transparent 100%);
    animation: bingzhuishu-magic-circle 1.1s ease-out;
    box-shadow: 0 0 35px rgba(173, 216, 230, 0.8), inset 0 0 25px rgba(135, 206, 235, 0.6);
}

@keyframes bingzhuishu-magic-circle {
    0% {
        transform: translate(-50%, -50%) scale(0) rotate(0deg);
        opacity: 0;
    }
    35% {
        transform: translate(-50%, -50%) scale(1.3) rotate(120deg);
        opacity: 0.9;
    }
    100% {
        transform: translate(-50%, -50%) scale(1) rotate(360deg);
        opacity: 1;
    }
}

/* 内圈冰霜符文 */
.bingzhuishu-inner-runes {
    position: absolute;
    width: 95px;
    height: 95px;
    transform: translate(-50%, -50%);
    border: 3px solid rgba(135, 206, 235, 0.9);
    border-radius: 50%;
    background: conic-gradient(from 0deg, 
        rgba(173, 216, 230, 0.7), 
        rgba(135, 206, 235, 0.5), 
        rgba(176, 224, 230, 0.3), 
        rgba(135, 206, 235, 0.5), 
        rgba(173, 216, 230, 0.7));
    animation: bingzhuishu-inner-runes 1.1s ease-out infinite;
}

@keyframes bingzhuishu-inner-runes {
    0%, 100% {
        transform: translate(-50%, -50%) scale(0.9) rotate(0deg);
        opacity: 0.8;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.1) rotate(180deg);
        opacity: 0.5;
    }
}

/* 外圈寒气符文 */
.bingzhuishu-outer-runes {
    position: absolute;
    width: 155px;
    height: 155px;
    transform: translate(-50%, -50%);
    border: 2px solid rgba(176, 224, 230, 0.7);
    border-radius: 50%;
    background: conic-gradient(from 180deg, 
        rgba(135, 206, 235, 0.4), 
        rgba(176, 224, 230, 0.2), 
        rgba(230, 230, 250, 0.1), 
        rgba(176, 224, 230, 0.2), 
        rgba(135, 206, 235, 0.4));
    animation: bingzhuishu-outer-runes 1.7s ease-out infinite;
}

@keyframes bingzhuishu-outer-runes {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1) rotate(0deg);
        opacity: 0.7;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.2) rotate(-180deg);
        opacity: 0.4;
    }
}

/* 武器图片旋转 */
.bingzhuishu-weapon-sprite {
    position: absolute;
    width: 48px;
    height: 48px;
    transform: translate(-50%, -50%);
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    animation: bingzhuishu-weapon-rotate 1.1s linear infinite;
    -webkit-filter: drop-shadow(0 0 10px rgba(173, 216, 230, 0.9));
    filter: drop-shadow(0 0 10px rgba(173, 216, 230, 0.9));
}

@keyframes bingzhuishu-weapon-rotate {
    0% { transform: translate(-50%, -50%) rotate(0deg) scale(1); }
    25% { transform: translate(-50%, -50%) rotate(90deg) scale(1.1); }
    50% { transform: translate(-50%, -50%) rotate(180deg) scale(1.2); }
    75% { transform: translate(-50%, -50%) rotate(270deg) scale(1.1); }
    100% { transform: translate(-50%, -50%) rotate(360deg) scale(1); }
}

/* 蓄力能量核心 */
.bingzhuishu-energy-core {
    position: absolute;
    width: 38px;
    height: 38px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, rgba(173, 216, 230, 0.9) 0%, rgba(135, 206, 235, 0.7) 50%, transparent 100%);
    border-radius: 50%;
    animation: bingzhuishu-energy-pulse 0.9s ease-in-out infinite alternate;
    box-shadow: 0 0 25px rgba(173, 216, 230, 0.9);
}

@keyframes bingzhuishu-energy-pulse {
    0% {
        transform: translate(-50%, -50%) scale(0.7);
        box-shadow: 0 0 20px rgba(173, 216, 230, 0.7);
    }
    100% {
        transform: translate(-50%, -50%) scale(1.3);
        box-shadow: 0 0 30px rgba(173, 216, 230, 1);
    }
}

/* 冰霜粒子汇聚效果 */
.bingzhuishu-charge-frost {
    position: absolute;
    width: 5px;
    height: 5px;
    transform: translate(-50%, -50%);
    background: linear-gradient(45deg, rgba(173, 216, 230, 0.9), rgba(135, 206, 235, 0.8));
    border-radius: 40%;
    animation: bingzhuishu-frost-gather 1.1s ease-out forwards;
    box-shadow: 0 0 5px rgba(173, 216, 230, 0.7);
}

@keyframes bingzhuishu-frost-gather {
    0% {
        transform: translate(calc(-50% + var(--chargeX)), calc(-50% + var(--chargeY))) scale(0) rotate(0deg);
        opacity: 0;
    }
    35% {
        transform: translate(calc(-50% + var(--chargeX) * 0.65), calc(-50% + var(--chargeY) * 0.65)) scale(1.2) rotate(150deg);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(0.4) rotate(360deg);
        opacity: 0;
    }
}

/* 环绕寒气螺旋 */
.bingzhuishu-charge-spiral {
    position: absolute;
    width: 10px;
    height: 10px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, rgba(135, 206, 235, 0.8), rgba(176, 224, 230, 0.4));
    border-radius: 50%;
    animation: bingzhuishu-spiral-move 1s ease-out forwards;
}

@keyframes bingzhuishu-spiral-move {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0;
    }
    30% {
        transform: translate(-50%, -50%) scale(1.7);
        opacity: 0.9;
    }
    70% {
        transform: translate(-50%, -50%) scale(1.3);
        opacity: 0.6;
    }
    100% {
        transform: translate(-50%, -50%) scale(0.2);
        opacity: 0;
    }
}

/* 寒冰能量波纹 */
.bingzhuishu-energy-ripple {
    position: absolute;
    width: 28px;
    height: 28px;
    transform: translate(-50%, -50%);
    border: 3px solid rgba(173, 216, 230, 0.7);
    border-radius: 50%;
    animation: bingzhuishu-ripple-expand 0.9s ease-out forwards;
}

@keyframes bingzhuishu-ripple-expand {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0.9;
        border-width: 3px;
    }
    100% {
        transform: translate(-50%, -50%) scale(9);
        opacity: 0;
        border-width: 1px;
    }
}

/* 冰晶凝结效果 */
.bingzhuishu-ice-crystal {
    position: absolute;
    width: 4px;
    height: 8px;
    transform: translate(-50%, -50%);
    background: linear-gradient(to top, rgba(173, 216, 230, 0.8), rgba(230, 230, 250, 0.6));
    -webkit-clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    animation: bingzhuishu-crystal-form 1.3s ease-out forwards;
}

@keyframes bingzhuishu-crystal-form {
    0% {
        transform: translate(-50%, -50%) scaleY(0) rotate(0deg);
        opacity: 0;
    }
    50% {
        transform: translate(-50%, -50%) scaleY(1) rotate(30deg);
        opacity: 0.8;
    }
    100% {
        transform: translate(-50%, -50%) scaleY(0.3) rotate(60deg);
        opacity: 0;
    }
}

/* 寒气弥漫效果 */
.bingzhuishu-cold-mist {
    position: absolute;
    width: 8px;
    height: 8px;
    transform: translate(-50%, -50%);
    background: rgba(176, 224, 230, 0.6);
    border-radius: 50%;
    animation: bingzhuishu-mist-rise 1.6s ease-out forwards;
}

@keyframes bingzhuishu-mist-rise {
    0% {
        transform: translate(-50%, -50%) scale(0.4);
        opacity: 0.6;
    }
    50% {
        transform: translate(-50%, calc(-50% - 20px)) scale(1.8);
        opacity: 0.4;
    }
    100% {
        transform: translate(-50%, calc(-50% - 40px)) scale(3);
        opacity: 0;
    }
}

/* 冰霜共鸣效果 */
.bingzhuishu-frost-resonance {
    position: absolute;
    width: 22px;
    height: 22px;
    transform: translate(-50%, -50%);
    border: 2px solid rgba(135, 206, 235, 0.7);
    border-radius: 50%;
    animation: bingzhuishu-resonance-wave 1.8s ease-out forwards;
}

@keyframes bingzhuishu-resonance-wave {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0.7;
    }
    50% {
        transform: translate(-50%, -50%) scale(3);
        opacity: 0.4;
    }
    100% {
        transform: translate(-50%, -50%) scale(6);
        opacity: 0;
    }
}

/* === 发射阶段动画 === */

/* 预发射寒气爆发 */
.bingzhuishu-cold-burst {
    position: absolute;
    width: 85px;
    height: 85px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, rgba(173, 216, 230, 0.6), transparent);
    border-radius: 50%;
    animation: bingzhuishu-cold-burst 0.25s ease-in-out 4;
}

@keyframes bingzhuishu-cold-burst {
    0%, 100% { transform: translate(-50%, -50%) scale(1); }
    50% { transform: translate(-50%, -50%) scale(1.4); }
}

/* 主冰锥 - 更长更快，两端尖锐 */
.bingzhuishu-main-cone {
    position: absolute;
    width: 15px; /* 宽度变细一倍：30px → 15px */
    height: 140px; /* 长度加长一倍：70px → 140px */
    transform: translate(-50%, -50%);
    background: linear-gradient(to bottom, 
        rgba(230, 230, 250, 0.4) 0%, 
        rgba(176, 224, 230, 0.6) 20%, 
        rgba(135, 206, 235, 0.8) 50%, 
        rgba(176, 224, 230, 0.6) 80%, 
        rgba(230, 230, 250, 0.4) 100%);
    /* 两端尖锐的形状：上尖下尖 */
    -webkit-clip-path: polygon(50% 0%, 80% 20%, 100% 50%, 80% 80%, 50% 100%, 20% 80%, 0% 50%, 20% 20%);
    clip-path: polygon(50% 0%, 80% 20%, 100% 50%, 80% 80%, 50% 100%, 20% 80%, 0% 50%, 20% 20%);
    animation: bingzhuishu-cone-fly var(--flyTime) linear forwards;
    box-shadow: 0 0 12px rgba(173, 216, 230, 0.7);
}

/* 副冰锥1 - 左后方跟随，更长，两端尖锐 */
.bingzhuishu-sub-cone-1 {
    position: absolute;
    width: 11px; /* 宽度变细一倍：22px → 11px */
    height: 100px; /* 长度加长一倍：50px → 100px */
    transform: translate(-50%, -50%);
    background: linear-gradient(to bottom, 
        rgba(230, 230, 250, 0.3) 0%, 
        rgba(176, 224, 230, 0.5) 20%, 
        rgba(135, 206, 235, 0.7) 50%, 
        rgba(176, 224, 230, 0.5) 80%, 
        rgba(230, 230, 250, 0.3) 100%);
    /* 两端尖锐的形状：上尖下尖 */
    -webkit-clip-path: polygon(50% 0%, 80% 20%, 100% 50%, 80% 80%, 50% 100%, 20% 80%, 0% 50%, 20% 20%);
    clip-path: polygon(50% 0%, 80% 20%, 100% 50%, 80% 80%, 50% 100%, 20% 80%, 0% 50%, 20% 20%);
    animation: bingzhuishu-sub-cone-fly var(--flyTime) linear forwards;
    box-shadow: 0 0 8px rgba(173, 216, 230, 0.6);
}

/* 副冰锥2 - 右后方跟随，更长，两端尖锐 */
.bingzhuishu-sub-cone-2 {
    position: absolute;
    width: 12.5px; /* 宽度变细一倍：25px → 12.5px */
    height: 110px; /* 长度加长一倍：55px → 110px */
    transform: translate(-50%, -50%);
    background: linear-gradient(to bottom, 
        rgba(230, 230, 250, 0.35) 0%, 
        rgba(176, 224, 230, 0.55) 20%, 
        rgba(135, 206, 235, 0.75) 50%, 
        rgba(176, 224, 230, 0.55) 80%, 
        rgba(230, 230, 250, 0.35) 100%);
    /* 两端尖锐的形状：上尖下尖 */
    -webkit-clip-path: polygon(50% 0%, 80% 20%, 100% 50%, 80% 80%, 50% 100%, 20% 80%, 0% 50%, 20% 20%);
    clip-path: polygon(50% 0%, 80% 20%, 100% 50%, 80% 80%, 50% 100%, 20% 80%, 0% 50%, 20% 20%);
    animation: bingzhuishu-sub-cone-fly var(--flyTime) linear forwards;
    box-shadow: 0 0 10px rgba(173, 216, 230, 0.65);
}

@keyframes bingzhuishu-cone-fly {
    0% {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 0.8;
    }
    50% {
        transform: translate(calc(-50% + var(--targetX) * 0.5), calc(-50% + var(--targetY) * 0.5)) 
                   scale(1.2);
        opacity: 1;
    }
    100% {
        transform: translate(calc(-50% + var(--targetX)), calc(-50% + var(--targetY))) 
                   scale(1);
        opacity: 0.9;
    }
}

@keyframes bingzhuishu-sub-cone-fly {
    0% {
        transform: translate(-50%, -50%) scale(0.7);
        opacity: 0.7;
    }
    50% {
        transform: translate(calc(-50% + var(--targetX) * 0.5), calc(-50% + var(--targetY) * 0.5)) 
                   scale(1.1);
        opacity: 0.9;
    }
    100% {
        transform: translate(calc(-50% + var(--targetX)), calc(-50% + var(--targetY))) 
                   scale(0.9);
        opacity: 0.8;
    }
}

/* 冰锥旋转层 - 两端尖锐，更长更细 */
.bingzhuishu-spiral-layer-1 {
    position: absolute;
    width: 10px; /* 宽度变细一倍：20px → 10px */
    height: 80px; /* 长度加长一倍：40px → 80px */
    transform: translate(-50%, -50%);
    background: linear-gradient(to bottom, 
        rgba(176, 224, 230, 0.4) 0%, 
        rgba(135, 206, 235, 0.6) 50%, 
        rgba(176, 224, 230, 0.4) 100%);
    /* 两端尖锐的形状：上尖下尖 */
    -webkit-clip-path: polygon(50% 0%, 80% 20%, 100% 50%, 80% 80%, 50% 100%, 20% 80%, 0% 50%, 20% 20%);
    clip-path: polygon(50% 0%, 80% 20%, 100% 50%, 80% 80%, 50% 100%, 20% 80%, 0% 50%, 20% 20%);
    animation: bingzhuishu-spiral-fly var(--flyTime) linear forwards;
}

.bingzhuishu-spiral-layer-2 {
    position: absolute;
    width: 9px; /* 宽度变细一倍：18px → 9px */
    height: 70px; /* 长度加长一倍：35px → 70px */
    transform: translate(-50%, -50%);
    background: linear-gradient(to bottom, 
        rgba(135, 206, 235, 0.3) 0%, 
        rgba(176, 224, 230, 0.5) 50%, 
        rgba(135, 206, 235, 0.3) 100%);
    /* 两端尖锐的形状：上尖下尖 */
    -webkit-clip-path: polygon(50% 0%, 80% 20%, 100% 50%, 80% 80%, 50% 100%, 20% 80%, 0% 50%, 20% 20%);
    clip-path: polygon(50% 0%, 80% 20%, 100% 50%, 80% 80%, 50% 100%, 20% 80%, 0% 50%, 20% 20%);
    animation: bingzhuishu-spiral-fly var(--flyTime) linear forwards;
}

.bingzhuishu-spiral-layer-3 {
    position: absolute;
    width: 7.5px; /* 宽度变细一倍：15px → 7.5px */
    height: 60px; /* 长度加长一倍：30px → 60px */
    transform: translate(-50%, -50%);
    background: linear-gradient(to bottom, 
        rgba(176, 224, 230, 0.2) 0%, 
        rgba(135, 206, 235, 0.4) 50%, 
        rgba(176, 224, 230, 0.2) 100%);
    /* 两端尖锐的形状：上尖下尖 */
    -webkit-clip-path: polygon(50% 0%, 80% 20%, 100% 50%, 80% 80%, 50% 100%, 20% 80%, 0% 50%, 20% 20%);
    clip-path: polygon(50% 0%, 80% 20%, 100% 50%, 80% 80%, 50% 100%, 20% 80%, 0% 50%, 20% 20%);
    animation: bingzhuishu-spiral-fly var(--flyTime) linear forwards;
}

@keyframes bingzhuishu-spiral-fly {
    0% {
        transform: translate(-50%, -50%);
        opacity: 0.6;
    }
    100% {
        transform: translate(calc(-50% + var(--targetX)), calc(-50% + var(--targetY)));
        opacity: 0.3;
    }
}

/* 增强冰霜拖尾 - 两端尖锐，更长更细 */
.bingzhuishu-enhanced-trail {
    position: absolute;
    width: calc(11px - var(--trailIndex) * 0.75px); /* 宽度变细一倍：22px → 11px */
    height: calc(90px - var(--trailIndex) * 6px); /* 长度加长一倍：45px → 90px */
    transform: translate(-50%, -50%);
    background: linear-gradient(to bottom, 
        rgba(176, 224, 230, calc(0.15 - var(--trailIndex) * 0.0125)) 0%,
        rgba(135, 206, 235, calc(0.5 - var(--trailIndex) * 0.04)) 50%, 
        rgba(176, 224, 230, calc(0.15 - var(--trailIndex) * 0.0125)) 100%);
    /* 两端尖锐的形状：上尖下尖 */
    -webkit-clip-path: polygon(50% 0%, 80% 20%, 100% 50%, 80% 80%, 50% 100%, 20% 80%, 0% 50%, 20% 20%);
    clip-path: polygon(50% 0%, 80% 20%, 100% 50%, 80% 80%, 50% 100%, 20% 80%, 0% 50%, 20% 20%);
    animation: bingzhuishu-trail-fly var(--flyTime) linear forwards;
}

@keyframes bingzhuishu-trail-fly {
    0% {
        transform: translate(-50%, -50%);
        opacity: 0.4;
    }
    100% {
        transform: translate(calc(-50% + var(--targetX)), calc(-50% + var(--targetY)));
        opacity: 0;
    }
}

/* 飞行冰晶散落 */
.bingzhuishu-flight-crystal {
    position: absolute;
    width: 3px;
    height: 3px;
    transform: translate(-50%, -50%);
    background: rgba(173, 216, 230, 0.7);
    border-radius: 50%;
    animation: bingzhuishu-flight-crystal 0.9s ease-out forwards;
}

@keyframes bingzhuishu-flight-crystal {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0.7;
    }
    50% {
        transform: translate(-50%, -50%) scale(2);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(0.5);
        opacity: 0;
    }
}

/* 寒气轨迹 */
.bingzhuishu-flight-mist {
    position: absolute;
    width: 6px;
    height: 6px;
    transform: translate(-50%, -50%);
    background: rgba(176, 224, 230, 0.5);
    border-radius: 50%;
    animation: bingzhuishu-flight-mist 0.7s ease-out forwards;
}

@keyframes bingzhuishu-flight-mist {
    0% {
        transform: translate(-50%, -50%) scale(0.5);
        opacity: 0.5;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0.3;
    }
    100% {
        transform: translate(-50%, -50%) scale(2.5);
        opacity: 0;
    }
}

/* === 击中阶段动画 === */

/* 瞬间冰爆闪光 */
.bingzhuishu-impact-flash {
    position: absolute;
    width: 75px;
    height: 75px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, rgba(173, 216, 230, 0.9), rgba(135, 206, 235, 0.7), transparent);
    border-radius: 50%;
    animation: bingzhuishu-impact-flash 0.35s ease-out forwards;
}

@keyframes bingzhuishu-impact-flash {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
    }
    45% {
        transform: translate(-50%, -50%) scale(1.8);
        opacity: 0.8;
    }
    100% {
        transform: translate(-50%, -50%) scale(4);
        opacity: 0;
    }
}

/* 冰爆核心 */
.bingzhuishu-ice-core {
    position: absolute;
    width: 85px;
    height: 85px;
    transform: translate(-50%, -50%);
    background: conic-gradient(from 0deg, 
        rgba(173, 216, 230, 0.9), 
        rgba(135, 206, 235, 0.7), 
        rgba(176, 224, 230, 0.5), 
        rgba(135, 206, 235, 0.7), 
        rgba(173, 216, 230, 0.9));
    border-radius: 50%;
    animation: bingzhuishu-ice-core 2.0s ease-out forwards;
}

@keyframes bingzhuishu-ice-core {
    0% {
        transform: translate(-50%, -50%) rotate(0deg) scale(0);
        opacity: 0.9;
    }
    35% {
        transform: translate(-50%, -50%) rotate(180deg) scale(1.3);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) rotate(540deg) scale(0.2);
        opacity: 0;
    }
}

/* 冰霜冲击波 */
.bingzhuishu-impact-shockwave {
    position: absolute;
    width: 45px;
    height: 45px;
    transform: translate(-50%, -50%);
    border: 4px solid rgba(173, 216, 230, 0.7);
    border-radius: 50%;
    animation: bingzhuishu-shockwave-expand 1s ease-out forwards;
}

@keyframes bingzhuishu-shockwave-expand {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0.9;
        border-width: 4px;
    }
    100% {
        transform: translate(-50%, -50%) scale(7);
        opacity: 0;
        border-width: 1px;
    }
}

/* 冰晶碎片爆发 */
.bingzhuishu-ice-shard {
    position: absolute;
    width: 5px;
    height: 10px;
    transform: translate(-50%, -50%);
    background: linear-gradient(45deg, rgba(173, 216, 230, 0.9), rgba(135, 206, 235, 0.7));
    -webkit-clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    animation: bingzhuishu-shard-fly calc(1.5s * var(--shardVelocity)) ease-out forwards;
}

@keyframes bingzhuishu-shard-fly {
    0% {
        transform: translate(-50%, -50%) rotate(var(--shardAngle)) scale(0);
        opacity: 0.9;
    }
    25% {
        transform: translate(-50%, -50%) 
                   rotate(var(--shardAngle)) 
                   translateX(calc(var(--shardDistance) * 0.25)) 
                   scale(1.5);
        opacity: 1;
    }
    70% {
        transform: translate(-50%, -50%) 
                   rotate(var(--shardAngle)) 
                   translateX(calc(var(--shardDistance) * 0.8)) 
                   scale(1);
        opacity: 0.6;
    }
    100% {
        transform: translate(-50%, -50%) 
                   rotate(var(--shardAngle)) 
                   translateX(var(--shardDistance)) 
                   scale(0.2);
        opacity: 0;
    }
}

/* 霜冻扩散波 */
.bingzhuishu-frost-wave {
    position: absolute;
    width: 60px;
    height: 60px;
    transform: translate(-50%, -50%);
    border: 3px solid rgba(135, 206, 235, 0.6);
    border-radius: 50%;
    background: radial-gradient(circle, transparent 60%, rgba(173, 216, 230, 0.2) 80%, transparent 100%);
    animation: bingzhuishu-frost-wave 1.5s ease-out forwards;
}

@keyframes bingzhuishu-frost-wave {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0.6;
    }
    50% {
        transform: translate(-50%, -50%) scale(3);
        opacity: 0.4;
    }
    100% {
        transform: translate(-50%, -50%) scale(6);
        opacity: 0;
    }
}

/* 冰柱突起 */
.bingzhuishu-icicle {
    position: absolute;
    width: 8px;
    height: var(--icicleHeight);
    transform: translate(-50%, -100%);
    background: linear-gradient(to top, 
        rgba(173, 216, 230, 0.8) 0%, 
        rgba(135, 206, 235, 0.6) 60%, 
        rgba(230, 230, 250, 0.4) 100%);
    -webkit-clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    animation: bingzhuishu-icicle-emerge 0.8s ease-out forwards;
}

@keyframes bingzhuishu-icicle-emerge {
    0% {
        transform: translate(-50%, -100%) scaleY(0);
        opacity: 0;
    }
    60% {
        transform: translate(-50%, -100%) scaleY(1.1);
        opacity: 0.8;
    }
    100% {
        transform: translate(-50%, -100%) scaleY(1);
        opacity: 0.7;
    }
}

/* 寒雾弥漫 */
.bingzhuishu-impact-mist {
    position: absolute;
    width: 16px;
    height: 16px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, rgba(176, 224, 230, 0.6), transparent);
    border-radius: 50%;
    animation: bingzhuishu-mist-spread 2.5s ease-out forwards;
}

@keyframes bingzhuishu-mist-spread {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0.6;
    }
    40% {
        transform: translate(-50%, -50%) scale(4);
        opacity: 0.4;
    }
    100% {
        transform: translate(-50%, -50%) scale(8);
        opacity: 0;
    }
}

/* 冰霜结晶 */
.bingzhuishu-impact-crystal {
    position: absolute;
    width: 6px;
    height: 12px;
    transform: translate(-50%, -50%);
    background: linear-gradient(to top, rgba(173, 216, 230, 0.7), rgba(230, 230, 250, 0.5));
    -webkit-clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    animation: bingzhuishu-crystal-grow 1.8s ease-out forwards;
}

@keyframes bingzhuishu-crystal-grow {
    0% {
        transform: translate(-50%, -50%) scaleY(0) rotate(0deg);
        opacity: 0;
    }
    40% {
        transform: translate(-50%, -50%) scaleY(1) rotate(45deg);
        opacity: 0.7;
    }
    100% {
        transform: translate(-50%, -50%) scaleY(0.3) rotate(90deg);
        opacity: 0;
    }
}

/* 寒气余韵 */
.bingzhuishu-afterchill {
    position: absolute;
    width: 14px;
    height: 14px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, rgba(230, 230, 250, 0.7), rgba(176, 224, 230, 0.4));
    border-radius: 50%;
    animation: bingzhuishu-afterchill 2.8s ease-out forwards;
}

@keyframes bingzhuishu-afterchill {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0.7;
    }
    50% {
        transform: translate(-50%, -50%) scale(3);
        opacity: 0.5;
    }
    100% {
        transform: translate(-50%, -50%) scale(6);
        opacity: 0;
    }
}

/* 敌人受击效果 */
@keyframes ice-hit {
    0% { -webkit-filter: hue-rotate(0deg) saturate(1); }
    0% { filter: hue-rotate(0deg) saturate(1); }
    25% { -webkit-filter: hue-rotate(200deg) saturate(1.7) brightness(1.4); }
    25% { filter: hue-rotate(200deg) saturate(1.7) brightness(1.4); }
    50% { -webkit-filter: hue-rotate(0deg) saturate(1) brightness(0.7); }
    50% { filter: hue-rotate(0deg) saturate(1) brightness(0.7); }
    75% { -webkit-filter: hue-rotate(200deg) saturate(1.4) brightness(1.2); }
    75% { filter: hue-rotate(200deg) saturate(1.4) brightness(1.2); }
    100% { -webkit-filter: hue-rotate(0deg) saturate(1); }
    100% { filter: hue-rotate(0deg) saturate(1); }
}

@keyframes ice-freeze {
    0%, 100% { transform: translate(0, 0); }
    33% { transform: translate(-2px, 0); }
    66% { transform: translate(2px, 0); }
}

/* 移动端适配 */
@media (max-width: 768px) {
    .bingzhuishu-container {
        transform: scale(0.8);
    }
    
    .bingzhuishu-magic-circle {
        width: 115px;
        height: 115px;
    }
    
    .bingzhuishu-main-cone {
        width: 11px;
        height: 90px;
    }
    
    .bingzhuishu-sub-cone-1 {
        width: 8px;
        height: 64px;
    }
    
    .bingzhuishu-sub-cone-2 {
        width: 9px;
        height: 72px;
    }
    
    .bingzhuishu-impact-flash {
        width: 65px;
        height: 65px;
    }
}

/* ❄️⚔️ 玄冰剑技能动画样式 */

/* 动画容器 */
.xuanbingjian-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1000;
    will-change: auto;
    transform: translateZ(0);
}

/* === 第一阶段：冰锥蓄力效果 === */

/* 玄冰剑寒冰魔法阵 */
.xuanbingjian-ice-circle {
    position: absolute;
    width: 120px;
    height: 120px;
    transform: translate(-50%, -50%);
    border: 3px solid rgba(173, 216, 230, 0.9);
    border-radius: 50%;
    background: radial-gradient(circle, rgba(173, 216, 230, 0.3) 0%, rgba(135, 206, 235, 0.2) 50%, transparent 100%);
    animation: xuanbingjian-ice-circle 1.5s ease-out;
    -webkit-filter: drop-shadow(0 0 20px rgba(173, 216, 230, 0.8));
    filter: drop-shadow(0 0 20px rgba(173, 216, 230, 0.8));
}

@keyframes xuanbingjian-ice-circle {
    0% {
        transform: translate(-50%, -50%) scale(0) rotate(0deg);
        opacity: 0;
    }
    30% {
        transform: translate(-50%, -50%) scale(1.2) rotate(120deg);
        opacity: 0.8;
    }
    100% {
        transform: translate(-50%, -50%) scale(1) rotate(360deg);
        opacity: 1;
    }
}

/* 内圈冰霜符文 */
.xuanbingjian-inner-runes {
    position: absolute;
    width: 80px;
    height: 80px;
    transform: translate(-50%, -50%);
    border: 2px solid rgba(135, 206, 235, 0.8);
    border-radius: 50%;
    background: conic-gradient(from 0deg, 
        rgba(173, 216, 230, 0.6), 
        rgba(135, 206, 235, 0.4), 
        rgba(176, 224, 230, 0.2), 
        rgba(135, 206, 235, 0.4), 
        rgba(173, 216, 230, 0.6));
    animation: xuanbingjian-inner-runes 1.5s ease-out infinite;
}

@keyframes xuanbingjian-inner-runes {
    0%, 100% {
        transform: translate(-50%, -50%) scale(0.8) rotate(0deg);
        opacity: 0.7;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.1) rotate(180deg);
        opacity: 0.4;
    }
}

/* 外圈寒气符文 */
.xuanbingjian-outer-runes {
    position: absolute;
    width: 140px;
    height: 140px;
    transform: translate(-50%, -50%);
    border: 2px solid rgba(176, 224, 230, 0.6);
    border-radius: 50%;
    background: conic-gradient(from 180deg, 
        rgba(135, 206, 235, 0.3), 
        rgba(176, 224, 230, 0.1), 
        rgba(230, 230, 250, 0.05), 
        rgba(176, 224, 230, 0.1), 
        rgba(135, 206, 235, 0.3));
    animation: xuanbingjian-outer-runes 2.0s ease-out infinite;
}

@keyframes xuanbingjian-outer-runes {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1) rotate(0deg);
        opacity: 0.6;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.3) rotate(-180deg);
        opacity: 0.3;
    }
}

/* 蓄力能量核心 */
.xuanbingjian-energy-core {
    position: absolute;
    width: 30px;
    height: 30px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, rgba(173, 216, 230, 0.9) 0%, rgba(135, 206, 235, 0.7) 50%, transparent 100%);
    border-radius: 50%;
    animation: xuanbingjian-energy-pulse 0.8s ease-in-out infinite alternate;
    -webkit-filter: drop-shadow(0 0 15px rgba(173, 216, 230, 0.9));
    filter: drop-shadow(0 0 15px rgba(173, 216, 230, 0.9));
}

@keyframes xuanbingjian-energy-pulse {
    0% {
        transform: translate(-50%, -50%) scale(0.6);
        -webkit-filter: drop-shadow(0 0 10px rgba(173, 216, 230, 0.7));
        filter: drop-shadow(0 0 10px rgba(173, 216, 230, 0.7));
    }
    100% {
        transform: translate(-50%, -50%) scale(1.4);
        -webkit-filter: drop-shadow(0 0 25px rgba(173, 216, 230, 1));
        filter: drop-shadow(0 0 25px rgba(173, 216, 230, 1));
    }
}

/* 冰霜粒子汇聚效果 */
.xuanbingjian-frost-particle {
    position: absolute;
    width: 4px;
    height: 4px;
    transform: translate(-50%, -50%);
    background: linear-gradient(45deg, rgba(173, 216, 230, 0.8), rgba(135, 206, 235, 0.7));
    border-radius: 50%;
    animation: xuanbingjian-frost-gather 1.2s ease-out forwards;
    -webkit-filter: drop-shadow(0 0 3px rgba(173, 216, 230, 0.6));
    filter: drop-shadow(0 0 3px rgba(173, 216, 230, 0.6));
}

@keyframes xuanbingjian-frost-gather {
    0% {
        transform: translate(calc(-50% + var(--chargeX)), calc(-50% + var(--chargeY))) scale(0) rotate(0deg);
        opacity: 0;
    }
    30% {
        transform: translate(calc(-50% + var(--chargeX) * 0.7), calc(-50% + var(--chargeY) * 0.7)) scale(1.5) rotate(120deg);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(0.3) rotate(360deg);
        opacity: 0;
    }
}

/* === 第一阶段：3把剑叠加旋转 === */

/* 旋转剑样式 */
.xuanbingjian-rotating-sword {
    position: absolute;
    width: 35px;
    height: 70px;
    transform: translate(-50%, -50%);
    opacity: var(--layerOpacity, 0.8);
    pointer-events: none;
    z-index: calc(1001 + var(--swordIndex, 0));
    
    /* 支持背景图片显示 */
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    
    /* 叠加旋转动画 */
    animation: xuanbingjian-sword-rotate var(--rotationSpeed, 1.5s) linear infinite;
}

/* 为武器图片添加专门的发光效果 */
.xuanbingjian-rotating-sword[style*="background-image"] {
    /* 武器图片必须使用drop-shadow */
    -webkit-filter: drop-shadow(0 0 12px rgba(173, 216, 230, 0.9)) drop-shadow(0 0 24px rgba(135, 206, 235, 0.7)) drop-shadow(0 0 8px rgba(255, 255, 255, 0.5)) drop-shadow(0 0 4px rgba(230, 230, 250, 0.6));
    filter: drop-shadow(0 0 12px rgba(173, 216, 230, 0.9)) drop-shadow(0 0 24px rgba(135, 206, 235, 0.7)) drop-shadow(0 0 8px rgba(255, 255, 255, 0.5)) drop-shadow(0 0 4px rgba(230, 230, 250, 0.6));
}

/* 无背景图片时的默认样式 */
.xuanbingjian-rotating-sword:not([style*="background-image"]) {
    background: linear-gradient(to bottom, 
        rgba(173, 216, 230, 0.95), 
        rgba(135, 206, 235, 0.85), 
        rgba(176, 224, 230, 0.75));
    -webkit-clip-path: polygon(50% 0%, 40% 20%, 45% 80%, 50% 100%, 55% 80%, 60% 20%);
    clip-path: polygon(50% 0%, 40% 20%, 45% 80%, 50% 100%, 55% 80%, 60% 20%);
    border-radius: 2px;
    /* 添加冰晶边框效果 */
    border: 1px solid rgba(173, 216, 230, 0.4);
    /* 内层冰晶光泽 */
    box-shadow: 
        inset 0 0 8px rgba(255, 255, 255, 0.2),
        inset 0 0 4px rgba(173, 216, 230, 0.3);
    /* 默认发光效果 */
    -webkit-filter: drop-shadow(0 0 10px rgba(173, 216, 230, 0.8)) drop-shadow(0 0 20px rgba(135, 206, 235, 0.6)) drop-shadow(0 0 6px rgba(255, 255, 255, 0.4));
    filter: drop-shadow(0 0 10px rgba(173, 216, 230, 0.8)) drop-shadow(0 0 20px rgba(135, 206, 235, 0.6)) drop-shadow(0 0 6px rgba(255, 255, 255, 0.4));
}

/* 剑周围的光圈效果 */
.xuanbingjian-sword-aura {
    position: absolute;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: radial-gradient(circle, 
        rgba(173, 216, 230, 0.3) 0%, 
        rgba(135, 206, 235, 0.2) 40%, 
        rgba(255, 255, 255, 0.1) 70%, 
        transparent 100%);
    transform: translate(-50%, -50%);
    pointer-events: none;
    z-index: calc(1000 + var(--swordIndex, 0));
    animation: xuanbingjian-aura-pulse 2s ease-in-out infinite;
    animation-delay: calc(var(--swordIndex, 0) * 0.2s); /* 每把剑的光圈有不同延迟 */
}

@keyframes xuanbingjian-aura-pulse {
    0%, 100% {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 0.4;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.2);
        opacity: 0.8;
    }
}

@keyframes xuanbingjian-sword-rotate {
    0% {
        transform: translate(-50%, -50%) scale(var(--layerScale, 1)) rotate(var(--initialAngle, 0deg));
        opacity: 0;
    }
    20% {
        opacity: var(--layerOpacity, 0.8);
    }
    100% {
        transform: translate(-50%, -50%) scale(var(--layerScale, 1)) rotate(calc(var(--initialAngle, 0deg) + 360deg));
        opacity: var(--layerOpacity, 0.8);
    }
}

/* === 第二阶段：甩飞攻击动画 === */

/* 第一步：甩飞到随机位置 */
@keyframes xuanbingjian-sword-throw {
    0% {
        left: var(--initialX);
        top: var(--initialY);
        transform: translate(-50%, -50%) rotate(var(--initialAngle, 0deg)) scale(var(--layerScale, 1));
        opacity: var(--layerOpacity, 0.8);
        /* 飞行时的增强发光效果 */
        -webkit-filter: drop-shadow(0 0 10px rgba(173, 216, 230, 0.8)) drop-shadow(0 0 20px rgba(135, 206, 235, 0.6)) drop-shadow(0 0 5px rgba(255, 255, 255, 0.4));
        filter: drop-shadow(0 0 10px rgba(173, 216, 230, 0.8)) drop-shadow(0 0 20px rgba(135, 206, 235, 0.6)) drop-shadow(0 0 5px rgba(255, 255, 255, 0.4));
    }
    70% {
        left: var(--thrownX);
        top: var(--thrownY);
        transform: translate(-50%, -50%) rotate(calc(var(--initialAngle, 0deg) + 270deg)) scale(1.1);
        opacity: 1;
        -webkit-filter: drop-shadow(0 0 15px rgba(173, 216, 230, 0.9)) drop-shadow(0 0 30px rgba(135, 206, 235, 0.7)) drop-shadow(0 0 8px rgba(255, 255, 255, 0.4)) drop-shadow(0 0 5px rgba(230, 230, 250, 0.6));
        filter: drop-shadow(0 0 15px rgba(173, 216, 230, 0.9)) drop-shadow(0 0 30px rgba(135, 206, 235, 0.7)) drop-shadow(0 0 8px rgba(255, 255, 255, 0.4)) drop-shadow(0 0 5px rgba(230, 230, 250, 0.6));
    }
    100% {
        left: var(--thrownX);
        top: var(--thrownY);
        transform: translate(-50%, -50%) rotate(calc(var(--initialAngle, 0deg) + 360deg)) scale(1);
        opacity: 1;
        -webkit-filter: drop-shadow(0 0 12px rgba(173, 216, 230, 0.8)) drop-shadow(0 0 25px rgba(135, 206, 235, 0.6)) drop-shadow(0 0 6px rgba(255, 255, 255, 0.3));
        filter: drop-shadow(0 0 12px rgba(173, 216, 230, 0.8)) drop-shadow(0 0 25px rgba(135, 206, 235, 0.6)) drop-shadow(0 0 6px rgba(255, 255, 255, 0.3));
    }
}

/* 第二步：停顿并调整朝向 */
@keyframes xuanbingjian-sword-aim {
    0% {
        left: var(--thrownX);
        top: var(--thrownY);
        transform: translate(-50%, -50%) rotate(calc(var(--initialAngle, 0deg) + 360deg)) scale(1);
        opacity: 1;
    }
    100% {
        left: var(--thrownX);
        top: var(--thrownY);
        transform: translate(-50%, -50%) rotate(var(--angle, 0deg)) scale(1.1);
        opacity: 1;
        -webkit-filter: drop-shadow(0 0 18px rgba(173, 216, 230, 1)) drop-shadow(0 0 35px rgba(135, 206, 235, 0.8)) drop-shadow(0 0 10px rgba(255, 255, 255, 0.5));
        filter: drop-shadow(0 0 18px rgba(173, 216, 230, 1)) drop-shadow(0 0 35px rgba(135, 206, 235, 0.8)) drop-shadow(0 0 10px rgba(255, 255, 255, 0.5));
    }
}

/* 第三步：直线攻击穿透 */
@keyframes xuanbingjian-sword-strike {
    0% {
        left: var(--thrownX);
        top: var(--thrownY);
        transform: translate(-50%, -50%) rotate(var(--angle, 0deg)) scale(1.1);
        opacity: 1;
    }
    /* 快速加速到敌人位置 */
    60% {
        left: var(--targetX);
        top: var(--targetY);
        transform: translate(-50%, -50%) rotate(var(--angle, 0deg)) scale(1.3);
        opacity: 1;
        -webkit-filter: drop-shadow(0 0 25px rgba(173, 216, 230, 1)) drop-shadow(0 0 50px rgba(135, 206, 235, 0.9)) drop-shadow(0 0 15px rgba(255, 255, 255, 0.6)) drop-shadow(0 0 10px rgba(230, 230, 250, 0.7));
        filter: drop-shadow(0 0 25px rgba(173, 216, 230, 1)) drop-shadow(0 0 50px rgba(135, 206, 235, 0.9)) drop-shadow(0 0 15px rgba(255, 255, 255, 0.6)) drop-shadow(0 0 10px rgba(230, 230, 250, 0.7));
    }
    /* 穿透到终点 */
    100% {
        left: var(--finalX);
        top: var(--finalY);
        transform: translate(-50%, -50%) rotate(var(--angle, 0deg)) scale(0.8);
        opacity: 0;
        -webkit-filter: drop-shadow(0 0 15px rgba(173, 216, 230, 0.6));
        filter: drop-shadow(0 0 15px rgba(173, 216, 230, 0.6));
    }
}

/* 飞行光圈跟随动画 */
.xuanbingjian-flying-aura {
    position: absolute;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
    z-index: 998;
}

/* 光圈甩飞动画 */
@keyframes xuanbingjian-aura-throw {
    0% {
        left: var(--initialX);
        top: var(--initialY);
        opacity: 0.6;
        transform: translate(-50%, -50%) scale(0.8);
    }
    70% {
        left: var(--thrownX);
        top: var(--thrownY);
        opacity: 0.8;
        transform: translate(-50%, -50%) scale(1.1);
    }
    100% {
        left: var(--thrownX);
        top: var(--thrownY);
        opacity: 0.7;
        transform: translate(-50%, -50%) scale(1);
    }
}

/* 光圈定向动画 */
@keyframes xuanbingjian-aura-aim {
    0% {
        left: var(--thrownX);
        top: var(--thrownY);
        opacity: 0.7;
        transform: translate(-50%, -50%) scale(1);
    }
    100% {
        left: var(--thrownX);
        top: var(--thrownY);
        opacity: 0.9;
        transform: translate(-50%, -50%) scale(1.2);
        background: radial-gradient(circle, 
            rgba(173, 216, 230, 0.5) 0%, 
            rgba(135, 206, 235, 0.4) 40%, 
            rgba(255, 255, 255, 0.3) 70%, 
            transparent 100%);
    }
}

/* 光圈攻击动画 */
@keyframes xuanbingjian-aura-strike {
    0% {
        left: var(--thrownX);
        top: var(--thrownY);
        opacity: 0.9;
        transform: translate(-50%, -50%) scale(1.2);
    }
    60% {
        left: var(--targetX);
        top: var(--targetY);
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.5);
        background: radial-gradient(circle, 
            rgba(173, 216, 230, 0.6) 0%, 
            rgba(135, 206, 235, 0.5) 30%, 
            rgba(255, 255, 255, 0.4) 60%, 
            transparent 100%);
    }
    100% {
        left: var(--finalX);
        top: var(--finalY);
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.5);
    }
}

/* 冰刺轨迹跟随动画 */
.xuanbingjian-ice-spike-trail {
    position: absolute;
    width: 4px;
    height: 14px;
    background: linear-gradient(to bottom, 
        rgba(173, 216, 230, 0.95), 
        rgba(135, 206, 235, 0.8), 
        rgba(176, 224, 230, 0.6),
        rgba(230, 230, 250, 0.4));
    -webkit-clip-path: polygon(50% 0%, 20% 80%, 50% 100%, 80% 80%);
    clip-path: polygon(50% 0%, 20% 80%, 50% 100%, 80% 80%);
    transform: translate(-50%, -50%);
    opacity: var(--opacity, 0.8);
    /* 增强冰刺发光效果 */
    -webkit-filter: drop-shadow(0 0 4px rgba(173, 216, 230, 0.8)) drop-shadow(0 0 2px rgba(255, 255, 255, 0.4));
    filter: drop-shadow(0 0 4px rgba(173, 216, 230, 0.8)) drop-shadow(0 0 2px rgba(255, 255, 255, 0.4));
    z-index: 999;
    /* 冰晶边框 */
    border: 0.5px solid rgba(173, 216, 230, 0.5);
}

@keyframes xuanbingjian-spike-trail {
    0% {
        left: var(--startX);
        top: var(--startY);
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.5);
    }
    10% {
        opacity: var(--opacity, 0.8);
        transform: translate(-50%, -50%) scale(1);
    }
    60% {
        left: var(--targetX);
        top: var(--targetY);
        opacity: var(--opacity, 0.8);
        transform: translate(-50%, -50%) scale(1.1);
        -webkit-filter: drop-shadow(0 0 6px rgba(173, 216, 230, 0.9));
        filter: drop-shadow(0 0 6px rgba(173, 216, 230, 0.9));
    }
    100% {
        left: var(--finalX);
        top: var(--finalY);
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.3);
        -webkit-filter: drop-shadow(0 0 3px rgba(173, 216, 230, 0.5));
        filter: drop-shadow(0 0 3px rgba(173, 216, 230, 0.5));
    }
}

/* === 第三阶段：击中效果 === */

/* 冰剑爆炸核心 */
.xuanbingjian-explosion-core {
    position: absolute;
    width: 80px;
    height: 80px;
    background: conic-gradient(from 0deg, 
        rgba(173, 216, 230, 0.9), 
        rgba(135, 206, 235, 0.7), 
        rgba(176, 224, 230, 0.5), 
        rgba(135, 206, 235, 0.7), 
        rgba(173, 216, 230, 0.9));
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: xuanbingjian-explosion-core 1.5s ease-out forwards;
    -webkit-filter: drop-shadow(0 0 30px rgba(173, 216, 230, 1));
    filter: drop-shadow(0 0 30px rgba(173, 216, 230, 1));
}

@keyframes xuanbingjian-explosion-core {
    0% {
        transform: translate(-50%, -50%) rotate(0deg) scale(0);
        opacity: 0.9;
    }
    30% {
        transform: translate(-50%, -50%) rotate(180deg) scale(1.5);
        opacity: 1;
        -webkit-filter: drop-shadow(0 0 50px rgba(173, 216, 230, 1)) drop-shadow(0 0 100px rgba(135, 206, 235, 0.8));
        filter: drop-shadow(0 0 50px rgba(173, 216, 230, 1)) drop-shadow(0 0 100px rgba(135, 206, 235, 0.8));
    }
    100% {
        transform: translate(-50%, -50%) rotate(540deg) scale(0.2);
        opacity: 0;
        -webkit-filter: drop-shadow(0 0 20px rgba(173, 216, 230, 0.5));
        filter: drop-shadow(0 0 20px rgba(173, 216, 230, 0.5));
    }
}

/* 冰霜冲击波 */
.xuanbingjian-ice-shockwave {
    position: absolute;
    width: 40px;
    height: 40px;
    border: 3px solid rgba(173, 216, 230, 0.8);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: xuanbingjian-shockwave-expand 1.2s ease-out forwards;
}

@keyframes xuanbingjian-shockwave-expand {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0.8;
        border-width: 3px;
        border-color: rgba(173, 216, 230, 0.8);
    }
    50% {
        transform: translate(-50%, -50%) scale(4);
        opacity: 0.5;
        border-width: 2px;
        border-color: rgba(135, 206, 235, 0.6);
    }
    100% {
        transform: translate(-50%, -50%) scale(8);
        opacity: 0;
        border-width: 1px;
        border-color: rgba(176, 224, 230, 0.3);
    }
}

/* 冰晶碎片爆发 */
.xuanbingjian-ice-shard {
    position: absolute;
    width: 4px;
    height: 8px;
    background: linear-gradient(45deg, rgba(173, 216, 230, 0.9), rgba(135, 206, 235, 0.7));
    -webkit-clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    transform: translate(-50%, -50%);
    animation: xuanbingjian-shard-fly calc(1.2s * var(--shardVelocity)) ease-out forwards;
    -webkit-filter: drop-shadow(0 0 5px rgba(173, 216, 230, 0.8));
    filter: drop-shadow(0 0 5px rgba(173, 216, 230, 0.8));
}

@keyframes xuanbingjian-shard-fly {
    0% {
        transform: translate(-50%, -50%) rotate(var(--shardAngle)) scale(0);
        opacity: 0.9;
    }
    20% {
        transform: translate(-50%, -50%) 
                   rotate(var(--shardAngle)) 
                   translateX(calc(var(--shardDistance) * 0.2)) 
                   scale(1.5);
        opacity: 1;
    }
    60% {
        transform: translate(-50%, -50%) 
                   rotate(var(--shardAngle)) 
                   translateX(calc(var(--shardDistance) * 0.7)) 
                   scale(1);
        opacity: 0.7;
    }
    100% {
        transform: translate(-50%, -50%) 
                   rotate(var(--shardAngle)) 
                   translateX(var(--shardDistance)) 
                   scale(0.3);
        opacity: 0;
    }
}

/* === 敌人受击动画 === */

/* 冰霜受击效果 */
@keyframes xuanbingjian-enemy-ice-hit {
    0% { 
        -webkit-filter: hue-rotate(0deg) saturate(1) brightness(1);
        filter: hue-rotate(0deg) saturate(1) brightness(1);
    }
    20% { 
        -webkit-filter: hue-rotate(200deg) saturate(1.8) brightness(1.5) drop-shadow(0 0 20px rgba(173, 216, 230, 0.8));
        filter: hue-rotate(200deg) saturate(1.8) brightness(1.5) drop-shadow(0 0 20px rgba(173, 216, 230, 0.8));
    }
    40% { 
        -webkit-filter: hue-rotate(0deg) saturate(1) brightness(0.6);
        filter: hue-rotate(0deg) saturate(1) brightness(0.6);
    }
    60% { 
        -webkit-filter: hue-rotate(200deg) saturate(1.5) brightness(1.3) drop-shadow(0 0 15px rgba(135, 206, 235, 0.7));
        filter: hue-rotate(200deg) saturate(1.5) brightness(1.3) drop-shadow(0 0 15px rgba(135, 206, 235, 0.7));
    }
    80% { 
        -webkit-filter: hue-rotate(0deg) saturate(1) brightness(0.8);
        filter: hue-rotate(0deg) saturate(1) brightness(0.8);
    }
    100% { 
        -webkit-filter: hue-rotate(0deg) saturate(1) brightness(1);
        filter: hue-rotate(0deg) saturate(1) brightness(1);
    }
}

/* 冰冻震动效果 */
@keyframes xuanbingjian-enemy-freeze-shake {
    0%, 100% { transform: translate(0, 0); }
    25% { transform: translate(-1px, 0); }
    50% { transform: translate(1px, -1px); }
    75% { transform: translate(0, 1px); }
}

/* 移动端适配 */
@media (max-width: 768px) {
    .xuanbingjian-container {
        transform: scale(0.85);
    }
    
    .xuanbingjian-ice-circle {
        width: 100px;
        height: 100px;
    }
    
    .xuanbingjian-inner-runes {
        width: 65px;
        height: 65px;
    }
    
    .xuanbingjian-outer-runes {
        width: 115px;
        height: 115px;
    }
    
    .xuanbingjian-rotating-sword {
        width: 28px;
        height: 56px;
    }
    
    .xuanbingjian-sword-aura {
        width: 40px;
        height: 40px;
    }
    
    .xuanbingjian-flying-aura {
        width: 50px;
        height: 50px;
    }
    
    .xuanbingjian-ice-spike-trail {
        width: 3px;
        height: 10px;
    }
    
    .xuanbingjian-explosion-core {
        width: 65px;
        height: 65px;
    }
}

/* 性能优化 */
.xuanbingjian-ice-circle,
.xuanbingjian-inner-runes,
.xuanbingjian-outer-runes,
.xuanbingjian-energy-core,
.xuanbingjian-rotating-sword,
.xuanbingjian-sword-aura,
.xuanbingjian-flying-aura,
.xuanbingjian-ice-spike-trail,
.xuanbingjian-explosion-core {
    will-change: transform, opacity;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    contain: layout style paint;
}

/* 减少动画计算的媒体查询 */
@media (prefers-reduced-motion: reduce) {
    .xuanbingjian-ice-circle,
    .xuanbingjian-inner-runes,
    .xuanbingjian-outer-runes,
    .xuanbingjian-energy-core,
    .xuanbingjian-rotating-sword,
    .xuanbingjian-sword-aura,
    .xuanbingjian-flying-aura,
    .xuanbingjian-ice-spike-trail {
        animation-duration: 0.2s !important;
    }
} 