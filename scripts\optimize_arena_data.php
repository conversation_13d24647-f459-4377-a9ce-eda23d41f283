<?php
/**
 * 竞技场数据优化脚本
 * 解决真实玩家匹配困难问题
 */

require_once __DIR__ . '/../src/config/database.php';

try {
    $pdo = getDatabase();
    if (!$pdo) {
        throw new Exception("数据库连接失败");
    }
    
    echo "=== 竞技场数据优化脚本 ===\n\n";
    
    // 1. 为现有玩家初始化竞技场数据
    echo "1. 初始化现有玩家竞技场数据\n";
    echo "----------------------------------------\n";
    
    $stmt = $pdo->query("
        SELECT id, character_name, realm_id, 
               COALESCE(arena_dao_power, 0) as current_dao_power
        FROM characters 
        WHERE arena_dao_power IS NULL OR arena_dao_power = 0
    ");
    
    $playersToUpdate = $stmt->fetchAll();
    echo "需要初始化的玩家数量: " . count($playersToUpdate) . "\n";
    
    foreach ($playersToUpdate as $player) {
        // 计算基础道行值
        $baseDaoPower = $player['realm_id'] * 100 + rand(200, 800);
        
        $updateStmt = $pdo->prepare("
            UPDATE characters 
            SET arena_dao_power = ?,
                arena_daily_attempts = 0,
                arena_purchased_attempts = 0,
                arena_last_reset = CURDATE(),
                arena_total_battles = rand(0, 10),
                arena_total_wins = FLOOR(arena_total_battles * (0.3 + RAND() * 0.4)),
                arena_rank_level = 1,
                arena_rank_points = rand(0, 100)
            WHERE id = ?
        ");
        
        $updateStmt->execute([$baseDaoPower, $player['id']]);
        echo "更新玩家 {$player['character_name']} (ID: {$player['id']}) 道行值: {$baseDaoPower}\n";
    }
    
    // 2. 创建测试玩家数据
    echo "\n2. 创建测试玩家数据\n";
    echo "----------------------------------------\n";
    
    // 检查是否已有足够的测试数据
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM characters WHERE character_name LIKE '测试玩家%'");
    $testPlayerCount = $stmt->fetch()['count'];
    
    if ($testPlayerCount < 20) {
        $needCreate = 20 - $testPlayerCount;
        echo "需要创建 {$needCreate} 个测试玩家\n";
        
        // 获取现有用户ID用于创建角色
        $stmt = $pdo->query("SELECT id FROM users ORDER BY id LIMIT 1");
        $userId = $stmt->fetch()['id'];
        
        if (!$userId) {
            echo "警告: 没有找到用户数据，跳过测试玩家创建\n";
        } else {
            for ($i = $testPlayerCount + 1; $i <= 20; $i++) {
                $realmId = rand(1, 60); // 随机境界
                $daoPower = $realmId * 100 + rand(200, 800);
                
                $insertStmt = $pdo->prepare("
                    INSERT INTO characters (
                        user_id, character_name, realm_id, 
                        arena_dao_power, arena_daily_attempts, arena_purchased_attempts,
                        arena_last_reset, arena_total_battles, arena_total_wins,
                        arena_rank_level, arena_rank_points,
                        physique, comprehension, constitution, spirit, agility,
                        current_hp, current_mp, created_at, updated_at
                    ) VALUES (
                        ?, ?, ?, ?, 0, 0, CURDATE(), ?, ?, 1, ?,
                        10, 10, 10, 10, 10, 100, 100, NOW(), NOW()
                    )
                ");
                
                $battles = rand(5, 50);
                $wins = floor($battles * (0.3 + rand() / getrandmax() * 0.4));
                $points = rand(0, 500);
                
                $insertStmt->execute([
                    $userId,
                    "测试玩家{$i}",
                    $realmId,
                    $daoPower,
                    $battles,
                    $wins,
                    $points
                ]);
                
                echo "创建测试玩家{$i}: 境界{$realmId}, 道行{$daoPower}, 战绩{$wins}/{$battles}\n";
            }
        }
    } else {
        echo "已有足够的测试玩家数据 ({$testPlayerCount}个)\n";
    }
    
    // 3. 平衡境界分布
    echo "\n3. 平衡境界分布\n";
    echo "----------------------------------------\n";
    
    // 检查当前境界分布
    $stmt = $pdo->query("
        SELECT realm_id, COUNT(*) as count 
        FROM characters 
        GROUP BY realm_id 
        HAVING COUNT(*) > 5
        ORDER BY COUNT(*) DESC
    ");
    
    $overPopulatedRealms = $stmt->fetchAll();
    
    if (!empty($overPopulatedRealms)) {
        echo "发现过度集中的境界:\n";
        foreach ($overPopulatedRealms as $realm) {
            echo "境界 {$realm['realm_id']}: {$realm['count']} 个玩家\n";
            
            // 将部分玩家分散到相邻境界
            $redistributeCount = floor(($realm['count'] - 3) / 2);
            if ($redistributeCount > 0) {
                $newRealm1 = max(1, $realm['realm_id'] - rand(1, 5));
                $newRealm2 = min(280, $realm['realm_id'] + rand(1, 5));
                
                // 更新一半玩家到新境界1
                $updateStmt = $pdo->prepare("
                    UPDATE characters 
                    SET realm_id = ?, arena_dao_power = realm_id * 100 + ?
                    WHERE realm_id = ? 
                    ORDER BY RAND() 
                    LIMIT ?
                ");
                $updateStmt->execute([$newRealm1, rand(200, 800), $realm['realm_id'], floor($redistributeCount/2)]);
                
                // 更新另一半到新境界2
                $updateStmt->execute([$newRealm2, rand(200, 800), $realm['realm_id'], ceil($redistributeCount/2)]);
                
                echo "  重新分配 {$redistributeCount} 个玩家到境界 {$newRealm1} 和 {$newRealm2}\n";
            }
        }
    } else {
        echo "境界分布相对均衡\n";
    }
    
    // 4. 更新最后登录时间（模拟活跃玩家）
    echo "\n4. 更新玩家活跃状态\n";
    echo "----------------------------------------\n";
    
    $stmt = $pdo->prepare("
        UPDATE characters 
        SET last_login_time = DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 7) DAY)
        WHERE character_name LIKE '测试玩家%' OR last_login_time IS NULL
    ");
    $stmt->execute();
    
    $affectedRows = $stmt->rowCount();
    echo "更新了 {$affectedRows} 个玩家的登录时间\n";
    
    // 5. 生成最终统计报告
    echo "\n5. 优化后统计报告\n";
    echo "----------------------------------------\n";
    
    // 总玩家数
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM characters");
    $totalPlayers = $stmt->fetch()['total'];
    echo "总玩家数: {$totalPlayers}\n";
    
    // 活跃玩家数
    $stmt = $pdo->query("
        SELECT COUNT(*) as active 
        FROM characters 
        WHERE last_login_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    ");
    $activePlayers = $stmt->fetch()['active'];
    echo "活跃玩家数（7天内）: {$activePlayers}\n";
    
    // 有竞技场数据的玩家
    $stmt = $pdo->query("
        SELECT COUNT(*) as arena_ready 
        FROM characters 
        WHERE arena_dao_power > 0
    ");
    $arenaPlayers = $stmt->fetch()['arena_ready'];
    echo "竞技场就绪玩家: {$arenaPlayers}\n";
    
    // 境界分布
    echo "\n境界分布（前10个境界）:\n";
    $stmt = $pdo->query("
        SELECT 
            c.realm_id,
            r.realm_name,
            COUNT(*) as player_count,
            COUNT(CASE WHEN c.last_login_time >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as active_count
        FROM characters c
        LEFT JOIN realm_levels r ON c.realm_id = r.id
        GROUP BY c.realm_id, r.realm_name
        ORDER BY player_count DESC
        LIMIT 10
    ");
    
    while ($row = $stmt->fetch()) {
        printf("境界 %2d %-15s: 总数 %2d, 活跃 %2d\n", 
            $row['realm_id'], 
            $row['realm_name'] ?: '未知', 
            $row['player_count'], 
            $row['active_count']
        );
    }
    
    echo "\n✅ 竞技场数据优化完成！\n";
    echo "建议：现在可以测试竞技场匹配功能，应该能找到更多真实玩家对手。\n";
    
} catch (Exception $e) {
    echo "优化失败: " . $e->getMessage() . "\n";
    echo "错误详情: " . $e->getTraceAsString() . "\n";
}
?>
