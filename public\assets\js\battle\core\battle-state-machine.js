// 战斗状态机管理器
class BattleStateMachine {
    constructor(battleSystem) {
        this.battleSystem = battleSystem;
        this.isInitialized = false;
        this.isInitializing = false;
        console.log('✅ 状态机实例已创建');
    }

    // 从BattleSystem迁移的initialize方法
    async initialize() {
        console.log('⚡ 状态机 initialize 开始执行');
        if (this.isInitialized) {
            console.log('状态机已初始化，跳过重复初始化');
            return true;
        }

        if (this.isInitializing) {
            console.log('状态机正在初始化中，等待完成...');
            return new Promise(resolve => {
                const checkInterval = setInterval(() => {
                    if (!this.isInitializing) {
                        clearInterval(checkInterval);
                        resolve(this.isInitialized);
                    }
                }, 100);
            });
        }

        this.isInitializing = true;

        try {
            const success = await this.initializeBattle();
            this.isInitialized = success;
            console.log('⚡ 状态机 initialize 执行完成:', success);
            return success;
        } finally {
            this.isInitializing = false;
        }
    }

    // 从BattleSystem迁移的initializeBattle方法
    async initializeBattle() {
        console.log('⚡ 状态机 initializeBattle 开始执行');
        try {
            // 显示加载状态
            this.battleSystem.updateBattleStatus('正在加载战斗数据...');

            // 🔧 修复：检查是否已有数据管理器实例，避免重复创建
            if (!this.battleSystem.dataManager) {
                this.battleSystem.dataManager = new BattleDataManager();
            }

            // 🔧 新增：设置UI管理器的数据管理器引用
            if (this.battleSystem.uiManager) {
                this.battleSystem.uiManager.setDataManager(this.battleSystem.dataManager);
            }

            // 🔥 新增：设置战斗流程管理器的关联
            if (this.battleSystem.battleFlowManager) {
                this.battleSystem.battleFlowManager.setDataManager(this.battleSystem.dataManager);
                this.battleSystem.battleFlowManager.setUIManager(this.battleSystem.uiManager);
                console.log('✅ 战斗流程管理器关联完成');
            }

            // 🔥 新增：设置奖励管理器的关联
            if (this.battleSystem.rewardManager) {
                this.battleSystem.rewardManager.setDataManager(this.battleSystem.dataManager);
                this.battleSystem.rewardManager.setUIManager(this.battleSystem.uiManager);
                console.log('✅ 奖励管理器关联完成');
            }

            // 🔥 新增：设置胜利面板管理器的关联
            if (this.battleSystem.victoryPanelManager) {
                this.battleSystem.victoryPanelManager.setDataManager(this.battleSystem.dataManager);
                this.battleSystem.victoryPanelManager.setUIManager(this.battleSystem.uiManager);
                this.battleSystem.victoryPanelManager.setRewardManager(
                    this.battleSystem.rewardManager
                );
                console.log('✅ 胜利面板管理器关联完成');
            }

            const success = await this.battleSystem.dataManager.initializeBattleData();

            if (!success) {
                console.warn('⚠️ 加载战斗数据失败，使用默认配置');
                this.battleSystem.updateBattleStatus('加载战斗数据失败，使用默认配置');
            }

            // 更新区域信息显示
            this.battleSystem.updateAreaInfo();

            // 🔧 新增：设置战斗背景图片
            this.battleSystem.dataManager.setBattleBackground();

            // 获取最终玩家属性
            this.battleSystem.playerStats = this.battleSystem.dataManager.calculateFinalStats();

            // 🔧 修复：统一使用hp_total/mp_total字段，保持兼容性，使用正确的字段名
            this.battleSystem.playerStats.hp_total = this.battleSystem.playerStats.hp_bonus || 300;
            this.battleSystem.playerStats.mp_total = this.battleSystem.playerStats.mp_bonus || 100;
            this.battleSystem.playerStats.max_hp = this.battleSystem.playerStats.hp_total; // 兼容性
            this.battleSystem.playerStats.max_mp = this.battleSystem.playerStats.mp_total; // 兼容性
            this.battleSystem.playerStats.currentMp = this.battleSystem.playerStats.mp_total;
            console.log('🔋 玩家HP/MP初始化:', {
                max_hp: this.battleSystem.playerStats.max_hp,
                max_mp: this.battleSystem.playerStats.max_mp,
                currentMp: this.battleSystem.playerStats.currentMp,
                hp_bonus: this.battleSystem.playerStats.hp_bonus,
                mp_bonus: this.battleSystem.playerStats.mp_bonus,
            });

            // 先获取技能序列（在更新武器显示之前）
            this.battleSystem.skillSequence =
                this.battleSystem.dataManager.getBattleSkillSequence();

            console.log('=== 完整玩家数据 ===', this.battleSystem.playerStats);

            // 创建角色，正确传递头像数据
            this.battleSystem.player = new Character(document.querySelector('.player'), {
                name: this.battleSystem.playerStats.name || '修仙者',
                level: this.battleSystem.playerStats.level,
                max_hp: this.battleSystem.playerStats.max_hp,
                max_mp: this.battleSystem.playerStats.max_mp,
                currentMp: this.battleSystem.playerStats.currentMp,
                attack: this.battleSystem.playerStats.physical_attack,
                defense: this.battleSystem.playerStats.physical_defense,
                avatar:
                    this.battleSystem.playerStats.character_avatar ||
                    this.battleSystem.playerStats.avatar_image,
            });

            // 🔥 修复：确保玩家角色对象的MP值与playerStats同步
            this.battleSystem.player.max_mp = this.battleSystem.playerStats.max_mp;
            this.battleSystem.player.currentMp = this.battleSystem.playerStats.currentMp;
            this.battleSystem.player.updateUI();

            // 🔧 修复：检查敌人数据是否存在
            if (!this.battleSystem.dataManager.enemyData) {
                console.error('❌ 敌人数据为空，无法初始化战斗');
                throw new Error('敌人数据加载失败');
            }

            // 敌人可能有avatar属性，确保传递
            const enemyData = {
                ...this.battleSystem.dataManager.enemyData,
                // 🔧 修复：正确处理敌人头像路径
                avatar:
                    this.battleSystem.dataManager.enemyData.avatarImage ||
                    this.battleSystem.dataManager.enemyData.modelImage ||
                    this.battleSystem.dataManager.enemyData.avatar ||
                    this.battleSystem.dataManager.enemyData.monster_avatar,
                // 🔧 修复：确保境界信息传递
                realm_name: this.battleSystem.dataManager.enemyData.realm_name,
                realm_id: this.battleSystem.dataManager.enemyData.realm_id,
            };

            this.battleSystem.enemy = new Character(document.querySelector('.enemy'), enemyData);

            // 现在更新武器显示（skillSequence和skillNames都已经初始化）
            this.battleSystem.updateWeaponDisplay();

            console.log('战斗配置:', {
                playerStats: this.battleSystem.playerStats,
                enemyData: this.battleSystem.dataManager.enemyData,
                weaponSlots: this.battleSystem.dataManager.weaponSlots,
                skillSequence: this.battleSystem.skillSequence,
            });

            // 🔧 新增：详细的敌人技能调试信息
            console.log('=== 敌人技能数据调试 ===');
            console.log('敌人原始数据:', this.battleSystem.dataManager.enemyData);
            console.log('敌人技能列表:', this.battleSystem.dataManager.enemyData?.skills);
            console.log('技能列表类型:', typeof this.battleSystem.dataManager.enemyData?.skills);
            console.log('技能数量:', this.battleSystem.dataManager.enemyData?.skills?.length || 0);
            if (
                this.battleSystem.dataManager.enemyData?.skills &&
                Array.isArray(this.battleSystem.dataManager.enemyData.skills)
            ) {
                this.battleSystem.dataManager.enemyData.skills.forEach((skill, index) => {
                    console.log(`  技能${index + 1}: "${skill}" (类型: ${typeof skill})`);
                });
            }

            console.log('=== 详细战斗数据 ===');
            console.log('玩家基础攻击力:', this.battleSystem.playerStats.physical_attack);
            console.log(
                '玩家头像:',
                this.battleSystem.playerStats.character_avatar ||
                    this.battleSystem.playerStats.avatar_image
            );
            console.log('敌人生命值:', this.battleSystem.enemy.max_hp);
            console.log('敌人防御力:', this.battleSystem.enemy.defense);
            console.log('敌人头像:', enemyData.avatar);
            console.log('武器槽位详情:');
            this.battleSystem.skillSequence.forEach((skill, index) => {
                console.log(`  槽位${index + 1}:`, {
                    hasWeapon: skill.hasWeapon,
                    weaponName: skill.weaponName,
                    weaponAttack: skill.weaponAttack,
                    skillName: skill.skillName,
                    damageMultiplier: skill.damageMultiplier,
                });
            });

            // 开始战斗
            this.battleSystem.updateBattleStatus('战斗开始！');
            setTimeout(async () => {
                await this.battleSystem.battleFlowManager.autoBattle();
            }, 500);

            console.log('⚡ 状态机 initializeBattle 执行完成');
            return true;
        } catch (error) {
            console.error('❌ 状态机 initializeBattle 执行失败:', error);
            return false;
        }
    }

    // 从BattleSystem迁移的gameOver方法
    async gameOver(message) {
        console.log('⚡ 状态机 gameOver 开始执行:', message);
        this.battleSystem.isGameOver = true;

        // 清除所有战斗相关的定时器
        if (this.battleSystem.battleTimeout) {
            clearTimeout(this.battleSystem.battleTimeout);
        }

        // 停止自动战斗
        this.stopAutoBattle();

        // 更新战斗状态
        this.battleSystem.updateBattleStatus(message);

        // 记录战斗结束时间
        this.battleSystem.battleEndTime = Date.now();

        console.log('⚡ 状态机 gameOver 执行完成');
        return true;
    }

    // 从BattleSystem迁移的handleBattleEnd方法
    async handleBattleEnd(isVictory, droppedItems = []) {
        console.log('⚡ 状态机 handleBattleEnd 开始执行:', { isVictory, droppedItems });
        if (isVictory) {
            // 胜利处理
            await this.battleSystem.showVictoryPanel('🎉 玩家胜利！战斗结束！', droppedItems);
        } else {
            // 失败处理
            await this.battleSystem.showVictoryPanel('💀 战斗失败！', []);
        }

        // 🔧 修复：挂机模式现在由胜利面板管理器处理，这里不再需要手动处理
        console.log('⚡ 状态机 handleBattleEnd 执行完成');
    }

    // 🗑️ 已删除：不必要的代理方法，直接使用battleFlowManager.autoBattle()

    // 从BattleSystem迁移的stopAutoBattle方法
    stopAutoBattle() {
        console.log('⚡ 状态机 stopAutoBattle 开始执行');

        // 清理战斗超时定时器
        if (this.battleSystem.battleTimeout) {
            clearTimeout(this.battleSystem.battleTimeout);
            this.battleSystem.battleTimeout = null;
            console.log('🗑️ 清理战斗超时定时器');
        }

        // 清理倒计时定时器
        if (this.battleSystem.autoBattleCountdown) {
            clearInterval(this.battleSystem.autoBattleCountdown);
            this.battleSystem.autoBattleCountdown = null;
            console.log('🗑️ 清理倒计时定时器');
        }

        // 🔧 修复：不要在这里清理挂机状态，挂机状态应该由挂机管理器控制
        // 这个方法只负责清理当前战斗的定时器，不应该影响挂机模式
        console.log('🗑️ 清理挂机状态');

        // 🔧 修复：不要在这里更新UI，UI更新应该由胜利面板管理器处理
        console.log('⚡ 状态机 stopAutoBattle 执行完成');
    }
}

// 导出状态机类
window.BattleStateMachine = BattleStateMachine;
