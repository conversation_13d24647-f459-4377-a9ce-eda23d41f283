-- 竞技场段位积分系统数据库更新脚本
-- 创建时间：2024年12月19日
-- 说明：添加段位积分字段和积分获取机制

USE yn_game;

-- 1. 为角色表添加段位积分字段
ALTER TABLE `characters` 
ADD COLUMN IF NOT EXISTS `arena_rank_points` int(11) DEFAULT 0 COMMENT '竞技场段位积分' AFTER `arena_rank_level`;

-- 2. 为段位表添加积分要求字段
ALTER TABLE `immortal_arena_ranks` 
ADD COLUMN IF NOT EXISTS `required_points` int(11) DEFAULT 0 COMMENT '晋升所需积分' AFTER `reward_multiplier`;

-- 3. 更新段位表的积分要求
UPDATE `immortal_arena_ranks` SET `required_points` = 
CASE `rank_level`
    WHEN 1 THEN 0      -- 练气期：0积分
    WHEN 2 THEN 50     -- 筑基期：50积分
    WHEN 3 THEN 150    -- 结丹期：150积分
    WHEN 4 THEN 300    -- 元婴期：300积分
    WHEN 5 THEN 500    -- 化神期：500积分
    WHEN 6 THEN 750    -- 合体期：750积分
    WHEN 7 THEN 1050   -- 大乘期：1050积分
    WHEN 8 THEN 1400   -- 渡劫期：1400积分
    WHEN 9 THEN 1800   -- 仙人境：1800积分
    WHEN 10 THEN 2250  -- 仙君境：2250积分
    ELSE 0
END;

-- 4. 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS `idx_arena_rank_points` ON `characters` (`arena_rank_points`);

-- 5. 初始化现有角色的段位积分（基于战绩计算）
UPDATE `characters` 
SET `arena_rank_points` = GREATEST(0, 
    (`arena_total_wins` * 15) - (`arena_total_battles` - `arena_total_wins`) * 5
)
WHERE `arena_rank_points` = 0 OR `arena_rank_points` IS NULL;

-- 完成
SELECT '✅ 竞技场段位积分系统数据库更新完成!' as message; 