/**
 * 装备套装管理器
 * 基于实际game_item_sets表结构的前端管理
 */
class EquipmentSetManager {
    constructor() {
        // 🔧 修复：使用配置文件中的API路径
        this.apiUrl = window.GameConfig ? window.GameConfig.getApiUrl('equipment_set_system.php') : '/yinian/src/api/equipment_set_system.php';
        this.currentSets = [];
        this.allSets = [];
        this.initializeEventListeners();
    }

    /**
     * 初始化事件监听器
     */
    initializeEventListeners() {
        // 监听页面加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.init());
        } else {
            this.init();
        }
    }

    /**
     * 初始化套装系统
     */
    async init() {
        console.log('套装管理器初始化...');
        
        // 检查必要的容器是否存在
        this.setsContainer = document.getElementById('equipment-sets-container');
        if (!this.setsContainer) {
            console.warn('未找到套装容器，创建临时容器');
            this.createTemporaryContainer();
        }

        // 加载套装状态
        await this.loadCharacterSets();
    }

    /**
     * 创建临时容器用于演示
     */
    createTemporaryContainer() {
        this.setsContainer = document.createElement('div');
        this.setsContainer.id = 'equipment-sets-container';
        this.setsContainer.className = 'equipment-sets-container';
        document.body.appendChild(this.setsContainer);
    }

    /**
     * 加载角色当前套装状态
     */
    async loadCharacterSets() {
        try {
            const data = await ajaxManager.get(`${this.apiUrl}?action=get_character_sets`);

            if (data.success) {
                this.currentSets = data.sets;
                this.renderCharacterSets(data.sets, data.total_power_bonus);
                console.log('套装状态加载成功:', data);
            } else {
                console.error('加载套装状态失败:', data.message);
                this.showMessage('加载套装状态失败: ' + data.message, 'error');
            }
        } catch (error) {
            console.error('网络错误:', error);
            this.showMessage('网络连接失败', 'error');
        }
    }

    /**
     * 渲染角色套装状态
     */
    renderCharacterSets(sets, totalPowerBonus) {
        if (!this.setsContainer) return;

        // 🔧 修复：显示套装对角色战力的真实贡献
        let html = `
            <div class="sets-header">
                <h3>装备套装</h3>
                <div class="total-power">
                    <span class="label">套装战力加成:</span>
                    <span class="value" id="set-power-value">计算中...</span>
                </div>
            </div>
        `;

        // 异步计算真实的套装战力贡献
        this.calculateRealSetPowerBonus(sets).then(realPowerBonus => {
            const powerElement = document.getElementById('set-power-value');
            if (powerElement) {
                powerElement.textContent = `+${realPowerBonus.toLocaleString()}`;
            }
        }).catch(error => {
            console.error('计算套装战力失败:', error);
            const powerElement = document.getElementById('set-power-value');
            if (powerElement) {
                powerElement.textContent = '计算失败';
            }
        });

        if (sets.length === 0) {
            html += `
                <div class="no-sets">
                    <p>当前没有激活的套装效果</p>
                    <p class="hint">装备同一套装的多件装备可激活套装效果</p>
                </div>
            `;
        } else {
            html += '<div class="sets-list">';
            
            sets.forEach(set => {
                html += this.renderSetCard(set);
            });
            
            html += '</div>';
        }

        this.setsContainer.innerHTML = html;
        this.bindSetEvents();
    }

    /**
     * 渲染单个套装卡片
     */
    renderSetCard(set) {
        const rarityClass = this.getRarityClass(set.rarity);
        const rarityColor = this.getRarityColor(set.rarity);
        
        let html = `
            <div class="set-card ${rarityClass}" data-set-id="${set.set_id}">
                <div class="set-header">
                    <div class="set-name-row">
                        <h4 class="set-name" style="color: ${rarityColor}">${set.set_name}</h4>
                        <span class="set-pieces-count">${set.pieces_count}/${set.max_pieces}</span>
                    </div>
                    <div class="set-meta">
                        <span class="set-rarity">${set.rarity}</span>
                        <span class="realm-requirement">境界需求: ${set.realm_requirement}</span>
                    </div>
                </div>

                <div class="set-description">
                    ${set.description}
                </div>

                <div class="equipped-pieces">
                    <h5>已装备部件:</h5>
                    <div class="pieces-grid">
        `;

        // 显示已装备的部件
        set.equipped_pieces.forEach(piece => {
            html += `
                <div class="piece-item equipped">
                    <span class="piece-name">${piece.item_name}</span>
                    <span class="piece-slot">${this.getSlotDisplayName(piece.slot_type)}</span>
                </div>
            `;
        });

        html += `
                    </div>
                </div>

                <div class="set-effects">
                    <h5>套装效果:</h5>
        `;

        // 显示套装效果
        html += this.renderSetEffects(set.all_effects, set.active_effects, set.pieces_count);

        html += `
                </div>

                <div class="set-actions">
                    <button class="btn-details" onclick="window.equipmentSetManager.showSetDetails(${set.set_id})">
                        查看详情
                    </button>
                </div>
            </div>
        `;

        return html;
    }

    /**
     * 渲染套装效果
     */
    renderSetEffects(allEffects, activeEffects, piecesCount) {
        let html = '';

        // 2件套效果
        if (allEffects.two_piece) {
            const isActive = piecesCount >= 2;
            const activeClass = isActive ? 'active' : 'inactive';
            html += `
                <div class="effect-tier ${activeClass}">
                    <div class="tier-header">
                        <span class="tier-name">2件套</span>
                        ${isActive ? '<span class="status-badge active">已激活</span>' : '<span class="status-badge inactive">未激活</span>'}
                    </div>
                    <div class="effect-content">
                        ${this.formatEffectText(allEffects.two_piece)}
                    </div>
                </div>
            `;
        }

        // 4件套效果
        if (allEffects.four_piece) {
            const isActive = piecesCount >= 4;
            const activeClass = isActive ? 'active' : 'inactive';
            html += `
                <div class="effect-tier ${activeClass}">
                    <div class="tier-header">
                        <span class="tier-name">4件套</span>
                        ${isActive ? '<span class="status-badge active">已激活</span>' : '<span class="status-badge inactive">未激活</span>'}
                    </div>
                    <div class="effect-content">
                        ${this.formatEffectText(allEffects.four_piece)}
                    </div>
                </div>
            `;
        }

        // 6件套效果
        if (allEffects.six_piece) {
            const isActive = piecesCount >= 6;
            const activeClass = isActive ? 'active' : 'inactive';
            html += `
                <div class="effect-tier ${activeClass}">
                    <div class="tier-header">
                        <span class="tier-name">6件套</span>
                        ${isActive ? '<span class="status-badge active">已激活</span>' : '<span class="status-badge inactive">未激活</span>'}
                    </div>
                    <div class="effect-content">
                        ${this.formatEffectText(allEffects.six_piece)}
                    </div>
                </div>
            `;
        }

        return html;
    }

    /**
     * 格式化效果文本
     */
    formatEffectText(effect) {
        let text = '';
        
        // 处理属性加成
        const attributeNames = {
            'physical_attack': '物理攻击',
            'immortal_attack': '仙术攻击',
            'physical_defense': '物理防御',
            'immortal_defense': '仙术防御',
            'max_hp': '生命值',
            'max_mp': '法力值',
            'speed': '速度',
            'crit_rate': '暴击率',
            'crit_damage': '暴击伤害',
            'hit_rate': '命中率',
            'dodge_rate': '闪避率',
            'block_rate': '格挡率'
        };

        const attributes = [];
        Object.keys(effect).forEach(key => {
            if (key === 'special_effect') return;
            
            const displayName = attributeNames[key] || key;
            const value = effect[key];
            
            if (key.includes('rate') || key.includes('damage')) {
                attributes.push(`${displayName}+${value}%`);
            } else {
                attributes.push(`${displayName}+${value}`);
            }
        });

        text += attributes.join('，');

        // 处理特殊效果
        if (effect.special_effect) {
            if (text) text += '<br>';
            text += `<span class="special-effect">特殊效果: ${effect.special_effect}</span>`;
        }

        return text;
    }

    /**
     * 获取品质CSS类名
     */
    getRarityClass(rarity) {
        const rarityMap = {
            '普通': 'common',
            '稀有': 'rare',
            '史诗': 'epic',
            '传说': 'legendary',
            '神话': 'mythic'
        };
        return `rarity-${rarityMap[rarity] || 'common'}`;
    }

    /**
     * 获取品质颜色
     */
    getRarityColor(rarity) {
        const colorMap = {
            '普通': '#ffffff',
            '稀有': '#1eff00',
            '史诗': '#0070dd',
            '传说': '#a335ee',
            '神话': '#ff8000'
        };
        return colorMap[rarity] || '#ffffff';
    }

    /**
     * 获取槽位显示名称
     */
    getSlotDisplayName(slotType) {
        const slotNames = {
            'sword': '武器',
            'fan': '法器',
            'chest': '上衣',
            'legs': '下装',
            'feet': '鞋子',
            'ring': '戒指',
            'necklace': '项链',
            'bracelet': '手镯'
        };
        return slotNames[slotType] || slotType;
    }

    /**
     * 绑定套装相关事件
     */
    bindSetEvents() {
        // 套装卡片点击事件已在HTML中通过onclick绑定
    }

    /**
     * 显示套装详情
     */
    async showSetDetails(setId) {
        try {
            const data = await ajaxManager.get(`${this.apiUrl}?action=get_set_details&set_id=${setId}`);

            if (data.success) {
                this.renderSetDetailsModal(data);
            } else {
                this.showMessage('获取套装详情失败: ' + data.message, 'error');
            }
        } catch (error) {
            this.showMessage('网络连接失败', 'error');
        }
    }

    /**
     * 渲染套装详情模态框
     */
    renderSetDetailsModal(data) {
        const { set_info, items, effects } = data;
        
        // 创建模态框
        const modal = document.createElement('div');
        modal.className = 'set-details-modal';
        modal.innerHTML = `
            <div class="modal-overlay" onclick="this.parentElement.remove()"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h3>${set_info.set_name}</h3>
                    <button class="close-btn" onclick="this.closest('.set-details-modal').remove()">×</button>
                </div>
                
                <div class="modal-body">
                    <div class="set-info">
                        <div class="info-row">
                            <span class="label">品质:</span>
                            <span class="value ${this.getRarityClass(set_info.rarity)}">${set_info.rarity}</span>
                        </div>
                        <div class="info-row">
                            <span class="label">境界需求:</span>
                            <span class="value">${set_info.realm_requirement}</span>
                        </div>
                        <div class="info-row">
                            <span class="label">套装描述:</span>
                            <span class="value">${set_info.description}</span>
                        </div>
                    </div>

                    <div class="set-items">
                        <h4>套装部件 (${items.length}件)</h4>
                        <div class="items-grid">
                            ${items.map(item => `
                                <div class="item-card">
                                    <div class="item-name">${item.item_name}</div>
                                    <div class="item-slot">${this.getSlotDisplayName(item.slot_type)}</div>
                                    <div class="item-realm">境界需求: ${item.realm_requirement || '无'}</div>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <div class="detailed-effects">
                        <h4>详细效果</h4>
                        ${this.renderDetailedEffects(effects)}
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    /**
     * 渲染详细效果
     */
    renderDetailedEffects(effects) {
        let html = '';

        ['two_piece', 'four_piece', 'six_piece'].forEach(tier => {
            if (effects[tier]) {
                const tierName = tier === 'two_piece' ? '2件套' : tier === 'four_piece' ? '4件套' : '6件套';
                html += `
                    <div class="effect-detail">
                        <h5>${tierName}效果</h5>
                        <div class="effect-text">
                            ${this.formatEffectText(effects[tier])}
                        </div>
                    </div>
                `;
            }
        });

        return html;
    }

    /**
     * 获取所有套装列表
     */
    async loadAllSets() {
        try {
            const data = await ajaxManager.get(`${this.apiUrl}?action=get_all_sets`);

            if (data.success) {
                this.allSets = data.sets;
                return data;
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            this.showMessage('获取套装列表失败: ' + error.message, 'error');
            throw error;
        }
    }

    /**
     * 计算真实的套装战力加成
     * 通过API获取装备套装前后的实际战力差值
     */
    async calculateRealSetPowerBonus(sets) {
        try {
            // 调用后端API计算真实的套装战力差值
            const response = await fetch(window.GameConfig ? window.GameConfig.getApiUrl('equipment_set_system.php') : '/yinian/src/api/equipment_set_system.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: 'action=calculate_real_set_power_difference'
            });

            const data = await response.json();

            if (data.success) {
                console.log('🔍 真实套装战力差值:', data.power_difference);
                console.log('🔍 当前总战力:', data.current_power);
                console.log('🔍 无套装战力:', data.power_without_sets);
                return data.power_difference;
            } else {
                console.error('计算真实套装战力失败:', data.message);
                return 0;
            }
        } catch (error) {
            console.error('API调用失败:', error);
            // 降级方案：显示0而不是错误的7000
            return 0;
        }
    }

    /**
     * 计算套装战力
     */
    async calculateSetPower() {
        try {
            const data = await ajaxManager.get(`${this.apiUrl}?action=calculate_set_power`);

            if (data.success) {
                return data;
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            this.showMessage('计算套装战力失败: ' + error.message, 'error');
            throw error;
        }
    }

    /**
     * 模拟套装效果
     */
    async simulateSetEffect(setId, piecesCount) {
        try {
            const data = await ajaxManager.post(this.apiUrl, {
                action: 'simulate_set_effect',
                set_id: setId,
                pieces_count: piecesCount
            });

            if (data.success) {
                return data;
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            this.showMessage('模拟套装效果失败: ' + error.message, 'error');
            throw error;
        }
    }

    /**
     * 显示消息提示
     */
    showMessage(message, type = 'info') {
        // 移除现有消息
        const existingMessage = document.querySelector('.set-message');
        if (existingMessage) {
            existingMessage.remove();
        }

        // 创建新消息
        const messageDiv = document.createElement('div');
        messageDiv.className = `set-message ${type}`;
        messageDiv.textContent = message;
        
        document.body.appendChild(messageDiv);

        // 3秒后自动移除
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.remove();
            }
        }, 3000);
    }

    /**
     * 刷新套装状态
     */
    async refresh() {
        await this.loadCharacterSets();
    }
}

// 等待ajaxManager准备好后初始化
function initializeSetManager() {
    console.log('初始化套装管理器...');
    
    // 检查ajaxManager是否已准备好
    if (typeof window.ajaxManager === 'undefined') {
        console.log('等待ajaxManager初始化...');
        setTimeout(initializeSetManager, 100);
        return;
    }
    
    // 检查是否在需要套装功能的页面
    if (document.getElementById('set-status-container') || 
        document.querySelector('.equipment-page') ||
        document.querySelector('.attributes-page') ||
        document.querySelector('.equipment-integrated')) {
        
        // 创建全局实例
        window.equipmentSetManager = new EquipmentSetManager();
        
        // 初始化管理器
        window.equipmentSetManager.init();
        
        console.log('套装管理器初始化完成');
    }
}

// 等待DOM完全加载后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeSetManager);
} else {
    // DOM已经准备好了
    initializeSetManager();
}

// 导出类供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EquipmentSetManager;
} 