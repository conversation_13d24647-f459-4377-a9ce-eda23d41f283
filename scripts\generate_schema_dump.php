<?php
// scripts/generate_schema_dump.php

// 设置时区和错误报告
date_default_timezone_set('Asia/Shanghai');
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义项目根目录
$root_dir = dirname(__DIR__);

// 引入数据库配置文件
$db_config_path = $root_dir . '/src/config/database.php';
if (!file_exists($db_config_path)) {
    die("数据库配置文件未找到: " . $db_config_path);
}
require_once $db_config_path;

// 获取数据库连接
try {
    $pdo = getDatabase();
} catch (PDOException $e) {
    die("数据库连接失败: " . $e->getMessage());
}

// 获取所有表名
try {
    $tables_result = $pdo->query("SHOW TABLES");
    $tables = $tables_result->fetchAll(PDO::FETCH_COLUMN);
} catch (PDOException $e) {
    die("获取表列表失败: " . $e->getMessage());
}

if (empty($tables)) {
    die("数据库中没有找到任何表。");
}

// 定义输出文件路径
$output_file = $root_dir . '/data/schema_dump.sql';
$output_handle = fopen($output_file, 'w');
if (!$output_handle) {
    die("无法打开文件进行写入: " . $output_file);
}

fwrite($output_handle, "-- 数据库 `yn_game` 结构转储\n");
fwrite($output_handle, "-- 生成时间: " . date('Y-m-d H:i:s') . "\n");
fwrite($output_handle, "-- 共 " . count($tables) . " 个表\n\n");

// 遍历每个表并获取其结构
foreach ($tables as $table) {
    try {
        $create_table_stmt = $pdo->query("SHOW CREATE TABLE `{$table}`");
        $create_table_info = $create_table_stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($create_table_info && isset($create_table_info['Create Table'])) {
            fwrite($output_handle, "-- ----------------------------\n");
            fwrite($output_handle, "-- 表结构: `{$table}`\n");
            fwrite($output_handle, "-- ----------------------------\n");
            fwrite($output_handle, $create_table_info['Create Table'] . ";\n\n");
        }
    } catch (PDOException $e) {
        fwrite($output_handle, "-- 无法获取表 `{$table}` 的结构: " . $e->getMessage() . "\n\n");
    }
}

fclose($output_handle);

echo "数据库结构已成功导出到: " . realpath($output_file) . "\n";

?> 