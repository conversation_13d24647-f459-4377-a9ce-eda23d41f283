<?php
// 使用主游戏数据库配置
$db_config = [
    'host' => 'localhost',
    'username' => 'ynxx',
    'password' => 'mjlxz159',
    'database' => 'yn_game'
];

try {
    $conn = new PDO(
        "mysql:host={$db_config['host']};dbname={$db_config['database']};charset=utf8mb4",
        $db_config['username'],
        $db_config['password']
    );
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // 获取所有表
    $stmt = $conn->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);

    echo "数据库 {$db_config['database']} 中的表：\n";
    foreach ($tables as $table) {
        echo "- {$table}\n";
    }

} catch(PDOException $e) {
    echo "错误: " . $e->getMessage();
} 