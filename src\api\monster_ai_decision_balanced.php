<?php
/**
 * 平衡化怪物AI决策API
 * 基于固定概率的简化AI系统，保持与玩家自动战斗的公平性
 */

// 禁用错误显示，确保JSON响应纯净
ini_set('display_errors', 0);
error_reporting(E_ALL);

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

class BalancedMonsterAI {
    
    /**
     * 获取简化的AI行动决策
     * @param string $aiPattern AI模式类型
     * @return array 行动决策
     */
    static function getSimpleAIAction($aiPattern) {
        $rand = rand(1, 100);
        
        switch ($aiPattern) {
            case 'conservative':
                return self::getConservativeAction($rand);
            case 'balanced':
                return self::getBalancedAction($rand);
            case 'aggressive':
                return self::getAggressiveAction($rand);
            case 'random':
            case 'intelligent': // 智能型改为随机型
                return self::getRandomAction($rand);
            default:
                return self::getBalancedAction($rand);
        }
    }
    
    /**
     * 保守型AI：80%普攻，15%防御，5%技能
     */
    static function getConservativeAction($rand) {
        if ($rand <= 15) {
            // 15%概率防御
            return [
                'action' => 'defend',
                'damage_modifier' => 0.7, // 防御时受到伤害减少30%
                'animation' => 'defend',
                'message' => '进入防御姿态',
                'behavior_text' => '谨慎地防御着'
            ];
        } elseif ($rand <= 20) {
            // 5%概率使用技能
            return [
                'action' => 'skill',
                'damage_modifier' => 1.2, // 技能伤害增加20%
                'animation' => 'skill',
                'message' => '小心地使用技能',
                'behavior_text' => '谨慎地施展法术'
            ];
        } else {
            // 80%概率普通攻击
            return [
                'action' => 'attack',
                'damage_modifier' => 1.0,
                'animation' => 'attack',
                'skill' => '普通攻击',
                'message' => '发动普通攻击',
                'behavior_text' => '稳重地攻击'
            ];
        }
    }
    
    /**
     * 均衡型AI：70%普攻，20%技能，10%防御
     */
    static function getBalancedAction($rand) {
        if ($rand <= 10) {
            // 10%概率防御
            return [
                'action' => 'defend',
                'damage_modifier' => 0.8, // 防御时受到伤害减少20%
                'animation' => 'defend',
                'message' => '进入防御状态',
                'behavior_text' => '冷静地防御'
            ];
        } elseif ($rand <= 30) {
            // 20%概率使用技能
            return [
                'action' => 'skill',
                'damage_modifier' => 1.3, // 技能伤害增加30%
                'animation' => 'skill',
                'message' => '使用技能攻击',
                'behavior_text' => '施展法术攻击'
            ];
        } else {
            // 70%概率普通攻击
            return [
                'action' => 'attack',
                'damage_modifier' => 1.0,
                'animation' => 'attack',
                'skill' => '普通攻击',
                'message' => '发动攻击',
                'behavior_text' => '平稳地攻击'
            ];
        }
    }
    
    /**
     * 攻击型AI：60%普攻，35%技能，5%防御
     */
    static function getAggressiveAction($rand) {
        if ($rand <= 5) {
            // 5%概率防御
            return [
                'action' => 'defend',
                'damage_modifier' => 0.9, // 防御时受到伤害减少10%（防御效果较弱）
                'animation' => 'defend',
                'message' => '勉强防御',
                'behavior_text' => '不情愿地防御'
            ];
        } elseif ($rand <= 40) {
            // 35%概率使用技能
            return [
                'action' => 'skill',
                'damage_modifier' => 1.4, // 技能伤害增加40%
                'animation' => 'skill',
                'message' => '释放强力技能',
                'behavior_text' => '狂暴地施展法术'
            ];
        } else {
            // 60%概率普通攻击
            return [
                'action' => 'attack',
                'damage_modifier' => 1.1, // 普攻伤害也略高
                'animation' => 'attack',
                'skill' => '普通攻击',
                'message' => '猛烈攻击',
                'behavior_text' => '凶猛地攻击'
            ];
        }
    }
    
    /**
     * 随机型AI：50%普攻，30%技能，15%连击，5%防御（BOSS专用）
     */
    static function getRandomAction($rand) {
        if ($rand <= 5) {
            // 5%概率防御
            return [
                'action' => 'defend',
                'damage_modifier' => 0.6, // 防御效果很强
                'animation' => 'defend',
                'message' => '战术防御',
                'behavior_text' => '智慧地防御'
            ];
        } elseif ($rand <= 20) {
            // 15%概率连击
            return [
                'action' => 'combo',
                'hit_count' => rand(2, 3), // 2-3次连击
                'damage_modifier' => 1.3, // 总伤害增加30%
                'animation' => 'combo',
                'message' => '发动连击',
                'behavior_text' => '连环攻击'
            ];
        } elseif ($rand <= 50) {
            // 30%概率使用技能
            return [
                'action' => 'skill',
                'damage_modifier' => 1.5, // 技能伤害增加50%
                'animation' => 'skill',
                'message' => '释放强大技能',
                'behavior_text' => '施展强力法术'
            ];
        } else {
            // 50%概率普通攻击
            return [
                'action' => 'attack',
                'damage_modifier' => 1.0,
                'animation' => 'attack',
                'skill' => '普通攻击',
                'message' => '发动攻击',
                'behavior_text' => '攻击'
            ];
        }
    }
    
    /**
     * 获取AI模式的描述文本
     */
    static function getAIDescription($aiPattern) {
        switch ($aiPattern) {
            case 'conservative':
                return '这只怪物看起来很谨慎，偏向防御';
            case 'balanced':
                return '这只怪物保持着攻防平衡';
            case 'aggressive':
                return '这只怪物充满攻击性，十分危险';
            case 'random':
            case 'intelligent':
                return '这只强大的怪物行为难以预测';
            default:
                return '这只怪物的行为模式未知';
        }
    }
    
    /**
     * 智能选择怪物技能
     */
    static function selectMonsterSkill($monsterData, $aiPattern) {
        // 如果怪物有技能列表，从中选择
        if (isset($monsterData['skills']) && is_array($monsterData['skills']) && count($monsterData['skills']) > 0) {
            $skills = $monsterData['skills'];
            
            // 根据AI模式选择技能
            switch ($aiPattern) {
                case 'conservative':
                    // 保守型：优先选择基础技能
                    return $skills[0];
                    
                case 'balanced':
                    // 均衡型：随机选择前半部分技能
                    $maxIndex = min(2, count($skills) - 1);
                    return $skills[rand(0, $maxIndex)];
                    
                case 'aggressive':
                    // 攻击型：优先选择强力技能
                    $startIndex = max(0, count($skills) - 2);
                    return $skills[rand($startIndex, count($skills) - 1)];
                    
                case 'random':
                    // 随机型：完全随机选择
                    return $skills[rand(0, count($skills) - 1)];
                    
                default:
                    return $skills[0];
            }
        }
        
        // 备用方案：返回普通攻击
        return '普通攻击';
    }
}

try {
    // 获取请求数据
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (!$data) {
        throw new Exception('无效的请求数据');
    }
    
    // 提取AI模式
    $aiPattern = isset($data['ai_pattern']) ? $data['ai_pattern'] : 'balanced';
    
    // 获取简化的AI决策
    $aiDecision = BalancedMonsterAI::getSimpleAIAction($aiPattern);
    
    // 🔧 调试：确保skill字段存在
    if ($aiDecision['action'] === 'attack' && !isset($aiDecision['skill'])) {
        $aiDecision['skill'] = '普通攻击';
    }
    
    // 添加AI描述
    $aiDescription = BalancedMonsterAI::getAIDescription($aiPattern);
    
    // 返回决策结果
    $response = [
        'success' => true,
        'decision' => $aiDecision,
        'ai_description' => $aiDescription,
        'ai_pattern' => $aiPattern,
        'timestamp' => time(),
        'version' => 'balanced_v1.0'
    ];
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    // 错误处理 - 返回默认攻击决策
    $defaultDecision = [
        'action' => 'attack',
        'damage_modifier' => 1.0,
        'animation' => 'attack',
        'skill' => '普通攻击',
        'message' => '普通攻击',
        'behavior_text' => '攻击'
    ];
    
    $response = [
        'success' => false,
        'error' => $e->getMessage(),
        'decision' => $defaultDecision, // 提供默认决策确保游戏继续
        'ai_description' => '怪物行为未知',
        'timestamp' => time()
    ];
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
}
?> 