{
    "recommendations": [
        // PHP开发核心
        "bmewburn.vscode-intelephense-client",
        "xdebug.php-debug",

        // 数据库管理
        "formulahendry.vscode-mysql",
        "mtxr.sqltools",
        "mtxr.sqltools-driver-mysql",

        // 前端开发
        "ritwickdey.liveserver",
        "formulahendry.auto-rename-tag",
        "bradlc.vscode-tailwindcss",

        // 代码质量
        "esbenp.prettier-vscode",
        "dbaeumer.vscode-eslint",
        "ikappas.phpcs",

        // 项目管理
        "eamodio.gitlens",
        "alefragnani.project-manager",

        // 开发效率
        "christian-kohler.path-intellisense",
        "coenraads.bracket-pair-colorizer-2",

        // API测试
        "humao.rest-client",
        "rangav.vscode-thunder-client",

        // Web游戏开发辅助
        "eriklynd.json-tools",
        "naumovs.color-highlight",
        "pranaygp.vscode-css-peek",

        // 通用工具
        "ms-vscode.vscode-json",
        "redhat.vscode-yaml",
        "ms-vscode.powershell",

        // AI和智能开发工具
        "github.copilot",
        "github.copilot-chat",
        "ms-vscode.vscode-ai-toolkit",

        // 高级调试和分析
        "ms-vscode.vscode-profile-table",
        "ms-python.debugpy",
        "ms-vscode.vscode-speech",

        // 代码质量和安全
        "ms-vscode.vscode-security-scan",
        "sonarsource.sonarlint-vscode",
        "snyk-security.snyk-vulnerability-scanner",

        // 性能和监控
        "ms-vscode.vscode-performance-toolkit",
        "wayou.vscode-todo-highlight",
        "gruntfuggly.todo-tree",

        // 协作和文档
        "ms-vsliveshare.vsliveshare",
        "yzhang.markdown-all-in-one",
        "davidanson.vscode-markdownlint",

        // 容器和部署
        "ms-vscode-remote.remote-containers",
        "ms-azuretools.vscode-docker",

        // 数据库高级工具
        "cweijan.vscode-database-client2",
        "bajdzis.vscode-database"
    ]
}
