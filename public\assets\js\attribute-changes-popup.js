/**
 * 通用属性变化浮窗系统
 * 用于显示装备、武器、丹药等操作引起的属性变化
 */

// 🔧 修复：属性名称映射，统一使用speed_bonus
const ATTRIBUTE_NAMES = {
    physical_attack: '物理攻击',
    immortal_attack: '仙术攻击',
    physical_defense: '物理防御',
    immortal_defense: '仙术防御',
    hp_bonus: '生命值',
    mp_bonus: '法力值',
    speed_bonus: '速度', // 统一使用speed_bonus
    critical_bonus: '暴击率',
    critical_damage: '暴击伤害',
    accuracy_bonus: '命中率',
    dodge_bonus: '闪避率',
    block_bonus: '格挡加成',
    critical_resistance: '免暴率',
    // 基础属性
    physique: '筋骨',
    comprehension: '悟性',
    constitution: '体魄',
    spirit: '神魂',
    agility: '身法',
    spiritual_power: '灵力',
    cultivation_speed: '修炼速度',
    breakthrough_success_rate: '突破成功率',
};

// 百分比属性列表
const PERCENTAGE_ATTRIBUTES = [
    'critical_damage', // 暴击伤害是百分比
    'breakthrough_success_rate', // 突破成功率是百分比
    'cultivation_speed', // 修炼速度是百分比
];

/**
 * 显示属性变化浮窗
 * @param {Object} oldStats - 旧属性数据
 * @param {Object} newStats - 新属性数据
 * @param {Object} options - 可选配置
 * @param {string} options.title - 弹窗标题，默认为"⚔️ 属性变化"
 * @param {number} options.duration - 显示时长（毫秒），默认1000ms
 * @param {string} options.successMessage - 无变化时的成功提示，默认为"操作成功！"
 * @param {number} options.minChangeThreshold - 最小变化阈值，默认为1，低于此值的变化不显示
 */
async function showAttributeChanges(oldStats, newStats, options = {}) {
    console.log('🔧 showAttributeChanges 被调用');
    console.log('🔧 oldStats:', oldStats);
    console.log('🔧 newStats:', newStats);

    const config = {
        title: options.title || '属性变化',
        successMessage: options.successMessage || '操作成功！',
        duration: options.duration || 3000,
        minChangeThreshold: options.minChangeThreshold || 0.1,
    };

    console.log('🔧 config:', config);

    // 检查数据完整性
    if (!oldStats || !newStats) {
        console.log('🔧 数据不完整，显示成功提示');
        if (typeof showMessage === 'function') {
            showMessage(config.successMessage, 'success');
        }
        return;
    }

    // 🔧 移除战力计算功能（用户要求只显示属性变化）

    console.log('旧属性:', oldStats);
    console.log('新属性:', newStats);

    // 计算属性变化
    const changes = [];
    console.log('🔧 开始计算属性变化...');

    // 检查所有有映射的属性
    for (const key of Object.keys(ATTRIBUTE_NAMES)) {
        const name = ATTRIBUTE_NAMES[key];
        const oldValue = parseFloat(oldStats[key]) || 0;
        const newValue = parseFloat(newStats[key]) || 0;
        const diff = newValue - oldValue;

        console.log(`🔧 属性 ${name} (${key}): ${oldValue} -> ${newValue}, 差值: ${diff}`);

        // 使用阈值判断
        const isPercentage = PERCENTAGE_ATTRIBUTES.includes(key);
        const threshold = isPercentage ? 0.01 : config.minChangeThreshold;

        if (Math.abs(diff) >= threshold) {
            console.log(`🔧 检测到属性变化: ${name} 变化了 ${diff}`);

            // 格式化显示数值
            let displayDiff;
            if (isPercentage) {
                displayDiff = (diff * 100).toFixed(1) + '%';
            } else {
                if (Math.abs(diff) < 1) {
                    displayDiff = diff.toFixed(1);
                } else {
                    displayDiff = Math.round(diff).toString();
                }
            }

            const arrow = diff > 0 ? '↑' : '↓';
            const color = diff > 0 ? '#4CAF50' : '#F44336';
            const sign = diff > 0 ? '+' : '';

            changes.push({
                name: name,
                change: `${sign}${displayDiff}`,
                arrow: arrow,
                color: color,
                key: key,
                diff: diff,
            });
        }
    }

    console.log('🔧 检测到的属性变化:', changes);
    console.log('🔧 属性变化数量:', changes.length);

    // 如果没有属性变化，显示简单提示
    if (changes.length === 0) {
        console.log('🔧 没有属性变化，显示成功提示');
        if (typeof showMessage === 'function') {
            showMessage(config.successMessage, 'success');
        }
        return;
    }

    // 创建属性变化浮窗
    console.log('🔧 开始创建属性变化弹窗');
    createAttributeChangesPopup(changes, config);
}

/**
 * 创建属性变化弹窗DOM
 * @param {Array} changes - 属性变化数组
 * @param {Object} config - 配置对象
 */
function createAttributeChangesPopup(changes, config) {
    console.log('🔧 createAttributeChangesPopup 被调用');
    console.log('🔧 changes:', changes);
    console.log('🔧 config:', config);

    const overlayId = 'attribute-changes-overlay';
    const popupId = 'attribute-changes-popup';

    // 移除已存在的弹窗
    const existingOverlay = document.getElementById(overlayId);
    if (existingOverlay) {
        console.log('🔧 移除已存在的弹窗');
        existingOverlay.remove();
    }

    // 生成属性变化列表HTML
    const changesHtml = changes
        .map(
            change => `
        <div class="attribute-change-item" style="color: ${change.color};">
            <span class="attribute-name">${change.name}</span>
            <span class="attribute-value">${change.change} ${change.arrow}</span>
        </div>
    `
        )
        .join('');

    const popupHtml = `
        <div id="${overlayId}" class="attribute-changes-overlay">
            <div id="${popupId}" class="attribute-changes-popup">
                <div class="popup-header">
                    <h3>${config.title}</h3>
                    <div class="popup-close-hint">点击任意位置关闭</div>
                </div>
                <div class="popup-content">
                    ${changesHtml}
                </div>
            </div>
        </div>
    `;

    // 添加到页面
    console.log('🔧 添加弹窗HTML到页面');
    console.log('🔧 popupHtml:', popupHtml);
    document.body.insertAdjacentHTML('beforeend', popupHtml);

    // 验证弹窗是否成功添加
    const addedOverlay = document.getElementById(overlayId);
    console.log('🔧 弹窗是否成功添加:', !!addedOverlay);
    if (addedOverlay) {
        console.log('🔧 弹窗元素:', addedOverlay);
        console.log('🔧 弹窗样式:', window.getComputedStyle(addedOverlay));
    }

    // 添加样式（如果不存在）
    addAttributeChangesStyles();

    // 添加点击关闭事件
    const overlay = document.getElementById(overlayId);
    if (overlay) {
        let isClosing = false; // 防止重复关闭

        const closePopup = () => {
            if (isClosing) return;
            isClosing = true;

            overlay.style.opacity = '0';
            setTimeout(() => {
                if (overlay && overlay.parentNode) {
                    overlay.remove();
                }
            }, 300); // 淡出动画时间
        };

        // 自动消失倒计时
        let autoCloseTimer = setTimeout(() => {
            closePopup();
        }, config.duration);

        // 统一的点击处理函数
        const handleClick = e => {
            // 阻止事件冒泡和默认行为，防止点击穿透
            e.stopPropagation();
            e.preventDefault();

            // 清除自动关闭定时器
            clearTimeout(autoCloseTimer);

            // 关闭弹窗
            closePopup();
        };

        // 点击整个遮罩层关闭（包括弹窗内容）
        overlay.addEventListener('click', handleClick, true); // 使用捕获阶段
    }
}

/**
 * 添加属性变化弹窗的CSS样式
 */
function addAttributeChangesStyles() {
    // 检查是否已经添加过样式
    if (document.getElementById('attribute-changes-styles')) {
        return;
    }

    const styles = `
        <style id="attribute-changes-styles">
        .attribute-changes-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 999999;
            opacity: 1;
            transition: opacity 0.3s ease;
            pointer-events: auto;
        }
        
        .attribute-changes-popup {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            border: 2px solid #f39c12;
            border-radius: 15px;
            padding: 20px;
            max-width: 400px;
            width: 90%;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            animation: popupSlideIn 0.3s ease-out;
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            position: relative;
            z-index: 1000000;
            pointer-events: auto;
        }
        
        .attribute-changes-popup:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.6);
        }
        
        @keyframes popupSlideIn {
            from {
                transform: translateY(-50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }
        
        .attribute-changes-popup .popup-header {
            text-align: center;
            margin-bottom: 15px;
        }
        
        .attribute-changes-popup .popup-header h3 {
            color: #f39c12;
            margin: 0;
            font-size: 18px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }
        
        .attribute-changes-popup .popup-close-hint {
            color: #bdc3c7;
            font-size: 12px;
            margin-top: 5px;
            opacity: 0.8;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        }
        
        .attribute-changes-popup .popup-content {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .attribute-change-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            font-size: 14px;
            font-weight: bold;
        }
        
        .attribute-change-item .attribute-name {
            flex: 1;
            text-align: left;
        }
        
        .attribute-change-item .attribute-value {
            font-weight: bold;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        }
        
        /* 移动端适配 */
        @media (max-width: 768px) {
            .attribute-changes-popup {
                width: 95%;
                padding: 15px;
            }
            
            .attribute-changes-popup .popup-header h3 {
                font-size: 16px;
            }
            
            .attribute-change-item {
                font-size: 13px;
                padding: 6px 10px;
            }
            
            .attribute-changes-popup .popup-close-hint {
                font-size: 11px;
            }
        }
        </style>
    `;

    document.head.insertAdjacentHTML('beforeend', styles);
}

/**
 * 获取当前装备属性统计（通用方法）
 * @param {string} apiUrl - API地址
 * @param {string} action - API动作
 * @returns {Promise<Object|null>} 属性对象或null
 */
async function getCurrentEquipmentStats(apiUrl = null, action = 'get_user_stats') {
    try {
        // 使用配置化的API路径
        const finalApiUrl =
            apiUrl ||
            (window.GameConfig
                ? window.GameConfig.getApiUrl('equipment_integrated.php')
                : './src/api/equipment_integrated.php');
        const response = await fetch(finalApiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=${action}`,
        });

        const data = await response.json();
        console.log('🔧 获取到的属性数据:', data);

        if (data.success) {
            // 🔧 修复关键问题：优先使用equipment_stats来检测装备变化，而不是总属性stats
            const equipmentStats = data.equipment_stats || {};
            const totalStats = data.stats || data.user || {};

            console.log('🔧 装备属性:', equipmentStats);
            console.log('🔧 总属性:', totalStats);

            // 🔧 关键修复：统一使用装备属性来检测装备变化，确保显示正确的装备加成差值
            return {
                // 🔧 修复：所有属性都使用装备属性（equipmentStats），这样才能正确反映装备的加成变化
                physical_attack: parseInt(equipmentStats.physical_attack) || 0,
                immortal_attack: parseInt(equipmentStats.immortal_attack) || 0,
                physical_defense: parseInt(equipmentStats.physical_defense) || 0,
                immortal_defense: parseInt(equipmentStats.immortal_defense) || 0,
                hp_bonus: parseInt(equipmentStats.hp_bonus) || 0,
                mp_bonus: parseInt(equipmentStats.mp_bonus) || 0,
                speed_bonus: parseInt(equipmentStats.speed_bonus) || 0,
                critical_bonus: parseFloat(equipmentStats.critical_bonus) || 0,
                critical_damage: parseFloat(equipmentStats.critical_damage) || 0,
                accuracy_bonus: parseFloat(equipmentStats.accuracy_bonus) || 0,
                dodge_bonus: parseFloat(equipmentStats.dodge_bonus) || 0,
                block_bonus: parseFloat(equipmentStats.block_bonus) || 0,
                critical_resistance: parseFloat(equipmentStats.critical_resistance) || 0,
            };
        }
        return null;
    } catch (error) {
        console.error('获取装备属性失败:', error);
        return null;
    }
}

// 🔧 战力计算功能已移除（用户要求只显示属性变化）

// 导出函数供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        showAttributeChanges,
        getCurrentEquipmentStats,
        ATTRIBUTE_NAMES,
        PERCENTAGE_ATTRIBUTES,
    };
}
