/**
 * 战力评分系统 - 前端实现
 * 一念修仙项目战力系统v2.0简化版
 */

class PowerRating {
    
    constructor() {
        this.betterItems = [];
        this.initialized = false;
        this.currentEquipment = null;
    }
    
    // 初始化
    init() {
        if (this.initialized) return;
        this.initialized = true;
        console.log('🔥 战力系统初始化完成');
    }
    
    // 显示角色战力
    async displayCharacterPower(containerId) {
        try {
            const response = await fetch(window.GameConfig ? window.GameConfig.getApiUrl('power_rating.php') : '/yinian/src/api/power_rating.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: 'action=get_character_power'
            });
            
            const data = await response.json();
            if (data.success) {
                const container = document.getElementById(containerId);
                if (container) {
                    container.innerHTML = `
                        <div class="power-rating-display">
                            战力值：${data.total_power}
                        </div>
                    `;
                }
            }
        } catch (error) {
            console.error('获取角色战力失败:', error);
        }
    }
    
    // 显示装备战力
    async displayEquipmentPower(itemId, containerId) {
        if (!itemId || itemId === 'undefined') {
            console.warn('🚫 无效的物品ID:', itemId);
            return;
        }
        
        console.log('🔥 开始获取装备战力:', { itemId, containerId });
        
        try {
            const response = await fetch(window.GameConfig ? window.GameConfig.getApiUrl('power_rating.php') : '/yinian/src/api/power_rating.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `action=get_equipment_power&equipment_id=${itemId}`
            });
            
            const text = await response.text();
            console.log('🔥 API响应文本:', text);
            
            const data = JSON.parse(text);
            console.log('🔥 API响应数据:', data);
            
            if (data.success) {
                const container = document.getElementById(containerId);
                if (container) {
                    container.innerHTML = `战力值：${data.power_rating}`;
                    container.className = 'power-rating-display';
                    console.log('✅ 战力显示成功:', data.power_rating);
                } else {
                    console.error('❌ 战力显示容器不存在:', containerId);
                }
            } else {
                console.error('❌ API返回失败:', data.message);
                const container = document.getElementById(containerId);
                if (container) {
                    container.innerHTML = `战力值：${data.message || '获取失败'}`;
                }
            }
        } catch (error) {
            console.error('❌ 获取装备战力失败:', error);
            const container = document.getElementById(containerId);
            if (container) {
                container.innerHTML = '战力值：--';
            }
        }
    }
    
    // 检查背包中比当前装备更好的物品 (扩展到所有装备)
    async checkBetterEquipment() {
        try {
            console.log('🔥 开始检查更好的装备...');
            
            // 清除现有箭头 - 只清除背包中的箭头
            document.querySelectorAll('.inventory-item .power-arrow').forEach(arrow => arrow.remove());
            
            // 检查所有装备槽位的更好装备
            const response = await fetch(window.GameConfig ? window.GameConfig.getApiUrl('power_rating.php') : '/yinian/src/api/power_rating.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: 'action=check_better_equipment'
            });
            
            const text = await response.text();
            console.log('🔥 API返回文本:', text);
            
            let data;
            try {
                data = JSON.parse(text);
            } catch (e) {
                console.error('❌ JSON解析错误:', text);
                return;
            }
            
            if (data.success) {
                console.log('🔥 API调用成功，调试信息:', data.debug_info);
                
                if (data.better_items && data.better_items.length > 0) {
                    console.log('🔥 找到更好的装备:', data.better_items);
                    
                                    // 为更好的装备添加绿色上箭头 - 使用美观的箭头符号，只在背包中显示
                data.better_items.forEach(inventoryId => {
                    // 🔧 修复：只选择背包中的物品，不包括武器槽位
                    const itemElement = document.querySelector(`.inventory-item[data-item-id="${inventoryId}"]`);
                    if (itemElement) {
                        console.log('🔥 为背包装备添加箭头:', inventoryId);
                        
                        // 确保容器有相对定位
                        if (getComputedStyle(itemElement).position === 'static') {
                            itemElement.style.position = 'relative';
                        }
                        
                        // 创建箭头元素
                        const arrow = document.createElement('div');
                        arrow.className = 'power-arrow better';
                        arrow.innerHTML = '▲'; // 使用更好看的三角形箭头
                        arrow.title = '战力提升';
                        
                        itemElement.appendChild(arrow);
                    } else {
                        console.warn('❌ 未找到背包装备元素:', inventoryId);
                        // 🔧 调试：列出所有背包中的data-item-id元素
                        const allInventoryElements = document.querySelectorAll('.inventory-item[data-item-id]');
                        console.log('🔧 当前背包所有data-item-id元素:', Array.from(allInventoryElements).map(el => el.getAttribute('data-item-id')));
                    }
                });
                } else {
                    console.log('🔥 没有找到更好的装备，调试信息:', data.debug_info);
                    if (data.debug_info && data.debug_info.min_weapon_power !== undefined) {
                        console.log(`🔍 当前最低武器战力: ${data.debug_info.min_weapon_power}，背包武器需要超过此值才显示箭头`);
                    }
                }
            } else {
                console.error('❌ API调用失败:', data.message);
            }
        } catch (error) {
            console.error('❌ 检查更好装备失败:', error);
        }
    }
    
    // 比较武器槽位战力
    async compareWeaponSlots(weaponId) {
        try {
            const response = await fetch(window.GameConfig ? window.GameConfig.getApiUrl('power_rating.php') : '/yinian/src/api/power_rating.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `action=check_weapon_slot_comparison&weapon_id=${weaponId}`
            });
            
            const data = await response.json();
            if (data.success) {
                return data.comparisons || {};
            }
        } catch (error) {
            console.error('比较武器槽位失败:', error);
        }
        return {};
    }
    
    // 更新武器槽位比较显示
    updateWeaponSlotsDisplay(comparisons) {
        // 清除现有武器槽位箭头
        document.querySelectorAll('.equipped-item .power-arrow').forEach(arrow => arrow.remove());
        
        // comparisons是一个对象，键为槽位类型
        Object.keys(comparisons).forEach(slotType => {
            const comp = comparisons[slotType];
            // 查找对应槽位的装备元素
            const slotElement = document.querySelector(`[data-slot-type="${slotType}"]`) ||
                               document.querySelector(`.equipped-${slotType}`) ||
                               document.querySelector(`[data-equipment-slot="${slotType}"]`);
            
            if (slotElement && comp.is_worse) {
                this.addBetterItemArrow(slotElement, false); // 添加红色下箭头
            }
        });
    }
    
    // 威胁等级评估
    async getThreatLevel(monsterId) {
        try {
            const response = await fetch(window.GameConfig ? window.GameConfig.getApiUrl('power_rating.php') : '/yinian/src/api/power_rating.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `action=get_threat_level&monster_id=${monsterId}`
            });
            
            const data = await response.json();
            if (data.success) {
                return {
                    level: data.threat_level,
                    winRate: data.win_rate,
                    color: this.getThreatColor(data.threat_level)
                };
            }
        } catch (error) {
            console.error('获取威胁等级失败:', error);
        }
        return { level: '未知', winRate: 50, color: '#95a5a6' };
    }
    
    // 获取威胁等级颜色
    getThreatColor(level) {
        const colors = {
            '轻松': '#2ecc71',
            '普通': '#f39c12',
            '困难': '#e67e22',
            '危险': '#e74c3c',
            '致命': '#8e44ad'
        };
        return colors[level] || '#95a5a6';
    }
}

// 战力系统集成管理器
class PowerRatingIntegration {
    
    static init() {
        // 确保实例已创建（在文件末尾已经创建了）
        if (!window.powerRating) {
            window.powerRating = new PowerRating();
            window.powerRating.init();
        }
        
        // 监听页面加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                PowerRatingIntegration.onPageLoad();
            });
        } else {
            PowerRatingIntegration.onPageLoad();
        }
    }
    
    static onPageLoad() {
        // 检查是否在装备界面
        const isEquipmentPage = window.location.pathname.includes('equipment_integrated.html');
        
        if (isEquipmentPage) {
            console.log('🔥 装备界面加载，启动战力系统');
            PowerRatingIntegration.initEquipmentPage();
        }
    }
    
    static initEquipmentPage() {
        // 🔧 增加延迟时间，确保装备数据完全加载
        setTimeout(() => {
            if (window.powerRating) {
                console.log('🔥 [PowerRating] 开始执行装备页面箭头检查...');
                window.powerRating.checkBetterEquipment();
            }
        }, 2000); // 增加到2秒
        
        // 监听装备变化
        PowerRatingIntegration.watchEquipmentChanges();
    }
    
    static watchEquipmentChanges() {
        // 监听DOM变化，当装备列表更新时重新检查
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && 
                    mutation.target.classList.contains('inventory-grid')) {
                    // 背包内容发生变化，重新检查更好的装备
                    setTimeout(() => {
                        if (window.powerRating) {
                            window.powerRating.checkBetterEquipment();
                        }
                    }, 500);
                }
            });
        });
        
        const inventoryGrid = document.querySelector('.inventory-grid');
        if (inventoryGrid) {
            observer.observe(inventoryGrid, { childList: true, subtree: true });
        }
    }
    
    static onInventoryTabSwitch(tab) {
        // 切换标签时重新检查装备
        setTimeout(() => {
            if (window.powerRating) {
                window.powerRating.checkBetterEquipment();
            }
        }, 300);
    }
    
    static onWeaponSelect(itemId) {
        // 选择武器时显示槽位比较
        if (window.powerRating) {
            window.powerRating.compareWeaponSlots(itemId).then(comparisons => {
                window.powerRating.updateWeaponSlotsDisplay(comparisons);
            });
        }
    }
}

// 全局暴露类和实例
window.PowerRating = PowerRating;
window.PowerRatingIntegration = PowerRatingIntegration;

// 创建全局PowerRating实例
window.powerRating = new PowerRating();

// 页面加载完成后自动初始化
document.addEventListener('DOMContentLoaded', function() {
    window.powerRating.init();
    console.log('🔥 战力系统全局实例已创建并初始化');
});

// 自动初始化
PowerRatingIntegration.init();

// 🔧 调试：添加手动触发箭头检查的全局函数
window.manualCheckBetterEquipment = function() {
    console.log('🔧 手动触发箭头检查...');
    if (window.powerRating) {
        window.powerRating.checkBetterEquipment();
    } else {
        console.error('❌ powerRating实例不存在');
    }
}; 