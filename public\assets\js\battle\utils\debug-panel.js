/**
 * 🔧 战斗系统调试面板
 * 提供实时监控、性能分析和系统调试功能
 */
class BattleDebugPanel {
    constructor() {
        this.isVisible = false;
        this.updateInterval = null;
        this.panel = null;
        this.tabs = ['overview', 'managers', 'performance', 'cache', 'logs'];
        this.currentTab = 'overview';
        this.logs = [];
        this.maxLogs = 100;
        
        this.createPanel();
        this.bindEvents();
        
        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('debug-panel', '🔧 战斗系统调试面板已初始化');
        }
    }

    /**
     * 创建调试面板DOM结构
     */
    createPanel() {
        this.panel = document.createElement('div');
        this.panel.id = 'battle-debug-panel';
        this.panel.className = 'debug-panel hidden';
        
        this.panel.innerHTML = `
            <div class="debug-panel-header">
                <h3>🔧 战斗系统调试面板</h3>
                <div class="debug-panel-controls">
                    <button id="debug-refresh-btn">🔄 刷新</button>
                    <button id="debug-clear-btn">🧹 清理</button>
                    <button id="debug-close-btn">✖️ 关闭</button>
                </div>
            </div>
            
            <div class="debug-panel-tabs">
                <button class="debug-tab active" data-tab="overview">📊 概览</button>
                <button class="debug-tab" data-tab="managers">🏗️ 管理器</button>
                <button class="debug-tab" data-tab="performance">⚡ 性能</button>
                <button class="debug-tab" data-tab="cache">💾 缓存</button>
                <button class="debug-tab" data-tab="logs">📝 日志</button>
            </div>
            
            <div class="debug-panel-content">
                <div id="debug-overview" class="debug-tab-content active">
                    <div class="debug-section">
                        <h4>🎮 战斗系统状态</h4>
                        <div id="battle-system-status"></div>
                    </div>
                    <div class="debug-section">
                        <h4>📈 实时统计</h4>
                        <div id="realtime-stats"></div>
                    </div>
                </div>
                
                <div id="debug-managers" class="debug-tab-content">
                    <div class="debug-section">
                        <h4>🏗️ 管理器状态</h4>
                        <div id="managers-status"></div>
                    </div>
                    <div class="debug-section">
                        <h4>🔗 依赖关系图</h4>
                        <div id="dependency-graph"></div>
                    </div>
                </div>
                
                <div id="debug-performance" class="debug-tab-content">
                    <div class="debug-section">
                        <h4>⚡ 性能指标</h4>
                        <div id="performance-metrics"></div>
                    </div>
                    <div class="debug-section">
                        <h4>🎯 技能性能</h4>
                        <div id="skill-performance"></div>
                    </div>
                </div>
                
                <div id="debug-cache" class="debug-tab-content">
                    <div class="debug-section">
                        <h4>💾 缓存统计</h4>
                        <div id="cache-stats"></div>
                    </div>
                    <div class="debug-section">
                        <h4>🧠 内存使用</h4>
                        <div id="memory-usage"></div>
                    </div>
                </div>
                
                <div id="debug-logs" class="debug-tab-content">
                    <div class="debug-section">
                        <h4>📝 系统日志</h4>
                        <div class="debug-log-controls">
                            <button id="clear-logs-btn">清空日志</button>
                            <button id="export-logs-btn">导出日志</button>
                        </div>
                        <div id="debug-logs-content"></div>
                    </div>
                </div>
            </div>
        `;

        // 添加样式
        this.addStyles();
        
        // 添加到页面
        document.body.appendChild(this.panel);
    }

    /**
     * 添加调试面板样式
     */
    addStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .debug-panel {
                position: fixed;
                top: 20px;
                right: 20px;
                width: 450px;
                max-height: 70vh;
                background: rgba(0, 0, 0, 0.95);
                color: #fff;
                border: 2px solid #4CAF50;
                border-radius: 8px;
                z-index: 10000;
                font-family: 'Courier New', monospace;
                font-size: 12px;
                overflow: hidden;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
            }
            
            .debug-panel.hidden {
                display: none;
            }
            
            .debug-panel-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 10px;
                background: #4CAF50;
                color: #000;
            }
            
            .debug-panel-header h3 {
                margin: 0;
                font-size: 14px;
            }
            
            .debug-panel-controls button {
                margin-left: 5px;
                padding: 3px 8px;
                border: none;
                border-radius: 3px;
                background: #fff;
                color: #000;
                cursor: pointer;
                font-size: 10px;
            }
            
            .debug-panel-tabs {
                display: flex;
                background: #333;
            }
            
            .debug-tab {
                flex: 1;
                padding: 8px 4px;
                border: none;
                background: #333;
                color: #ccc;
                cursor: pointer;
                font-size: 10px;
                border-right: 1px solid #555;
            }
            
            .debug-tab.active {
                background: #4CAF50;
                color: #000;
            }
            
            .debug-panel-content {
                max-height: 50vh;
                overflow-y: auto;
                padding: 10px;
            }
            
            .debug-tab-content {
                display: none;
            }
            
            .debug-tab-content.active {
                display: block;
            }
            
            .debug-section {
                margin-bottom: 15px;
                padding: 8px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 4px;
            }
            
            .debug-section h4 {
                margin: 0 0 8px 0;
                color: #4CAF50;
                font-size: 12px;
            }
            
            .debug-item {
                display: flex;
                justify-content: space-between;
                margin: 3px 0;
                font-size: 11px;
            }
            
            .debug-value {
                color: #FFC107;
            }
            
            .debug-log-controls {
                margin-bottom: 10px;
            }
            
            .debug-log-controls button {
                margin-right: 5px;
                padding: 3px 8px;
                border: none;
                border-radius: 3px;
                background: #4CAF50;
                color: #000;
                cursor: pointer;
                font-size: 10px;
            }
            
            #debug-logs-content {
                max-height: 200px;
                overflow-y: auto;
                background: #000;
                padding: 5px;
                border-radius: 3px;
                font-family: monospace;
                font-size: 10px;
            }
            
            .log-entry {
                margin: 2px 0;
                padding: 2px;
                border-left: 3px solid #4CAF50;
                padding-left: 8px;
            }
            
            .log-error {
                border-left-color: #f44336;
                color: #ffcdd2;
            }
            
            .log-warn {
                border-left-color: #ff9800;
                color: #ffe0b2;
            }
            
            .log-info {
                border-left-color: #2196f3;
                color: #bbdefb;
            }
            
            @media (max-width: 768px) {
                .debug-panel {
                    width: 90vw;
                    right: 5vw;
                    top: 10px;
                }
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 关闭按钮
        document.addEventListener('click', (e) => {
            if (e.target.id === 'debug-close-btn') {
                this.hide();
            }
            if (e.target.id === 'debug-refresh-btn') {
                this.updateContent();
            }
            if (e.target.id === 'debug-clear-btn') {
                this.clearCache();
            }
            if (e.target.id === 'clear-logs-btn') {
                this.clearLogs();
            }
            if (e.target.id === 'export-logs-btn') {
                this.exportLogs();
            }
        });

        // 标签切换
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('debug-tab')) {
                this.switchTab(e.target.dataset.tab);
            }
        });

        // 快捷键
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.shiftKey && e.key === 'D') {
                e.preventDefault();
                this.toggle();
            }
        });

        // 监听战斗系统错误
        window.addEventListener('battleSystemError', (e) => {
            this.addLog('error', `[${e.detail.manager}] ${e.detail.context}: ${e.detail.error.message}`);
        });
    }

    /**
     * 显示调试面板
     */
    show() {
        this.isVisible = true;
        this.panel.classList.remove('hidden');
        this.startUpdating();
        this.updateContent();
    }

    /**
     * 隐藏调试面板
     */
    hide() {
        this.isVisible = false;
        this.panel.classList.add('hidden');
        this.stopUpdating();
    }

    /**
     * 切换调试面板显示状态
     */
    toggle() {
        if (this.isVisible) {
            this.hide();
        } else {
            this.show();
        }
    }

    /**
     * 切换标签
     */
    switchTab(tabName) {
        // 更新标签状态
        document.querySelectorAll('.debug-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // 更新内容显示
        document.querySelectorAll('.debug-tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`debug-${tabName}`).classList.add('active');

        this.currentTab = tabName;
        this.updateContent();
    }

    /**
     * 开始定时更新
     */
    startUpdating() {
        this.stopUpdating();
        this.updateInterval = setInterval(() => {
            if (this.isVisible) {
                this.updateContent();
            }
        }, 2000); // 每2秒更新一次
    }

    /**
     * 停止定时更新
     */
    stopUpdating() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }

    /**
     * 更新内容
     */
    updateContent() {
        switch (this.currentTab) {
            case 'overview':
                this.updateOverview();
                break;
            case 'managers':
                this.updateManagers();
                break;
            case 'performance':
                this.updatePerformance();
                break;
            case 'cache':
                this.updateCache();
                break;
            case 'logs':
                this.updateLogs();
                break;
        }
    }

    /**
     * 更新概览信息
     */
    updateOverview() {
        const battleSystemEl = document.getElementById('battle-system-status');
        const realtimeStatsEl = document.getElementById('realtime-stats');

        // 战斗系统状态
        const battleSystem = window.battleSystem;
        let battleStatus = '';
        if (battleSystem) {
            battleStatus = `
                <div class="debug-item"><span>系统状态:</span> <span class="debug-value">运行中</span></div>
                <div class="debug-item"><span>当前轮次:</span> <span class="debug-value">${battleSystem.attackCount || 0}</span></div>
                <div class="debug-item"><span>游戏结束:</span> <span class="debug-value">${battleSystem.isGameOver || false}</span></div>
                <div class="debug-item"><span>技能序列:</span> <span class="debug-value">${battleSystem.skillSequence?.length || 0} 个技能</span></div>
                <div class="debug-item"><span>玩家MP:</span> <span class="debug-value">${battleSystem.playerStats?.currentMp || 0}/${battleSystem.playerStats?.mp_bonus || 100}</span></div>
            `;
        } else {
            battleStatus = '<div class="debug-item"><span>系统状态:</span> <span class="debug-value">未初始化</span></div>';
        }
        battleSystemEl.innerHTML = battleStatus;

        // 实时统计
        const now = new Date();
        const stats = `
            <div class="debug-item"><span>当前时间:</span> <span class="debug-value">${now.toLocaleTimeString()}</span></div>
            <div class="debug-item"><span>页面加载时间:</span> <span class="debug-value">${Math.round((performance.now() / 1000))} 秒</span></div>
            <div class="debug-item"><span>内存使用:</span> <span class="debug-value">${this.getMemoryUsage()}</span></div>
            <div class="debug-item"><span>FPS:</span> <span class="debug-value">${this.getFPS()}</span></div>
        `;
        realtimeStatsEl.innerHTML = stats;
    }

    /**
     * 更新管理器信息
     */
    updateManagers() {
        const managersStatusEl = document.getElementById('managers-status');
        const dependencyGraphEl = document.getElementById('dependency-graph');

        if (window.ManagerRegistry) {
            const stats = window.ManagerRegistry.getStats();
            
            // 管理器状态
            let managersHtml = '';
            stats.managerNames.forEach(name => {
                // 使用has方法避免警告日志
                const exists = window.ManagerRegistry.has(name);
                const status = exists ? '✅ 活跃' : '❌ 未找到';
                managersHtml += `<div class="debug-item"><span>${name}:</span> <span class="debug-value">${status}</span></div>`;
            });
            managersStatusEl.innerHTML = managersHtml;

            // 依赖关系图
            let dependencyHtml = '';
            Object.entries(stats.dependencyGraph).forEach(([name, deps]) => {
                dependencyHtml += `<div class="debug-item"><span>${name}:</span> <span class="debug-value">[${deps.join(', ') || '无依赖'}]</span></div>`;
            });
            dependencyGraphEl.innerHTML = dependencyHtml;
        } else {
            managersStatusEl.innerHTML = '<div class="debug-item">管理器注册中心未初始化</div>';
            dependencyGraphEl.innerHTML = '<div class="debug-item">依赖关系图不可用</div>';
        }
    }

    /**
     * 更新性能信息
     */
    updatePerformance() {
        const performanceMetricsEl = document.getElementById('performance-metrics');
        const skillPerformanceEl = document.getElementById('skill-performance');

        // 性能指标
        const metrics = `
            <div class="debug-item"><span>DOM节点数:</span> <span class="debug-value">${document.getElementsByTagName('*').length}</span></div>
            <div class="debug-item"><span>事件监听器:</span> <span class="debug-value">${this.getEventListenerCount()}</span></div>
            <div class="debug-item"><span>计时器数量:</span> <span class="debug-value">${this.getTimerCount()}</span></div>
            <div class="debug-item"><span>网络请求:</span> <span class="debug-value">${this.getNetworkRequestCount()}</span></div>
        `;
        performanceMetricsEl.innerHTML = metrics;

        // 技能性能
        let skillStats = '';
        if (window.SkillLoaderInstance) {
            const skillCache = window.SkillLoaderInstance.getCacheStats();
            skillStats = `
                <div class="debug-item"><span>已加载模块:</span> <span class="debug-value">${skillCache.loadedModules}</span></div>
                <div class="debug-item"><span>已加载CSS:</span> <span class="debug-value">${skillCache.loadedCSS}</span></div>
                <div class="debug-item"><span>缓存实例:</span> <span class="debug-value">${skillCache.cachedInstances}</span></div>
            `;
        } else {
            skillStats = '<div class="debug-item">技能加载器不可用</div>';
        }
        skillPerformanceEl.innerHTML = skillStats;
    }

    /**
     * 更新缓存信息
     */
    updateCache() {
        const cacheStatsEl = document.getElementById('cache-stats');
        const memoryUsageEl = document.getElementById('memory-usage');

        // 缓存统计
        let cacheHtml = '';
        
        // 图片缓存
        if (window.ImagePathManager) {
            const imageCache = window.ImagePathManager.getCacheStats();
            cacheHtml += `<div class="debug-item"><span>图片路径缓存:</span> <span class="debug-value">${imageCache.size} 项</span></div>`;
        }

        // 技能缓存
        if (window.SkillLoaderInstance) {
            const skillCache = window.SkillLoaderInstance.getCacheStats();
            cacheHtml += `<div class="debug-item"><span>技能实例缓存:</span> <span class="debug-value">${skillCache.cachedInstances} 项</span></div>`;
        }

        cacheStatsEl.innerHTML = cacheHtml || '<div class="debug-item">缓存信息不可用</div>';

        // 内存使用
        let memoryHtml = '';
        if (window.battleMemoryManager) {
            const memStats = window.battleMemoryManager.getMemoryStats();
            memoryHtml = `
                <div class="debug-item"><span>活跃定时器:</span> <span class="debug-value">${memStats.activeTimers}</span></div>
                <div class="debug-item"><span>活跃间隔器:</span> <span class="debug-value">${memStats.activeIntervals}</span></div>
                <div class="debug-item"><span>事件监听器:</span> <span class="debug-value">${memStats.activeEventListeners}</span></div>
                <div class="debug-item"><span>技能实例:</span> <span class="debug-value">${memStats.skillInstances}</span></div>
            `;
        } else {
            memoryHtml = '<div class="debug-item">内存管理器不可用</div>';
        }
        memoryUsageEl.innerHTML = memoryHtml;
    }

    /**
     * 更新日志信息
     */
    updateLogs() {
        const logsContentEl = document.getElementById('debug-logs-content');
        
        let logsHtml = '';
        this.logs.forEach(log => {
            logsHtml += `<div class="log-entry log-${log.level}">[${log.timestamp}] ${log.message}</div>`;
        });
        
        logsContentEl.innerHTML = logsHtml || '<div class="log-entry">暂无日志</div>';
        
        // 自动滚动到底部
        logsContentEl.scrollTop = logsContentEl.scrollHeight;
    }

    /**
     * 添加日志
     */
    addLog(level, message) {
        const timestamp = new Date().toLocaleTimeString();
        this.logs.push({ level, message, timestamp });
        
        // 限制日志数量
        if (this.logs.length > this.maxLogs) {
            this.logs.shift();
        }
        
        // 如果当前在日志标签，立即更新
        if (this.currentTab === 'logs' && this.isVisible) {
            this.updateLogs();
        }
    }

    /**
     * 清空日志
     */
    clearLogs() {
        this.logs = [];
        this.updateLogs();
    }

    /**
     * 导出日志
     */
    exportLogs() {
        const logsText = this.logs.map(log => `[${log.timestamp}] ${log.level.toUpperCase()}: ${log.message}`).join('\n');
        const blob = new Blob([logsText], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `battle-debug-logs-${new Date().toISOString().slice(0, 19)}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    /**
     * 清理缓存
     */
    clearCache() {
        if (window.ImagePathManager && typeof window.ImagePathManager.clearImageCache === 'function') {
            window.ImagePathManager.clearImageCache();
        }
        
        if (window.battleMemoryManager && typeof window.battleMemoryManager.partialCleanup === 'function') {
            window.battleMemoryManager.partialCleanup();
        }
        
        this.addLog('info', '缓存清理完成');
        this.updateContent();
    }

    /**
     * 获取内存使用情况
     */
    getMemoryUsage() {
        if ('memory' in performance) {
            return `${Math.round(performance.memory.usedJSHeapSize / 1024 / 1024)} MB`;
        }
        return '不可用';
    }

    /**
     * 获取FPS
     */
    getFPS() {
        // 简单的FPS估算
        return Math.round(1000 / 16) + ' (估算)';
    }

    /**
     * 获取事件监听器数量
     */
    getEventListenerCount() {
        if (window.battleMemoryManager) {
            const stats = window.battleMemoryManager.getMemoryStats();
            return stats.activeEventListeners || 0;
        }
        return '不可用';
    }

    /**
     * 获取计时器数量
     */
    getTimerCount() {
        if (window.battleMemoryManager) {
            const stats = window.battleMemoryManager.getMemoryStats();
            return (stats.activeTimers || 0) + (stats.activeIntervals || 0);
        }
        return '不可用';
    }

    /**
     * 获取网络请求数量
     */
    getNetworkRequestCount() {
        if ('getEntriesByType' in performance) {
            return performance.getEntriesByType('navigation').length + 
                   performance.getEntriesByType('resource').length;
        }
        return '不可用';
    }

    /**
     * 销毁调试面板
     */
    destroy() {
        this.stopUpdating();
        if (this.panel && this.panel.parentNode) {
            this.panel.parentNode.removeChild(this.panel);
        }
    }
}

// 创建全局调试面板实例
window.BattleDebugPanel = new BattleDebugPanel();

// 在控制台提供快捷访问
if (window.BattleDebugConfig && window.BattleDebugConfig.isDebugEnabled('debug-panel')) {
    console.log('🔧 调试面板已加载！使用 Ctrl+Shift+D 打开/关闭调试面板');
    console.log('🔧 或使用 window.BattleDebugPanel.show() 显示调试面板');
} 