<?php
// 🔧 修复：避免重复启动session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
header('Content-Type: application/json; charset=utf-8');

// 处理会话状态检查请求（GET请求）
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    // 修复need_create_character标志
    if (isset($_GET['fix_need_create'])) {
        $_SESSION['need_create_character'] = true;
        
        error_log("修复need_create_character标志: 用户ID=" . (isset($_SESSION['user_id']) ? $_SESSION['user_id'] : '未知'));
        echo json_encode(['success' => true, 'message' => 'need_create_character标志已设置']);
        exit;
    }

    // 检查need_create_character标志
    if (isset($_GET['check_need_create'])) {
        $response = [
            'has_need_create_character' => isset($_SESSION['need_create_character']),
            'need_create_character_value' => isset($_SESSION['need_create_character']) ? $_SESSION['need_create_character'] : null,
            'session_id' => session_id(),
            'user_id' => isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null
        ];
        
        error_log("need_create_character检查结果: " . json_encode($response));
        echo json_encode($response);
        exit;
    }

    // 会话诊断
    if (isset($_GET['diagnostic'])) {
        $sessionData = [
            'session_id' => session_id(),
            'session_status' => [
                'has_user_id' => isset($_SESSION['user_id']),
                'user_id' => isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null,
                'has_need_create_character' => isset($_SESSION['need_create_character']),
                'need_create_character' => isset($_SESSION['need_create_character']) ? $_SESSION['need_create_character'] : null
            ],
            'session_data' => $_SESSION,
            'server_time' => date('Y-m-d H:i:s'),
            'php_session_status' => session_status(),
            'cookie_params' => session_get_cookie_params()
        ];
        
        error_log("会话诊断结果: " . json_encode($sessionData));
        echo json_encode($sessionData);
        exit;
    }

    // 默认返回会话状态
    echo json_encode([
        'session_active' => session_status() === PHP_SESSION_ACTIVE,
        'user_logged_in' => isset($_SESSION['user_id']),
        'user_id' => isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null,
        'session_id' => session_id()
    ]);
    exit;
}

// 🔧 性能监控：记录开始时间
$start_time = microtime(true);
$request_id = uniqid('req_');

// 记录详细日志，帮助调试
error_log("[$request_id] 🚀 角色创建请求开始处理，时间：" . date('Y-m-d H:i:s.u'));
error_log("[$request_id] 📊 内存使用: " . memory_get_usage(true) . " bytes");
error_log("[$request_id] 👤 用户ID: " . (isset($_SESSION['user_id']) ? $_SESSION['user_id'] : '未登录'));

// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    $login_check_time = microtime(true);
    error_log("[$request_id] ❌ 创建角色失败：用户未登录，耗时: " . round(($login_check_time - $start_time) * 1000, 2) . "ms");
    echo json_encode([
        'success' => false,
        'message' => '请先登录',
        'debug' => [
            'request_id' => $request_id,
            'error_type' => 'not_logged_in',
            'processing_time' => round(($login_check_time - $start_time) * 1000, 2)
        ]
    ]);
    exit;
}

// 数据库连接
require_once __DIR__ . '/../config/database.php';

try {
    // 🔧 性能监控：数据库连接时间
    $db_start_time = microtime(true);
    $pdo = getDatabase();
    $db_connect_time = microtime(true);
    
    error_log("[$request_id] 🔗 数据库连接耗时: " . round(($db_connect_time - $db_start_time) * 1000, 2) . "ms");
    
    if (!$pdo) {
        throw new Exception('数据库连接失败');
    }
    
    // 获取POST数据
    $input_start_time = microtime(true);
    $input = json_decode(file_get_contents('php://input'), true);
    $input_parse_time = microtime(true);
    
    error_log("[$request_id] 📝 输入数据解析耗时: " . round(($input_parse_time - $input_start_time) * 1000, 2) . "ms");
    
    if (!$input || !isset($input['character_name'])) {
        echo json_encode([
            'success' => false,
            'message' => '缺少角色名参数',
            'debug' => [
                'request_id' => $request_id,
                'error_type' => 'missing_parameters',
                'processing_time' => round((microtime(true) - $start_time) * 1000, 2)
            ]
        ]);
        exit;
    }
    
    $character_name = trim($input['character_name']);
    $character_avatar = isset($input['character_avatar']) ? trim($input['character_avatar']) : 'ck.png';
    $user_id = $_SESSION['user_id'];
    
    // 🔧 新增：处理灵根数据
    $spirit_roots = null;
    if (isset($input['spirit_roots']) && is_array($input['spirit_roots'])) {
        $spirit_roots = $input['spirit_roots'];
        
        // 验证灵根数据
        $required_elements = ['metal', 'wood', 'water', 'fire', 'earth'];
        $total_points = 0;
        
        foreach ($required_elements as $element) {
            if (!isset($spirit_roots[$element]) || !is_numeric($spirit_roots[$element])) {
                echo json_encode([
                    'success' => false,
                    'message' => "缺少{$element}灵根数据",
                    'debug' => [
                        'request_id' => $request_id,
                        'error_type' => 'invalid_spirit_roots',
                        'processing_time' => round((microtime(true) - $start_time) * 1000, 2)
                    ]
                ]);
                exit;
            }
            
            $value = intval($spirit_roots[$element]);
            if ($value < 8 || $value > 35) {
                echo json_encode([
                    'success' => false,
                    'message' => "灵根数值必须在8-35之间",
                    'debug' => [
                        'request_id' => $request_id,
                        'error_type' => 'invalid_spirit_value',
                        'processing_time' => round((microtime(true) - $start_time) * 1000, 2)
                    ]
                ]);
                exit;
            }
            
            $total_points += $value;
        }
        
        // 验证总数必须为100
        if ($total_points !== 100) {
            echo json_encode([
                'success' => false,
                'message' => "灵根总数必须为100点，当前为{$total_points}点",
                'debug' => [
                    'request_id' => $request_id,
                    'error_type' => 'invalid_total_points',
                    'processing_time' => round((microtime(true) - $start_time) * 1000, 2)
                ]
            ]);
            exit;
        }
    }
    
    error_log("[$request_id] 📋 创建角色请求 - 用户ID: $user_id, 角色名: $character_name, 头像: $character_avatar");
    if ($spirit_roots) {
        error_log("[$request_id] 🌟 灵根数据: " . json_encode($spirit_roots));
    }
    
    // 🔧 性能监控：验证阶段
    $validation_start_time = microtime(true);
    
    // 验证角色名
    if (empty($character_name)) {
        echo json_encode([
            'success' => false,
            'message' => '请输入道号',
            'debug' => [
                'request_id' => $request_id,
                'error_type' => 'empty_name',
                'processing_time' => round((microtime(true) - $start_time) * 1000, 2)
            ]
        ]);
        exit;
    }
    
    if (mb_strlen($character_name) < 2) {
        echo json_encode([
            'success' => false,
            'message' => '道号至少需要2个字符',
            'debug' => [
                'request_id' => $request_id,
                'error_type' => 'name_too_short',
                'processing_time' => round((microtime(true) - $start_time) * 1000, 2)
            ]
        ]);
        exit;
    }
    
    if (mb_strlen($character_name) > 8) {
        echo json_encode([
            'success' => false,
            'message' => '道号不能超过8个字符',
            'debug' => [
                'request_id' => $request_id,
                'error_type' => 'name_too_long',
                'processing_time' => round((microtime(true) - $start_time) * 1000, 2)
            ]
        ]);
        exit;
    }
    
    // 检查角色名是否包含非法字符
    if (!preg_match('/^[\x{4e00}-\x{9fa5}a-zA-Z0-9_]+$/u', $character_name)) {
        echo json_encode([
            'success' => false,
            'message' => '道号只能包含中文、英文、数字和下划线',
            'debug' => [
                'request_id' => $request_id,
                'error_type' => 'invalid_characters',
                'processing_time' => round((microtime(true) - $start_time) * 1000, 2)
            ]
        ]);
        exit;
    }
    
    // 验证头像文件名（安全检查）
    if (!preg_match('/^[a-zA-Z0-9_.-]+\.png$/i', $character_avatar)) {
        $character_avatar = 'ck.png'; // 使用默认头像
    }
    
    $validation_end_time = microtime(true);
    error_log("[$request_id] ✅ 数据验证完成，耗时: " . round(($validation_end_time - $validation_start_time) * 1000, 2) . "ms");
    
    // 🔧 性能监控：数据库查询阶段
    $db_query_start_time = microtime(true);
    
    // 检查角色名是否已被使用
    $stmt = $pdo->prepare("SELECT id FROM characters WHERE character_name = ? AND user_id != ?");
    $stmt->execute([$character_name, $user_id]);
    
    if ($stmt->fetch()) {
        echo json_encode([
            'success' => false,
            'message' => '此道号已被使用，请选择其他道号',
            'debug' => [
                'request_id' => $request_id,
                'error_type' => 'name_exists',
                'processing_time' => round((microtime(true) - $start_time) * 1000, 2)
            ]
        ]);
        exit;
    }
    
    // 检查用户是否已经创建过角色
    $stmt = $pdo->prepare("SELECT id FROM characters WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $existingCharacter = $stmt->fetch();
    
    if ($existingCharacter) {
        echo json_encode([
            'success' => false,
            'message' => '角色已创建，无法重复创建',
            'debug' => [
                'request_id' => $request_id,
                'error_type' => 'character_exists',
                'processing_time' => round((microtime(true) - $start_time) * 1000, 2)
            ]
        ]);
        exit;
    }
    
    $db_query_end_time = microtime(true);
    error_log("[$request_id] 🔍 数据库查询完成，耗时: " . round(($db_query_end_time - $db_query_start_time) * 1000, 2) . "ms");
    
    // 🔧 性能监控：事务处理阶段
    $transaction_start_time = microtime(true);
    
    // 开始事务
    $pdo->beginTransaction();
    
    try {
        // 获取服务器ID
        $serverStmt = $pdo->query("SELECT id FROM game_servers WHERE is_active = TRUE ORDER BY id LIMIT 1");
        $server = $serverStmt->fetch(PDO::FETCH_ASSOC);
        $serverId = $server ? $server['id'] : 1;
        
        // 获取初始境界ID
        $realmStmt = $pdo->query("SELECT id FROM realm_levels WHERE realm_level = 1 LIMIT 1");
        $realm = $realmStmt->fetch(PDO::FETCH_ASSOC);
        $realmId = $realm ? $realm['id'] : 1;
        
        error_log("[$request_id] 🎮 游戏数据获取 - 服务器ID: $serverId, 境界ID: $realmId");
        
        // 🔧 性能监控：角色创建
        $character_create_start = microtime(true);
        
        // 创建角色记录
        $stmt = $pdo->prepare("
            INSERT INTO characters (
                user_id,
                server_id,
                original_server_id,
                character_name,
                original_name,
                realm_id,
                avatar_image,
                physique,
                comprehension,
                constitution,
                spirit,
                agility,
                current_hp,
                current_mp,
                metal_affinity,
                wood_affinity,
                water_affinity,
                fire_affinity,
                earth_affinity,
                last_login_time,
                created_at,
                updated_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, 
                10, 10, 10, 10, 10, 
                100, 100, 
                ?, ?, ?, ?, ?,
                NOW(), NOW(), NOW()
            )
        ");
        
        // 获取灵根数值，如果前端没传则使用默认值20
        $metal_affinity = $spirit_roots ? intval($spirit_roots['metal']) : 20;
        $wood_affinity = $spirit_roots ? intval($spirit_roots['wood']) : 20;
        $water_affinity = $spirit_roots ? intval($spirit_roots['water']) : 20;
        $fire_affinity = $spirit_roots ? intval($spirit_roots['fire']) : 20;
        $earth_affinity = $spirit_roots ? intval($spirit_roots['earth']) : 20;
        
        $result = $stmt->execute([
            $user_id,
            $serverId,
            $serverId,
            $character_name,
            $character_name, // 🔧 修复：original_name使用相同的角色名
            $realmId,
            $character_avatar,
            $metal_affinity,
            $wood_affinity,
            $water_affinity,
            $fire_affinity,
            $earth_affinity
        ]);
        
        if (!$result) {
            throw new Exception("角色数据插入失败");
        }
        
        $characterId = $pdo->lastInsertId();
        $character_create_end = microtime(true);
        
        error_log("[$request_id] 👤 角色创建完成，ID: $characterId，耗时: " . round(($character_create_end - $character_create_start) * 1000, 2) . "ms");
        
        // 🔧 修改：使用前端传来的灵根数据，而不是自动生成
        $spiritual_root_start_time = microtime(true);
        
        if ($spirit_roots) {
        require_once __DIR__ . '/five_elements_spiritual_root.php';
        
            // 构建灵根数据结构供评价函数使用
            $spiritualRoots = [];
            $elements = ['metal', 'wood', 'water', 'fire', 'earth'];
            
            foreach ($elements as $element) {
                $value = intval($spirit_roots[$element]);
                $spiritualRoots[$element] = [
                    'value' => $value,
                    'quality' => determineRootQualityHelper($value),
                    'multiplier' => 1.0 // 简化处理
                ];
            }
            
            // 获取灵根总体评价
            $rootEvaluation = FiveElementsSpiritualRootSystem::getRootOverallEvaluation($spiritualRoots);
            error_log("[$request_id] 🌟 用户自定义灵根 - 体质: {$rootEvaluation['overall_level']}, 主属性: {$rootEvaluation['dominant_element_name']}");
        } else {
            // 如果没有传灵根数据，记录使用默认值
            error_log("[$request_id] 🌟 使用默认灵根值（每个20点）");
            $spiritualRoots = null;
            $rootEvaluation = null;
        }
        
        $spiritual_root_end_time = microtime(true);
        error_log("[$request_id] 🌟 灵根数据处理完成，耗时: " . round(($spiritual_root_end_time - $spiritual_root_start_time) * 1000, 2) . "ms");
        
        // 🆕 为新角色设置默认功法：凝气决，并设置为当前修炼
        $technique_start_time = microtime(true);
        
        try {
            $defaultTechniques = [
                'ningqi' => [
                    'id' => 'ningqi',
                    'name' => '凝气决',
                    'level' => 1,
                    'exp' => 0,
                    'type' => 'basic',
                    'source' => 'basic',
                    'unlocked' => true,
                    'efficiency_bonus' => 0
                ]
            ];
            
            $techniquesJson = json_encode($defaultTechniques, JSON_UNESCAPED_UNICODE);
            
            // 🔧 修复：同时设置功法、当前修炼功法和修炼时间基准
            $stmt = $pdo->prepare("
                UPDATE characters 
                SET cultivation_techniques = ?,
                    current_technique = ?,
                    last_cultivation_time = NOW()
                WHERE id = ?
            ");
            
            $stmt->execute([$techniquesJson, 'ningqi', $characterId]);
            
            error_log("[$request_id] 📜 默认功法凝气决设置完成，已启用自动修炼，当前修炼功法：凝气决");
            
        } catch (Exception $e) {
            error_log("[$request_id] ⚠️ 设置默认功法失败: " . $e->getMessage());
            // 不抛出异常，让角色创建继续
        }
        
        $technique_end_time = microtime(true);
        error_log("[$request_id] 📜 功法设置完成，耗时: " . round(($technique_end_time - $technique_start_time) * 1000, 2) . "ms");
        
        // 🆕 新增：初始化武器槽位（在装备分配之前）
        $weapon_slot_start_time = microtime(true);
        
        try {
            initializeWeaponSlots($pdo, $characterId);
            error_log("[$request_id] ⚔️ 武器槽位初始化成功");
        } catch (Exception $e) {
            error_log("[$request_id] ⚠️ 武器槽位初始化失败: " . $e->getMessage());
            // 不抛出异常，让装备分配继续尝试
        }
        
        $weapon_slot_end_time = microtime(true);
        error_log("[$request_id] ⚔️ 武器槽位初始化完成，耗时: " . round(($weapon_slot_end_time - $weapon_slot_start_time) * 1000, 2) . "ms");
        
        // 🔧 性能监控：装备分配阶段
        $equipment_start_time = microtime(true);
        
        // 为新角色分配基础装备（升级版本 - 完整7件套）
        $equipmentResult = giveStarterEquipmentFast($pdo, $characterId);
        
        $equipment_end_time = microtime(true);
        error_log("[$request_id] ⚔️ 装备分配完成，耗时: " . round(($equipment_end_time - $equipment_start_time) * 1000, 2) . "ms");
        
        if (!$equipmentResult['success']) {
            error_log("[$request_id] ⚠️ 基础装备分配失败: " . $equipmentResult['message']);
            
            // 🔧 新增：装备分配失败时的补救措施
            try {
                // 尝试基础的装备分配修复
                $fixResult = fixNewbieEquipmentForCharacter($pdo, $characterId);
                if ($fixResult['success']) {
                    error_log("[$request_id] 🔧 装备分配修复成功: " . $fixResult['message']);
                    $equipmentResult = $fixResult; // 更新结果
                } else {
                    error_log("[$request_id] ❌ 装备分配修复也失败: " . $fixResult['message']);
                    // 记录详细的错误信息但不中断角色创建
                    error_log("[$request_id] 📝 装备分配失败详情: " . json_encode($equipmentResult));
                }
            } catch (Exception $fixException) {
                error_log("[$request_id] 💥 装备修复异常: " . $fixException->getMessage());
            }
        } else {
            error_log("[$request_id] ✅ 基础装备分配成功: " . $equipmentResult['message']);
        }
        
        // 🔧 性能监控：日志记录阶段
        $log_start_time = microtime(true);
        
        // 记录角色创建日志
        $stmt = $pdo->prepare("
            INSERT INTO login_logs (
                user_id, 
                username, 
                login_type, 
                ip_address, 
                user_agent, 
                created_at
            ) VALUES (
                ?, ?, 'success', ?, ?, NOW()
            )
        ");
        
        $ip_address = isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : 'unknown';
        $user_agent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : 'unknown';
        $stmt->execute([
            $user_id,
            $_SESSION['username'],
            $ip_address,
            $user_agent
        ]);
        
        $log_end_time = microtime(true);
        error_log("[$request_id] 📝 日志记录完成，耗时: " . round(($log_end_time - $log_start_time) * 1000, 2) . "ms");
        
        // 提交事务
        $pdo->commit();
        $transaction_end_time = microtime(true);
        
        error_log("[$request_id] 💾 事务提交完成，耗时: " . round(($transaction_end_time - $transaction_start_time) * 1000, 2) . "ms");
        
        // 🔧 新增：角色创建成功后立即更新会话信息
        $session_update_start = microtime(true);
        
        // 获取完整的角色信息用于会话
        $stmt = $pdo->prepare("
            SELECT c.id, c.character_name, c.avatar_image, c.realm_id,
                   r.realm_name, r.realm_level
            FROM characters c
            LEFT JOIN realm_levels r ON c.realm_id = r.id
            WHERE c.id = ?
        ");
        $stmt->execute([$characterId]);
        $charDetail = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($charDetail) {
            // 设置角色信息到会话
            $_SESSION['character_id'] = $charDetail['id'];
            $_SESSION['character_name'] = $charDetail['character_name'];
            $_SESSION['character_avatar'] = $charDetail['avatar_image'];
            $_SESSION['realm_id'] = $charDetail['realm_id'];
            $_SESSION['realm_name'] = $charDetail['realm_name'];
            $_SESSION['realm_level'] = $charDetail['realm_level'];
            
            // 移除need_create_character标志
            if (isset($_SESSION['need_create_character'])) {
                unset($_SESSION['need_create_character']);
                error_log("[$request_id] 🗑️ 已移除need_create_character标志");
            }
            
            error_log("[$request_id] ✅ 会话信息已更新 - 角色ID: {$charDetail['id']}, 角色名: {$charDetail['character_name']}");
        } else {
            error_log("[$request_id] ⚠️ 无法获取角色详细信息，会话更新失败");
        }
        
        $session_update_end = microtime(true);
        error_log("[$request_id] 🔄 会话更新完成，耗时: " . round(($session_update_end - $session_update_start) * 1000, 2) . "ms");
        
        // 🔧 性能监控：总耗时统计
        $total_time = microtime(true) - $start_time;
        $memory_usage = memory_get_usage(true);
        $peak_memory = memory_get_peak_usage(true);
        
        error_log("[$request_id] 🎉 角色创建成功！总耗时: " . round($total_time * 1000, 2) . "ms");
        error_log("[$request_id] 📊 内存使用: " . $memory_usage . " bytes, 峰值: " . $peak_memory . " bytes");
        
        // 返回成功响应
        echo json_encode([
            'success' => true,
            'message' => '角色创建成功',
            'data' => [
                'character_id' => $characterId,
                'character_name' => $character_name,
                'equipment_result' => $equipmentResult,
                'spiritual_roots' => $spiritualRoots,
                'root_evaluation' => $rootEvaluation
            ],
            'debug' => [
                'request_id' => $request_id,
                'processing_time' => round($total_time * 1000, 2),
                'memory_usage' => $memory_usage,
                'peak_memory' => $peak_memory,
                'performance_breakdown' => [
                    'database_connection' => round(($db_connect_time - $db_start_time) * 1000, 2),
                    'input_parsing' => round(($input_parse_time - $input_start_time) * 1000, 2),
                    'validation' => round(($validation_end_time - $validation_start_time) * 1000, 2),
                    'database_queries' => round(($db_query_end_time - $db_query_start_time) * 1000, 2),
                    'character_creation' => round(($character_create_end - $character_create_start) * 1000, 2),
                    'spiritual_root_generation' => round(($spiritual_root_end_time - $spiritual_root_start_time) * 1000, 2),
                    'weapon_slot_initialization' => round(($weapon_slot_end_time - $weapon_slot_start_time) * 1000, 2),
                    'equipment_assignment' => round(($equipment_end_time - $equipment_start_time) * 1000, 2),
                    'log_recording' => round(($log_end_time - $log_start_time) * 1000, 2),
                    'transaction_total' => round(($transaction_end_time - $transaction_start_time) * 1000, 2)
                ]
            ]
        ]);
        
    } catch (Exception $e) {
        $pdo->rollback();
        $error_time = microtime(true);
        
        error_log("[$request_id] 💥 事务回滚，错误: " . $e->getMessage() . "，耗时: " . round(($error_time - $transaction_start_time) * 1000, 2) . "ms");
        throw $e;
    }
    
} catch (Exception $e) {
    $error_time = microtime(true);
    $total_error_time = $error_time - $start_time;
    
    error_log("[$request_id] ❌ 角色创建失败: " . $e->getMessage() . "，总耗时: " . round($total_error_time * 1000, 2) . "ms");
    error_log("[$request_id] 📍 错误堆栈: " . $e->getTraceAsString());
    
    echo json_encode([
        'success' => false,
        'message' => '角色创建失败: ' . $e->getMessage(),
        'debug' => [
            'request_id' => $request_id,
            'error_type' => 'exception',
            'error_message' => $e->getMessage(),
            'error_file' => $e->getFile(),
            'error_line' => $e->getLine(),
            'processing_time' => round($total_error_time * 1000, 2),
            'memory_usage' => memory_get_usage(true)
        ]
    ]);
}

/**
 * 🚀 超快速：为新角色分配基础装备（升级版本 - 完整7件套）
 * 新手套装包含：1把武器 + 6件装备，覆盖所有槽位
 */
function giveStarterEquipmentFast($pdo, $characterId) {
    try {
        error_log("[装备分配] 🚀 开始为角色ID {$characterId} 分配新手装备");
        
        // 检查是否已有基础装备
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM user_inventories WHERE character_id = ?");
        $stmt->execute([$characterId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result['count'] > 0) {
            error_log("[装备分配] ⚠️ 角色ID {$characterId} 已有装备，跳过分配");
            return [
                'success' => true,
                'message' => '角色已有装备，跳过分配',
                'items' => []
            ];
        }
        
        error_log("[装备分配] ✅ 角色ID {$characterId} 背包为空，继续装备分配");
        
        // 🚀 性能优化：预先检查所有新手装备是否存在（青云套装7件套）
        $itemCodes = [
            'newbie_sword',      // 青云弟子剑（武器）
            'newbie_robe',       // 青云道袍（胸甲）
            'newbie_belt',       // 青云腰带（腰带）
            'newbie_boots',      // 青云云靴（靴子）
            'newbie_bracelet',   // 青云护腕（护腕）
            'newbie_ring',       // 青云灵戒（戒指）
            'newbie_necklace'    // 青云灵珠（项链）
        ];
        
        $placeholders = str_repeat('?,', count($itemCodes) - 1) . '?';
        $stmt = $pdo->prepare("SELECT item_code, id FROM game_items WHERE item_code IN ($placeholders)");
        $stmt->execute($itemCodes);
        $existingItems = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $existingItems[$row['item_code']] = $row['id'];
        }
        
        error_log("[装备分配] 🔍 找到的装备: " . json_encode($existingItems));
        
        // 检查是否缺少关键装备
        $criticalItems = ['newbie_sword', 'newbie_robe', 'newbie_belt', 'newbie_boots', 'newbie_bracelet', 'newbie_ring', 'newbie_necklace'];
        $missingItems = array_diff($criticalItems, array_keys($existingItems));
        if (!empty($missingItems)) {
            $errorMsg = '青云套装装备缺失: ' . implode(', ', $missingItems);
            error_log("[装备分配] ❌ 错误: " . $errorMsg);
            return [
                'success' => false,
                'message' => $errorMsg,
                'missing_items' => $missingItems,
                'available_items' => array_keys($existingItems)
            ];
        }
        
        error_log("[装备分配] ✅ 所有装备数据完整");
        
        $itemIds = $existingItems;
        $addedItems = [];
        
        // 🚀 性能优化：批量添加到背包（包含6把青云弟子剑）
        $inventoryData = [];
        foreach ($itemCodes as $code) {
            if (!isset($itemIds[$code])) {
                error_log("[装备分配] ⚠️ 警告: 装备 {$code} 不存在，跳过");
                continue;
            }
            
            // 根据装备代码确定物品类型
            $itemType = (strpos($code, 'sword') !== false) ? 'weapon' : 'equipment';
            $maxDurability = 100; // 统一耐久度为100
            
            // 🔧 新增：如果是青云弟子剑，添加6把到背包
            if ($code === 'newbie_sword') {
                for ($j = 1; $j <= 6; $j++) {
                    $inventoryData[] = [
                        'character_id' => $characterId,
                        'item_id' => $itemIds[$code],
                        'item_type' => $itemType,
                        'current_durability' => $maxDurability,
                        'max_durability' => $maxDurability,
                        'weapon_slot' => $j // 标记用于哪个武器槽位
                    ];
                }
                
                // 获取物品名称
                $stmt = $pdo->prepare("SELECT item_name FROM game_items WHERE id = ?");
                $stmt->execute([$itemIds[$code]]);
                $itemName = $stmt->fetchColumn();
                $addedItems[] = $itemName . ' x6'; // 显示数量
                error_log("[装备分配] 📦 准备添加: {$itemName} x6");
            } else {
                // 其他装备添加1件
                $inventoryData[] = [
                    'character_id' => $characterId,
                    'item_id' => $itemIds[$code],
                    'item_type' => $itemType,
                    'current_durability' => $maxDurability,
                    'max_durability' => $maxDurability
                ];
                
                // 获取物品名称
                $stmt = $pdo->prepare("SELECT item_name FROM game_items WHERE id = ?");
                $stmt->execute([$itemIds[$code]]);
                $itemName = $stmt->fetchColumn();
                $addedItems[] = $itemName;
                error_log("[装备分配] 📦 准备添加: {$itemName}");
            }
        }
        
        if (empty($inventoryData)) {
            $errorMsg = "没有可分配的装备数据";
            error_log("[装备分配] ❌ 错误: " . $errorMsg);
            return [
                'success' => false,
                'message' => $errorMsg
            ];
        }
        
        error_log("[装备分配] 📊 准备添加到背包的装备总数: " . count($inventoryData));
        
        // 批量插入背包
        require_once __DIR__ . '/../includes/inventory_utils.php';
        
        $stmt = $pdo->prepare("
            INSERT INTO user_inventories (
                character_id, item_id, item_type, 
                current_durability, max_durability, obtained_source, sort_weight
            ) VALUES (?, ?, ?, ?, ?, '新手装备', ?)
        ");
        
        $inventoryItemIds = []; // 记录背包物品ID
        $inventoryAddCount = 0;
        foreach ($inventoryData as $data) {
            try {
                $sortWeight = calculateSortWeight($pdo, $data['character_id'], $data['item_type']);
                $stmt->execute([
                    $data['character_id'], $data['item_id'], $data['item_type'],
                    $data['current_durability'], $data['max_durability'], $sortWeight
                ]);
                $inventoryItemIds[] = $pdo->lastInsertId();
                $inventoryAddCount++;
                error_log("[装备分配] ✅ 背包添加成功: 物品ID={$data['item_id']}, 背包ID=" . $pdo->lastInsertId());
            } catch (Exception $e) {
                error_log("[装备分配] ❌ 背包添加失败: " . $e->getMessage());
                error_log("[装备分配] 🔍 失败的装备数据: " . json_encode($data));
                throw new Exception("背包添加失败: " . $e->getMessage());
            }
        }
        
        error_log("[装备分配] 🎯 成功添加到背包，共 {$inventoryAddCount} 件装备，背包ID数组: " . json_encode($inventoryItemIds));
        
        // 🔧 修改：创建装备记录时关联背包物品ID（包含6把武器）
        $weaponInventoryIds = [];
        $equipmentInventoryIdMap = [];
        $itemIndex = 0;
        
        // 分离武器和装备的背包ID
        foreach ($inventoryData as $data) {
            $inventoryId = $inventoryItemIds[$itemIndex];
            
            if ($data['item_type'] === 'weapon') {
                // 武器按槽位索引存储
                $slotIndex = isset($data['weapon_slot']) ? $data['weapon_slot'] : 1;
                $weaponInventoryIds[$slotIndex] = $inventoryId;
                error_log("[装备分配] 🗡️ 武器槽位 {$slotIndex} 对应背包ID: {$inventoryId}");
            } else {
                // 装备按物品ID存储
                foreach ($itemCodes as $code) {
                    if (isset($itemIds[$code]) && $itemIds[$code] == $data['item_id'] && $data['item_type'] === 'equipment') {
                        $equipmentInventoryIdMap[$code] = $inventoryId;
                        error_log("[装备分配] 🛡️ 装备 {$code} 对应背包ID: {$inventoryId}");
                        break;
                    }
                }
            }
            $itemIndex++;
        }
        
        error_log("[装备分配] 📋 武器背包ID映射: " . json_encode($weaponInventoryIds));
        error_log("[装备分配] 📋 装备背包ID映射: " . json_encode($equipmentInventoryIdMap));
        
        // 🔧 修改：创建6个武器槽位，每个都装备一把青云弟子剑
        $stmt = $pdo->prepare("
            INSERT INTO character_equipment (
                character_id, item_id, inventory_item_id, slot_type, slot_index, 
                attack_order, is_active
            ) VALUES (?, ?, ?, 'weapon', ?, ?, TRUE)
        ");
        
        $weaponEquippedCount = 0;
        for ($i = 1; $i <= 6; $i++) {
            try {
                $weaponItemId = $itemIds['newbie_sword']; // 每个槽位都装备青云弟子剑
                $inventoryItemId = isset($weaponInventoryIds[$i]) ? $weaponInventoryIds[$i] : 0;
                
                error_log("[装备分配] ⚔️ 装备武器槽位 {$i}: 物品ID={$weaponItemId}, 背包ID={$inventoryItemId}");
                
                $stmt->execute([$characterId, $weaponItemId, $inventoryItemId, $i, $i]);
                $weaponEquippedCount++;
                error_log("[装备分配] ✅ 武器槽位 {$i} 装备成功");
            } catch (Exception $e) {
                error_log("[装备分配] ❌ 武器槽位 {$i} 装备失败: " . $e->getMessage());
                error_log("[装备分配] 🔍 失败的参数: characterId={$characterId}, weaponItemId={$weaponItemId}, inventoryItemId={$inventoryItemId}");
                throw new Exception("武器槽位装备失败: " . $e->getMessage());
            }
        }
        
        error_log("[装备分配] 🎯 成功装备武器到 {$weaponEquippedCount} 个槽位");
        
        // 🔧 修改：自动装备非武器物品到对应槽位
        $equipmentSlots = [
            'newbie_robe' => 'chest',
            'newbie_belt' => 'legs',
            'newbie_boots' => 'feet',
            'newbie_bracelet' => 'bracelet',
            'newbie_ring' => 'ring',
            'newbie_necklace' => 'necklace'
        ];
        
        $stmt = $pdo->prepare("
            INSERT INTO character_equipment (
                character_id, item_id, inventory_item_id, slot_type, slot_index, is_active
            ) VALUES (?, ?, ?, ?, 1, TRUE)
        ");
        
        $equipmentEquippedCount = 0;
        foreach ($equipmentSlots as $itemCode => $slotType) {
            if (isset($itemIds[$itemCode]) && isset($equipmentInventoryIdMap[$itemCode])) {
                try {
                    $inventoryItemId = $equipmentInventoryIdMap[$itemCode];
                    
                    error_log("[装备分配] 🛡️ 装备 {$slotType}: {$itemCode}, 物品ID={$itemIds[$itemCode]}, 背包ID={$inventoryItemId}");
                    
                    $stmt->execute([
                        $characterId, 
                        $itemIds[$itemCode], 
                        $inventoryItemId,
                        $slotType
                    ]);
                    $equipmentEquippedCount++;
                    error_log("[装备分配] ✅ 成功装备 {$slotType}: {$itemCode}");
                } catch (Exception $e) {
                    error_log("[装备分配] ❌ 装备槽位 {$slotType} 装备失败: " . $e->getMessage());
                    error_log("[装备分配] 🔍 失败的参数: characterId={$characterId}, itemId={$itemIds[$itemCode]}, inventoryItemId={$inventoryItemId}, slotType={$slotType}");
                    throw new Exception("装备槽位装备失败: " . $e->getMessage());
                }
            } else {
                error_log("[装备分配] ⚠️ 警告: 装备 {$itemCode} 或背包ID不存在，跳过装备");
                error_log("[装备分配] 🔍 调试信息: itemIds存在=" . (isset($itemIds[$itemCode]) ? 'true' : 'false') . ", 背包ID存在=" . (isset($equipmentInventoryIdMap[$itemCode]) ? 'true' : 'false'));
            }
        }
        
        error_log("[装备分配] 🎯 成功装备非武器装备到 {$equipmentEquippedCount} 个槽位");
        
        $successMsg = "青云套装分配完成 (12件套装：6把武器 + 6件装备)";
        error_log("[装备分配] 🎉 " . $successMsg);
        
        return [
            'success' => true,
            'message' => $successMsg,
            'items' => $addedItems,
            'stats' => [
                'weapons_equipped' => $weaponEquippedCount,
                'equipment_equipped' => $equipmentEquippedCount,
                'total_items' => count($inventoryItemIds),
                'inventory_adds' => $inventoryAddCount
            ]
        ];
        
    } catch (Exception $e) {
        $errorMsg = "新手装备分配失败: " . $e->getMessage();
        error_log("[装备分配] 💥 " . $errorMsg);
        error_log("[装备分配] 📍 错误位置: " . $e->getFile() . ":" . $e->getLine());
        error_log("[装备分配] 🔍 堆栈跟踪: " . $e->getTraceAsString());
        return [
            'success' => false,
            'message' => $errorMsg,
            'error_details' => [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]
        ];
    }
}

/**
 * 🔧 新增：初始化角色武器槽位
 */
function initializeWeaponSlots($pdo, $characterId) {
    try {
        error_log("🔧 [武器槽位] 开始为角色ID {$characterId} 初始化武器槽位");
        
        // 检查是否已有武器槽位
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count 
            FROM character_equipment 
            WHERE character_id = ? AND slot_type = 'weapon'
        ");
        $stmt->execute([$characterId]);
        $slotCount = $stmt->fetch(PDO::FETCH_ASSOC);
        
        error_log("🔍 [武器槽位] 当前武器槽位数量: {$slotCount['count']}");
        
        // 如果没有槽位，创建6个武器槽位
        if ($slotCount['count'] == 0) {
            error_log("⚠️ [武器槽位] 没有武器槽位，开始创建6个槽位");
            
            $createdCount = 0;
            for ($i = 1; $i <= 6; $i++) {
                try {
                    $stmt = $pdo->prepare("
                        INSERT INTO character_equipment (
                            character_id, item_id, slot_type, slot_index, 
                            attack_order, is_active
                        ) VALUES (?, 0, 'weapon', ?, ?, TRUE)
                    ");
                    $stmt->execute([$characterId, $i, $i]);
                    
                    if ($stmt->rowCount() > 0) {
                        $createdCount++;
                        error_log("✅ [武器槽位] 成功创建武器槽位 {$i}");
                    } else {
                        error_log("⚠️ [武器槽位] 武器槽位 {$i} 创建可能失败（rowCount=0）");
                    }
                } catch (Exception $e) {
                    error_log("❌ [武器槽位] 创建武器槽位 {$i} 失败: " . $e->getMessage());
                    throw $e;
                }
            }
            
            error_log("🎯 [武器槽位] 武器槽位创建完成，成功创建 {$createdCount} 个槽位");
            
            // 验证创建结果
            $stmt = $pdo->prepare("
                SELECT COUNT(*) as count 
                FROM character_equipment 
                WHERE character_id = ? AND slot_type = 'weapon'
            ");
            $stmt->execute([$characterId]);
            $finalCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            error_log("🔍 [武器槽位] 最终武器槽位数量: {$finalCount}");
            
            if ($finalCount < 6) {
                throw new Exception("武器槽位创建不完整，期望6个，实际{$finalCount}个");
            }
        } else {
            error_log("✅ [武器槽位] 武器槽位已存在，跳过创建");
        }
        
        return true;
        
    } catch (Exception $e) {
        error_log("💥 [武器槽位] 武器槽位初始化失败: " . $e->getMessage());
        error_log("📍 [武器槽位] 错误位置: " . $e->getFile() . ":" . $e->getLine());
        throw $e;
    }
}

/**
 * 辅助函数：判断灵根品质
 */
function determineRootQualityHelper($value) {
    if ($value >= 35) return '上品灵根';
    if ($value >= 25) return '中品灵根';
    if ($value >= 15) return '下品灵根';
    return '废灵根';
}

/**
 * 🔧 新增：修复新手装备分配失败的补救函数
 * 用于角色创建时装备分配失败时的补救措施
 */
function fixNewbieEquipmentForCharacter($pdo, $characterId) {
    try {
        error_log("🔧 [装备修复] 开始修复角色ID {$characterId} 的新手装备...");
        
        // 检查青云弟子剑数据
        $stmt = $pdo->prepare('SELECT * FROM game_items WHERE item_code = ?');
        $stmt->execute(['newbie_sword']);
        $sword = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$sword) {
            error_log("❌ [装备修复] 青云弟子剑数据不存在，无法修复");
            return [
                'success' => false,
                'message' => '青云弟子剑数据不存在，无法修复'
            ];
        }
        
        $swordId = $sword['id'];
        $fixedProblems = 0;
        error_log("✅ [装备修复] 青云弟子剑数据正常，ID: {$swordId}");
        
        // 1. 检查并修复武器槽位
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count 
            FROM character_equipment 
            WHERE character_id = ? AND slot_type = 'weapon'
        ");
        $stmt->execute([$characterId]);
        $weaponSlotCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        error_log("🗡️ [装备修复] 当前武器槽位数量: {$weaponSlotCount}");
        
        if ($weaponSlotCount < 6) {
            error_log("⚠️ [装备修复] 武器槽位不足，需要修复");
            
            // 删除现有武器槽位
            $stmt = $pdo->prepare("DELETE FROM character_equipment WHERE character_id = ? AND slot_type = 'weapon'");
            $stmt->execute([$characterId]);
            error_log("🗑️ [装备修复] 已删除现有武器槽位");
            
            // 重新创建6个武器槽位
            for ($i = 1; $i <= 6; $i++) {
                $stmt = $pdo->prepare("
                    INSERT INTO character_equipment (
                        character_id, item_id, slot_type, slot_index, 
                        attack_order, is_active
                    ) VALUES (?, 0, 'weapon', ?, ?, TRUE)
                ");
                $stmt->execute([$characterId, $i, $i]);
                error_log("➕ [装备修复] 创建武器槽位 {$i}");
            }
            $fixedProblems++;
            error_log("✅ [装备修复] 修复了武器槽位，创建了6个槽位");
        } else {
            error_log("✅ [装备修复] 武器槽位数量正常");
        }
        
        // 2. 检查并补充背包中的青云弟子剑
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count 
            FROM user_inventories ui
            JOIN game_items gi ON ui.item_id = gi.id
            WHERE ui.character_id = ? AND gi.item_code = 'newbie_sword'
        ");
        $stmt->execute([$characterId]);
        $swordCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        error_log("📦 [装备修复] 当前背包中青云弟子剑数量: {$swordCount}");
        
        if ($swordCount < 6) {
            $needToAdd = 6 - $swordCount;
            error_log("⚠️ [装备修复] 青云弟子剑不足，需要补充 {$needToAdd} 把");
            
            // 添加缺失的青云弟子剑到背包
            require_once __DIR__ . '/../includes/inventory_utils.php';
            
            for ($i = 0; $i < $needToAdd; $i++) {
                $sortWeight = calculateSortWeight($pdo, $characterId, 'weapon');
                $stmt = $pdo->prepare("
                    INSERT INTO user_inventories (
                        character_id, item_id, item_type, 
                        current_durability, max_durability, obtained_source, sort_weight
                    ) VALUES (?, ?, 'weapon', 100, 100, '装备修复', ?)
                ");
                $stmt->execute([$characterId, $swordId, $sortWeight]);
                $addedId = $pdo->lastInsertId();
                error_log("➕ [装备修复] 添加青云弟子剑到背包，背包ID: {$addedId}");
            }
            $fixedProblems++;
            error_log("✅ [装备修复] 补充了 {$needToAdd} 把青云弟子剑到背包");
        } else {
            error_log("✅ [装备修复] 背包中青云弟子剑数量正常");
        }
        
        // 3. 为所有武器槽位装备青云弟子剑
        $stmt = $pdo->prepare("
            SELECT ui.id 
            FROM user_inventories ui
            JOIN game_items gi ON ui.item_id = gi.id
            WHERE ui.character_id = ? AND gi.item_code = 'newbie_sword'
            ORDER BY ui.id
            LIMIT 6
        ");
        $stmt->execute([$characterId]);
        $inventoryIds = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        error_log("🔍 [装备修复] 找到可用的青云弟子剑背包ID: " . json_encode($inventoryIds));
        
        if (count($inventoryIds) >= 6) {
            // 更新武器槽位装备
            $stmt = $pdo->prepare("
                UPDATE character_equipment 
                SET item_id = ?, inventory_item_id = ? 
                WHERE character_id = ? AND slot_type = 'weapon' AND slot_index = ?
            ");
            
            $equippedCount = 0;
            for ($i = 1; $i <= 6; $i++) {
                $inventoryId = isset($inventoryIds[$i-1]) ? $inventoryIds[$i-1] : 0;
                $stmt->execute([$swordId, $inventoryId, $characterId, $i]);
                if ($stmt->rowCount() > 0) {
                    $equippedCount++;
                    error_log("⚔️ [装备修复] 装备青云弟子剑到武器槽位 {$i}，背包ID: {$inventoryId}");
                } else {
                    error_log("⚠️ [装备修复] 武器槽位 {$i} 装备失败");
                }
            }
            
            if ($equippedCount > 0) {
                $fixedProblems++;
                error_log("✅ [装备修复] 装备了 {$equippedCount} 把青云弟子剑到武器槽位");
            }
        } else {
            error_log("❌ [装备修复] 背包中青云弟子剑数量不足，无法装备到武器槽位");
        }
        
        // 4. 检查并修复其他装备槽位
        $equipmentSlots = [
            'newbie_robe' => 'chest',
            'newbie_belt' => 'legs', 
            'newbie_boots' => 'feet',
            'newbie_bracelet' => 'bracelet',
            'newbie_ring' => 'ring',
            'newbie_necklace' => 'necklace'
        ];
        
        $fixedEquipmentCount = 0;
        foreach ($equipmentSlots as $itemCode => $slotType) {
            error_log("🔍 [装备修复] 检查装备槽位: {$slotType} ({$itemCode})");
            
            // 检查装备槽位是否存在
            $stmt = $pdo->prepare("
                SELECT COUNT(*) as count 
                FROM character_equipment 
                WHERE character_id = ? AND slot_type = ?
            ");
            $stmt->execute([$characterId, $slotType]);
            $slotExists = $stmt->fetch(PDO::FETCH_ASSOC)['count'] > 0;
            
            if (!$slotExists) {
                error_log("⚠️ [装备修复] {$slotType} 槽位不存在，需要修复");
                
                // 获取装备ID
                $stmt = $pdo->prepare('SELECT id FROM game_items WHERE item_code = ?');
                $stmt->execute([$itemCode]);
                $itemId = $stmt->fetchColumn();
                
                if ($itemId) {
                    // 检查背包中是否有该装备
                    $stmt = $pdo->prepare("
                        SELECT ui.id 
                        FROM user_inventories ui
                        WHERE ui.character_id = ? AND ui.item_id = ?
                        LIMIT 1
                    ");
                    $stmt->execute([$characterId, $itemId]);
                    $inventoryId = $stmt->fetchColumn();
                    
                    if (!$inventoryId) {
                        // 添加到背包
                        require_once __DIR__ . '/../includes/inventory_utils.php';
                        $sortWeight = calculateSortWeight($pdo, $characterId, 'equipment');
                        $stmt = $pdo->prepare("
                            INSERT INTO user_inventories (
                                character_id, item_id, item_type, 
                                current_durability, max_durability, obtained_source, sort_weight
                            ) VALUES (?, ?, 'equipment', 100, 100, '装备修复', ?)
                        ");
                        $stmt->execute([$characterId, $itemId, $sortWeight]);
                        $inventoryId = $pdo->lastInsertId();
                        error_log("➕ [装备修复] 添加 {$itemCode} 到背包，背包ID: {$inventoryId}");
                    } else {
                        error_log("✅ [装备修复] {$itemCode} 已在背包中，背包ID: {$inventoryId}");
                    }
                    
                    // 装备到对应槽位
                    $stmt = $pdo->prepare("
                        INSERT INTO character_equipment (
                            character_id, item_id, inventory_item_id, slot_type, slot_index, is_active
                        ) VALUES (?, ?, ?, ?, 1, TRUE)
                    ");
                    $stmt->execute([$characterId, $itemId, $inventoryId, $slotType]);
                    $fixedEquipmentCount++;
                    error_log("✅ [装备修复] 修复了 {$slotType} 装备槽位，装备ID: {$itemId}，背包ID: {$inventoryId}");
                } else {
                    error_log("❌ [装备修复] 找不到装备 {$itemCode} 的数据");
                }
            } else {
                error_log("✅ [装备修复] {$slotType} 槽位已存在");
            }
        }
        
        if ($fixedEquipmentCount > 0) {
            $fixedProblems += $fixedEquipmentCount;
        }
        
        error_log("🎉 [装备修复] 修复完成，共修复了 {$fixedProblems} 个问题");
        
        return [
            'success' => true,
            'message' => "装备修复完成，共修复了 {$fixedProblems} 个问题",
            'fixed_count' => $fixedProblems
        ];
        
    } catch (Exception $e) {
        error_log("💥 [装备修复] 装备修复失败: " . $e->getMessage());
        error_log("📍 [装备修复] 错误位置: " . $e->getFile() . ":" . $e->getLine());
        return [
            'success' => false,
            'message' => '装备修复失败: ' . $e->getMessage()
        ];
    }
}
?> 