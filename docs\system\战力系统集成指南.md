# 🔥 战力系统集成指南

*版本: v2.0 简化版*  
*创建时间: 2025年6月17日*

## 🚀 快速集成步骤

### 1. 引入文件

在需要使用战力系统的页面头部添加：

```html
<!-- 引入战力系统样式 -->
<link rel="stylesheet" href="assets/css/power-rating.css">

<!-- 引入战力系统脚本 -->
<script src="assets/js/power-rating.js"></script>
```

### 2. 在属性页面显示角色战力

在 `public/attributes.html` 中添加：

```html
<!-- 在角色属性显示区域添加 -->
<div class="attribute-item">
    <div class="attribute-label">角色战力：</div>
    <div id="character-power-rating" class="power-rating-display">计算中...</div>
</div>

<script>
// 页面加载后自动显示战力
document.addEventListener('DOMContentLoaded', function() {
    PowerRating.displayCharacterPower('character-power-rating');
});
</script>
```

### 3. 在装备详情弹窗显示装备战力

在装备详情弹窗中添加：

```html
<!-- 在装备详情信息中添加 -->
<div class="equipment-info-item">
    <div id="equipment-power-rating" class="power-rating-display">计算中...</div>
</div>

<script>
// 显示装备详情时调用
function showEquipmentDetail(equipmentId) {
    // 其他显示逻辑...
    
    // 显示装备战力
    PowerRating.displayEquipmentPower(equipmentId, 'equipment-power-rating');
}
</script>
```

### 4. 在背包页面标记更好装备

在背包页面中添加：

```html
<script>
// 打开背包时标记更好的装备
function openInventory() {
    // 背包显示逻辑...
    
    // 标记所有更好的装备
    setTimeout(() => {
        PowerRating.markAllBetterEquipment();
    }, 500); // 等待背包内容加载完成
}

// 或者分别标记不同槽位
function openWeaponTab() {
    PowerRating.markBetterEquipment('weapon');
}

function openArmorTab() {
    PowerRating.markBetterEquipment('chest');
    PowerRating.markBetterEquipment('legs');
    PowerRating.markBetterEquipment('feet');
}

function openAccessoryTab() {
    PowerRating.markBetterEquipment('ring');
    PowerRating.markBetterEquipment('necklace');
    PowerRating.markBetterEquipment('bracelet');
}
</script>
```

### 5. 在战斗页面显示威胁等级

在 `public/battle.html` 中添加：

```html
<!-- 在怪物信息显示区域添加 -->
<div class="monster-info">
    <div class="monster-name">怪物名称</div>
    <div id="monster-threat-level" class="threat-level-display">评估中...</div>
</div>

<script>
// 开始战斗时显示威胁等级
function startBattle(monsterId) {
    // 战斗初始化逻辑...
    
    // 显示威胁等级
    PowerRating.displayMonsterThreat(monsterId, 'monster-threat-level');
}
</script>
```

## 🎯 高级用法

### 监听事件自动更新

```javascript
// 装备变化时自动更新战力
function equipItem(itemId, slot) {
    // 装备逻辑...
    
    // 触发装备变化事件
    document.dispatchEvent(new CustomEvent('equipmentChanged', {
        detail: { itemId, slot }
    }));
}

// 境界突破时自动更新战力
function breakthrough() {
    // 突破逻辑...
    
    // 触发境界突破事件
    document.dispatchEvent(new CustomEvent('realmBreakthrough'));
}
```

### 手动刷新战力

```javascript
// 手动刷新角色战力
PowerRating.refreshCharacterPower();

// 清除所有升级箭头
PowerRating.clearAllUpgradeArrows();

// 重新标记更好装备
PowerRating.markAllBetterEquipment();
```

### 自定义显示容器

```javascript
// 在自定义容器中显示战力
PowerRating.displayCharacterPower('my-custom-power-container');

// 显示特定装备的战力
PowerRating.displayEquipmentPower(12345, 'equipment-power-container');
```

## 🛠️ 数据要求

### 装备物品元素要求

背包中的装备物品元素需要有 `data-item-id` 属性：

```html
<div class="inventory-item" data-item-id="12345">
    <!-- 装备内容 -->
</div>
```

### 数据库字段要求

确保以下字段存在于对应表中：

**characters 表:**
- `realm_level` (境界等级)
- `physical_attack`, `immortal_attack` (攻击力)
- `physical_defense`, `immortal_defense` (防御力)
- `hp`, `speed_bonus` (生命值和速度)

**game_items/item_attributes 表:**
- 所有装备属性字段
- `equipment_slot` (装备槽位)

**monsters 表:**
- 同角色的战斗属性字段

## 🎨 样式自定义

### 修改战力显示颜色

```css
.power-rating-display {
    color: #your-color !important;
    background: rgba(your-color, 0.1) !important;
}
```

### 修改升级箭头样式

```css
.power-upgrade-arrow {
    background: #your-color !important;
    width: 25px !important;
    height: 25px !important;
}
```

### 移动端适配

```css
@media (max-width: 768px) {
    .power-rating-display {
        font-size: 12px;
    }
}
```

## 🚨 注意事项

1. **性能考虑**: 战力计算是实时的，频繁调用可能影响性能
2. **数据完整性**: 确保数据库字段完整，避免计算错误
3. **权限验证**: API会检查用户登录状态，确保session有效
4. **错误处理**: 前端会在控制台输出错误信息，便于调试
5. **兼容性**: 使用ES6语法，需要现代浏览器支持

## 📋 测试检查清单

- [ ] 角色战力显示正常
- [ ] 装备战力显示正常
- [ ] 背包升级箭头显示正常
- [ ] 威胁等级评估正常
- [ ] 移动端显示适配
- [ ] 事件监听自动更新
- [ ] API接口响应正常
- [ ] 样式显示美观

---

*战力系统集成完成后，用户可以在各个页面直观地看到"战力值：xxx"和绿色升级箭头提示。* 