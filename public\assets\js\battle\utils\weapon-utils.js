/**
 * 武器系统工具类
 * 提供武器相关的处理和管理方法
 */
class BattleWeaponUtils {
    
    /**
     * 处理武器耐久损耗
     * @param {Array} skillSequence 技能序列
     * @param {string} gameResult 战斗结果
     */
    static async handleWeaponDurabilityLoss(skillSequence, gameResult) {
        try {
            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.log('weapon-utils', '=== 处理武器耐久损耗 ===');
            }
            
            // 获取当前装备的武器
            const equippedWeapons = skillSequence.filter(skill => 
                skill && 
                skill.hasWeapon && 
                skill.weaponId && 
                typeof skill.weaponId === 'number' && 
                skill.weaponId > 0 &&
                !skill.isDurabilityZero // 🔧 只排除耐久度为0的武器
            );
            
            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.log('weapon-utils', '技能序列:', skillSequence);
                window.BattleDebugConfig.log('weapon-utils', '筛选出的武器:', equippedWeapons);
            }
            
            if (!equippedWeapons.length) {
                if (window.BattleDebugConfig) {
                    window.BattleDebugConfig.log('weapon-utils', '没有有效装备武器，跳过耐久损耗处理');
                }
                return;
            }
            
            // 🔧 新增：预先验证所有武器ID的有效性
            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.log('weapon-utils', '=== 预验证武器ID有效性 ===');
            }
            const validWeapons = [];
            for (const weapon of equippedWeapons) {
                const weaponId = weapon.weaponId;
                const weaponName = weapon.weaponName || '未知武器';
                const currentDurability = parseInt(weapon.durability) || 0;
                
                if (window.BattleDebugConfig) {
                    window.BattleDebugConfig.log('weapon-utils', `🔍 检查武器: ${weaponName} (ID: ${weaponId}) 耐久度: ${currentDurability}`);
                }
                
                // 检查武器ID是否有效
                if (!weaponId || weaponId <= 0) {
                    if (window.BattleDebugConfig) {
                        window.BattleDebugConfig.warn('weapon-utils', `⚠️ 跳过无效武器ID: ${weaponId} (${weaponName})`);
                    }
                    continue;
                }
                
                // 检查耐久度是否已经为0
                if (currentDurability <= 0) {
                    if (window.BattleDebugConfig) {
                        window.BattleDebugConfig.warn('weapon-utils', `⚠️ 跳过耐久度为0的武器: ${weaponName} (ID: ${weaponId})`);
                    }
                    continue;
                }
                
                validWeapons.push(weapon);
                if (window.BattleDebugConfig) {
                    window.BattleDebugConfig.log('weapon-utils', `✅ 武器验证通过: ${weaponName} (ID: ${weaponId})`);
                }
            }
            
            if (!validWeapons.length) {
                if (window.BattleDebugConfig) {
                    window.BattleDebugConfig.log('weapon-utils', '🔧 预验证后没有有效武器，跳过耐久损耗处理');
                }
                return;
            }
            
            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.log('weapon-utils', `🔧 预验证通过的武器数量: ${validWeapons.length}/${equippedWeapons.length}`);
            }
            
            // 为每个有效的武器处理耐久度
            for (const weapon of validWeapons) {
                try {
                    // 构造更新数据
                    const weaponUpdate = {
                        inventory_id: weapon.weaponId,
                        durability_change: -1 // 每场战斗默认消耗1点耐久度
                    };
                    
                    if (window.BattleDebugConfig) {
                        window.BattleDebugConfig.log('weapon-utils', '准备更新武器耐久度:', weaponUpdate);
                    }
                    
                    // 调用API更新耐久度
                    const apiUrl = window.GameConfig ? window.GameConfig.getApiUrl('equipment_integrated.php') : '../../../src/api/equipment_integrated.php';
                    const response = await fetch(`${apiUrl}?action=update_weapon_durability`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: `weapon_updates=${encodeURIComponent(JSON.stringify([weaponUpdate]))}`
                    });
                    
                    if (!response.ok) {
                        if (window.BattleDebugConfig) {
                            window.BattleDebugConfig.warn('weapon-utils', `⚠️ 武器${weapon.weaponName}耐久度更新HTTP失败: ${response.status}`);
                        }
                        continue; // 🔧 修复：使用continue代替throw，允许其他武器继续更新
                    }
                    
                    const result = await response.json();
                    
                    if (!result.success) {
                        if (window.BattleDebugConfig) {
                            window.BattleDebugConfig.warn('weapon-utils', `⚠️ 武器${weapon.weaponName}耐久度更新API失败: ${result.message || '未知错误'}`);
                        }
                        continue; // 🔧 修复：使用continue代替throw，允许其他武器继续更新
                    }
                    
                    if (window.BattleDebugConfig) {
                        window.BattleDebugConfig.log('weapon-utils', `✅ 武器${weapon.weaponName}耐久度更新成功`);
                    }
                    
                    // 🔥 新增：清除武器数据缓存，确保下次加载获取最新耐久度
                    if (window.battleSystem && window.battleSystem.dataManager && typeof window.battleSystem.dataManager.clearWeaponCache === 'function') {
                        window.battleSystem.dataManager.clearWeaponCache();
                        if (window.BattleDebugConfig) {
                            window.BattleDebugConfig.log('weapon-utils', '🔄 已清除武器缓存，下次加载将获取最新耐久度');
                        }
                    }
                    
                } catch (error) {
                    if (window.BattleDebugConfig) {
                        window.BattleDebugConfig.warn('weapon-utils', `⚠️ 更新武器${weapon.weaponName}耐久度时发生错误:`, error.message);
                        // 🔧 修复：不再重新抛出错误，允许其他武器继续更新
                    }
                }
            }
        } catch (error) {
            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.error('weapon-utils', '处理武器耐久损耗时发生错误:', error);
                // 🔧 修复：耐久度更新失败不应该影响战斗流程
            }
        }
    }

    /**
     * 检查武器是否有效
     * @param {Object} weaponData 武器数据
     * @returns {boolean} 是否有效
     */
    static isValidWeapon(weaponData) {
        if (!weaponData) return false;
        
        return !!(
            weaponData.weaponId &&
            typeof weaponData.weaponId === 'number' &&
            weaponData.weaponId > 0 &&
            weaponData.hasWeapon &&
            !weaponData.isDurabilityZero
        );
    }

    /**
     * 计算武器耐久度百分比
     * @param {number} currentDurability 当前耐久度
     * @param {number} maxDurability 最大耐久度
     * @returns {number} 耐久度百分比
     */
    static calculateDurabilityPercentage(currentDurability, maxDurability) {
        if (!maxDurability || maxDurability <= 0) return 0;
        return Math.max(0, Math.min(100, Math.round((currentDurability / maxDurability) * 100)));
    }

    /**
     * 获取武器耐久度状态
     * @param {number} durabilityPercentage 耐久度百分比
     * @returns {string} 状态（healthy, warning, critical, broken）
     */
    static getDurabilityStatus(durabilityPercentage) {
        if (durabilityPercentage <= 0) return 'broken';
        if (durabilityPercentage <= 20) return 'critical';
        if (durabilityPercentage <= 50) return 'warning';
        return 'healthy';
    }

    /**
     * 获取武器耐久度颜色
     * @param {number} durabilityPercentage 耐久度百分比
     * @returns {string} 颜色代码
     */
    static getDurabilityColor(durabilityPercentage) {
        const status = this.getDurabilityStatus(durabilityPercentage);
        
        const colors = {
            'healthy': '#4CAF50',   // 绿色
            'warning': '#FF9800',   // 橙色
            'critical': '#F44336',  // 红色
            'broken': '#9E9E9E'     // 灰色
        };
        
        return colors[status] || colors.healthy;
    }

    /**
     * 格式化武器信息显示
     * @param {Object} weaponData 武器数据
     * @returns {Object} 格式化的显示信息
     */
    static formatWeaponDisplay(weaponData) {
        if (!weaponData || !weaponData.hasWeapon) {
            return {
                name: '空槽位',
                skillName: '剑气外放！',
                durabilityText: '无装备',
                durabilityPercent: 0,
                status: 'empty',
                cssClass: 'weapon-item default'
            };
        }

        const currentDurability = parseInt(weaponData.durability) || 0;
        const maxDurability = parseInt(weaponData.maxDurability) || 100;
        const durabilityPercent = this.calculateDurabilityPercentage(currentDurability, maxDurability);
        const status = this.getDurabilityStatus(durabilityPercent);

        let cssClass = 'weapon-item equipped';
        if (status === 'broken') {
            cssClass += ' broken-weapon';
        } else if (status === 'critical') {
            cssClass += ' low-durability';
        }

        return {
            name: weaponData.weaponName || '未知武器',
            skillName: weaponData.skillName || '技能',
            durabilityText: status === 'broken' ? '已损坏' : `${currentDurability}/${maxDurability}`,
            durabilityPercent: durabilityPercent,
            status: status,
            cssClass: cssClass
        };
    }

    /**
     * 获取武器类型对应的技能类型
     * @param {string} weaponType 武器类型
     * @returns {string} 技能类型
     */
    static getSkillTypeByWeaponType(weaponType) {
        const weaponSkillMap = {
            'sword': 'physical',    // 剑类武器 - 物理技能
            'fan': 'magic',         // 扇类武器 - 法术技能
            'bow': 'ranged',        // 弓类武器 - 远程技能
            'staff': 'magic'        // 法杖 - 法术技能
        };
        
        return weaponSkillMap[weaponType] || 'physical';
    }

    /**
     * 检查武器是否需要修复
     * @param {Object} weaponData 武器数据
     * @returns {boolean} 是否需要修复
     */
    static needsRepair(weaponData) {
        if (!weaponData || !weaponData.hasWeapon) return false;
        
        const currentDurability = parseInt(weaponData.durability) || 0;
        const maxDurability = parseInt(weaponData.maxDurability) || 100;
        
        return currentDurability < maxDurability;
    }

    /**
     * 计算武器修复费用
     * @param {Object} weaponData 武器数据
     * @returns {number} 修复费用
     */
    static calculateRepairCost(weaponData) {
        if (!weaponData || !this.needsRepair(weaponData)) return 0;
        
        const currentDurability = parseInt(weaponData.durability) || 0;
        const maxDurability = parseInt(weaponData.maxDurability) || 100;
        const durabilityLoss = maxDurability - currentDurability;
        
        // 修复费用：每点耐久度100金币
        const repairCost = durabilityLoss * 100;
        
        // 确保至少1金币
        return Math.max(1, repairCost);
    }

    /**
     * 生成武器状态报告
     * @param {Array} skillSequence 技能序列
     * @returns {Object} 武器状态报告
     */
    static generateWeaponStatusReport(skillSequence) {
        const report = {
            total: skillSequence.length,
            equipped: 0,
            healthy: 0,
            warning: 0,
            critical: 0,
            broken: 0,
            needRepair: 0,
            totalRepairCost: 0
        };

        skillSequence.forEach(weapon => {
            if (weapon && weapon.hasWeapon) {
                report.equipped++;
                
                const currentDurability = parseInt(weapon.durability) || 0;
                const maxDurability = parseInt(weapon.maxDurability) || 100;
                const durabilityPercent = this.calculateDurabilityPercentage(currentDurability, maxDurability);
                const status = this.getDurabilityStatus(durabilityPercent);
                
                report[status]++;
                
                if (this.needsRepair(weapon)) {
                    report.needRepair++;
                    report.totalRepairCost += this.calculateRepairCost(weapon);
                }
            }
        });

        return report;
    }
}

// 全局导出工具类
window.BattleWeaponUtils = BattleWeaponUtils;

if (window.BattleDebugConfig) {
    window.BattleDebugConfig.log('weapon-utils', '⚔️ 武器系统工具模块已加载');
} else {
    console.log('⚔️ 武器系统工具模块已加载');
} 