/* 装备系统集成页面脚本文件 */
/* 创建时间: 2024年12月19日 */
/* 用途: 装备和背包管理页面的所有JavaScript功能 */

/* 这里将包含所有从HTML文件中分离出来的JavaScript代码 */
let currentUser = null;
let inventoryItems = [];
let equippedItems = [];
let weaponSlots = [];
let selectedItem = null;
let currentTab = 'all';

// 头像选择相关变量
let availableAvatars = { free: [], vip: [] };
let selectedAvatar = null;
let currentAvatar = null;

// 调试日志函数
function debugLog(message) {
    console.log(message);
}

// 🔧 初始化物品详情弹窗组件的回调函数
function initItemDetailPopupCallbacks() {
    ItemDetailPopup.setCallbacks({
        onEquip: async item => {
            console.log('装备物品:', item);
            if (['weapon', 'sword', 'fan'].includes(item.slot_type)) {
                await equipWeaponFromPopup(item);
            } else {
                await equipItemFromPopup(item);
            }
        },
        onUnequip: async item => {
            console.log('卸下物品:', item);
            if (['weapon', 'sword', 'fan'].includes(item.slot_type)) {
                await unequipWeaponFromPopup(item);
            } else {
                await unequipItemFromPopup(item);
            }
        },
        onUse: async item => {
            console.log('使用物品:', item);
            await useItemFromPopup(item);
        },
        onRecycle: async (item, price, quantity) => {
            console.log('回收物品:', item, '价格:', price, '数量:', quantity);
            await recycleItemFromPopup(item, price, quantity);
        },
        onRepair: async item => {
            console.log('修复物品:', item);
            await repairWeaponFromPopup(item);
        },
    });
}

// 页面加载
document.addEventListener('DOMContentLoaded', async function () {
    console.log('装备页面开始加载...');

    // 🎵 全局音乐管理器会自动处理音乐播放

    // 装备页面加载完成

    try {
        // 🔧 初始化物品详情弹窗组件
        initItemDetailPopupCallbacks();

        // 🔧 修改：不再需要加载角色装备组件，直接加载数据
        console.log('开始加载用户和装备数据...');
        await loadUserData();
        await loadEquipmentData();
        console.log('✅ 所有数据加载完成');

        // 初始化套装系统
        if (window.equipmentSetManager) {
            console.log('🎯 初始化套装系统...');
            try {
                await window.equipmentSetManager.init();
                console.log('✅ 套装系统初始化完成');
            } catch (error) {
                console.error('❌ 套装系统初始化失败:', error);
            }
        }
    } catch (error) {
        console.error('页面加载失败:', error);
        showMessage('页面加载失败: ' + error.message, 'error');
    }

    // 添加触摸反馈
    addTouchFeedback();
});

// 添加触摸反馈效果
function addTouchFeedback() {
    document
        .querySelectorAll('.btn, .tab, .inventory-item, .equipment-slot, .weapon-slot')
        .forEach(element => {
            element.addEventListener(
                'touchstart',
                function () {
                    this.style.transform = 'scale(0.95)';
                },
                { passive: true }
            );

            element.addEventListener(
                'touchend',
                function () {
                    this.style.transform = '';
                },
                { passive: true }
            );
        });
}

// 加载用户数据
async function loadUserData() {
    try {
        console.log('开始加载用户数据...');

        // 🔧 修复：优先尝试cultivation.php API，fallback到简化版API
        let userData;

        try {
            // 首先尝试使用cultivation.php API
            console.log('🔄 尝试使用cultivation.php API...');
            const cultivationUrl = window.GameConfig
                ? window.GameConfig.getApiUrl('cultivation.php')
                : '/yinian/src/api/cultivation.php';

            const cultivationResponse = await fetch(cultivationUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=get_user_data',
            });
            const cultivationText = await cultivationResponse.text();

            console.log('🔍 cultivation API响应状态:', cultivationResponse.status);
            console.log('🔍 cultivation API响应前100字符:', cultivationText.substring(0, 100));

            const cultivationData = JSON.parse(cultivationText);

            if (cultivationData.success) {
                console.log('✅ cultivation.php API调用成功');
                // 转换数据格式以兼容装备页面
                userData = {
                    success: true,
                    user: {
                        username: cultivationData.username,
                        character_name: cultivationData.character_name,
                        character_avatar:
                            cultivationData.character_avatar || cultivationData.avatar_image,
                        avatar_image:
                            cultivationData.character_avatar || cultivationData.avatar_image,
                        avatar_frame: cultivationData.avatar_frame,
                        character_id: cultivationData.character_id,

                        // 🔧 修复：从attributes中获取属性 - 使用正确的字段名
                        physical_attack: cultivationData.attributes?.physical_attack || 100,
                        immortal_attack: cultivationData.attributes?.immortal_attack || 80,
                        physical_defense: cultivationData.attributes?.physical_defense || 50,
                        immortal_defense: cultivationData.attributes?.immortal_defense || 40,
                        hp_bonus: cultivationData.attributes?.hp_bonus || 1000, // 修正：max_hp -> hp_bonus
                        mp_bonus: cultivationData.attributes?.mp_bonus || 500, // 修正：max_mp -> mp_bonus
                        speed_bonus: cultivationData.attributes?.speed_bonus || 60, // 修正：speed -> speed_bonus
                        critical_bonus: cultivationData.attributes?.critical_bonus || 0.1,
                        accuracy_bonus: cultivationData.attributes?.accuracy_bonus || 85.0,
                        dodge_bonus: cultivationData.attributes?.dodge_bonus || 10.0,

                        // 基础属性
                        physique: cultivationData.attributes?.physique || 10,
                        constitution: cultivationData.attributes?.constitution || 10,
                        comprehension: cultivationData.attributes?.comprehension || 10,
                        spirit: cultivationData.attributes?.spirit || 10,
                        agility: cultivationData.attributes?.agility || 10,

                        // 境界信息
                        realm_name: cultivationData.attributes?.current_realm || '练气期',
                        realm_level: cultivationData.attributes?.realm_level || 1,

                        // 资源
                        gold: cultivationData.attributes?.gold || 0,
                        spirit_stones: cultivationData.attributes?.spirit_stones || 0,
                        spiritual_power: cultivationData.attributes?.spiritual_power || 0,
                        silver: cultivationData.attributes?.silver || 0,

                        // 背包容量
                        inventory_slots: cultivationData.attributes?.inventory_slots || 30,
                    },
                };
            } else {
                throw new Error(cultivationData.message || 'cultivation.php API返回失败');
            }
        } catch (cultivationError) {
            console.log('❌ cultivation.php API失败:', cultivationError.message);
            console.log('🔄 尝试使用简化版API...');

            // fallback到简化版API
            const simpleUrl = window.GameConfig
                ? window.GameConfig.getApiUrl('simple_user_stats.php')
                : '/yinian/src/api/simple_user_stats.php';

            const simpleResponse = await fetch(simpleUrl);
            const simpleText = await simpleResponse.text();

            console.log('🔍 简化API响应状态:', simpleResponse.status);
            console.log('🔍 简化API响应前100字符:', simpleText.substring(0, 100));

            userData = JSON.parse(simpleText);

            if (!userData.success) {
                throw new Error(userData.message || '简化API也失败了');
            }

            console.log('✅ 简化API调用成功');
        }

        console.log('🎯 [装备] 用户API返回数据:', userData);

        if (userData.success) {
            currentUser = userData.user;
            console.log('🎯 [装备] 设置currentUser:', currentUser);
            console.log('🎯 [装备] 头像字段检查:', {
                avatar_image: currentUser.avatar_image,
                character_avatar: currentUser.character_avatar,
            });

            // 🔧 修复：设置修炼境界信息
            currentUser.cultivation_realm = {
                realm_name: currentUser.realm_name || '练气期',
                level: currentUser.realm_level || 1,
            };

            console.log('用户数据加载成功:', currentUser);

            // 🔧 修复：调用updateUserDisplay来显示人物形象
            updateUserDisplay(currentUser, userData.equipment_stats || {});

            // 🔥 新增：加载角色战力显示
            await loadCharacterPowerRating();
        } else {
            console.error('用户数据加载失败:', userData.message);
            showMessage(userData.message || '用户数据加载失败', 'error');
        }
    } catch (error) {
        console.error('加载用户数据失败:', error);
        showMessage('加载用户数据失败: ' + error.message, 'error');

        // 设置默认用户数据以防止页面完全崩溃
        currentUser = {
            username: '游客',
            character_name: '游客',
            character_avatar: 'ck.png',
            cultivation_realm: {
                realm_name: '开光期一阶',
                level: 1,
            },
        };
    }
}

// 加载装备数据
async function loadEquipmentData() {
    try {
        console.log('开始加载装备数据...');

        const [equipmentResponse, weaponResponse] = await Promise.all([
            fetch(
                window.GameConfig
                    ? window.GameConfig.getApiUrl(
                          'equipment_integrated.php?action=get_all_equipment'
                      )
                    : '/yinian/src/api/equipment_integrated.php?action=get_all_equipment'
            ),
            fetch(
                window.GameConfig
                    ? window.GameConfig.getApiUrl(
                          'equipment_integrated.php?action=get_weapon_slots'
                      )
                    : '/yinian/src/api/equipment_integrated.php?action=get_weapon_slots'
            ),
        ]);

        const equipmentData = await equipmentResponse.json();
        const weaponData = await weaponResponse.json();

        console.log('API响应数据:');
        console.log('- 装备数据:', equipmentData);
        console.log('- 武器数据:', weaponData);

        if (equipmentData.success) {
            inventoryItems = equipmentData.inventory || [];
            equippedItems = equipmentData.equipped || [];

            // 处理用户数据
            if (equipmentData.user && !currentUser) {
                currentUser = equipmentData.user;
            } else if (equipmentData.user && currentUser) {
                // 合并用户数据
                Object.assign(currentUser, equipmentData.user);
            }

            console.log('设置背包物品:', inventoryItems?.length || 0, '个');
            console.log('设置已装备物品:', equippedItems?.length || 0, '个');
            console.log('已装备物品详情:', equippedItems);
            console.log('用户背包容量:', currentUser?.inventory_slots);
        } else {
            showMessage(equipmentData.message || '装备数据加载失败', 'error');
        }

        if (weaponData.success) {
            weaponSlots = weaponData.weapon_slots || [];
            console.log('设置武器槽位:', weaponSlots?.length || 0, '个');
            console.log(
                '武器槽位详情:',
                weaponSlots?.map(slot => ({
                    slot_index: slot.slot_index,
                    inventory_item_id: slot.inventory_item_id,
                    name: slot.name,
                }))
            );
        } else {
            showMessage(weaponData.message || '武器数据加载失败', 'error');
        }

        // 确保数据都设置好后再渲染
        console.log('开始渲染界面...');
        renderAllUI();
        console.log('界面渲染完成');
    } catch (error) {
        console.error('数据加载错误:', error);
        showMessage('加载装备数据失败: ' + error.message, 'error');
    }
}

// 更新用户显示
function updateUserDisplay(user, equipmentStats) {
    console.log('=== updateUserDisplay 调试信息 ===');
    console.log('用户数据:', user);
    console.log('用户数据类型:', typeof user);
    console.log('用户数据键:', user ? Object.keys(user) : 'null');
    console.log('装备属性:', equipmentStats);

    // 更新角色头像和名称
    const mainAvatar = document.getElementById('main-avatar');

    console.log('DOM元素检查:');
    console.log('- mainAvatar元素:', !!mainAvatar);

    // 优先使用角色名，如果角色名不同于用户名则使用角色名，否则使用用户名
    const displayName =
        user.character_name && user.character_name !== user.username
            ? user.character_name
            : user.username;

    console.log('显示名称:', displayName);
    console.log('角色头像数据:', user.character_avatar);

    if (mainAvatar) {
        // 添加点击事件和样式
        mainAvatar.onclick = openAvatarSelector;
        mainAvatar.style.cursor = 'pointer';
        mainAvatar.title = '点击更换形象';

        // 🔧 修复：优先使用avatar_image，fallback到character_avatar
        let avatarFile = user.avatar_image || user.character_avatar;
        if (
            !avatarFile ||
            avatarFile === 0 ||
            avatarFile === '0' ||
            avatarFile === 'null' ||
            avatarFile === 'undefined'
        ) {
            avatarFile = null; // 设为null以便后续使用默认头像
        }
        console.log('🎯 获取到的头像文件:', avatarFile);
        console.log('🎯 头像字段详情:', {
            avatar_image: user.avatar_image,
            character_avatar: user.character_avatar,
            final_avatar: avatarFile,
        });

        if (
            avatarFile &&
            avatarFile !== 'assets/images/avatars/default_male.png' &&
            avatarFile !== 'default_male.png' &&
            avatarFile !== ''
        ) {
            console.log('🎯 开始设置角色头像:', avatarFile);

            // 🔧 优化：智能路径构建，优先使用最可能成功的路径
            let primaryPath = avatarFile;
            if (
                !primaryPath.startsWith('assets/') &&
                !primaryPath.startsWith('/') &&
                !primaryPath.startsWith('http')
            ) {
                primaryPath = `assets/images/char/${avatarFile}`;
            }

            console.log('🎯 主要头像路径:', primaryPath);

            // 🔧 增强：预定义所有可能的路径，按成功概率排序
            const allPossiblePaths = [
                primaryPath,
                `assets/images/char/${avatarFile}`,
                `./assets/images/char/${avatarFile}`,
                `../assets/images/char/${avatarFile}`,
                `/assets/images/char/${avatarFile}`,
                `public/assets/images/char/${avatarFile}`,
                `assets/images/avatars/${avatarFile}`,
                `assets/images/${avatarFile}`,
                avatarFile,
            ].filter((path, index, arr) => arr.indexOf(path) === index); // 去重

            console.log('🔄 将尝试的所有路径:', allPossiblePaths);

            let pathIndex = 0;
            let loadSuccess = false;

            function tryLoadAvatar() {
                if (pathIndex >= allPossiblePaths.length) {
                    console.log('❌ 所有头像路径都失败，使用默认显示');
                    useDefaultAvatar();
                    return;
                }

                const currentPath = allPossiblePaths[pathIndex];
                console.log(
                    `🔄 尝试路径 ${pathIndex + 1}/${allPossiblePaths.length}: ${currentPath}`
                );

                const testImg = new Image();
                testImg.onload = function () {
                    if (!loadSuccess) {
                        // 防止重复设置
                        loadSuccess = true;
                        console.log('✅ 头像加载成功:', currentPath);
                        console.log('🎯 图片尺寸:', testImg.width, 'x', testImg.height);

                        // 设置头像
                        mainAvatar.style.backgroundImage = `url('${currentPath}')`;
                        mainAvatar.style.backgroundSize = 'cover';
                        mainAvatar.style.backgroundPosition = 'center';
                        mainAvatar.style.backgroundRepeat = 'no-repeat';
                        mainAvatar.textContent = '';

                        console.log('✅ 头像设置完成');
                    }
                };

                testImg.onerror = function (error) {
                    console.log(
                        `❌ 路径失败 ${pathIndex + 1}/${allPossiblePaths.length}: ${currentPath}`
                    );
                    pathIndex++;
                    tryLoadAvatar(); // 尝试下一个路径
                };

                // 设置超时，避免长时间等待
                setTimeout(() => {
                    if (!loadSuccess && pathIndex < allPossiblePaths.length) {
                        console.log(
                            `⏰ 路径超时 ${pathIndex + 1}/${
                                allPossiblePaths.length
                            }: ${currentPath}`
                        );
                        pathIndex++;
                        tryLoadAvatar();
                    }
                }, 3000); // 3秒超时

                testImg.src = currentPath;
            }

            // 开始尝试加载
            tryLoadAvatar();
        } else {
            console.log('🔄 使用默认头像显示，原因：头像文件为空或是默认值');
            console.log('🎯 头像文件检查结果:', {
                avatarFile: avatarFile,
                isEmpty: !avatarFile,
                isDefault1: avatarFile === 'assets/images/avatars/default_male.png',
                isDefault2: avatarFile === 'default_male.png',
                isEmptyString: avatarFile === '',
            });
            useDefaultAvatar();
        }

        function useDefaultAvatar() {
            console.log('🎯 执行默认头像设置');
            mainAvatar.style.backgroundImage = '';
            // 修复：安全地获取字符，避免charAt错误
            let displayChar = '?';
            if (user.character_name && user.character_name.length > 0) {
                displayChar = user.character_name.charAt(0);
                console.log('🎯 使用角色名首字符:', displayChar, '来自:', user.character_name);
            } else if (user.username && user.username.length > 0) {
                displayChar = user.username.charAt(0);
                console.log('🎯 使用用户名首字符:', displayChar, '来自:', user.username);
            } else {
                console.log('🎯 使用默认字符:', displayChar);
            }
            mainAvatar.textContent = displayChar;
            console.log('✅ 默认头像设置完成，显示字符:', displayChar);
        }
    } else {
        console.error('❌ mainAvatar元素未找到！');
    }

    console.log('=== updateUserDisplay 完成 ===');
}

// 统一渲染所有界面元素（确保数据完整后调用）
function renderAllUI() {
    console.log('开始统一渲染UI...');
    console.log('当前数据状态:');
    console.log('- inventoryItems:', inventoryItems?.length || 0);
    console.log('- equippedItems:', equippedItems?.length || 0);
    console.log('- weaponSlots:', weaponSlots?.length || 0);

    // 渲染背包
    renderInventory();

    // 渲染已装备物品
    renderEquippedItems();

    // 渲染武器槽位
    renderWeaponSlots();

    // 更新背包空间显示
    updateInventorySpaceDisplay();

    console.log('UI渲染完成');
}

// 渲染背包物品
function renderInventory() {
    const container = document.getElementById('inventory-grid');
    let filteredItems = inventoryItems;
    // === 修复：按sort_weight排序 - 权重大的在前面 ===
    filteredItems = filteredItems.slice().sort((a, b) => {
        const aw = typeof a.sort_weight === 'number' ? a.sort_weight : parseInt(a.sort_weight) || 0;
        const bw = typeof b.sort_weight === 'number' ? b.sort_weight : parseInt(b.sort_weight) || 0;
        if (aw !== bw) return bw - aw; // 🔧 修复：改为降序排列，权重大的在前面
        return (parseInt(b.id) || 0) - (parseInt(a.id) || 0); // 权重相同时，ID大的在前面
    });

    debugLog(`🎒 渲染背包: ${inventoryItems?.length || 0}个物品, 当前tab: ${currentTab}`);

    // 过滤器逻辑 - 修复：根据新的分类方式进行过滤
    if (currentTab !== 'all') {
        filteredItems = inventoryItems?.filter(item => {
            if (currentTab === 'weapon') {
                // 武器分类：只显示武器（修复：包含sword和fan）
                return (
                    item.item_type === 'weapon' ||
                    ['weapon', 'sword', 'fan'].includes(item.slot_type)
                );
            } else if (currentTab === 'equipment') {
                // 装备分类：显示除武器外的所有可装备物品
                const equipmentSlotTypes = [
                    'chest',
                    'ring',
                    'bracers',
                    'accessory',
                    'belt',
                    'boots',
                    'head',
                    'legs',
                    'necklace',
                    'bracelet',
                    'feet',
                ];
                return equipmentSlotTypes.includes(item.slot_type);
            } else if (currentTab === 'other') {
                // 其他分类：显示除武器和装备外的其他道具（修复：包含sword和fan）
                const equipmentSlotTypes = [
                    'chest',
                    'ring',
                    'bracers',
                    'accessory',
                    'belt',
                    'boots',
                    'head',
                    'legs',
                    'necklace',
                    'bracelet',
                    'feet',
                    'weapon',
                    'sword',
                    'fan',
                ];
                return !equipmentSlotTypes.includes(item.slot_type);
            }
            return true;
        });
    }

    debugLog(`🔍 过滤后物品数: ${filteredItems.length}`);

    if (filteredItems.length === 0) {
        container.innerHTML = '<div class="loading">暂无物品</div>';
        return;
    }

    const itemsHtml = filteredItems
        .map(item => {
            // 🔧 修复：确保每个物品都有有效的rarity字段
            if (
                !item.rarity ||
                item.rarity === '' ||
                item.rarity === null ||
                item.rarity === undefined
            ) {
                item.rarity = 'common'; // 默认为普通品质
            }

            // 🔧 修复：使用与getActionButtons一致的装备状态检查逻辑
            let isEquipped = false;

            // 🔧 使用slot_type来判断物品类型，这是数据库中的实际字段（修复：包含sword和fan）
            const isWeapon = ['weapon', 'sword', 'fan'].includes(item.slot_type);
            // 🔧 修复：装备类物品包括所有非武器的装备槽位类型
            const isEquipment = [
                'chest',
                'ring',
                'bracers',
                'accessory',
                'belt',
                'boots',
                'head',
                'legs',
                'necklace',
                'bracelet',
                'feet',
            ].includes(item.slot_type);

            debugLog(`🔍 检查装备状态: ${item.name} (${item.slot_type})`);

            if (isWeapon) {
                // 武器装备状态检查
                if (!weaponSlots || weaponSlots.length === 0) {
                    isEquipped = false;
                } else {
                    // 检查武器是否已装备 - 使用数字比较确保准确
                    isEquipped = weaponSlots?.some(slot => {
                        // 确保slot和inventory_item_id都存在
                        if (!slot || !slot.inventory_item_id) return false;

                        // 将两个值都转换为数字进行比较
                        const slotItemId = parseInt(slot.inventory_item_id);
                        const itemId = parseInt(item.id);

                        const match = !isNaN(slotItemId) && !isNaN(itemId) && slotItemId === itemId;
                        return match;
                    });
                }
            } else if (isEquipment) {
                // 🔧 装备类物品：使用inventory_item_id进行匹配（现在后端已修复）

                // 🔧 修复：使用inventory_item_id进行匹配（正确的方式）
                isEquipped =
                    Array.isArray(equippedItems) &&
                    equippedItems.some(eq => {
                        return parseInt(eq.inventory_item_id) === parseInt(item.id);
                    });
            } else {
                // 非装备类物品，不会有装备状态
                isEquipped = false;
            }

            return `
                    <div class="inventory-item rarity-${item.rarity} ${
                isEquipped ? 'equipped' : ''
            }" onclick="selectItem(${item.id})" data-item-id="${item.id}">
                        ${
                            item.quantity > 1
                                ? `<div class="item-quantity">${item.quantity}</div>`
                                : ''
                        }
                        ${isEquipped ? '<div class="equipped-badge">已装备</div>' : ''}
                        <div class="item-image" style="background-image: url('${getItemImageUrl(
                            item
                        )}'); background-size: contain; background-repeat: no-repeat; background-position: center; width: 100%; height: 60%; position: absolute; top: 0; left: 0;"></div>
                        <div class="item-name-box">
                            <div class="item-name-text">
                                ${item.name}
                            </div>
                        </div>
                    </div>
                `;
        })
        .join('');

    container.innerHTML = itemsHtml;

    // 重新添加触摸反馈
    addTouchFeedback();

    // 🔥 触发战力系统检查更好装备
    if (window.powerRating) {
        setTimeout(() => {
            console.log('🔥 [Equipment] 背包渲染完成，触发箭头检查...');
            window.powerRating.checkBetterEquipment();
        }, 1000); // 增加延迟时间
    }
}

// 渲染已装备物品
function renderEquippedItems() {
    debugLog(`⚔️ 渲染已装备物品: ${equippedItems?.length || 0}个`);

    // 先清除所有装备槽位
    const allSlots = document.querySelectorAll('.equipment-slot');
    allSlots.forEach((slot, index) => {
        slot.classList.remove('equipped');

        // 🔧 新增：清除品质颜色类
        const rarityClasses = [
            'rarity-common',
            'rarity-uncommon',
            'rarity-rare',
            'rarity-epic',
            'rarity-legendary',
        ];
        rarityClasses.forEach(cls => slot.classList.remove(cls));

        // 🔧 修复：清除背景图片
        slot.style.backgroundImage = '';
        slot.style.backgroundSize = '';
        slot.style.backgroundRepeat = '';
        slot.style.backgroundPosition = '';

        // 恢复原始文字内容和标题
        slot.innerHTML = slot.getAttribute('title');
        slot.title = slot.getAttribute('title');
        slot.onclick = null; // 清除点击事件
    });

    // 检查equippedItems是否是数组
    if (!Array.isArray(equippedItems)) {
        console.error('equippedItems不是数组:', equippedItems);
        return;
    }

    equippedItems.forEach((equippedItem, index) => {
        debugLog(`🔧 装备槽位: ${equippedItem.name} -> ${equippedItem.slot_type}`);

        // 🔧 修复：直接从背包中找到对应的完整物品数据
        const inventoryItem = inventoryItems?.find(invItem => {
            // 使用inventory_item_id进行匹配
            return parseInt(invItem.id) === parseInt(equippedItem.inventory_item_id);
        });

        if (!inventoryItem) {
            console.warn('在背包中未找到对应的物品数据:', equippedItem);
            return;
        }

        // 装备类型映射 - 修复数据库槽位名称到前端HTML元素的映射
        let slotSelector = '';
        switch (equippedItem.slot_type) {
            case 'ring':
                slotSelector = '[data-slot="ring"]';
                break;
            case 'bracelet': // 数据库中是bracelet，前端HTML是bracers
                slotSelector = '[data-slot="bracers"]';
                break;
            case 'chest':
                slotSelector = '[data-slot="chest"]';
                break;
            case 'necklace': // 数据库中是necklace，前端HTML是accessory
                slotSelector = '[data-slot="accessory"]';
                break;
            case 'legs': // 数据库中是legs，前端HTML是belt
                slotSelector = '[data-slot="belt"]';
                break;
            case 'feet': // 数据库中是feet，前端HTML是boots
                slotSelector = '[data-slot="boots"]';
                break;
            default:
                console.warn('未知的装备类型:', equippedItem.slot_type);
                return;
        }

        const slot = document.querySelector(slotSelector);

        if (slot && !slot.classList.contains('weapon-slot')) {
            slot.classList.add('equipped');

            // 🔧 新增：添加品质颜色类，与背包物品保持一致
            if (
                !inventoryItem.rarity ||
                inventoryItem.rarity === '' ||
                inventoryItem.rarity === null ||
                inventoryItem.rarity === undefined
            ) {
                inventoryItem.rarity = 'common'; // 默认为普通品质
            }
            slot.classList.add(`rarity-${inventoryItem.rarity}`);

            const levelText = inventoryItem.level_requirement
                ? `Lv${inventoryItem.level_requirement}`
                : '';

            // 🔧 修复：显示物品图片而不是文字
            const itemImageUrl = getItemImageUrl(inventoryItem);

            // 设置槽位背景图片
            slot.style.backgroundImage = `url('${itemImageUrl}')`;
            slot.style.backgroundSize = 'contain';
            slot.style.backgroundRepeat = 'no-repeat';
            slot.style.backgroundPosition = 'center';

            // 清空文字内容，只显示图片
            slot.innerHTML = '';

            // 添加物品名称提示
            slot.title = `${inventoryItem.name}${levelText ? ` (${levelText})` : ''}`;

            // 🔧 修复：直接使用背包中的完整物品数据
            slot.onclick = async () => {
                // 直接调用selectItem函数，传入背包物品的ID
                await selectItem(inventoryItem.id);
            };
        } else {
            console.warn('❌ 未找到槽位元素或槽位是武器槽位:', slotSelector);
        }
    });
}

// 渲染武器槽位
function renderWeaponSlots() {
    if (!Array.isArray(weaponSlots)) {
        console.error('weaponSlots不是数组:', weaponSlots);
        return;
    }

    weaponSlots.forEach((slot, index) => {
        const slotElement = document.querySelector(`.weapon-slot[data-slot="${slot.slot_index}"]`);

        if (!slotElement) return;

        // 清除旧的点击事件和样式
        slotElement.onclick = null;
        slotElement.classList.remove('equipped');
        const rarityClasses = [
            'rarity-common',
            'rarity-uncommon',
            'rarity-rare',
            'rarity-epic',
            'rarity-legendary',
        ];
        rarityClasses.forEach(cls => slotElement.classList.remove(cls));

        // 检查是否有装备武器
        if (slot.inventory_item_id && slot.item_id > 0) {
            // 尝试从背包中找到对应的完整物品数据
            const inventoryItem = inventoryItems?.find(invItem => {
                return parseInt(invItem.id) === parseInt(slot.inventory_item_id);
            });

            if (inventoryItem) {
                // 找到了对应的背包物品，使用完整数据
                console.log(`武器槽位 ${slot.slot_index} 使用背包物品数据:`, inventoryItem);

                slotElement.classList.add('equipped');

                // 确保武器有有效的rarity字段
                if (
                    !inventoryItem.rarity ||
                    inventoryItem.rarity === '' ||
                    inventoryItem.rarity === null ||
                    inventoryItem.rarity === undefined
                ) {
                    inventoryItem.rarity = 'common';
                }
                slotElement.classList.add(`rarity-${inventoryItem.rarity}`);

                // 显示武器图片
                const weaponImageUrl = getItemImageUrl(inventoryItem);
                slotElement.style.backgroundImage = `url('${weaponImageUrl}')`;
                slotElement.style.backgroundSize = 'contain';
                slotElement.style.backgroundRepeat = 'no-repeat';
                slotElement.style.backgroundPosition = 'center';

                // 耐久度条
                slotElement.innerHTML = `
                            <div class="weapon-durability" style="position: absolute; bottom: 2px; left: 2px; right: 2px;">
                                <div class="durability-bar" style="width: ${getDurabilityPercent(
                                    inventoryItem
                                )}%; height: 3px; background: ${
                    getDurabilityPercent(inventoryItem) > 50
                        ? '#27ae60'
                        : getDurabilityPercent(inventoryItem) > 20
                        ? '#f39c12'
                        : '#e74c3c'
                }; border-radius: 1px;"></div>
                            </div>
                        `;

                // 提示信息
                const skillText = inventoryItem.skill_name
                    ? ` | 技能: ${inventoryItem.skill_name}`
                    : '';
                const durabilityText =
                    inventoryItem.current_durability && inventoryItem.max_durability
                        ? ` | 耐久: ${inventoryItem.current_durability}/${inventoryItem.max_durability}`
                        : '';
                slotElement.title = `${inventoryItem.name}${skillText}${durabilityText}`;

                // 🔥 添加武器槽位标识，用于战力系统
                slotElement.setAttribute('data-weapon-slot', slot.slot_index);
                slotElement.setAttribute('data-item-id', inventoryItem.id);

                // 点击事件
                slotElement.onclick = async () => {
                    console.log('点击武器槽位，使用背包物品数据:', inventoryItem);
                    await selectItem(inventoryItem.id);
                };
            } else if (slot.name) {
                // 找不到背包物品，但有基础信息，使用基础显示
                console.log(`武器槽位 ${slot.slot_index} 使用基础数据:`, slot);

                slotElement.classList.add('equipped');

                // 基础品质
                const rarity = slot.rarity || 'common';
                slotElement.classList.add(`rarity-${rarity}`);

                // 文字显示
                slotElement.style.backgroundImage = '';
                slotElement.innerHTML = `
                            <div class="weapon-name" style="font-size: 10px; color: white; text-align: center; margin-top: 5px;">${slot.name}</div>
                            <div class="weapon-durability" style="position: absolute; bottom: 2px; left: 2px; right: 2px;">
                                <div class="durability-bar" style="width: 50%; height: 3px; background: #f39c12; border-radius: 1px;"></div>
                            </div>
                        `;

                slotElement.title = slot.name;

                // 点击事件（构建临时物品对象）
                slotElement.onclick = async () => {
                    console.log('点击武器槽位（基础数据）:', slot);
                    const tempWeapon = {
                        id: slot.inventory_item_id,
                        name: slot.name,
                        item_type: 'weapon',
                        slot_type: slot.slot_type || 'weapon',
                        current_durability: slot.current_durability || 0,
                        max_durability: slot.max_durability || 100,
                        rarity: slot.rarity || 'common',
                    };
                    await selectItem(tempWeapon.id);
                };
            } else {
                // 有inventory_item_id但没有名称，可能是数据问题
                console.warn(`武器槽位 ${slot.slot_index} 数据异常:`, slot);
                renderEmptySlot(slotElement, slot.slot_index);
            }
        } else {
            // 空槽位
            renderEmptySlot(slotElement, slot.slot_index);
        }
    });
}

// 渲染空槽位的辅助函数
function renderEmptySlot(slotElement, slotIndex) {
    slotElement.style.backgroundImage = '';
    slotElement.title = `武器槽位 ${slotIndex}`;
    slotElement.innerHTML = `
                <div>${slotIndex}</div>
                <div class="weapon-durability">
                    <div class="durability-bar" style="width: 0%; height: 3px; background: #666; border-radius: 1px;"></div>
                </div>
            `;
}

// 🔧 更新：选择物品，使用新的独立组件
async function selectItem(itemId) {
    // 兼容字符串和数字ID
    selectedItem = inventoryItems?.find(item => item.id == itemId || item.id === itemId);

    if (!selectedItem) {
        showMessage('未找到选中的物品', 'error');
        return;
    }

    // 🔧 设置当前用户信息
    ItemDetailPopup.setCurrentUser(currentUser);

    // 🔧 确定按钮显示选项
    const isWeapon = ['weapon', 'sword', 'fan'].includes(selectedItem.slot_type);
    const isEquipment = ['ring', 'bracelet', 'chest', 'necklace', 'legs', 'feet'].includes(
        selectedItem.slot_type
    );
    const isConsumable = selectedItem.item_type === 'consumable';
    // 🔧 新增：识别丹炉、丹方等特殊物品类型
    const isFurnace =
        selectedItem.name &&
        (selectedItem.name.includes('丹炉') || selectedItem.name.includes('炼丹'));
    const isRecipe = selectedItem.name && selectedItem.name.includes('丹方');
    const isPill =
        selectedItem.name &&
        selectedItem.name.includes('丹') &&
        !selectedItem.name.includes('丹方') &&
        !selectedItem.name.includes('丹炉');
    // 🔧 修复：增强材料类型判断，与item-detail-popup.js保持一致
    const isMaterial =
        selectedItem.item_type === 'material' ||
        selectedItem.slot_type === 'material' ||
        (selectedItem.name &&
            (selectedItem.name.includes('草') ||
                selectedItem.name.includes('花') ||
                selectedItem.name.includes('根') ||
                selectedItem.name.includes('叶') ||
                selectedItem.name.includes('果') ||
                selectedItem.name.includes('石')));

    // 检查物品是否已装备
    let isEquipped = false;
    if (isWeapon) {
        isEquipped =
            weaponSlots &&
            weaponSlots.some(slot => {
                if (!slot || !slot.inventory_item_id) return false;
                const slotItemId = parseInt(slot.inventory_item_id);
                const itemId = parseInt(selectedItem.id);
                return !isNaN(slotItemId) && !isNaN(itemId) && slotItemId === itemId;
            });
    } else if (isEquipment) {
        isEquipped =
            Array.isArray(equippedItems) &&
            equippedItems.some(eq => {
                return parseInt(eq.inventory_item_id) === parseInt(selectedItem.id);
            });
    }

    // 🔧 设置物品的装备状态到物品对象中，确保物品详情弹窗能正确判断
    selectedItem.is_equipped = isEquipped;

    // 🔧 修复：确定是否显示使用按钮
    let showUse = false;
    if (isConsumable || isPill) {
        showUse = true; // 消耗品和丹药显示使用按钮
    } else if (isRecipe) {
        showUse = true; // 丹方显示学习按钮（使用）
    } else if (isFurnace) {
        showUse = false; // 丹炉不显示使用按钮，只显示回收按钮
    }

    // 🔧 修复：确定是否显示修复按钮 - 只有武器且耐久度不满时才显示
    let showRepair = false;
    if (
        isWeapon &&
        selectedItem.current_durability !== undefined &&
        selectedItem.max_durability !== undefined
    ) {
        const currentDurability = parseInt(selectedItem.current_durability) || 0;
        const maxDurability = parseInt(selectedItem.max_durability) || 100;
        showRepair = currentDurability < maxDurability; // 只有耐久度不满时才显示修复按钮
    }

    // 🔧 修复：确定是否显示回收按钮 - 支持所有可回收物品类型
    const canRecycle =
        (isWeapon || isEquipment || isConsumable || isMaterial || isPill || isFurnace) &&
        !selectedItem.bind_status &&
        !isEquipped;

    console.log('🔧 [SelectItem] 物品类型判断:', {
        name: selectedItem.name,
        item_type: selectedItem.item_type,
        slot_type: selectedItem.slot_type,
        isWeapon,
        isEquipment,
        isConsumable,
        isMaterial,
        isPill,
        isFurnace,
        isRecipe,
        bind_status: selectedItem.bind_status,
        isEquipped,
        canRecycle,
    });

    // 显示物品详情弹窗
    await ItemDetailPopup.show(selectedItem, {
        showEquip: (isWeapon || isEquipment) && !isEquipped,
        showUnequip: (isWeapon || isEquipment) && isEquipped,
        showUse: showUse, // 🔧 修复：使用计算后的showUse值
        showRepair: showRepair, // 🔧 修复：使用计算后的showRepair值
        showRecycle: canRecycle, // 🔧 修复：使用计算后的canRecycle值，支持所有可回收物品类型
    });
}

// 切换标签
function switchTab(tab) {
    currentTab = tab;

    document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
    event.currentTarget.classList.add('active');

    // 控制背包管理按钮的显示/隐藏
    const inventoryManagement = document.getElementById('inventory-management');
    if (inventoryManagement) {
        if (tab === 'sets') {
            // 套装选项卡下隐藏背包管理按钮
            inventoryManagement.style.display = 'none';
        } else {
            // 其他选项卡下显示背包管理按钮
            inventoryManagement.style.display = 'flex';
        }
    }

    // 根据标签类型显示不同内容
    if (tab === 'sets') {
        showEquipmentSets();
    } else {
        hideEquipmentSets();
        renderAllUI();

        // 🔥 集成战力系统：切换标签时重新检查更好的装备
        if (window.PowerRatingIntegration) {
            PowerRatingIntegration.onInventoryTabSwitch(tab);
        }
    }
}

/**
 * 显示装备套装
 */
function showEquipmentSets() {
    const inventoryGrid = document.getElementById('inventory-grid');
    const setsContainer = document.getElementById('equipment-sets-container');

    if (inventoryGrid && setsContainer) {
        inventoryGrid.style.display = 'none';
        setsContainer.style.display = 'block';

        // 触发套装系统加载
        if (window.equipmentSetManager) {
            window.equipmentSetManager.refresh();
        }
    }
}

/**
 * 隐藏装备套装
 */
function hideEquipmentSets() {
    const inventoryGrid = document.getElementById('inventory-grid');
    const setsContainer = document.getElementById('equipment-sets-container');

    if (inventoryGrid && setsContainer) {
        inventoryGrid.style.display = 'grid';
        setsContainer.style.display = 'none';
    }
}

// 刷新数据
async function refreshData() {
    selectedItem = null;

    await Promise.all([loadUserData(), loadEquipmentData()]);
}

// 工具函数
function getDurabilityPercent(item) {
    if (!item.max_durability) return 0;
    return Math.round((item.current_durability / item.max_durability) * 100);
}

function getRarityText(rarity) {
    const rarityMap = {
        common: '普通',
        uncommon: '罕见',
        rare: '稀有',
        epic: '史诗',
        legendary: '传说',
    };
    return rarityMap[rarity] || rarity;
}

function showMessage(text, type) {
    // 移除现有消息
    const existingMessage = document.querySelector('.message');
    if (existingMessage) {
        existingMessage.remove();
    }

    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.textContent = text;
    document.body.appendChild(messageDiv);

    setTimeout(() => {
        messageDiv.remove();
    }, 3000);
}

function goBack() {
    window.location.href = 'game.html';
}

// 底部导航功能
function goToHome() {
    window.location.href = 'game.html';
}

function goToEquipment() {
    // 当前就在装备界面，可以添加一些效果提示
    showMessage('当前已在装备界面', 'info');
}

function goToAttributes() {
    // 跳转到属性页面
    window.location.href = 'attributes.html';
}

function goToBattle() {
    // 跳转到战斗页面
    window.location.href = 'battle.html';
}

function openSettings() {
    // 跳转到设置页面
    window.location.href = 'settings.html';
}

// 🔧 已删除：closeItemDetail函数，现在由ItemDetailPopup组件处理

// 头像选择相关功能

// 打开头像选择器
async function openAvatarSelector() {
    try {
        // 确保角色头像元素存在
        const avatarElement = document.getElementById('main-avatar');
        if (!avatarElement) {
            console.error('角色头像元素未找到');
            showMessage('头像功能暂不可用', 'error');
            return;
        }

        // 为角色头像添加点击事件（如果还没有）
        avatarElement.style.cursor = 'pointer';
        avatarElement.title = '点击更换形象';

        // 加载可用头像列表
        await loadAvailableAvatars();

        // 🔧 修复：处理数据库中avatar_image为数字0的情况
        let avatarFile = currentUser?.avatar_image;
        if (
            !avatarFile ||
            avatarFile === 0 ||
            avatarFile === '0' ||
            avatarFile === 'null' ||
            avatarFile === 'undefined'
        ) {
            avatarFile = 'ck.png';
        }
        console.log('🔍 原始头像数据:', {
            avatar_image: currentUser?.avatar_image,
            avatarFile: avatarFile,
            currentUser: currentUser,
        });

        // 如果是完整路径，提取文件名
        if (avatarFile && avatarFile.includes('/')) {
            avatarFile = avatarFile.split('/').pop();
        }

        // 确保头像文件名有效
        if (
            !avatarFile ||
            avatarFile === 'null' ||
            avatarFile === 'undefined' ||
            avatarFile === ''
        ) {
            avatarFile = 'ck.png';
        }

        currentAvatar = avatarFile;
        selectedAvatar = currentAvatar;

        console.log('🎯 头像选择器初始化:');
        console.log('- currentUser:', currentUser);
        console.log('- currentAvatar:', currentAvatar);
        console.log('- selectedAvatar:', selectedAvatar);

        // 渲染头像选择界面
        renderAvatarSelector();

        // 显示弹窗
        document.getElementById('avatarSelectorModal').style.display = 'flex';
    } catch (error) {
        console.error('打开头像选择器失败:', error);
        showMessage('打开头像选择器失败', 'error');
    }
}

// 关闭头像选择器
function closeAvatarSelector() {
    document.getElementById('avatarSelectorModal').style.display = 'none';
    // 🔧 修复：不要将selectedAvatar设置为null，保持当前头像状态
    selectedAvatar = currentAvatar;
}

// 加载可用头像列表
async function loadAvailableAvatars() {
    try {
        console.log('开始加载头像列表...');
        const response = await fetch(
            window.GameConfig
                ? window.GameConfig.getApiUrl(
                      'equipment_integrated.php?action=get_available_avatars'
                  )
                : '/yinian/src/api/equipment_integrated.php?action=get_available_avatars'
        );
        const data = await response.json();

        console.log('头像API返回数据:', data);

        if (data.success) {
            availableAvatars = data.avatars;
            console.log('头像列表加载成功:', availableAvatars);

            // 确保数据结构正确
            if (!availableAvatars.free) {
                availableAvatars.free = [];
            }
            if (!availableAvatars.vip) {
                availableAvatars.vip = [];
            }
        } else {
            console.error('获取头像列表失败:', data.message);
            showMessage('获取头像列表失败', 'error');
            // 设置默认头像数据
            availableAvatars = {
                free: [
                    { filename: 'ck.png', name: '初心', type: 'free', owned: true },
                    { filename: 'cy.png', name: '慈悲', type: 'free', owned: true },
                    { filename: 'huaishang.png', name: '怀伤', type: 'free', owned: true },
                    { filename: 'my.png', name: '明月', type: 'free', owned: true },
                ],
                vip: [],
            };
        }
    } catch (error) {
        console.error('加载头像列表失败:', error);
        showMessage('加载头像列表失败', 'error');
        // 设置默认头像数据
        availableAvatars = {
            free: [
                { filename: 'ck.png', name: '初心', type: 'free', owned: true },
                { filename: 'cy.png', name: '慈悲', type: 'free', owned: true },
                { filename: 'huaishang.png', name: '怀伤', type: 'free', owned: true },
                { filename: 'my.png', name: '明月', type: 'free', owned: true },
            ],
            vip: [],
        };
    }
}

// 渲染头像选择器
function renderAvatarSelector() {
    console.log('开始渲染头像选择器...');
    console.log('可用头像数据:', availableAvatars);
    console.log('🎯 渲染时的头像状态:');
    console.log('- currentAvatar:', currentAvatar);
    console.log('- selectedAvatar:', selectedAvatar);

    // 渲染免费头像
    const freeGrid = document.getElementById('freeAvatarGrid');
    if (!freeGrid) {
        console.error('freeAvatarGrid元素未找到');
        return;
    }

    freeGrid.innerHTML = '';

    if (availableAvatars.free && availableAvatars.free.length > 0) {
        availableAvatars.free.forEach(avatar => {
            const avatarOption = document.createElement('div');
            avatarOption.className = 'avatar-option';
            avatarOption.style.backgroundImage = `url('assets/images/char/${avatar.filename}')`;
            avatarOption.dataset.avatar = avatar.filename;
            avatarOption.title = avatar.name || avatar.filename;

            if (avatar.filename === selectedAvatar) {
                avatarOption.classList.add('selected');
                console.log('✅ 设置选中头像:', avatar.filename);
            }

            avatarOption.addEventListener('click', () =>
                selectAvatar(avatar.filename, avatarOption)
            );
            freeGrid.appendChild(avatarOption);
        });
        console.log('免费头像渲染完成，数量:', availableAvatars.free.length);
    } else {
        freeGrid.innerHTML =
            '<div style="text-align: center; color: #bdc3c7; padding: 20px;">暂无免费形象</div>';
        console.log('无免费头像数据');
    }

    // 渲染付费头像
    const vipGrid = document.getElementById('vipAvatarGrid');
    if (!vipGrid) {
        console.error('vipAvatarGrid元素未找到');
        return;
    }

    vipGrid.innerHTML = '';

    if (availableAvatars.vip && availableAvatars.vip.length > 0) {
        availableAvatars.vip.forEach(avatar => {
            const avatarOption = document.createElement('div');
            avatarOption.className = 'avatar-option';
            avatarOption.style.backgroundImage = `url('assets/images/char/${avatar.filename}')`;
            avatarOption.dataset.avatar = avatar.filename;
            avatarOption.title = avatar.name || avatar.filename;

            if (!avatar.owned) {
                avatarOption.classList.add('locked');

                // 添加价格标签
                const priceTag = document.createElement('div');
                priceTag.className = 'avatar-price';
                priceTag.textContent = `${avatar.price}💎`;
                avatarOption.appendChild(priceTag);

                avatarOption.addEventListener('click', () => {
                    showMessage('此形象需要购买才能使用', 'info');
                    // TODO: 显示购买确认弹窗
                });
            } else {
                if (avatar.filename === selectedAvatar) {
                    avatarOption.classList.add('selected');
                    console.log('✅ 设置选中付费头像:', avatar.filename);
                }
                avatarOption.addEventListener('click', () =>
                    selectAvatar(avatar.filename, avatarOption)
                );
            }

            vipGrid.appendChild(avatarOption);
        });
        console.log('付费头像渲染完成，数量:', availableAvatars.vip.length);
    } else {
        vipGrid.innerHTML =
            '<div style="text-align: center; color: #bdc3c7; padding: 20px;">暂无付费形象</div>';
        console.log('无付费头像数据');
    }
}

// 选择头像
function selectAvatar(avatarFilename, avatarElement) {
    // 移除其他选中状态
    document.querySelectorAll('.avatar-option.selected').forEach(el => {
        el.classList.remove('selected');
    });

    // 设置选中状态
    avatarElement.classList.add('selected');
    selectedAvatar = avatarFilename;

    console.log('选择头像:', avatarFilename);
}

// 确认头像更换
async function confirmAvatarChange() {
    if (!selectedAvatar || selectedAvatar === currentAvatar) {
        console.log('🔄 头像未改变，关闭选择器');
        closeAvatarSelector();
        return;
    }

    console.log('🔄 开始保存头像:', selectedAvatar);
    console.log('🔄 当前头像:', currentAvatar);

    try {
        const formData = new FormData();
        formData.append('action', 'change_avatar');
        formData.append('avatar', selectedAvatar);

        console.log('🔄 发送API请求...');
        const apiUrl = window.GameConfig
            ? window.GameConfig.getApiUrl('equipment_integrated.php')
            : '/yinian/src/api/equipment_integrated.php';
        console.log('🔄 API URL:', apiUrl);

        const response = await fetch(apiUrl, {
            method: 'POST',
            body: formData,
        });

        console.log('🔄 API响应状态:', response.status);
        const responseText = await response.text();
        console.log('🔄 API响应文本:', responseText.substring(0, 200));

        let data;
        try {
            data = JSON.parse(responseText);
        } catch (parseError) {
            console.error('❌ JSON解析失败:', parseError);
            console.error('❌ 响应内容:', responseText);
            showMessage('服务器响应格式错误', 'error');
            return;
        }

        console.log('🔄 API响应数据:', data);

        if (data.success) {
            console.log('✅ API调用成功，开始更新本地数据');

            // 更新当前用户数据
            if (currentUser) {
                console.log('🔄 更新currentUser数据');
                console.log('🔄 更新前:', {
                    character_avatar: currentUser.character_avatar,
                    avatar_image: currentUser.avatar_image,
                });

                currentUser.character_avatar = selectedAvatar;
                currentUser.avatar_image = selectedAvatar; // 🔧 同时更新avatar_image字段

                console.log('🔄 更新后:', {
                    character_avatar: currentUser.character_avatar,
                    avatar_image: currentUser.avatar_image,
                });
            } else {
                console.warn('⚠️ currentUser为空，无法更新本地数据');
            }

            // 更新当前头像状态
            console.log('🔄 更新currentAvatar:', currentAvatar, '->', selectedAvatar);
            currentAvatar = selectedAvatar;

            // 更新界面显示
            console.log('🔄 更新界面显示');
            updateAvatarDisplay(selectedAvatar);

            // 关闭弹窗
            console.log('🔄 关闭头像选择器');
            closeAvatarSelector();

            console.log('✅ 头像更换完成:', selectedAvatar);
            console.log('✅ 最终状态:', {
                currentAvatar: currentAvatar,
                selectedAvatar: selectedAvatar,
                currentUser: currentUser,
            });

            showMessage('形象更换成功！', 'success');
        } else {
            console.error('❌ API返回失败:', data.message);
            showMessage(data.message || '形象更换失败', 'error');
        }
    } catch (error) {
        console.error('❌ 更换头像异常:', error);
        console.error('❌ 错误详情:', {
            message: error.message,
            stack: error.stack,
            selectedAvatar: selectedAvatar,
            currentAvatar: currentAvatar,
        });
        showMessage('更换头像失败: ' + error.message, 'error');
    }
}

// 更新头像显示
function updateAvatarDisplay(avatarFilename) {
    const avatarElement = document.getElementById('main-avatar');
    if (avatarElement && avatarFilename) {
        avatarElement.style.backgroundImage = `url('assets/images/char/${avatarFilename}')`;
        avatarElement.style.backgroundSize = 'cover';
        avatarElement.style.backgroundPosition = 'center';
        avatarElement.style.backgroundRepeat = 'no-repeat';
        avatarElement.textContent = ''; // 清空文字内容

        console.log('更新头像显示:', avatarFilename);
    }
}

// 🔧 新增：获取已装备物品的操作按钮
function getEquippedItemActionButtons(item, slotType) {
    const isWeapon = ['weapon', 'sword', 'fan'].includes(item.slot_type);
    let buttons = [];

    if (isWeapon) {
        // 1. 人物武器栏：卸下 + (修复) + 关闭
        const equippedSlot = weaponSlots?.find(slot => {
            const slotItemId = parseInt(slot.inventory_item_id);
            const itemId = parseInt(item.id);
            return !isNaN(slotItemId) && !isNaN(itemId) && slotItemId === itemId;
        });

        if (equippedSlot) {
            // 第一项：卸下武器
            buttons.push(
                `<button class="btn btn-unequip" onclick="unequipWeapon(${equippedSlot.slot_index})">卸下武器</button>`
            );

            // 中间项：修复按钮（如果耐久度不满）
            if (
                item.current_durability !== undefined &&
                item.max_durability !== undefined &&
                parseInt(item.current_durability) < parseInt(item.max_durability)
            ) {
                buttons.push(
                    `<button class="btn btn-repair" onclick="repairWeapon(${item.id})">修复武器</button>`
                );
            }
        }
    } else {
        // 2. 人物装备栏：卸下 + 关闭
        buttons.push(
            `<button onclick="unequipItem(${item.id}, '${slotType}')" class="btn btn-unequip">卸下装备</button>`
        );
    }

    // 最后一项：关闭按钮
    buttons.push('<button class="btn btn-close" onclick="closeItemDetail()">关闭</button>');

    return buttons.join('');
}

// 🔧 修复：获取物品图片URL，正确拼接icon_image字段路径
function getItemImageUrl(item) {
    debugLog(`🖼️ 获取图片: ${item.name}`);

    // 🔧 修复：优先使用后端处理好的image_url字段
    if (item.image_url) {
        return item.image_url;
    }

    // 🔧 次选：使用icon_image字段，并正确拼接路径
    if (item.icon_image) {
        // 如果icon_image字段已经包含完整路径（以 / 或 http 或 assets/ 开头），直接使用
        if (
            item.icon_image.startsWith('/') ||
            item.icon_image.startsWith('http') ||
            item.icon_image.startsWith('assets/')
        ) {
            return item.icon_image;
        }

        // 否则拼接 assets/images/ 路径
        return `assets/images/${item.icon_image}`;
    }

    // 🔧 再次：使用item_image字段
    if (item.item_image) {
        return item.item_image;
    }

    // 🔧 最后：使用默认图片
    return getDefaultItemImage(item);
}

// 🔧 新增：获取默认物品图片
function getDefaultItemImage(item) {
    // 根据物品名称和类型生成默认图片路径
    const realmKeywords = {
        开光: 1,
        灵虚: 2,
        辟谷: 3,
        心动: 4,
        元化: 5,
        元婴: 6,
        离合: 7,
        空冥: 8,
        寂灭: 9,
        大乘: 10,
    };

    let realmLevel = 1;
    const itemName = item.name || item.item_name || '';
    for (const [keyword, level] of Object.entries(realmKeywords)) {
        if (itemName.includes(keyword)) {
            realmLevel = level;
            break;
        }
    }

    if (item.item_type === 'weapon' || item.slot_type === 'weapon') {
        if (item.slot_type === 'sword' || itemName.includes('剑')) {
            const swordImages = [
                'bw_11001.png',
                'bw_11002.png',
                'bw_11003.png',
                'bw_11004.png',
                'bw_11301.png',
                'bw_11601.png',
                'bw_11602.png',
                'bw_11603.png',
                'bw_11604.png',
                'bw_11605.png',
            ];
            return `assets/images/${swordImages[realmLevel - 1] || swordImages[0]}`;
        } else if (item.slot_type === 'fan' || itemName.includes('扇')) {
            const fanImages = [
                'bw_10301.png',
                'bw_10302.png',
                'bw_10601.png',
                'bw_10602.png',
                'bw_10801.png',
                'bw_11801.png',
                'bw_11802.png',
                'bw_11606.png',
                'bw_11607.png',
                'bw_11608.png',
            ];
            return `assets/images/${fanImages[realmLevel - 1] || fanImages[0]}`;
        }
        return 'assets/images/bw_11001.png';
    } else if (item.item_type === 'equipment') {
        if (item.slot_type === 'ring') {
            const ringImages = [
                '1200.png',
                '1201.png',
                '1202.png',
                '1203.png',
                '1204.png',
                '1205.png',
                '1206.png',
                '1207.png',
                '1208.png',
                '1209.png',
            ];
            return `assets/images/${ringImages[realmLevel - 1] || ringImages[0]}`;
        } else if (item.slot_type === 'bracers') {
            const bracersImages = [
                'zb_10101.png',
                'zb_10201.png',
                'zb_10202.png',
                'zb_10261.png',
                'zb_10301.png',
                'zb_10302.png',
                'zb_10361.png',
                'zb_10401.png',
                'zb_10402.png',
                'zb_10403.png',
            ];
            return `assets/images/${bracersImages[realmLevel - 1] || bracersImages[0]}`;
        } else if (item.slot_type === 'chest') {
            const chestImages = [
                'zb_20101.png',
                'zb_20201.png',
                'zb_20202.png',
                'zb_20261.png',
                'zb_20301.png',
                'zb_20302.png',
                'zb_20361.png',
                'zb_20401.png',
                'zb_20402.png',
                'zb_20403.png',
            ];
            return `assets/images/${chestImages[realmLevel - 1] || chestImages[0]}`;
        }
        return 'assets/images/zb_10101.png';
    }

    return 'assets/images/1100.png';
}

// 🆕 获取物品类型文字
function getItemTypeText(item) {
    // 🔥 属性丹类型判断 (ID: 314-358)
    if (item.item_id >= 314 && item.item_id <= 358) {
        return '属性丹';
    }

    // 🔧 新增：丹炉类型判断
    if (item.name && (item.name.includes('丹炉') || item.name.includes('炼丹'))) {
        return '炼丹工具';
    }

    // 通过slot_type判断类型
    if (item.slot_type) {
        switch (item.slot_type) {
            case 'weapon':
                return '武器';
            case 'sword':
                return '剑';
            case 'fan':
                return '扇';
            case 'chest':
                return '胸甲';
            case 'head':
                return '头盔';
            case 'legs':
                return '护腿';
            case 'boots':
                return '靴子';
            case 'ring':
                return '戒指';
            case 'necklace':
                return '项链';
            case 'bracelet':
                return '手镯';
            case 'bracers':
                return '护腕';
            case 'belt':
                return '腰带';
            case 'accessory':
                return '饰品';
            case 'consumable':
                return '消耗品';
            case 'spirit':
                return '灵石';
            case 'technique_manual':
                return '功法秘籍';
            case 'material':
                return '材料';
            default:
                break;
        }
    }

    // 通过item_type判断类型（兜底）
    if (item.item_type) {
        switch (item.item_type) {
            case 'consumable':
                return '消耗品';
            case 'weapon':
                return '武器';
            case 'equipment':
                return '装备';
            case 'material':
                return '材料';
            case 'treasure':
                return '珍宝';
            default:
                break;
        }
    }

    // 通过名称判断特殊类型
    if (item.name || item.item_name) {
        const name = item.name || item.item_name;

        if (name.includes('丹方')) {
            return '丹方';
        }
        if (name.includes('功法') || name.includes('心法') || name.includes('秘籍')) {
            return '功法秘籍';
        }
        if (name.includes('灵石')) {
            return '灵石';
        }
        if (name.includes('丹药') || name.includes('丹')) {
            return '丹药';
        }
        if (name.includes('材料')) {
            return '材料';
        }
    }

    return null; // 未知类型不显示
}

// 🔧 新增：获取特殊效果显示
function getSpecialEffectsDisplay(item) {
    // 🔧 修复：过滤掉不应该显示的内部字段，只显示真正的特殊效果
    if (!item.special_effects) {
        return '';
    }

    // 🔧 如果special_effects是字符串，尝试解析
    let effects = item.special_effects;
    if (typeof effects === 'string') {
        try {
            effects = JSON.parse(effects);
        } catch (e) {
            console.warn('特殊效果JSON解析失败:', e);
            return '';
        }
    }

    // 🔧 如果special_effects不是对象或为空，不显示
    if (!effects || typeof effects !== 'object' || Object.keys(effects).length === 0) {
        return '';
    }

    // 🔧 过滤掉不应该显示的内部字段
    const forbiddenKeys = [
        'id',
        'item_id',
        'name',
        'item_name',
        'description',
        'item_type',
        'slot_type',
        'rarity',
        'level_requirement',
        'hp_bonus',
        'mp_bonus',
        'critical_bonus',
        'critical_damage',
        'accuracy_bonus',
        'dodge_bonus',
        'block_bonus',
        'speed_bonus',
        'damage_multiplier',
        'sell_price',
        'buy_price',
        'current_durability',
        'max_durability',
        'skill_name',
        'quantity',
        'obtained_time',
        'obtained_source',
        'bind_status',
        'enhancement_level',
        'socket_gems',
        'is_active',
        'created_at',
        'updated_at',
        'category',
        'subcategory',
        'image_url',
        'icon_image',
        'item_image',
        'realm_requirement',
        'usage_limit',
        'cooldown_time',
        'success_rate',
        'min_quantity',
        'max_quantity',
        'durability',
        'type',
        'alchemy_furnace',
    ];

    // 🔧 只显示真正的特殊效果
    const validEffects = {};
    for (const [key, value] of Object.entries(effects)) {
        if (!forbiddenKeys.includes(key) && value !== null && value !== undefined && value !== '') {
            validEffects[key] = value;
        }
    }

    if (Object.keys(validEffects).length === 0) {
        return '';
    }

    return `
                <div style="margin: 15px 0; padding: 10px; background: rgba(155, 89, 182, 0.2); border-radius: 8px; border: 1px solid rgba(155, 89, 182, 0.4);">
                    <div style="font-weight: bold; color: #9b59b6; font-size: 13px; margin-bottom: 6px;">特殊效果</div>
                    <div style="font-size: 11px; color: #ecf0f1; line-height: 1.3;">
                        ${Object.entries(validEffects)
                            .map(
                                ([key, value]) => `
                            <div style="margin: 2px 0;">${key}：${value}</div>
                        `
                            )
                            .join('')}
                    </div>
                </div>
            `;
}

// 🆕 整理背包功能 - 完全重写
async function organizeInventory() {
    const organizeBtn = document.querySelector('.inventory-organize-btn');

    // 防止重复点击
    if (organizeBtn.disabled) {
        return;
    }

    console.log('🔄 点击整理按钮，开始处理...');

    // 🔧 修复：立即禁用按钮并开始倒计时
    organizeBtn.disabled = true;

    // 🔧 修复：定义倒计时函数
    function startCountdown() {
        let countdown = 5;
        console.log('⏰ 开始5秒倒计时...');

        // 立即显示第一个倒计时状态
        organizeBtn.innerHTML = `
                    <span class="organize-icon">⏰</span>
                    <span class="countdown-text">${countdown}s</span>
                `;

        const countdownInterval = setInterval(() => {
            countdown--;
            console.log(`⏰ 倒计时: ${countdown}s`);

            if (countdown > 0) {
                organizeBtn.innerHTML = `
                            <span class="organize-icon">⏰</span>
                            <span class="countdown-text">${countdown}s</span>
                        `;
            } else {
                // 倒计时结束，恢复按钮状态
                console.log('✅ 倒计时结束，恢复按钮');
                clearInterval(countdownInterval);
                organizeBtn.disabled = false;
                organizeBtn.classList.remove('organizing-indicator');
                organizeBtn.innerHTML = `
                            <span class="organize-icon">📦</span>
                            <span class="organize-text">整理</span>
                        `;
            }
        }, 1000);
    }

    // 🔧 修复：先显示整理中状态
    organizeBtn.classList.add('organizing-indicator');
    organizeBtn.innerHTML = `
                <span class="organize-icon">⏳</span>
                <span class="organize-text">整理中...</span>
            `;

    // 🔧 修复：异步处理API请求，然后立即开始倒计时
    try {
        console.log('📡 发送整理请求...');

        const response = await fetch(
            window.GameConfig
                ? window.GameConfig.getApiUrl('equipment_integrated.php')
                : '/yinian/src/api/equipment_integrated.php',
            {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=organize_inventory',
            }
        );

        const data = await response.json();
        console.log('📦 整理背包响应:', data);

        if (data.success) {
            showMessage(data.message || '背包整理完成！', 'success');
            // 重新加载背包数据
            await loadEquipmentData();
        } else {
            showMessage(data.message || '背包整理失败', 'error');
        }
    } catch (error) {
        console.error('整理背包失败:', error);
        showMessage('整理背包失败：' + error.message, 'error');
    }

    // 🔧 修复：API请求完成后，短暂延迟开始倒计时
    setTimeout(() => {
        startCountdown();
    }, 500); // 500ms延迟让用户看到"整理中..."状态
}

// 🆕 更新背包空间显示
function updateInventorySpaceDisplay() {
    const spaceCountElement = document.getElementById('space-count');
    const expandBtn = document.querySelector('.space-expand-btn');

    if (!spaceCountElement) return;

    // 计算已使用的格子数
    const usedSlots = inventoryItems?.length || 0;
    const currentMaxSlots = currentUser?.inventory_slots || 30;

    spaceCountElement.textContent = `${usedSlots}/${currentMaxSlots}`;

    // 如果接近满格，改变颜色提醒
    if (usedSlots >= currentMaxSlots * 0.9) {
        spaceCountElement.style.color = '#e74c3c'; // 红色警告
    } else if (usedSlots >= currentMaxSlots * 0.7) {
        spaceCountElement.style.color = '#f39c12'; // 橙色提醒
    } else {
        spaceCountElement.style.color = '#d4af37'; // 正常金色
    }

    // 🔧 新规则：检查扩展限制和芥子石
    const baseSlots = 30;
    const absoluteMaxSlots = 130; // 最高130格
    const expandSlots = 5; // 每次扩展5格

    // 检查是否有芥子石可用于扩展
    const jieziCount = inventoryItems
        ?.filter(item => (item.name || item.item_name).includes('芥子石'))
        .reduce((total, item) => total + (item.quantity || 1), 0);

    if (expandBtn) {
        if (currentMaxSlots >= absoluteMaxSlots) {
            // 已达到扩展上限
            expandBtn.disabled = true;
            expandBtn.textContent = '已满级';
            expandBtn.title = '背包容量已达到最大值(130格)';
        } else if (jieziCount > 0) {
            expandBtn.disabled = false;
            expandBtn.textContent = `扩展(${jieziCount})`;
            const nextSlots = Math.min(currentMaxSlots + expandSlots, absoluteMaxSlots);
            expandBtn.title = `使用1个芥子石扩展5个背包格子\n当前拥有 ${jieziCount} 个芥子石\n扩展后容量：${nextSlots}格`;
        } else {
            expandBtn.disabled = true;
            expandBtn.textContent = '扩展';
            expandBtn.title = '需要芥子石才能扩展背包空间';
        }
    }
}

// 🆕 扩展背包空间功能
async function expandInventorySpace() {
    const expandBtn = document.querySelector('.space-expand-btn');

    if (expandBtn.disabled) {
        showMessage('需要芥子石才能扩展背包空间', 'error');
        return;
    }

    // 查找芥子石
    const jieziItem = inventoryItems?.find(item =>
        (item.name || item.item_name).includes('芥子石')
    );

    if (!jieziItem) {
        showMessage('背包中没有芥子石', 'error');
        return;
    }

    // 🔧 新规则：每次扩展5格，只需要1个芥子石
    const currentSlots = currentUser?.inventory_slots || 30;
    const maxSlots = 130;
    const expandSlots = 5;
    const requiredJiezi = 1; // 每次扩展只需要1个芥子石

    // 检查是否达到扩展上限
    if (currentSlots >= maxSlots) {
        showMessage('背包容量已达到上限(130格)', 'error');
        return;
    }

    const totalJiezi = inventoryItems
        ?.filter(item => (item.name || item.item_name).includes('芥子石'))
        .reduce((total, item) => total + (item.quantity || 1), 0);

    if (totalJiezi < requiredJiezi) {
        showMessage(`扩展背包需要 ${requiredJiezi} 个芥子石，当前只有 ${totalJiezi} 个`, 'error');
        return;
    }

    const newSlots = Math.min(currentSlots + expandSlots, maxSlots);
    const actualExpandSlots = newSlots - currentSlots;

    // 确认扩展
    if (
        !confirm(
            `确认使用 ${requiredJiezi} 个芥子石扩展${actualExpandSlots}个背包格子吗？\n当前背包容量：${currentSlots}格\n扩展后容量：${newSlots}格`
        )
    ) {
        return;
    }

    try {
        expandBtn.disabled = true;
        expandBtn.textContent = '扩展中...';

        const response = await fetch(
            window.GameConfig
                ? window.GameConfig.getApiUrl('equipment_integrated.php')
                : '/yinian/src/api/equipment_integrated.php',
            {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=expand_inventory',
            }
        );

        const data = await response.json();
        console.log('📦 扩展背包响应:', data);

        if (data.success) {
            showMessage(data.message || '背包扩展成功！', 'success');

            // 更新用户数据
            if (currentUser && data.data) {
                currentUser.inventory_slots = data.data.new_slots;
            }

            // 重新加载数据
            await loadEquipmentData();
        } else {
            showMessage(data.message || '背包扩展失败', 'error');
        }
    } catch (error) {
        console.error('扩展背包失败:', error);
        showMessage('扩展背包失败：' + error.message, 'error');
    } finally {
        // 重新计算按钮状态
        updateInventorySpaceDisplay();
    }
}

// 🆕 判断物品是否为法术导向（法修装备和扇类武器）
function isMagicItem(item) {
    if (!item) return false;

    // 1. 扇类武器肯定是法术导向
    if (
        item.slot_type === 'fan' ||
        (item.slot_type === 'weapon' && item.name && item.name.includes('扇'))
    ) {
        return true;
    }

    // 2. 剑类武器肯定是物理导向
    if (
        item.slot_type === 'sword' ||
        (item.slot_type === 'weapon' &&
            item.name &&
            (item.name.includes('剑') ||
                item.name.includes('刀') ||
                item.name.includes('枪') ||
                item.name.includes('斧')))
    ) {
        return false;
    }

    // 3. 装备类通过名称判断是否为法修装备
    if (
        item.item_type === 'equipment' ||
        item.item_type === 'armor' ||
        item.item_type === 'accessory'
    ) {
        const magicKeywords = [
            '法',
            '魔',
            '灵',
            '仙',
            '道',
            '散修',
            '法王',
            '法尊',
            '法帝',
            '法神',
            '月华',
            '星辰',
            '虚空',
            '太极',
            '素衣',
            '圣光',
            '玄袍',
            '白袍',
        ];

        const itemName = item.name || item.item_name || '';

        // 检查是否包含法修关键词
        return magicKeywords.some(keyword => itemName.includes(keyword));
    }

    // 4. 默认情况根据是否有魔法值加成来判断
    if (item.mp_bonus && item.mp_bonus > 0) {
        return true;
    }

    // 5. 其他情况默认为物理导向
    return false;
}

// 🔧 新增：新组件的回调函数实现

// 装备物品回调
async function equipWeaponFromPopup(item) {
    try {
        // 🔧 修复：武器装备需要选择槽位
        console.log('准备装备武器:', item);
        console.log('当前武器槽位状态:', weaponSlots);

        // 显示武器槽位选择弹窗
        showWeaponSlotSelection(item);
    } catch (error) {
        console.error('装备武器失败:', error);
        showMessage('装备武器失败：' + error.message, 'error');
    }
}

// 🆕 显示武器槽位选择弹窗
async function showWeaponSlotSelection(item) {
    try {
        // 创建弹窗HTML
        const overlayId = 'weapon-slot-selection-overlay';
        const popupId = 'weapon-slot-selection-popup';

        // 移除已存在的弹窗
        const existingOverlay = document.getElementById(overlayId);
        if (existingOverlay) {
            existingOverlay.remove();
        }

        // 🔥 获取武器槽位战力对比数据
        let slotComparisons = {};
        try {
            const response = await fetch(
                window.GameConfig
                    ? window.GameConfig.getApiUrl('power_rating.php')
                    : '/yinian/src/api/power_rating.php',
                {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: `action=compare_weapon_slots&weapon_id=${item.id}`,
                }
            );

            const data = await response.json();
            if (data.success) {
                slotComparisons = data.slot_comparisons;
                console.log('🔥 武器槽位对比数据:', slotComparisons);
            }
        } catch (error) {
            console.error('获取武器槽位对比失败:', error);
        }

        // 生成槽位选项
        let slotsHtml = '';
        for (let i = 1; i <= 6; i++) {
            const slot = weaponSlots?.find(s => parseInt(s.slot_index) === i);
            const isEmpty = !slot || !slot.inventory_item_id || !slot.name;
            const slotStatus = isEmpty ? '空闲' : `已装备：${slot.name}`;
            const slotClass = isEmpty ? 'slot-empty' : 'slot-occupied';

            // 🔥 获取战力对比图标
            let comparisonIcon = '';
            const comparison = slotComparisons[i];
            if (comparison) {
                if (comparison.comparison === 'better') {
                    comparisonIcon = '<div class="power-comparison-icon better">⬆</div>';
                } else if (comparison.comparison === 'worse') {
                    comparisonIcon = '<div class="power-comparison-icon worse">⬇</div>';
                } else {
                    comparisonIcon = '<div class="power-comparison-icon equal">—</div>';
                }
            }

            slotsHtml += `
                        <div class="weapon-slot-option ${slotClass}" onclick="selectWeaponSlot(${i}, '${
                item.id
            }')">
                            <div class="slot-number">槽位 ${i}</div>
                            <div class="slot-status">${slotStatus}</div>
                            ${!isEmpty ? `<div class="slot-warning">🔄 点击替换为新武器</div>` : ''}
                            ${comparisonIcon}
                        </div>
                    `;
        }

        const popupHtml = `
                    <div id="${overlayId}" class="weapon-slot-overlay" onclick="closeWeaponSlotSelection()">
                        <div id="${popupId}" class="weapon-slot-popup" onclick="event.stopPropagation()">
                            <div class="popup-header">
                                <h3>选择武器槽位</h3>
                                <button class="close-btn" onclick="closeWeaponSlotSelection()">×</button>
                            </div>
                            <div class="popup-content">
                                <div class="weapon-info">
                                    <div class="weapon-name rarity-${item.rarity}">${item.name}</div>
                                    <div class="weapon-desc">请选择要装备到的武器槽位（已占用槽位将自动替换）：</div>
                                    <div class="power-legend">
                                        <span class="legend-item">⬆ 战力提升</span>
                                        <span class="legend-item">⬇ 战力下降</span>
                                        <span class="legend-item">— 战力不变</span>
                                    </div>
                                </div>
                                <div class="weapon-slots-grid">
                                    ${slotsHtml}
                                </div>
                            </div>
                            <div class="popup-actions">
                                <button class="btn btn-cancel" onclick="closeWeaponSlotSelection()">取消</button>
                            </div>
                        </div>
                    </div>
                `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', popupHtml);
    } catch (error) {
        console.error('显示武器槽位选择失败:', error);
        showMessage('显示武器槽位选择失败', 'error');
    }
}

// 🆕 选择武器槽位
async function selectWeaponSlot(slotNumber, itemId) {
    try {
        console.log('选择武器槽位:', slotNumber, '装备物品ID:', itemId);

        // 🔧 新增：装备前获取当前属性
        const oldStats = await getCurrentEquipmentStats();

        const response = await fetch(
            window.GameConfig
                ? window.GameConfig.getApiUrl('equipment_integrated.php')
                : '/yinian/src/api/equipment_integrated.php',
            {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=equip_weapon&inventory_item_id=${itemId}&slot_number=${slotNumber}`,
            }
        );

        const data = await response.json();
        console.log('装备武器响应:', data);

        // 关闭弹窗
        closeWeaponSlotSelection();

        if (data.success) {
            // 🔧 新增：重新加载数据后获取新属性并显示变化
            await loadEquipmentData();
            const newStats = await getCurrentEquipmentStats();
            showAttributeChanges(oldStats, newStats);

            // 🔥 新增：更新战力显示
            await refreshCharacterPowerRating();
        } else {
            showMessage(data.message || '武器装备失败', 'error');
        }
    } catch (error) {
        console.error('装备武器失败:', error);
        showMessage('装备武器失败：' + error.message, 'error');
        closeWeaponSlotSelection();
    }
}

// 🆕 关闭武器槽位选择弹窗
function closeWeaponSlotSelection() {
    const overlay = document.getElementById('weapon-slot-selection-overlay');
    if (overlay) {
        overlay.remove();
    }
}

async function equipItemFromPopup(item) {
    try {
        // 🔧 新增：装备前获取当前属性
        const oldStats = await getCurrentEquipmentStats();

        const response = await fetch(
            window.GameConfig
                ? window.GameConfig.getApiUrl('equipment_integrated.php')
                : '/yinian/src/api/equipment_integrated.php',
            {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=equip_item&inventory_item_id=${item.id}`,
            }
        );

        const data = await response.json();
        console.log('装备物品响应:', data);

        if (data.success) {
            // 🔧 新增：重新加载数据后获取新属性并显示变化
            await loadEquipmentData();
            const newStats = await getCurrentEquipmentStats();
            showAttributeChanges(oldStats, newStats);

            // 🔥 新增：更新战力显示
            await refreshCharacterPowerRating();
        } else {
            showMessage(data.message || '装备失败', 'error');
        }
    } catch (error) {
        console.error('装备物品失败:', error);
        showMessage('装备物品失败：' + error.message, 'error');
    }
}

// 卸下物品回调
async function unequipWeaponFromPopup(item) {
    try {
        // 🔧 新增：卸下前获取当前属性
        const oldStats = await getCurrentEquipmentStats();

        // 找到装备的武器槽位
        const equippedSlot = weaponSlots?.find(slot => {
            if (!slot || !slot.inventory_item_id) return false;
            const slotItemId = parseInt(slot.inventory_item_id);
            const itemId = parseInt(item.id);
            return !isNaN(slotItemId) && !isNaN(itemId) && slotItemId === itemId;
        });

        if (!equippedSlot) {
            showMessage('未找到装备的武器槽位', 'error');
            return;
        }

        const response = await fetch(
            window.GameConfig
                ? window.GameConfig.getApiUrl('equipment_integrated.php')
                : '/yinian/src/api/equipment_integrated.php',
            {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=unequip_weapon&slot_number=${equippedSlot.slot_index}`,
            }
        );

        const data = await response.json();
        console.log('卸下武器响应:', data);

        if (data.success) {
            // 🔧 新增：重新加载数据后获取新属性并显示变化
            await loadEquipmentData();
            // 🔧 添加小延迟确保数据更新
            await new Promise(resolve => setTimeout(resolve, 100));
            const newStats = await getCurrentEquipmentStats();
            console.log('🔧 卸下武器后 - oldStats:', oldStats);
            console.log('🔧 卸下武器后 - newStats:', newStats);
            showAttributeChanges(oldStats, newStats);

            // 🔥 新增：更新战力显示
            await refreshCharacterPowerRating();
        } else {
            showMessage(data.message || '武器卸下失败', 'error');
        }
    } catch (error) {
        console.error('卸下武器失败:', error);
        showMessage('卸下武器失败：' + error.message, 'error');
    }
}

async function unequipItemFromPopup(item) {
    try {
        // 🔧 新增：卸下前获取当前属性
        const oldStats = await getCurrentEquipmentStats();

        const response = await fetch(
            window.GameConfig
                ? window.GameConfig.getApiUrl('equipment_integrated.php')
                : '/yinian/src/api/equipment_integrated.php',
            {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=unequip_item&inventory_item_id=${item.id}&slot_type=${item.slot_type}`,
            }
        );

        const data = await response.json();
        console.log('卸下装备响应:', data);

        if (data.success) {
            // 🔧 新增：重新加载数据后获取新属性并显示变化
            await loadEquipmentData();
            const newStats = await getCurrentEquipmentStats();
            showAttributeChanges(oldStats, newStats);

            // 🔥 新增：更新战力显示
            await refreshCharacterPowerRating();
        } else {
            showMessage(data.message || '装备卸下失败', 'error');
        }
    } catch (error) {
        console.error('卸下装备失败:', error);
        showMessage('卸下装备失败：' + error.message, 'error');
    }
}

// 使用物品回调
async function useItemFromPopup(item) {
    try {
        // 🔧 丹药使用：不需要获取旧属性，直接使用API返回的变化数据

        const response = await fetch(
            window.GameConfig
                ? window.GameConfig.getApiUrl('equipment_integrated.php')
                : '/yinian/src/api/equipment_integrated.php',
            {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=use_item&inventory_item_id=${item.id}`,
            }
        );

        const data = await response.json();
        console.log('使用物品响应:', data);

        if (data.success) {
            showMessage(data.message || '物品使用成功！', 'success');
            await loadEquipmentData(); // 重新加载数据

            // 🔥 检查是否为属性丹使用，显示属性变化弹窗
            if (data.attribute_changes) {
                const attributeChanges = data.attribute_changes;

                // 构造属性变化数据：旧属性为0，新属性为变化值
                const oldStats = {};
                const newStats = {
                    [attributeChanges.attribute_field]: attributeChanges.bonus, // 直接显示变化量
                };

                // 显示属性变化弹窗
                setTimeout(() => {
                    showAttributeChanges(oldStats, newStats, {
                        title: '丹药使用成功！',
                        description: `${attributeChanges.attribute_display}+${attributeChanges.bonus}`,
                    });
                }, 500);

                // 🔥 新增：使用属性丹后更新战力显示
                await refreshCharacterPowerRating();
            }
        } else {
            showMessage(data.message || '物品使用失败', 'error');
        }
    } catch (error) {
        console.error('使用物品失败:', error);
        showMessage('使用物品失败：' + error.message, 'error');
    }
}

// 回收物品回调 - 🔧 修复：增加数量参数支持
async function recycleItemFromPopup(item, price, quantity = 1) {
    try {
        const response = await fetch(
            window.GameConfig
                ? window.GameConfig.getApiUrl('equipment_integrated.php')
                : '/yinian/src/api/equipment_integrated.php',
            {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=recycle_item&inventory_item_id=${item.id}&recycle_quantity=${quantity}`,
            }
        );

        const data = await response.json();
        console.log('回收物品响应:', data);

        if (data.success) {
            showMessage(data.message || `物品回收成功，获得 ${price} 金币！`, 'success');
            await loadEquipmentData(); // 重新加载数据
        } else {
            showMessage(data.message || '物品回收失败', 'error');
        }
    } catch (error) {
        console.error('回收物品失败:', error);
        showMessage('回收物品失败：' + error.message, 'error');
    }
}

// 修复武器回调
async function repairWeaponFromPopup(item) {
    try {
        const response = await fetch(
            window.GameConfig
                ? window.GameConfig.getApiUrl('equipment_integrated.php')
                : '/yinian/src/api/equipment_integrated.php',
            {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=repair_weapon&inventory_item_id=${item.id}`,
            }
        );

        const data = await response.json();
        console.log('修复武器响应:', data);

        if (data.success) {
            showMessage(data.message || '武器修复成功！', 'success');
            await loadEquipmentData(); // 重新加载数据
        } else {
            showMessage(data.message || '武器修复失败', 'error');
        }
    } catch (error) {
        console.error('修复武器失败:', error);
        showMessage('修复武器失败：' + error.message, 'error');
    }
}

// 🔧 移除：战力系统已移除，改用后端API返回的attribute_changes数据

// 🔧 重构：使用通用属性变化浮窗方法，移除重复代码
// getCurrentEquipmentStats 和 showAttributeChanges 函数已移至 attribute-changes-popup.js

// 🔥 新增：角色战力加载函数
async function loadCharacterPowerRating() {
    try {
        console.log('开始加载角色战力...');

        // 🔧 调试：输出请求信息
        const apiUrl = window.GameConfig
            ? window.GameConfig.getApiUrl('power_rating.php')
            : '/yinian/src/api/power_rating.php';
        console.log('🔍 请求URL:', apiUrl);
        console.log('🔍 当前位置:', window.location.href);
        console.log('🔍 域名:', window.location.origin);

        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=get_character_power',
        });

        // 🔧 调试：输出响应信息
        console.log('🔍 响应状态:', response.status);
        console.log('🔍 最终URL:', response.url);

        const data = await response.json();
        console.log('战力API响应:', data);

        if (data.success) {
            const powerValue = data.power_rating || 0;
            const powerElement = document.getElementById('character-power-value');

            if (powerElement) {
                powerElement.textContent = powerValue;
                console.log('✅ 战力显示更新:', powerValue);
            } else {
                console.warn('❌ 未找到战力显示元素');
            }
        } else {
            console.error('战力计算失败:', data.message);
            const powerElement = document.getElementById('character-power-value');
            if (powerElement) {
                powerElement.textContent = '计算失败';
            }
        }
    } catch (error) {
        console.error('加载战力失败:', error);
        const powerElement = document.getElementById('character-power-value');
        if (powerElement) {
            powerElement.textContent = '加载失败';
        }
    }
}

// 🔥 新增：装备变化后更新战力
async function refreshCharacterPowerRating() {
    await loadCharacterPowerRating();
}
