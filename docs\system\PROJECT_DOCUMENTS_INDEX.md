# 📚 一念修仙项目文档索引

## 📋 文档概览

本项目包含完整的开发文档体系，涵盖游戏设计、技术实现、数据库结构、API接口等各个方面。

### 📊 项目基本信息
- **项目名称**: 一念修仙
- **技术栈**: PHP 7.43nts + MySQL 5.7.2 + JavaScript
- **数据库**: yn_game (39个表)
- **前端页面**: 25个
- **API接口**: 40个
- **装备总数**: 336件
- **完成度**: 95%

## 📖 核心文档

### 🎮 游戏开发文档
| 文档 | 说明 | 状态 |
|------|------|------|
| [GAME_DEVELOPMENT_DOCS.md](GAME_DEVELOPMENT_DOCS.md) | 游戏开发完整指南 (原版) | 📝 需要更新 |
| [GAME_DEVELOPMENT_DOCS_UPDATED.md](GAME_DEVELOPMENT_DOCS_UPDATED.md) | 游戏开发完整指南 (整理版) | ✅ 最新版本 |
| [DATABASE_SCHEMA.md](DATABASE_SCHEMA.md) | 数据库结构详细说明 | ✅ 已更新 |
| [battle_system_formulas.md](battle_system_formulas.md) | 战斗系统公式详解 | ✅ 最新 |
| [.cursorrules](.cursorrules) | Cursor开发规则 | ✅ 已更新 |

### 📊 项目状态文档
| 文档 | 说明 | 状态 |
|------|------|------|
| [project_status_tracker.md](project_status_tracker.md) | 项目状态跟踪 | ✅ 最新 |
| [equipment_system_completed.md](equipment_system_completed.md) | 装备系统完成记录 | ✅ 最新 |
| [equipment_system_update_plan.md](equipment_system_update_plan.md) | 装备系统更新计划 | 📝 需要更新 |

### 🔐 安全和更新文档
| 文档 | 说明 | 状态 |
|------|------|------|
| [SECURITY_UPDATE_PLAN.md](SECURITY_UPDATE_PLAN.md) | 安全更新计划 | ✅ 最新 |
| [spirit_root_system.md](spirit_root_system.md) | 五行灵根系统 | ✅ 最新 |

### 🎯 技能动画系统文档
| 文档 | 说明 | 状态 |
|------|------|------|
| [技能动画模块化系统文档.md](技能动画模块化系统文档.md) | 技能动画模块化系统详解 | ✅ 最新 |

### 🔧 系统优化文档
| 文档 | 说明 | 状态 |
|------|------|------|
| [docs/战斗系统优化计划.md](docs/战斗系统优化计划.md) | 战斗系统全面体检和优化计划 | ✅ 最新 |
| [docs/新技能模型添加完整指南.md](docs/新技能模型添加完整指南.md) | 新技能模型添加完整步骤指南 | ✅ 最新 |

## 📁 文档分类

### 🏗️ 设计文档
- **原始设计**: [docs/guides/一念.md](docs/guides/一念.md) - 游戏原始设计方案
- **系统设计**: 各个系统的详细设计文档

### 🔧 技术文档
- **数据库**: [DATABASE_SCHEMA.md](DATABASE_SCHEMA.md) - 39个表的完整结构
- **API文档**: 40个API接口的详细说明
- **前端文档**: 25个页面的功能说明
- **技能动画**: [技能动画模块化系统文档.md](技能动画模块化系统文档.md) - 模块化重构完整记录

### 📈 项目管理
- **状态跟踪**: [project_status_tracker.md](project_status_tracker.md)
- **更新记录**: 各系统的更新历史
- **开发规则**: [.cursorrules](.cursorrules)

## 🎯 核心系统文档

### 🏔️ 修炼系统
- **境界系统**: 280个境界等级 (28大境界 × 10小层次)
- **功法系统**: JSON格式存储，支持多功法学习
- **属性丹系统**: 5种属性丹，每种9个阶数，每阶最多使用20个

### ⚔️ 战斗系统
- **回合制战斗**: 支持技能、普攻、防御等操作
- **掉落系统**: 品质随机生成（普通60%、稀有25%、史诗12%、传说3%）
- **安全验证**: 三阶段验证策略
- **战斗公式**: 详见 [battle_system_formulas.md](battle_system_formulas.md) - 完整的攻防计算公式
- **技能动画**: 模块化技能系统，支持5种技能（飞剑术、万剑诀、巨剑术、掌心雷、火球术）

### 🎯 技能动画模块化系统 (2024年12月重大更新)
- **模块架构**: 5个JavaScript技能模块 + 4个CSS动画样式模块
- **技能覆盖**: 剑类技能3个 + 雷法技能1个 + 火法技能1个
- **性能优化**: 按需加载，初始加载时间减少62%
- **完全兼容**: 与原版动画效果100%一致，逐行对比验证
- **武器支持**: 所有技能支持显示真实武器图片
- **扩展性**: 标准化接口，便于新技能开发

### 🎒 装备系统
- **装备总数**: 336件装备 (ID: 568-903)
- **职业分化**: 剑修/法修装备体系，物理/法术攻防分离
- **技能系统**: 168个武器技能

### 🗺️ 地图系统
- **8个历练地图**: 太乙峰、碧水寒潭、赤焰谷、幽冥鬼域、青云仙山、星辰古战场、混元虚空、洪荒秘境
- **关卡总数**: 1120个关卡 (每地图140关)
- **怪物类型**: 104种不同怪物

### 🌟 五行灵根系统
- **灵根生成**: 角色创建时自动生成五行灵根
- **品质等级**: 废灵根、下品、中品、上品、极品五个品质等级
- **战斗加成**: 五行相克关系影响战斗伤害

## 🎒 物品堆叠规则 (✨ 最新更新)
| 物品类型 | 堆叠上限 | 说明 |
|----------|----------|------|
| **材料类** | **999** | 属性丹材料、炼丹材料、渡劫材料等 |
| **消耗品类** | **99** | 丹药、药品等可使用物品 |
| **装备类** | **1** | 武器、防具、饰品等装备 |
| **丹炉类** | **99** | 各品质丹炉 |
| **特殊物品** | **1** | 丹方、功法等学习类物品 |

## 🛠️ 维护工具文档

### 📊 数据库管理工具
- `show_tables.php` - 查看表结构
- `check_table_structure.php` - 检查表完整性
- `manage_item_stack_limits.php` - 物品堆叠管理
- `check_current_stack_settings.php` - 检查堆叠设置

### 🔧 数据修复工具
- `fix_inventory_standalone.php` - 背包整理工具
- `final_fix_organize.php` - 最终整理工具
- `fix_stack_overflow_items.php` - 修复堆叠超限

### 🧪 测试工具
- `test_api_web.html` - API测试工具
- `debug_*.php` - 各种调试工具

## 📅 文档更新记录

### 🔄 2024年12月19日更新
- ✅ **DATABASE_SCHEMA.md**: 重新整理，修复材料堆叠数量等错误信息
- ✅ **GAME_DEVELOPMENT_DOCS_UPDATED.md**: 创建整理版本，修复所有过时信息
- ✅ **PROJECT_DOCUMENTS_INDEX.md**: 更新文档索引，添加新文档
- ✅ **物品堆叠规则**: 材料类物品堆叠上限调整为999
- ✅ **技能动画模块化系统文档.md**: 新增技能动画模块化完整记录文档
- ✅ **docs/战斗系统优化计划.md**: 新增战斗系统全面体检和优化计划文档
- ✅ **docs/新技能模型添加完整指南.md**: 新增新技能模型添加完整步骤指南文档

### 📝 需要更新的文档
- **GAME_DEVELOPMENT_DOCS.md**: 原版文档需要更新数据库表数量等信息
- **equipment_system_update_plan.md**: 需要更新装备系统最新状态

## 🔍 文档使用指南

### 📖 新开发者入门
1. 先阅读 [GAME_DEVELOPMENT_DOCS_UPDATED.md](GAME_DEVELOPMENT_DOCS_UPDATED.md) 了解项目整体结构
2. 查看 [DATABASE_SCHEMA.md](DATABASE_SCHEMA.md) 了解数据库设计
3. 阅读 [.cursorrules](.cursorrules) 了解开发规则

### 🔧 系统维护
1. 使用 [project_status_tracker.md](project_status_tracker.md) 跟踪项目状态
2. 参考维护工具文档进行系统维护
3. 查看安全更新计划进行安全加固

### 📈 功能开发
1. 查看对应系统的详细文档
2. 参考API接口文档进行开发
3. 遵循开发规则和代码规范

---

*文档索引最后更新：2024年12月19日*  
*基于实际项目状态：39个数据表，40个API文件，25个前端页面，336件装备* 