/**
 * 一念修仙 - 角色类
 * 负责管理战斗中的角色（玩家和敌人）
 * 包括生命值、法力值、头像、UI更新等功能
 */

class Character {
    constructor(element, characterData = {}) {
        this.element = element;

        // 🔧 修复：检查element是否存在，避免null错误
        if (!element) {
            console.warn('Character构造函数收到null元素，使用默认配置');
            this.spriteElement = null;
            this.isEnemy = false;
        } else {
            this.spriteElement = element.querySelector('.character-sprite');
            this.isEnemy = element.classList.contains('enemy');
        }

        // 设置角色数据
        this.name = characterData.name || (this.isEnemy ? '山精' : '修仙者');
        this.level = characterData.level || 1;
        // 🔧 修复：添加境界信息
        this.realm_name = characterData.realm_name || null;
        this.realm_id = characterData.realm_id || null;
        // 🆕 新增：怪物类型属性
        this.type = characterData.type || (this.isEnemy ? 'normal' : null);
        // 🔧 修复：使用更合理的默认值，玩家和敌人区分
        this.max_hp = characterData.max_hp || (this.isEnemy ? 100 : 300);
        this.currentHp = characterData.currentHp || this.max_hp;
        this.attack = characterData.attack || 10;
        this.defense = characterData.defense || 5;

        // 🔥 新增：MP属性，使用更合理的默认值
        this.max_mp = characterData.max_mp || (this.isEnemy ? 50 : 100);
        this.currentMp = characterData.currentMp || this.max_mp;

        // 🔧 修复：只有在element存在时才更新DOM
        if (element) {
            // 更新DOM元素
            const nameElement = element.querySelector('.character-name');
            if (nameElement) {
                nameElement.textContent = this.name;
            }

            const levelElement = element.querySelector('.character-level');
            if (levelElement) {
                // 🔧 修复：优先使用真实境界名称，然后使用境界系统计算
                let realmText;
                if (this.realm_name && this.realm_name !== 'null' && this.realm_name !== '') {
                    realmText = this.realm_name;
                } else if (typeof RealmSystem !== 'undefined' && RealmSystem.getShortRealm) {
                    realmText = RealmSystem.getShortRealm(this.level);
                } else {
                    realmText = `Lv.${this.level}`;
                }
                levelElement.textContent = realmText;
            }
        }

        // 设置头像 - 处理enemy和char目录
        if (characterData.avatar) {
            this.setAvatar(characterData.avatar);
        } else {
            // 为敌人和玩家设置不同的默认头像
            if (characterData.isEnemy || this.isEnemy) {
                this.setAvatar('assets/images/enemy/yelang.png');
            } else {
                this.setAvatar('assets/images/char/ck.png');
            }
        }

        // 更新UI
        this.updateUI();
    }

    // 更新角色UI
    updateUI() {
        try {
            // 安全地获取元素
            if (!this.nameElement && this.element) {
                this.nameElement = this.element.querySelector(`.character-name`);
            }

            if (!this.levelElement && this.element) {
                this.levelElement = this.element.querySelector(`.character-level`);
            }

            if (!this.hpElement && this.element) {
                this.hpElement = this.element.querySelector(`.hp-bar`);
            }

            if (!this.hpFill && this.hpElement) {
                this.hpFill = this.hpElement.querySelector(`.hp-fill`);
            }

            // 🔥 新增：MP条元素获取
            if (!this.mpElement && this.element) {
                this.mpElement = this.element.querySelector(`.mp-bar`);
            }

            if (!this.mpFill && this.mpElement) {
                this.mpFill = this.mpElement.querySelector(`.mp-fill`);
            }

            if (!this.spriteElement && this.element) {
                this.spriteElement = this.element.querySelector(`.character-sprite`);
            }

            // 更新元素内容
            if (this.nameElement) {
                this.nameElement.textContent = this.name;

                // 🆕 根据怪物类型设置名称颜色
                if (this.isEnemy && this.type) {
                    switch (this.type) {
                        case 'normal':
                            this.nameElement.style.color = '#ffffff'; // 普通怪物：白色
                            break;
                        case 'elite':
                            this.nameElement.style.color = '#00ff00'; // 精英怪物：绿色
                            break;
                        case 'mini_boss':
                            this.nameElement.style.color = '#0088ff'; // 小Boss：蓝色
                            break;
                        case 'boss':
                            this.nameElement.style.color = '#ff6600'; // Boss：橙色
                            break;
                        default:
                            this.nameElement.style.color = '#ffffff'; // 默认：白色
                    }
                } else if (!this.isEnemy) {
                    // 玩家名称保持默认颜色
                    this.nameElement.style.color = '#ffffff';
                }
            }

            if (this.levelElement) {
                // 🔧 修复：优先使用真实境界名称，然后使用境界系统计算
                let realmText;
                if (this.realm_name && this.realm_name !== 'null' && this.realm_name !== '') {
                    realmText = this.realm_name;
                } else if (typeof RealmSystem !== 'undefined' && RealmSystem.getShortRealm) {
                    realmText = RealmSystem.getShortRealm(this.level);
                } else {
                    realmText = `Lv.${this.level}`;
                }
                this.levelElement.textContent = realmText;
            }

            // 小心地处理HP元素
            if (this.hpElement) {
                const hpValueElement = this.hpElement.querySelector(
                    `#${this.isEnemy ? 'enemy' : 'player'}-hp`
                );
                const maxHpValueElement = this.hpElement.querySelector(
                    `#${this.isEnemy ? 'enemy' : 'player'}-max-hp`
                );

                if (hpValueElement)
                    hpValueElement.textContent = Math.max(0, Math.floor(this.currentHp));
                if (maxHpValueElement) maxHpValueElement.textContent = this.max_hp;

                // 🔧 新增：显示护盾值
                const shieldValueElement = this.hpElement.querySelector(
                    `#${this.isEnemy ? 'enemy' : 'player'}-shield`
                );
                if (shieldValueElement) {
                    const shieldValue = this.shield || 0;
                    shieldValueElement.textContent = shieldValue > 0 ? `🛡️${shieldValue}` : '';
                    shieldValueElement.style.display = shieldValue > 0 ? 'inline' : 'none';
                }
            }

            // 更新血条
            if (this.hpFill) {
                const hpPercentage = Math.max(
                    0,
                    Math.min(100, (this.currentHp / this.max_hp) * 100)
                );
                this.hpFill.style.width = `${hpPercentage}%`;

                // 根据血量变化血条颜色
                if (hpPercentage > 50) {
                    this.hpFill.style.backgroundColor = '#4CAF50'; // 绿色
                } else if (hpPercentage > 25) {
                    this.hpFill.style.backgroundColor = '#FFC107'; // 黄色
                } else {
                    this.hpFill.style.backgroundColor = '#F44336'; // 红色
                }
            }

            // 🔥 新增：更新MP条
            if (this.mpElement) {
                const mpValueElement = this.mpElement.querySelector(
                    `#${this.isEnemy ? 'enemy' : 'player'}-mp`
                );
                const maxMpValueElement = this.mpElement.querySelector(
                    `#${this.isEnemy ? 'enemy' : 'player'}-max-mp`
                );

                if (mpValueElement)
                    mpValueElement.textContent = Math.max(0, Math.floor(this.currentMp));
                if (maxMpValueElement) maxMpValueElement.textContent = this.max_mp;
            }

            // 🔥 新增：更新MP条填充
            if (this.mpFill) {
                const mpPercentage = Math.max(
                    0,
                    Math.min(100, (this.currentMp / this.max_mp) * 100)
                );
                this.mpFill.style.width = `${mpPercentage}%`;

                // 根据MP量变化MP条颜色
                if (mpPercentage > 50) {
                    this.mpFill.style.backgroundColor = '#2196F3'; // 蓝色
                } else if (mpPercentage > 25) {
                    this.mpFill.style.backgroundColor = '#9C27B0'; // 紫色
                } else {
                    this.mpFill.style.backgroundColor = '#607D8B'; // 深灰色
                }
            }
        } catch (error) {
            console.error('更新角色UI时出错:', error);
        }
    }

    // 设置角色头像
    setAvatar(avatarSrc) {
        try {
            console.log('设置头像:', avatarSrc, '角色类型:', this.isEnemy ? '敌人' : '玩家');

            // 🔧 修复：使用图片路径管理器处理头像路径
            let finalAvatarSrc;
            if (window.ImagePathManager) {
                // 🏆 特殊处理：竞技场真实玩家对手应该使用角色图片路径
                const isArenaPlayer =
                    this.isEnemy && window.battleSystem?.dataManager?.enemyData?.isPlayer;

                if (this.isEnemy && !isArenaPlayer) {
                    // 普通怪物使用敌人图片路径
                    finalAvatarSrc = window.ImagePathManager.getEnemyImage(avatarSrc);
                } else {
                    // 玩家或竞技场真实玩家对手使用角色图片路径
                    finalAvatarSrc = window.ImagePathManager.getCharacterImage(avatarSrc);
                }
                console.log('图片路径管理器处理结果:', finalAvatarSrc);
            } else {
                // 备用方案
                console.warn('图片路径管理器未加载，使用备用方案');
                if (!avatarSrc || avatarSrc === 'undefined' || avatarSrc === 'null') {
                    finalAvatarSrc = this.isEnemy
                        ? 'assets/images/enemy/yelang.png'
                        : 'assets/images/char/ck.png';
                } else if (avatarSrc.includes('assets/images/')) {
                    finalAvatarSrc = avatarSrc;
                } else if (avatarSrc.startsWith('../../images/')) {
                    // 转换旧格式路径为新格式
                    finalAvatarSrc = avatarSrc.replace('../../images/', 'assets/images/');
                } else if (avatarSrc.endsWith('.png') && !avatarSrc.includes('/')) {
                    // 纯文件名，添加目录前缀
                    const dir = this.isEnemy ? 'enemy' : 'char';
                    finalAvatarSrc = `assets/images/${dir}/${avatarSrc}`;
                } else {
                    finalAvatarSrc = avatarSrc;
                }
            }

            console.log('最终头像路径:', finalAvatarSrc);

            // 🔧 修复：获取正确的角色精灵元素（使用character-sprite而不是character-avatar）
            const avatarElement = this.element.querySelector('.character-sprite');
            if (!avatarElement) {
                console.error('未找到角色精灵元素');
                return;
            }

            // 创建图片对象测试加载
            const img = new Image();
            img.onload = () => {
                console.log('✅ 头像加载成功:', finalAvatarSrc);
                avatarElement.style.backgroundImage = `url('${finalAvatarSrc}')`;
                avatarElement.style.backgroundSize = 'cover';
                avatarElement.style.backgroundPosition = 'center';
                avatarElement.style.backgroundRepeat = 'no-repeat';
                avatarElement.style.display = 'block';
            };

            img.onerror = () => {
                console.error('❌ 头像加载失败:', finalAvatarSrc);
                // 使用默认图片
                const defaultSrc = this.isEnemy
                    ? 'assets/images/enemy/yelang.png'
                    : 'assets/images/char/ck.png';
                console.log('使用默认头像:', defaultSrc);
                avatarElement.style.backgroundImage = `url('${defaultSrc}')`;
                avatarElement.style.backgroundSize = 'cover';
                avatarElement.style.backgroundPosition = 'center';
                avatarElement.style.backgroundRepeat = 'no-repeat';
                avatarElement.style.display = 'block';
            };

            // 开始加载图片
            img.src = finalAvatarSrc;
        } catch (error) {
            console.error('设置头像时发生错误:', error);
            const avatarElement = this.element.querySelector('.character-sprite');
            if (avatarElement) {
                const defaultSrc = this.isEnemy
                    ? 'assets/images/enemy/yelang.png'
                    : 'assets/images/char/ck.png';
                avatarElement.style.backgroundImage = `url('${defaultSrc}')`;
                avatarElement.style.backgroundSize = 'cover';
                avatarElement.style.backgroundPosition = 'center';
                avatarElement.style.backgroundRepeat = 'no-repeat';
                avatarElement.style.display = 'block';
            }
        }
    }

    // 更新生命值
    updateHp(newHp) {
        this.currentHp = Math.max(0, newHp);
        this.updateUI();
    }

    // 受到伤害 - 🔧 新版本：支持暴击、MISS和护盾
    takeDamage(damageData) {
        // 🔧 兼容旧版本：如果传入的是数字，直接处理
        if (typeof damageData === 'number') {
            const finalDamage = this.applyShieldDamage(damageData);
            this.currentHp = Math.max(0, this.currentHp - finalDamage);
            this.updateUI();
            this.showDamageNumber(finalDamage, false, false);
            return this.currentHp <= 0;
        }

        // 🔧 新版本：处理完整的战斗结果
        const battleResult = damageData;

        // 🔧 修复：统一使用showDamageNumber处理所有情况
        if (battleResult.isMiss) {
            // 未命中时显示MISS效果
            this.showDamageNumber(0, false, true);

            // 🔥 新增：显示未命中战报
            const target = this.isEnemy ? 'enemy' : 'player';
            window.battleReportManager?.showMissReport(target);

            return false; // MISS不会造成死亡
        }

        // 正常伤害处理 - 🔧 新增：应用护盾减免
        const originalDamage = battleResult.finalDamage;
        const finalDamage = this.applyShieldDamage(originalDamage);

        this.currentHp = Math.max(0, this.currentHp - finalDamage);
        this.updateUI();

        // 显示伤害数字（包含暴击效果）
        this.showDamageNumber(finalDamage, battleResult.isCritical, false);

        // 🔥 新增：显示伤害战报
        const target = this.isEnemy ? 'enemy' : 'player';
        window.battleReportManager?.showDamageReport(finalDamage, battleResult.isCritical, target);

        return this.currentHp <= 0;
    }

    // 🔧 新增：护盾伤害吸收机制
    applyShieldDamage(damage) {
        if (!this.shield || this.shield <= 0) {
            return damage; // 没有护盾，直接返回原伤害
        }

        const originalShield = this.shield;

        if (this.shield >= damage) {
            // 护盾足够吸收全部伤害
            this.shield -= damage;
            console.log(`🛡️ 护盾吸收伤害: ${damage}, 剩余护盾: ${this.shield}`);

            // 显示护盾吸收效果
            this.showShieldAbsorbEffect(damage);

            return 0; // 伤害被完全吸收
        } else {
            // 护盾不足，部分伤害穿透
            const remainingDamage = damage - this.shield;
            console.log(`🛡️ 护盾吸收: ${this.shield}, 穿透伤害: ${remainingDamage}`);

            // 显示护盾吸收效果
            this.showShieldAbsorbEffect(this.shield);

            this.shield = 0; // 护盾耗尽

            return remainingDamage;
        }
    }

    // 🔧 新增：显示护盾吸收效果
    showShieldAbsorbEffect(absorbedDamage) {
        const shieldElement = document.createElement('div');
        shieldElement.className = 'shield-absorb-effect';
        shieldElement.textContent = `护盾-${absorbedDamage}`;

        // 添加到角色容器
        const characterContainer = this.isEnemy
            ? document.querySelector('.enemy-character')
            : document.querySelector('.player-character');

        if (characterContainer) {
            characterContainer.appendChild(shieldElement);

            // 动画效果
            setTimeout(() => {
                shieldElement.style.opacity = '0';
                shieldElement.style.transform = 'translateY(-30px)';
            }, 100);

            // 清理元素
            setTimeout(() => {
                if (shieldElement.parentNode) {
                    shieldElement.parentNode.removeChild(shieldElement);
                }
            }, 1500);
        }
    }

    // 🔧 新增：显示伤害数字动画
    showDamageNumber(damage, isCritical = false, isMiss = false) {
        const damageElement = document.createElement('div');

        // 根据伤害类型设置不同的样式
        if (isMiss) {
            damageElement.className = 'miss-effect';
            damageElement.textContent = '未命中';
        } else if (isCritical) {
            damageElement.className = 'damage-number critical';
            damageElement.textContent = `-${damage}`;
        } else {
            damageElement.className = 'damage-number';
            damageElement.textContent = `-${damage}`;
        }

        // 获取角色位置
        const characterRect = this.element.getBoundingClientRect();
        const battleContainer = document.querySelector('.battle-container');
        const containerRect = battleContainer.getBoundingClientRect();

        // 设置伤害数字位置（在角色中央偏上）
        damageElement.style.left = `${
            characterRect.left - containerRect.left + characterRect.width / 2
        }px`;
        damageElement.style.top = `${
            characterRect.top - containerRect.top + characterRect.height / 3
        }px`;

        // 添加到效果容器
        const effectsContainer = document.querySelector('.effects-container');
        if (effectsContainer) {
            effectsContainer.appendChild(damageElement);

            // 1.5秒后移除
            setTimeout(() => {
                if (damageElement && damageElement.parentNode) {
                    damageElement.remove();
                }
            }, 1500);
        }
    }

    // 恢复生命值
    heal(amount) {
        this.currentHp = Math.min(this.max_hp, this.currentHp + amount);
        this.updateUI();
    }

    // 🔥 新增：更新MP
    updateMp(newMp) {
        this.currentMp = Math.max(0, Math.min(this.max_mp, newMp));
        this.updateUI();
    }

    // 🔥 新增：消耗MP
    consumeMp(amount) {
        const oldMp = this.currentMp;
        this.currentMp = Math.max(0, this.currentMp - amount);
        this.updateUI();
        return oldMp - this.currentMp; // 返回实际消耗的MP
    }

    // 🔥 新增：恢复MP
    restoreMp(amount) {
        this.currentMp = Math.min(this.max_mp, this.currentMp + amount);
        this.updateUI();
    }
}

// 导出Character类供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Character;
} else {
    window.Character = Character;
}
