<?php
/**
 * 套装特殊效果重新设计
 * 基于回合制游戏特点，设计简单直接的特殊效果
 */

// 新的套装特殊效果设计方案
$newSetEffects = [
    // 攻击类效果
    'attack_effects' => [
        '暴击伤害' => '攻击时有15%概率造成200%伤害',
        '连击' => '攻击时有10%概率连续攻击2次',
        '破甲' => '攻击时有20%概率无视敌人50%防御',
        '吸血' => '攻击造成伤害的30%转化为生命值',
        '必杀' => '攻击时有5%概率造成300%伤害',
    ],
    
    // 防御类效果
    'defense_effects' => [
        '格挡' => '受到攻击时有25%概率减少50%伤害',
        '反击' => '受到攻击时有20%概率反弹30%伤害',
        '护盾' => '战斗开始时获得最大生命值20%的护盾',
        '坚韧' => '受到攻击时有15%概率免疫本次伤害',
        '荆棘' => '受到攻击时对敌人造成固定100点伤害',
    ],
    
    // 控制类效果
    'control_effects' => [
        '眩晕' => '攻击时有10%概率使敌人眩晕1回合',
        '减速' => '攻击时有20%概率降低敌人20%攻击力持续3回合',
        '虚弱' => '攻击时有15%概率降低敌人30%防御力持续2回合',
        '沉默' => '攻击时有8%概率使敌人无法使用特殊技能1回合',
    ],
    
    // 辅助类效果
    'support_effects' => [
        '回复' => '每回合结束时恢复最大生命值的5%',
        '狂暴' => '生命值低于30%时攻击力提升50%',
        '专注' => '连续3回合未受到伤害时攻击力提升30%',
        '不屈' => '生命值低于20%时受到伤害减少40%',
        '重生' => '死亡时有20%概率恢复50%生命值复活(每场战斗限1次)',
    ]
];

try {
    $pdo = new PDO('mysql:host=127.0.0.1;port=3306;dbname=yn_game;charset=utf8mb4', 'root', 'mjlxz159');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== 套装特殊效果重新设计方案 ===\n\n";
    
    // 显示新效果分类
    foreach ($newSetEffects as $category => $effects) {
        $categoryName = [
            'attack_effects' => '🗡️ 攻击类效果',
            'defense_effects' => '🛡️ 防御类效果', 
            'control_effects' => '🎯 控制类效果',
            'support_effects' => '✨ 辅助类效果'
        ][$category];
        
        echo "$categoryName:\n";
        foreach ($effects as $name => $description) {
            echo "  • $name: $description\n";
        }
        echo "\n";
    }
    
    // 获取现有套装列表
    $stmt = $pdo->query("SELECT id, set_name FROM game_item_sets ORDER BY id");
    $sets = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "=== 现有套装重新分配效果建议 ===\n\n";
    
    // 为每个套装分配合适的特殊效果
    $setAssignments = [
        '金衣上人' => [
            'four_piece' => ['type' => 'defense', 'effect' => '格挡', 'desc' => '受到攻击时有25%概率减少50%伤害'],
            'six_piece' => ['type' => 'support', 'effect' => '不屈', 'desc' => '生命值低于20%时受到伤害减少40%']
        ],
        '白袍剑君' => [
            'four_piece' => ['type' => 'attack', 'effect' => '破甲', 'desc' => '攻击时有20%概率无视敌人50%防御'],
            'six_piece' => ['type' => 'attack', 'effect' => '暴击伤害', 'desc' => '攻击时有15%概率造成200%伤害']
        ],
        '月华法尊' => [
            'four_piece' => ['type' => 'control', 'effect' => '虚弱', 'desc' => '攻击时有15%概率降低敌人30%防御力持续2回合'],
            'six_piece' => ['type' => 'attack', 'effect' => '吸血', 'desc' => '攻击造成伤害的30%转化为生命值']
        ],
        '紫霞仙子' => [
            'four_piece' => ['type' => 'support', 'effect' => '回复', 'desc' => '每回合结束时恢复最大生命值的5%'],
            'six_piece' => ['type' => 'defense', 'effect' => '护盾', 'desc' => '战斗开始时获得最大生命值20%的护盾']
        ],
        '青龙战甲' => [
            'four_piece' => ['type' => 'defense', 'effect' => '反击', 'desc' => '受到攻击时有20%概率反弹30%伤害'],
            'six_piece' => ['type' => 'attack', 'effect' => '连击', 'desc' => '攻击时有10%概率连续攻击2次']
        ],
        '凤凰羽衣' => [
            'four_piece' => ['type' => 'control', 'effect' => '眩晕', 'desc' => '攻击时有10%概率使敌人眩晕1回合'],
            'six_piece' => ['type' => 'support', 'effect' => '重生', 'desc' => '死亡时有20%概率恢复50%生命值复活(每场战斗限1次)']
        ]
    ];
    
    foreach ($sets as $set) {
        $setName = $set['set_name'];
        echo "【{$setName}】:\n";
        
        if (isset($setAssignments[$setName])) {
            $assignment = $setAssignments[$setName];
            echo "  4件套: {$assignment['four_piece']['desc']}\n";
            echo "  6件套: {$assignment['six_piece']['desc']}\n";
        } else {
            echo "  建议分配: 待定\n";
        }
        echo "\n";
    }
    
    echo "=== 设计原则 ===\n";
    echo "✅ 所有效果都基于回合制战斗\n";
    echo "✅ 概率触发简单明确\n";
    echo "✅ 效果持续时间以回合计算\n";
    echo "✅ 避免复杂的计数机制\n";
    echo "✅ 效果数值平衡合理\n";
    echo "✅ 每个套装有独特的战斗风格\n";
    
} catch (Exception $e) {
    echo '错误: ' . $e->getMessage() . "\n";
}
?>
