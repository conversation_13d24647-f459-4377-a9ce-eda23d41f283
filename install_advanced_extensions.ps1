# 一念修仙项目高级扩展安装脚本
# 包含AI工具、MCP支持、安全扫描等高级功能

Write-Host "开始安装一念修仙项目高级扩展..." -ForegroundColor Green

# 检查VSCode是否安装
$vscodeCmd = Get-Command code -ErrorAction SilentlyContinue
if (-not $vscodeCmd) {
    Write-Host "错误: 未找到VSCode命令行工具 'code'。" -ForegroundColor Red
    exit 1
}

# 高级扩展列表
$advancedExtensions = @(
    # AI和智能开发工具
    "github.copilot",
    "github.copilot-chat", 
    "ms-vscode.vscode-ai-toolkit",
    
    # 高级调试和分析
    "ms-vscode.vscode-profile-table",
    "ms-python.debugpy",
    "ms-vscode.vscode-speech",
    
    # 代码质量和安全
    "ms-vscode.vscode-security-scan",
    "sonarsource.sonarlint-vscode",
    "snyk-security.snyk-vulnerability-scanner",
    
    # 性能和监控
    "ms-vscode.vscode-performance-toolkit",
    "wayou.vscode-todo-highlight",
    "gruntfuggly.todo-tree",
    
    # 协作和文档
    "ms-vsliveshare.vsliveshare",
    "yzhang.markdown-all-in-one",
    "davidanson.vscode-markdownlint",
    
    # 容器和部署
    "ms-vscode-remote.remote-containers",
    "ms-azuretools.vscode-docker",
    
    # 数据库高级工具
    "cweijan.vscode-database-client2",
    "bajdzis.vscode-database",
    
    # 额外的Web开发工具
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "formulahendry.code-runner",
    "ms-vscode.vscode-json",
    
    # Git增强
    "mhutchie.git-graph",
    "donjayamanne.githistory",
    
    # 主题和美化
    "pkief.material-icon-theme",
    "zhuangtongfa.material-theme",
    "oderwat.indent-rainbow"
)

$successCount = 0
$failCount = 0

Write-Host "准备安装 $($advancedExtensions.Count) 个高级扩展..." -ForegroundColor Yellow

foreach ($extension in $advancedExtensions) {
    Write-Host "正在安装: $extension" -ForegroundColor Cyan
    
    try {
        $result = & code --install-extension $extension --force 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ 成功安装: $extension" -ForegroundColor Green
            $successCount++
        } else {
            Write-Host "✗ 安装失败: $extension - $result" -ForegroundColor Red
            $failCount++
        }
    } catch {
        Write-Host "✗ 安装异常: $extension - $($_.Exception.Message)" -ForegroundColor Red
        $failCount++
    }
    
    Start-Sleep -Milliseconds 800
}

# 安装MCP相关工具
Write-Host "`n开始配置MCP (Model Context Protocol) 支持..." -ForegroundColor Yellow

# 检查Node.js
$nodeCmd = Get-Command node -ErrorAction SilentlyContinue
if ($nodeCmd) {
    Write-Host "✓ Node.js 已安装: $(node --version)" -ForegroundColor Green
    
    # 安装MCP服务器
    $mcpServers = @(
        "@modelcontextprotocol/server-filesystem",
        "@modelcontextprotocol/server-sqlite", 
        "@modelcontextprotocol/server-brave-search",
        "@modelcontextprotocol/server-git"
    )
    
    foreach ($server in $mcpServers) {
        Write-Host "安装MCP服务器: $server" -ForegroundColor Cyan
        try {
            & npm install -g $server
            Write-Host "✓ MCP服务器安装成功: $server" -ForegroundColor Green
        } catch {
            Write-Host "✗ MCP服务器安装失败: $server" -ForegroundColor Red
        }
    }
} else {
    Write-Host "⚠ Node.js 未安装，跳过MCP配置" -ForegroundColor Yellow
    Write-Host "请安装Node.js以启用MCP功能: https://nodejs.org/" -ForegroundColor Yellow
}

# 配置GitHub Copilot (如果需要)
Write-Host "`n配置GitHub Copilot..." -ForegroundColor Yellow
Write-Host "请在VSCode中使用 Ctrl+Shift+P -> 'GitHub Copilot: Sign In' 登录" -ForegroundColor Cyan

Write-Host "`n=== 高级扩展安装完成 ===" -ForegroundColor Green
Write-Host "成功安装: $successCount 个扩展" -ForegroundColor Green
Write-Host "安装失败: $failCount 个扩展" -ForegroundColor Red

if ($failCount -gt 0) {
    Write-Host "`n建议手动安装失败的扩展，或检查网络连接。" -ForegroundColor Yellow
}

Write-Host "`n下一步操作:" -ForegroundColor Cyan
Write-Host "1. 重启VSCode以加载所有扩展" -ForegroundColor White
Write-Host "2. 使用 Ctrl+Shift+P -> 'GitHub Copilot: Sign In' 登录Copilot" -ForegroundColor White
Write-Host "3. 配置SonarLint和Snyk安全扫描" -ForegroundColor White
Write-Host "4. 打开 .vscode/workspace.code-workspace 使用工作区模式" -ForegroundColor White

Write-Host "`n享受强大的AI辅助开发体验！" -ForegroundColor Green
