# 🎯 一念修仙技能开发指南 (v3.0)

## 📋 概述

基于**战斗系统v3.0架构**的技能开发指南，采用统一配置系统、简化映射、数据库驱动的全新架构。

## 🚀 v3.0 重大更新 (2024年12月)

### ✨ 核心特性
- **统一配置系统**：所有技能映射集中在 `skill-config.js` 中管理
- **数据库驱动**：完全由后台 `animation_model` 字段控制技能动画
- **简化映射**：删除复杂的别名映射和特殊判断逻辑
- **英文类名规范**：所有JavaScript类名必须使用英文

### 🎯 核心原则
- **单一数据源**：只有 `skill-config.js` 负责 animation_model 映射
- **完全可控**：您可以随意配置哪个技能用哪个动画
- **降级保护**：统一配置不可用时自动降级到本地配置
- **无冗余映射**：消除了多处重复的映射配置

## 🏗️ 技能系统架构 (v3.0)

### 📁 文件结构
```
public/assets/js/battle/skills/
├── skill-config.js                # 🌟 统一配置系统（v3.0核心）
├── skill-loader.js                # 技能加载器（降级方案）
├── base-skill.js                  # 技能基类
├── feijian-skill.js               # 飞剑术（独立）
├── wanjianjue-skill.js            # 万剑诀（独立）
├── fire-skills.js                 # 火法技能模块
├── lightning-skills.js            # 雷法技能模块
└── {新技能}-skill.js              # 新技能文件

public/assets/css/battle/skills/
├── base-animations.css            # 基础动画
├── feijian-animations.css         # 飞剑术动画
├── fire-animations.css            # 火法动画
└── {新技能}-animations.css       # 新技能动画
```

### 🔧 统一配置系统 (skill-config.js)

```javascript
class SkillConfig {
    constructor() {
        // 核心映射：animation_model → 技能名称
        this.animationModelMapping = {
            'feijian': '飞剑术',
            'wanjianjue': '万剑诀',
            'zhangxinlei': '掌心雷',
            'huoqiushu': '火球术',
            'hengzhan': '横斩'
        };

        // 技能实现配置：技能名称 → 模块配置
        this.skillImplementationMapping = {
            '飞剑术': { 
                module: 'feijian-skill', 
                class: 'FeiJianSkill',        // ✅ 英文类名
                css: 'feijian-animations',
                animationModel: 'feijian'
            },
            '火球术': { 
                module: 'fire-skills', 
                class: 'HuoQiuShuSkill',      // ✅ 英文类名
                css: 'fire-animations',
                animationModel: 'huoqiushu'
            }
        };
    }
}
```

## 🛠️ 技能开发步骤

### 步骤1：确定技能信息

#### 1.1 命名规范 (v3.0 强制英文)
- **类名**: 英文拼音 + "Skill" (如: `HuoQiuShuSkill`) ❌不能用中文
- **文件名**: 技能英文名 + "-skill.js" (如: `huoqiu-skill.js`)
- **CSS文件**: 技能英文名 + "-animations.css"

#### 1.2 数据库配置
确保数据库中 `item_skills` 表的 `animation_model` 字段配置正确：
```sql
UPDATE item_skills 
SET animation_model = 'huoqiu' 
WHERE skill_name = '火球术';
```

### 步骤2：创建技能文件

#### 2.1 JavaScript技能类
创建 `huoqiu-skill.js`:

```javascript
/**
 * 火球术技能模块
 * 对应 animation_model = 'huoqiu'
 */

/**
 * 火球术技能类
 * ✅ 类名必须使用英文
 */
class HuoQiuSkill extends BaseSkill {
    async execute(skillData, weaponImage) {
        // 使用数据库中的真实技能名称
        const skillName = skillData?.skillName || '火球术';
        await this.showSkillShout(skillName);
        await this.createHuoQiu(weaponImage);
    }

    async createHuoQiu(weaponImage) {
        // 获取角色位置
        const casterPos = this.getCharacterPosition(true);
        const targetPos = this.getCharacterPosition(false);
        
        // 创建技能容器
        const container = this.createElement('div', {
            className: 'huoqiu-container',
            style: {
                position: 'absolute',
                top: '0',
                left: '0',
                width: '100%',
                height: '100%',
                pointerEvents: 'none',
                zIndex: 1000
            }
        });
        
        this.effectsContainer.appendChild(container);
        
        try {
            // 技能动画实现
            // 1. 蓄力阶段
            await this.createChargeEffect(container, casterPos);
            
            // 2. 发射阶段
            await this.createFireball(container, casterPos, targetPos);
            
            // 3. 击中效果
            this.createHitEffect(targetPos.x, targetPos.y, true);
            
        } finally {
            // 清理容器
            setTimeout(() => {
                if (container && container.parentNode) {
                    container.parentNode.removeChild(container);
                }
            }, 100);
        }
    }
}

// ✅ 导出技能类（英文类名）
window.HuoQiuSkills = { HuoQiuSkill };
```

#### 2.2 CSS动画样式
创建 `huoqiu-animations.css`:

```css
/* 火球术技能动画 */
.huoqiu-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1000;
}

/* 蓄力效果 */
@keyframes huoqiu-charge {
    0% { 
        transform: translate(-50%, -50%) scale(0); 
        opacity: 0; 
    }
    100% { 
        transform: translate(-50%, -50%) scale(1); 
        opacity: 1; 
    }
}

/* 火球飞行 */
@keyframes huoqiu-fly {
    0% { 
        left: var(--startX); 
        top: var(--startY); 
    }
    100% { 
        left: var(--targetX); 
        top: var(--targetY); 
    }
}

/* 移动端适配 */
@media (max-width: 768px) {
    .huoqiu-container {
        /* 移动端调整 */
    }
}
```

### 步骤3：更新统一配置

在 `skill-config.js` 中添加新技能：

```javascript
// 添加到 animationModelMapping
this.animationModelMapping = {
    // ... 现有映射 ...
    'huoqiu': '火球术',  // 新增
};

// 添加到 skillImplementationMapping
this.skillImplementationMapping = {
    // ... 现有配置 ...
    '火球术': { 
        module: 'huoqiu-skill',          // 文件名（不含.js）
        class: 'HuoQiuSkill',            // ✅ 英文类名
        css: 'huoqiu-animations',        // CSS文件名
        animationModel: 'huoqiu'         // animation_model值
    },
};
```

## 🎯 开发核心规范

### ✅ 必须遵守

1. **英文类名**：所有JavaScript类名必须使用英文
   - ✅ `class HuoQiuSkill`
   - ❌ `class 火球Skill`

2. **统一配置**：新技能必须在 `skill-config.js` 中配置

3. **正确导出**：
   ```javascript
   window.{ModuleName}Skills = { SkillClass };
   ```

4. **数据库驱动**：animation_model 字段决定使用哪个动画

### 🚫 已废弃

- ❌ 别名映射系统
- ❌ 中文类名
- ❌ 复杂的特殊判断逻辑
- ❌ 多处重复映射配置

## 🔍 调试验证

### 检查配置
```javascript
// 控制台检查
console.log(window.SkillConfig.getSkillConfig('火球术'));
console.log(window.SkillConfig.getAllSkillNames());
```

### 开发清单
- [ ] 类名使用英文
- [ ] 在 skill-config.js 中配置
- [ ] 创建对应CSS文件
- [ ] 正确导出技能类
- [ ] 数据库 animation_model 配置
- [ ] 实战测试验证

## 🎮 调用流程

```
数据库 animation_model → SkillConfig → 技能名称 → SkillLoader → 技能类实例
       ↓
   'huoqiu' → '火球术' → huoqiu-skill.js → HuoQiuSkill
```

现在系统完全由数据库控制，您可以随意配置技能使用任何动画！

---
*更新日期: 2024年12月*  
*版本: v3.0 统一配置系统* 