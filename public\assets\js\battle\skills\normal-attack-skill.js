/**
 * 普通攻击技能动画 - 怪物技能模板版本
 * 基于怪物技能开发模板重新实现，确保双向兼容
 */
class NormalAttackSkill extends BaseSkill {
    constructor(battleSystem, isEnemySkill = false) {
        super(battleSystem);
        this.skillName = '普通攻击';
        this.isEnemySkill = isEnemySkill;
        
        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('normal-attack', `🌪️ 普通攻击技能实例创建 (isEnemySkill: ${isEnemySkill})`);
        }
    }

    async execute(skillData, weaponImage) {
        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('normal-attack', '🌪️ 普通攻击技能开始执行，isEnemySkill:', this.isEnemySkill);
            window.BattleDebugConfig.log('normal-attack', '🌪️ 技能数据:', skillData);
            window.BattleDebugConfig.log('normal-attack', '🌪️ 武器图片:', weaponImage);
        }
        
        // 🔧 修复：检查敌人类型，决定技能喊话
        const isArenaPlayer = skillData?.isArenaPlayer || 
                             (skillData?.isEnemy && !skillData?.isMonsterSkill) ||
                             (skillData?.isEnemySkill && skillData?.skillName && skillData?.skillName !== '普通攻击');
        
        let skillShoutName;
        if (isArenaPlayer) {
            // 🏆 竞技场玩家对手：使用实际技能名称
            skillShoutName = skillData?.skillName || skillData?.displayName || '基础攻击';
            console.log('🏆 [竞技场] 玩家对手使用技能:', skillShoutName);
        } else {
            // 🐺 怪物对手：使用怪物攻击喊话
            skillShoutName = '妖爪撕裂！';
            console.log('🐺 [怪物] 使用普通攻击');
        }
        
        // 🔧 修复：不重复显示喊话（在battle-flow-manager中已经显示）
        // await this.showSkillShout(skillShoutName);
        
        await this.createNormalAttack(weaponImage, skillData);
    }
    
    async createNormalAttackAnimation(weaponImage) {
        // 🔧 动态判断位置映射
        let casterPos, targetPos;
        
        if (this.isEnemySkill) {
            // 敌方技能：敌人是施法者，玩家是目标
            casterPos = this.getCharacterPosition(false); // 敌人位置
            targetPos = this.getCharacterPosition(true);  // 玩家位置
        } else {
            // 我方技能：玩家是施法者，敌人是目标
            casterPos = this.getCharacterPosition(true);  // 玩家位置
            targetPos = this.getCharacterPosition(false); // 敌人位置
        }
        
        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('normal-attack', '🌪️ 位置信息:', {
                isEnemySkill: this.isEnemySkill,
                casterPos: casterPos,
                targetPos: targetPos
            });
        }
        
        // 创建动画容器
        const container = this.createElement('normal-attack-container', {
            style: {
                position: 'absolute',
                top: '0',
                left: '0',
                width: '100%',
                height: '100%',
                pointerEvents: 'none',
                zIndex: 1000
            }
        });
        
        this.effectsContainer.appendChild(container);
        
        try {
            // 第一阶段：蓄力准备
            await this.createChargePhase(container, casterPos, weaponImage);
            
            // 第二阶段：攻击发射
            await this.createAttackPhase(container, casterPos, targetPos, weaponImage);
            
            // 第三阶段：击中效果
            await this.createHitPhase(container, targetPos);
            
        } finally {
            // 清理动画容器
            setTimeout(() => {
                if (container && container.parentNode) {
                    container.parentNode.removeChild(container);
                }
            }, 100);
        }
    }
    
    // 第一阶段：蓄力准备
    async createChargePhase(container, casterPos, weaponImage) {
        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('normal-attack', '🌪️ 第一阶段：蓄力准备开始');
        }
        
        // 蓄力核心
        const chargeCore = this.createElement('normal-attack-charge-core', {
            style: {
                position: 'absolute',
                left: casterPos.x + 'px',
                top: casterPos.y + 'px',
                transform: 'translate(-50%, -50%)'
            }
        });
        container.appendChild(chargeCore);
        
        // 如果有武器图片，添加武器发光效果
        if (weaponImage) {
            const weaponSprite = this.createElement('normal-attack-weapon-sprite', {
                style: {
                    position: 'absolute',
                    width: '50px',
                    height: '50px',
                    left: casterPos.x + 'px',
                    top: casterPos.y + 'px',
                    transform: 'translate(-50%, -50%)',
                    backgroundImage: `url(${weaponImage})`,
                    backgroundSize: 'contain',
                    backgroundRepeat: 'no-repeat',
                    backgroundPosition: 'center'
                }
            });
            container.appendChild(weaponSprite);
            
            // 延迟清理武器精灵
            setTimeout(() => {
                if (weaponSprite && weaponSprite.parentNode) {
                    weaponSprite.parentNode.removeChild(weaponSprite);
                }
            }, 800);
        }
        
        // 蓄力时间
        await this.wait(600);
        
        // 清理蓄力核心
        setTimeout(() => {
            if (chargeCore && chargeCore.parentNode) {
                chargeCore.parentNode.removeChild(chargeCore);
            }
        }, 100);
        
        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('normal-attack', '🌪️ 第一阶段：蓄力准备完成');
        }
    }
    
    // 第二阶段：攻击发射
    async createAttackPhase(container, casterPos, targetPos, weaponImage) {
        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('normal-attack', '🌪️ 第二阶段：攻击发射开始');
        }
        
        // 计算飞行路径
        const distance = Math.sqrt(
            Math.pow(targetPos.x - casterPos.x, 2) + 
            Math.pow(targetPos.y - casterPos.y, 2)
        );
        const angle = Math.atan2(
            targetPos.y - casterPos.y, 
            targetPos.x - casterPos.x
        ) * 180 / Math.PI;
        
        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('normal-attack', '🌪️ 飞行参数:', {
                distance: distance,
                angle: angle,
                casterPos: casterPos,
                targetPos: targetPos
            });
        }
        
        // 创建攻击投射物
        const projectile = this.createElement('normal-attack-projectile', {
            style: {
                position: 'absolute',
                left: casterPos.x + 'px',
                top: casterPos.y + 'px',
                transform: `translate(-50%, -50%) rotate(${angle}deg)`
            },
            cssVariables: {
                '--startX': casterPos.x + 'px',
                '--startY': casterPos.y + 'px',
                '--targetX': targetPos.x + 'px',
                '--targetY': targetPos.y + 'px',
                '--angle': angle + 'deg'
            }
        });
        container.appendChild(projectile);
        
        // 如果有武器图片，添加到投射物上
        if (weaponImage) {
            this.addWeaponImage(projectile, weaponImage);
            // 🗡️ 动态调整武器图片角度
            const weaponImg = projectile.querySelector('.weapon-image');
            if (weaponImg) {
                weaponImg.style.transform = `rotate(${this.calculateInitialSwordRotation()}deg)`;
            }
        }
        
        // 计算飞行时间
        const flightTime = Math.max(400, Math.min(1200, distance * 2));
        
        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('normal-attack', '🌪️ 飞行时间:', flightTime + 'ms');
        }
        
        await this.wait(flightTime);
        
        // 清理投射物
        setTimeout(() => {
            if (projectile && projectile.parentNode) {
                projectile.parentNode.removeChild(projectile);
            }
        }, 100);
        
        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('normal-attack', '🌪️ 第二阶段：攻击发射完成');
        }
    }
    
    // 第三阶段：击中效果
    async createHitPhase(container, targetPos) {
        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('normal-attack', '🌪️ 第三阶段：击中效果开始');
        }
        
        // 创建击中特效
        this.createHitEffect(targetPos.x, targetPos.y, !this.isEnemySkill);
        
        // 创建爆炸效果
        const explosion = this.createElement('normal-attack-explosion', {
            style: {
                position: 'absolute',
                left: targetPos.x + 'px',
                top: targetPos.y + 'px',
                transform: 'translate(-50%, -50%)'
            }
        });
        container.appendChild(explosion);
        
        // 等待爆炸效果完成
        await this.wait(600);
        
        // 清理爆炸效果
        setTimeout(() => {
            if (explosion && explosion.parentNode) {
                explosion.parentNode.removeChild(explosion);
            }
        }, 100);
        
        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('normal-attack', '🌪️ 第三阶段：击中效果完成');
        }
    }

    // 🗡️ 动态计算剑的初始旋转角度
    calculateInitialSwordRotation() {
        // 敌方技能：剑尖向下（指向玩家），我方技能：剑尖向上（指向敌人）
        return this.isEnemySkill ? 0 : 180;
    }
}

// 导出技能类
window.NormalAttackSkills = { NormalAttackSkill }; 