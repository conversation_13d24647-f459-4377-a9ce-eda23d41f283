<?php
// 引入全局配置文件
$root_dir = dirname(dirname(__DIR__));
require_once $root_dir . '/setting.php';

// 注意：不再需要单独引入database.php，因为setting.php已经包含了数据库配置
// require_once getConfigPath('database.php'); // 已注释，避免函数重复定义

// 开启会话（前提是headers未发送）- 使用统一的会话参数
if (session_status() == PHP_SESSION_NONE && !headers_sent()) {
    session_set_cookie_params([
        'lifetime' => 0,
        'path' => '/yinian/',
        'domain' => '',
        'secure' => false,
        'httponly' => true,
        'samesite' => 'Lax'
    ]);
    session_start();
}

// 响应类型设置
function setJsonResponse()
{
    header('Content-Type: application/json; charset=utf-8');
}

// 验证用户名格式
function validateUsername($username)
{
    if (strlen($username) < 3 || strlen($username) > 20) {
        return false;
    }
    if (!preg_match('/^[a-zA-Z0-9_\x{4e00}-\x{9fff}]+$/u', $username)) {
        return false;
    }
    return true;
}

// 验证邮箱格式
function validateEmail($email)
{
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// 验证密码强度
function validatePassword($password)
{
    if (strlen($password) < 6) {
        return false;
    }
    return true;
}

// 生成安全哈希密码
function hashPassword($password)
{
    return password_hash($password, PASSWORD_DEFAULT);
}

// 验证密码
function verifyPassword($password, $hash)
{
    return password_verify($password, $hash);
}

// 检查用户名是否存在
function usernameExists($username)
{
    $pdo = getDatabase();
    $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ? AND status != 'banned'");
    $stmt->execute([$username]);
    return $stmt->fetch() !== false;
}

// 检查邮箱是否存在
function emailExists($email)
{
    $pdo = getDatabase();
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ? AND status != 'banned'");
    $stmt->execute([$email]);
    return $stmt->fetch() !== false;
}

// 注册用户
function registerUser($username, $email, $password)
{
    try {
        $pdo = getDatabase();
        $pdo->beginTransaction();

        // 插入用户数据到新的users表结构
        $hashedPassword = hashPassword($password);
        $stmt = $pdo->prepare("
            INSERT INTO users (
                username, 
                email, 
                password_hash,
                nickname,
                status,
                spirit_stones,
                gold,
                created_at,
                updated_at
            ) VALUES (
                ?, 
                ?, 
                ?,
                ?,
                'active',
                0,
                1000,
                CURRENT_TIMESTAMP,
                CURRENT_TIMESTAMP
            )
        ");
        $stmt->execute([
            $username,
            $email,
            $hashedPassword,
            $username  // 默认昵称为用户名
        ]);

        $userId = $pdo->lastInsertId();

        // 不再需要初始化user_game_data表
        // 用户创建角色时会在characters表中创建数据

        $pdo->commit();
        return $userId;
    } catch (Exception $e) {
        $pdo->rollBack();
        error_log("注册用户失败: " . $e->getMessage());
        return false;
    }
}

// 用户登录
function loginUser($username, $password)
{
    try {
        $pdo = getDatabase();

        // 首先检查用户表中的password_hash字段是否存在
        $tableCheckQuery = "SHOW COLUMNS FROM users LIKE 'password_hash'";
        $tableCheckStmt = $pdo->query($tableCheckQuery);
        $hasPasswordHash = $tableCheckStmt->rowCount() > 0;
        error_log("是否存在password_hash字段: " . ($hasPasswordHash ? "是" : "否"));

        // 检查是否存在characters表
        $charactersTableCheck = $pdo->query("SHOW TABLES LIKE 'characters'");
        $hasCharactersTable = $charactersTableCheck->rowCount() > 0;
        error_log("是否存在characters表: " . ($hasCharactersTable ? "是" : "否"));

        // 1. 首先尝试使用新的数据库结构（适配新版本）
        if ($hasPasswordHash && $hasCharactersTable) {
            error_log("使用新数据库结构处理登录 - 用户名: " . $username);

            // 查找用户
            $stmt = $pdo->prepare("
                SELECT u.id, u.username, u.email, u.password_hash, u.status, u.nickname,
                       u.spirit_stones, u.gold, u.last_login_time, u.login_count
                FROM users u 
                WHERE (u.username = ? OR u.email = ?)
            ");
            $stmt->execute([$username, $username]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$user) {
                error_log("新结构：用户不存在 - " . $username);
                return ['success' => false, 'message' => '用户名或密码错误'];
            }

            if ($user['status'] !== 'active') {
                error_log("新结构：用户状态不为active - " . $username . ", 状态: " . $user['status']);
                return ['success' => false, 'message' => '账号已被禁用'];
            }

            // 验证密码
            if (!verifyPassword($password, $user['password_hash'])) {
                error_log("新结构：密码验证失败 - " . $username);
                return ['success' => false, 'message' => '用户名或密码错误'];
            }

            // 更新登录信息
            $updateStmt = $pdo->prepare("
                UPDATE users 
                SET last_login_time = NOW(), 
                    login_count = login_count + 1 
                WHERE id = ?
            ");
            $updateStmt->execute([$user['id']]);

            // 设置会话
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['email'] = $user['email'];

            // 查找角色信息
            $characterStmt = $pdo->prepare("
                SELECT c.id as character_id, c.character_name, c.realm_id, c.realm_progress,
                       c.physique, c.comprehension, c.constitution, c.spirit, c.agility,
                       c.avatar_image, c.last_login_time,
                       r.realm_name, r.realm_level
                FROM characters c
                LEFT JOIN realm_levels r ON c.realm_id = r.id
                WHERE c.user_id = ? 
                ORDER BY c.id DESC 
                LIMIT 1
            ");
            $characterStmt->execute([$user['id']]);
            $character = $characterStmt->fetch(PDO::FETCH_ASSOC);

            // 检查是否需要创建角色
            $needCreateCharacter = !$character;

            if ($needCreateCharacter) {
                error_log("新结构：用户需要创建角色 - " . $username);
                return [
                    'success' => true,
                    'user' => [
                        'id' => $user['id'],
                        'username' => $user['username'],
                        'email' => $user['email'],
                        'nickname' => $user['nickname'],
                        'spirit_stones' => $user['spirit_stones'],
                        'gold' => $user['gold']
                    ],
                    'need_create_character' => true,
                    'redirect' => 'character_creation.html'
                ];
            } else {
                // 更新角色最后登录时间
                $updateCharacterStmt = $pdo->prepare("
                    UPDATE characters 
                    SET last_login_time = NOW()
                    WHERE id = ?
                ");
                $updateCharacterStmt->execute([$character['character_id']]);

                // 设置角色会话信息
                $_SESSION['character_id'] = $character['character_id'];
                $_SESSION['character_name'] = $character['character_name'];
                $_SESSION['character_avatar'] = $character['avatar_image'];
                $_SESSION['realm_id'] = $character['realm_id'];
                $_SESSION['realm_name'] = $character['realm_name'];
                $_SESSION['realm_level'] = $character['realm_level'];

                error_log("新结构：用户登录成功，有角色 - " . $username . ", 角色: " . $character['character_name']);

                // 返回用户和角色信息
                $finalStats = [];
                $finalStats['character_id'] = $character['character_id'];
                $finalStats['character_name'] = $character['character_name'];
                $finalStats['character_avatar'] = $character['avatar_image'];
                $finalStats['realm_id'] = $character['realm_id'];
                $finalStats['realm_name'] = $character['realm_name'];
                $finalStats['realm_level'] = $character['realm_level'];

                return [
                    'success' => true,
                    'user' => [
                        'id' => $user['id'],
                        'username' => $user['username'],
                        'email' => $user['email'],
                        'nickname' => $user['nickname'],
                        'spirit_stones' => $user['spirit_stones'],
                        'gold' => $user['gold'],
                        'character_id' => $character['character_id'],
                        'character_name' => $character['character_name'],
                        'character_avatar' => $character['avatar_image'],
                        'realm_id' => $character['realm_id'],
                        'realm_name' => $character['realm_name'],
                        'realm_level' => $character['realm_level'],
                        'physique' => $character['physique'],
                        'comprehension' => $character['comprehension'],
                        'constitution' => $character['constitution'],
                        'spirit' => $character['spirit'],
                        'agility' => $character['agility']
                    ],
                    'need_create_character' => false,
                    'redirect' => 'game.html'
                ];
            }
        }
        // 2. 如果是旧版结构，使用旧的方式查询
        else {
            error_log("使用旧数据库结构处理登录 - 用户名: " . $username);

            $stmt = $pdo->prepare("
                SELECT id, username, email, password, level, experience, gold, is_active
                FROM users 
                WHERE (username = ? OR email = ?)
            ");
            $stmt->execute([$username, $username]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$user) {
                error_log("旧结构：用户不存在 - " . $username);
                return ['success' => false, 'message' => '用户名或密码错误'];
            }

            if (!$user['is_active']) {
                error_log("旧结构：用户状态不为active - " . $username);
                return ['success' => false, 'message' => '账号已被禁用'];
            }

            // 旧版使用直接密码比较
            if ($user['password'] !== $password) {
                error_log("旧结构：密码不匹配 - " . $username);
                return ['success' => false, 'message' => '用户名或密码错误'];
            }

            // 查询用户游戏数据
            $gameDataStmt = $pdo->prepare("
                SELECT * FROM user_game_data WHERE user_id = ?
            ");
            $gameDataStmt->execute([$user['id']]);
            $gameData = $gameDataStmt->fetch(PDO::FETCH_ASSOC);

            // 设置会话
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['email'] = $user['email'];

            error_log("旧结构：用户登录成功 - " . $username);
            return [
                'success' => true,
                'user' => [
                    'id' => $user['id'],
                    'username' => $user['username'],
                    'email' => $user['email'],
                    'level' => $user['level'],
                    'experience' => $user['experience'],
                    'gold' => $user['gold']
                ],
                'game_data' => $gameData,
                'redirect' => 'game.html'
            ];
        }
    } catch (Exception $e) {
        error_log("登录错误 " . $username . ": " . $e->getMessage());
        error_log("堆栈跟踪: " . $e->getTraceAsString());
        return ['success' => false, 'message' => '登录失败，请稍后重试', 'error' => $e->getMessage()];
    }
}

// 检查是否已登录
function isLoggedIn()
{
    return isset($_SESSION['user_id']);
}

// 获取当前用户信息
function getCurrentUser()
{
    if (!isLoggedIn()) {
        return null;
    }

    try {
        $pdo = getDatabase();
        $stmt = $pdo->prepare("
            SELECT 
                u.id,
                u.username,
                u.email,
                u.nickname,
                u.status,
                u.spirit_stones,
                u.gold,
                c.id as character_id,
                c.character_name,
                c.avatar_image,
                c.realm_id,
                c.realm_progress,
                c.current_hp,
                c.current_mp,
                c.cultivation_points,
                -- 基础属性
                c.physique,
                c.comprehension,
                c.constitution,
                c.spirit,
                c.agility,
                -- 灵根属性
                c.metal_affinity,
                c.wood_affinity,
                c.water_affinity,
                c.fire_affinity,
                c.earth_affinity,
                -- 修炼相关
                c.cultivation_techniques,
                c.current_technique,
                -- 战斗相关
                c.total_battles,
                c.total_victories,
                c.total_defeats,
                c.highest_damage,
                c.highest_healing,
                -- 时间记录
                c.last_cultivation_time,
                c.last_login_time,
                c.last_battle_time,
                c.last_tribulation_time,
                c.total_online_time,
                -- 境界信息
                r.realm_name,
                r.realm_level,
                r.hp_multiplier,
                r.mp_multiplier,
                r.attack_multiplier,
                r.defense_multiplier,
                r.speed_multiplier
            FROM users u 
            LEFT JOIN characters c ON u.id = c.user_id
            LEFT JOIN realm_levels r ON c.realm_id = r.id
            WHERE u.id = ? AND u.status = 'active'
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $userData = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$userData) {
            return null;
        }

        // 确保角色信息不为空
        if (!isset($userData['character_id']) || !$userData['character_id']) {
            // 设置默认角色信息
            $userData['character_id'] = null;
            $userData['character_name'] = '原石道人';
            $userData['avatar_image'] = 'assets/images/avatars/default_male.png';
            $userData['realm_id'] = 1;
            $userData['realm_name'] = '练气期';
            $userData['realm_level'] = 1;
            $userData['current_hp'] = 100;
            $userData['current_mp'] = 100;
            $userData['physique'] = 10;
            $userData['comprehension'] = 10;
            $userData['constitution'] = 10;
            $userData['spirit'] = 10;
            $userData['agility'] = 10;

            // 创建need_create_character标志
            $userData['need_create_character'] = true;
        }

        // 计算实际属性（考虑境界加成）
        $hp_multiplier = isset($userData['hp_multiplier']) ? $userData['hp_multiplier'] : 1;
        $mp_multiplier = isset($userData['mp_multiplier']) ? $userData['mp_multiplier'] : 1;
        $attack_multiplier = isset($userData['attack_multiplier']) ? $userData['attack_multiplier'] : 1;
        $defense_multiplier = isset($userData['defense_multiplier']) ? $userData['defense_multiplier'] : 1;
        $speed_multiplier = isset($userData['speed_multiplier']) ? $userData['speed_multiplier'] : 1;

        $userData['max_hp'] = round((isset($userData['current_hp']) ? $userData['current_hp'] : 100) * $hp_multiplier);
        $userData['max_mp'] = round((isset($userData['current_mp']) ? $userData['current_mp'] : 100) * $mp_multiplier);

        $userData['speed_base'] = round((isset($userData['agility']) ? $userData['agility'] : 10) * $speed_multiplier);

        return $userData;
    } catch (Exception $e) {
        error_log("获取用户信息失败: " . $e->getMessage());
        return null;
    }
}

// 用户登出
function logoutUser()
{
    session_destroy();
    return true;
}

// 更新用户游戏数据
function updateUserGameData($userId, $data)
{
    try {
        $pdo = getDatabase();

        // 开始事务
        $pdo->beginTransaction();

        // 分离用户表和角色表的数据
        $userFields = [];
        $userValues = [];
        $characterFields = [];
        $characterValues = [];

        foreach ($data as $key => $value) {
            // 用户表字段
            if (in_array($key, ['spirit_stones', 'gold', 'nickname', 'status'])) {
                $userFields[] = "$key = ?";
                $userValues[] = $value;
            }
            // 角色表字段
            else {
                $characterFields[] = "$key = ?";
                $characterValues[] = $value;
            }
        }

        // 更新用户表
        if (!empty($userFields)) {
            $userValues[] = $userId;
            $sql = "UPDATE users SET " . implode(', ', $userFields) . " WHERE id = ?";
            $stmt = $pdo->prepare($sql);
            $stmt->execute($userValues);
        }

        // 更新角色表
        if (!empty($characterFields)) {
            $characterValues[] = $userId;
            $sql = "UPDATE characters SET " . implode(', ', $characterFields) . " WHERE user_id = ?";
            $stmt = $pdo->prepare($sql);
            $stmt->execute($characterValues);
        }

        // 提交事务
        $pdo->commit();
        return true;
    } catch (Exception $e) {
        // 回滚事务
        $pdo->rollBack();
        error_log("更新游戏数据失败: " . $e->getMessage());
        return false;
    }
}

// 获取用户排行榜
function getLeaderboard($type = 'level', $limit = 10)
{
    try {
        $pdo = getDatabase();

        $orderBy = '';
        switch ($type) {
            case 'level':
                $orderBy = 'u.level DESC, u.experience DESC';
                break;
            case 'wins':
                $orderBy = 'ugd.battles_won DESC';
                break;
            case 'damage':
                $orderBy = 'ugd.total_damage_dealt DESC';
                break;
            default:
                $orderBy = 'u.level DESC';
        }

        $stmt = $pdo->prepare("
            SELECT u.username, u.level, u.experience, 
                   ugd.battles_won, ugd.battles_lost, ugd.total_damage_dealt
            FROM users u 
            LEFT JOIN user_game_data ugd ON u.id = ugd.user_id 
            WHERE u.status = 'active'
            ORDER BY $orderBy
            LIMIT ?
        ");
        $stmt->execute([$limit]);

        return $stmt->fetchAll();
    } catch (Exception $e) {
        error_log("获取排行榜失败: " . $e->getMessage());
        return [];
    }
}

// 防止XSS攻击
function sanitizeOutput($string)
{
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

// 生成CSRF令牌
function generateCSRFToken()
{
    // 确保session已启动
    if (session_status() != PHP_SESSION_ACTIVE) {
        error_log("无法生成CSRF令牌: 会话未启动");
        return '';
    }

    // 如果尚未设置CSRF令牌或者强制刷新
    if (!isset($_SESSION['csrf_token'])) {
        try {
            $token = bin2hex(random_bytes(32));
            $_SESSION['csrf_token'] = $token;
            error_log("已生成新的CSRF令牌: " . substr($token, 0, 10) . "... (长度: " . strlen($token) . ")");
        } catch (Exception $e) {
            error_log("生成CSRF令牌失败: " . $e->getMessage());
            // 备用方案
            $token = md5(uniqid(mt_rand(), true));
            $_SESSION['csrf_token'] = $token;
            error_log("已使用备用方法生成CSRF令牌: " . substr($token, 0, 10) . "... (长度: " . strlen($token) . ")");
        }
    } else {
        error_log("使用现有CSRF令牌: " . substr($_SESSION['csrf_token'], 0, 10) . "... (长度: " . strlen($_SESSION['csrf_token']) . ")");
    }

    return $_SESSION['csrf_token'];
}

// 验证CSRF令牌
function validateCSRFToken($token)
{
    // 确保session已启动
    if (session_status() != PHP_SESSION_ACTIVE) {
        error_log("CSRF验证失败: 会话未启动");
        return false;
    }

    if (empty($token)) {
        error_log("CSRF验证失败: 提交的令牌为空");
        return false;
    }

    if (empty($_SESSION['csrf_token'])) {
        error_log("CSRF验证失败: 会话中的令牌为空");
        return false;
    }

    // 记录详细的令牌信息以便调试
    error_log("提交的令牌: " . substr($token, 0, 10) . "... (长度: " . strlen($token) . ")");
    error_log("会话中的令牌: " . substr($_SESSION['csrf_token'], 0, 10) . "... (长度: " . strlen($_SESSION['csrf_token']) . ")");

    $result = hash_equals($_SESSION['csrf_token'], $token);

    if (!$result) {
        error_log("CSRF验证结果: 不匹配");
    } else {
        error_log("CSRF验证结果: 匹配成功");
    }

    return $result;
}

/**
 * 获取角色套装属性加成
 * @param PDO $pdo 数据库连接
 * @param int $character_id 角色ID
 * @return array 套装属性加成数组
 */
function getCharacterSetBonus($pdo, $character_id)
{
    try {
        // 获取角色已装备的套装物品
        $stmt = $pdo->prepare("
            SELECT gi.set_id, gis.set_name, gis.effects, COUNT(*) as pieces_count,
                   gis.min_pieces, gis.max_pieces
            FROM character_equipment ce
            JOIN game_items gi ON ce.item_id = gi.id
            JOIN game_item_sets gis ON gi.set_id = gis.id
            WHERE ce.character_id = ? AND gi.set_id IS NOT NULL AND gis.status = 'active'
            GROUP BY gi.set_id, gis.set_name, gis.effects, gis.min_pieces, gis.max_pieces
        ");
        $stmt->execute([$character_id]);
        $equipped_sets = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // 初始化套装属性加成 - 使用正确的数据库字段名
        $setBonus = [
            'physical_attack' => 0,
            'immortal_attack' => 0,
            'physical_defense' => 0,
            'immortal_defense' => 0,
            'hp_bonus' => 0,           // 修正：max_hp -> hp_bonus
            'mp_bonus' => 0,           // 修正：max_mp -> mp_bonus
            'speed_bonus' => 0,        // 修正：speed -> speed_bonus
            'accuracy_bonus' => 0,     // 修正：hit_rate -> accuracy_bonus
            'dodge_bonus' => 0,        // 修正：dodge_rate -> dodge_bonus
            'critical_bonus' => 0,     // 修正：crit_rate -> critical_bonus
            'critical_damage' => 0,    // 修正：crit_damage -> critical_damage
            'critical_resistance' => 0, // 修正：crit_resistance -> critical_resistance
            'special_effects' => [] // 特殊效果数组
        ];

        // 计算每个套装的属性加成
        foreach ($equipped_sets as $set) {
            $pieces_count = $set['pieces_count'];
            $effects = json_decode($set['effects'], true);

            if (!$effects) continue;

            // 检查激活的套装效果（2件套、4件套、6件套）
            $activeEffects = [];
            if ($pieces_count >= 2 && isset($effects['two_piece'])) {
                $activeEffects[] = $effects['two_piece'];
            }
            if ($pieces_count >= 4 && isset($effects['four_piece'])) {
                $activeEffects[] = $effects['four_piece'];
            }
            if ($pieces_count >= 6 && isset($effects['six_piece'])) {
                $activeEffects[] = $effects['six_piece'];
            }

            // 累加属性加成
            foreach ($activeEffects as $effect) {
                foreach ($effect as $attr => $value) {
                    if ($attr === 'special_effect') {
                        // 特殊效果单独处理
                        $setBonus['special_effects'][] = [
                            'set_name' => $set['set_name'],
                            'pieces' => $pieces_count,
                            'effect' => $value
                        ];
                    } elseif (isset($setBonus[$attr])) {
                        $setBonus[$attr] += intval($value);
                    }
                }
            }
        }

        return $setBonus;
    } catch (Exception $e) {
        error_log("获取套装属性加成失败: " . $e->getMessage());
        return [
            'physical_attack' => 0,
            'immortal_attack' => 0,
            'physical_defense' => 0,
            'immortal_defense' => 0,
            'hp_bonus' => 0,           // 修正：max_hp -> hp_bonus
            'mp_bonus' => 0,           // 修正：max_mp -> mp_bonus
            'speed_bonus' => 0,        // 修正：speed -> speed_bonus
            'accuracy_bonus' => 0,     // 修正：hit_rate -> accuracy_bonus
            'dodge_bonus' => 0,        // 修正：dodge_rate -> dodge_bonus
            'critical_bonus' => 0,     // 修正：crit_rate -> critical_bonus
            'critical_damage' => 0,    // 修正：crit_damage -> critical_damage
            'critical_resistance' => 0, // 修正：crit_resistance -> critical_resistance
            'special_effects' => []
        ];
    }
}

/**
 * 通用角色属性计算函数
 * 计算角色的所有战斗属性，包括装备加成、功法加成、套装加成等
 *
 * @param array $characterInfo 角色基础信息
 * @param array $equipmentBonus 装备加成
 * @param array $cultivationBonus 功法加成，默认为空数组
 * @param array $otherBonus 其他加成，默认为空数组
 * @param array $setBonus 套装加成，默认为空数组
 * @return array 计算后的属性数组
 */
function calculateCharacterAttributes($characterInfo, $equipmentBonus = [], $cultivationBonus = [], $otherBonus = [], $setBonus = [])
{
    $attributes = [];

    // 确保基础属性为数值类型
    $physique = (int)(isset($characterInfo['physique']) ? $characterInfo['physique'] : 0);
    $comprehension = (int)(isset($characterInfo['comprehension']) ? $characterInfo['comprehension'] : 0);
    $constitution = (int)(isset($characterInfo['constitution']) ? $characterInfo['constitution'] : 0);
    $spirit = (int)(isset($characterInfo['spirit']) ? $characterInfo['spirit'] : 0);
    $agility = (int)(isset($characterInfo['agility']) ? $characterInfo['agility'] : 0);

    // 确保乘数为数值类型
    $attack_multiplier = (float)(isset($characterInfo['attack_multiplier']) ? $characterInfo['attack_multiplier'] : 1.0);
    $defense_multiplier = (float)(isset($characterInfo['defense_multiplier']) ? $characterInfo['defense_multiplier'] : 1.0);
    $hp_multiplier = (float)(isset($characterInfo['hp_multiplier']) ? $characterInfo['hp_multiplier'] : 1.0);
    $mp_multiplier = (float)(isset($characterInfo['mp_multiplier']) ? $characterInfo['mp_multiplier'] : 1.0);
    $speed_multiplier = (float)(isset($characterInfo['speed_multiplier']) ? $characterInfo['speed_multiplier'] : 1.0);

    // 🔧 修复：如果没有传入装备加成，自动获取
    if (empty($equipmentBonus) && isset($characterInfo['id'])) {
        require_once __DIR__ . '/equipment_stats_manager.php';
        require_once dirname(dirname(__DIR__)) . '/src/config/database.php';

        try {
            $pdo = getDatabase();
            $equipmentStats = EquipmentStatsManager::getAllEquipmentStats($pdo, $characterInfo['id']);

            // 转换字段名以匹配函数期望的格式
            $equipmentBonus = [
                'physical_attack' => $equipmentStats['physical_attack'],
                'immortal_attack' => $equipmentStats['immortal_attack'],
                'physical_defense' => $equipmentStats['physical_defense'],
                'immortal_defense' => $equipmentStats['immortal_defense'],
                'hp_bonus' => $equipmentStats['hp_bonus'],
                'mp_bonus' => $equipmentStats['mp_bonus'],
                'speed_bonus' => $equipmentStats['speed_bonus'],
                // 🔧 删除：废弃兼容字段已清理
                'critical_bonus' => $equipmentStats['critical_bonus']
            ];

            error_log("🔧 [角色属性计算] 自动获取装备加成: " . json_encode($equipmentBonus));
        } catch (Exception $e) {
            error_log("获取装备加成失败: " . $e->getMessage());
            $equipmentBonus = [];
        }
    } else if (!empty($equipmentBonus)) {
        error_log("🔧 [角色属性计算] 使用传入的装备加成: " . json_encode($equipmentBonus));
    }

    // 确保装备加成为数值类型
    $equipmentBonus = array_merge([
        'physical_attack' => 0,
        'immortal_attack' => 0,
        'physical_defense' => 0,
        'immortal_defense' => 0,
        'hp_bonus' => 0,
        'mp_bonus' => 0,
        'speed_bonus' => 0,
        'accuracy_bonus' => 0,
        'dodge_bonus' => 0,
        'critical_bonus' => 0,
        'critical_damage' => 0,
        'critical_resistance' => 0
    ], $equipmentBonus);

    // 确保功法加成为数值类型
    $cultivationBonus = array_merge([
        'physical_attack' => 0,
        'immortal_attack' => 0,
        'physical_defense' => 0,
        'immortal_defense' => 0,
        'hp_bonus' => 0,
        'mp_bonus' => 0,
        'speed_bonus' => 0,
        'accuracy_bonus' => 0,
        'dodge_bonus' => 0,
        'critical_bonus' => 0,
        'critical_damage' => 0,
        'critical_resistance' => 0
    ], $cultivationBonus);

    // 确保其他加成为数值类型
    $otherBonus = array_merge([
        'physical_attack' => 0,
        'immortal_attack' => 0,
        'physical_defense' => 0,
        'immortal_defense' => 0,
        'hp_bonus' => 0,
        'mp_bonus' => 0,
        'speed_bonus' => 0,
        'accuracy_bonus' => 0,
        'dodge_bonus' => 0,
        'critical_bonus' => 0,
        'critical_damage' => 0,
        'critical_resistance' => 0
    ], $otherBonus);



    // 🔧 修复：统一字段命名规范 - 基础值、装备加成、总值分离

    // 物理攻击力
    $attributes['physical_attack_base'] = (int)(($physique + $constitution) * $attack_multiplier);
    $attributes['physical_attack_total'] = $attributes['physical_attack_base'] +
        $equipmentBonus['physical_attack'] +
        $otherBonus['physical_attack'] +
        $setBonus['physical_attack'];

    // 法术攻击力
    $attributes['immortal_attack_base'] = (int)(($spirit + $comprehension) * $attack_multiplier);
    $attributes['immortal_attack_total'] = $attributes['immortal_attack_base'] +
        $equipmentBonus['immortal_attack'] +
        $otherBonus['immortal_attack'] +
        $setBonus['immortal_attack'];

    // 物理防御力
    $attributes['physical_defense_base'] = (int)(($physique + $constitution) * $defense_multiplier);
    $attributes['physical_defense_total'] = $attributes['physical_defense_base'] +
        $equipmentBonus['physical_defense'] +
        $otherBonus['physical_defense'] +
        $setBonus['physical_defense'];

    // 法术防御力
    $attributes['immortal_defense_base'] = (int)(($spirit + $comprehension) * $defense_multiplier);
    $attributes['immortal_defense_total'] = $attributes['immortal_defense_base'] +
        $equipmentBonus['immortal_defense'] +
        $otherBonus['immortal_defense'] +
        $setBonus['immortal_defense'];

    // 生命值
    $attributes['hp_base'] = (int)((100 + $constitution * 10) * $hp_multiplier);
    $attributes['hp_total'] = $attributes['hp_base'] +
        $equipmentBonus['hp_bonus'] +
        $otherBonus['hp_bonus'] +
        $setBonus['max_hp'];

    // 法力值
    $attributes['mp_base'] = (int)((50 + $spirit * 5) * $mp_multiplier);
    $attributes['mp_total'] = $attributes['mp_base'] +
        $equipmentBonus['mp_bonus'] +
        $otherBonus['mp_bonus'] +
        $setBonus['max_mp'];

    // 🔧 修复：明确区分基础速度和总速度
    $attributes['speed_base'] = (int)(($agility * 5) * $speed_multiplier);  // 角色基础速度
    $attributes['speed_total'] = $attributes['speed_base'] +
        $equipmentBonus['speed_bonus'] +
        $otherBonus['speed_bonus'] +
        $setBonus['speed'];                        // 总速度（基础+装备+套装）

    // 🔧 修复：动态属性计算 - 统一字段命名规范

    // 命中率：基础85 + 悟性×0.5 + 装备加成 + 套装加成
    $attributes['accuracy_bonus_base'] = 85 + ($comprehension * 0.5);
    $attributes['accuracy_bonus_total'] = $attributes['accuracy_bonus_base'] +
        $equipmentBonus['accuracy_bonus'] +
        $otherBonus['accuracy_bonus'] +
        $setBonus['hit_rate'];

    // 闪避率：基础5 + 身法×0.3 + 装备加成 + 套装加成
    $attributes['dodge_bonus_base'] = 5 + ($agility * 0.3);
    $attributes['dodge_bonus_total'] = $attributes['dodge_bonus_base'] +
        $equipmentBonus['dodge_bonus'] +
        $otherBonus['dodge_bonus'] +
        $setBonus['dodge_rate'];

    // 暴击率：基础5 + 身法×0.2 + 装备加成 + 套装加成
    $attributes['critical_bonus_base'] = 5 + ($agility * 0.2);
    $attributes['critical_bonus_total'] = $attributes['critical_bonus_base'] +
        $equipmentBonus['critical_bonus'] +
        $otherBonus['critical_bonus'] +
        $setBonus['crit_rate'];

    // 抗暴率：体魄×0.1 + 装备加成 + 套装加成
    $attributes['critical_resistance_base'] = ($constitution * 0.1);
    $attributes['critical_resistance_total'] = $attributes['critical_resistance_base'] +
        $equipmentBonus['critical_resistance'] +
        $otherBonus['critical_resistance'] +
        $setBonus['crit_resistance'];

    // 🔧 修复：暴击伤害统一格式 - 基础50%暴击伤害（小数格式）
    $attributes['critical_damage_base'] = 1.5; // 基础150%暴击伤害
    $attributes['critical_damage_total'] = $attributes['critical_damage_base'] +
        $equipmentBonus['critical_damage'] +
        $otherBonus['critical_damage'] +
        ($setBonus['crit_damage'] / 100); // 套装暴击伤害转换为小数

    // 🔧 删除：废弃兼容字段已清理，统一使用新字段名

    // 🔧 修复：统一的装备加成明细（用于前端显示来源）
    $attributes['physical_attack_equipment'] = $equipmentBonus['physical_attack'];
    $attributes['physical_attack_other'] = $otherBonus['physical_attack'];
    $attributes['physical_attack_set'] = $setBonus['physical_attack'];

    $attributes['immortal_attack_equipment'] = $equipmentBonus['immortal_attack'];
    $attributes['immortal_attack_other'] = $otherBonus['immortal_attack'];
    $attributes['immortal_attack_set'] = $setBonus['immortal_attack'];

    $attributes['physical_defense_equipment'] = $equipmentBonus['physical_defense'];
    $attributes['physical_defense_other'] = $otherBonus['physical_defense'];
    $attributes['physical_defense_set'] = $setBonus['physical_defense'];

    $attributes['immortal_defense_equipment'] = $equipmentBonus['immortal_defense'];
    $attributes['immortal_defense_other'] = $otherBonus['immortal_defense'];
    $attributes['immortal_defense_set'] = $setBonus['immortal_defense'];

    $attributes['hp_equipment'] = $equipmentBonus['hp_bonus'];
    $attributes['hp_other'] = $otherBonus['hp_bonus'];
    $attributes['hp_set'] = $setBonus['max_hp'];

    $attributes['mp_equipment'] = $equipmentBonus['mp_bonus'];
    $attributes['mp_other'] = $otherBonus['mp_bonus'];
    $attributes['mp_set'] = $setBonus['max_mp'];

    $attributes['speed_equipment'] = $equipmentBonus['speed_bonus'];
    $attributes['speed_other'] = $otherBonus['speed_bonus'];
    $attributes['speed_set'] = $setBonus['speed'];

    $attributes['accuracy_bonus_equipment'] = $equipmentBonus['accuracy_bonus'];
    $attributes['accuracy_bonus_other'] = $otherBonus['accuracy_bonus'];
    $attributes['accuracy_bonus_set'] = $setBonus['hit_rate'];

    $attributes['dodge_bonus_equipment'] = $equipmentBonus['dodge_bonus'];
    $attributes['dodge_bonus_other'] = $otherBonus['dodge_bonus'];
    $attributes['dodge_bonus_set'] = $setBonus['dodge_rate'];

    $attributes['critical_bonus_equipment'] = $equipmentBonus['critical_bonus'];
    $attributes['critical_bonus_other'] = $otherBonus['critical_bonus'];
    $attributes['critical_bonus_set'] = $setBonus['crit_rate'];

    $attributes['critical_resistance_equipment'] = $equipmentBonus['critical_resistance'];
    $attributes['critical_resistance_other'] = $otherBonus['critical_resistance'];
    $attributes['critical_resistance_set'] = $setBonus['crit_resistance'];

    $attributes['critical_damage_equipment'] = $equipmentBonus['critical_damage'];
    $attributes['critical_damage_other'] = $otherBonus['critical_damage'];
    $attributes['critical_damage_set'] = ($setBonus['crit_damage'] / 100);

    // 添加属性计算来源明细
    $attributes['sources'] = [
        'base' => [
            'physique' => $physique,
            'comprehension' => $comprehension,
            'constitution' => $constitution,
            'spirit' => $spirit,
            'agility' => $agility,
            'attack_multiplier' => $attack_multiplier,
            'defense_multiplier' => $defense_multiplier,
            'hp_multiplier' => $hp_multiplier,
            'mp_multiplier' => $mp_multiplier,
            'speed_multiplier' => $speed_multiplier
        ],
        'equipment' => $equipmentBonus,
        'other' => $otherBonus,
        'set' => $setBonus
    ];

    return $attributes;
}

/**
 * 🚫 已废弃：获取修炼功法加成
 * 功法不再提供战斗属性加成，仅影响修炼效率
 * 
 * @param array $technique 功法信息数组
 * @return array 空的属性加成数组
 */
function getCultivationTechniqueBonus($technique)
{
    // 🔧 功法不再提供战斗属性加成
    return [
        'physical_attack' => 0,
        'immortal_attack' => 0,
        'physical_defense' => 0,
        'immortal_defense' => 0,
        'hp_bonus' => 0,
        'mp_bonus' => 0,
        'speed_bonus' => 0,
        // 🔧 删除：废弃字段已清理
        'critical_bonus' => 0
    ];
}

/**
 * 获取角色灵根属性
 * 
 * @param PDO $pdo 数据库连接
 * @param int $characterId 角色ID
 * @return array 灵根属性数组
 */
function getRootAttributes($pdo, $characterId)
{
    // 🔧 新增：使用五行灵根系统
    require_once __DIR__ . '/../api/five_elements_spiritual_root.php';

    try {
        // 从数据库读取灵根数据
        $roots = FiveElementsSpiritualRootSystem::loadRootsFromDatabase($pdo, $characterId);

        if (!$roots) {
            // 如果没有灵根数据，返回默认值
            return [
                'gold_root' => 0,
                'wood_root' => 0,
                'water_root' => 0,
                'fire_root' => 0,
                'earth_root' => 0,
                'spiritual_root_bonus' => [],
                'overall_evaluation' => null
            ];
        }

        // 计算灵根属性加成
        $rootAttributeBonus = FiveElementsSpiritualRootSystem::calculateRootAttributeBonus($roots);

        // 获取总体评价
        $overallEvaluation = FiveElementsSpiritualRootSystem::getRootOverallEvaluation($roots);

        // 返回兼容的数据格式（保持原有字段名的同时添加新功能）
        return [
            'metal_affinity' => $roots['metal']['value'],
            'wood_affinity' => $roots['wood']['value'],
            'water_affinity' => $roots['water']['value'],
            'fire_affinity' => $roots['fire']['value'],
            'earth_affinity' => $roots['earth']['value'],

            'gold_root' => $roots['metal']['value'],
            'wood_root' => $roots['wood']['value'],
            'water_root' => $roots['water']['value'],
            'fire_root' => $roots['fire']['value'],
            'earth_root' => $roots['earth']['value'],

            'spiritual_root_bonus' => $rootAttributeBonus,
            'overall_evaluation' => $overallEvaluation,
            'root_details' => $roots // 详细的灵根信息
        ];
    } catch (Exception $e) {
        error_log("获取灵根属性失败: " . $e->getMessage());

        // 降级处理：直接从数据库读取原始值
        $stmt = $pdo->prepare("
            SELECT 
                metal_affinity as gold_root,
                wood_affinity as wood_root,
                water_affinity as water_root,
                fire_affinity as fire_root,
                earth_affinity as earth_root
            FROM characters
            WHERE id = ?
        ");
        $stmt->execute([$characterId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($result) {
            return array_merge($result, [
                'spiritual_root_bonus' => [],
                'overall_evaluation' => null
            ]);
        } else {
            return [
                'gold_root' => 0,
                'wood_root' => 0,
                'water_root' => 0,
                'fire_root' => 0,
                'earth_root' => 0,
                'spiritual_root_bonus' => [],
                'overall_evaluation' => null
            ];
        }
    }
}
