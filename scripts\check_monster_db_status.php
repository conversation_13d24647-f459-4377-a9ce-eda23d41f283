<?php
/**
 * 检查怪物系统数据库状态
 * 用于评估怪物属性重设计的需求
 */

try {
    $pdo = new PDO('mysql:host=localhost;dbname=yn_game;charset=utf8mb4', 'ynxx', 'mjlxz159');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== 怪物系统数据库状态检查 ===\n\n";
    
    // 1. 检查map_stages表结构
    echo "1. map_stages表字段结构:\n";
    echo "----------------------------------------\n";
    $stmt = $pdo->query('DESCRIBE map_stages');
    $existingFields = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $existingFields[] = $row['Field'];
        printf("%-25s %-20s %-10s\n", $row['Field'], $row['Type'], $row['Null']);
    }
    
    // 2. 检查缺失的重要字段
    echo "\n2. 关键字段状态检查:\n";
    echo "----------------------------------------\n";
    $requiredFields = [
        'accuracy_bonus' => '命中率',
        'critical_damage' => '暴击伤害',
        'critical_resistance' => '免暴率', 
        'block_bonus' => '格挡率',
        'immortal_attack' => '法术攻击',
        'immortal_defense' => '法术防御',
        'physical_attack' => '物理攻击',
        'physical_defense' => '物理防御',
        'hp_bonus' => '生命值',
        'mp_bonus' => '法力值',
        'speed_bonus' => '速度'
    ];
    
    foreach ($requiredFields as $field => $name) {
        $exists = in_array($field, $existingFields);
        printf("%-20s %-10s %s\n", $field, $exists ? '✓ 存在' : '✗ 缺失', $name);
    }
    
    // 3. 统计当前关卡数据
    echo "\n3. 关卡数据统计:\n";
    echo "----------------------------------------\n";
    $stmt = $pdo->query("
        SELECT ms.map_id, gm.map_name, COUNT(*) as stage_count, 
               MIN(ms.monster_level) as min_level, MAX(ms.monster_level) as max_level
        FROM map_stages ms 
        LEFT JOIN game_maps gm ON ms.map_id = gm.id
        GROUP BY ms.map_id
        ORDER BY ms.map_id
    ");
    
    $totalStages = 0;
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $mapName = $row['map_name'] ?: '未知地图';
        printf("地图%d (%s): %d关 (境界%d-%d)\n", 
            $row['map_id'], $mapName, $row['stage_count'], 
            $row['min_level'], $row['max_level']);
        $totalStages += $row['stage_count'];
    }
    echo "总计: {$totalStages}关\n";
    
    // 4. 检查属性数据示例
    echo "\n4. 当前怪物属性样本 (前5关):\n";
    echo "----------------------------------------\n";
    $stmt = $pdo->query("
        SELECT stage_number, monster_name, monster_level, 
               base_hp, base_attack, base_defense, base_speed,
               critical_bonus, dodge_bonus
        FROM map_stages 
        WHERE map_id = 1 
        ORDER BY stage_number 
        LIMIT 5
    ");
    
    echo "关卡  怪物名称        境界  血量   攻击  防御  速度  暴击  闪避\n";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        printf("%-4d  %-12s  %-4d  %-5d  %-4d  %-4d  %-4d  %-4.1f  %-4.1f\n",
            $row['stage_number'], $row['monster_name'], $row['monster_level'],
            $row['base_hp'], $row['base_attack'], $row['base_defense'], $row['base_speed'],
            $row['critical_bonus'], $row['dodge_bonus']);
    }
    
    // 5. 检查属性增长模式
    echo "\n5. 属性增长分析 (太乙峰1-20关):\n";
    echo "----------------------------------------\n";
    $stmt = $pdo->query("
        SELECT stage_number, base_hp, base_attack, base_defense
        FROM map_stages 
        WHERE map_id = 1 AND stage_number <= 20
        ORDER BY stage_number
    ");
    
    $prev = null;
    echo "关卡  血量     攻击   防御   血量增长  攻击增长\n";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $hpGrowth = $prev ? ($row['base_hp'] - $prev['base_hp']) : 0;
        $attGrowth = $prev ? ($row['base_attack'] - $prev['base_attack']) : 0;
        
        printf("%-4d  %-7d  %-5d  %-5d  %-8s  %-8s\n",
            $row['stage_number'], $row['base_hp'], $row['base_attack'], $row['base_defense'],
            $prev ? sprintf("%+d", $hpGrowth) : '-',
            $prev ? sprintf("%+d", $attGrowth) : '-');
        
        $prev = $row;
    }
    
    echo "\n=== 检查完成 ===\n";
    echo "建议: 根据以上分析制定属性重设计方案\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
?> 