<?php

/**
 * 战斗掉落系统API - 适配新数据库结构
 * 
 * 注意：此文件已更新为使用新的数据库结构，所有的背包相关操作使用user_inventories表
 * 掉落物品按照数据库真实数据掉落，武器技能从数据库配置获取
 */

// 🔧 防止重复执行 - 增强版
$requestId = uniqid('battle_', true);
error_log("🔧 请求ID: {$requestId} - battle_drops_unified.php 开始执行");

if (defined('BATTLE_DROPS_UNIFIED_EXECUTED')) {
    error_log("❌ 请求ID: {$requestId} - 检测到重复执行，已阻止");
    exit;
}
define('BATTLE_DROPS_UNIFIED_EXECUTED', true);

// 🔧 强制清空所有输出缓冲区，防止任何之前的输出
while (ob_get_level()) {
    ob_end_clean();
}

// 🔧 启动会话（如果还没有启动）
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// 🔧 重新开始输出缓冲，确保只有一个缓冲区
ob_start();

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

require_once __DIR__ . '/../config/database.php';
// 🔧 移除已弃用的技能配置文件引用
// require_once 'weapon_skills_config.php'; // 已弃用：技能配置现在从数据库获取
require_once __DIR__ . '/../includes/functions.php';

// 🔧 调试信息已移除，避免干扰正常输出

try {
    $database = new Database();
    $db = $database->getConnection();

    if (!$db) {
        throw new Exception('数据库连接失败');
    }

    // 🔧 修复：简化action参数获取逻辑，优先检查POST
    $action = '';

    if (isset($_POST['action'])) {
        $action = $_POST['action'];
    } elseif (isset($_GET['action'])) {
        $action = $_GET['action'];
    }

    // 🔧 获取前端请求ID
    $frontendRequestId = isset($_POST['frontend_request_id']) ? $_POST['frontend_request_id'] : 'N/A';

    // 🔧 记录action参数，用于调试
    error_log("=== 后端请求ID: {$requestId} | 前端请求ID: {$frontendRequestId} - API调用记录 ===");
    error_log("后端请求ID: {$requestId} | 前端请求ID: {$frontendRequestId} - 时间: " . date('Y-m-d H:i:s.u'));
    error_log("后端请求ID: {$requestId} | 前端请求ID: {$frontendRequestId} - 请求方法: " . $_SERVER['REQUEST_METHOD']);
    error_log("后端请求ID: {$requestId} | 前端请求ID: {$frontendRequestId} - Action参数: " . ($action ? $action : 'EMPTY'));
    error_log("请求ID: {$requestId} - Content-Type: " . (isset($_SERVER['CONTENT_TYPE']) ? $_SERVER['CONTENT_TYPE'] : 'N/A'));
    error_log("请求ID: {$requestId} - POST数据: " . json_encode($_POST));
    error_log("请求ID: {$requestId} - GET数据: " . json_encode($_GET));
    error_log("请求ID: {$requestId} - 原始POST数据大小: " . strlen(file_get_contents('php://input')));
    error_log("请求ID: {$requestId} - HTTP_USER_AGENT: " . (isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : 'N/A'));
    error_log("请求ID: {$requestId} - REQUEST_URI: " . (isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : 'N/A'));
    error_log("请求ID: {$requestId} - HTTP_REFERER: " . (isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : 'N/A'));
    error_log("请求ID: {$requestId} - REMOTE_ADDR: " . (isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : 'N/A'));
    error_log("请求ID: {$requestId} - SESSION_ID: " . session_id());

    // 🔧 新增：检查是否有重复的请求头
    $allHeaders = getallheaders();
    error_log("请求ID: {$requestId} - 所有请求头: " . json_encode($allHeaders));

    // 🔧 新增：检查输出缓冲区状态
    error_log("输出缓冲区层级: " . ob_get_level());
    if (ob_get_level() > 0) {
        error_log("输出缓冲区内容长度: " . strlen(ob_get_contents()));
    }

    // 🔧 如果action为空，直接返回错误并记录详细信息
    if (empty($action)) {
        error_log("❌ 请求ID: {$requestId} - Action参数为空，拒绝处理");
        error_log("❌ 请求ID: {$requestId} - 完整POST数据: " . json_encode($_POST));
        error_log("❌ 请求ID: {$requestId} - 完整GET数据: " . json_encode($_GET));
        error_log("❌ 请求ID: {$requestId} - 调用堆栈: " . print_r(debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS), true));

        // 🔧 强制清空输出缓冲区并退出
        while (ob_get_level()) {
            ob_end_clean();
        }

        header('Content-Type: application/json; charset=utf-8');
        // 🔧 临时注释掉这个输出，避免双重JSON问题
        // echo json_encode(array('success' => false, 'message' => '未知的操作'));
        error_log("🔧 请求ID: {$requestId} - Action为空，直接退出，不输出JSON");
        exit(); // 使用exit()确保完全停止执行
    }

    switch ($action) {
        case 'get_monster':
            echo json_encode(getMonster($db));
            exit;

        case 'calculate_drops':
            echo json_encode(calculateDrops($db));
            exit;

        case 'save_victory_result':
            error_log("🔧 请求ID: {$requestId} - 进入save_victory_result分支");

            // 🔧 强制清空所有输出缓冲区
            while (ob_get_level()) {
                error_log("🔧 请求ID: {$requestId} - 清理输出缓冲区层级: " . ob_get_level());
                ob_end_clean();
            }
            ob_start();

            // 🔧 防重复处理机制
            $lockFile = sys_get_temp_dir() . '/battle_save_' . session_id() . '.lock';
            if (file_exists($lockFile) && (time() - filemtime($lockFile)) < 5) {
                error_log("🔧 请求ID: {$requestId} - 请求过于频繁，拒绝处理");
                echo json_encode(array('success' => false, 'message' => '请求过于频繁，请稍候再试'));
                ob_end_flush();
                exit;
            }
            file_put_contents($lockFile, time());

            error_log("🔧 请求ID: {$requestId} - 开始调用saveVictoryResultUnified");
            $result = saveVictoryResultUnified($db);
            error_log("🔧 请求ID: {$requestId} - saveVictoryResultUnified返回结果: " . json_encode($result));

            // 清理锁文件
            if (file_exists($lockFile)) {
                unlink($lockFile);
            }

            error_log("🔧 请求ID: {$requestId} - 准备输出JSON结果");
            echo json_encode($result);
            ob_end_flush();
            error_log("🔧 请求ID: {$requestId} - save_victory_result分支处理完成，即将退出");
            exit;

        case 'get_user_progress':
            echo json_encode(getUserProgress($db));
            exit;

        case 'get_map_info':
            echo json_encode(getMapInfo($db));
            exit;

        case 'check_table':
            echo json_encode(checkTableExists($db));
            exit;

        case 'check_table_exists':
            echo json_encode(checkTableExists($db));
            exit;

        case 'check_user_inventories_columns':
            echo json_encode(checkUserInventoriesColumns($db));
            exit;

        case 'check_game_items_columns':
            echo json_encode(checkGameItemsColumns($db));
            exit;

        case 'cleanup_test_data':
            echo json_encode(cleanupTestData($db));
            exit;

        case 'set_stage_progress':
            echo json_encode(setStageProgress($db));
            exit;

        case 'get_current_stage':
            echo json_encode(getCurrentStage($db));
            exit;

        default:
            error_log("❌ 未知的操作: " . $action);
            error_log("所有可用操作: get_monster, calculate_drops, save_victory_result, get_user_progress, get_map_info, check_table, check_table_exists, check_user_inventories_columns, check_game_items_columns, cleanup_test_data, set_stage_progress, get_current_stage");
            error_log("❌ Default分支调用堆栈: " . print_r(debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS), true));

            // 🔧 强制清空输出缓冲区并退出
            while (ob_get_level()) {
                ob_end_clean();
            }

            header('Content-Type: application/json; charset=utf-8');
            // 🔧 临时注释掉这个输出，避免双重JSON问题
            // echo json_encode(array('success' => false, 'message' => '未知的操作'));
            error_log("🔧 Default分支，直接退出，不输出JSON");
            exit(); // 使用exit()确保完全停止执行
    }
} catch (Exception $e) {
    // 🔧 强制清空输出缓冲区并退出
    while (ob_get_level()) {
        ob_end_clean();
    }

    header('Content-Type: application/json; charset=utf-8');
    echo json_encode(array('success' => false, 'message' => $e->getMessage()));
    exit;
}

function getMonster($db)
{
    try {
        $mapCode = isset($_GET['map_code']) ? $_GET['map_code'] : 'kunlun';
        $currentStage = intval(isset($_GET['current_stage']) ? $_GET['current_stage'] : 1);

        // 🔧 修复：首先尝试从新的game_maps和map_stages表获取怪物数据
        $stmt = $db->prepare("SHOW TABLES LIKE 'game_maps'");
        $stmt->execute();
        $gameMapsTableExists = $stmt->rowCount() > 0;

        $stmt = $db->prepare("SHOW TABLES LIKE 'map_stages'");
        $stmt->execute();
        $mapStagesTableExists = $stmt->rowCount() > 0;

        $stmt = $db->prepare("SHOW TABLES LIKE 'monsters'");
        $stmt->execute();
        $monstersTableExists = $stmt->rowCount() > 0;

        if ($gameMapsTableExists && $mapStagesTableExists && $monstersTableExists) {
            // 使用新的数据库结构
            return getMonsterNewStructure($db, $mapCode, $currentStage);
        }

        // 🔧 回退到旧的数据库结构
        return getMonsterOldStructure($db, $mapCode, $currentStage);
    } catch (Exception $e) {
        error_log("获取怪物数据失败: " . $e->getMessage());
        return getDefaultMonsterData($currentStage);
    }
}

/**
 * 使用新数据库结构获取怪物数据
 */
function getMonsterNewStructure($db, $mapCode, $currentStage)
{
    try {
        // 获取地图ID
        $stmt = $db->prepare("SELECT id, map_name FROM game_maps WHERE map_code = ?");
        $stmt->execute(array($mapCode));
        $map = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$map) {
            error_log("地图不存在: {$mapCode}");
            return getDefaultMonsterData($currentStage);
        }

        $mapId = $map['id'];

        // 判断是否为BOSS关卡
        $isBossStage = ($currentStage % 10 == 0);

        // 查询map_stages表获取关卡怪物信息
        $stmt = $db->prepare("
            SELECT ms.*, gm.map_name
            FROM map_stages ms
            JOIN game_maps gm ON ms.map_id = gm.id
            WHERE ms.map_id = ? AND ms.stage_number = ?
        ");
        $stmt->execute(array($mapId, $currentStage));
        $stageInfo = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($stageInfo) {
            // 解析技能JSON
            $skills = array('普通攻击');
            if ($stageInfo['skills']) {
                try {
                    $skillsData = json_decode($stageInfo['skills'], true);
                    if (json_last_error() === JSON_ERROR_NONE && is_array($skillsData)) {
                        $skills = $skillsData;
                    }
                } catch (Exception $e) {
                    error_log("解析怪物技能失败: " . $e->getMessage());
                }
            }

            // 计算怪物属性（基于等级调整）
            $monsterLevel = $stageInfo['monster_level'] ?: $currentStage;
            $levelMultiplier = 1 + ($monsterLevel - 1) * 0.1;

            $monsterData = array(
                'id' => $stageInfo['monster_id'],
                'name' => $stageInfo['monster_name'],
                'level' => $monsterLevel,
                'hp' => intval($stageInfo['base_hp'] * $levelMultiplier),
                'mp' => intval($stageInfo['base_mp'] * $levelMultiplier),
                'attack' => intval($stageInfo['base_attack'] * $levelMultiplier),
                'defense' => intval($stageInfo['base_defense'] * $levelMultiplier),
                'speed' => intval($stageInfo['base_speed'] * $levelMultiplier),
                'critical_bonus' => $stageInfo['critical_bonus'] ?: 5.0,
                'dodge_bonus' => $stageInfo['dodge_bonus'] ?: 5.0,
                'monster_type' => $stageInfo['monster_tier'] ?: ($isBossStage ? 'boss' : 'normal'),
                'skills' => $skills,
                'avatar_image' => $stageInfo['avatar_image'],
                'model_image' => $stageInfo['model_image'],
                'description' => $stageInfo['description'],
                'ai_pattern' => $stageInfo['ai_pattern'] ?: 'normal',
                'spiritStoneReward' => (isset($stageInfo['spirit_stone_reward']) && $stageInfo['spirit_stone_reward'] > 0) ? intval($stageInfo['spirit_stone_reward']) : ($monsterLevel * 10),
                'goldReward' => (isset($stageInfo['gold_reward']) && $stageInfo['gold_reward'] > 0) ? intval($stageInfo['gold_reward']) : ($monsterLevel * 5),
                'exp_reward' => (isset($stageInfo['spirit_stone_reward']) && $stageInfo['spirit_stone_reward'] > 0) ? intval($stageInfo['spirit_stone_reward']) : ($monsterLevel * 10),
                'gold_reward' => (isset($stageInfo['gold_reward']) && $stageInfo['gold_reward'] > 0) ? intval($stageInfo['gold_reward']) : ($monsterLevel * 5),
                'map_name' => $stageInfo['map_name'],
                'stage_name' => $stageInfo['stage_name'],
                'is_boss_stage' => $stageInfo['is_boss_stage'] ? true : $isBossStage
            );

            error_log("新结构获取怪物成功 - 地图: {$mapCode}, 关卡: {$currentStage}, 怪物: {$monsterData['name']}");
            error_log("奖励信息 - 灵石奖励: {$monsterData['spiritStoneReward']}, 金币奖励: {$monsterData['goldReward']}");
            error_log("原始数据 - spirit_stone_reward: " . ($stageInfo['spirit_stone_reward'] ?: 'null') . ", gold_reward: " . ($stageInfo['gold_reward'] ?: 'null'));

            return array(
                'success' => true,
                'monster' => $monsterData,
                'is_boss' => $monsterData['is_boss_stage'],
                'source' => 'new_structure'
            );
        }

        // 如果没有找到具体关卡数据，生成默认怪物
        error_log("关卡数据不存在，生成默认怪物 - 地图: {$mapCode}, 关卡: {$currentStage}");
        return getDefaultMonsterData($currentStage, $map['map_name']);
    } catch (Exception $e) {
        error_log("新结构获取怪物失败: " . $e->getMessage());
        return getDefaultMonsterData($currentStage);
    }
}

/**
 * 使用旧数据库结构获取怪物数据（向后兼容）
 */
function getMonsterOldStructure($db, $mapCode, $currentStage)
{
    try {
        // 获取地图信息
        $stmt = $db->prepare("SELECT id, map_name FROM maps WHERE map_code = ?");
        $stmt->execute(array($mapCode));
        $map = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$map) {
            error_log("旧结构 - 地图不存在: {$mapCode}");
            return getDefaultMonsterData($currentStage);
        }

        $mapId = $map['id'];
        $mapName = $map['map_name'];

        // 判断是否为BOSS关卡
        $isBossStage = ($currentStage % 10 == 0);

        // 查询怪物信息
        $stmt = $db->prepare("
            SELECT m.*, ms.stage_number, ms.monster_level
            FROM monsters m
            JOIN map_stages ms ON m.id = ms.monster_id
            WHERE ms.map_id = ? AND ms.stage_number = ?
        ");
        $stmt->execute(array($mapId, $currentStage));
        $monsterInfo = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($monsterInfo) {
            // 解析技能JSON
            $skills = array('普通攻击');
            if ($monsterInfo['skills']) {
                try {
                    $skillsData = json_decode($monsterInfo['skills'], true);
                    if (json_last_error() === JSON_ERROR_NONE && is_array($skillsData)) {
                        $skills = $skillsData;
                    }
                } catch (Exception $e) {
                    error_log("解析怪物技能失败: " . $e->getMessage());
                }
            }

            // 计算怪物属性（基于等级调整）
            $monsterLevel = $monsterInfo['monster_level'] ?: $currentStage;
            $levelMultiplier = 1 + ($monsterLevel - 1) * 0.1;

            $monsterData = array(
                'id' => $monsterInfo['id'],
                'name' => $monsterInfo['monster_name'],
                'level' => $monsterLevel,
                'hp' => intval($monsterInfo['base_hp'] * $levelMultiplier),
                'mp' => intval($monsterInfo['base_mp'] * $levelMultiplier),
                'attack' => intval($monsterInfo['base_attack'] * $levelMultiplier),
                'defense' => intval($monsterInfo['base_defense'] * $levelMultiplier),
                'speed' => intval($monsterInfo['base_speed'] * $levelMultiplier),
                'critical_bonus' => $monsterInfo['critical_bonus'] ?: 5.0,
                'dodge_bonus' => $monsterInfo['dodge_bonus'] ?: 5.0,
                'monster_type' => $monsterInfo['monster_type'] ?: ($isBossStage ? 'boss' : 'normal'),
                'skills' => $skills,
                'avatar_image' => $monsterInfo['avatar_image'],
                'model_image' => $monsterInfo['model_image'],
                'description' => $monsterInfo['description'],
                'ai_pattern' => $monsterInfo['ai_pattern'] ?: 'normal',
                'spiritStoneReward' => 0, // 旧结构不支持数据库奖励配置
                'goldReward' => 0, // 旧结构不支持数据库奖励配置
                'exp_reward' => $monsterLevel * 10,
                'gold_reward' => $monsterLevel * 5,
                'map_name' => $mapName,
                'stage_name' => "第{$currentStage}关",
                'is_boss_stage' => $isBossStage
            );

            error_log("旧结构获取怪物成功 - 地图: {$mapCode}, 关卡: {$currentStage}, 怪物: {$monsterData['name']}");

            return array(
                'success' => true,
                'monster' => $monsterData,
                'is_boss' => $monsterData['is_boss_stage'],
                'source' => 'old_structure'
            );
        }

        // 如果没有找到具体关卡数据，生成默认怪物
        error_log("旧结构关卡数据不存在，生成默认怪物 - 地图: {$mapCode}, 关卡: {$currentStage}");
        return getDefaultMonsterData($currentStage, $mapName);
    } catch (Exception $e) {
        error_log("旧结构获取怪物失败: " . $e->getMessage());
        return getDefaultMonsterData($currentStage);
    }
}

/**
 * 生成默认怪物数据（当数据库配置不可用时）
 */
function getDefaultMonsterData($currentStage, $mapName = '未知区域')
{
    $isBossStage = ($currentStage % 10 == 0);
    $monsterLevel = $currentStage;

    // 基础属性随关卡递增
    $baseHp = 100 + ($monsterLevel * 20);
    $baseMp = 50 + ($monsterLevel * 10);
    $baseAttack = 20 + ($monsterLevel * 5);
    $baseDefense = 15 + ($monsterLevel * 3);
    $baseSpeed = 10 + ($monsterLevel * 2);

    // BOSS关卡属性增强
    if ($isBossStage) {
        $baseHp *= 2;
        $baseMp *= 1.5;
        $baseAttack *= 1.5;
        $baseDefense *= 1.3;
        $baseSpeed *= 1.2;
    }

    $monsterData = array(
        'id' => 9999,
        'name' => $isBossStage ? "第{$currentStage}关守护者" : "第{$currentStage}关魔物",
        'level' => $monsterLevel,
        'hp' => intval($baseHp),
        'mp' => intval($baseMp),
        'attack' => intval($baseAttack),
        'defense' => intval($baseDefense),
        'speed' => intval($baseSpeed),
        'critical_bonus' => 5.0,
        'dodge_bonus' => 5.0,
        'monster_type' => $isBossStage ? 'boss' : 'normal',
        'skills' => array('普通攻击', '重击'),
        'avatar_image' => 'assets/images/monsters/default.png',
        'model_image' => 'assets/images/monsters/default_model.png',
        'description' => $isBossStage ? '强大的关卡守护者' : '普通的魔物',
        'ai_pattern' => 'normal',
        'spiritStoneReward' => 0, // 默认数据不提供奖励
        'goldReward' => 0, // 默认数据不提供奖励
        'exp_reward' => $monsterLevel * 10,
        'gold_reward' => $monsterLevel * 5,
        'map_name' => $mapName,
        'stage_name' => "第{$currentStage}关",
        'is_boss_stage' => $isBossStage
    );

    error_log("生成默认怪物数据 - 关卡: {$currentStage}, 怪物: {$monsterData['name']}");

    return array(
        'success' => true,
        'monster' => $monsterData,
        'is_boss' => $monsterData['is_boss_stage'],
        'source' => 'default'
    );
}

function calculateDrops($db)
{
    try {
        $mapCode = isset($_GET['map_code']) ? $_GET['map_code'] : 'kunlun';
        $currentStage = intval(isset($_GET['current_stage']) ? $_GET['current_stage'] : 1);
        $monsterType = isset($_GET['monster_type']) ? $_GET['monster_type'] : 'normal';

        error_log("掉落计算 - 地图代码: {$mapCode}, 关卡: {$currentStage}, 怪物类型: {$monsterType}");

        // 🔧 修复：检查新数据库结构
        $stmt = $db->prepare("SHOW TABLES LIKE 'game_maps'");
        $stmt->execute();
        $gameMapsExists = $stmt->rowCount() > 0;

        $stmt = $db->prepare("SHOW TABLES LIKE 'drop_groups'");
        $stmt->execute();
        $dropGroupsExists = $stmt->rowCount() > 0;

        if ($gameMapsExists && $dropGroupsExists) {
            // 使用新的数据库结构
            return calculateDropsNewStructure($db, $mapCode, $currentStage, $monsterType);
        }

        // 🔧 回退到旧的数据库结构
        return calculateDropsOldStructure($db, $mapCode, $currentStage, $monsterType);
    } catch (Exception $e) {
        error_log("掉落计算失败: " . $e->getMessage());
        return array(
            'success' => true,
            'drops' => array(),
            'message' => '掉落计算失败: ' . $e->getMessage()
        );
    }
}

/**
 * 使用新数据库结构计算掉落
 */
function calculateDropsNewStructure($db, $mapCode, $currentStage, $monsterType)
{
    try {
        // 获取地图ID - 优先查询game_maps表（新结构）
        $mapId = null;
        $mapName = '未知区域';

        // 1. 优先查询game_maps表（新的数据库结构）
        $stmt = $db->prepare("SELECT id, map_name FROM game_maps WHERE map_code = ?");
        $stmt->execute(array($mapCode));
        $map = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($map) {
            $mapId = $map['id'];
            $mapName = $map['map_name'];
            error_log("从game_maps表找到地图: ID={$mapId}, 名称={$mapName}");
        } else {
            // 2. 如果game_maps表中没有，回退到maps表（向后兼容）
            $stmt = $db->prepare("SELECT id, map_name FROM maps WHERE map_code = ?");
            $stmt->execute(array($mapCode));
            $map = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($map) {
                $mapId = $map['id'];
                $mapName = $map['map_name'];
                error_log("从maps表找到地图: ID={$mapId}, 名称={$mapName}");
            } else {
                error_log("在任何地图表中都找不到地图代码: {$mapCode}");
                // 使用默认掉落
                return generateDefaultDrops($currentStage, $monsterType, $db);
            }
        }

        // 🔥 境界掉落系统：不再依赖map_drop_configs，直接基于怪物境界掉落
        $isBossStage = ($monsterType === 'boss' || $currentStage % 10 == 0);

        // 统一掉落几率设置 - BOSS关卡90%，普通关卡80%
        $dropChance = $isBossStage ? 90.0 : 80.0;
        $randomChance = mt_rand(1, 10000) / 100.0; // 生成0.01-100.00的随机数，精度到小数点后2位

        error_log("境界掉落几率检查 - 需要: {$dropChance}%, 随机: {$randomChance}%");

        // 掉落几率判断
        if ($randomChance > $dropChance) {
            error_log("掉落几率检查失败 - 需要: {$dropChance}%, 随机: {$randomChance}%");
            return array('success' => true, 'drops' => array(), 'message' => '掉落几率未触发');
        }

        // 🔥 境界掉落系统：根据怪物境界获取掉落物品
        $monsterRealm = getMonsterRealmFromStage($db, $mapId, $currentStage);
        $dropItems = getMonsterRealmDropItems($db, $monsterRealm);

        if (empty($dropItems)) {
            error_log("境界掉落系统无结果，返回默认掉落");
            return generateDefaultDrops($currentStage, $monsterType, $db);
        }

        error_log("境界掉落系统成功：怪物境界{$monsterRealm}，返回" . count($dropItems) . "件装备");

        $actualDrops = array();

        // 🔥 掉落数量设置：BOSS关卡1-3件，普通关卡0-3件
        if ($isBossStage) {
            $maxDrops = min(3, count($dropItems));
            $minDrops = 1;
            error_log("BOSS关卡掉落数量 - 最小: {$minDrops}, 最大: {$maxDrops}");
        } else {
            $maxDrops = min(3, count($dropItems));
            $minDrops = 0;
            error_log("普通关卡掉落数量 - 最小: {$minDrops}, 最大: {$maxDrops}");
        }

        // 计算实际掉落数量
        $actualDropCount = mt_rand($minDrops, $maxDrops);
        error_log("计算掉落数量 - 实际掉落: {$actualDropCount}件");

        // 🔧 修复：权重随机选择物品算法
        $totalWeight = array_sum(array_column($dropItems, 'drop_weight'));
        $selectedItems = array();
        $availableItems = $dropItems; // 复制数组，避免修改原数组

        for ($i = 0; $i < $actualDropCount && count($availableItems) > 0; $i++) {
            // 🔧 修复：每次重新计算总权重
            $currentTotalWeight = array_sum(array_column($availableItems, 'drop_weight'));

            if ($currentTotalWeight <= 0) {
                error_log("权重计算错误：总权重为0或负数");
                break;
            }

            $randomWeight = mt_rand(1, $currentTotalWeight);
            $currentWeight = 0;
            $selectedIndex = -1;

            foreach ($availableItems as $index => $item) {
                $currentWeight += intval($item['drop_weight']);
                if ($randomWeight <= $currentWeight) {
                    $selectedIndex = $index;
                    break;
                }
            }

            if ($selectedIndex >= 0) {
                $item = $availableItems[$selectedIndex];
                $quantity = mt_rand(intval($item['min_quantity']), intval($item['max_quantity']));

                // 🔥 生成稀有度（基于境界掉落系统和怪物类型）
                $rarity = generateItemRarity('装备', $monsterType);

                // 🔧 修复：构建装备属性数据，确保包含原始属性
                $itemData = array();
                if (in_array($item['item_type'], ['weapon', 'equipment', 'armor', 'accessory'])) {
                    // 装备类物品，包含属性数据
                    $itemData = array(
                        'physical_attack' => intval($item['physical_attack']),
                        'immortal_attack' => intval($item['immortal_attack']),
                        'physical_defense' => intval($item['physical_defense']),
                        'immortal_defense' => intval($item['immortal_defense']),
                        'hp_bonus' => intval($item['hp_bonus']),
                        'mp_bonus' => intval($item['mp_bonus']),
                        'speed_bonus' => intval($item['speed_bonus']),
                        'critical_bonus' => floatval($item['critical_bonus']),
                        'critical_damage' => floatval($item['critical_damage']),
                        'accuracy_bonus' => floatval($item['accuracy_bonus']),
                        'dodge_bonus' => floatval($item['dodge_bonus']),
                        'critical_resistance' => floatval($item['critical_resistance']),
                        'realm_requirement' => intval($item['realm_requirement']) // 添加境界要求到属性数据中
                    );
                    error_log("🎯 装备属性数据已构建: {$item['item_name']} - 物理攻击:{$itemData['physical_attack']}, 仙攻:{$itemData['immortal_attack']}, 境界要求:{$itemData['realm_requirement']}");
                }

                // 🔧 修复：处理图片路径，确保前端能正确加载
                $iconPath = $item['icon_image'];
                if ($iconPath) {
                    // 🔧 修复：清理路径，移除多余的前缀
                    $iconPath = trim($iconPath);

                    // 移除可能的路径前缀，但保留原有的文件扩展名
                    $iconPath = str_replace(['assets/images/', 'images/', './'], '', $iconPath);

                    // 确保使用正确的路径格式，如果已有扩展名就不重复添加
                    if (!preg_match('/\.(png|jpg|jpeg|gif|webp)$/i', $iconPath)) {
                        $iconPath = 'assets/images/' . $iconPath . '.png';
                    } else {
                        $iconPath = 'assets/images/' . $iconPath;
                    }
                } else {
                    // 如果没有图片信息，使用默认图片
                    $iconPath = 'assets/images/battle_sword.png';
                }

                $actualDrops[] = array(
                    'id' => $item['item_id'],
                    'name' => $item['item_name'],
                    'type' => $item['item_type'],
                    'rarity' => $rarity,
                    'quantity' => $quantity,
                    'icon_image' => $iconPath,  // 🔧 统一使用icon_image字段
                    'description' => $item['description'] ?: '暂无描述',
                    'sell_price' => intval($item['sell_price']) ?: 0,
                    'realm_requirement' => intval($item['realm_requirement']) ?: $monsterRealm, // 如果没有设置，使用怪物境界
                    'item_data' => $itemData
                );

                error_log("选中掉落物品: {$item['item_name']} x{$quantity} (权重: {$item['drop_weight']}, 品质: {$rarity})");

                // 🔧 修复：移除已选择的物品（避免重复）
                array_splice($availableItems, $selectedIndex, 1);
            } else {
                error_log("权重选择失败：无法找到有效的物品索引");
                break;
            }
        }

        error_log("掉落计算完成 - 地图: {$mapName} ({$mapCode}), 关卡: {$currentStage}, 掉落数量: " . count($actualDrops));

        return array(
            'success' => true,
            'drops' => $actualDrops,
            'drop_config' => "境界掉落系统 - 怪物境界{$monsterRealm}",
            'drop_chance_used' => $dropChance,
            'random_chance' => $randomChance
        );
    } catch (Exception $e) {
        error_log("新结构掉落计算失败: " . $e->getMessage());
        return generateDefaultDrops($currentStage, $monsterType, $db);
    }
}

/**
 * 使用旧数据库结构计算掉落（向后兼容）
 */
function calculateDropsOldStructure($db, $mapCode, $currentStage, $monsterType)
{
    try {
        // 获取地图ID
        $stmt = $db->prepare("SELECT id FROM maps WHERE map_code = ?");
        $stmt->execute(array($mapCode));
        $map = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$map) {
            error_log("旧结构 - 地图不存在: {$mapCode}");
            return array('success' => true, 'drops' => array());
        }

        $mapId = $map['id'];

        // 获取可能的掉落物品
        $stmt = $db->prepare("
            SELECT d.*, md.bonus_rate 
            FROM drops d 
            JOIN map_drops md ON d.id = md.drop_id 
            WHERE md.map_id = ? 
            AND ? BETWEEN md.stage_range_start AND md.stage_range_end 
            AND (md.monster_type_filter = 'all' OR md.monster_type_filter = ?)
            ORDER BY d.rarity DESC, d.drop_rate DESC
        ");
        $stmt->execute(array($mapId, $currentStage, $monsterType));
        $possibleDrops = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $actualDrops = array();

        foreach ($possibleDrops as $drop) {
            // 计算实际掉落概率
            $finalDropRate = $drop['drop_rate'] * $drop['bonus_rate'];

            // BOSS额外增加掉落率
            if ($monsterType === 'boss') {
                $finalDropRate *= 1.5;
            }

            // 随机判断是否掉落
            if (mt_rand() / mt_getrandmax() <= $finalDropRate) {
                // 计算掉落数量
                $quantity = mt_rand($drop['min_quantity'], $drop['max_quantity']);

                // 解析物品数据
                $itemData = json_decode($drop['item_data'], true);
                if (!$itemData) {
                    $itemData = array();
                }

                // 🔧 修复：处理图片路径
                $iconPath = $drop['item_icon'];
                if ($iconPath && strpos($iconPath, 'assets/') !== 0) {
                    $iconPath = 'assets/images/' . $iconPath;
                }

                $actualDrops[] = array(
                    'id' => $drop['id'],
                    'name' => $drop['item_name'],
                    'type' => $drop['item_type'],
                    'rarity' => $drop['rarity'],
                    'quantity' => $quantity,
                    'icon_image' => $iconPath,  // 🔧 统一使用icon_image字段
                    'description' => $drop['description'],
                    'sell_price' => $drop['sell_price'],
                    'realm_requirement' => intval($drop['realm_requirement']) ?: 1, // 添加境界要求
                    'item_data' => $itemData
                );

                error_log("旧结构掉落物品: {$drop['item_name']}, 描述: '" . ($drop['description'] ?: '[空]') . "'");
            }
        }

        return array(
            'success' => true,
            'drops' => $actualDrops
        );
    } catch (Exception $e) {
        error_log("旧结构掉落计算失败: " . $e->getMessage());
        return array('success' => false, 'message' => $e->getMessage());
    }
}

/**
 * 根据掉落组类型和怪物类型生成物品稀有度
 * 🔧 修复：按照设计文档的品质概率设置
 */
function generateItemRarity($groupType, $monsterType)
{
    // 🔧 修复：使用设计文档中的标准品质概率
    // 基础概率：普通60%、稀有25%、史诗12%、传说3%

    $rarityChances = array();

    if ($groupType === 'boss' || $monsterType === 'boss') {
        // BOSS掉落：提升高品质概率，但保持合理性
        $rarityChances = array(
            'common' => 30,      // 普通 30% (降低)
            'uncommon' => 35,    // 稀有 35% (提升)
            'rare' => 25,        // 史诗 25% (大幅提升)
            'epic' => 8,         // 传说 8% (提升)
            'legendary' => 2     // 神话 2% (特殊获得)
        );
    } elseif ($groupType === 'rare' || $groupType === 'special') {
        // 稀有/特殊掉落组：中等概率提升
        $rarityChances = array(
            'common' => 45,      // 普通 45%
            'uncommon' => 30,    // 稀有 30%
            'rare' => 18,        // 史诗 18%
            'epic' => 6,         // 传说 6%
            'legendary' => 1     // 神话 1%
        );
    } else {
        // 普通掉落：严格按照设计文档的标准概率
        $rarityChances = array(
            'common' => 60,      // 普通 60%
            'uncommon' => 25,    // 稀有 25%
            'rare' => 12,        // 史诗 12%
            'epic' => 3,         // 传说 3%
            'legendary' => 0     // 神话 0% (普通掉落不出神话)
        );
    }

    // 🔧 修复：权重随机选择品质
    $totalWeight = array_sum($rarityChances);
    $randomWeight = mt_rand(1, $totalWeight);
    $currentWeight = 0;

    foreach ($rarityChances as $rarity => $weight) {
        $currentWeight += $weight;
        if ($randomWeight <= $currentWeight) {
            error_log("生成品质: {$rarity} (组类型: {$groupType}, 怪物类型: {$monsterType}, 概率: {$weight}%)");
            return $rarity;
        }
    }

    // 🔧 修复：默认返回普通品质
    error_log("品质生成失败，返回默认品质: common");
    return 'common';
}

/**
 * 保存战斗胜利结果（统一版本）
 * 直接保存到user_inventories表，并更新user_map_progress表
 */
function saveVictoryResultUnified($db)
{
    try {
        // 引入背包工具函数
        require_once __DIR__ . '/../includes/inventory_utils.php';

        // 🔧 添加调试信息
        error_log("=== 开始保存战斗结果 ===");
        error_log("Session user_id: " . (isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 'null'));
        error_log("POST数据: " . print_r($_POST, true));

        // 🔧 修复：支持游客模式，只有登录用户才保存数据
        if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
            // 游客模式，不保存数据但返回成功，允许继续游戏
            return array(
                'success' => true,
                'message' => '游客模式，数据未保存',
                'next_stage' => intval(isset($_POST['completed_stage']) ? $_POST['completed_stage'] : 1) + 1,
                'items_saved' => 0,
                'is_guest' => true
            );
        }

        $userId = $_SESSION['user_id'];
        $mapCode = isset($_POST['map_code']) ? $_POST['map_code'] : 'kunlun';
        $currentStage = intval(isset($_POST['completed_stage']) ? $_POST['completed_stage'] : 1);
        $droppedItems = isset($_POST['dropped_items']) ? json_decode($_POST['dropped_items'], true) : array();

        // 🔐 安全修复：从数据库重新计算奖励，绝不信任前端传来的值
        error_log("🔐 安全检查：从数据库重新计算奖励，拒绝前端传来的值");

        // 🔐 从数据库重新计算真实奖励值
        $realRewards = calculateRealRewards($db, $mapCode, $currentStage);
        $expGained = $realRewards['spirit_stones'];
        $goldGained = $realRewards['gold'];

        error_log("🔐 计算结果验证: expGained={$expGained}, goldGained={$goldGained}");

        error_log("🔐 数据库计算的真实奖励: 灵石={$expGained}, 金币={$goldGained}");

        // 🔐 检查前端传来的值是否与数据库计算的一致（仅用于安全监控）
        $frontendExpGained = intval(isset($_POST['exp_gained']) ? $_POST['exp_gained'] : 0);
        $frontendGoldGained = intval(isset($_POST['gold_gained']) ? $_POST['gold_gained'] : 0);

        if ($frontendExpGained != $expGained || $frontendGoldGained != $goldGained) {
            error_log("⚠️ 安全警告: 前端奖励与数据库不一致! 前端灵石={$frontendExpGained}, 数据库灵石={$expGained}; 前端金币={$frontendGoldGained}, 数据库金币={$goldGained}");
            // 使用数据库计算的值，忽略前端传来的值
        }

        // 🔧 新增：获取战斗时间
        $battleDuration = intval(isset($_POST['battle_duration']) ? $_POST['battle_duration'] : 0);

        // 🔧 新增：获取战斗回合数并验证
        $battleRounds = intval(isset($_POST['battle_rounds']) ? $_POST['battle_rounds'] : 0);
        $maxRounds = 30; // 最大回合数限制

        // 🔧 新增：验证回合数合理性
        if ($battleRounds > 0) {
            if ($battleRounds > $maxRounds) {
                error_log("⚠️ 检测到异常回合数（超过限制）: {$battleRounds}回合, 最多{$maxRounds}回合");
                return [
                    'success' => false,
                    'message' => '战斗回合数超出限制，可能存在异常操作'
                ];
            }

            if ($battleRounds < 1) {
                error_log("⚠️ 检测到异常回合数（过低）: {$battleRounds}回合, 最少1回合");
                // 不阻止保存，但记录日志
            }

            error_log("✅ 回合数验证通过: {$battleRounds}回合");
        } else {
            error_log("⚠️ 未接收到战斗回合数数据，可能使用了旧版前端");
        }

        // 🔧 修复：区分正常挂机和失败后挂机
        $isFailedHangupMode = isset($_POST['failed_hangup_mode']) && $_POST['failed_hangup_mode'] === 'true';
        $isNormalHangupMode = isset($_POST['normal_hangup_mode']) && $_POST['normal_hangup_mode'] === 'true';
        $isAutoBattleMode = isset($_POST['auto_battle_mode']) && $_POST['auto_battle_mode'] === 'true';
        $isNormalVictoryMode = isset($_POST['normal_victory_mode']) && $_POST['normal_victory_mode'] === 'true';

        // 🔧 修复：正常胜利和挂机都不自动更新地图进度
        if ($isFailedHangupMode || $isAutoBattleMode || $isNormalVictoryMode) {
            $currentStage = intval(isset($_POST['current_stage']) ? $_POST['current_stage'] : 1);
            if ($isNormalVictoryMode) {
                error_log("🎮 正常胜利模式：完成第{$currentStage}层，不自动更新地图进度");
            } else {
                error_log("🚩 挂机模式：重复挑战第{$currentStage}层，不更新进度");
            }
        } else {
            $currentStage = intval(isset($_POST['completed_stage']) ? $_POST['completed_stage'] : 1);
            if ($isNormalHangupMode) {
                error_log("🎮 正常挂机模式：完成第{$currentStage}层，将更新进度到下一层");
            } else {
                error_log("🎮 旧版正常模式：完成第{$currentStage}层，将更新进度到下一层");
            }
        }

        // 🔧 新增：简单的数值验证（防止明显异常）
        $maxExpPerBattle = 50000; // 单次战斗最大经验
        $maxGoldPerBattle = 10000; // 单次战斗最大金币
        $maxDropsPerBattle = 30; // 单次战斗最大掉落数量
        $minBattleDuration = 2; // 最短战斗时间（秒）
        $maxBattleDuration = 1800; // 最长战斗时间（30分钟）

        // 🔧 新增：验证战斗时间合理性
        if ($battleDuration > 0) {
            if ($battleDuration < $minBattleDuration) {
                error_log("⚠️ 检测到异常战斗时间（过短）: {$battleDuration}秒, 最少需要{$minBattleDuration}秒");
                // 不阻止保存，但记录日志
            }

            if ($battleDuration > $maxBattleDuration) {
                error_log("⚠️ 检测到异常战斗时间（过长）: {$battleDuration}秒, 最多{$maxBattleDuration}秒");
                // 可能是挂机，记录但不阻止
            }
        } else {
            error_log("⚠️ 未接收到战斗时间数据，可能使用了旧版前端");
        }

        // 验证并修正异常数值
        if ($expGained > $maxExpPerBattle) {
            error_log("⚠️ 检测到异常经验值: {$expGained}, 已修正为: {$maxExpPerBattle}");
            $expGained = $maxExpPerBattle;
        }

        if ($goldGained > $maxGoldPerBattle) {
            error_log("⚠️ 检测到异常金币值: {$goldGained}, 已修正为: {$maxGoldPerBattle}");
            $goldGained = $maxGoldPerBattle;
        }

        if (count($droppedItems) > $maxDropsPerBattle) {
            error_log("⚠️ 检测到异常掉落数量: " . count($droppedItems) . ", 已截取前{$maxDropsPerBattle}个");
            $droppedItems = array_slice($droppedItems, 0, $maxDropsPerBattle);
        }

        // 🧪 测试模式：如果没有掉落物品，添加一些虚拟物品来测试回收机制
        // 🚫 已禁用：测试模式代码，防止误导性的回收提示
        if (false && empty($droppedItems)) {
            error_log("🧪 测试模式：没有掉落物品，添加虚拟物品来测试回收机制");
        }

        // 🔧 新增：验证传说品质掉落数量（防止刷传说装备）
        $legendaryCount = 0;
        foreach ($droppedItems as $item) {
            if (isset($item['rarity']) && in_array($item['rarity'], ['legendary', '传说', 'epic', '史诗'])) {
                $legendaryCount++;
            }
        }

        if ($legendaryCount > 3) {
            error_log("⚠️ 检测到过多高品质掉落: {$legendaryCount}个, 这可能是异常情况");
            // 不阻止保存，但记录日志用于后续分析
        }

        $db->beginTransaction();

        // 获取角色ID和背包信息
        $stmt = $db->prepare("SELECT id, inventory_slots FROM characters WHERE user_id = ? LIMIT 1");
        $stmt->execute([$userId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$character) {
            error_log("保存战斗掉落失败: 找不到用户的活跃角色");
            return [
                'success' => false,
                'message' => '保存失败: 找不到活跃角色'
            ];
        }

        $characterId = $character['id'];
        $maxSlots = intval($character['inventory_slots'] ?: 30);

        // 🆕 获取装备拾取设置
        $pickupSettings = null;
        $currentRealmLevel = 1;

        try {
            // 获取拾取设置
            $stmt = $db->prepare("SELECT pickup_settings, realm_id FROM characters WHERE id = ?");
            $stmt->execute([$characterId]);
            $characterData = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($characterData && !empty($characterData['pickup_settings'])) {
                $pickupSettings = json_decode($characterData['pickup_settings'], true);
                error_log("🎯 装备拾取设置: " . json_encode($pickupSettings, JSON_UNESCAPED_UNICODE));
            } else {
                // 🔧 修复：如果没有设置，使用默认的全拾取设置
                $pickupSettings = [
                    'quality_filter' => ['普通', '稀有', '史诗', '传说', '神话'],
                    'filter_below_realm' => false
                ];
                error_log("🎯 使用默认装备拾取设置（全拾取）: " . json_encode($pickupSettings, JSON_UNESCAPED_UNICODE));
            }

            // 获取当前境界等级
            if ($characterData && $characterData['realm_id']) {
                // 🔧 修复双重JSON问题：直接实现getCurrentRealmLevel函数，避免包含cultivation.php
                $currentRealmLevel = getCurrentRealmLevelInternal($db, $characterData['realm_id']);
                error_log("🏔️ 当前境界等级: {$currentRealmLevel}");
            }
        } catch (Exception $e) {
            error_log("获取装备拾取设置失败: " . $e->getMessage());
            // 🔧 修复：异常情况下也使用默认设置
            $pickupSettings = [
                'quality_filter' => ['普通', '稀有', '史诗', '传说', '神话'],
                'filter_below_realm' => false
            ];
        }

        // 🔧 临时修改：强制设置较小的背包容量来测试回收功能
        // 🚫 已禁用：测试代码，防止强制触发回收机制
        // $maxSlots = 5; // 临时设置为5个槽位，强制触发回收
        // error_log("🧪 测试模式：临时设置背包容量为 {$maxSlots} 个槽位");

        // 🆕 新增：检查背包空间
        $stmt = $db->prepare("SELECT COUNT(*) as used_slots FROM user_inventories WHERE character_id = ?");
        $stmt->execute([$characterId]);
        $usedSlots = intval($stmt->fetch(PDO::FETCH_ASSOC)['used_slots']);
        $availableSlots = $maxSlots - $usedSlots;

        // 🆕 应用装备拾取设置过滤
        $originalDroppedItems = $droppedItems; // 🔧 保存原始掉落数据，用于后续显示
        $filteredDroppedItems = [];
        $autoRecycledItems = [];
        $autoRecycleGold = 0;

        error_log("🎯 开始装备拾取过滤，原始物品数量: " . count($droppedItems));
        error_log("🎯 装备拾取设置: " . json_encode($pickupSettings));
        error_log("🎯 当前境界等级: " . $currentRealmLevel);

        foreach ($droppedItems as $index => $drop) {
            $originalItemType = $drop['type'];
            $itemType = mapItemTypeToDatabase($originalItemType);

            error_log("🎯 处理物品{$index}: {$drop['name']} (原始类型: {$originalItemType}, 数据库类型: {$itemType}, 品质: {$drop['rarity']}, 售价: {$drop['sell_price']})");

            // 检查是否为装备类物品
            if (in_array($itemType, ['weapon', 'equipment', 'armor', 'accessory'])) {
                error_log("🎯 装备类物品，开始过滤检查");
                // 应用装备拾取设置过滤
                $shouldPickup = shouldPickupEquipmentInternal($drop, $pickupSettings, $currentRealmLevel);
                error_log("🎯 过滤结果: " . ($shouldPickup ? '保留' : '回收'));

                if (!$shouldPickup) {
                    // 不拾取，自动回收 - 使用统一的回收价格计算逻辑
                    $sellPrice = intval($drop['sell_price']) ?: 1;
                    $itemRarity = $drop['rarity'] ?: '普通';

                    // 为没有售价的物品设置默认售价（与前端逻辑保持一致）
                    if ($sellPrice <= 0) {
                        $basePrice = 10; // 装备基础售价

                        // 根据品质调整售价
                        $qualityMultiplier = 1.0;
                        switch ($itemRarity) {
                            case '普通':
                                $qualityMultiplier = 1.0;
                                break;
                            case '稀有':
                                $qualityMultiplier = 1.5;
                                break;
                            case '史诗':
                                $qualityMultiplier = 2.0;
                                break;
                            case '传说':
                                $qualityMultiplier = 3.0;
                                break;
                            case '神话':
                                $qualityMultiplier = 5.0;
                                break;
                        }

                        $sellPrice = intval($basePrice * $qualityMultiplier);
                    }

                    // 计算回收价格（品质越高回收比例越高）
                    $recycleRatio = 0.4; // 基础回收比例40%
                    switch ($itemRarity) {
                        case '稀有':
                            $recycleRatio = 0.45;
                            break; // 稀有45%
                        case '史诗':
                            $recycleRatio = 0.5;
                            break; // 史诗50%
                        case '传说':
                            $recycleRatio = 0.55;
                            break; // 传说55%
                        case '神话':
                            $recycleRatio = 0.6;
                            break; // 神话60%
                    }

                    $recyclePrice = max(1, intval($sellPrice * $recycleRatio));
                    $autoRecycleGold += $recyclePrice;
                    $autoRecycledItems[] = $drop['name'];
                    error_log("♻️ 装备回收: {$drop['name']} (品质: {$itemRarity}) 售价: {$sellPrice} -> 回收价: {$recyclePrice}");
                    continue;
                }
                error_log("✅ 装备保留: {$drop['name']}");
            } else {
                error_log("✅ 非装备物品保留: {$drop['name']}");
            }

            // 通过过滤，添加到最终掉落列表
            $filteredDroppedItems[] = $drop;
        }

        error_log("🎯 装备拾取过滤结果: 原始{" . count($droppedItems) . "}件 -> 过滤后{" . count($filteredDroppedItems) . "}件，自动回收{" . count($autoRecycledItems) . "}件，回收金币: {$autoRecycleGold}");

        // 🔧 调试：详细记录过滤后的物品
        foreach ($filteredDroppedItems as $index => $item) {
            error_log("🎯 过滤后物品{$index}: {$item['name']} (类型: {$item['type']}, 品质: {$item['rarity']})");
        }

        // 🔧 调试：记录过滤前后的数组变化
        error_log("🔧 过滤前droppedItems数量: " . count($droppedItems));
        error_log("🔧 过滤后filteredDroppedItems数量: " . count($filteredDroppedItems));

        // 更新掉落物品列表为过滤后的列表
        $droppedItems = $filteredDroppedItems;

        // 🔧 调试：确认更新后的数组
        error_log("🔧 更新后droppedItems数量: " . count($droppedItems));

        // 计算需要的背包空间（装备类物品每个占一格，可堆叠物品可能合并）
        $requiredSlots = 0;
        $stackableItems = []; // 可堆叠物品统计

        foreach ($droppedItems as $drop) {
            $originalItemType = $drop['type'];
            $itemType = mapItemTypeToDatabase($originalItemType);
            $quantity = intval($drop['quantity']);

            if (in_array($itemType, ['material', 'consumable', 'spirit'])) {
                // 可堆叠物品，检查是否已有相同物品
                $itemName = $drop['name'];
                if (!isset($stackableItems[$itemName])) {
                    $stackableItems[$itemName] = 0;

                    // 检查背包中是否已有此物品
                    $stmt = $db->prepare("
                        SELECT COUNT(*) as existing_count FROM user_inventories ui
                        JOIN game_items gi ON ui.item_id = gi.id
                        WHERE ui.character_id = ? AND gi.item_name = ? AND ui.bind_status = 'unbound'
                    ");
                    $stmt->execute([$characterId, $itemName]);
                    $existingCount = intval($stmt->fetch(PDO::FETCH_ASSOC)['existing_count']);

                    if ($existingCount == 0) {
                        $requiredSlots++; // 需要新的背包位置
                    }
                }
                $stackableItems[$itemName] += $quantity;
            } else {
                // 装备类物品，每个占一格
                $requiredSlots += $quantity;
            }
        }

        error_log("🎒 背包空间检查 - 已用: {$usedSlots}/{$maxSlots}, 可用: {$availableSlots}, 需要: {$requiredSlots}");

        // 🔧 修复：初始化变量，确保在所有情况下都有正确的值
        $recycledItems = [];
        $recycleGold = 0;
        $totalGoldGained = $goldGained + $autoRecycleGold; // 🔧 修复：包含自动回收的金币

        // 如果背包空间不足，自动回收装备并计算回收金币
        if ($requiredSlots > $availableSlots) {
            $shortageSlots = $requiredSlots - $availableSlots;
            // 🔧 修复：重置变量，准备处理回收逻辑
            $recycledItems = [];
            $recycleGold = 0;
            $savedItems = [];

            // 分离可保存的物品和需要回收的物品
            $itemsToProcess = [];
            foreach ($droppedItems as $drop) {
                $originalItemType = $drop['type'];
                $itemType = mapItemTypeToDatabase($originalItemType);
                $quantity = intval($drop['quantity']);

                if (in_array($itemType, ['material', 'consumable', 'spirit'])) {
                    // 可堆叠物品，检查是否已有相同物品
                    $itemName = $drop['name'];
                    $stmt = $db->prepare("
                        SELECT COUNT(*) as existing_count FROM user_inventories ui
                        JOIN game_items gi ON ui.item_id = gi.id
                        WHERE ui.character_id = ? AND gi.item_name = ? AND ui.bind_status = 'unbound'
                    ");
                    $stmt->execute([$characterId, $itemName]);
                    $existingCount = intval($stmt->fetch(PDO::FETCH_ASSOC)['existing_count']);

                    if ($existingCount > 0) {
                        // 可以堆叠，不占用新空间
                        $itemsToProcess[] = ['item' => $drop, 'slots_needed' => 0, 'can_save' => true];
                    } else {
                        // 需要新空间
                        $itemsToProcess[] = ['item' => $drop, 'slots_needed' => 1, 'can_save' => false];
                    }
                } else {
                    // 装备类物品，每个占一格
                    for ($i = 0; $i < $quantity; $i++) {
                        $itemsToProcess[] = ['item' => $drop, 'slots_needed' => 1, 'can_save' => false, 'is_single_equipment' => true];
                    }
                }
            }

            // 优先保存可堆叠的物品
            $usedSlots = 0;
            foreach ($itemsToProcess as &$processItem) {
                if ($processItem['can_save'] || $usedSlots + $processItem['slots_needed'] <= $availableSlots) {
                    $processItem['can_save'] = true;
                    $usedSlots += $processItem['slots_needed'];
                } else {
                    $processItem['can_save'] = false;
                    // 计算回收金币 - 使用统一的回收价格计算逻辑
                    $item = $processItem['item'];
                    $sellPrice = intval($item['sell_price']) ?: 1;
                    $itemRarity = $item['rarity'] ?: '普通';

                    // 为没有售价的物品设置默认售价
                    if ($sellPrice <= 0) {
                        $basePrice = 10; // 装备基础售价

                        // 根据品质调整售价
                        $qualityMultiplier = 1.0;
                        switch ($itemRarity) {
                            case '普通':
                                $qualityMultiplier = 1.0;
                                break;
                            case '稀有':
                                $qualityMultiplier = 1.5;
                                break;
                            case '史诗':
                                $qualityMultiplier = 2.0;
                                break;
                            case '传说':
                                $qualityMultiplier = 3.0;
                                break;
                            case '神话':
                                $qualityMultiplier = 5.0;
                                break;
                        }

                        $sellPrice = intval($basePrice * $qualityMultiplier);
                    }

                    // 计算回收价格（品质越高回收比例越高）
                    $recycleRatio = 0.4; // 基础回收比例40%
                    switch ($itemRarity) {
                        case '稀有':
                            $recycleRatio = 0.45;
                            break; // 稀有45%
                        case '史诗':
                            $recycleRatio = 0.5;
                            break; // 史诗50%
                        case '传说':
                            $recycleRatio = 0.55;
                            break; // 传说55%
                        case '神话':
                            $recycleRatio = 0.6;
                            break; // 神话60%
                    }

                    $recyclePrice = max(1, intval($sellPrice * $recycleRatio));
                    $recycleGold += $recyclePrice;
                    $recycledItems[] = $item['name'];
                }
            }

            // 🔧 修复：更新$droppedItems数组，只保留实际保存的物品
            $actualSavedItems = [];

            // 保存可以保存的物品
            $itemsSaved = 0;
            foreach ($itemsToProcess as $processItem) {
                if (!$processItem['can_save']) continue;

                // 🔧 将实际保存的物品添加到新数组
                if (!isset($processItem['is_single_equipment'])) {
                    // 非单件装备（堆叠物品或完整的装备批次）
                    $actualSavedItems[] = $processItem['item'];
                }

                $drop = $processItem['item'];
                $itemName = $drop['name'];
                $originalItemType = $drop['type'];
                $itemRarity = $drop['rarity'];
                $quantity = isset($processItem['is_single_equipment']) ? 1 : intval($drop['quantity']);
                $itemData = isset($drop['item_data']) ? $drop['item_data'] : array();

                $itemType = mapItemTypeToDatabase($originalItemType);
                $description = isset($drop['description']) ? $drop['description'] : '';

                // 查找或创建物品记录（与原逻辑相同）
                $gameItemStmt = $db->prepare("
                    SELECT id, item_code, item_type, slot_type, realm_requirement, max_durability 
                    FROM game_items 
                    WHERE item_name = ? AND item_type = ?
                ");
                $gameItemStmt->execute([$itemName, $itemType]);
                $gameItem = $gameItemStmt->fetch(PDO::FETCH_ASSOC);

                if (!$gameItem) {
                    // 创建临时物品记录
                    try {
                        $tempItemCode = 'temp_' . strtolower(str_replace(' ', '_', $itemName)) . '_' . time();
                        $defaultSlotType = getSlotTypeFromItemType($itemType);
                        $defaultMaxDurability = ($itemType === 'weapon' || $itemType === 'equipment') ? 100 : null;
                        $defaultRealmRequirement = 1;

                        $createItemStmt = $db->prepare("
                            INSERT INTO game_items (
                                item_code, item_name, item_type, slot_type, 
                                realm_requirement, max_durability, description,
                                created_at
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                        ");
                        $createItemStmt->execute([
                            $tempItemCode,
                            $itemName,
                            $itemType,
                            $defaultSlotType,
                            $defaultRealmRequirement,
                            $defaultMaxDurability,
                            $description
                        ]);

                        $itemId = $db->lastInsertId();
                        $maxDurability = $defaultMaxDurability ?: 100;
                    } catch (Exception $e) {
                        error_log("创建临时物品失败: " . $e->getMessage());
                        continue;
                    }
                } else {
                    $itemId = $gameItem['id'];
                    $maxDurability = $gameItem['max_durability'] ? $gameItem['max_durability'] : 100;
                }

                // 保存物品到背包（与原逻辑相同）
                if (in_array($itemType, ['material', 'consumable', 'spirit'])) {
                    // 可堆叠物品
                    $stmt = $db->prepare("
                        SELECT id, quantity FROM user_inventories 
                        WHERE character_id = ? AND item_id = ? AND bind_status = 'unbound'
                    ");
                    $stmt->execute([$characterId, $itemId]);
                    $existingItem = $stmt->fetch(PDO::FETCH_ASSOC);

                    if ($existingItem) {
                        $newQuantity = $existingItem['quantity'] + $quantity;
                        $stmt = $db->prepare("
                            UPDATE user_inventories 
                            SET quantity = ?, updated_at = CURRENT_TIMESTAMP 
                            WHERE id = ?
                        ");
                        $stmt->execute([$newQuantity, $existingItem['id']]);
                    } else {
                        // 创建新的背包位置
                        $sortWeight = calculateSortWeight($db, $characterId, $itemType);

                        $stmt = $db->prepare("
                            INSERT INTO user_inventories (
                                character_id, item_id, item_type, quantity, 
                                bind_status, obtained_source, obtained_time, sort_weight
                            ) VALUES (?, ?, ?, ?, 'unbound', 'battle_drop', CURRENT_TIMESTAMP, ?)
                        ");
                        $stmt->execute([
                            $characterId,
                            $itemId,
                            $itemType,
                            $quantity,
                            $sortWeight
                        ]);

                        error_log("堆叠物品保存成功 - 新增: {$itemName} x{$quantity}");
                    }
                } else {
                    error_log("装备类物品，逐个保存 {$quantity} 件");

                    // 装备类物品（weapon, equipment, armor, accessory, special），每个都是独立的，使用品质系统
                    for ($i = 0; $i < $quantity; $i++) {
                        error_log("保存第 " . ($i + 1) . " 件装备: {$itemName}");

                        // 🔧 使用统一的品质系统处理装备
                        $customAttributes = [];

                        // 将英文品质转换为中文
                        $chineseRarityMapping = [
                            'common' => '普通',
                            'uncommon' => '稀有',
                            'rare' => '史诗',
                            'epic' => '传说',
                            'legendary' => '神话'
                        ];
                        $chineseRarity = isset($chineseRarityMapping[$itemRarity]) ? $chineseRarityMapping[$itemRarity] : '普通';

                        // 如果是装备类物品，使用品质系统
                        if (in_array($itemType, ['weapon', 'equipment', 'armor', 'accessory'])) {
                            try {
                                // 引入品质系统
                                require_once __DIR__ . '/equipment_quality_system.php';

                                // 使用品质系统获取完整的装备数据
                                $equipmentData = EquipmentQualitySystem::getEquipmentForInventory(
                                    $itemId,
                                    $chineseRarity,  // 使用转换后的中文品质
                                    'battle'  // 战斗掉落使用battle类型
                                );

                                // 使用品质系统的数据
                                $customAttributes = [
                                    'rarity' => $equipmentData['rarity'],
                                    'rarity_en' => $equipmentData['rarity_en'],
                                    'rarity_color' => $equipmentData['rarity_color'],
                                    'multiplier' => $equipmentData['multiplier'],
                                    'generation_time' => time(),
                                    'context_type' => 'battle_drop'
                                ];

                                if (!empty($equipmentData['attributes'])) {
                                    $customAttributes['calculated_attributes'] = $equipmentData['attributes'];
                                }

                                error_log("品质系统处理成功(背包充足) - 装备: {$itemName}, 品质: {$equipmentData['rarity']}, 倍率: {$equipmentData['multiplier']}");
                            } catch (Exception $e) {
                                error_log("品质系统处理失败(背包充足): " . $e->getMessage() . ", 使用传统处理方式");

                                // 如果品质系统失败，使用传统方式
                                $customAttributes = [
                                    'rarity' => $chineseRarity,
                                    'rarity_en' => $itemRarity,
                                    'generation_time' => time(),
                                    'context_type' => 'battle_drop'
                                ];

                                // 处理传统属性（保持兼容性）
                                if (isset($itemData)) {
                                    $equipmentAttributes = processEquipmentAttributes($itemData, $itemRarity);
                                    if (!empty($equipmentAttributes)) {
                                        $customAttributes = array_merge($customAttributes, $equipmentAttributes);
                                    }
                                }
                            }
                        } else {
                            // 非装备物品的品质信息
                            $customAttributes = [
                                'rarity' => $chineseRarity,
                                'rarity_en' => $itemRarity,
                                'generation_time' => time(),
                                'context_type' => 'battle_drop'
                            ];
                        }

                        $sortWeight = calculateSortWeight($db, $characterId, $itemType);

                        $stmt = $db->prepare("
                            INSERT INTO user_inventories (
                                character_id, item_id, item_type, current_durability, max_durability,
                                custom_attributes, bind_status, obtained_source, obtained_time, sort_weight
                            ) VALUES (?, ?, ?, ?, ?, ?, 'unbound', 'battle_drop', CURRENT_TIMESTAMP, ?)
                        ");
                        $stmt->execute([
                            $characterId,
                            $itemId,
                            $itemType,
                            $maxDurability,
                            $maxDurability,
                            !empty($customAttributes) ? json_encode($customAttributes) : null,
                            $sortWeight
                        ]);

                        $inventoryItemId = $db->lastInsertId();
                        error_log("装备物品保存成功(背包充足): {$itemName}, ID: {$inventoryItemId}");
                    }
                }
                $itemsSaved++;

                error_log("物品 {$itemName} 处理完成，总计已保存: {$itemsSaved} 种物品");
            }

            // 🔧 修复：更新$droppedItems为实际保存的物品
            $droppedItems = $actualSavedItems;

            // 🔧 修复：计算包含回收金币的总金币
            $totalGoldGained = $goldGained + $recycleGold;
            error_log("🎒 背包空间不足处理完成 - 原始金币: {$goldGained}, 回收金币: {$recycleGold}, 总金币: {$totalGoldGained}");
            error_log("🔧 修复：实际保存物品数量: " . count($droppedItems) . ", 回收物品数量: " . count($recycledItems));
        } else {
            error_log("背包空间充足，正常保存所有物品");
            error_log("🔧 准备保存的物品数量: " . count($droppedItems));

            // 🔧 修复：实现背包空间充足时的物品保存逻辑
            $itemsSaved = 0;
            foreach ($droppedItems as $drop) {
                $itemName = $drop['name'];
                $originalItemType = $drop['type'];
                $itemRarity = $drop['rarity'];
                $quantity = intval($drop['quantity']);
                $itemData = isset($drop['item_data']) ? $drop['item_data'] : array();

                $itemType = mapItemTypeToDatabase($originalItemType);
                $description = isset($drop['description']) ? $drop['description'] : '';

                // 查找或创建物品记录
                $gameItemStmt = $db->prepare("
                    SELECT id, item_code, item_type, slot_type, realm_requirement, max_durability 
                    FROM game_items 
                    WHERE item_name = ? AND item_type = ?
                ");
                $gameItemStmt->execute([$itemName, $itemType]);
                $gameItem = $gameItemStmt->fetch(PDO::FETCH_ASSOC);

                if (!$gameItem) {
                    // 创建临时物品记录
                    try {
                        $tempItemCode = 'temp_' . strtolower(str_replace(' ', '_', $itemName)) . '_' . time();
                        $defaultSlotType = getSlotTypeFromItemType($itemType);
                        $defaultMaxDurability = ($itemType === 'weapon' || $itemType === 'equipment') ? 100 : null;
                        $defaultRealmRequirement = 1;

                        $createItemStmt = $db->prepare("
                            INSERT INTO game_items (
                                item_code, item_name, item_type, slot_type, 
                                realm_requirement, max_durability, description,
                                created_at
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                        ");
                        $createItemStmt->execute([
                            $tempItemCode,
                            $itemName,
                            $itemType,
                            $defaultSlotType,
                            $defaultRealmRequirement,
                            $defaultMaxDurability,
                            $description
                        ]);

                        $itemId = $db->lastInsertId();
                        $maxDurability = $defaultMaxDurability ?: 100;
                        error_log("创建临时物品成功: {$itemName}, ID: {$itemId}");
                    } catch (Exception $e) {
                        error_log("创建临时物品失败: " . $e->getMessage());
                        continue;
                    }
                } else {
                    $itemId = $gameItem['id'];
                    $maxDurability = $gameItem['max_durability'] ? $gameItem['max_durability'] : 100;
                }

                // 保存物品到背包
                if (in_array($itemType, ['material', 'consumable', 'spirit'])) {
                    // 可堆叠物品
                    $stmt = $db->prepare("
                        SELECT id, quantity FROM user_inventories 
                        WHERE character_id = ? AND item_id = ? AND bind_status = 'unbound'
                    ");
                    $stmt->execute([$characterId, $itemId]);
                    $existingItem = $stmt->fetch(PDO::FETCH_ASSOC);

                    if ($existingItem) {
                        $newQuantity = $existingItem['quantity'] + $quantity;
                        $stmt = $db->prepare("
                            UPDATE user_inventories 
                            SET quantity = ?, updated_at = CURRENT_TIMESTAMP 
                            WHERE id = ?
                        ");
                        $stmt->execute([$newQuantity, $existingItem['id']]);
                        error_log("堆叠物品更新成功: {$itemName} x{$quantity} (总计: {$newQuantity})");
                    } else {
                        $sortWeight = calculateSortWeight($db, $characterId, $itemType);

                        $stmt = $db->prepare("
                            INSERT INTO user_inventories (
                                character_id, item_id, item_type, quantity, 
                                bind_status, obtained_source, obtained_time, sort_weight
                            ) VALUES (?, ?, ?, ?, 'unbound', 'battle_drop', CURRENT_TIMESTAMP, ?)
                        ");
                        $stmt->execute([
                            $characterId,
                            $itemId,
                            $itemType,
                            $quantity,
                            $sortWeight
                        ]);
                        error_log("堆叠物品保存成功 - 新增: {$itemName} x{$quantity}");
                    }
                } else {
                    // 装备类物品，每个都是独立的
                    for ($i = 0; $i < $quantity; $i++) {
                        // 🔧 修复：背包空间充足时也要使用品质系统
                        $customAttributes = [];

                        // 将英文品质转换为中文
                        $chineseRarityMapping = [
                            'common' => '普通',
                            'uncommon' => '稀有',
                            'rare' => '史诗',
                            'epic' => '传说',
                            'legendary' => '神话'
                        ];
                        $chineseRarity = isset($chineseRarityMapping[$itemRarity]) ? $chineseRarityMapping[$itemRarity] : '普通';

                        // 如果是装备类物品，使用品质系统
                        if (in_array($itemType, ['weapon', 'equipment', 'armor', 'accessory'])) {
                            try {
                                // 引入品质系统
                                require_once __DIR__ . '/equipment_quality_system.php';

                                // 使用品质系统获取完整的装备数据
                                $equipmentData = EquipmentQualitySystem::getEquipmentForInventory(
                                    $itemId,
                                    $chineseRarity,  // 使用转换后的中文品质
                                    'battle'  // 战斗掉落使用battle类型
                                );

                                // 使用品质系统的数据
                                $customAttributes = [
                                    'rarity' => $equipmentData['rarity'],
                                    'rarity_en' => $equipmentData['rarity_en'],
                                    'rarity_color' => $equipmentData['rarity_color'],
                                    'multiplier' => $equipmentData['multiplier'],
                                    'generation_time' => time(),
                                    'context_type' => 'battle_drop'
                                ];

                                if (!empty($equipmentData['attributes'])) {
                                    $customAttributes['calculated_attributes'] = $equipmentData['attributes'];
                                }

                                error_log("品质系统处理成功(背包充足) - 装备: {$itemName}, 品质: {$equipmentData['rarity']}, 倍率: {$equipmentData['multiplier']}");
                            } catch (Exception $e) {
                                error_log("品质系统处理失败(背包充足): " . $e->getMessage() . ", 使用传统处理方式");

                                // 如果品质系统失败，使用传统方式
                                $customAttributes = [
                                    'rarity' => $chineseRarity,
                                    'rarity_en' => $itemRarity,
                                    'generation_time' => time(),
                                    'context_type' => 'battle_drop'
                                ];

                                // 处理传统属性（保持兼容性）
                                if (isset($itemData)) {
                                    $equipmentAttributes = processEquipmentAttributes($itemData, $itemRarity);
                                    if (!empty($equipmentAttributes)) {
                                        $customAttributes = array_merge($customAttributes, $equipmentAttributes);
                                    }
                                }
                            }
                        } else {
                            // 非装备物品的品质信息
                            $customAttributes = [
                                'rarity' => $chineseRarity,
                                'rarity_en' => $itemRarity,
                                'generation_time' => time(),
                                'context_type' => 'battle_drop'
                            ];
                        }

                        $sortWeight = calculateSortWeight($db, $characterId, $itemType);

                        $stmt = $db->prepare("
                            INSERT INTO user_inventories (
                                character_id, item_id, item_type, current_durability, max_durability,
                                custom_attributes, bind_status, obtained_source, obtained_time, sort_weight
                            ) VALUES (?, ?, ?, ?, ?, ?, 'unbound', 'battle_drop', CURRENT_TIMESTAMP, ?)
                        ");
                        $stmt->execute([
                            $characterId,
                            $itemId,
                            $itemType,
                            $maxDurability,
                            $maxDurability,
                            !empty($customAttributes) ? json_encode($customAttributes) : null,
                            $sortWeight
                        ]);

                        $inventoryItemId = $db->lastInsertId();
                        error_log("装备物品保存成功(背包充足): {$itemName}, ID: {$inventoryItemId}");
                    }
                }
                $itemsSaved++;

                error_log("物品 {$itemName} 处理完成，总计已保存: {$itemsSaved} 种物品");
            }

            error_log("✅ 背包空间充足，所有物品保存完成，共保存 {$itemsSaved} 种物品");
        }

        // 🔧 新增：更新用户的金币和灵石
        if ($expGained > 0 || $totalGoldGained > 0) {
            error_log("=== 开始更新用户奖励 ===");
            error_log("灵石奖励: {$expGained}, 金币奖励: {$totalGoldGained} (原始: {$goldGained}, 回收: {$recycleGold})");

            try {
                // 更新users表中的金币和灵石
                $stmt = $db->prepare("
                    UPDATE users 
                    SET gold = gold + ?, 
                        spirit_stones = spirit_stones + ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ");
                $stmt->execute([$totalGoldGained, $expGained, $userId]);

                $affectedRows = $stmt->rowCount();
                if ($affectedRows > 0) {
                    error_log("✅ 用户奖励更新成功 - 用户ID: {$userId}, 金币+{$totalGoldGained}, 灵石+{$expGained}");
                } else {
                    error_log("⚠️ 用户奖励更新失败 - 没有影响任何行，用户ID: {$userId}");
                }

                // 🔧 新增：记录资源变动日志（如果表存在）
                $stmt = $db->prepare("SHOW TABLES LIKE 'user_resources_log'");
                $stmt->execute();
                $resourceLogExists = $stmt->rowCount() > 0;

                if ($resourceLogExists) {
                    try {
                        if ($totalGoldGained > 0) {
                            $stmt = $db->prepare("
                                INSERT INTO user_resources_log (
                                    user_id, resource_type, change_amount, change_reason, 
                                    battle_stage, created_at
                                ) VALUES (?, 'gold', ?, 'battle_victory', ?, CURRENT_TIMESTAMP)
                            ");
                            $stmt->execute([$userId, $totalGoldGained, $currentStage]);
                        }

                        if ($expGained > 0) {
                            $stmt = $db->prepare("
                                INSERT INTO user_resources_log (
                                    user_id, resource_type, change_amount, change_reason, 
                                    battle_stage, created_at
                                ) VALUES (?, 'spirit_stones', ?, 'battle_victory', ?, CURRENT_TIMESTAMP)
                            ");
                            $stmt->execute([$userId, $expGained, $currentStage]);
                        }

                        error_log("✅ 资源变动日志记录成功");
                    } catch (Exception $e) {
                        error_log("⚠️ 资源变动日志记录失败: " . $e->getMessage());
                        // 不影响主流程
                    }
                }
            } catch (Exception $e) {
                error_log("❌ 更新用户奖励失败: " . $e->getMessage());
                // 这是关键错误，需要回滚事务
                throw $e;
            }
        }

        // 2. 更新或创建地图进度（战斗胜利时才更新进度）
        // 🔧 修复：挂机模式下不更新进度，只更新战斗统计
        if ($isAutoBattleMode) {
            $nextStage = $currentStage; // 挂机模式保持当前层数
            error_log("🤖 挂机模式：保持在第{$currentStage}层，不更新进度");
        } else {
            $nextStage = $currentStage + 1; // 正常模式进入下一层
            error_log("🎮 正常模式：从第{$currentStage}层进入第{$nextStage}层");
        }

        error_log("=== 开始保存战斗进度 ===");
        error_log("当前关卡: {$currentStage}, 下一关卡: {$nextStage}, 地图代码: {$mapCode}, 挂机模式: " . ($isAutoBattleMode ? '是' : '否'));

        // 获取地图信息
        $mapId = 0;
        $maxStages = 100; // 默认值

        // 🔧 修复：首先尝试从game_maps获取地图信息
        $stmt = $db->prepare("SHOW TABLES LIKE 'game_maps'");
        $stmt->execute();
        $gameMapsExists = $stmt->rowCount() > 0;

        if ($gameMapsExists) {
            // 使用新的数据库结构
            $stmt = $db->prepare("SELECT id, max_stages FROM game_maps WHERE map_code = ?");
            $stmt->execute([$mapCode]);
            $mapInfo = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($mapInfo) {
                $mapId = $mapInfo['id'];
                $maxStages = $mapInfo['max_stages'] ?: 100;

                // 🔧 修复：使用character_id和map_code查询用户地图进度
                $stmt = $db->prepare("
                    SELECT id, current_stage, max_stage_reached, total_battles, total_victories 
                    FROM user_map_progress 
                    WHERE character_id = ? AND map_code = ?
                ");
                $stmt->execute([$characterId, $mapCode]);
                $progress = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($progress) {
                    // 🔧 修复：只有失败后挂机才不更新进度，正常挂机要推进进度
                    if ($isFailedHangupMode || $isAutoBattleMode) {
                        // 失败后挂机模式：只更新战斗统计，不改变current_stage
                        $stmt = $db->prepare("
                            UPDATE user_map_progress 
                            SET total_battles = total_battles + 1,
                                total_victories = total_victories + 1,
                                last_played_at = CURRENT_TIMESTAMP
                            WHERE id = ?
                        ");
                        $stmt->execute([$progress['id']]);

                        error_log("🚩 失败后挂机：只更新战斗统计 - 地图代码: {$mapCode}, 保持关卡: {$currentStage}");
                    } else {
                        // 正常模式和正常挂机：更新进度和战斗统计
                        $newMaxStage = max($progress['max_stage_reached'], $currentStage);
                        $newCurrentStage = min($nextStage, $maxStages);

                        $stmt = $db->prepare("
                            UPDATE user_map_progress 
                            SET current_stage = ?, 
                                max_stage_reached = ?,
                                total_battles = total_battles + 1,
                                total_victories = total_victories + 1,
                                last_played_at = CURRENT_TIMESTAMP
                            WHERE id = ?
                        ");
                        $stmt->execute([$newCurrentStage, $newMaxStage, $progress['id']]);

                        if ($isNormalHangupMode) {
                            error_log("🎮 正常挂机：更新地图进度 - 地图代码: {$mapCode}, 当前关卡: {$newCurrentStage}, 最高关卡: {$newMaxStage}");
                        } else {
                            error_log("🎮 正常模式：更新地图进度 - 地图代码: {$mapCode}, 当前关卡: {$newCurrentStage}, 最高关卡: {$newMaxStage}");
                        }
                    }
                } else {
                    // 创建新的进度记录
                    if ($isFailedHangupMode || $isAutoBattleMode) {
                        // 失败后挂机：创建进度但不前进到下一层
                        $stmt = $db->prepare("
                            INSERT INTO user_map_progress (
                                character_id, map_id, map_code, current_stage, max_stage_reached,
                                total_battles, total_victories, last_played_at
                            ) VALUES (?, ?, ?, ?, ?, 1, 1, CURRENT_TIMESTAMP)
                        ");
                        $stmt->execute([
                            $characterId,
                            $mapId,
                            $mapCode,
                            $currentStage,
                            $currentStage
                        ]);

                        error_log("🚩 失败后挂机：创建地图进度 - 地图代码: {$mapCode}, 保持关卡: {$currentStage}");
                    } else {
                        // 正常模式和正常挂机：创建进度并前进到下一层
                        $stmt = $db->prepare("
                            INSERT INTO user_map_progress (
                                character_id, map_id, map_code, current_stage, max_stage_reached,
                                total_battles, total_victories, last_played_at
                            ) VALUES (?, ?, ?, ?, ?, 1, 1, CURRENT_TIMESTAMP)
                        ");
                        $stmt->execute([
                            $characterId,
                            $mapId,
                            $mapCode,
                            min($nextStage, $maxStages),
                            $currentStage
                        ]);

                        if ($isNormalHangupMode) {
                            error_log("🎮 正常挂机：创建地图进度 - 地图代码: {$mapCode}, 当前关卡: " . min($nextStage, $maxStages));
                        } else {
                            error_log("🎮 正常模式：创建地图进度 - 地图代码: {$mapCode}, 当前关卡: " . min($nextStage, $maxStages));
                        }
                    }
                }
            } else {
                error_log("警告：地图 {$mapCode} 在game_maps表中不存在");
            }
        } else {
            // 🔧 回退到旧的数据库结构
            $stmt = $db->prepare("SELECT id FROM maps WHERE map_code = ?");
            $stmt->execute([$mapCode]);
            $mapInfo = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($mapInfo) {
                $mapId = $mapInfo['id'];

                // 检查是否有user_map_progress表
                $stmt = $db->prepare("SHOW TABLES LIKE 'user_map_progress'");
                $stmt->execute();
                $progressTableExists = $stmt->rowCount() > 0;

                if ($progressTableExists) {
                    // 🔧 修复：旧结构也使用map_code查询
                    $stmt = $db->prepare("
                        SELECT id, current_stage, max_stage_reached 
                        FROM user_map_progress 
                        WHERE character_id = ? AND map_code = ?
                    ");
                    $stmt->execute([$characterId, $mapCode]);
                    $progress = $stmt->fetch(PDO::FETCH_ASSOC);

                    if ($progress) {
                        // 更新现有进度
                        $newMaxStage = max($progress['max_stage_reached'], $currentStage);
                        $newCurrentStage = $nextStage;

                        $stmt = $db->prepare("
                            UPDATE user_map_progress 
                            SET current_stage = ?, max_stage_reached = ?
                            WHERE id = ?
                        ");
                        $stmt->execute([$newCurrentStage, $newMaxStage, $progress['id']]);
                    } else {
                        // 创建新的进度记录
                        $stmt = $db->prepare("
                            INSERT INTO user_map_progress (character_id, map_id, map_code, current_stage, max_stage_reached)
                            VALUES (?, ?, ?, ?, ?)
                        ");
                        $stmt->execute([$characterId, $mapId, $mapCode, $nextStage, $currentStage]);
                    }
                }
            }
        }

        // 3. 记录战斗日志（如果battle_records表存在）
        $stmt = $db->prepare("SHOW TABLES LIKE 'battle_records'");
        $stmt->execute();
        $battleRecordsExists = $stmt->rowCount() > 0;

        if ($battleRecordsExists && $mapId > 0) {
            try {
                $stmt = $db->prepare("
                    INSERT INTO battle_records (
                        character_id, map_id, stage_number, battle_type, battle_result,
                        experience_gained, spirit_stones_gained, items_dropped, created_at
                    ) VALUES (?, ?, ?, 'pve', 'victory', ?, ?, ?, CURRENT_TIMESTAMP)
                ");
                $stmt->execute([
                    $characterId,
                    $mapId,
                    $currentStage,
                    $expGained,
                    $goldGained,
                    json_encode($droppedItems)
                ]);

                error_log("战斗记录已保存 - 角色ID: {$characterId}, 地图ID: {$mapId}, 关卡: {$currentStage}");
            } catch (Exception $e) {
                error_log("保存战斗记录失败: " . $e->getMessage());
                // 不影响主流程，继续执行
            }
        }

        $db->commit();

        error_log("=== 战斗结果保存完成 ===");
        error_log("保存的物品数量: " . count($droppedItems));
        error_log("下一关卡: {$nextStage}");
        error_log("返回奖励数据 - 经验: {$expGained}, 金币: {$totalGoldGained} (原始: {$goldGained}, 回收: {$recycleGold})");
        error_log("挂机模式: " . ($isAutoBattleMode ? '是' : '否'));

        // 🔧 新增：详细的回收信息调试日志
        error_log("=== 🔍 回收信息详细调试 ===");
        error_log("recycledItems数组: " . json_encode($recycledItems));
        error_log("recycledItems数量: " . count($recycledItems));
        error_log("recycleGold: {$recycleGold}");
        error_log("has_recycled_items: " . (!empty($recycledItems) ? 'true' : 'false'));
        error_log("totalGoldGained: {$totalGoldGained}");
        error_log("originalGoldGained: {$goldGained}");
        error_log("=== 回收信息调试结束 ===");

        // 🔧 新增：构建完整的掉落物品展示数据
        $allDroppedItems = [];

        // 1. 添加成功保存的物品
        foreach ($droppedItems as $item) {
            $allDroppedItems[] = [
                'name' => $item['name'],
                'type' => $item['type'],
                'rarity' => $item['rarity'],
                'quantity' => $item['quantity'],
                'sell_price' => $item['sell_price'],
                'description' => $item['description'],
                'icon_image' => isset($item['icon_image']) ? $item['icon_image'] : 'assets/images/battle_sword.png', // 🔧 修复：包含图片字段
                'status' => 'saved',
                'display_status' => '已获得',
                'recycled' => false
            ];
        }

        // 2. 添加被回收的物品（背包空间不足回收）
        if (!empty($recycledItems)) {
            foreach ($recycledItems as $recycledItemName) {
                // 🔧 修复：从原始掉落数据中找到对应的物品信息
                $originalItem = null;
                foreach ($originalDroppedItems as $item) {
                    if ($item['name'] === $recycledItemName) {
                        $originalItem = $item;
                        break;
                    }
                }

                if ($originalItem) {
                    $allDroppedItems[] = [
                        'name' => $originalItem['name'],
                        'type' => $originalItem['type'],
                        'rarity' => $originalItem['rarity'],
                        'quantity' => $originalItem['quantity'],
                        'sell_price' => $originalItem['sell_price'],
                        'description' => $originalItem['description'] . ' (背包空间不足，已自动回收)',
                        'icon_image' => isset($originalItem['icon_image']) ? $originalItem['icon_image'] : 'assets/images/battle_sword.png', // 🔧 修复：包含图片字段
                        'status' => 'recycled',
                        'display_status' => '已回收',
                        'recycled' => true
                    ];
                } else {
                    // 如果找不到原始信息，创建基础的回收物品信息
                    $allDroppedItems[] = [
                        'name' => $recycledItemName,
                        'type' => 'equipment',
                        'rarity' => 'common',
                        'quantity' => 1,
                        'sell_price' => 0,
                        'description' => '背包空间不足，已自动回收',
                        'icon_image' => 'assets/images/battle_sword.png', // 🔧 修复：包含图片字段
                        'status' => 'recycled',
                        'display_status' => '已回收',
                        'recycled' => true
                    ];
                }
            }
        }

        // 🆕 3. 添加拾取过滤回收的物品
        if (!empty($autoRecycledItems)) {
            foreach ($autoRecycledItems as $autoRecycledItemName) {
                // 从保存的原始掉落数据中查找对应的物品信息
                $originalItem = null;
                foreach ($originalDroppedItems as $item) {
                    if ($item['name'] === $autoRecycledItemName) {
                        $originalItem = $item;
                        break;
                    }
                }

                if ($originalItem) {
                    $allDroppedItems[] = [
                        'name' => $originalItem['name'],
                        'type' => $originalItem['type'],
                        'rarity' => $originalItem['rarity'],
                        'quantity' => $originalItem['quantity'],
                        'sell_price' => $originalItem['sell_price'],
                        'description' => $originalItem['description'] . ' (因拾取设置过滤，已自动回收)',
                        'icon_image' => isset($originalItem['icon_image']) ? $originalItem['icon_image'] : 'assets/images/battle_sword.png', // 🔧 修复：包含图片字段
                        'status' => 'recycled',
                        'display_status' => '已过滤',
                        'recycled' => true,
                        'recycle_reason' => 'pickup_filter' // 标记回收原因
                    ];
                } else {
                    // 如果找不到原始信息，创建基础的回收物品信息
                    $allDroppedItems[] = [
                        'name' => $autoRecycledItemName,
                        'type' => 'equipment', // 拾取过滤主要针对装备
                        'rarity' => 'common', // 默认品质
                        'quantity' => 1,
                        'sell_price' => 1, // 默认售价
                        'description' => '因拾取设置过滤，已自动回收',
                        'icon_image' => 'assets/images/battle_sword.png', // 🔧 修复：包含图片字段
                        'status' => 'recycled',
                        'display_status' => '已过滤',
                        'recycled' => true,
                        'recycle_reason' => 'pickup_filter' // 标记回收原因
                    ];
                }

                error_log("🎯 添加拾取过滤回收物品到显示列表: " . $autoRecycledItemName);
            }
        }

        error_log("🎯 构建完整掉落展示数据: " . json_encode($allDroppedItems));

        // 🔧 新增：对allDroppedItems进行去重处理，避免同一物品重复显示
        $uniqueDroppedItems = [];
        $processedItems = [];

        foreach ($allDroppedItems as $item) {
            $itemKey = $item['name'] . '_' . $item['type'] . '_' . $item['rarity'];

            // 如果已经处理过这个物品，检查优先级
            if (isset($processedItems[$itemKey])) {
                // 优先保留已保存的状态，其次是已回收的状态
                if ($item['status'] === 'saved' && $processedItems[$itemKey]['status'] !== 'saved') {
                    $processedItems[$itemKey] = $item;
                    error_log("🔧 去重：替换物品 {$itemKey} 为已保存状态");
                } else {
                    error_log("🔧 去重：跳过重复物品 {$itemKey}，保持 {$processedItems[$itemKey]['status']} 状态");
                }
            } else {
                $processedItems[$itemKey] = $item;
            }
        }

        // 重建数组
        $allDroppedItems = array_values($processedItems);

        error_log("🔧 去重后的掉落展示数据: " . count($allDroppedItems) . " 件物品");
        error_log("🎯 去重处理完成: " . json_encode($allDroppedItems));

        // 🔧 调试：详细记录返回结果的关键数据
        // 🎲 新增：奇遇系统集成 - 战斗胜利后增加奇遇值
        $adventureResult = null;
        try {
            // 调用奇遇系统API增加奇遇值
            $adventureResult = addAdventureValueInternal($db, $characterId);
            if ($adventureResult['success']) {
                error_log("🎲 奇遇值增加成功: +{$adventureResult['added_value']}, 当前: {$adventureResult['current_value']}/1000");
            } else {
                error_log("⚠️ 奇遇值增加失败: " . $adventureResult['message']);
            }
        } catch (Exception $e) {
            error_log("⚠️ 奇遇系统调用异常: " . $e->getMessage());
            // 不影响战斗结果保存，继续执行
        }

        error_log("🔧 返回结果调试信息:");
        error_log("  - droppedItems数量: " . count($droppedItems));
        error_log("  - allDroppedItems数量: " . count($allDroppedItems));
        error_log("  - recycledItems数量: " . count($recycledItems));
        error_log("  - items_saved将返回: " . count($droppedItems));
        error_log("  - saved_count将返回: " . count($droppedItems));

        return array(
            'success' => true,
            'message' => '战斗结果保存成功',
            'next_stage' => $nextStage,
            'current_stage' => $currentStage, // 🔧 新增：返回当前关卡信息
            'is_auto_battle' => $isAutoBattleMode, // 🔧 新增：返回挂机模式状态
            'items_saved' => count($droppedItems),
            'map_id' => $mapId,
            'character_id' => $characterId,
            // 🔧 修复：使用正确的字段名称
            'spirit_stones_gained' => $expGained, // 主要字段：灵石奖励
            'gold_gained' => $totalGoldGained, // 主要字段：金币奖励（包含回收）
            'total_gold_gained' => $totalGoldGained, // 🔧 新增：明确的总金币字段
            // 🔧 保持兼容性的字段
            'exp_gained' => $expGained, // 兼容字段
            'spiritual_power_gained' => $expGained, // 兼容字段
            'silver_gained' => $totalGoldGained, // 兼容字段
            // 🆕 新增：回收信息
            'recycled_items' => $recycledItems,
            'recycle_gold' => $recycleGold,
            'auto_recycled_items' => $autoRecycledItems,
            'auto_recycle_gold' => $autoRecycleGold,
            'original_gold_gained' => $goldGained,
            'has_recycled_items' => !empty($recycledItems) || !empty($autoRecycledItems),
            // 🎯 新增：完整的掉落物品展示数据
            'all_dropped_items' => $allDroppedItems,
            'total_dropped_count' => count($allDroppedItems),
            'saved_count' => count($droppedItems),
            'recycled_count' => count($recycledItems) + count($autoRecycledItems),
            'space_recycled_count' => count($recycledItems),
            'filter_recycled_count' => count($autoRecycledItems),
            // 🎲 新增：奇遇系统信息
            'adventure_result' => $adventureResult
        );
    } catch (Exception $e) {
        $db->rollback();
        error_log("保存战斗结果失败: " . $e->getMessage());
        return array(
            'success' => false,
            'message' => '保存失败: ' . $e->getMessage()
        );
    }
}

// 根据物品类型获取槽位类型
function getSlotTypeFromItemType($itemType)
{
    switch ($itemType) {
        case 'weapon':
            return 'weapon';
        case 'equipment':
            return 'equipment'; // 通用装备
        case 'armor':
            return 'chest'; // 护甲默认为胸甲槽位
        case 'accessory':
            return 'accessory'; // 饰品
        case 'material':
            return 'material';
        case 'consumable':
            return 'consumable';
        case 'spirit':
            return 'spirit';
        case 'special':
            return 'special';
        default:
            return $itemType;
    }
}

function getMapInfo($db)
{
    try {
        $mapCode = isset($_GET['map_code']) ? $_GET['map_code'] : 'kunlun';

        // 记录查询的地图代码
        error_log("getMapInfo - 查询地图代码: " . $mapCode);

        // 首先尝试查询game_maps表
        $stmt = $db->prepare("SHOW TABLES LIKE 'game_maps'");
        $stmt->execute();
        $gameMapsTableExists = $stmt->rowCount() > 0;

        if ($gameMapsTableExists) {
            // 查询game_maps表
            $stmt = $db->prepare("SELECT * FROM game_maps WHERE map_code = ?");
            $stmt->execute(array($mapCode));
            $mapInfo = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($mapInfo) {
                // 转换字段名以保持与前端的兼容性
                $result = array(
                    'id' => $mapInfo['id'],
                    'map_name' => $mapInfo['map_name'], // 使用正确的字段名
                    'map_code' => $mapInfo['map_code'],
                    'description' => $mapInfo['description'],
                    'min_level' => $mapInfo['level_requirement'],
                    'max_level' => $mapInfo['level_requirement'] + 100,
                    'monster_count' => 10,
                    'drop_count' => 20,
                    'max_stages' => $mapInfo['max_stages']
                );

                error_log("getMapInfo - 从game_maps表查询到地图: " . $mapInfo['map_name']);

                return array(
                    'success' => true,
                    'map_info' => $result
                );
            }
        }

        // 🔧 修复：使用数据库实际关卡数，不再硬编码
        $actualStages = getActualMapStages($db, $mapCode);

        return array(
            'success' => true,
            'map_data' => array(
                'id' => 1,
                'name' => $mapName,
                'description' => '修仙者的起始之地',
                'code' => $mapCode,
                'monster_count' => 10,
                'drop_count' => 20,
                'max_stages' => $actualStages
            ),
            'message' => '找不到地图记录，使用默认数据'
        );
    } catch (Exception $e) {
        error_log("获取地图信息失败: " . $e->getMessage());
        return array(
            'success' => false,
            'message' => '获取地图信息失败'
        );
    }
}

function getUserProgress($db)
{
    try {
        $mapCode = isset($_GET['map_code']) ? $_GET['map_code'] : 'kunlun';

        // 🔧 修复：如果用户未登录，返回游客默认进度
        if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
            return array(
                'success' => true,
                'progress' => array(
                    'current_stage' => 1,
                    'max_stage_reached' => 1,
                    'is_guest' => true
                ),
                'message' => '游客模式，从第一关开始'
            );
        }

        // 获取角色ID
        $userId = $_SESSION['user_id'];
        $characterId = 0;

        $stmt = $db->prepare("SELECT id FROM characters WHERE user_id = ? LIMIT 1");
        $stmt->execute([$userId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($character) {
            $characterId = $character['id'];
        }

        // 使用character_id和map_code查询进度
        $stmt = $db->prepare("
            SELECT current_stage, max_stage_reached 
            FROM user_map_progress 
            WHERE character_id = ? AND map_code = ?
        ");
        $stmt->execute([$characterId, $mapCode]);
        $progress = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$progress) {
            // 🔧 修复：没有记录时返回默认第一关，不预先创建数据
            return array(
                'success' => true,
                'progress' => array(
                    'current_stage' => 1,
                    'max_stage_reached' => 1,
                    'is_new_player' => true
                ),
                'message' => '新玩家，从第一关开始'
            );
        }

        return array(
            'success' => true,
            'progress' => $progress
        );
    } catch (Exception $e) {
        return array('success' => false, 'message' => $e->getMessage());
    }
}

/**
 * 处理装备属性映射和随机属性生成
 */
function processEquipmentAttributes($itemData, $rarity)
{
    // 🔧 修复：基础属性映射 - 使用数据库实际字段名
    $attributes = [
        'physical_attack' => 0,
        'immortal_attack' => 0,
        'physical_defense' => 0,
        'immortal_defense' => 0,
        'hp_bonus' => 0,
        'mp_bonus' => 0,
        'speed_bonus' => 0,
        'critical_bonus' => 0.0,
        'critical_damage' => 0.0,
        'accuracy_bonus' => 0.0,
        'dodge_bonus' => 0.0,
        'critical_resistance' => 0.0,
        'level_requirement' => 1,
        'current_durability' => 100,
        'max_durability' => 100,

    ];

    // 🔧 修复：属性名映射到数据库实际字段结构
    $attributeMapping = [
        // 🔧 新字段映射（优先使用）
        'physical_attack' => 'physical_attack',
        'immortal_attack' => 'immortal_attack',
        'physical_defense' => 'physical_defense',
        'immortal_defense' => 'immortal_defense',
        'hp_bonus' => 'hp_bonus',
        'mp_bonus' => 'mp_bonus',
        'speed_bonus' => 'speed_bonus',
        'critical_bonus' => 'critical_bonus',
        'critical_damage' => 'critical_damage',
        'accuracy_bonus' => 'accuracy_bonus',
        'dodge_bonus' => 'dodge_bonus',
        'critical_resistance' => 'critical_resistance',

        // 🔧 修复：兼容性字段映射
        'hp' => 'hp_bonus',
        'mana' => 'mp_bonus',
        'mp' => 'mp_bonus',
        'level_req' => 'level_requirement',
        'durability' => 'max_durability',
        'critical' => 'critical_bonus',         // 🔧 修复：统一使用critical_bonus
    ];

    // 🔧 处理基础属性
    foreach ($attributeMapping as $sourceKey => $targetKey) {
        if (isset($itemData[$sourceKey])) {
            $value = $itemData[$sourceKey];

            // 🔧 修复：只有暴击伤害是百分比，其他都是整数数值
            if ($targetKey === 'critical_damage') {
                // 暴击伤害：百分比属性，存储为小数
                if (floatval($value) > 1) {
                    $attributes[$targetKey] = floatval($value) / 100.0;
                } else {
                    $attributes[$targetKey] = floatval($value);
                }
            } else {
                // 其他属性：整数数值（包括暴击率）
                $attributes[$targetKey] = intval($value);
            }
        }
    }



    // 🔧 设置耐久度
    if ($attributes['max_durability'] > 0) {
        $attributes['current_durability'] = $attributes['max_durability'];
    } else {
        $attributes['current_durability'] = 100;
        $attributes['max_durability'] = 100;
    }

    // 🔧 重构：使用品质系统统一处理随机属性
    // 直接调用品质系统来计算包含随机属性的完整属性
    try {
        require_once __DIR__ . '/equipment_quality_system.php';

        // 使用品质系统重新计算属性（包含随机属性）
        $attributesWithRandom = EquipmentQualitySystem::calculateEquipmentAttributes($attributes, $rarity);

        // 使用品质系统计算的结果
        $attributes = $attributesWithRandom;

        error_log("🎯 [重构] 使用品质系统计算属性完成，品质: {$rarity}");
    } catch (Exception $e) {
        error_log("⚠️ [重构] 品质系统调用失败: " . $e->getMessage() . "，使用基础属性");
        // 如果品质系统调用失败，保持基础属性不变
    }

    // 🔧 修复：移除旧的随机属性处理逻辑（已被上面的新逻辑替代）
    /*
    // 🔧 处理随机属性
    if (isset($itemData['random_attrs']) && is_array($itemData['random_attrs'])) {
        // 旧逻辑已移除，现在使用品质决定随机属性条数
    }
    */

    // 🔧 修复：确保数值合理性
    $attributes['critical_bonus'] = min($attributes['critical_bonus'], 100); // 最大100点 (整数)
    $attributes['critical_damage'] = min($attributes['critical_damage'], 3.0); // 最大300% (百分比)
    $attributes['accuracy_bonus'] = min($attributes['accuracy_bonus'], 100); // 最大100点 (整数)
    $attributes['dodge_bonus'] = min($attributes['dodge_bonus'], 50); // 最大50点 (整数)
    $attributes['critical_resistance'] = min($attributes['critical_resistance'], 50); // 最大50点 (整数)

    return $attributes;
}

/**
 * 🔧 已废弃：生成随机属性加成值
 * 该函数已被移动到 EquipmentQualitySystem 类中统一处理
 * 保留此注释作为重构记录
 */

/**
 * 生成默认掉落物品（当数据库配置不可用时）
 */
function generateDefaultDrops($currentStage, $monsterType, $db = null)
{
    try {
        $drops = array();

        // 基础掉落几率
        $baseDropChance = 0.3; // 30%基础掉落几率

        // BOSS关卡增加掉落几率
        if ($monsterType === 'boss' || $currentStage % 10 == 0) {
            $baseDropChance = 0.8; // 80%掉落几率
        }

        // 随机判断是否掉落
        if (mt_rand() / mt_getrandmax() > $baseDropChance) {
            return array('success' => true, 'drops' => array(), 'message' => '默认掉落几率未触发');
        }

        // 🔧 修复：确保所有掉落物品都来自真实的数据库记录
        if ($db !== null) {
            try {
                // 🔧 修复：获取不同类型的真实物品
                $materialStmt = $db->prepare("
                    SELECT id, item_name, item_type, icon_image, description, sell_price 
                    FROM game_items 
                    WHERE item_type = 'material' 
                    AND id IS NOT NULL
                    ORDER BY RAND() 
                    LIMIT 3
                ");
                $materialStmt->execute();
                $materialItems = $materialStmt->fetchAll(PDO::FETCH_ASSOC);

                $consumableStmt = $db->prepare("
                    SELECT id, item_name, item_type, icon_image, description, sell_price 
                    FROM game_items 
                    WHERE item_type = 'consumable' 
                    AND id IS NOT NULL
                    ORDER BY RAND() 
                    LIMIT 3
                ");
                $consumableStmt->execute();
                $consumableItems = $consumableStmt->fetchAll(PDO::FETCH_ASSOC);

                // 🔧 合并所有可用物品
                $availableItems = array_merge($materialItems, $consumableItems);

                if (!empty($availableItems)) {
                    // 根据怪物类型决定掉落数量
                    $dropCount = ($monsterType === 'boss') ? mt_rand(1, 2) : 1;

                    for ($i = 0; $i < $dropCount && count($availableItems) > 0; $i++) {
                        $selectedItem = $availableItems[array_rand($availableItems)];

                        // 根据关卡等级调整数量
                        $stageLevel = max(1, ceil($currentStage / 10));
                        $quantity = mt_rand(1, 3) * $stageLevel;

                        // 生成品质
                        $rarity = generateItemRarity('normal', $monsterType);

                        // 🔧 修复：处理图片路径，确保不为空
                        $iconPath = $selectedItem['icon_image'];
                        if ($iconPath) {
                            // 如果已经有完整路径，直接使用
                            if (strpos($iconPath, 'assets/') === 0) {
                                // 已经是完整路径，不需要修改
                            } else {
                                // 拼接完整路径
                                $iconPath = 'assets/images/' . $iconPath;
                            }
                        } else {
                            // 🔧 修复：如果数据库中没有图片路径，设置为null，让后续逻辑决定
                            $iconPath = null;
                        }

                        $drops[] = array(
                            'id' => intval($selectedItem['id']), // 🔧 确保ID为整数
                            'name' => $selectedItem['item_name'],
                            'type' => $selectedItem['item_type'],
                            'rarity' => $rarity,
                            'quantity' => $quantity,
                            'icon_image' => $iconPath,  // 🔧 统一使用icon_image字段
                            'description' => $selectedItem['description'] ?: '修炼必需的材料',
                            'sell_price' => intval($selectedItem['sell_price']) ?: 1,
                            'item_data' => array()
                        );

                        error_log("默认掉落使用真实物品: ID={$selectedItem['id']}, 名称={$selectedItem['item_name']}, 数量={$quantity}");

                        // 🔧 移除已选择的物品，避免重复
                        $availableItems = array_filter($availableItems, function ($item) use ($selectedItem) {
                            return $item['id'] !== $selectedItem['id'];
                        });
                        $availableItems = array_values($availableItems); // 重新索引
                    }
                } else {
                    error_log("⚠️ 数据库中没有找到可用的材料或消耗品，跳过掉落");
                }
            } catch (Exception $e) {
                error_log("获取真实物品失败: " . $e->getMessage());
            }
        }

        // 🔧 修复：如果没有获取到真实物品，返回空掉落而不是虚拟物品
        if (empty($drops)) {
            error_log("⚠️ 无法生成真实物品掉落，返回空掉落列表");
            return array(
                'success' => true,
                'drops' => array(),
                'message' => '无可用的真实物品掉落'
            );
        }

        error_log("生成默认掉落 - 关卡: {$currentStage}, 怪物类型: {$monsterType}, 掉落数量: " . count($drops));

        return array(
            'success' => true,
            'drops' => $drops,
            'message' => '使用真实物品默认掉落配置'
        );
    } catch (Exception $e) {
        error_log("生成默认掉落失败: " . $e->getMessage());
        return array(
            'success' => true,
            'drops' => array(),
            'message' => '默认掉落生成失败: ' . $e->getMessage()
        );
    }
}

/**
 * 检查数据库表是否存在
 */
function checkTableExists($db)
{
    try {
        $tables = ['user_inventories', 'game_items', 'characters', 'user_map_progress', 'game_maps', 'maps'];
        $result = [];

        foreach ($tables as $table) {
            $stmt = $db->prepare("SHOW TABLES LIKE ?");
            $stmt->execute([$table]);
            $exists = $stmt->rowCount() > 0;
            $result[$table] = $exists;

            if ($exists) {
                // 如果表存在，也获取表的行数
                $countStmt = $db->prepare("SELECT COUNT(*) as count FROM `$table`");
                $countStmt->execute();
                $count = $countStmt->fetch(PDO::FETCH_ASSOC);
                $result[$table . '_count'] = $count['count'];
            }
        }

        return [
            'success' => true,
            'tables' => $result,
            'message' => '数据库表检查完成'
        ];
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => '检查表时出错: ' . $e->getMessage()
        ];
    }
}

// 🔧 新增：检查user_inventories表结构
function checkUserInventoriesColumns($db)
{
    try {
        $stmt = $db->prepare("DESCRIBE user_inventories");
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

        return [
            'success' => true,
            'columns' => $columns,
            'message' => 'user_inventories表结构检查完成'
        ];
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => '检查user_inventories表结构时出错: ' . $e->getMessage()
        ];
    }
}

// 🔧 新增：检查game_items表结构
function checkGameItemsColumns($db)
{
    try {
        $stmt = $db->prepare("DESCRIBE game_items");
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

        return [
            'success' => true,
            'columns' => $columns,
            'message' => 'game_items表结构检查完成'
        ];
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => '检查game_items表结构时出错: ' . $e->getMessage()
        ];
    }
}

// 🔧 新增：清理测试数据
function cleanupTestData($db)
{
    try {
        $db->beginTransaction();

        // 删除临时创建的game_items
        $stmt = $db->prepare("DELETE FROM game_items WHERE item_code LIKE 'temp_%'");
        $stmt->execute();
        $deletedGameItems = $stmt->rowCount();

        // 删除测试用户的背包物品（只删除战斗掉落的物品）
        $stmt = $db->prepare("DELETE FROM user_inventories WHERE obtained_source = 'battle_drop' AND obtained_time > DATE_SUB(NOW(), INTERVAL 1 HOUR)");
        $stmt->execute();
        $deletedInventoryItems = $stmt->rowCount();

        $db->commit();

        return [
            'success' => true,
            'deleted_game_items' => $deletedGameItems,
            'deleted_inventory_items' => $deletedInventoryItems,
            'message' => '测试数据清理完成'
        ];
    } catch (Exception $e) {
        $db->rollBack();
        return [
            'success' => false,
            'message' => '清理测试数据时出错: ' . $e->getMessage()
        ];
    }
}

/**
 * 🔧 更新：将前端传递的物品类型映射到数据库枚举值
 * 数据库 item_type 枚举: 'weapon','equipment','armor','accessory','consumable','material','spirit','special'
 */
function mapItemTypeToDatabase($frontendType)
{
    $typeMapping = [
        // 直接匹配的类型
        'weapon' => 'weapon',
        'equipment' => 'equipment',
        'armor' => 'armor',
        'accessory' => 'accessory',
        'consumable' => 'consumable',
        'material' => 'material',
        'spirit' => 'spirit',
        'special' => 'special',

        // 需要映射的类型
        'currency' => 'special',       // 货币映射为特殊物品
        'treasure' => 'special',       // 宝物映射为特殊物品
        'pill' => 'consumable',        // 丹药映射为消耗品
        'recipe' => 'special',         // 丹方映射为特殊物品
        'furnace' => 'special',        // 丹炉映射为特殊物品
        'technique' => 'special',      // 功法映射为特殊物品
        'manual' => 'special',         // 秘籍映射为特殊物品
        'gem' => 'material',           // 宝石映射为材料
        'ore' => 'material',           // 矿石映射为材料
        'herb' => 'material',          // 草药映射为材料
        'crystal' => 'material',       // 水晶映射为材料
        'essence' => 'material',       // 精华映射为材料
        'scroll' => 'consumable',      // 卷轴映射为消耗品
        'potion' => 'consumable',      // 药水映射为消耗品
        'food' => 'consumable',        // 食物映射为消耗品
        'tool' => 'special',           // 工具映射为特殊物品
        'misc' => 'special',           // 杂项映射为特殊物品
        'other' => 'special'           // 其他映射为特殊物品
    ];

    // 转换为小写进行匹配
    $lowerType = strtolower(trim($frontendType));

    if (isset($typeMapping[$lowerType])) {
        return $typeMapping[$lowerType];
    }

    // 如果没有找到匹配，记录日志并返回默认值
    error_log("⚠️ 未知的物品类型: '{$frontendType}', 使用默认类型 'special'");
    return 'special';
}

/**
 * 设置地图进度
 */
function setStageProgress($db)
{
    try {
        if (!isset($_SESSION['user_id'])) {
            return array('success' => false, 'message' => '用户未登录');
        }

        $userId = $_SESSION['user_id'];
        $mapCode = isset($_POST['map_code']) ? $_POST['map_code'] : '';
        $targetStage = intval(isset($_POST['target_stage']) ? $_POST['target_stage'] : 1);

        if (empty($mapCode)) {
            return array('success' => false, 'message' => '地图代码不能为空');
        }

        if ($targetStage < 1) {
            return array('success' => false, 'message' => '目标关卡必须大于0');
        }

        // 🔧 修复：获取角色ID而不是直接使用用户ID
        $stmt = $db->prepare("SELECT id FROM characters WHERE user_id = ? LIMIT 1");
        $stmt->execute([$userId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$character) {
            return array('success' => false, 'message' => '找不到角色');
        }

        $characterId = $character['id'];

        // 验证地图是否存在
        $stmt = $db->prepare("SELECT id FROM game_maps WHERE map_code = ?");
        $stmt->execute(array($mapCode));
        $map = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$map) {
            return array('success' => false, 'message' => '地图不存在');
        }

        $mapId = $map['id'];

        // 🔧 修复：直接使用character_id和map_code查询进度
        $stmt = $db->prepare("SELECT id, current_stage FROM user_map_progress WHERE character_id = ? AND map_code = ?");
        $stmt->execute(array($characterId, $mapCode));
        $progress = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($progress) {
            // 更新现有进度
            $stmt = $db->prepare("UPDATE user_map_progress SET current_stage = ?, last_played_at = CURRENT_TIMESTAMP WHERE character_id = ? AND map_code = ?");
            $stmt->execute(array($targetStage, $characterId, $mapCode));
        } else {
            // 创建新的进度记录
            $stmt = $db->prepare("INSERT INTO user_map_progress (character_id, map_id, map_code, current_stage, max_stage_reached, total_battles, total_victories, last_played_at) VALUES (?, ?, ?, ?, ?, 0, 0, CURRENT_TIMESTAMP)");
            $stmt->execute(array($characterId, $mapId, $mapCode, $targetStage, $targetStage));
        }

        return array(
            'success' => true,
            'message' => '进度设置成功',
            'map_code' => $mapCode,
            'current_stage' => $targetStage,
            'previous_stage' => $progress ? $progress['current_stage'] : 1
        );
    } catch (Exception $e) {
        error_log("设置地图进度失败: " . $e->getMessage());
        return array('success' => false, 'message' => '设置进度失败: ' . $e->getMessage());
    }
}

/**
 * 获取当前地图进度
 */
function getCurrentStage($db)
{
    try {
        if (!isset($_SESSION['user_id'])) {
            return array('success' => false, 'message' => '用户未登录');
        }

        $userId = $_SESSION['user_id'];
        $mapCode = isset($_POST['map_code']) ? $_POST['map_code'] : '';

        if (empty($mapCode)) {
            return array('success' => false, 'message' => '地图代码不能为空');
        }

        // 🔧 修复：获取角色ID而不是直接使用用户ID
        $stmt = $db->prepare("SELECT id FROM characters WHERE user_id = ? LIMIT 1");
        $stmt->execute([$userId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$character) {
            return array('success' => false, 'message' => '找不到角色');
        }

        $characterId = $character['id'];

        // 获取地图信息
        $stmt = $db->prepare("SELECT id, map_name FROM game_maps WHERE map_code = ?");
        $stmt->execute(array($mapCode));
        $map = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$map) {
            return array('success' => false, 'message' => '地图不存在');
        }

        // 🔧 修复：直接使用character_id和map_code查询进度
        $stmt = $db->prepare("SELECT current_stage, last_played_at FROM user_map_progress WHERE character_id = ? AND map_code = ?");
        $stmt->execute(array($characterId, $mapCode));
        $progress = $stmt->fetch(PDO::FETCH_ASSOC);

        $currentStage = $progress ? $progress['current_stage'] : 1;

        return array(
            'success' => true,
            'map_code' => $mapCode,
            'map_name' => $map['map_name'],
            'current_stage' => $currentStage,
            'updated_at' => $progress ? $progress['last_played_at'] : null
        );
    } catch (Exception $e) {
        error_log("获取地图进度失败: " . $e->getMessage());
        return array('success' => false, 'message' => '获取进度失败: ' . $e->getMessage());
    }
}

/**
 * 🔐 安全函数：从数据库重新计算真实奖励，绝不信任前端传来的值
 */
function calculateRealRewards($db, $mapCode, $currentStage)
{
    try {
        error_log("🔐 开始计算真实奖励: 地图={$mapCode}, 关卡={$currentStage}");

        // 获取地图ID
        $stmt = $db->prepare("SELECT id FROM game_maps WHERE map_code = ?");
        $stmt->execute(array($mapCode));
        $map = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$map) {
            error_log("⚠️ 地图不存在，使用默认奖励: {$mapCode}");
            return array(
                'spirit_stones' => $currentStage * 10,
                'gold' => $currentStage * 5
            );
        }

        $mapId = $map['id'];

        // 查询关卡奖励配置
        $stmt = $db->prepare("
            SELECT spirit_stone_reward, gold_reward, monster_level
            FROM map_stages 
            WHERE map_id = ? AND stage_number = ?
        ");
        $stmt->execute(array($mapId, $currentStage));
        $stageInfo = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($stageInfo) {
            $monsterLevel = $stageInfo['monster_level'] ?: $currentStage;

            // 🔐 安全计算：优先使用数据库配置的奖励，为0时使用等级公式
            $spiritStones = (isset($stageInfo['spirit_stone_reward']) && $stageInfo['spirit_stone_reward'] > 0)
                ? intval($stageInfo['spirit_stone_reward'])
                : ($monsterLevel * 10);

            $gold = (isset($stageInfo['gold_reward']) && $stageInfo['gold_reward'] > 0)
                ? intval($stageInfo['gold_reward'])
                : ($monsterLevel * 5);

            error_log("🔐 数据库奖励计算成功: 灵石={$spiritStones}, 金币={$gold}");
            error_log("🔐 原始数据: spirit_stone_reward={$stageInfo['spirit_stone_reward']}, gold_reward={$stageInfo['gold_reward']}, monster_level={$monsterLevel}");

            return array(
                'spirit_stones' => $spiritStones,
                'gold' => $gold
            );
        } else {
            error_log("⚠️ 关卡数据不存在，使用默认奖励: 地图{$mapId}第{$currentStage}关");
            return array(
                'spirit_stones' => $currentStage * 10,
                'gold' => $currentStage * 5
            );
        }
    } catch (Exception $e) {
        error_log("❌ 计算真实奖励失败: " . $e->getMessage());
        return array(
            'spirit_stones' => $currentStage * 10,
            'gold' => $currentStage * 5
        );
    }
}

/**
 * 🔧 新增：获取地图的实际关卡数
 */
function getActualMapStages($db, $mapCode)
{
    try {
        // 地图代码到ID的映射
        $mapCodeToId = [
            'kunlun' => 1,      // 太乙峰
            'donghai' => 2,     // 碧水寒潭  
            'jiuyou' => 3,      // 赤焰谷
            'santiansan' => 4,  // 幽冥鬼域
            'huolong' => 5,     // 青云仙山
            'hanbing' => 6,     // 星辰古战场
            'xukong' => 7,      // 混元虚空
            'honghuang' => 8    // 洪荒秘境
        ];

        $mapId = isset($mapCodeToId[$mapCode]) ? $mapCodeToId[$mapCode] : 1;

        // 查询数据库获取实际关卡数
        $stmt = $db->prepare("SELECT COUNT(*) as total_stages FROM map_stages WHERE map_id = ?");
        $stmt->execute([$mapId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($result && $result['total_stages'] > 0) {
            return intval($result['total_stages']);
        }

        // 🔧 备用：基于数据库实际配置的默认关卡数
        $defaultStages = [
            1 => 60,  // 太乙峰
            2 => 90,  // 碧水寒潭
            3 => 120, // 赤焰谷
            4 => 140, // 幽冥鬼域
            5 => 150, // 青云仙山
            6 => 140, // 星辰古战场
            7 => 140, // 混元虚空
            8 => 175  // 洪荒秘境
        ];

        return isset($defaultStages[$mapId]) ? $defaultStages[$mapId] : 100;
    } catch (Exception $e) {
        error_log("获取地图关卡数失败: " . $e->getMessage());
        return 100; // 安全默认值
    }
}

/**
 * 检查装备是否应该被拾取（内部函数）
 * @param array $equipmentData 装备数据
 * @param array $pickupSettings 拾取设置
 * @param int $currentRealmLevel 当前境界等级
 * @return bool 是否应该拾取
 */
function shouldPickupEquipmentInternal($equipmentData, $pickupSettings, $currentRealmLevel)
{
    error_log("🔍 装备拾取过滤详细检查开始");
    error_log("🔍 装备数据: " . json_encode($equipmentData, JSON_UNESCAPED_UNICODE));
    error_log("🔍 拾取设置: " . json_encode($pickupSettings, JSON_UNESCAPED_UNICODE));
    error_log("🔍 当前境界等级: " . $currentRealmLevel);

    // 如果没有设置，默认拾取所有装备
    if (!$pickupSettings) {
        error_log("✅ 无拾取设置，默认拾取所有装备");
        return true;
    }

    // 检查品质过滤
    $qualityFilter = isset($pickupSettings['quality_filter']) ? $pickupSettings['quality_filter'] : [];
    error_log("🔍 品质过滤列表: " . json_encode($qualityFilter, JSON_UNESCAPED_UNICODE));

    if (!empty($qualityFilter)) {
        $equipmentQuality = isset($equipmentData['rarity']) ? $equipmentData['rarity'] : '普通';

        // 🔧 修复：英文品质转换为中文品质（完整映射）
        $englishToChinese = [
            'common' => '普通',
            'uncommon' => '稀有',
            'rare' => '史诗',
            'epic' => '传说',
            'legendary' => '神话',
            // 🔧 新增：处理可能的其他格式
            'normal' => '普通',
            'white' => '普通',
            'green' => '稀有',
            'blue' => '史诗',
            'purple' => '传说',
            'orange' => '神话',
            'gold' => '神话'
        ];

        // 如果是英文品质，转换为中文
        $originalQuality = $equipmentQuality;
        if (isset($englishToChinese[$equipmentQuality])) {
            $equipmentQuality = $englishToChinese[$equipmentQuality];
        }

        error_log("🎯 装备拾取品质检查: 原始品质={$originalQuality}, 转换后品质={$equipmentQuality}, 过滤列表=" . json_encode($qualityFilter, JSON_UNESCAPED_UNICODE));

        if (!in_array($equipmentQuality, $qualityFilter)) {
            error_log("🚫 装备品质过滤: {$equipmentQuality} 不在拾取列表中，装备被过滤");
            return false; // 品质不在拾取列表中
        }
        error_log("✅ 装备品质检查通过: {$equipmentQuality} 在拾取列表中");
    } else {
        error_log("✅ 无品质过滤，跳过品质检查");
    }

    // 检查境界过滤
    $filterBelowRealm = isset($pickupSettings['filter_below_realm']) ? $pickupSettings['filter_below_realm'] : false;
    error_log("🔍 境界过滤设置: " . ($filterBelowRealm ? '开启' : '关闭'));

    if ($filterBelowRealm) {
        // 从装备数据中获取境界要求
        $equipmentLevel = 1; // 默认值

        // 先尝试从根级别获取
        if (isset($equipmentData['realm_requirement'])) {
            $equipmentLevel = intval($equipmentData['realm_requirement']);
        }
        // 如果根级别没有，尝试从item_data中获取
        else if (isset($equipmentData['item_data']) && isset($equipmentData['item_data']['realm_requirement'])) {
            $equipmentLevel = intval($equipmentData['item_data']['realm_requirement']);
        }

        error_log("🔍 装备境界要求: {$equipmentLevel}");

        // 🔧 修改为大境界过滤：计算大境界等级（每10级一个大境界）
        $currentMajorRealm = intval(($currentRealmLevel - 1) / 10) + 1; // 当前大境界
        $equipmentMajorRealm = intval(($equipmentLevel - 1) / 10) + 1; // 装备大境界

        error_log("🔍 大境界过滤检查: 装备境界={$equipmentLevel}(大境界{$equipmentMajorRealm}), 当前境界={$currentRealmLevel}(大境界{$currentMajorRealm})");

        if ($equipmentMajorRealm < $currentMajorRealm) {
            error_log("🚫 大境界过滤: 装备大境界{$equipmentMajorRealm} < 当前大境界{$currentMajorRealm}，装备被过滤");
            return false; // 装备大境界低于当前大境界
        }
    }

    error_log("✅ 装备拾取过滤检查完成：通过所有过滤条件");
    return true; // 通过所有过滤条件
}

/**
 * 🔧 修复双重JSON问题：内部实现getCurrentRealmLevel函数，避免包含cultivation.php
 * 获取当前境界等级
 * @param PDO $db 数据库连接
 * @param int $realmId 境界ID
 * @return int 境界等级
 */
function getCurrentRealmLevelInternal($db, $realmId)
{
    try {
        error_log("🔧 内部函数：获取境界等级，境界ID: {$realmId}");

        $stmt = $db->prepare("SELECT realm_level FROM realm_levels WHERE id = ?");
        $stmt->execute([$realmId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($result) {
            $realmLevel = intval($result['realm_level']);
            error_log("🔧 内部函数：境界等级获取成功: {$realmLevel}");
            return $realmLevel;
        } else {
            error_log("🔧 内部函数：境界ID {$realmId} 不存在，返回默认等级1");
            return 1; // 默认等级
        }
    } catch (Exception $e) {
        error_log("🔧 内部函数：获取境界等级失败: " . $e->getMessage());
        return 1; // 安全默认值
    }
}

/**
 * 奇遇系统内部函数 - 增加奇遇值
 * 避免外部文件引用，防止循环依赖
 */
function addAdventureValueInternal($db, $characterId)
{
    try {
        // 随机增加1-3点奇遇值
        $addValue = mt_rand(1, 3);

        // 获取当前奇遇记录
        $stmt = $db->prepare("
            SELECT current_adventure_value, total_adventure_count 
            FROM user_adventure_records 
            WHERE character_id = ?
        ");
        $stmt->execute([$characterId]);
        $record = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$record) {
            // 如果没有记录，创建新记录
            $stmt = $db->prepare("
                INSERT INTO user_adventure_records (character_id, current_adventure_value, total_adventure_count)
                VALUES (?, ?, 0)
            ");
            $stmt->execute([$characterId, $addValue]);
            $newValue = $addValue;
        } else {
            $newValue = $record['current_adventure_value'] + $addValue;

            // 更新奇遇值
            $stmt = $db->prepare("
                UPDATE user_adventure_records 
                SET current_adventure_value = ?, updated_at = CURRENT_TIMESTAMP
                WHERE character_id = ?
            ");
            $stmt->execute([$newValue, $characterId]);
        }

        // 检查是否达到触发条件（1000点）
        $shouldTrigger = $newValue >= 1000;
        $adventureEvent = null;

        // 🎲 新增：如果达到触发条件，自动触发奇遇事件
        if ($shouldTrigger) {
            try {
                $adventureEvent = triggerAdventureEventInternal($db, $characterId);
                error_log("🎲 奇遇事件自动触发: " . json_encode($adventureEvent, JSON_UNESCAPED_UNICODE));
            } catch (Exception $e) {
                error_log("⚠️ 奇遇事件触发失败: " . $e->getMessage());
                // 不影响奇遇值增加，继续执行
            }
        }

        return [
            'success' => true,
            'added_value' => $addValue,
            'current_value' => $newValue,
            'should_trigger' => $shouldTrigger,
            'adventure_event' => $adventureEvent, // 新增：奇遇事件结果
            'message' => "奇遇值增加 {$addValue} 点，当前 {$newValue}/1000" . ($shouldTrigger ? "，触发奇遇事件！" : "")
        ];
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => '增加奇遇值失败: ' . $e->getMessage()
        ];
    }
}

/**
 * 🎲 新增：奇遇事件触发内部函数
 * 当奇遇值达到1000时自动调用
 */
function triggerAdventureEventInternal($db, $characterId)
{
    try {
        // 获取角色信息（包括user_id）
        $stmt = $db->prepare("
            SELECT c.*, r.realm_level 
            FROM characters c 
            LEFT JOIN realm_levels r ON c.realm_id = r.id 
            WHERE c.id = ?
        ");
        $stmt->execute([$characterId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$character) {
            throw new Exception('角色不存在');
        }

        $userId = $character['user_id'];

        // 获取当前挂机地图
        $mapId = getCurrentHangupMapInternal($db, $characterId);
        $realmLevel = $character['realm_level'] ?: 1;

        // 从数据库获取所有可用的奇遇事件及其概率
        $stmt = $db->prepare("SELECT event_type, probability FROM adventure_events WHERE is_active = 1");
        $stmt->execute();
        $eventRecords = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (empty($eventRecords)) {
            throw new Exception('没有可用的奇遇事件');
        }

        // 构建事件类型和概率映射
        $eventTypes = [];
        foreach ($eventRecords as $record) {
            $eventTypes[$record['event_type']] = floatval($record['probability']);
        }

        // 根据概率选择事件
        $selectedEvent = selectRandomEvent($eventTypes);

        // 获取事件ID
        $stmt = $db->prepare("SELECT id FROM adventure_events WHERE event_type = ? LIMIT 1");
        $stmt->execute([$selectedEvent]);
        $eventRecord = $stmt->fetch(PDO::FETCH_ASSOC);
        $eventId = $eventRecord ? $eventRecord['id'] : 1; // 如果找不到，使用默认ID 1

        // 生成奇遇奖励
        $rewards = generateAdventureRewardsInternal($db, $selectedEvent, $characterId, $mapId, $realmLevel);

        // 重置奇遇值为0，增加触发次数
        $stmt = $db->prepare("
            UPDATE user_adventure_records 
            SET current_adventure_value = 0, 
                total_adventure_count = total_adventure_count + 1,
                last_adventure_time = CURRENT_TIMESTAMP
            WHERE character_id = ?
        ");
        $stmt->execute([$characterId]);

        // 记录奇遇触发日志
        $stmt = $db->prepare("
            INSERT INTO adventure_trigger_logs 
            (user_id, character_id, event_type, event_id, map_id, adventure_value_before, adventure_value_after, rewards_received, trigger_time)
            VALUES (?, ?, ?, ?, ?, 1000, 0, ?, CURRENT_TIMESTAMP)
        ");
        $stmt->execute([$userId, $characterId, $selectedEvent, $eventId, $mapId, json_encode($rewards, JSON_UNESCAPED_UNICODE)]);

        // 发放奖励到背包
        $distributedRewards = distributeAdventureRewards($db, $characterId, $rewards);

        // 获取事件信息
        $eventInfo = isset($GLOBALS['current_adventure_event']) ? $GLOBALS['current_adventure_event'] : null;

        return [
            'success' => true,
            'event_type' => $selectedEvent,
            'event_name' => $eventInfo ? $eventInfo['event_name'] : getEventName($selectedEvent),
            'description' => $eventInfo ? $eventInfo['description'] : "恭喜！触发奇遇事件：" . getEventName($selectedEvent),
            'rewards' => $rewards,
            'distributed_rewards' => $distributedRewards,
            'message' => $eventInfo ? $eventInfo['description'] : "恭喜！触发奇遇事件：" . getEventName($selectedEvent)
        ];
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => '奇遇事件触发失败: ' . $e->getMessage()
        ];
    }
}

/**
 * 🎲 辅助函数：根据概率选择随机事件
 */
function selectRandomEvent($eventTypes)
{
    $totalWeight = array_sum($eventTypes);
    $random = mt_rand(1, $totalWeight);

    $currentWeight = 0;
    foreach ($eventTypes as $event => $weight) {
        $currentWeight += $weight;
        if ($random <= $currentWeight) {
            return $event;
        }
    }

    // 默认返回第一个事件
    return array_keys($eventTypes)[0];
}

/**
 * 🎲 辅助函数：获取事件中文名称
 */
function getEventName($eventType)
{
    $eventNames = [
        'treasure_discovery' => '天材地宝发现',
        'equipment_find' => '极品装备发现',
        'skill_fragment' => '功法碎片获得',
        'precious_pills' => '珍贵丹药发现',
        'spirit_stone_treasure' => '灵石宝藏',
        'recipe_inheritance' => '丹方传承',
        'secret_realm_discovery' => '秘境地图发现'
    ];

    return isset($eventNames[$eventType]) ? $eventNames[$eventType] : '神秘奇遇';
}

/**
 * 🎲 辅助函数：获取当前挂机地图
 */
function getCurrentHangupMapInternal($db, $characterId)
{
    // 这里可以根据实际需求获取当前挂机地图
    // 暂时返回默认地图1
    return 1;
}

/**
 * 🎲 辅助函数：生成奇遇奖励（从adventure_events表读取配置）
 */
function generateAdventureRewardsInternal($db, $eventType, $characterId, $mapId, $realmLevel)
{
    try {
        // 从adventure_events表获取奖励配置
        $stmt = $db->prepare("SELECT reward_config, event_name, description FROM adventure_events WHERE event_type = ? AND is_active = 1 LIMIT 1");
        $stmt->execute([$eventType]);
        $eventConfig = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$eventConfig || !$eventConfig['reward_config']) {
            throw new Exception("未找到事件类型 {$eventType} 的奖励配置");
        }

        // 保存事件信息供后续使用
        $GLOBALS['current_adventure_event'] = $eventConfig;

        $rewardConfig = json_decode($eventConfig['reward_config'], true);
        if (!$rewardConfig) {
            throw new Exception("奖励配置JSON解析失败");
        }

        $rewards = [];

        switch ($rewardConfig['type']) {
            case 'item':
                // 物品奖励（天材地宝、功法碎片、秘境钥匙等）
                if (isset($rewardConfig['item_ids']) && !empty($rewardConfig['item_ids'])) {
                    $itemIds = $rewardConfig['item_ids'];
                    $selectedItemId = $itemIds[array_rand($itemIds)]; // 随机选择一个物品ID

                    $quantity = isset($rewardConfig['quantity_min']) && isset($rewardConfig['quantity_max'])
                        ? mt_rand($rewardConfig['quantity_min'], $rewardConfig['quantity_max'])
                        : (isset($rewardConfig['quantity']) ? $rewardConfig['quantity'] : 1);

                    // 获取物品信息
                    $stmt = $db->prepare("SELECT item_name FROM game_items WHERE id = ?");
                    $stmt->execute([$selectedItemId]);
                    $itemInfo = $stmt->fetch(PDO::FETCH_ASSOC);

                    $rewards[] = [
                        'type' => 'item',
                        'item_id' => $selectedItemId,
                        'item_name' => $itemInfo ? $itemInfo['item_name'] : '未知物品',
                        'quantity' => $quantity,
                        'description' => "获得{$itemInfo['item_name']} × {$quantity}"
                    ];
                }
                break;

            case 'equipment':
                // 装备奖励（使用品质系统动态生成）
                $quality = isset($rewardConfig['quality']) ? $rewardConfig['quality'] : 'legendary';
                $quantity = isset($rewardConfig['quantity']) ? $rewardConfig['quantity'] : 1;

                // 将英文品质转换为中文显示
                $qualityMap = [
                    'common' => '普通',
                    'uncommon' => '稀有',
                    'rare' => '史诗',
                    'epic' => '史诗',
                    'legendary' => '传说',
                    'mythic' => '神话',
                    'artifact' => '神器'
                ];
                $chineseQuality = isset($qualityMap[$quality]) ? $qualityMap[$quality] : '神话';

                $rewards[] = [
                    'type' => 'equipment',
                    'quality' => $quality,
                    'level' => $realmLevel,
                    'quantity' => $quantity,
                    'description' => "获得{$chineseQuality}品质装备"
                ];
                break;

            case 'spirit_stones':
                // 灵石奖励
                if (isset($rewardConfig['calculation_method']) && $rewardConfig['calculation_method'] == 'map_based') {
                    $baseAmount = 10 + ($mapId * 10); // 基础灵石
                    $multiplier = mt_rand($rewardConfig['multiplier_min'], $rewardConfig['multiplier_max']);
                    $spiritStones = $baseAmount * $multiplier;
                } else {
                    $spiritStones = isset($rewardConfig['base_amount']) ? $rewardConfig['base_amount'] : 1000;
                }

                $rewards[] = [
                    'type' => 'spirit_stones',
                    'quantity' => $spiritStones,
                    'description' => "获得灵石 × {$spiritStones}"
                ];
                break;

            case 'pills':
                // 丹药奖励（按当前大境界智能分配）
                $quantity = isset($rewardConfig['quantity']) ? $rewardConfig['quantity'] : 1;

                // 获取角色当前境界信息
                $stmt = $db->prepare("SELECT realm_id FROM characters WHERE id = ?");
                $stmt->execute([$characterId]);
                $characterInfo = $stmt->fetch(PDO::FETCH_ASSOC);
                $realmId = $characterInfo ? $characterInfo['realm_id'] : 1;

                // 获取境界名称
                $stmt = $db->prepare("SELECT realm_name FROM realm_levels WHERE id = ?");
                $stmt->execute([$realmId]);
                $realmInfo = $stmt->fetch(PDO::FETCH_ASSOC);
                $realmName = $realmInfo ? $realmInfo['realm_name'] : '开光期';

                // 提取大境界名称（去掉"初期"、"中期"、"后期"、"巅峰"等）
                $majorRealm = preg_replace('/[初中后巅峰期]+$/', '', $realmName);

                // 智能选择符合境界的丹药
                $selectedPillId = null;
                $pillName = '';

                // 优先选择养魂丹和渡劫丹
                $stmt = $db->prepare("
                    SELECT id, item_name 
                    FROM game_items 
                    WHERE (item_name LIKE ? OR item_name LIKE ?) 
                    AND (item_name LIKE '%养魂丹%' OR item_name LIKE '%渡劫丹%')
                    ORDER BY RAND() 
                    LIMIT 1
                ");
                $stmt->execute(["%{$majorRealm}%", "%{$realmName}%"]);
                $pill = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($pill) {
                    $selectedPillId = $pill['id'];
                    $pillName = $pill['item_name'];
                } else {
                    // 如果没有找到对应境界的养魂丹/渡劫丹，选择高品质的通用丹药
                    $stmt = $db->prepare("
                        SELECT id, item_name 
                        FROM game_items 
                        WHERE rarity IN ('传说', '史诗', '稀有') 
                        AND item_type = 'consumable'
                        AND (item_name LIKE '%丹%' OR item_name LIKE '%药%')
                        ORDER BY 
                            CASE rarity 
                                WHEN '传说' THEN 3 
                                WHEN '史诗' THEN 2 
                                WHEN '稀有' THEN 1 
                                ELSE 0 
                            END DESC, 
                            RAND() 
                        LIMIT 1
                    ");
                    $stmt->execute();
                    $pill = $stmt->fetch(PDO::FETCH_ASSOC);

                    if ($pill) {
                        $selectedPillId = $pill['id'];
                        $pillName = $pill['item_name'];
                    }
                }

                if ($selectedPillId) {
                    $rewards[] = [
                        'type' => 'item',
                        'item_id' => $selectedPillId,
                        'item_name' => $pillName,
                        'quantity' => $quantity,
                        'description' => "获得{$pillName} × {$quantity}"
                    ];
                } else {
                    // 如果都没找到，给默认的高级丹药
                    $rewards[] = [
                        'type' => 'spirit_stones',
                        'quantity' => 5000,
                        'description' => "获得灵石 × 5000（丹药替代奖励）"
                    ];
                }
                break;

            case 'recipe':
                // 丹方奖励（智能分配未学过的丹方，符合当前大境界）
                $quantity = isset($rewardConfig['quantity']) ? $rewardConfig['quantity'] : 1;

                // 获取角色当前境界信息
                $stmt = $db->prepare("SELECT realm_id FROM characters WHERE id = ?");
                $stmt->execute([$characterId]);
                $characterInfo = $stmt->fetch(PDO::FETCH_ASSOC);
                $realmId = $characterInfo ? $characterInfo['realm_id'] : 1;

                // 获取境界名称
                $stmt = $db->prepare("SELECT realm_name FROM realm_levels WHERE id = ?");
                $stmt->execute([$realmId]);
                $realmInfo = $stmt->fetch(PDO::FETCH_ASSOC);
                $realmName = $realmInfo ? $realmInfo['realm_name'] : '开光期';

                // 提取大境界名称
                $majorRealm = preg_replace('/[初中后巅峰期]+$/', '', $realmName);

                // 获取已学会的丹方列表
                $stmt = $db->prepare("SELECT recipe_id FROM user_learned_recipes WHERE character_id = ?");
                $stmt->execute([$characterId]);
                $learnedRecipes = $stmt->fetchAll(PDO::FETCH_COLUMN);
                $learnedRecipeIds = !empty($learnedRecipes) ? implode(',', $learnedRecipes) : '0';

                $selectedRecipeId = null;
                $recipeName = '';

                // 优先选择符合境界的养魂丹方和渡劫丹方（未学过的）
                $stmt = $db->prepare("
                    SELECT cr.id, cr.recipe_name 
                    FROM crafting_recipes cr
                    WHERE cr.id NOT IN ({$learnedRecipeIds})
                    AND (cr.recipe_name LIKE ? OR cr.recipe_name LIKE ?)
                    AND (cr.recipe_name LIKE '%养魂丹方%' OR cr.recipe_name LIKE '%渡劫丹方%')
                    ORDER BY RAND() 
                    LIMIT 1
                ");
                $stmt->execute(["%{$majorRealm}%", "%{$realmName}%"]);
                $recipe = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($recipe) {
                    $selectedRecipeId = $recipe['id'];
                    $recipeName = $recipe['recipe_name'];
                } else {
                    // 如果没有找到对应境界的养魂丹方/渡劫丹方，选择属性丹方（未学过的）
                    $stmt = $db->prepare("
                        SELECT cr.id, cr.recipe_name 
                        FROM crafting_recipes cr
                        WHERE cr.id NOT IN ({$learnedRecipeIds})
                        AND cr.recipe_name LIKE '%属性丹方%'
                        ORDER BY RAND() 
                        LIMIT 1
                    ");
                    $stmt->execute();
                    $recipe = $stmt->fetch(PDO::FETCH_ASSOC);

                    if ($recipe) {
                        $selectedRecipeId = $recipe['id'];
                        $recipeName = $recipe['recipe_name'];
                    } else {
                        // 如果属性丹方也都学会了，选择任意未学过的丹方
                        $stmt = $db->prepare("
                            SELECT cr.id, cr.recipe_name 
                            FROM crafting_recipes cr
                            WHERE cr.id NOT IN ({$learnedRecipeIds})
                            ORDER BY RAND() 
                            LIMIT 1
                        ");
                        $stmt->execute();
                        $recipe = $stmt->fetch(PDO::FETCH_ASSOC);

                        if ($recipe) {
                            $selectedRecipeId = $recipe['id'];
                            $recipeName = $recipe['recipe_name'];
                        }
                    }
                }

                if ($selectedRecipeId) {
                    // 直接学会丹方，而不是给物品
                    $stmt = $db->prepare("
                        INSERT INTO user_learned_recipes (character_id, recipe_id, learned_time) 
                        VALUES (?, ?, NOW())
                        ON DUPLICATE KEY UPDATE learned_time = NOW()
                    ");
                    $stmt->execute([$characterId, $selectedRecipeId]);

                    $rewards[] = [
                        'type' => 'recipe_learned',
                        'recipe_id' => $selectedRecipeId,
                        'recipe_name' => $recipeName,
                        'quantity' => 1,
                        'description' => "学会丹方：{$recipeName}"
                    ];
                } else {
                    // 如果所有丹方都学会了，给灵石奖励
                    $rewards[] = [
                        'type' => 'spirit_stones',
                        'quantity' => 8000,
                        'description' => "获得灵石 × 8000（丹方替代奖励，所有丹方已学会）"
                    ];
                }
                break;

            default:
                throw new Exception("未知的奖励类型: {$rewardConfig['type']}");
        }

        return $rewards;
    } catch (Exception $e) {
        error_log("生成奇遇奖励失败: " . $e->getMessage());

        // 返回默认奖励
        return [
            [
                'type' => 'spirit_stones',
                'quantity' => 1000,
                'description' => "获得灵石 × 1000（默认奖励）"
            ]
        ];
    }
}

/**
 * 🎲 辅助函数：发放奇遇奖励到背包
 */
function distributeAdventureRewards($db, $characterId, $rewards)
{
    $distributedRewards = [];

    // 检查背包空间（奇遇可以例外，但最多超出20个空位）
    $stmt = $db->prepare("SELECT inventory_slots FROM characters WHERE id = ? LIMIT 1");
    $stmt->execute([$characterId]);
    $character = $stmt->fetch(PDO::FETCH_ASSOC);
    $maxSlots = intval($character['inventory_slots'] ?: 30);

    $stmt = $db->prepare("SELECT COUNT(*) as used_slots FROM user_inventories WHERE character_id = ?");
    $stmt->execute([$characterId]);
    $usedSlots = intval($stmt->fetch(PDO::FETCH_ASSOC)['used_slots']);

    $availableSlots = $maxSlots - $usedSlots;
    $maxExtraSlots = 20; // 奇遇最多可以超出20个空位
    $allowedSlots = $availableSlots + $maxExtraSlots;

    // 计算需要的空间（简化计算，每个奖励最多占1格）
    $requiredSlots = count($rewards);

    if ($requiredSlots > $allowedSlots && $availableSlots < -$maxExtraSlots) {
        // 如果已经超出了20个空位的限制，拒绝发放
        return [
            [
                'success' => false,
                'error' => "背包空间严重不足！当前已超出正常容量，无法继续获得奇遇奖励。请先整理背包。",
                'description' => "背包空间检查失败"
            ]
        ];
    }

    foreach ($rewards as $reward) {
        try {
            if ($reward['type'] == 'spirit_stones') {
                // 发放灵石到users表的spirit_stones字段
                // 首先获取角色对应的用户ID
                $stmt = $db->prepare("SELECT user_id FROM characters WHERE id = ?");
                $stmt->execute([$characterId]);
                $character = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($character && $character['user_id']) {
                    $stmt = $db->prepare("
                        UPDATE users 
                        SET spirit_stones = spirit_stones + ? 
                        WHERE id = ?
                    ");
                    $stmt->execute([$reward['quantity'], $character['user_id']]);
                } else {
                    throw new Exception("无法找到角色对应的用户ID");
                }

                $distributedRewards[] = [
                    'success' => true,
                    'type' => 'spirit_stones',
                    'quantity' => $reward['quantity'],
                    'description' => $reward['description']
                ];
            } elseif ($reward['type'] == 'item') {
                // 发放物品到背包（使用具体的物品ID，支持自动叠加）
                $itemId = isset($reward['item_id']) ? $reward['item_id'] : null;
                $quantity = isset($reward['quantity']) ? $reward['quantity'] : 1;

                if ($itemId) {
                    // 获取物品信息
                    $stmt = $db->prepare("SELECT item_name, item_type, max_stack, is_stackable FROM game_items WHERE id = ?");
                    $stmt->execute([$itemId]);
                    $item = $stmt->fetch(PDO::FETCH_ASSOC);

                    if ($item) {
                        $maxStack = intval($item['max_stack'] ?: 99);
                        $isStackable = $item['is_stackable'] !== 0; // 默认可堆叠

                        // 设置自定义属性
                        $customAttributes = [
                            'generation_time' => time(),
                            'context_type' => 'adventure_reward'
                        ];

                        if ($isStackable && $maxStack > 1) {
                            // 可堆叠物品：智能分配到现有堆叠或创建新堆叠
                            $remainingQuantity = $quantity;

                            // 1. 先尝试填充现有的未满堆叠位置
                            $stmt = $db->prepare("
                                SELECT id, quantity FROM user_inventories 
                                WHERE character_id = ? AND item_id = ? AND quantity < ?
                                ORDER BY quantity DESC
                            ");
                            $stmt->execute([$characterId, $itemId, $maxStack]);
                            $existingSlots = $stmt->fetchAll(PDO::FETCH_ASSOC);

                            foreach ($existingSlots as $slot) {
                                if ($remainingQuantity <= 0) break;

                                $availableSpace = $maxStack - $slot['quantity'];
                                $addToThisSlot = min($remainingQuantity, $availableSpace);

                                // 更新现有槽位
                                $stmt = $db->prepare("
                                    UPDATE user_inventories 
                                    SET quantity = quantity + ?, obtained_time = NOW()
                                    WHERE id = ?
                                ");
                                $stmt->execute([$addToThisSlot, $slot['id']]);

                                $remainingQuantity -= $addToThisSlot;
                            }

                            // 2. 如果还有剩余数量，创建新的背包位置
                            while ($remainingQuantity > 0) {
                                $addToNewSlot = min($remainingQuantity, $maxStack);
                                $sortWeight = time() + mt_rand(1, 1000); // 避免时间戳冲突

                                $stmt = $db->prepare("
                                    INSERT INTO user_inventories (
                                        character_id, item_id, item_type, quantity, 
                                        custom_attributes, bind_status, obtained_source, obtained_time, sort_weight
                                    ) VALUES (?, ?, ?, ?, ?, 'unbound', 'adventure_reward', NOW(), ?)
                                ");
                                $stmt->execute([
                                    $characterId,
                                    $itemId,
                                    $item['item_type'],
                                    $addToNewSlot,
                                    json_encode($customAttributes),
                                    $sortWeight
                                ]);

                                $remainingQuantity -= $addToNewSlot;
                            }
                        } else {
                            // 不可堆叠物品：逐个添加
                            for ($i = 0; $i < $quantity; $i++) {
                                $sortWeight = time() + mt_rand(1, 1000); // 避免时间戳冲突

                                $stmt = $db->prepare("
                                    INSERT INTO user_inventories (
                                        character_id, item_id, item_type, quantity, 
                                        custom_attributes, bind_status, obtained_source, obtained_time, sort_weight
                                    ) VALUES (?, ?, ?, 1, ?, 'unbound', 'adventure_reward', NOW(), ?)
                                ");
                                $stmt->execute([
                                    $characterId,
                                    $itemId,
                                    $item['item_type'],
                                    json_encode($customAttributes),
                                    $sortWeight
                                ]);
                            }
                        }

                        $distributedRewards[] = [
                            'success' => true,
                            'type' => 'item',
                            'item_name' => $item['item_name'],
                            'quantity' => $quantity,
                            'description' => $reward['description']
                        ];
                    } else {
                        $distributedRewards[] = [
                            'success' => false,
                            'error' => "未找到物品ID: {$itemId}",
                            'description' => $reward['description']
                        ];
                    }
                } else {
                    $distributedRewards[] = [
                        'success' => false,
                        'error' => "缺少物品ID",
                        'description' => $reward['description']
                    ];
                }
            } elseif ($reward['type'] == 'equipment') {
                // 发放装备到背包（使用品质系统动态生成）
                $targetQuality = isset($reward['quality']) ? $reward['quality'] : 'legendary';
                $level = isset($reward['level']) ? $reward['level'] : 1;

                // 🔧 修复：根据角色境界选择合适的装备
                // 获取角色当前境界信息
                $stmt = $db->prepare("SELECT realm_id FROM characters WHERE id = ?");
                $stmt->execute([$characterId]);
                $characterInfo = $stmt->fetch(PDO::FETCH_ASSOC);
                $realmId = $characterInfo ? $characterInfo['realm_id'] : 1;

                // 获取境界等级信息
                $stmt = $db->prepare("SELECT level FROM realm_levels WHERE id = ?");
                $stmt->execute([$realmId]);
                $realmInfo = $stmt->fetch(PDO::FETCH_ASSOC);
                $realmLevel = $realmInfo ? $realmInfo['level'] : 5;

                // 随机选择装备类型
                $itemTypes = ['weapon', 'armor', 'accessory'];
                $selectedType = $itemTypes[array_rand($itemTypes)];

                // 🔧 修复：根据境界等级选择合适的装备（允许±10级的范围）
                $minLevel = max(1, $realmLevel - 10);
                $maxLevel = $realmLevel + 10;

                $stmt = $db->prepare("
                    SELECT id, item_name, item_type, realm_requirement 
                    FROM game_items 
                    WHERE item_type = ? 
                    AND realm_requirement BETWEEN ? AND ?
                    ORDER BY ABS(realm_requirement - ?) ASC, RAND()
                    LIMIT 1
                ");
                $stmt->execute([$selectedType, $minLevel, $maxLevel, $realmLevel]);
                $equipment = $stmt->fetch(PDO::FETCH_ASSOC);

                // 如果没找到合适境界的装备，扩大搜索范围
                if (!$equipment) {
                    $stmt = $db->prepare("
                        SELECT id, item_name, item_type, realm_requirement 
                        FROM game_items 
                        WHERE item_type = ? 
                        AND realm_requirement <= ?
                        ORDER BY realm_requirement DESC, RAND()
                        LIMIT 1
                    ");
                    $stmt->execute([$selectedType, $realmLevel + 20]);
                    $equipment = $stmt->fetch(PDO::FETCH_ASSOC);
                }

                // 如果还是没找到，使用任意装备
                if (!$equipment) {
                    $stmt = $db->prepare("
                        SELECT id, item_name, item_type 
                        FROM game_items 
                        WHERE item_type = ? 
                        ORDER BY RAND() 
                        LIMIT 1
                    ");
                    $stmt->execute([$selectedType]);
                    $equipment = $stmt->fetch(PDO::FETCH_ASSOC);
                }

                if ($equipment) {
                    // 使用品质系统生成装备品质和属性
                    require_once __DIR__ . '/equipment_quality_system.php';

                    $itemId = $equipment['id'];
                    $itemType = $equipment['item_type'];
                    $itemName = $equipment['item_name'];

                    // 奇遇装备固定给神话品质
                    $generatedQuality = '神话';

                    // 使用品质系统获取完整的装备数据（包含正确的属性）
                    $equipmentData = EquipmentQualitySystem::getEquipmentForInventory(
                        $itemId,
                        $generatedQuality,
                        'special' // 特殊获得方式
                    );

                    // 获取物品的最大耐久度
                    $maxDurability = 100; // 默认耐久度
                    $stmt = $db->prepare("SELECT max_durability FROM game_items WHERE id = ?");
                    $stmt->execute([$itemId]);
                    $itemInfo = $stmt->fetch(PDO::FETCH_ASSOC);
                    if ($itemInfo && $itemInfo['max_durability']) {
                        $maxDurability = $itemInfo['max_durability'];
                    }

                    // 计算排序权重
                    $sortWeight = time(); // 简单的时间戳排序

                    // 设置自定义属性（包含品质系统生成的完整属性）
                    $customAttributes = [
                        'rarity' => $equipmentData['rarity'],
                        'rarity_en' => $equipmentData['rarity_en'],
                        'rarity_color' => $equipmentData['rarity_color'],
                        'multiplier' => $equipmentData['multiplier'],
                        'generation_time' => time(),
                        'context_type' => 'adventure_reward',
                        'original_quality' => $targetQuality,
                        'realm_matched' => true, // 标记为境界匹配的装备
                        'target_realm_level' => $realmLevel
                    ];

                    // 如果有计算后的属性，保存到自定义属性中
                    if (!empty($equipmentData['attributes'])) {
                        $customAttributes['calculated_attributes'] = $equipmentData['attributes'];
                    }

                    // 添加到背包
                    $stmt = $db->prepare("
                        INSERT INTO user_inventories (
                            character_id, item_id, item_type, current_durability, max_durability,
                            custom_attributes, bind_status, obtained_source, obtained_time, sort_weight
                        ) VALUES (?, ?, ?, ?, ?, ?, 'unbound', 'adventure_reward', NOW(), ?)
                    ");
                    $stmt->execute([
                        $characterId,
                        $itemId,
                        $itemType,
                        $maxDurability,
                        $maxDurability,
                        json_encode($customAttributes),
                        $sortWeight
                    ]);

                    $distributedRewards[] = [
                        'success' => true,
                        'type' => 'equipment',
                        'item_name' => $itemName,
                        'quality' => $generatedQuality,
                        'quantity' => 1,
                        'realm_requirement' => isset($equipment['realm_requirement']) ? $equipment['realm_requirement'] : $realmLevel,
                        'description' => "获得{$generatedQuality}品质装备：{$itemName}（境界要求：{$equipment['realm_requirement']}级）"
                    ];
                } else {
                    $distributedRewards[] = [
                        'success' => false,
                        'error' => "未找到任何装备",
                        'description' => $reward['description']
                    ];
                }
            } elseif ($reward['type'] == 'recipe_learned') {
                // 丹方已经在生成阶段直接学会了，这里只记录成功
                $distributedRewards[] = [
                    'success' => true,
                    'type' => 'recipe_learned',
                    'recipe_name' => $reward['recipe_name'],
                    'quantity' => 1,
                    'description' => $reward['description']
                ];
            } else {
                $distributedRewards[] = [
                    'success' => false,
                    'error' => "未知的奖励类型: " . $reward['type'],
                    'description' => $reward['description']
                ];
            }
        } catch (Exception $e) {
            $distributedRewards[] = [
                'success' => false,
                'error' => $e->getMessage(),
                'description' => $reward['description']
            ];
        }
    }

    return $distributedRewards;
}

/**
 * 🔥 境界掉落系统核心函数：根据地图关卡获取怪物境界
 */
function getMonsterRealmFromStage($db, $mapId, $stageNumber)
{
    try {
        // 从map_stages表获取怪物境界
        $stmt = $db->prepare("SELECT realm_level FROM map_stages WHERE map_id = ? AND stage_number = ?");
        $stmt->execute([$mapId, $stageNumber]);
        $result = $stmt->fetchColumn();

        if ($result) {
            error_log("🔥 境界掉落：地图{$mapId}关卡{$stageNumber}的怪物境界为{$result}");
            return intval($result);
        }

        // 如果map_stages表没有数据，根据关卡估算境界
        $estimatedRealm = max(1, floor($stageNumber / 5));
        error_log("🔥 境界掉落：关卡{$stageNumber}估算怪物境界为{$estimatedRealm}");
        return $estimatedRealm;
    } catch (Exception $e) {
        error_log("🔥 境界掉落系统错误：获取怪物境界失败 - " . $e->getMessage());
        return 1; // 默认境界
    }
}

/**
 * 🔥 境界掉落系统核心函数：根据怪物境界计算大境界编号
 */
function getMajorRealmByMonsterRealm($monsterRealm)
{
    return ceil($monsterRealm / 10); // 1-10归入大境界1，11-20归入大境界2，以此类推
}

/**
 * 🔥 境界掉落系统核心函数：根据怪物境界获取掉落物品（60%当前+30%前+10%后大境界）
 */
function getMonsterRealmDropItems($db, $monsterRealm, $fallbackGroupId = null)
{
    try {
        $majorRealm = getMajorRealmByMonsterRealm($monsterRealm);
        $allDropItems = [];

        // 28个大境界名称映射
        $realmNames = [
            1 => '开光期',
            2 => '灵虚期',
            3 => '辟谷期',
            4 => '心动期',
            5 => '元化期',
            6 => '元婴期',
            7 => '离合期',
            8 => '空冥期',
            9 => '寂灭期',
            10 => '大乘期',
            11 => '渡劫期',
            12 => '凡仙期',
            13 => '地仙期',
            14 => '天仙期',
            15 => '真仙期',
            16 => '太乙真仙期',
            17 => '太乙金仙期',
            18 => '太乙玄仙期',
            19 => '大罗真仙期',
            20 => '大罗金仙期',
            21 => '大罗玄仙期',
            22 => '准圣期',
            23 => '教主期',
            24 => '混元期',
            25 => '混元金仙期',
            26 => '混元至仙期',
            27 => '天道期',
            28 => '鸿蒙至元期'
        ];

        // 权重配置：当前大境界60%、前一大境界30%、后一大境界10%
        $realmWeights = [
            ['realm' => $majorRealm, 'weight' => 60],      // 当前大境界
        ];

        // 前一个大境界（30%权重）
        if ($majorRealm > 1) {
            $realmWeights[] = ['realm' => $majorRealm - 1, 'weight' => 30];
        }

        // 后一个大境界（10%权重）
        if ($majorRealm < 28) {
            $realmWeights[] = ['realm' => $majorRealm + 1, 'weight' => 10];
        }

        foreach ($realmWeights as $realmWeight) {
            $realmId = $realmWeight['realm'];
            $groupWeight = $realmWeight['weight'];

            if (!isset($realmNames[$realmId])) continue;

            $groupName = $realmNames[$realmId] . '装备掉落';

            // 查找对应的大境界掉落组
            $stmt = $db->prepare("SELECT id FROM drop_groups WHERE group_name = ? AND is_active = 1");
            $stmt->execute([$groupName]);
            $groupId = $stmt->fetchColumn();

            if ($groupId) {
                // 获取该掉落组的装备
                $stmt = $db->prepare("
                    SELECT dgi.*, gi.item_name, gi.item_type, gi.icon_image, gi.description, gi.sell_price,
                           gi.physical_attack, gi.immortal_attack, gi.physical_defense, gi.immortal_defense,
                           gi.hp_bonus, gi.mp_bonus, gi.speed_bonus, gi.critical_bonus, gi.critical_damage,
                           gi.accuracy_bonus, gi.dodge_bonus, gi.critical_resistance, gi.realm_requirement,
                           ? as group_weight
                    FROM drop_group_items dgi
                    JOIN game_items gi ON dgi.item_id = gi.id
                    WHERE dgi.group_id = ? AND gi.is_active = 1
                    AND gi.item_type IN ('weapon', 'armor', 'accessory')
                    ORDER BY dgi.drop_weight DESC
                ");
                $stmt->execute([$groupWeight, $groupId]);
                $groupItems = $stmt->fetchAll(PDO::FETCH_ASSOC);

                // 将物品加入总列表，权重基于大境界权重
                foreach ($groupItems as $item) {
                    $item['final_weight'] = intval($item['drop_weight']) * $groupWeight / 100;
                    $allDropItems[] = $item;
                }

                error_log("🔥 境界掉落：{$groupName} 权重{$groupWeight}% 获得" . count($groupItems) . "件装备");
            }
        }

        if (empty($allDropItems)) {
            error_log("🔥 境界掉落：怪物境界{$monsterRealm}（大境界{$majorRealm}）无装备，返回空");
            return [];
        }

        error_log("🔥 境界掉落成功：怪物境界{$monsterRealm}（大境界{$majorRealm}）总计" . count($allDropItems) . "件装备");
        return $allDropItems;
    } catch (Exception $e) {
        error_log("🔥 境界掉落系统错误：" . $e->getMessage());
        return [];
    }
}
