-- ========================================
-- 游戏物品表结构合并脚本
-- 目标：将 item_attributes 表合并到 game_items 表中
-- 执行时间：2024年12月19日
-- ========================================

-- 🔍 第一步：备份现有数据
CREATE TABLE `game_items_backup` AS SELECT * FROM `game_items`;
CREATE TABLE `item_attributes_backup` AS SELECT * FROM `item_attributes`;

-- 🔧 第二步：为 game_items 表添加 item_attributes 表的字段
ALTER TABLE `game_items` 
-- 战斗属性字段
ADD COLUMN `physical_attack` int(11) DEFAULT '0' COMMENT '物理攻击力',
ADD COLUMN `immortal_attack` int(11) DEFAULT '0' COMMENT '仙术攻击力',
ADD COLUMN `physical_defense` int(11) DEFAULT '0' COMMENT '物理防御力', 
ADD COLUMN `immortal_defense` int(11) DEFAULT '0' COMMENT '仙术防御力',
ADD COLUMN `hp_bonus` int(11) DEFAULT '0' COMMENT '生命值加成',
ADD COLUMN `mp_bonus` int(11) DEFAULT '0' COMMENT '法力值加成',
ADD COLUMN `speed_bonus` int(11) DEFAULT '0' COMMENT '速度加成',
ADD COLUMN `critical_rate` decimal(5,2) DEFAULT '0.00' COMMENT '暴击率(%)',
ADD COLUMN `critical_damage` decimal(5,2) DEFAULT '0.00' COMMENT '暴击伤害(%)',
ADD COLUMN `accuracy_bonus` decimal(5,2) DEFAULT '0.00' COMMENT '命中率加成(%)',
ADD COLUMN `dodge_bonus` decimal(5,2) DEFAULT '0.00' COMMENT '闪避率加成(%)',
ADD COLUMN `block_bonus` decimal(5,2) DEFAULT '0.00' COMMENT '格挡率加成(%)',

-- 特殊效果字段
ADD COLUMN `special_effects` text DEFAULT NULL COMMENT '特殊效果(JSON格式)',

-- 属性类型字段（保留用于兼容性）
ADD COLUMN `attribute_type` varchar(50) DEFAULT 'base' COMMENT '属性类型';

-- 🔄 第三步：迁移 item_attributes 表的数据到 game_items 表
UPDATE `game_items` gi
JOIN `item_attributes` ia ON gi.id = ia.item_id
SET 
    gi.physical_attack = COALESCE(ia.physical_attack, 0),
    gi.immortal_attack = COALESCE(ia.immortal_attack, 0),
    gi.physical_defense = COALESCE(ia.physical_defense, 0),
    gi.immortal_defense = COALESCE(ia.immortal_defense, 0),
    gi.hp_bonus = COALESCE(ia.hp_bonus, 0),
    gi.mp_bonus = COALESCE(ia.mp_bonus, 0),
    gi.speed_bonus = COALESCE(ia.speed_bonus, 0),
    gi.critical_rate = COALESCE(ia.critical_rate, 0.00),
    gi.critical_damage = COALESCE(ia.critical_damage, 0.00),
    gi.accuracy_bonus = COALESCE(ia.accuracy_bonus, 0.00),
    gi.dodge_bonus = COALESCE(ia.dodge_bonus, 0.00),
    gi.block_bonus = COALESCE(ia.block_bonus, 0.00),
    gi.special_effects = ia.special_effects,
    gi.attribute_type = COALESCE(ia.attribute_type, 'base');

-- 🔍 第四步：验证数据迁移结果
SELECT 
    '迁移前 item_attributes 记录数' as description,
    COUNT(*) as count 
FROM item_attributes_backup;

SELECT 
    '迁移后 game_items 中有属性的记录数' as description,
    COUNT(*) as count 
FROM game_items 
WHERE physical_attack > 0 OR immortal_attack > 0 OR physical_defense > 0 OR immortal_defense > 0;

-- 🔍 第五步：检查是否有遗漏的数据
SELECT 
    ia.item_id,
    gi.item_name,
    ia.physical_attack,
    ia.immortal_attack,
    'item_attributes中存在但game_items中不存在的记录' as note
FROM item_attributes ia
LEFT JOIN game_items gi ON ia.item_id = gi.id
WHERE gi.id IS NULL;

-- 📊 第六步：创建索引优化查询性能
CREATE INDEX idx_game_items_physical_attack ON game_items(physical_attack);
CREATE INDEX idx_game_items_immortal_attack ON game_items(immortal_attack);
CREATE INDEX idx_game_items_physical_defense ON game_items(physical_defense);
CREATE INDEX idx_game_items_immortal_defense ON game_items(immortal_defense);

-- 🎯 第七步：更新相关字段的默认值和约束
-- 确保新字段有合理的默认值
UPDATE game_items 
SET 
    physical_attack = 0 WHERE physical_attack IS NULL,
    immortal_attack = 0 WHERE immortal_attack IS NULL,
    physical_defense = 0 WHERE physical_defense IS NULL,
    immortal_defense = 0 WHERE immortal_defense IS NULL,
    hp_bonus = 0 WHERE hp_bonus IS NULL,
    mp_bonus = 0 WHERE mp_bonus IS NULL,
    speed_bonus = 0 WHERE speed_bonus IS NULL,
    critical_rate = 0.00 WHERE critical_rate IS NULL,
    critical_damage = 0.00 WHERE critical_damage IS NULL,
    accuracy_bonus = 0.00 WHERE accuracy_bonus IS NULL,
    dodge_bonus = 0.00 WHERE dodge_bonus IS NULL,
    block_bonus = 0.00 WHERE block_bonus IS NULL,
    attribute_type = 'base' WHERE attribute_type IS NULL;

-- 🗑️ 第八步：重命名 item_attributes 表（不直接删除，以防需要回滚）
RENAME TABLE `item_attributes` TO `item_attributes_deprecated`;

-- 📝 第九步：创建视图保持向后兼容性（可选）
CREATE VIEW `item_attributes_compat` AS
SELECT 
    id as attribute_id,
    id as item_id,
    physical_attack,
    immortal_attack,
    physical_defense,
    immortal_defense,
    hp_bonus,
    mp_bonus,
    speed_bonus,
    critical_rate,
    critical_damage,
    accuracy_bonus,
    dodge_bonus,
    block_bonus,
    special_effects,
    attribute_type,
    created_at,
    updated_at
FROM game_items
WHERE item_type IN ('weapon', 'armor', 'accessory');

-- ✅ 第十步：验证合并结果
SELECT 
    'game_items表总记录数' as metric,
    COUNT(*) as value
FROM game_items
UNION ALL
SELECT 
    '有物理攻击力的物品数',
    COUNT(*)
FROM game_items 
WHERE physical_attack > 0
UNION ALL
SELECT 
    '有仙术攻击力的物品数',
    COUNT(*)
FROM game_items 
WHERE immortal_attack > 0
UNION ALL
SELECT 
    '有物理防御力的物品数',
    COUNT(*)
FROM game_items 
WHERE physical_defense > 0
UNION ALL
SELECT 
    '有仙术防御力的物品数',
    COUNT(*)
FROM game_items 
WHERE immortal_defense > 0;

-- 📋 合并完成报告
SELECT 
    '✅ 表合并完成' as status,
    NOW() as completion_time,
    'game_items 和 item_attributes 已成功合并' as message;

-- ⚠️ 重要提醒：
-- 1. 执行此脚本前请确保数据库已备份
-- 2. 合并后需要更新所有相关的PHP代码，移除JOIN item_attributes的查询
-- 3. 如果需要回滚，可以使用备份表恢复数据
-- 4. 建议在测试环境先执行此脚本验证无误后再在生产环境执行 