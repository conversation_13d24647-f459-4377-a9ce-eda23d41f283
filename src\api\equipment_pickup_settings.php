<?php

/**
 * 装备拾取设置API
 * 处理战斗中装备掉落的拾取过滤设置
 */

require_once __DIR__ . '/../includes/functions.php';

header('Content-Type: application/json; charset=utf-8');

// 检查维护模式
if (isMaintenanceMode()) {
    echo json_encode([
        'success' => false,
        'message' => '游戏正在维护中，请稍后再试',
        'maintenance' => true
    ]);
    exit;
}

// 记录API调用（如果开启了调试）
if (DEBUG_LOG_API_CALLS) {
    writeLog("API调用: equipment_pickup_settings.php", 'DEBUG', 'api.log');
}

// 检查用户登录状态
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => '请先登录']);
    exit;
}

// 获取用户ID
$userId = $_SESSION['user_id'];
if (!$userId) {
    echo json_encode(['success' => false, 'message' => '用户信息获取失败']);
    exit;
}

$action = isset($_GET['action']) ? $_GET['action'] : (isset($_POST['action']) ? $_POST['action'] : '');

try {
    $pdo = getDatabase();

    switch ($action) {
        case 'get_settings':
            getPickupSettings($pdo, $userId);
            break;

        case 'save_settings':
            savePickupSettings($pdo, $userId);
            break;

        case 'get_quality_list':
            getQualityList();
            break;

        default:
            echo json_encode(['success' => false, 'message' => '无效的操作']);
            break;
    }
} catch (Exception $e) {
    error_log("装备拾取设置API错误: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => '系统错误，请稍后重试']);
}

/**
 * 获取用户的装备拾取设置
 */
function getPickupSettings($pdo, $userId)
{
    try {
        // 获取角色ID
        $stmt = $pdo->prepare("SELECT id FROM characters WHERE user_id = ? LIMIT 1");
        $stmt->execute([$userId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$character) {
            echo json_encode(['success' => false, 'message' => '角色不存在']);
            return;
        }

        $characterId = $character['id'];

        // 检查pickup_settings字段是否存在，如果不存在则添加
        try {
            $stmt = $pdo->query("SHOW COLUMNS FROM characters LIKE 'pickup_settings'");
            if ($stmt->rowCount() == 0) {
                $pdo->exec("ALTER TABLE characters ADD COLUMN pickup_settings TEXT COMMENT '装备拾取设置JSON'");
                error_log("已添加pickup_settings字段到characters表");
            }
        } catch (Exception $e) {
            error_log("检查/添加pickup_settings字段失败: " . $e->getMessage());
        }

        // 检查是否已有设置记录
        $stmt = $pdo->prepare("
            SELECT pickup_settings FROM characters 
            WHERE id = ?
        ");
        $stmt->execute([$characterId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        $defaultSettings = [
            'quality_filter' => ['普通', '稀有', '史诗', '传说', '神话'], // 默认全选
            'filter_below_realm' => false // 默认不过滤低境界装备
        ];

        $settings = $defaultSettings;

        if ($result && !empty($result['pickup_settings'])) {
            $savedSettings = json_decode($result['pickup_settings'], true);
            if ($savedSettings) {
                $settings = array_merge($defaultSettings, $savedSettings);
            }
        }

        echo json_encode([
            'success' => true,
            'settings' => $settings
        ]);
    } catch (Exception $e) {
        error_log("获取拾取设置失败: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => '获取设置失败']);
    }
}

/**
 * 保存用户的装备拾取设置
 */
function savePickupSettings($pdo, $userId)
{
    try {
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input) {
            echo json_encode(['success' => false, 'message' => '无效的请求数据']);
            return;
        }

        // 获取角色ID
        $stmt = $pdo->prepare("SELECT id FROM characters WHERE user_id = ? LIMIT 1");
        $stmt->execute([$userId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$character) {
            echo json_encode(['success' => false, 'message' => '角色不存在']);
            return;
        }

        $characterId = $character['id'];

        // 验证品质过滤设置
        $validQualities = ['普通', '稀有', '史诗', '传说', '神话'];
        $qualityFilter = isset($input['quality_filter']) ? $input['quality_filter'] : [];

        // 确保品质过滤是数组且包含有效品质
        if (!is_array($qualityFilter)) {
            $qualityFilter = [];
        }

        $qualityFilter = array_intersect($qualityFilter, $validQualities);

        // 境界过滤设置
        $filterBelowRealm = isset($input['filter_below_realm']) ? (bool)$input['filter_below_realm'] : false;

        $settings = [
            'quality_filter' => $qualityFilter,
            'filter_below_realm' => $filterBelowRealm,
            'updated_time' => time()
        ];

        // 检查pickup_settings字段是否存在，如果不存在则添加
        try {
            $stmt = $pdo->query("SHOW COLUMNS FROM characters LIKE 'pickup_settings'");
            if ($stmt->rowCount() == 0) {
                $pdo->exec("ALTER TABLE characters ADD COLUMN pickup_settings TEXT COMMENT '装备拾取设置JSON'");
                error_log("已添加pickup_settings字段到characters表");
            }
        } catch (Exception $e) {
            error_log("检查/添加pickup_settings字段失败: " . $e->getMessage());
        }

        // 保存设置
        $stmt = $pdo->prepare("
            UPDATE characters 
            SET pickup_settings = ? 
            WHERE id = ?
        ");
        $stmt->execute([json_encode($settings, JSON_UNESCAPED_UNICODE), $characterId]);

        echo json_encode([
            'success' => true,
            'message' => '装备拾取设置已保存',
            'settings' => $settings
        ]);
    } catch (Exception $e) {
        error_log("保存拾取设置失败: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => '保存设置失败']);
    }
}

/**
 * 获取装备品质列表
 */
function getQualityList()
{
    $qualities = [
        [
            'name' => '普通',
            'en_name' => 'common',
            'color' => '#95a5a6',
            'description' => '普通品质装备'
        ],
        [
            'name' => '稀有',
            'en_name' => 'uncommon',
            'color' => '#27ae60',
            'description' => '稀有品质装备'
        ],
        [
            'name' => '史诗',
            'en_name' => 'rare',
            'color' => '#3498db',
            'description' => '史诗品质装备'
        ],
        [
            'name' => '传说',
            'en_name' => 'epic',
            'color' => '#9b59b6',
            'description' => '传说品质装备'
        ],
        [
            'name' => '神话',
            'en_name' => 'legendary',
            'color' => '#d4af37',
            'description' => '神话品质装备'
        ]
    ];

    echo json_encode([
        'success' => true,
        'qualities' => $qualities
    ]);
}
