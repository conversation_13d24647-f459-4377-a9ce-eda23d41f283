/**
 * 冰系技能模块 - 冰锥术
 * 适用于 item_skills 表中的 animation_model = 'bingzhuishu'
 */

// 冰系技能 - 冰锥术
class BingZhuiShuSkill extends BaseSkill {
    async execute(skillData, weaponImage) {
        // 使用数据库中的真实技能名称，而不是硬编码
        const skillName = skillData?.skillName || skillData?.displayName || '冰锥术'; // 提供默认值作为后备
        await this.showSkillShout(skillName);
        
        // 执行冰锥术动画，传递武器图片
        await this.createBingZhuiShuAnimation(weaponImage);
    }
    
    async createBingZhuiShuAnimation(weaponImage) {
        // 🔧 修复：动态判断位置映射
        let casterPos, targetPos;
        
        if (this.isEnemySkill) {
            // 敌方技能：敌人是施法者，玩家是目标
            casterPos = this.getCharacterPosition(false); // 敌人位置
            targetPos = this.getCharacterPosition(true);  // 玩家位置
        } else {
            // 我方技能：玩家是施法者，敌人是目标
            casterPos = this.getCharacterPosition(true);  // 玩家位置
            targetPos = this.getCharacterPosition(false); // 敌人位置
        }
        
        // 创建动画容器
        const container = document.createElement('div');
        container.className = 'bingzhuishu-container';
        this.effectsContainer.appendChild(container);
        
        // 🔧 修复：使用动态位置
        const startX = casterPos.x;
        const startY = casterPos.y;
        const endX = targetPos.x;
        const endY = targetPos.y;
        
        try {
            // === 第一阶段：蓄力 - 寒冰元素汇聚 ===
            await this.createChargePhase(container, startX, startY, weaponImage);
            
            // === 第二阶段：发射 - 冰锥高速飞行 ===
            await this.createLaunchPhase(container, startX, startY, endX, endY);
            
            // === 第三阶段：击中 - 冰爆和霜冻扩散 ===
            await this.createImpactPhase(container, endX, endY);
            
        } finally {
            // 清理容器
            setTimeout(() => {
                if (container && container.parentNode) {
                    container.parentNode.removeChild(container);
                }
            }, 100);
        }
    }
    
    // 蓄力阶段：寒冰元素汇聚
    async createChargePhase(container, startX, startY, weaponImage) {
        // 创建寒冰魔法阵
        const iceCircle = document.createElement('div');
        iceCircle.className = 'bingzhuishu-magic-circle';
        iceCircle.style.left = `${startX}px`;
        iceCircle.style.top = `${startY}px`;
        container.appendChild(iceCircle);
        
        // 创建内圈冰霜符文
        const innerRunes = document.createElement('div');
        innerRunes.className = 'bingzhuishu-inner-runes';
        innerRunes.style.left = `${startX}px`;
        innerRunes.style.top = `${startY}px`;
        container.appendChild(innerRunes);
        
        // 创建外圈寒气符文
        const outerRunes = document.createElement('div');
        outerRunes.className = 'bingzhuishu-outer-runes';
        outerRunes.style.left = `${startX}px`;
        outerRunes.style.top = `${startY}px`;
        container.appendChild(outerRunes);
        
        // 创建武器图片在中心旋转
        if (weaponImage) {
            const weaponSprite = document.createElement('div');
            weaponSprite.className = 'bingzhuishu-weapon-sprite';
            weaponSprite.style.left = `${startX}px`;
            weaponSprite.style.top = `${startY}px`;
            
            // 添加武器图片背景
            this.addWeaponImage(weaponSprite, weaponImage);
            
            container.appendChild(weaponSprite);
        }
        
        // 创建蓄力能量核心
        const energyCore = document.createElement('div');
        energyCore.className = 'bingzhuishu-energy-core';
        energyCore.style.left = `${startX}px`;
        energyCore.style.top = `${startY}px`;
        container.appendChild(energyCore);
        
        // 创建冰霜粒子汇聚效果（增加到35个）
        for (let i = 0; i < 35; i++) {
            const frostParticle = document.createElement('div');
            frostParticle.className = 'bingzhuishu-charge-frost';
            frostParticle.style.left = `${startX}px`;
            frostParticle.style.top = `${startY}px`;
            
            const angle = Math.random() * Math.PI * 2;
            const radius = 18 + Math.random() * 42;
            const moveX = Math.cos(angle) * radius;
            const moveY = Math.sin(angle) * radius;
            
            frostParticle.style.setProperty('--chargeX', `${moveX}px`);
            frostParticle.style.setProperty('--chargeY', `${moveY}px`);
            frostParticle.style.animationDelay = `${Math.random() * 1.1}s`;
            
            container.appendChild(frostParticle);
        }
        
        // 创建环绕的寒气螺旋
        for (let i = 0; i < 22; i++) {
            const angle = (i / 22) * Math.PI * 2;
            const radius = 75;
            const spiralX = startX + Math.cos(angle) * radius;
            const spiralY = startY + Math.sin(angle) * radius;
            
            const spiral = document.createElement('div');
            spiral.className = 'bingzhuishu-charge-spiral';
            spiral.style.left = `${spiralX}px`;
            spiral.style.top = `${spiralY}px`;
            spiral.style.animationDelay = `${i * 0.03}s`;
            container.appendChild(spiral);
        }
        
        // 创建寒冰能量波纹
        for (let i = 0; i < 8; i++) {
            const ripple = document.createElement('div');
            ripple.className = 'bingzhuishu-energy-ripple';
            ripple.style.left = `${startX}px`;
            ripple.style.top = `${startY}px`;
            ripple.style.animationDelay = `${i * 0.08}s`;
            container.appendChild(ripple);
        }
        
        // 创建冰晶凝结效果
        for (let i = 0; i < 16; i++) {
            const crystal = document.createElement('div');
            crystal.className = 'bingzhuishu-ice-crystal';
            crystal.style.left = `${startX + (Math.random() - 0.5) * 55}px`;
            crystal.style.top = `${startY + (Math.random() - 0.5) * 55}px`;
            crystal.style.animationDelay = `${Math.random() * 0.9}s`;
            container.appendChild(crystal);
        }
        
        // 创建寒气弥漫效果
        for (let i = 0; i < 12; i++) {
            const mist = document.createElement('div');
            mist.className = 'bingzhuishu-cold-mist';
            mist.style.left = `${startX + (Math.random() - 0.5) * 60}px`;
            mist.style.top = `${startY + (Math.random() - 0.5) * 60}px`;
            mist.style.animationDelay = `${Math.random() * 0.8}s`;
            container.appendChild(mist);
        }
        
        // 创建冰霜共鸣效果
        for (let i = 0; i < 6; i++) {
            const resonance = document.createElement('div');
            resonance.className = 'bingzhuishu-frost-resonance';
            resonance.style.left = `${startX}px`;
            resonance.style.top = `${startY}px`;
            resonance.style.animationDelay = `${i * 0.18}s`;
            container.appendChild(resonance);
        }
        
        // 等待蓄力完成
        await this.wait(1100);
        
        // 移除蓄力效果
        iceCircle.remove();
        innerRunes.remove();
        outerRunes.remove();
        energyCore.remove();
        document.querySelectorAll('.bingzhuishu-charge-frost').forEach(f => f.remove());
        document.querySelectorAll('.bingzhuishu-charge-spiral').forEach(s => s.remove());
        document.querySelectorAll('.bingzhuishu-energy-ripple').forEach(r => r.remove());
        document.querySelectorAll('.bingzhuishu-ice-crystal').forEach(c => c.remove());
        document.querySelectorAll('.bingzhuishu-cold-mist').forEach(m => m.remove());
        document.querySelectorAll('.bingzhuishu-frost-resonance').forEach(r => r.remove());
        document.querySelectorAll('.bingzhuishu-weapon-sprite').forEach(w => w.remove());
    }
    
    // 发射阶段：冰锥高速飞行
    async createLaunchPhase(container, startX, startY, endX, endY) {
        // 预发射寒气爆发
        for (let i = 0; i < 4; i++) {
            const burst = document.createElement('div');
            burst.className = 'bingzhuishu-cold-burst';
            burst.style.left = `${startX}px`;
            burst.style.top = `${startY}px`;
            burst.style.animationDelay = `${i * 0.25}s`;
            container.appendChild(burst);
        }
        
        await this.wait(300);
        
        // 计算飞行参数
        const deltaX = endX - startX;
        const deltaY = endY - startY;
        const flyTime = 1.2; // 飞行时间
        
        // 主冰锥
        const mainCone = document.createElement('div');
        mainCone.className = 'bingzhuishu-main-cone';
        mainCone.style.left = `${startX}px`;
        mainCone.style.top = `${startY}px`;
        mainCone.style.setProperty('--targetX', `${deltaX}px`);
        mainCone.style.setProperty('--targetY', `${deltaY}px`);
        mainCone.style.setProperty('--flyTime', `${flyTime}s`);
        
        // 🔧 修复：动态调整冰锥方向
        if (this.isEnemySkill) {
            // 敌方技能：冰锥尖端向下指向玩家
            mainCone.style.transform = 'rotate(180deg)';
        } else {
            // 我方技能：冰锥尖端向上指向敌人
            mainCone.style.transform = 'rotate(0deg)';
        }
        
        container.appendChild(mainCone);
        
        // 副冰锥1 - 左后方跟随
        await this.wait(150);
        const subCone1 = document.createElement('div');
        subCone1.className = 'bingzhuishu-sub-cone-1';
        subCone1.style.left = `${startX - 15}px`; // 左偏移
        subCone1.style.top = `${startY + 10}px`;  // 稍微下偏移
        subCone1.style.setProperty('--targetX', `${deltaX + 10}px`); // 目标位置稍微调整
        subCone1.style.setProperty('--targetY', `${deltaY - 5}px`);
        subCone1.style.setProperty('--flyTime', `${flyTime * 1.1}s`); // 稍微慢一点
        
        // 🔧 修复：动态调整副冰锥1方向
        if (this.isEnemySkill) {
            // 敌方技能：冰锥尖端向下指向玩家
            subCone1.style.transform = 'rotate(180deg)';
        } else {
            // 我方技能：冰锥尖端向上指向敌人
            subCone1.style.transform = 'rotate(0deg)';
        }
        
        container.appendChild(subCone1);
        
        // 副冰锥2 - 右后方跟随
        await this.wait(100);
        const subCone2 = document.createElement('div');
        subCone2.className = 'bingzhuishu-sub-cone-2';
        subCone2.style.left = `${startX + 12}px`; // 右偏移
        subCone2.style.top = `${startY + 8}px`;   // 稍微下偏移
        subCone2.style.setProperty('--targetX', `${deltaX - 8}px`); // 目标位置稍微调整
        subCone2.style.setProperty('--targetY', `${deltaY + 3}px`);
        subCone2.style.setProperty('--flyTime', `${flyTime * 1.05}s`); // 稍微慢一点
        
        // 🔧 修复：动态调整副冰锥2方向
        if (this.isEnemySkill) {
            // 敌方技能：冰锥尖端向下指向玩家
            subCone2.style.transform = 'rotate(180deg)';
        } else {
            // 我方技能：冰锥尖端向上指向敌人
            subCone2.style.transform = 'rotate(0deg)';
        }
        
        container.appendChild(subCone2);
        
        // 冰锥旋转层效果（跟随主冰锥）
        for (let layer = 1; layer <= 3; layer++) {
            const spiralLayer = document.createElement('div');
            spiralLayer.className = `bingzhuishu-spiral-layer-${layer}`;
            spiralLayer.style.left = `${startX}px`;
            spiralLayer.style.top = `${startY}px`;
            spiralLayer.style.setProperty('--targetX', `${deltaX}px`);
            spiralLayer.style.setProperty('--targetY', `${deltaY}px`);
            spiralLayer.style.setProperty('--flyTime', `${flyTime}s`);
            spiralLayer.style.animationDelay = `${layer * 0.05}s`;
            container.appendChild(spiralLayer);
        }
        
        // 增强冰霜拖尾（跟随主冰锥）
        for (let i = 0; i < 10; i++) {
            const trail = document.createElement('div');
            trail.className = 'bingzhuishu-enhanced-trail';
            trail.style.left = `${startX}px`;
            trail.style.top = `${startY}px`;
            trail.style.setProperty('--targetX', `${deltaX}px`);
            trail.style.setProperty('--targetY', `${deltaY}px`);
            trail.style.setProperty('--flyTime', `${flyTime}s`);
            trail.style.setProperty('--trailIndex', i);
            trail.style.animationDelay = `${i * 0.08}s`;
            container.appendChild(trail);
        }
        
        // 飞行冰晶散落
        for (let i = 0; i < 8; i++) {
            const crystal = document.createElement('div');
            crystal.className = 'bingzhuishu-flight-crystal';
            crystal.style.left = `${startX + (Math.random() - 0.5) * 40}px`;
            crystal.style.top = `${startY + (Math.random() - 0.5) * 40}px`;
            crystal.style.animationDelay = `${i * 0.1}s`;
            container.appendChild(crystal);
        }
        
        // 寒气轨迹
        for (let i = 0; i < 6; i++) {
            const mist = document.createElement('div');
            mist.className = 'bingzhuishu-flight-mist';
            mist.style.left = `${startX + (Math.random() - 0.5) * 30}px`;
            mist.style.top = `${startY + (Math.random() - 0.5) * 30}px`;
            mist.style.animationDelay = `${i * 0.12}s`;
            container.appendChild(mist);
        }
        
        await this.wait(flyTime * 1000);
    }
    
    // 击中阶段：冰爆和霜冻扩散
    async createImpactPhase(container, endX, endY) {
        // 第一阶段：瞬间冰爆闪光
        const impactFlash = document.createElement('div');
        impactFlash.className = 'bingzhuishu-impact-flash';
        impactFlash.style.left = `${endX}px`;
        impactFlash.style.top = `${endY}px`;
        container.appendChild(impactFlash);
        
        // 第二阶段：冰爆核心
        const iceCore = document.createElement('div');
        iceCore.className = 'bingzhuishu-ice-core';
        iceCore.style.left = `${endX}px`;
        iceCore.style.top = `${endY}px`;
        container.appendChild(iceCore);
        
        // 第三阶段：多层冰霜冲击波
        setTimeout(() => {
            for (let i = 0; i < 5; i++) {
                const shockwave = document.createElement('div');
                shockwave.className = 'bingzhuishu-impact-shockwave';
                shockwave.style.left = `${endX}px`;
                shockwave.style.top = `${endY}px`;
                shockwave.style.animationDelay = `${i * 0.1}s`;
                container.appendChild(shockwave);
            }
        }, 100);
        
        // 第四阶段：冰晶碎片爆发
        setTimeout(() => {
            for (let i = 0; i < 45; i++) {
                const shard = document.createElement('div');
                shard.className = 'bingzhuishu-ice-shard';
                shard.style.left = `${endX}px`;
                shard.style.top = `${endY}px`;
                
                const angle = (i / 45) * Math.PI * 2;
                const distance = 25 + Math.random() * 75;
                const velocity = 0.7 + Math.random() * 0.8;
                
                shard.style.setProperty('--shardAngle', `${angle * 180 / Math.PI}deg`);
                shard.style.setProperty('--shardDistance', `${distance}px`);
                shard.style.setProperty('--shardVelocity', velocity);
                shard.style.animationDelay = `${i * 0.012}s`;
                
                container.appendChild(shard);
            }
        }, 200);
        
        // 第五阶段：霜冻扩散波
        setTimeout(() => {
            for (let i = 0; i < 3; i++) {
                const frostWave = document.createElement('div');
                frostWave.className = 'bingzhuishu-frost-wave';
                frostWave.style.left = `${endX}px`;
                frostWave.style.top = `${endY}px`;
                frostWave.style.animationDelay = `${i * 0.15}s`;
                container.appendChild(frostWave);
            }
        }, 300);
        
        // 第六阶段：冰柱突起
        setTimeout(() => {
            for (let i = 0; i < 12; i++) {
                const icicle = document.createElement('div');
                icicle.className = 'bingzhuishu-icicle';
                
                const angle = (i / 12) * Math.PI * 2;
                const radius = 30 + Math.random() * 25;
                const icicleX = endX + Math.cos(angle) * radius;
                const icicleY = endY + Math.sin(angle) * radius;
                
                icicle.style.left = `${icicleX}px`;
                icicle.style.top = `${icicleY}px`;
                icicle.style.setProperty('--icicleHeight', `${40 + Math.random() * 25}px`);
                icicle.style.animationDelay = `${i * 0.04}s`;
                
                container.appendChild(icicle);
            }
        }, 400);
        
        // 第七阶段：寒雾弥漫
        setTimeout(() => {
            for (let i = 0; i < 20; i++) {
                const coldMist = document.createElement('div');
                coldMist.className = 'bingzhuishu-impact-mist';
                coldMist.style.left = `${endX + (Math.random() - 0.5) * 140}px`;
                coldMist.style.top = `${endY + (Math.random() - 0.5) * 140}px`;
                coldMist.style.animationDelay = `${i * 0.05}s`;
                container.appendChild(coldMist);
            }
        }, 500);
        
        // 第八阶段：冰霜结晶
        setTimeout(() => {
            for (let i = 0; i < 15; i++) {
                const crystal = document.createElement('div');
                crystal.className = 'bingzhuishu-impact-crystal';
                crystal.style.left = `${endX + (Math.random() - 0.5) * 80}px`;
                crystal.style.top = `${endY + (Math.random() - 0.5) * 80}px`;
                crystal.style.animationDelay = `${i * 0.08}s`;
                container.appendChild(crystal);
            }
        }, 600);
        
        // 第九阶段：寒气余韵
        setTimeout(() => {
            for (let i = 0; i < 8; i++) {
                const afterchill = document.createElement('div');
                afterchill.className = 'bingzhuishu-afterchill';
                afterchill.style.left = `${endX + (Math.random() - 0.5) * 60}px`;
                afterchill.style.top = `${endY + (Math.random() - 0.5) * 60}px`;
                afterchill.style.animationDelay = `${i * 0.2}s`;
                container.appendChild(afterchill);
            }
        }, 800);
        
        // 击中特效
        this.createHitEffect(endX, endY, true);
        
        // 🔧 修复：给被攻击者添加冰冻效果
        const targetSelector = !this.isEnemySkill ? '.enemy .character-sprite' : '.player .character-sprite';
        const targetSprite = document.querySelector(targetSelector);
        if (targetSprite) {
            targetSprite.style.animation = 'ice-hit 2.2s ease-out, ice-freeze 0.3s ease-in-out 3';
        }
        
        await this.wait(2000);
        
        // 🔧 修复：清理击中效果
        if (targetSprite) {
            targetSprite.style.animation = '';
        }
    }
}

/**
 * 玄冰剑技能类 - 剑类/冰系混合技能
 * 结合冰锥术的蓄力效果和万剑诀的飞行击中效果
 * 3把剑120度间隔旋转，甩飞后穿透攻击
 */
class XuanBingJianSkill extends BaseSkill {
    constructor(battleSystem) {
        super(battleSystem);
        this.skillName = '玄冰剑';
        this.elementType = 'ice-sword';
        
        // v2.0新增：技能实例管理
        this.animationContainers = new Set();
        this.activeTimers = new Set();
    }

    async execute(skillData, weaponImage) {
        try {
            if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('ice-skills', '❄️⚔️ 玄冰剑技能开始执行');
        }
            
            // 🔧 修复：使用真实技能名称而不是动画名称
            const skillName = skillData?.skillName || skillData?.displayName || this.skillName || '玄冰剑';
            
            // 必须调用技能喊话
            await this.showSkillShout(skillName);
            
            // 调用具体的技能动画方法
            await this.createXuanBingJian(weaponImage);
            
        } catch (error) {
            console.error(`❌ ${this.skillName} 执行失败:`, error);
            this.handleError(error, 'execute');
        }
    }
    
    async createXuanBingJian(weaponImage) {
        console.log(`❄️⚔️ createXuanBingJian 开始，isEnemySkill: ${this.isEnemySkill}`);
        // 🔧 修复：动态判断位置映射
        // 根据技能使用者动态确定施法者和目标位置
        let casterPos, targetPos;
        
        if (this.isEnemySkill) {
            // 敌方技能：敌人是施法者，玩家是目标
            casterPos = this.getCharacterPosition(false); // 敌人位置
            targetPos = this.getCharacterPosition(true);  // 玩家位置
        } else {
            // 我方技能：玩家是施法者，敌人是目标
            casterPos = this.getCharacterPosition(true);  // 玩家位置
            targetPos = this.getCharacterPosition(false); // 敌人位置
        }
        
        console.log(`❄️⚔️ 玄冰剑位置计算 - 敌方技能:${this.isEnemySkill}, 施法者:`, casterPos, '目标:', targetPos);
        
        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('ice-skills', '❄️⚔️ 玄冰剑位置计算:', {
            起点: {x: casterPos.x, y: casterPos.y},
            终点: {x: targetPos.x, y: targetPos.y}
        });
        }
        
        // 创建技能动画容器
        const container = this.createElement('xuanbingjian-container', {
            style: {
                position: 'absolute',
                top: '0',
                left: '0',
                width: '100%',
                height: '100%',
                pointerEvents: 'none',
                zIndex: 1000
            }
        });
        
        // v2.0新增：容器管理
        this.animationContainers.add(container);
        this.effectsContainer.appendChild(container);
        
        try {
            // 第一阶段：冰锥术蓄力效果 + 中间3把剑叠加旋转（1.5秒）
            const rotatedSwords = await this.createIceChargeWithSwords(container, casterPos, weaponImage);
            
            // 第二阶段：3把剑旋转甩飞，使用万剑诀飞行逻辑（1.0秒）
            await this.createSwordAttack(container, rotatedSwords, casterPos, targetPos);
            
            // 第三阶段：万剑诀击中效果（1.0秒）
            await this.createSwordHitEffects(container, targetPos);
            
        } finally {
            // v2.0优化：安全清理机制
            this.safeCleanupContainer(container);
        }
    }
    
    // 第一阶段：冰锥术蓄力效果 + 中间3把剑叠加旋转
    async createIceChargeWithSwords(container, casterPos, weaponImage) {
        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('ice-skills', '❄️ 玄冰剑第一阶段：冰锥蓄力 + 3把剑旋转');
        }
        
        // === 创建冰锥术的蓄力特效 ===
        
        // 创建寒冰魔法阵
        const iceCircle = this.createElement('xuanbingjian-ice-circle', {
            style: {
                position: 'absolute',
                left: casterPos.x + 'px',
                top: casterPos.y + 'px',
                transform: 'translate(-50%, -50%)'
            }
        });
        container.appendChild(iceCircle);
        
        // 创建内圈冰霜符文
        const innerRunes = this.createElement('xuanbingjian-inner-runes', {
            style: {
                position: 'absolute',
                left: casterPos.x + 'px',
                top: casterPos.y + 'px',
                transform: 'translate(-50%, -50%)'
            }
        });
        container.appendChild(innerRunes);
        
        // 创建外圈寒气符文
        const outerRunes = this.createElement('xuanbingjian-outer-runes', {
            style: {
                position: 'absolute',
                left: casterPos.x + 'px',
                top: casterPos.y + 'px',
                transform: 'translate(-50%, -50%)'
            }
        });
        container.appendChild(outerRunes);
        
        // 创建蓄力能量核心
        const energyCore = this.createElement('xuanbingjian-energy-core', {
            style: {
                position: 'absolute',
                left: casterPos.x + 'px',
                top: casterPos.y + 'px',
                transform: 'translate(-50%, -50%)'
            }
        });
        container.appendChild(energyCore);
        
        // 创建冰霜粒子汇聚效果
        for (let i = 0; i < 25; i++) {
            const frostParticle = this.createElement('xuanbingjian-frost-particle', {
                style: {
                    position: 'absolute',
                    left: casterPos.x + 'px',
                    top: casterPos.y + 'px',
                    transform: 'translate(-50%, -50%)',
                    animationDelay: `${Math.random() * 1.0}s`
                }
            });
            
            const angle = Math.random() * Math.PI * 2;
            const radius = 15 + Math.random() * 35;
            const moveX = Math.cos(angle) * radius;
            const moveY = Math.sin(angle) * radius;
            
            frostParticle.style.setProperty('--chargeX', `${moveX}px`);
            frostParticle.style.setProperty('--chargeY', `${moveY}px`);
            
            container.appendChild(frostParticle);
        }
        
        // === 在最上层中间创建3把剑叠加旋转 ===
        const swords = [];
        const swordRadius = 0; // 叠加在一起，半径为0
        
        for (let i = 0; i < 3; i++) {
            const sword = this.createElement('xuanbingjian-rotating-sword', {
                style: {
                    position: 'absolute',
                    left: casterPos.x + 'px',
                    top: casterPos.y + 'px',
                    transform: 'translate(-50%, -50%)',
                    opacity: '0',
                    zIndex: '1001'
                }
            });
            
            // 添加武器图片
            if (weaponImage) {
                this.addWeaponImage(sword, weaponImage);
                // 🗡️ 动态调整武器图片角度
                const weaponImg = sword.querySelector('.weapon-image');
                if (weaponImg) {
                    weaponImg.style.transform = `rotate(${this.calculateInitialSwordRotation()}deg)`;
                }
            }
            
            // 设置CSS变量用于动画 - 120度间隔
            const angleOffset = i * 120; // 0度、120度、240度
            sword.style.setProperty('--swordIndex', i);
            sword.style.setProperty('--initialAngle', `${angleOffset}deg`); // 初始角度偏移
            sword.style.setProperty('--rotationSpeed', `${1.2 + i * 0.1}s`); // 不同的旋转速度
            sword.style.setProperty('--layerOpacity', `${0.8 - i * 0.1}`); // 不同的透明度
            sword.style.setProperty('--layerScale', `${1.0 - i * 0.05}`); // 不同的大小
            
            // 为每把剑添加光圈效果
            const aura = this.createElement('xuanbingjian-sword-aura', {
                style: {
                    position: 'absolute',
                    left: casterPos.x + 'px',
                    top: casterPos.y + 'px'
                }
            });
            aura.style.setProperty('--swordIndex', i);
            
            container.appendChild(aura);
            container.appendChild(sword);
            swords.push(sword);
            
            if (window.BattleDebugConfig) {
                window.BattleDebugConfig.log('ice-skills', `❄️ 创建第${i+1}把旋转剑，角度偏移: ${angleOffset}度`);
            }
        }
        
        // 等待蓄力完成
        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('ice-skills', '❄️ 等待1200毫秒蓄力完成...');
        }
        await this.wait(1200);
        
        // 清理蓄力特效，保留剑用于下一阶段
        this.safeRemoveElement(iceCircle);
        this.safeRemoveElement(innerRunes);
        this.safeRemoveElement(outerRunes);
        this.safeRemoveElement(energyCore);
        
        // 清理粒子
        container.querySelectorAll('.xuanbingjian-frost-particle').forEach(particle => {
            this.safeRemoveElement(particle);
        });
        
        // 清理旋转阶段的光圈
        container.querySelectorAll('.xuanbingjian-sword-aura').forEach(aura => {
            this.safeRemoveElement(aura);
        });
        
        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('ice-skills', `✅ 玄冰剑第一阶段完成，返回${swords.length}把剑`);
        }
        return swords;
    }
    
    // 第二阶段：3把剑旋转甩飞，使用万剑诀飞行逻辑
    async createSwordAttack(container, swords, casterPos, targetPos) {
        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('ice-skills', '⚔️ 玄冰剑第二阶段：3把剑甩飞攻击');
        }
        
        // 计算每把剑的甩飞位置和攻击路径
        swords.forEach((sword, index) => {
            // 计算甩飞的随机位置（角色周边，不要太远）
            const maxRadius = 80; // 最大距离80px
            const minRadius = 50; // 最小距离50px
            const radius = minRadius + Math.random() * (maxRadius - minRadius);
            const randomAngle = Math.random() * Math.PI * 2; // 随机角度
            
            const thrownX = casterPos.x + Math.cos(randomAngle) * radius;
            const thrownY = casterPos.y + Math.sin(randomAngle) * radius;
            
            // 计算从甩飞位置到敌人的攻击角度
            const deltaX = targetPos.x - thrownX;
            const deltaY = targetPos.y - thrownY;
            const attackAngle = Math.atan2(deltaY, deltaX);
            let angleDeg = (attackAngle * 180 / Math.PI) - 90; // 剑尖指向目标
        
        // 🔧 动态判断：角度计算已经能够正确处理双向攻击
        console.log(`❄️⚔️ 玄冰剑技能角度计算: ${angleDeg}度 (isEnemySkill: ${this.isEnemySkill})`);
        console.log(`❄️⚔️ 攻击向量: (${deltaX}, ${deltaY})`);
            
            // 计算穿透终点（敌人后方150px）
            const penetrateDistance = 150;
            const finalX = targetPos.x + Math.cos(attackAngle) * penetrateDistance;
            const finalY = targetPos.y + Math.sin(attackAngle) * penetrateDistance;
            
            // 设置攻击相关的CSS变量
            sword.style.setProperty('--initialX', `${casterPos.x}px`);
            sword.style.setProperty('--initialY', `${casterPos.y}px`);
            sword.style.setProperty('--thrownX', `${thrownX}px`); // 甩飞到的位置
            sword.style.setProperty('--thrownY', `${thrownY}px`);
            sword.style.setProperty('--targetX', `${targetPos.x}px`);
            sword.style.setProperty('--targetY', `${targetPos.y}px`);
            sword.style.setProperty('--finalX', `${finalX}px`); // 穿透终点
            sword.style.setProperty('--finalY', `${finalY}px`);
            sword.style.setProperty('--angle', `${angleDeg}deg`);
            
            // 三阶段动画：甩飞 → 停顿定向 → 直线攻击穿透
            const throwTime = 0.4; // 甩飞时间
            const pauseTime = 0.2; // 停顿定向时间
            const attackDelay = index * 0.12; // 每把剑间隔0.12秒
            const attackTime = 0.8; // 飞行攻击时间
            
            sword.style.animation = `
                xuanbingjian-sword-throw ${throwTime}s ease-out ${attackDelay}s forwards,
                xuanbingjian-sword-aim ${pauseTime}s ease-in-out ${attackDelay + throwTime}s forwards,
                xuanbingjian-sword-strike ${attackTime}s cubic-bezier(0.3, 0, 0.2, 1) ${attackDelay + throwTime + pauseTime}s forwards
            `;
            
            // 为每把剑创建冰刺轨迹
            this.createIceSpikesTrail(container, sword, thrownX, thrownY, targetPos.x, targetPos.y, finalX, finalY, index, attackDelay + throwTime + pauseTime, attackTime);
            
            // 为飞行过程创建跟随光圈
            this.createFollowingAura(container, sword, index, attackDelay, throwTime, pauseTime, attackTime);
            
            console.log(`⚔️ 第${index+1}把剑甩飞到(${Math.round(thrownX)}, ${Math.round(thrownY)})，延迟${attackDelay.toFixed(2)}s`);
        });
        
        // 在每把剑击中时创建受击特效
        swords.forEach((sword, index) => {
            const throwTime = 0.4;
            const pauseTime = 0.2;
            const attackDelay = index * 0.12;
            const attackTime = 0.8;
            // 击中时机是在攻击动画60%时
            const hitTime = (attackDelay + throwTime + pauseTime + attackTime * 0.6) * 1000;
            
            const timer = setTimeout(() => {
                // 使用万剑诀相同的受击特效方法
                if (this.battleSystem && this.battleSystem.createSwordHitEffect) {
                    this.battleSystem.createSwordHitEffect(targetPos.x, targetPos.y, true);
                } else {
                    this.createHitEffect(targetPos.x, targetPos.y, true);
                }
                console.log(`⚔️ 第${index+1}把剑击中目标`);
                this.activeTimers.delete(timer);
            }, hitTime);
            
            this.activeTimers.add(timer);
        });
        
        // 等待所有剑完成攻击（最后一把剑的总时长）
        const maxDelay = (swords.length - 1) * 0.12; // 最大延迟
        const totalTime = maxDelay + 0.4 + 0.2 + 0.8; // 延迟 + 甩飞 + 停顿 + 攻击
        console.log(`⚔️ 等待${Math.round(totalTime * 1000)}毫秒攻击完成...`);
        await this.wait(Math.round(totalTime * 1000));
        
        // 清理剑
        swords.forEach(sword => this.safeRemoveElement(sword));
        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('ice-skills', '✅ 玄冰剑第二阶段完成');
        }
    }
    
    // 创建冰刺轨迹跟随
    createIceSpikesTrail(container, sword, startX, startY, targetX, targetY, finalX, finalY, swordIndex, startDelay, duration) {
        const spikeCount = 3 + Math.floor(Math.random() * 2); // 3-4根冰刺
        console.log(`❄️ 为第${swordIndex+1}把剑创建${spikeCount}根冰刺轨迹`);
        
        for (let i = 0; i < spikeCount; i++) {
            const spike = this.createElement('xuanbingjian-ice-spike-trail', {
                style: {
                    position: 'absolute',
                    left: startX + 'px',
                    top: startY + 'px',
                    transform: 'translate(-50%, -50%)',
                    opacity: '0',
                    zIndex: '999' // 在剑后面
                }
            });
            
            // 计算冰刺的延迟和间隔
            const spikeDelay = startDelay + (i * 0.1); // 每根冰刺间隔0.1秒
            const spikeLag = 15 + (i * 8); // 冰刺落后距离，越后面越远
            
            // 设置冰刺的CSS变量
            spike.style.setProperty('--startX', `${startX}px`);
            spike.style.setProperty('--startY', `${startY}px`);
            spike.style.setProperty('--targetX', `${targetX}px`);
            spike.style.setProperty('--targetY', `${targetY}px`);
            spike.style.setProperty('--finalX', `${finalX}px`);
            spike.style.setProperty('--finalY', `${finalY}px`);
            spike.style.setProperty('--lagDistance', `${spikeLag}px`);
            spike.style.setProperty('--spikeIndex', i);
            spike.style.setProperty('--opacity', `${0.8 - i * 0.1}`); // 越后面越透明
            
            // 冰刺跟随动画
            spike.style.animation = `
                xuanbingjian-spike-trail ${duration}s cubic-bezier(0.3, 0, 0.2, 1) ${spikeDelay}s forwards
            `;
            
            container.appendChild(spike);
            
            // 冰刺消失延迟清理
            const cleanupTimer = setTimeout(() => {
                this.safeRemoveElement(spike);
                this.activeTimers.delete(cleanupTimer);
            }, (spikeDelay + duration + 0.5) * 1000);
            
            this.activeTimers.add(cleanupTimer);
        }
    }
    
    // 创建跟随光圈效果
    createFollowingAura(container, sword, swordIndex, attackDelay, throwTime, pauseTime, attackTime) {
        const aura = this.createElement('xuanbingjian-flying-aura', {
            style: {
                position: 'absolute',
                left: sword.style.getPropertyValue('--initialX') || '0px',
                top: sword.style.getPropertyValue('--initialY') || '0px',
                width: '60px',
                height: '60px',
                borderRadius: '50%',
                background: `radial-gradient(circle, 
                    rgba(173, 216, 230, 0.4) 0%, 
                    rgba(135, 206, 235, 0.3) 40%, 
                    rgba(255, 255, 255, 0.2) 70%, 
                    transparent 100%)`,
                transform: 'translate(-50%, -50%)',
                opacity: '0',
                zIndex: '998', // 在剑后面
                pointerEvents: 'none'
            }
        });
        
        // 设置相同的飞行路径变量
        aura.style.setProperty('--initialX', sword.style.getPropertyValue('--initialX'));
        aura.style.setProperty('--initialY', sword.style.getPropertyValue('--initialY'));
        aura.style.setProperty('--thrownX', sword.style.getPropertyValue('--thrownX'));
        aura.style.setProperty('--thrownY', sword.style.getPropertyValue('--thrownY'));
        aura.style.setProperty('--targetX', sword.style.getPropertyValue('--targetX'));
        aura.style.setProperty('--targetY', sword.style.getPropertyValue('--targetY'));
        aura.style.setProperty('--finalX', sword.style.getPropertyValue('--finalX'));
        aura.style.setProperty('--finalY', sword.style.getPropertyValue('--finalY'));
        
        // 光圈跟随剑的动画，稍微延迟一点
        const auraDelay = attackDelay + 0.05; // 光圈稍微延迟
        aura.style.animation = `
            xuanbingjian-aura-throw ${throwTime}s ease-out ${auraDelay}s forwards,
            xuanbingjian-aura-aim ${pauseTime}s ease-in-out ${auraDelay + throwTime}s forwards,
            xuanbingjian-aura-strike ${attackTime}s cubic-bezier(0.3, 0, 0.2, 1) ${auraDelay + throwTime + pauseTime}s forwards
        `;
        
        container.appendChild(aura);
        
        // 光圈消失清理
        const cleanupTimer = setTimeout(() => {
            this.safeRemoveElement(aura);
            this.activeTimers.delete(cleanupTimer);
        }, (auraDelay + throwTime + pauseTime + attackTime + 0.5) * 1000);
        
        this.activeTimers.add(cleanupTimer);
        
        console.log(`✨ 为第${swordIndex+1}把剑创建跟随光圈`);
    }
    
    // 第三阶段：万剑诀击中效果
    async createSwordHitEffects(container, targetPos) {
        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('ice-skills', '💥 玄冰剑第三阶段：万剑诀击中效果');
        }
        
        // 创建冰剑爆炸核心
        const iceExplosion = this.createElement('xuanbingjian-explosion-core', {
            style: {
                position: 'absolute',
                left: targetPos.x + 'px',
                top: targetPos.y + 'px',
                transform: 'translate(-50%, -50%)'
            }
        });
        container.appendChild(iceExplosion);
        
        // 创建冰霜冲击波
        for (let i = 0; i < 3; i++) {
            const shockwave = this.createElement('xuanbingjian-ice-shockwave', {
                style: {
                    position: 'absolute',
                    left: targetPos.x + 'px',
                    top: targetPos.y + 'px',
                    transform: 'translate(-50%, -50%)',
                    animationDelay: `${i * 0.1}s`
                }
            });
            container.appendChild(shockwave);
        }
        
        // 创建冰晶碎片爆发
        for (let i = 0; i < 20; i++) {
            const iceShard = this.createElement('xuanbingjian-ice-shard', {
                style: {
                    position: 'absolute',
                    left: targetPos.x + 'px',
                    top: targetPos.y + 'px',
                    transform: 'translate(-50%, -50%)',
                    animationDelay: `${i * 0.03}s`
                }
            });
            
            const angle = (i / 20) * Math.PI * 2;
            const distance = 30 + Math.random() * 50;
            const velocity = 0.8 + Math.random() * 0.6;
            
            iceShard.style.setProperty('--shardAngle', `${angle * 180 / Math.PI}deg`);
            iceShard.style.setProperty('--shardDistance', `${distance}px`);
            iceShard.style.setProperty('--shardVelocity', velocity);
            
            container.appendChild(iceShard);
        }
        
        // 创建敌人受击动画
        this.createXuanBingJianEnemyHit();
        
        console.log(`💥 等待1000毫秒击中效果完成...`);
        await this.wait(1000);
        
        if (window.BattleDebugConfig) {
            window.BattleDebugConfig.log('ice-skills', '✅ 玄冰剑第三阶段完成');
        }
    }
    
    // 创建敌人受击动画
    // 🗡️ 动态计算剑的初始旋转角度
    calculateInitialSwordRotation() {
        // 敌方技能：剑尖向下（指向玩家），我方技能：剑尖向上（指向敌人）
        return this.isEnemySkill ? 180 : 0;
    }

    createXuanBingJianEnemyHit() {
        // 🔧 修复：动态判断被攻击目标的选择器
        let targetSelectors = [];
        if (!this.isEnemySkill) {
            // 我方技能攻击敌人
            targetSelectors = ['.enemy .character-sprite', '.enemy img', '.enemy'];
        } else {
            // 敌方技能攻击玩家
            targetSelectors = ['.player .character-sprite', '.player img', '.player'];
        }
        
        let target = null;
        
        for (const selector of targetSelectors) {
            target = document.querySelector(selector);
            if (target) break;
        }
        
        if (target) {
            // 冰霜受击效果（蓝色发光 + 震动）
            const originalAnimation = target.style.animation;
            target.style.animation = 'xuanbingjian-enemy-ice-hit 1.5s ease-out, xuanbingjian-enemy-freeze-shake 0.15s ease-in-out 8';
            
            // 1.5秒后恢复原始动画
            const timer = setTimeout(() => {
                target.style.animation = originalAnimation;
                this.activeTimers.delete(timer);
            }, 1500);
            
            this.activeTimers.add(timer);
            console.log(`❄️ ${!this.isEnemySkill ? '敌人' : '玩家'}冰霜受击动画已应用`);
        } else {
            console.warn(`⚠️ 未找到目标元素，无法应用受击动画`);
        }
    }

    // v2.0新增：安全清理方法
    safeRemoveElement(element) {
        if (element && element.parentNode) {
            try {
                element.parentNode.removeChild(element);
            } catch (error) {
                console.warn(`⚠️ 元素移除失败:`, error);
            }
        }
    }

    safeCleanupContainer(container) {
        if (container) {
            this.animationContainers.delete(container);
            
            // 延迟清理，确保动画完成
            const timer = setTimeout(() => {
                this.safeRemoveElement(container);
                this.activeTimers.delete(timer);
            }, 100);
            
            this.activeTimers.add(timer);
        }
    }

    // v2.0新增：技能实例清理方法
    cleanup() {
        // 清理所有容器
        this.animationContainers.forEach(container => {
            this.safeRemoveElement(container);
        });
        this.animationContainers.clear();

        // 清理所有定时器
        this.activeTimers.forEach(timer => {
            clearTimeout(timer);
        });
        this.activeTimers.clear();

        console.log(`✅ ${this.skillName} 实例已清理`);
    }

    // v2.0新增：错误处理
    handleError(error, context) {
        const errorInfo = {
            skill: this.skillName,
            context: context,
            error: error.message,
            timestamp: Date.now()
        };

        // 上报错误到调试面板
        if (window.BattleDebugPanel) {
            window.BattleDebugPanel.addLog('error', `${this.skillName} ${context} 失败`, errorInfo);
        }
    }
}

// 导出技能类（必须按此格式）
window.IceSkills = window.IceSkills || {};
window.IceSkills.BingZhuiShuSkill = BingZhuiShuSkill;
window.IceSkills.XuanBingJianSkill = XuanBingJianSkill; 