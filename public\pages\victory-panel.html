<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        /* 胜利面板样式 - 与JS中createSimpleVictoryPanel保持一致 */
        .game-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .victory-panel {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.8));
            border-radius: 15px;
            padding: 16px;
            width: 90%;
            max-width: 320px;
            border: 2px solid rgba(255, 215, 0, 0.4);
            backdrop-filter: blur(10px);
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.5);
            color: white;
            text-align: center;
        }

        .victory-title {
            font-size: 20px;
            color: #ffd700;
            margin-bottom: 6px;
            text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
        }

        .victory-subtitle {
            font-size: 13px;
            margin-bottom: 12px;
            opacity: 0.9;
        }

        /* 奖励区域样式 - 🔧 缩小间距 */
        .rewards-section {
            margin: 12px 0;
            background: rgba(0, 0, 0, 0.4);
            border-radius: 10px;
            border: 1px solid rgba(255, 215, 0, 0.3);
            padding: 10px;
            animation: rewardsFadeIn 0.6s ease-out;
        }

        @keyframes rewardsFadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .rewards-title {
            font-size: 14px;
            color: #ffd700;
            margin-bottom: 8px;
            font-weight: bold;
            text-shadow: 0 0 8px rgba(255, 215, 0, 0.6);
            animation: titleGlow 2s ease-in-out infinite alternate;
        }

        @keyframes titleGlow {
            from {
                text-shadow: 0 0 8px rgba(255, 215, 0, 0.6);
            }
            to {
                text-shadow: 0 0 12px rgba(255, 215, 0, 0.8), 0 0 20px rgba(255, 215, 0, 0.4);
            }
        }

        .rewards-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 8px;
            align-items: stretch;
        }

        .reward-item {
            border-radius: 8px;
            padding: 12px;
            border: 2px solid rgba(138, 43, 226, 0.4);
            transition: all 0.3s ease;
            animation: rewardItemSlide 0.8s ease-out;
            position: relative;
            overflow: hidden;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .reward-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s ease;
        }

        .reward-item:hover::before {
            left: 100%;
        }

        @keyframes rewardItemSlide {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .reward-item:nth-child(1) {
            background: linear-gradient(135deg, rgba(138, 43, 226, 0.3), rgba(138, 43, 226, 0.1));
            border-color: rgba(138, 43, 226, 0.6);
            animation-delay: 0.2s;
        }

        .reward-item:nth-child(2) {
            background: linear-gradient(135deg, rgba(255, 215, 0, 0.3), rgba(255, 215, 0, 0.1));
            border-color: rgba(255, 215, 0, 0.6);
            animation-delay: 0.4s;
        }

        .reward-item:nth-child(3) {
            background: linear-gradient(135deg, rgba(255, 165, 0, 0.3), rgba(255, 165, 0, 0.1));
            border-color: rgba(255, 165, 0, 0.6);
            animation-delay: 0.6s;
        }

        .reward-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .reward-info {
            text-align: center;
            position: relative;
            z-index: 1;
            width: 100%;
        }

        .reward-label {
            color: #d4af37;
            font-size: 12px;
            margin-bottom: 6px;
            font-weight: bold;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        }

        .reward-value {
            color: #ffd700;
            font-size: 20px;
            font-weight: bold;
            text-shadow: 0 0 8px rgba(255, 215, 0, 0.8);
            animation: valueCountUp 1s ease-out;
        }

        @keyframes valueCountUp {
            from {
                opacity: 0;
                transform: scale(0.5);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        /* 🔧 新增：回收警告动画 */
        @keyframes warningPulse {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.8;
                transform: scale(1.02);
            }
        }

        .recycle-warning {
            animation: warningPulse 2s ease-in-out infinite;
        }

        /* 🆕 循环挂机模式提示动画 */
        @keyframes hintGlow {
            from {
                text-shadow: 0 0 4px rgba(255, 165, 0, 0.6);
                opacity: 0.8;
            }
            to {
                text-shadow: 0 0 8px rgba(255, 165, 0, 0.9), 0 0 12px rgba(255, 165, 0, 0.5);
                opacity: 1;
            }
        }

        /* 🔧 重写：掉落物品区域样式 - 完全参考背包物品 */
        .drops-section {
            margin: 12px 0;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 10px;
            max-height: 180px;
            overflow-y: auto;
        }

        .drops-title {
            font-size: 14px;
            color: #ffd700;
            margin-bottom: 8px;
            font-weight: bold;
            text-shadow: 0 0 8px rgba(255, 215, 0, 0.4);
        }

        .drops-list {
            display: flex;
            gap: 8px;
            justify-content: center;
            flex-wrap: wrap;
            max-width: 100%;
        }

        /* 🔧 新样式：完全参考背包物品 */
        .drop-item {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, rgba(44, 62, 80, 0.9), rgba(52, 73, 94, 0.7));
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 15px;
            cursor: pointer;
            position: relative;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            backdrop-filter: blur(10px);
            box-shadow: 
                0 4px 8px rgba(0, 0, 0, 0.3),
                0 0 8px rgba(255, 255, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            overflow: hidden;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .drop-item:hover {
            transform: translateY(-4px) scale(1.05);
            box-shadow: 
                0 8px 16px rgba(0, 0, 0, 0.4),
                0 0 15px rgba(255, 255, 255, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .drop-item:active {
            transform: translateY(-2px) scale(1.02);
        }

        /* 🔧 品质颜色样式 - 完全复制背包物品的品质系统 */
        .drop-item.rarity-common { 
            border-color: #95a5a6 !important;
            box-shadow: 
                0 4px 8px rgba(0, 0, 0, 0.3),
                0 0 8px rgba(149, 165, 166, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
        }

        .drop-item.rarity-uncommon { 
            border-color: #27ae60 !important;
            box-shadow: 
                0 4px 8px rgba(0, 0, 0, 0.3),
                0 0 12px rgba(39, 174, 96, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
        }

        .drop-item.rarity-rare { 
            border-color: #3498db !important;
            box-shadow: 
                0 4px 8px rgba(0, 0, 0, 0.3),
                0 0 15px rgba(52, 152, 219, 0.5),
                inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
        }

        .drop-item.rarity-epic { 
            border-color: #9b59b6 !important;
            box-shadow: 
                0 4px 8px rgba(0, 0, 0, 0.3),
                0 0 18px rgba(155, 89, 182, 0.6),
                inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
        }

        .drop-item.rarity-legendary { 
            border-color: #d4af37 !important;
            box-shadow: 
                0 4px 8px rgba(0, 0, 0, 0.3),
                0 0 20px rgba(212, 175, 55, 0.7),
                inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
        }
        
        /* 🔧 新增：物品名称区域样式（完全复制背包物品样式） */
        .drop-item .item-image {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 60%;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            z-index: 1;
        }

        .drop-item .item-name-box {
            position: absolute;
            bottom: 1px;
            left: 1px;
            right: 1px;
            height: 40%;
            background: linear-gradient(to top, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.7), transparent);
            display: flex;
            align-items: flex-end;
            justify-content: center;
            z-index: 2;
            border-radius: 0 0 10px 10px;
        }

        .drop-item .item-name-text {
            color: #ecf0f1;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
            line-height: 1.1;
            text-align: center;
            word-break: break-all;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            line-clamp: 2;
            max-height: 14px;
            font-size: 8px;
            padding: 2px;
        }

        /* 🔧 新增：物品数量标签样式 */
        .drop-item .item-quantity {
            position: absolute;
            top: -2px;
            right: -2px;
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            font-size: 7px;
            padding: 2px 4px;
            border-radius: 8px;
            min-width: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
            border: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 3;
        }
        
        /* 🔧 品质悬停效果 */
        .drop-item.rarity-common:hover {
            border-color: #bdc3c7 !important;
            box-shadow: 
                0 6px 12px rgba(0, 0, 0, 0.4),
                0 0 15px rgba(149, 165, 166, 0.5),
                inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
            transform: translateY(-4px) scale(1.05);
        }
        
        .drop-item.rarity-uncommon:hover {
            border-color: #2ecc71 !important;
            box-shadow: 
                0 6px 12px rgba(0, 0, 0, 0.4),
                0 0 20px rgba(39, 174, 96, 0.6),
                inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
            transform: translateY(-4px) scale(1.05);
        }
        
        .drop-item.rarity-rare:hover {
            border-color: #5dade2 !important;
            box-shadow: 
                0 6px 12px rgba(0, 0, 0, 0.4),
                0 0 25px rgba(52, 152, 219, 0.7),
                inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
            transform: translateY(-4px) scale(1.05);
        }
        
        .drop-item.rarity-epic:hover {
            border-color: #bb8fce !important;
            box-shadow: 
                0 6px 12px rgba(0, 0, 0, 0.4),
                0 0 30px rgba(155, 89, 182, 0.8),
                inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
            transform: translateY(-4px) scale(1.05);
        }
        
        .drop-item.rarity-legendary:hover {
            border-color: #f4d03f !important;
            box-shadow: 
                0 6px 12px rgba(0, 0, 0, 0.4),
                0 0 35px rgba(212, 175, 55, 0.9),
                inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
            transform: translateY(-4px) scale(1.05);
        }

        /* 🆕 回收物品样式 */
        .drop-item.item-recycled {
            filter: grayscale(50%) brightness(0.7) !important;
            opacity: 0.8 !important;
            cursor: default !important;
            position: relative;
        }

        .drop-item.item-recycled:hover {
            transform: none !important; /* 禁用悬停动画 */
            box-shadow: 
                0 4px 8px rgba(0, 0, 0, 0.3),
                0 0 8px rgba(255, 255, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.1) !important; /* 保持原始阴影 */
        }

        /* 🆕 回收标签样式 - 模仿装备系统的标签风格 */
        .recycled-badge {
            position: absolute !important;
            top: -2px !important;
            left: -2px !important;
            background: linear-gradient(135deg, #e74c3c, #c0392b) !important;
            color: white !important;
            font-size: 6px !important;
            padding: 2px 4px !important;
            border-radius: 6px !important;
            font-weight: bold !important;
            z-index: 10 !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
            border: 1px solid rgba(255, 255, 255, 0.1) !important;
            text-shadow: 0 0 2px rgba(0, 0, 0, 0.8) !important;
            animation: recycledPulse 2s ease-in-out infinite;
        }
        
        /* 🆕 已出售标签样式 - 与已装备标签保持一致的风格 */
        .sold-badge {
            position: absolute !important;
            top: -2px !important;
            left: -2px !important;
            background: linear-gradient(135deg, #e67e22, #d35400) !important;
            color: white !important;
            font-size: 6px !important;
            padding: 2px 4px !important;
            border-radius: 6px !important;
            font-weight: bold !important;
            z-index: 10 !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
            border: 1px solid rgba(255, 255, 255, 0.1) !important;
            text-shadow: 0 0 2px rgba(0, 0, 0, 0.8) !important;
        }

        /* 🆕 回收标签脉冲动画 */
        @keyframes recycledPulse {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.8;
                transform: scale(1.05);
            }
        }

        /* 🆕 移动端回收标签适配 */
        @media (max-width: 480px) {
            .recycled-badge {
                font-size: 6px !important;
                padding: 1px 3px !important;
                top: 1px !important;
                left: 1px !important;
            }
        }
        


        /* 按钮样式 - 🔧 确保4个按钮在同一行，大小一致 */
        .victory-buttons {
            margin-top: 15px;
            display: flex;
            gap: 6px;
            justify-content: center;
            flex-wrap: nowrap; /* 禁止换行 */
            max-width: 400px;
            margin-left: auto;
            margin-right: auto;
        }

        .victory-button {
            padding: 6px 8px;
            border: 2px solid rgba(212, 175, 55, 0.6);
            border-radius: 6px;
            font-size: 10px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            min-height: 32px;
            flex: 1; /* 每个按钮占相等空间 */
            max-width: 80px; /* 限制最大宽度 */
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            line-height: 1.1;
            position: relative;
            overflow: hidden;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(10px);
            box-shadow: 
                0 2px 4px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        /* 🎯 退出战斗按钮 - 深红警示 */
        .exit-battle-button {
            background: linear-gradient(135deg, rgba(139, 0, 0, 0.8), rgba(220, 20, 60, 0.6));
            color: white;
            border-color: rgba(220, 20, 60, 0.8);
        }

        .exit-battle-button:hover:not(:disabled) {
            background: linear-gradient(135deg, rgba(220, 20, 60, 0.9), rgba(240, 128, 128, 0.7));
            border-color: rgba(240, 128, 128, 1);
            transform: translateY(-2px);
            box-shadow: 
                0 6px 12px rgba(0, 0, 0, 0.4),
                0 0 15px rgba(220, 20, 60, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        /* 🎯 挂机按钮 - 蓝色仙气 */
        .auto-battle-button {
            background: linear-gradient(135deg, rgba(65, 105, 225, 0.8), rgba(100, 149, 237, 0.6));
            color: white;
            border-color: rgba(100, 149, 237, 0.8);
        }

        .auto-battle-button:hover:not(:disabled) {
            background: linear-gradient(135deg, rgba(100, 149, 237, 0.9), rgba(135, 206, 235, 0.7));
            border-color: rgba(135, 206, 235, 1);
            transform: translateY(-2px);
            box-shadow: 
                0 6px 12px rgba(0, 0, 0, 0.4),
                0 0 15px rgba(100, 149, 237, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        /* 停止挂机模式 */
        .auto-battle-button.stop-mode {
            background: linear-gradient(135deg, rgba(255, 99, 71, 0.8), rgba(255, 127, 80, 0.6));
            border-color: rgba(255, 127, 80, 0.8);
        }

        .auto-battle-button.stop-mode:hover:not(:disabled) {
            background: linear-gradient(135deg, rgba(255, 127, 80, 0.9), rgba(255, 160, 122, 0.7));
            border-color: rgba(255, 160, 122, 1);
            transform: translateY(-2px);
            box-shadow: 
                0 6px 12px rgba(0, 0, 0, 0.4),
                0 0 15px rgba(255, 127, 80, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        /* 🆕 挂机暂停模式（战斗失败后） */
        .auto-battle-button.paused-mode {
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.8), rgba(255, 235, 59, 0.6));
            border-color: rgba(255, 235, 59, 0.8);
            animation: pausedPulse 2s ease-in-out infinite;
        }

        .auto-battle-button.paused-mode:hover:not(:disabled) {
            background: linear-gradient(135deg, rgba(255, 235, 59, 0.9), rgba(255, 241, 118, 0.7));
            border-color: rgba(255, 241, 118, 1);
            transform: translateY(-2px);
            box-shadow: 
                0 6px 12px rgba(0, 0, 0, 0.4),
                0 0 15px rgba(255, 235, 59, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        /* 暂停模式脉冲动画 */
        @keyframes pausedPulse {
            0%, 100% {
                box-shadow: 0 0 10px rgba(255, 193, 7, 0.5);
            }
            50% {
                box-shadow: 0 0 20px rgba(255, 193, 7, 0.8);
            }
        }

        /* 通用悬停和点击效果 */
        .victory-button:hover:not(:disabled) {
            transform: translateY(-2px);
        }

        .victory-button:active:not(:disabled) {
            transform: translateY(0) scale(0.98);
            transition: all 0.1s ease;
        }

        /* 挂机倒计时样式 - 🔧 缩小尺寸，简化文字 */
        .auto-battle-countdown {
            font-size: 14px;
            color: #FFE4B5;
            margin-top: 2px;
            min-height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-shadow: 0 0 8px rgba(255, 228, 181, 0.8);
            font-weight: bold;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 4px;
            padding: 2px 4px;
        }

        /* 倒计时文本样式 - 🔧 简化为数字 */
        .countdown-text {
            color: #FFE4B5;
            font-size: 14px;
            font-weight: bold;
            text-shadow: 0 0 8px rgba(255, 228, 181, 0.8);
        }

        /* 按钮图标和文字样式 */
        .victory-button span {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 1px;
            font-weight: bold;
        }

        /* 🔧 统一的按钮图标样式 - 统一大小 */
        .exit-battle-button span::before {
            content: '⚔️';
            font-size: 14px;
            margin-bottom: 1px;
        }

        .auto-battle-button:not(.stop-mode) span::before {
            content: '🤖';
            font-size: 14px;
            margin-bottom: 1px;
        }

        /* 🔧 停止挂机按钮 - 只显示停止图标，不显示文字 */
        .auto-battle-button.stop-mode span::before {
            content: '';
            font-size: 14px;
            margin-bottom: 1px;
        }

        /* 🆕 暂停挂机按钮 - 显示重置图标 */
        .auto-battle-button.paused-mode span::before {
            content: '🔄';
            font-size: 14px;
            margin-bottom: 1px;
        }

        /* 🔧 倒计时状态下隐藏图标，避免重复显示 */
        .auto-battle-button:has(.auto-battle-countdown:not(:empty)) span::before {
            display: none;
        }

        /* 通过CSS变量控制图标显示 */
        .auto-battle-button span[style*="--hide-icon: true"]::before {
            display: none;
        }

        /* 禁用状态处理 */
        .victory-button:disabled span::before {
            opacity: 0.5;
            filter: grayscale(100%);
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .victory-panel {
                padding: 12px;
                max-width: 90vw;
            }
            
            .rewards-grid {
                gap: 6px;
            }
            
            .reward-item {
                min-height: 55px;
                padding: 10px;
            }
            
            .reward-label {
                font-size: 11px;
            }
            
            .reward-value {
                font-size: 18px;
            }
            
            .victory-buttons {
                gap: 5px;
                max-width: 350px;
            }
            
            .victory-button {
                font-size: 9px;
                padding: 5px 6px;
                min-height: 30px;
                flex: 1;
                max-width: 75px;
            }

            .victory-button span::before {
                font-size: 12px;
            }

            .auto-battle-countdown {
                font-size: 12px;
                min-height: 16px;
            }
            
            .drops-list {
                gap: 6px;
            }
        }

        @media (max-width: 360px) {
            .rewards-grid {
                gap: 4px;
            }
            
            .reward-item {
                min-height: 50px;
                padding: 8px;
            }
            
            .reward-label {
                font-size: 10px;
            }
            
            .reward-value {
                font-size: 16px;
            }
            
            .victory-buttons {
                gap: 4px;
                max-width: 320px;
            }
            
            .victory-button {
                font-size: 8px;
                padding: 4px 5px;
                min-height: 28px;
                max-width: 70px;
            }

            .victory-button span::before {
                font-size: 12px;
            }

            .auto-battle-countdown {
                font-size: 11px;
                min-height: 14px;
            }
        }

        @media (max-width: 320px) {
            .victory-panel {
                padding: 10px;
                max-width: 95vw;
            }
            
            .rewards-grid {
                gap: 3px;
            }
            
            .reward-item {
                min-height: 45px;
                padding: 6px;
            }
            
            .reward-label {
                font-size: 9px;
            }
            
            .reward-value {
                font-size: 14px;
            }

            .victory-buttons {
                gap: 3px;
                max-width: 300px;
                flex-wrap: wrap;
            }
            
            .victory-button {
                font-size: 8px;
                padding: 4px 4px;
                min-height: 26px;
                max-width: 65px;
            }

            .victory-button span::before {
                font-size: 12px;
            }

            .auto-battle-countdown {
                font-size: 10px;
                min-height: 12px;
            }
        }

        /* 🎯 下一层按钮 - 翡翠绿色 */
        .next-stage-button {
            background: linear-gradient(135deg, rgba(34, 139, 34, 0.8), rgba(50, 205, 50, 0.6));
            color: white;
            border-color: rgba(50, 205, 50, 0.8);
        }

        .next-stage-button:hover:not(:disabled) {
            background: linear-gradient(135deg, rgba(50, 205, 50, 0.9), rgba(144, 238, 144, 0.7));
            border-color: rgba(144, 238, 144, 1);
            transform: translateY(-2px);
            box-shadow: 
                0 6px 12px rgba(0, 0, 0, 0.4),
                0 0 15px rgba(50, 205, 50, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            }

        .next-stage-button:disabled {
            background: linear-gradient(135deg, rgba(105, 105, 105, 0.6), rgba(128, 128, 128, 0.4));
            color: #D3D3D3;
            border-color: rgba(128, 128, 128, 0.6);
            opacity: 0.6;
            cursor: not-allowed;
            box-shadow: 
                0 2px 6px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(211, 211, 211, 0.1);
        }

        /* 🔧 新增：最大层数禁用状态样式 */
        .next-stage-button.max-stage-disabled {
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.6), rgba(255, 159, 0, 0.4));
            color: #fff;
            border-color: rgba(255, 193, 7, 0.8);
            opacity: 0.8;
            cursor: not-allowed;
            box-shadow: 
                0 2px 6px rgba(0, 0, 0, 0.2),
                0 0 8px rgba(255, 193, 7, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        .next-stage-button.max-stage-disabled span::before {
            content: '🏆'; /* 修改图标为奖杯表示已通关 */
        }

        .next-stage-button span::before {
            content: '➡️';
            font-size: 14px;
            margin-bottom: 1px;
        }
    </style>
</head>
<body>
    <!-- 胜利面板模板 -->
    <template id="victory-panel-template">
        <div class="game-overlay">
            <div class="victory-panel">
                <div class="victory-title" data-victory-title>🎉 胜利！</div>
                <div class="victory-subtitle" data-victory-subtitle>战斗胜利！</div>
                
                <!-- 奖励显示区域 -->
                <div class="rewards-section" data-rewards-section>
                    <div class="rewards-title">🎉 战斗奖励</div>

                    <div class="rewards-grid">
                        <div class="reward-item">
                            <div class="reward-info">
                                <div class="reward-label">🔹 灵石</div>
                                <div class="reward-value" data-exp-reward>+0</div>
                            </div>
                        </div>
                        <div class="reward-item">
                            <div class="reward-info">
                                <div class="reward-label">💰 金币</div>
                                <div class="reward-value" data-gold-reward>+0</div>
                            </div>
                        </div>
                        <div class="reward-item">
                            <div class="reward-info">
                                <div class="reward-label">🌟 奇遇值</div>
                                <div class="reward-value" data-adventure-reward>+0</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 掉落物品区域 -->
                <div class="drops-section" data-drops-section>
                    <div class="drops-title" data-drops-title>💎 获得战利品</div>
                    <div class="drops-list" data-drops-list>
                        <!-- 掉落物品将在这里动态生成 -->
                    </div>
                </div>
                                    
                <!-- 🔧 新增：回收警告区域 -->
                <div class="recycle-warning" data-recycle-warning style="
                    display: none;
                    background: rgba(220, 20, 60, 0.2);
                    border: 1px solid rgba(220, 20, 60, 0.5);
                    border-radius: 6px;
                    padding: 8px;
                    margin-bottom: 10px;
                    text-align: center;
                    color: #ff6b6b;
                    font-size: 12px;
                    font-weight: bold;
                    text-shadow: 0 0 4px rgba(220, 20, 60, 0.8);
                    animation: warningPulse 2s ease-in-out infinite;
                ">
                    ⚠️ 背包空间不足，已自动回收装备获得金币
                </div>
                                    
                <!-- 按钮区域 - 🔧 新布局：退出战斗、挂机、下一层 -->
                <div class="victory-buttons">
                    <button class="victory-button exit-battle-button" data-exit-battle-button>
                        <span>退出战斗</span>
                    </button>
                    <button class="victory-button auto-battle-button" data-auto-battle-button>
                        <span data-auto-battle-text>开始挂机</span>
                        <div class="auto-battle-countdown" data-auto-battle-countdown></div>
                    </button>
                    <button class="victory-button next-stage-button" data-next-stage-button>
                        <span>下一层</span>
                    </button>
                </div>
                
                <!-- 🆕 循环挂机模式提示 - 独立一行 -->
                <div class="auto-battle-mode-hint" data-auto-battle-mode-hint style="
                    display: none;
                    font-size: 11px;
                    color: #ffa500;
                    text-align: center;
                    margin-top: 8px;
                    text-shadow: 0 0 4px rgba(255, 165, 0, 0.6);
                    animation: hintGlow 2s ease-in-out infinite alternate;
                ">（循环挂机模式开启）</div>
            </div>
        </div>
    </template>

    <!-- 🔧 已删除：不再使用掉落物品模板，直接通过JS创建（与背包物品一致） -->
</body>
</html> 