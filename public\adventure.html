<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <!-- 移动端适配核心meta标签 -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover">
    
    <!-- 🔧 PWA模式底部白边强力修复 - 必须最早执行 -->
    <script src="assets/js/pwa-fix.js"></script>
    
    <!-- 🎛️ 全局调试开关 - 必须最早加载 -->
    <script src="assets/js/global-debug-switch.js"></script>

    <!-- 🔧 项目配置文件 - 必须早期加载 -->
    <script src="assets/js/config.js"></script>

    <!-- WebView优化配置 -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="screen-orientation" content="portrait">
    <meta name="x5-orientation" content="portrait">
    <meta name="full-screen" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta name="browsermode" content="application">
    <meta name="x5-page-mode" content="app">
    <!-- 禁用长按菜单 -->
    <meta name="format-detection" content="telephone=no,email=no,address=no">
    <!-- 强制使用最新版本 -->
    <meta http-equiv="Cache-Control" content="no-siteapp">
    <meta http-equiv="Cache-Control" content="no-transform">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <!-- 启用硬件加速 -->
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <!-- iOS图标 -->
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/images/app-icon-180.png">
    <link rel="apple-touch-icon" sizes="167x167" href="/assets/images/app-icon-167.png">
    <link rel="apple-touch-icon" sizes="152x152" href="/assets/images/app-icon-152.png">
    <link rel="apple-touch-icon" sizes="120x120" href="/assets/images/app-icon-120.png">
    <!-- PWA支持 -->
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#d4af37">

    <title>一念仙魔-历练地图</title>
    <!-- 引入通用导航样式 -->
    <link rel="stylesheet" href="assets/css/global.css">
    <link rel="stylesheet" href="assets/css/adventure.css">
    <!-- 引入通用导航样式 -->
    <link rel="stylesheet" href="assets/css/common-navigation.css">
    <!-- 移动端CSS已删除 -->
    
    <!-- 🔧 视口高度修复脚本 -->
    <script src="assets/js/viewport-height-fix.js"></script>
    
   <!-- 🔑 全局登录检查系统 -->
    <script src="assets/js/auth-check.js"></script>
    <!-- 🎵 全局音乐管理器 -->
    <script src="assets/js/global-music-manager.js"></script>

</head>
<body>
    <div class="main-container">
        <!-- 顶部标题 -->
        <div class="page-header">
            <div class="page-title">修仙历练</div>
        </div>

        <!-- 标签页 -->
        <div class="tabs-container">
            <div class="tab active" data-target="normalMaps">普通历练</div>
            <div class="tab" data-target="dungeonMaps">秘境探索</div>
        </div>

        <!-- 普通地图 -->
        <div id="normalMaps" class="tab-content map-areas active">
            <!-- 这里会通过JS动态加载地图数据 -->
            <div class="loading-placeholder">
                <div class="loading-spinner"></div>
                <p>正在加载地图数据...</p>
            </div>
        </div>

        <!-- 秘境地图 -->
        <div id="dungeonMaps" class="tab-content map-areas">
            <!-- 这里会通过JS动态加载地图数据 -->
            <div class="loading-placeholder">
                <div class="loading-spinner"></div>
                <p>正在加载地图数据...</p>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <script src="assets/js/common-navigation.js"></script>
        
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 🎵 全局音乐管理器会自动处理音乐播放
            
            // 切换标签功能
            const tabs = document.querySelectorAll('.tab');
            const mapContainers = document.querySelectorAll('.tab-content');
            
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const target = this.dataset.target;
            
                    // 移除所有激活状态
                    tabs.forEach(t => t.classList.remove('active'));
                    mapContainers.forEach(c => c.classList.remove('active'));
            
                    // 添加当前激活状态
                    this.classList.add('active');
                    document.getElementById(target).classList.add('active');
                });
        });
        
        // 加载地图数据
            loadMaps();
        });
                
        // 加载地图数据
        function loadMaps() {
            showLoading('正在加载地图数据...');
                
            // 使用新的API路径
            fetch(window.GameConfig ? window.GameConfig.getApiUrl('adventure_maps.php?action=get_maps') : '../src/api/adventure_maps.php?action=get_maps')
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    
                    if (data.success) {
                        console.log('加载地图数据成功:', data);
                        renderMaps(data.maps);
                    } else {
                        // 🔧 检查是否为会话过期错误
                        if (data.error_code === 'SESSION_EXPIRED') {
                            showError('会话已过期，正在跳转到登录页面...');
                            setTimeout(() => {
                                window.location.href = 'login.html';
                            }, 2000);
                        } else {
                            showError('获取地图数据失败: ' + data.message);
                        }
                    }
                })
                .catch(error => {
                    hideLoading();
                    showError('网络错误: ' + error.message);
                    console.error('Error:', error);
                });
        }
        
        // 渲染地图数据
        function renderMaps(maps) {
            // 渲染普通地图
            const normalMapContainer = document.getElementById('normalMaps');
            renderMapArea(normalMapContainer, maps.normal_maps, 'normal');
            
            // 渲染秘境地图
            const dungeonMapContainer = document.getElementById('dungeonMaps');
            renderMapArea(dungeonMapContainer, maps.dungeons, 'dungeon');
        }
        
        // 渲染地图区域
        function renderMapArea(container, maps, mapType) {
            container.innerHTML = '';
            
            if (!maps || maps.length === 0) {
                container.innerHTML = '<div class="empty-state">暂无可用地图</div>';
                return;
                        }
                        
            maps.forEach(map => {
                const mapElement = createMapElement(map, mapType);
                container.appendChild(mapElement);
            });
        }
        
        // 创建地图元素
        function createMapElement(map, mapType) {
            const mapDiv = document.createElement('div');
            mapDiv.className = `map-area ${mapType} ${map.is_unlocked ? 'unlocked' : 'locked'}`;
            mapDiv.dataset.mapId = map.id;
            mapDiv.dataset.mapCode = map.map_code;
            
            // 🎨 为地图选项卡设置背景图片
            if (map.map_icon) {
                mapDiv.style.backgroundImage = `url('${map.map_icon}')`;
                mapDiv.style.backgroundSize = 'cover';
                mapDiv.style.backgroundPosition = 'center';
                mapDiv.style.backgroundRepeat = 'no-repeat';
                // 添加半透明遮罩确保文字可读性
                mapDiv.style.position = 'relative';
            }
            
            // 直接使用数据库中的描述字段
            let detailedDescription = map.description || '暂无描述';
            
            // 🔧 修复：显示最高通关层数而不是当前层数
            const maxStageReached = map.max_stage_reached || 0;
            
            // 简化地图内容显示，移除等级相关显示
            mapDiv.innerHTML = `
                <div class="map-overlay"></div>
                <div class="area-header">
                    <div class="area-name">${map.map_name}</div>
                    <div class="area-progress">${maxStageReached}/${map.max_stages}</div>
                </div>
                ${!map.is_unlocked ? '<div class="lock-icon">🔒</div>' : ''}
                <div class="area-description">${detailedDescription}</div>
                <div class="map-limits">
                    ${map.realm_requirement > 1 ? `
                    <div class="map-limit realm">
                        <span>${getRealmName(map.realm_requirement)}</span>
                    </div>` : ''}
                    ${map.entry_cost > 0 ? `
                    <div class="map-limit entry">
                        <span>${map.entry_cost}灵石</span>
                    </div>` : ''}
                </div>
                <div class="area-info">
                    <div class="info-item">
                        <span class="info-value">🔄 ${map.total_battles || 0}战</span>
                    </div>
                    <div class="info-item">
                        <span class="info-value">✅ ${map.total_victories || 0}胜</span>
                    </div>
                </div>
                <button class="enter-button" ${!map.is_unlocked ? 'disabled' : ''} onclick="enterMap(${map.id}, '${map.map_code}')">
                    ${!map.is_unlocked ? '未解锁' : '进入'}
                </button>
            `;
                    
            return mapDiv;
        }
        
        // 进入地图
        function enterMap(mapId, mapCode) {
            if (!mapId) return;
            
            showLoading('正在获取地图信息...');
            
            // 获取地图详情
            fetch(window.GameConfig ? window.GameConfig.getApiUrl(`adventure_maps.php?action=get_map_detail&map_id=${mapId}`) : `../src/api/adventure_maps.php?action=get_map_detail&map_id=${mapId}`)
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    
                    if (data.success) {
                        console.log('成功获取地图详情:', data);
                        
                        // 🆕 显示层数选择对话框
                        showStageSelector(data, mapId, mapCode);
                    } else {
                        // 🔧 检查是否为会话过期错误
                        if (data.error_code === 'SESSION_EXPIRED') {
                            showError('会话已过期，正在跳转到登录页面...');
                            setTimeout(() => {
                                window.location.href = 'login.html';
                            }, 2000);
                        } else {
                            showError('获取地图详情失败: ' + data.message);
                        }
                    }
                })
                .catch(error => {
                    hideLoading();
                    showError('网络错误: ' + error.message);
                    console.error('Error:', error);
                });
        }
        
        // 🆕 显示层数选择对话框
        function showStageSelector(mapData, mapId, mapCode) {
            console.log('🔍 地图数据:', mapData);
            
            const progress = mapData.progress;
            // 🔧 修复：最高输入层数就是最高通关层数，不能+1
            const maxStageReached = progress ? (progress.max_stage_reached || progress.max_stage || 1) : 1;
            const maxStage = maxStageReached;
            const currentStage = progress ? progress.current_stage : 1;
            
            console.log('🔍 进度信息:', { progress, maxStage, currentStage });
            
            // 🔧 修复：如果没有进度记录或者最高通关层数为0，直接进入第1层
            if (!progress || maxStageReached <= 0) {
                console.log('🎯 没有通关记录，直接进入第1层');
                enterMapWithStage(mapData, mapId, mapCode, 1);
                return;
            }
            
            // 创建层数选择对话框
            const overlay = document.createElement('div');
            overlay.className = 'stage-selector-overlay';
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
            `;
            
            const dialog = document.createElement('div');
            dialog.className = 'stage-selector-dialog';
            dialog.style.cssText = `
                background: linear-gradient(135deg, #2c1810, #3a2418);
                border: 2px solid #d4af37;
                border-radius: 12px;
                padding: 20px;
                max-width: 400px;
                width: 90%;
                text-align: center;
                color: #f4f1e8;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
            `;
            
            dialog.innerHTML = `
                <h3 style="margin: 0 0 15px; color: #d4af37; font-size: 18px;">选择进入层数</h3>
                <p style="margin: 0 0 15px; font-size: 14px; color: #bbb;">
                    ${mapData.map.map_name} - 当前进度：第${currentStage}层 / 最高通关：第${maxStageReached}层
                </p>
                <div style="margin: 15px 0;">
                    <label style="display: block; margin-bottom: 8px; font-size: 14px;">进入层数：</label>
                    <input type="number" id="targetStage" min="1" max="${maxStage}" value="${maxStage}" 
                           style="width: 80px; padding: 8px; border: 1px solid #d4af37; border-radius: 4px; 
                                  background: #1a1a1a; color: #f4f1e8; text-align: center; font-size: 16px;">
                    <span style="margin-left: 8px; font-size: 14px; color: #bbb;">/ ${maxStage}</span>
                </div>
                <div style="margin: 20px 0;">
                    <button id="confirmEnterBtn" style="
                        background: linear-gradient(135deg, #d4af37, #f4d03f);
                        color: #2c1810;
                        border: none;
                        padding: 10px 20px;
                        border-radius: 6px;
                        font-weight: bold;
                        margin-right: 10px;
                        cursor: pointer;
                        font-size: 14px;
                    ">确定进入</button>
                    <button id="cancelEnterBtn" style="
                        background: #666;
                        color: white;
                        border: none;
                        padding: 10px 20px;
                        border-radius: 6px;
                        cursor: pointer;
                        font-size: 14px;
                    ">取消</button>
                </div>
            `;
            
            overlay.appendChild(dialog);
            document.body.appendChild(overlay);
            
            // 保存数据到全局变量供确认函数使用
            window.currentMapData = mapData;
            window.currentMapId = mapId;
            window.currentMapCode = mapCode;
            
            console.log('💾 已保存全局变量:', {
                currentMapData: window.currentMapData,
                currentMapId: window.currentMapId,
                currentMapCode: window.currentMapCode
            });
            
            // 🔧 修复：使用addEventListener绑定事件
            const confirmBtn = document.getElementById('confirmEnterBtn');
            const cancelBtn = document.getElementById('cancelEnterBtn');
            
            if (confirmBtn) {
                confirmBtn.addEventListener('click', confirmEnterStage);
                console.log('✅ 确认按钮事件已绑定');
            }
            
            if (cancelBtn) {
                cancelBtn.addEventListener('click', closeStageSelector);
                console.log('✅ 取消按钮事件已绑定');
            }
            
            // 聚焦到输入框并绑定回车事件
            setTimeout(() => {
                const input = document.getElementById('targetStage');
                if (input) {
                    input.focus();
                    input.select();
                    
                    // 支持回车键确认
                    input.addEventListener('keypress', function(e) {
                        if (e.key === 'Enter') {
                            confirmEnterStage();
                        }
                    });
                }
            }, 100);
        }
        
        // 🆕 确认进入指定层数
        function confirmEnterStage() {
            const targetStage = parseInt(document.getElementById('targetStage').value);
            
            // 🔧 修复：检查全局变量是否存在
            if (!window.currentMapData || !window.currentMapId || !window.currentMapCode) {
                console.error('❌ 全局变量丢失:', {
                    currentMapData: window.currentMapData,
                    currentMapId: window.currentMapId,
                    currentMapCode: window.currentMapCode
                });
                alert('数据丢失，请重新选择地图');
                closeStageSelector();
                return;
            }
            
            const progress = window.currentMapData.progress;
            // 🔧 修复：最高输入层数就是最高通关层数，不能+1  
            const maxStageReached = progress ? (progress.max_stage_reached || progress.max_stage || 1) : 1;
            const maxStage = maxStageReached;
            
            console.log('🔍 确认进入层数:', {
                targetStage,
                maxStage,
                progress,
                mapData: window.currentMapData,
                mapId: window.currentMapId,
                mapCode: window.currentMapCode
            });
            
            if (!targetStage || targetStage < 1 || targetStage > maxStage) {
                alert(`请输入有效的层数（1-${maxStage}）`);
                return;
            }
            
            // 关闭对话框
            closeStageSelector();
            
            // 进入指定层数
            enterMapWithStage(window.currentMapData, window.currentMapId, window.currentMapCode, targetStage);
        }
        
        // 🆕 关闭层数选择对话框
        function closeStageSelector() {
            const overlay = document.querySelector('.stage-selector-overlay');
            if (overlay) {
                overlay.remove();
            }
            // 🔧 修复：延迟清理全局变量，避免在确认进入时丢失数据
            setTimeout(() => {
                delete window.currentMapData;
                delete window.currentMapId;
                delete window.currentMapCode;
                console.log('🧹 已清理全局变量');
            }, 100);
        }
        
        // 🆕 进入地图的指定层数
        function enterMapWithStage(mapData, mapId, mapCode, targetStage) {
            console.log('🎯 进入地图指定层数:', { mapData, mapId, mapCode, targetStage });
            
            // 🔧 修复：参数验证
            if (!mapData || !mapId || !mapCode || !targetStage) {
                console.error('❌ 参数不完整:', { mapData, mapId, mapCode, targetStage });
                showError('参数错误，无法进入地图');
                return;
            }
            
            showLoading('正在进入第' + targetStage + '层...');
            
            // 如果目标层数与当前进度不同，需要更新进度
            const currentStage = mapData.progress ? mapData.progress.current_stage : 1;
            
            console.log('🔍 当前层数对比:', { currentStage, targetStage });
            
            if (targetStage !== currentStage) {
                // 🆕 调用后端API更新地图进度
                fetch(window.GameConfig ? window.GameConfig.getApiUrl('update_map_progress.php') : '../src/api/update_map_progress.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `action=set_stage&map_id=${mapId}&target_stage=${targetStage}&current_stage=${currentStage}`
                })
                .then(response => response.text())
                .then(responseText => {
                    console.log('🔍 设置层数API响应:', responseText);
                    
                    let result;
                    try {
                        result = JSON.parse(responseText);
                    } catch (parseError) {
                        console.error('❌ JSON解析失败:', parseError);
                        console.error('❌ 响应内容:', responseText);
                        throw new Error('服务器返回无效JSON格式: ' + responseText.substring(0, 100));
                    }
                    
                    if (result.success) {
                        console.log('✅ 地图进度已更新到第' + targetStage + '层');
                        // 更新本地数据并进入战斗
                        proceedToBattle(mapData, mapId, mapCode, targetStage);
                    } else {
                        hideLoading();
                        showError('更新地图进度失败: ' + result.message);
                    }
                })
                .catch(error => {
                    hideLoading();
                    showError('网络错误: ' + error.message);
                    console.error('Error:', error);
                });
            } else {
                // 目标层数与当前进度相同，直接进入
                proceedToBattle(mapData, mapId, mapCode, targetStage);
            }
        }
        
        // 🆕 进入战斗页面
        function proceedToBattle(mapData, mapId, mapCode, targetStage) {
            // 保存当前地图数据到本地存储
            localStorage.setItem('current_map', JSON.stringify(mapData.map));
            
            // 更新进度数据
            const updatedProgress = mapData.progress || {};
            updatedProgress.current_stage = targetStage;
            localStorage.setItem('map_progress', JSON.stringify(updatedProgress));
            
            // 只有在有关卡数据时才保存
            if (mapData.stages && Array.isArray(mapData.stages)) {
                localStorage.setItem('map_stages', JSON.stringify(mapData.stages));
            }
            
            // 创建currentArea对象（供战斗系统使用）
            const currentArea = {
                areaId: mapCode,
                areaName: mapData.map.map_name,
                progress: targetStage,
                total: mapData.map.max_stages,
                mapId: mapId
            };
            
            localStorage.setItem('currentArea', JSON.stringify(currentArea));
            console.log('保存地图信息到currentArea:', currentArea);
            
            // 🆕 设置从地图进入战斗的标识
            sessionStorage.setItem('fromMap', 'true');
            console.log('🗺️ 设置从地图进入战斗的标识');
            
            hideLoading();
            
            // 跳转到战斗页面，使用URL参数传递地图ID和代码
            window.location.href = `battle.html?map_id=${mapId}&map_code=${mapCode}`;
        }
        
        // 获取地图类型名称
        function getMapTypeName(type) {
            const types = {
                'normal': '普通',
                'dungeon': '秘境',
                'special': '特殊',
                'pvp': '战场'
            };
            return types[type] || '未知';
        }
        
        // 获取境界名称
        function getRealmName(realmId) {
            const realms = {
                1: '开光期',
                11: '灵虚期',
                21: '辟谷期',
                31: '心动期',
                41: '元化期',
                51: '元婴期',
                61: '离合期',
                71: '空冥期',
                81: '寂灭期',
                91: '大乘期',
                101: '渡劫期',
                111: '凡仙期',
                121: '地仙期',
                131: '天仙期',
                141: '真仙期',
                151: '太乙真仙期',
                161: '太乙金仙期',
                171: '太乙玄仙期',
                181: '大罗真仙期',
                191: '大罗金仙期',
                201: '大罗玄仙期',
                211: '准圣期',
                221: '教主期',
                231: '混元期',
                241: '天道期',
                251: '鸿蒙至元期'
            };
            return realms[realmId] || `${realmId}阶`;
        }
        
        // 显示加载中
        function showLoading(message) {
            // 删除现有的加载提示
            const existingLoading = document.querySelector('.loading-overlay');
            if (existingLoading) {
                existingLoading.remove();
            }
            
            const loadingDiv = document.createElement('div');
            loadingDiv.className = 'loading-overlay';
            loadingDiv.innerHTML = `
                <div class="loading-content">
                    <div class="loading-spinner"></div>
                    <div class="loading-message">${message || '加载中...'}</div>
                </div>
            `;
            document.body.appendChild(loadingDiv);
        }
        
        // 隐藏加载中
        function hideLoading() {
            const loading = document.querySelector('.loading-overlay');
            if (loading) {
                loading.remove();
            }
        }
        
        // 显示错误信息
        function showError(message) {
            // 删除现有的错误消息
            const existingError = document.querySelector('.error-message');
            if (existingError) {
                existingError.remove();
            }
            
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.innerHTML = `
                <div class="error-content">
                    <div class="error-icon">⚠️</div>
                    <div class="error-text">${message}</div>
                </div>
            `;
            document.body.appendChild(errorDiv);
            
            // 3秒后自动消失
            setTimeout(() => {
                errorDiv.remove();
            }, 3000);
        }
    </script>
</body>
</html>