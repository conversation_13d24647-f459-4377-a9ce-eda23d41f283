/* 角色装备组件 - 公用样式 */

/* 角色装备区域 - 重新设计为弧形布局 */
.character-section {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
    border-radius: 20px;
    padding: 20px;
    margin-bottom: 15px;
    border: 2px solid rgba(212, 175, 55, 0.4);
    backdrop-filter: blur(15px);
    box-shadow: 
        0 8px 25px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    min-height: 220px;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* 弧形装备槽位容器 */
.equipment-arc {
    position: relative;
    width: 100%;
    height: 200px;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* 角色头像 - 居中 */
.character-avatar {
    width: 120px;
    height: 150px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);    
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28px;
    color: white;    
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.character-name {
    position: absolute;
    bottom: 0px;
    left: 50%;
    transform: translateX(-50%);
    color: #d4af37;
    font-size: 13px;
    font-weight: bold;
    text-shadow: 0 0 8px rgba(212, 175, 55, 0.5);
    white-space: nowrap;
}

/* 装备槽位 - 统一大小，优美弧形排列 */
.equipment-slot, .weapon-slot, .inventory-item {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5));
    border: 2px solid rgba(212, 175, 55, 0.6);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 8px;
    text-align: center;
    backdrop-filter: blur(10px);
    line-height: 1;
    position: relative;
    box-shadow: 
        0 4px 8px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.equipment-slot {
    position: absolute;
}

/* 重新设计装备槽位布局 - 充分利用空间，避免重叠 */
/* 顶部装备 */
.equipment-slot[data-slot="ring"] {
    top: 10px;
    left: calc(50% - 130px);
}

.equipment-slot[data-slot="accessory"] {
    top: 10px;
    right: calc(50% - 130px);
}

/* 中间两侧装备 */
.equipment-slot[data-slot="bracers"] {
    top: 50%;
    left: 10px;
    transform: translateY(-50%);
}

.equipment-slot[data-slot="chest"] {
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
}

/* 底部装备 */
.equipment-slot[data-slot="belt"] {
    bottom: 10px;
    left: calc(50% - 130px);
}

.equipment-slot[data-slot="boots"] {
    bottom: 10px;
    right: calc(50% - 130px);
}

.equipment-slot:hover, .weapon-slot:hover, .inventory-item:hover {
    transform: scale(1.05);
    border-color: #f4d03f;
    box-shadow: 
        0 6px 12px rgba(0, 0, 0, 0.4),
        0 0 15px rgba(212, 175, 55, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* 中间两侧装备悬停时保持居中变换 */
.equipment-slot[data-slot="bracers"]:hover {
    transform: translateY(-50%) scale(1.05);
}

.equipment-slot[data-slot="chest"]:hover {
    transform: translateY(-50%) scale(1.05);
}

.equipment-slot:active, .weapon-slot:active, .inventory-item:active {
    transform: scale(0.95);
}

/* 中间两侧装备点击时保持居中变换 */
.equipment-slot[data-slot="bracers"]:active {
    transform: translateY(-50%) scale(0.95);
}

.equipment-slot[data-slot="chest"]:active {
    transform: translateY(-50%) scale(0.95);
}

.equipment-slot.equipped, .weapon-slot.equipped {
    border-color: #d4af37;
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.4), rgba(212, 175, 55, 0.2));
    box-shadow: 
        0 4px 8px rgba(0, 0, 0, 0.3),
        0 0 15px rgba(212, 175, 55, 0.6),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* 武器区域美化 */
.weapon-section {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
    border-radius: 18px;
    padding: 15px;
    margin-bottom: 15px;
    border: 2px solid rgba(212, 175, 55, 0.4);
    backdrop-filter: blur(15px);
    box-shadow: 
        0 8px 25px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.weapon-grid {
    display: flex;
    justify-content: space-between;
    gap: 8px;
    max-width: 100%;
}

.weapon-slot {
    width: calc((100% - 40px) / 6);
    height: 45px;
    border-radius: 12px;
    flex-direction: column;
    font-size: 8px;
}

/* 武器耐久条美化 */
.weapon-durability {
    position: absolute;
    bottom: 3px;
    left: 3px;
    right: 3px;
    height: 4px;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 3px;
    overflow: hidden;
    border: 1px solid rgba(212, 175, 55, 0.3);
}

.durability-bar {
    height: 100%;
    background: linear-gradient(90deg, #e74c3c, #f39c12, #27ae60);
    transition: width 0.3s ease;
    border-radius: 2px;
    box-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
}

/* 响应式优化 */
@media (max-width: 480px) {
    .character-section {
        padding: 15px;
        min-height: 180px;
    }
    
    .equipment-arc {
        height: 160px;
    }
    
    .character-avatar {
        width: 105px;
        height: 127.5px;
        font-size: 24px;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
    }
    
    .equipment-slot, .weapon-slot, .inventory-item {
        width: 40px;
        height: 40px;
        font-size: 7px;
    }
    
    .weapon-slot {
        height: 40px;
        width: calc((100% - 35px) / 6);
    }

    /* 480px屏幕装备槽位调整 */
    .equipment-slot[data-slot="ring"] {
        top: 8px;
        left: calc(50% - 100px);
    }

    .equipment-slot[data-slot="accessory"] {
        top: 8px;
        right: calc(50% - 100px);
    }

    .equipment-slot[data-slot="bracers"] {
        top: 50%;
        left: 8px;
        transform: translateY(-50%);
    }

    .equipment-slot[data-slot="chest"] {
        top: 50%;
        right: 8px;
        transform: translateY(-50%);
    }

    .equipment-slot[data-slot="belt"] {
        bottom: 8px;
        left: calc(50% - 100px);
    }

    .equipment-slot[data-slot="boots"] {
        bottom: 8px;
        right: calc(50% - 100px);
    }

    /* 保持居中变换 */
    .equipment-slot[data-slot="bracers"]:hover {
        transform: translateY(-50%) scale(1.05);
    }

    .equipment-slot[data-slot="chest"]:hover {
        transform: translateY(-50%) scale(1.05);
    }

    .equipment-slot[data-slot="bracers"]:active {
        transform: translateY(-50%) scale(0.95);
    }

    .equipment-slot[data-slot="chest"]:active {
        transform: translateY(-50%) scale(0.95);
    }
}

@media (max-width: 360px) {
    .character-section {
        padding: 12px;
        min-height: 160px;
    }
    
    .equipment-arc {
        height: 140px;
    }
    
    .character-avatar {
        width: 90px;
        height: 112.5px;
        font-size: 20px;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
    }
    
    .equipment-slot, .weapon-slot, .inventory-item {
        width: 35px;
        height: 35px;
        font-size: 6px;
    }
    
    .weapon-slot {
        height: 35px;
        width: calc((100% - 30px) / 6);
    }

    /* 360px屏幕装备槽位调整 */
    .equipment-slot[data-slot="ring"] {
        top: 5px;
        left: calc(50% - 80px);
    }

    .equipment-slot[data-slot="accessory"] {
        top: 5px;
        right: calc(50% - 80px);
    }

    .equipment-slot[data-slot="bracers"] {
        top: 50%;
        left: 5px;
        transform: translateY(-50%);
    }

    .equipment-slot[data-slot="chest"] {
        top: 50%;
        right: 5px;
        transform: translateY(-50%);
    }

    .equipment-slot[data-slot="belt"] {
        bottom: 5px;
        left: calc(50% - 80px);
    }

    .equipment-slot[data-slot="boots"] {
        bottom: 5px;
        right: calc(50% - 80px);
    }

    /* 保持居中变换 */
    .equipment-slot[data-slot="bracers"]:hover {
        transform: translateY(-50%) scale(1.05);
    }

    .equipment-slot[data-slot="chest"]:hover {
        transform: translateY(-50%) scale(1.05);
    }

    .equipment-slot[data-slot="bracers"]:active {
        transform: translateY(-50%) scale(0.95);
    }

    .equipment-slot[data-slot="chest"]:active {
        transform: translateY(-50%) scale(0.95);
    }
} 