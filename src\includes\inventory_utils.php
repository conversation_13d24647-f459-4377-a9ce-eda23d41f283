<?php
/**
 * 背包管理工具函数
 * 用于处理背包物品的排序权重等功能
 */

/**
 * 为新物品计算合适的sort_weight值
 * 新物品将排在背包最前面（权重值最大）
 * @param PDO $pdo 数据库连接
 * @param int $characterId 角色ID
 * @param string $itemType 物品类型
 * @return int 计算出的权重值
 */
function calculateSortWeight($pdo, $characterId, $itemType) {
    try {
        // 获取该角色当前最大的sort_weight值
        $stmt = $pdo->prepare("SELECT MAX(sort_weight) as max_weight FROM user_inventories WHERE character_id = ?");
        $stmt->execute([$characterId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $maxWeight = intval($result['max_weight'] ?: 0);
        
        // 返回比最大值还大的权重值，让新物品排在最前面
        return $maxWeight + 1;
        
    } catch (Exception $e) {
        error_log("计算sort_weight失败: " . $e->getMessage());
        // 如果计算失败，返回一个较大的权重值
        return time(); // 使用时间戳确保唯一性和递增性
    }
}

/**
 * 批量为物品分配sort_weight
 * @param PDO $pdo 数据库连接
 * @param int $characterId 角色ID
 * @param array $inventoryIds 需要分配权重的背包物品ID数组
 * @return bool 是否成功
 */
function assignSortWeights($pdo, $characterId, $inventoryIds) {
    try {
        if (empty($inventoryIds)) {
            return true;
        }
        
        // 获取当前最大权重
        $stmt = $pdo->prepare("SELECT MAX(sort_weight) as max_weight FROM user_inventories WHERE character_id = ?");
        $stmt->execute([$characterId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $currentWeight = intval($result['max_weight'] ?: 0);
        
        // 为每个物品分配递增的权重
        foreach ($inventoryIds as $inventoryId) {
            $currentWeight++;
            $stmt = $pdo->prepare("UPDATE user_inventories SET sort_weight = ? WHERE id = ?");
            $stmt->execute([$currentWeight, $inventoryId]);
        }
        
        return true;
        
    } catch (Exception $e) {
        error_log("批量分配sort_weight失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 重新整理角色背包的排序权重
 * @param PDO $pdo 数据库连接
 * @param int $characterId 角色ID
 * @return bool 是否成功
 */
function reorganizeInventorySortWeights($pdo, $characterId) {
    try {
        // 获取所有物品，按照理想的排序规则
        $stmt = $pdo->prepare("
            SELECT ui.id
            FROM user_inventories ui
            JOIN game_items gi ON ui.item_id = gi.id
            WHERE ui.character_id = ?
            ORDER BY 
                CASE ui.item_type
                    WHEN 'weapon' THEN 1
                    WHEN 'equipment' THEN 2
                    WHEN 'consumable' THEN 3
                    WHEN 'material' THEN 4
                    WHEN 'currency' THEN 5
                    ELSE 6
                END,
                gi.item_name,
                ui.obtained_time DESC,
                ui.id DESC
        ");
        $stmt->execute([$characterId]);
        $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 🔧 修复：重新分配权重 - 从大到小分配，最新的物品权重最大
        $weight = count($items); // 从物品总数开始，确保权重递减
        foreach ($items as $item) {
            $stmtUpdate = $pdo->prepare("UPDATE user_inventories SET sort_weight = ? WHERE id = ?");
            $stmtUpdate->execute([$weight, $item['id']]);
            $weight--; // 权重递减，后面的物品权重越来越小
        }
        
        return true;
        
    } catch (Exception $e) {
        error_log("重新整理背包排序权重失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 获取物品类型的排序优先级
 * @param string $itemType 物品类型
 * @return int 优先级数字（越小优先级越高）
 */
function getItemTypePriority($itemType) {
    $priorities = [
        'weapon' => 1,
        'equipment' => 2,
        'consumable' => 3,
        'material' => 4,
        'currency' => 5,
        'recipe' => 6,
        'special' => 7
    ];
    
    return isset($priorities[$itemType]) ? $priorities[$itemType] : 99;
}
?> 