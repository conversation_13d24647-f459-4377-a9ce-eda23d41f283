
        /* 主要内容区域 */
        .main-container {
            padding: 8px 8px 80px 8px;
            height: 100vh;
            position: relative;
            z-index: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        /* 顶部标题栏 */
        .header {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.12), rgba(255, 255, 255, 0.04));
            border-radius: 12px;
            padding: 8px 12px;
            margin-bottom: 8px;
            border: 1px solid rgba(212, 175, 55, 0.4);
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 40px;
        }
        
        .header-title {
            font-size: 16px;
            font-weight: bold;
            color: #d4af37;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .back-btn {
            background: rgba(212, 175, 55, 0.2);
            border: 1px solid rgba(212, 175, 55, 0.5);
            border-radius: 8px;
            color: #d4af37;
            padding: 4px 8px;
            font-size: 12px;
            cursor: pointer;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 3px;
        }

        /* 商城分类导航 */
        .shop-categories {
            display: flex;
            gap: 8px;
            margin-bottom: 8px;
            height: 50px;
        }

        .category-btn {
            flex: 1;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.03));
            border: 1px solid rgba(212, 175, 55, 0.3);
            border-radius: 12px;
            color: #fff;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 4px;
            backdrop-filter: blur(8px);
        }

        .category-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(212, 175, 55, 0.3);
        }

        .category-btn.active {
            background: linear-gradient(135deg, rgba(212, 175, 55, 0.3), rgba(212, 175, 55, 0.1));
            border-color: rgba(212, 175, 55, 0.8);
            box-shadow: 0 0 15px rgba(212, 175, 55, 0.4);
        }

        .category-icon {
            font-size: 18px;
            margin-bottom: 2px;
        }

        .category-name {
            font-size: 11px;
            font-weight: bold;
        }

        .category-currency {
            font-size: 8px;
            color: #bdc3c7;
            margin-top: 1px;
            display: none; /* 隐藏金币和灵石文字 */
        }

        /* 用户资源显示 */
        .user-resources {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.03));
            border-radius: 12px;
            padding: 8px;
            margin-bottom: 8px;
            border: 1px solid rgba(76, 175, 80, 0.4);
            backdrop-filter: blur(8px);
            height: 40px;
        }

        .resources-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            height: 100%;
        }

        .resource-item {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 6px;
            padding: 4px 6px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .resource-left {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .resource-icon {
            font-size: 12px;
        }

        .resource-name {
            font-size: 9px;
            color: #bdc3c7;
        }

        .resource-value {
            font-size: 10px;
            font-weight: bold;
            color: #d4af37;
        }

        /* 商城内容区域 */
        .shop-content {
            flex: 1;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        /* 商品分类区块 */
        .items-category {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.03));
            border-radius: 12px;
            padding: 10px;
            border: 1px solid rgba(212, 175, 55, 0.3);
            backdrop-filter: blur(8px);
        }

        .category-header {
            display: flex;
            align-items: center;
            gap: 6px;
            margin-bottom: 8px;
            padding-bottom: 4px;
            border-bottom: 1px solid rgba(212, 175, 55, 0.3);
        }

        .category-title {
            font-size: 13px;
            font-weight: bold;
            color: #d4af37;
        }

        /* 商品网格 */
        .items-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 6px;
        }

        .item-card {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.4), rgba(26, 26, 46, 0.6));
            border-radius: 10px;
            padding: 8px;
            border: 1px solid rgba(212, 175, 55, 0.2);
            transition: all 0.3s ease;
            position: relative;
        }

        .item-card:hover {
            transform: translateY(-2px);
            border-color: rgba(212, 175, 55, 0.5);
            box-shadow: 0 4px 12px rgba(212, 175, 55, 0.2);
        }

        .item-card.sold-out {
            opacity: 0.6;
            background: linear-gradient(135deg, rgba(100, 100, 100, 0.4), rgba(60, 60, 60, 0.6));
            cursor: not-allowed;
        }

        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 6px;
        }

        .item-info {
            flex: 1;
        }

        .item-name {
            font-size: 12px;
            font-weight: bold;
            color: #fff;
            margin-bottom: 2px;
        }

        .item-description {
            font-size: 9px;
            color: #bdc3c7;
            line-height: 1.3;
            margin-bottom: 4px;
        }

        .item-effect {
            font-size: 8px;
            color: #4CAF50;
            margin-bottom: 4px;
        }

        .item-level-req {
            font-size: 8px;
            color: #ff9800;
        }

        .item-icon {
            font-size: 24px;
            filter: drop-shadow(0 0 4px rgba(212, 175, 55, 0.5));
        }

        .item-rarity {
            position: absolute;
            top: 4px;
            right: 4px;
            padding: 1px 4px;
            border-radius: 8px;
            font-size: 7px;
            font-weight: bold;
        }

        .rarity-普通 { background: rgba(169, 169, 169, 0.8); color: #000; }
        .rarity-稀有 { background: rgba(30, 144, 255, 0.8); color: #fff; }
        .rarity-史诗 { background: rgba(138, 43, 226, 0.8); color: #fff; }
        .rarity-传说 { background: rgba(255, 140, 0, 0.8); color: #000; }
        .rarity-神话 { background: rgba(255, 215, 0, 0.8); color: #000; }

        .item-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 6px;
            padding-top: 6px;
            border-top: 1px solid rgba(212, 175, 55, 0.2);
        }

        .item-price {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .price-value {
            font-size: 11px;
            font-weight: bold;
            color: #ffd700;
        }

        .price-currency {
            font-size: 8px;
            color: #bdc3c7;
        }

        .item-purchase {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .purchase-limit {
            font-size: 8px;
            color: #bdc3c7;
        }

        .purchase-btn {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            border: none;
            border-radius: 6px;
            color: white;
            padding: 4px 8px;
            font-size: 9px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
        }

        .purchase-btn:hover {
            background: linear-gradient(135deg, #45a049, #4CAF50);
            transform: translateY(-1px);
        }

        .purchase-btn:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }
        
        /* 🆕 批量购买样式 */
        .bulk-purchase {
            flex-direction: column;
            align-items: stretch;
            gap: 4px;
        }
        
        .quantity-selector {
            display: flex;
            align-items: center;
            gap: 4px;
            justify-content: center;
        }
        
        .qty-btn {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            border: none;
            border-radius: 4px;
            color: white;
            width: 20px;
            height: 20px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .qty-btn:hover {
            background: linear-gradient(135deg, #1976D2, #2196F3);
            transform: scale(1.1);
        }
        
        .qty-input {
            width: 40px;
            height: 20px;
            border: 1px solid rgba(212, 175, 55, 0.3);
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.3);
            color: #fff;
            text-align: center;
            font-size: 10px;
            padding: 0 2px;
        }
        
        .qty-input:focus {
            outline: none;
            border-color: #d4af37;
            box-shadow: 0 0 4px rgba(212, 175, 55, 0.3);
        }
        
        .quick-qty-buttons {
            display: flex;
            gap: 2px;
            justify-content: center;
        }
        
        .quick-qty-btn {
            background: linear-gradient(135deg, #ff9800, #f57c00);
            border: none;
            border-radius: 3px;
            color: white;
            padding: 2px 4px;
            font-size: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 18px;
        }
        
        .quick-qty-btn:hover {
            background: linear-gradient(135deg, #f57c00, #ff9800);
            transform: scale(1.05);
        }
        
        .bulk-btn {
            background: linear-gradient(135deg, #9c27b0, #7b1fa2);
            font-size: 9px;
            margin-top: 2px;
        }
        
        .bulk-btn:hover {
            background: linear-gradient(135deg, #7b1fa2, #9c27b0);
        }

        /* 🆕 材料拥有数量显示 */
        .material-owned {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 4px;
            padding: 2px 6px;
            margin-bottom: 4px;
            font-size: 8px;
        }
        
        .owned-label {
            color: #bdc3c7;
        }
        
        .owned-value {
            color: #4CAF50;
            font-weight: bold;
        }
        
        /* 推荐按钮样式 */
        .recommended-btn {
            background: linear-gradient(135deg, #4CAF50, #45a049) !important;
            color: white !important;
            font-weight: bold;
        }
        
        .recommended-btn:hover {
            background: linear-gradient(135deg, #45a049, #4CAF50) !important;
        }
        
        /* 数量提示 */
        .quantity-hint {
            font-size: 7px;
            color: #9E9E9E;
            text-align: center;
            margin: 2px 0;
            line-height: 1.2;
            min-height: 14px;
        }

        /* 加载状态 */
        .loading {
            text-align: center;
            padding: 20px;
            color: #bdc3c7;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #bdc3c7;
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 10px;
            opacity: 0.5;
        }

        /* 确认购买弹窗 */
        .purchase-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1001;
            backdrop-filter: blur(5px);
        }

        .purchase-modal-content {
            background: linear-gradient(135deg, rgba(26, 26, 46, 0.95), rgba(22, 33, 62, 0.95));
            border-radius: 20px;
            padding: 20px;
            width: 90%;
            max-width: 380px;
            border: 2px solid rgba(76, 175, 80, 0.6);
            box-shadow: 0 0 30px rgba(76, 175, 80, 0.4);
            text-align: center;
        }

        .purchase-title {
            font-size: 16px;
            font-weight: bold;
            color: #4CAF50;
            margin-bottom: 15px;
        }

        .purchase-item-info {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 12px;
            margin: 10px 0;
        }

        .purchase-item-name {
            font-size: 14px;
            font-weight: bold;
            color: #fff;
            margin-bottom: 6px;
        }

        .purchase-item-effect {
            font-size: 11px;
            color: #4CAF50;
            margin-bottom: 6px;
        }

        .purchase-cost {
            font-size: 12px;
            color: #ffd700;
            margin-bottom: 15px;
        }

        .purchase-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .purchase-button {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .purchase-confirm {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
        }

        .purchase-confirm:hover {
            background: linear-gradient(135deg, #45a049, #3d8b40);
            transform: translateY(-2px);
        }

        .purchase-cancel {
            background: linear-gradient(135deg, #666, #888);
            color: white;
        }

        .purchase-cancel:hover {
            background: linear-gradient(135deg, #777, #999);
            transform: translateY(-2px);
        }

        /* 消息提示 */
        .message {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            padding: 12px 20px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            z-index: 1005;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            min-width: 200px;
            text-align: center;
            animation: messageSlideIn 0.3s ease-out;
        }

        @keyframes messageSlideIn {
            from {
                opacity: 0;
                transform: translate(-50%, -60%);
            }
            to {
                opacity: 1;
                transform: translate(-50%, -50%);
            }
        }

        @keyframes messageShake {
            0%, 100% { 
                transform: translate(-50%, -50%); 
            }
            10%, 30%, 50%, 70%, 90% { 
                transform: translate(-52%, -50%); 
            }
            20%, 40%, 60%, 80% { 
                transform: translate(-48%, -50%); 
            }
        }

        .message.success {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border-color: rgba(76, 175, 80, 0.4);
        }

        .message.error {
            background: linear-gradient(135deg, #f44336, #da190b);
            color: white;
            border-color: rgba(244, 67, 54, 0.4);
            box-shadow: 0 6px 20px rgba(244, 67, 54, 0.3);
            animation: messageSlideIn 0.3s ease-out, messageShake 0.5s ease-in-out 0.3s;
        }

        .message.info {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            color: white;
            border-color: rgba(33, 150, 243, 0.4);
        }

        /* 横屏适配 */
        @media (orientation: landscape) and (max-height: 500px) {
            .main-container {
                padding: 4px 4px 60px 4px;
            }
            
            .header {
                height: 32px;
                padding: 4px 8px;
                margin-bottom: 4px;
            }
            
            .shop-categories {
                height: 40px;
                margin-bottom: 4px;
            }
            
            .user-resources {
                height: 32px;
                margin-bottom: 4px;
            }
        }

        /* 滚动条样式 */
        .shop-content::-webkit-scrollbar {
            width: 4px;
        }

        .shop-content::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
        }

        .shop-content::-webkit-scrollbar-thumb {
            background: rgba(212, 175, 55, 0.6);
            border-radius: 2px;
        }

        .shop-content::-webkit-scrollbar-thumb:hover {
            background: rgba(212, 175, 55, 0.8);
        }