# 🔑 全局登录检查系统集成指南

## 📋 概述

全局登录检查系统是一个自动化的用户认证状态监控机制，能够在所有需要登录的页面中自动检查用户登录状态，如果发现用户未登录会自动跳转到登录页面。

## 🎯 主要功能

### ✨ 核心特性
- **自动初始化**: 页面加载时自动检查登录状态
- **定期检查**: 每30秒进行一次完整的登录状态检查
- **快速检查**: 每10秒进行一次轻量级的会话检查
- **智能跳转**: 未登录时自动跳转到登录页面
- **页面感知**: 自动识别哪些页面需要登录验证
- **网络容错**: 网络错误时避免误跳转
- **资源清理**: 页面卸载时自动清理定时器

### 🔧 API功能
- **标准检查**: 完整的数据库验证检查
- **快速检查**: 仅检查会话状态
- **会话刷新**: 重新从数据库获取最新用户信息
- **手动调用**: 支持手动触发检查

## 🚀 快速集成

### 第一步：引入脚本文件

在需要登录验证的页面头部添加：

```html
<head>
    <!-- 其他头部内容 -->
    
    <!-- 🔑 引入全局登录检查模块 -->
    <script src="assets/js/auth-check.js"></script>
</head>
```

### 第二步：自动生效

脚本引入后会自动工作，无需额外配置：

- 自动检查当前页面是否需要登录验证
- 如果需要验证且用户未登录，自动跳转到登录页面
- 启动定期检查机制

### 第三步：可选的手动调用

如果需要手动控制，可以使用全局对象：

```javascript
// 手动检查登录状态
const isLoggedIn = await window.AuthCheck.check();

// 快速检查（仅检查会话）
const isLoggedIn = await window.AuthCheck.quickCheck();

// 刷新会话信息
const refreshed = await window.AuthCheck.refresh();

// 清理资源
window.AuthCheck.cleanup();
```

## ⚙️ 配置选项

### 默认配置

```javascript
const AUTH_CONFIG = {
    // 登录页面路径
    LOGIN_PAGE: 'login.html',
    
    // API路径
    AUTH_STATUS_API: '../src/api/auth_status.php',
    USER_INFO_API: '../src/api/user_info.php',
    SESSION_DEBUG_API: '../session_debug.php',
    
    // 不需要登录检查的页面
    EXCLUDED_PAGES: [
        'login.html',
        'register.html', 
        'index.html'
    ],
    
    // 检查间隔
    CHECK_INTERVAL: 30000,        // 30秒完整检查
    QUICK_CHECK_INTERVAL: 10000,  // 10秒快速检查
    
    // 是否启用自动检查
    AUTO_CHECK_ENABLED: true
};
```

### 自定义配置

如果需要修改配置，可以在引入脚本后修改：

```javascript
// 修改检查间隔
window.AuthCheck.config.CHECK_INTERVAL = 60000; // 改为60秒

// 添加排除页面
window.AuthCheck.config.EXCLUDED_PAGES.push('help.html');

// 禁用自动检查
window.AuthCheck.config.AUTO_CHECK_ENABLED = false;
```

## 📁 文件结构

```
public/
├── assets/
│   └── js/
│       └── auth-check.js          # 全局登录检查模块
├── example_auth_integration.html  # 集成示例页面
└── [其他页面].html                # 需要集成的页面

src/
└── api/
    └── auth_status.php            # 登录状态检查API
```

## 🔍 工作原理

### 检查流程

1. **页面加载检查**
   - 判断当前页面是否需要登录验证
   - 如果需要，执行初始登录检查
   - 未登录则跳转到登录页面

2. **定期检查机制**
   - 每30秒执行一次完整检查（包含数据库验证）
   - 每10秒执行一次快速检查（仅检查会话）
   - 页面不可见时暂停检查

3. **事件触发检查**
   - 页面重新可见时检查
   - 窗口重新获得焦点时检查

### API调用层次

```
前端 auth-check.js
    ↓
后端 auth_status.php
    ↓
数据库验证
    ↓
返回详细状态信息
```

## 🎨 集成示例

### 基础集成

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>游戏页面</title>
    
    <!-- 引入全局登录检查 -->
    <script src="assets/js/auth-check.js"></script>
</head>
<body>
    <!-- 页面内容 -->
    <div class="game-content">
        <h1>游戏主界面</h1>
        <!-- 其他内容 -->
    </div>
</body>
</html>
```

### 高级集成（带自定义处理）

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>游戏页面</title>
    
    <!-- 引入全局登录检查 -->
    <script src="assets/js/auth-check.js"></script>
</head>
<body>
    <div class="game-content">
        <h1>游戏主界面</h1>
        <div id="user-info"></div>
    </div>

    <script>
        // 页面特定的登录处理
        document.addEventListener('DOMContentLoaded', async function() {
            // 等待 AuthCheck 初始化完成
            setTimeout(async () => {
                if (window.AuthCheck) {
                    // 获取用户信息并显示
                    try {
                        const response = await fetch('../src/api/auth_status.php?action=check');
                        const data = await response.json();
                        
                        if (data.success && data.user_info) {
                            document.getElementById('user-info').innerHTML = 
                                `欢迎，${data.user_info.username}！`;
                        }
                    } catch (error) {
                        console.error('获取用户信息失败:', error);
                    }
                }
            }, 1000);
        });
    </script>
</body>
</html>
```

## 🛠 现有页面改造

### 改造步骤

1. **添加脚本引用**
   ```html
   <script src="assets/js/auth-check.js"></script>
   ```

2. **移除现有的登录检查代码**（如果有）
   - 删除页面级别的登录状态检查
   - 删除手动的跳转逻辑

3. **测试验证**
   - 未登录状态访问页面应该自动跳转
   - 登录后页面应该正常显示
   - 登录过期后应该自动跳转

### 需要改造的页面列表

以下页面需要添加全局登录检查：

- ✅ `game.html` - 游戏主界面
- ✅ `cultivation.html` - 修炼页面
- ✅ `equipment_integrated.html` - 装备页面
- ✅ `battle.html` - 战斗页面
- ✅ `adventure.html` - 冒险页面
- ✅ `alchemy.html` - 炼丹页面
- ✅ `shop.html` - 商城页面
- ✅ `spirit_system.html` - 精灵系统
- ✅ `settings.html` - 设置页面
- ✅ `attributes.html` - 属性页面
- ✅ `map.html` - 地图页面

- ✅ `spirit_root.html` - 灵根页面

### 不需要改造的页面

以下页面无需登录验证：

- ❌ `login.html` - 登录页面
- ❌ `register.html` - 注册页面  
- ❌ `index.html` - 首页

## 🔧 故障排除

### 常见问题

1. **脚本未加载**
   - 检查文件路径是否正确
   - 确认 `assets/js/auth-check.js` 文件存在

2. **API调用失败**
   - 检查 `src/api/auth_status.php` 是否存在
   - 确认服务器PHP环境正常

3. **误跳转问题**
   - 检查页面是否在排除列表中
   - 确认会话状态正常

4. **定时器冲突**
   - 确保页面卸载时调用了 `cleanup()` 方法

### 调试方法

1. **查看控制台日志**
   ```javascript
   // AuthCheck 会输出详细的调试信息
   console.log('📦 全局登录状态检查模块已加载');
   ```

2. **手动测试**
   ```javascript
   // 在控制台中手动测试
   await window.AuthCheck.check(true);
   ```

3. **使用示例页面**
   - 访问 `example_auth_integration.html` 进行测试

## 📊 性能考虑

### 优化特性

- **智能检查**: 页面不可见时暂停检查
- **防重复**: 避免同时进行多个检查
- **网络容错**: 网络错误时不会误跳转
- **资源清理**: 自动清理定时器和事件监听器

### 性能指标

- **初始检查**: 通常在1-2秒内完成
- **定期检查**: 每次检查耗时约100-500ms
- **内存占用**: 极小，主要是定时器和事件监听器
- **网络请求**: 每30秒一次完整检查，每10秒一次快速检查

## 🔮 未来扩展

### 计划功能

- **多标签页同步**: 一个标签页登出，其他标签页同步跳转
- **离线检测**: 网络断开时的处理机制
- **自定义事件**: 登录状态变化时触发自定义事件
- **更细粒度控制**: 不同页面不同的检查策略

### 扩展接口

```javascript
// 未来可能的扩展接口
window.AuthCheck.on('login', function(userInfo) {
    // 登录成功时的处理
});

window.AuthCheck.on('logout', function() {
    // 登出时的处理
});

window.AuthCheck.on('session_expired', function() {
    // 会话过期时的处理
});
```

---

## 📞 技术支持

如果在集成过程中遇到问题，请：

1. 查看浏览器控制台的错误信息
2. 检查服务器PHP错误日志
3. 使用示例页面进行对比测试
4. 参考本文档的故障排除部分

---

*文档更新日期: 2024年12月19日*  
*适用版本: 一念修仙游戏 v1.0+*  
*维护责任: AI开发助手* 