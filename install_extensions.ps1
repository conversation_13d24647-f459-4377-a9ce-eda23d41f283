# 一念修仙项目 VSCode 扩展安装脚本
# 运行方式: 在PowerShell中执行 .\install_extensions.ps1

Write-Host "开始安装一念修仙项目推荐的VSCode扩展..." -ForegroundColor Green

# 检查VSCode是否安装
$vscodeCmd = Get-Command code -ErrorAction SilentlyContinue
if (-not $vscodeCmd) {
    Write-Host "错误: 未找到VSCode命令行工具 'code'。请确保VSCode已正确安装并添加到PATH。" -ForegroundColor Red
    exit 1
}

# 扩展列表
$extensions = @(
    # PHP开发核心
    "bmewburn.vscode-intelephense-client",
    "xdebug.php-debug",
    
    # 数据库管理
    "formulahendry.vscode-mysql",
    "mtxr.sqltools",
    "mtxr.sqltools-driver-mysql",
    
    # 前端开发
    "ritwickdey.liveserver",
    "formulahendry.auto-rename-tag",
    
    # 代码质量
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ikappas.phpcs",
    
    # 项目管理
    "eamodio.gitlens",
    "alefragnani.project-manager",
    
    # 开发效率
    "christian-kohler.path-intellisense",
    "coenraads.bracket-pair-colorizer-2",
    
    # API测试
    "humao.rest-client",
    "rangav.vscode-thunder-client",
    
    # Web游戏开发辅助
    "eriklynd.json-tools",
    "naumovs.color-highlight",
    "pranaygp.vscode-css-peek",
    
    # 通用工具
    "ms-vscode.vscode-json",
    "redhat.vscode-yaml",
    "ms-vscode.powershell"
)

$successCount = 0
$failCount = 0

foreach ($extension in $extensions) {
    Write-Host "正在安装: $extension" -ForegroundColor Yellow
    
    try {
        $result = & code --install-extension $extension 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ 成功安装: $extension" -ForegroundColor Green
            $successCount++
        } else {
            Write-Host "✗ 安装失败: $extension - $result" -ForegroundColor Red
            $failCount++
        }
    } catch {
        Write-Host "✗ 安装异常: $extension - $($_.Exception.Message)" -ForegroundColor Red
        $failCount++
    }
    
    Start-Sleep -Milliseconds 500
}

Write-Host "`n安装完成!" -ForegroundColor Green
Write-Host "成功安装: $successCount 个扩展" -ForegroundColor Green
Write-Host "安装失败: $failCount 个扩展" -ForegroundColor Red

if ($failCount -gt 0) {
    Write-Host "`n建议手动安装失败的扩展，或检查网络连接后重新运行此脚本。" -ForegroundColor Yellow
}

Write-Host "`n请重启VSCode以确保所有扩展正常加载。" -ForegroundColor Cyan
