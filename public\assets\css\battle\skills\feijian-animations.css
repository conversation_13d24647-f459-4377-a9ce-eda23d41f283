/**
 * 飞剑术技能动画样式
 * 对应 animation_model = 'feijian'
 */

/* 🗡️ 飞剑动画样式 */
.flying-sword {
    position: absolute;
    width: min(40px, 8vw);
    height: min(80px, 16vw);
    transform: translate(-50%, -50%) rotate(0deg);
    transform-origin: center;
    opacity: 0;
    pointer-events: none;
    z-index: 200;
    
    /* 支持背景图片显示 */
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    
    /* 🌟 剑身发光特效 */
    -webkit-filter: drop-shadow(0 0 8px rgba(135, 206, 235, 0.8)); 
    filter: drop-shadow(0 0 8px rgba(135, 206, 235, 0.8)) 
            drop-shadow(0 0 16px rgba(255, 255, 255, 0.6))
            drop-shadow(0 0 24px rgba(135, 206, 235, 0.4));
}

/* 飞剑武器图片样式 */
.flying-sword .weapon-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    z-index: 1;
    position: relative;
    opacity: 1 !important; /* 强制确保图片可见 */
    
    /* 🌟 武器图片发光强化 */
    -webkit-filter: drop-shadow(0 0 6px rgba(135, 206, 235, 1.0)); 
    filter: drop-shadow(0 0 6px rgba(135, 206, 235, 1.0)) 
            drop-shadow(0 0 12px rgba(255, 255, 255, 0.8))
            drop-shadow(0 0 18px rgba(135, 206, 235, 0.6))
            brightness(1.2) contrast(1.1);
}

/* 🌟 飞剑火箭尾焰效果 */
.flying-sword::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 8px;
    background: linear-gradient(-90deg, 
        transparent 0%,
        rgba(255, 255, 255, 0.4) 20%, 
        rgba(135, 206, 250, 0.6) 40%, 
        rgba(255, 255, 255, 0.8) 60%, 
        rgba(135, 206, 250, 0.9) 80%, 
        rgba(255, 255, 255, 1) 100%);
    transform: translate(-50%, -50%) rotate(90deg); /* 垂直向上的尾焰 */
    border-radius: 0 0 4px 4px; /* 底部圆角 */
    opacity: 0;
    z-index: -1;
}

/* 敌方飞剑发光和尾焰颜色 */
.flying-sword.enemy-sword {
    /* 🌟 敌方剑身发光特效 - 红色系 */
    -webkit-filter: drop-shadow(0 0 8px rgba(255, 69, 0, 0.8)); 
    filter: drop-shadow(0 0 8px rgba(255, 69, 0, 0.8)) 
            drop-shadow(0 0 16px rgba(255, 100, 100, 0.6))
            drop-shadow(0 0 24px rgba(255, 69, 0, 0.4));
}

.flying-sword.enemy-sword .weapon-image {
    /* 🌟 敌方武器图片发光强化 - 红色系 */
    -webkit-filter: drop-shadow(0 0 6px rgba(255, 69, 0, 1.0)); 
    filter: drop-shadow(0 0 6px rgba(255, 69, 0, 1.0)) 
            drop-shadow(0 0 12px rgba(255, 100, 100, 0.8))
            drop-shadow(0 0 18px rgba(255, 69, 0, 0.6))
            brightness(1.2) contrast(1.1);
}

.flying-sword.enemy-sword::before {
    background: linear-gradient(-90deg, 
        transparent 0%,
        rgba(255, 100, 100, 0.4) 20%, 
        rgba(255, 69, 0, 0.6) 40%, 
        rgba(255, 100, 100, 0.8) 60%, 
        rgba(255, 69, 0, 0.9) 80%, 
        rgba(255, 100, 100, 1) 100%);
}

/* 🌟 飞剑冲击波效果 - 移除 */
.flying-sword::after {
    display: none; /* 不再使用冲击波效果 */
}

/* 敌方飞剑冲击波颜色 - 移除 */
.flying-sword.enemy-sword::after {
    display: none; /* 不再使用冲击波效果 */
}

/* 🚀 飞行时自动触发尾焰效果 - 修复选择器 */
.flying-sword[style*="fly-sword-to-target-player"]::before {
    animation: sword-trail-player 0.6s ease-out forwards !important;
}

.flying-sword[style*="fly-sword-to-target-enemy"]::before {
    animation: sword-trail-enemy 0.6s ease-out forwards !important;
}

/* 🌟 发光效果已集成到基础样式和动画中，无需额外触发 */

/* 🚀 移除冲击波触发 */
.flying-sword[style*="fly-sword-to-target"]::after {
    display: none;
}

.flying-sword.enemy-sword[style*="fly-sword-to-target"]::after {
    display: none;
}

/* 🔥 飞剑术动画关键帧 */

/* 我方飞剑原地旋转动画 - 加快旋转速度 */
@keyframes sword-spin-player {
    0% {
        transform: translate(-50%, -50%) rotate(180deg);
        opacity: 0;
        -webkit-filter: drop-shadow(0 0 4px rgba(135, 206, 235, 0.4)); 
        filter: drop-shadow(0 0 4px rgba(135, 206, 235, 0.4)) 
                drop-shadow(0 0 8px rgba(255, 255, 255, 0.3));
    }
    30% {
        opacity: 1;
        transform: translate(-50%, -50%) rotate(540deg) scale(1.1);
        -webkit-filter: drop-shadow(0 0 8px rgba(135, 206, 235, 0.8));
        filter: drop-shadow(0 0 8px rgba(135, 206, 235, 0.8)) 
                drop-shadow(0 0 16px rgba(255, 255, 255, 0.6))
                drop-shadow(0 0 24px rgba(135, 206, 235, 0.4));
    }
    100% {
        opacity: 1;
        transform: translate(-50%, -50%) rotate(900deg) scale(1.2);
        -webkit-filter: drop-shadow(0 0 12px rgba(135, 206, 235, 1.0)); 
        filter: drop-shadow(0 0 12px rgba(135, 206, 235, 1.0)) 
                drop-shadow(0 0 24px rgba(255, 255, 255, 0.8))
                drop-shadow(0 0 36px rgba(135, 206, 235, 0.6))
                brightness(1.2);
    }
}

/* 敌方飞剑原地旋转动画 - 加快旋转速度 */
@keyframes sword-spin-enemy {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
        opacity: 0;
        -webkit-filter: drop-shadow(0 0 4px rgba(255, 69, 0, 0.4)); 
        filter: drop-shadow(0 0 4px rgba(255, 69, 0, 0.4)) 
                drop-shadow(0 0 8px rgba(255, 100, 100, 0.3));
    }
    30% {
        opacity: 1;
        transform: translate(-50%, -50%) rotate(-540deg) scale(1.1);
        -webkit-filter: drop-shadow(0 0 8px rgba(255, 69, 0, 0.8)); 
        filter: drop-shadow(0 0 8px rgba(255, 69, 0, 0.8)) 
                drop-shadow(0 0 16px rgba(255, 100, 100, 0.6))
                drop-shadow(0 0 24px rgba(255, 69, 0, 0.4));
    }
    100% {
        opacity: 1;
        transform: translate(-50%, -50%) rotate(-900deg) scale(1.2);
        -webkit-filter: drop-shadow(0 0 12px rgba(255, 69, 0, 1.0)); 
        filter: drop-shadow(0 0 12px rgba(255, 69, 0, 1.0)) 
                drop-shadow(0 0 24px rgba(255, 100, 100, 0.8))
                drop-shadow(0 0 36px rgba(255, 69, 0, 0.6))
                brightness(1.2);
    }
}

/* 我方飞剑高速飞向目标 - 改为匀速飞行 */
@keyframes fly-sword-to-target-player {
    0% {
        left: var(--startX);
        top: var(--startY);
        transform: translate(-50%, -50%) rotate(900deg) scale(1.3);
        opacity: 1;
    }
    100% {
        left: var(--targetCenterX);
        top: var(--targetCenterY);
        transform: translate(-50%, -50%) rotate(900deg) scale(1.3);
        opacity: 1;
    }
}

/* 我方飞剑尾焰动画 */
@keyframes sword-trail-player {
    0% { 
        opacity: 0; 
        height: 0; 
    }
    10% { 
        opacity: 1; 
        height: 40px; 
    }
    50% { 
        opacity: 1; 
        height: 60px; 
    }
    90% { 
        opacity: 1; 
        height: 80px; 
    }
    100% { 
        opacity: 0.8; 
        height: 100px; 
    }
}

/* 🌟 我方飞剑发光脉动动画 */
@keyframes sword-glow-pulse-player {
    0% {
        -webkit-filter: drop-shadow(0 0 8px rgba(135, 206, 235, 0.8)); 
        filter: drop-shadow(0 0 8px rgba(135, 206, 235, 0.8)) 
                drop-shadow(0 0 16px rgba(255, 255, 255, 0.6))
                drop-shadow(0 0 24px rgba(135, 206, 235, 0.4));
    }
    20% {
        -webkit-filter: drop-shadow(0 0 12px rgba(135, 206, 235, 1.0));
        filter: drop-shadow(0 0 12px rgba(135, 206, 235, 1.0)) 
                drop-shadow(0 0 24px rgba(255, 255, 255, 0.8))
                drop-shadow(0 0 36px rgba(135, 206, 235, 0.6))
                brightness(1.3);
    }
    50% {
        -webkit-filter: drop-shadow(0 0 16px rgba(135, 206, 235, 1.2)); 
        filter: drop-shadow(0 0 16px rgba(135, 206, 235, 1.2)) 
                drop-shadow(0 0 32px rgba(255, 255, 255, 1.0))
                drop-shadow(0 0 48px rgba(135, 206, 235, 0.8))
                brightness(1.5);
    }
    80% {
        -webkit-filter: drop-shadow(0 0 20px rgba(135, 206, 235, 1.5)); 
        filter: drop-shadow(0 0 20px rgba(135, 206, 235, 1.5)) 
                drop-shadow(0 0 40px rgba(255, 255, 255, 1.2))
                drop-shadow(0 0 60px rgba(135, 206, 235, 1.0))
                brightness(1.7);
    }
    100% {
        -webkit-filter: drop-shadow(0 0 24px rgba(135, 206, 235, 1.8)); 
        filter: drop-shadow(0 0 24px rgba(135, 206, 235, 1.8)) 
                drop-shadow(0 0 48px rgba(255, 255, 255, 1.5))
                drop-shadow(0 0 72px rgba(135, 206, 235, 1.2))
                brightness(2.0);
    }
}

/* 我方飞剑冲击波动画 - 已删除，不再使用 */

/* 敌方飞剑高速飞向目标 - 改为匀速飞行 */
@keyframes fly-sword-to-target-enemy {
    0% {
        left: var(--startX);
        top: var(--startY);
        transform: translate(-50%, -50%) rotate(-900deg) scale(1.3);
        opacity: 1;
    }
    100% {
        left: var(--targetCenterX);
        top: var(--targetCenterY);
        transform: translate(-50%, -50%) rotate(-900deg) scale(1.3);
        opacity: 1;
    }
}

/* 敌方飞剑尾焰动画 */
@keyframes sword-trail-enemy {
    0% { 
        opacity: 0; 
        height: 0; 
    }
    10% { 
        opacity: 1; 
        height: 40px; 
    }
    50% { 
        opacity: 1; 
        height: 60px; 
    }
    90% { 
        opacity: 1; 
        height: 80px; 
    }
    100% { 
        opacity: 0.8; 
        height: 100px; 
    }
}

/* 🌟 敌方飞剑发光脉动动画 */
@keyframes sword-glow-pulse-enemy {
    0% {
        -webkit-filter: drop-shadow(0 0 8px rgba(255, 69, 0, 0.8)); 
        filter: drop-shadow(0 0 8px rgba(255, 69, 0, 0.8)) 
                drop-shadow(0 0 16px rgba(255, 100, 100, 0.6))
                drop-shadow(0 0 24px rgba(255, 69, 0, 0.4));
    }
    20% {
        -webkit-filter: drop-shadow(0 0 12px rgba(255, 69, 0, 1.0)); 
        filter: drop-shadow(0 0 12px rgba(255, 69, 0, 1.0)) 
                drop-shadow(0 0 24px rgba(255, 100, 100, 0.8))
                drop-shadow(0 0 36px rgba(255, 69, 0, 0.6))
                brightness(1.3);
    }
    50% {
        -webkit-filter: drop-shadow(0 0 16px rgba(255, 69, 0, 1.2));
        filter: drop-shadow(0 0 16px rgba(255, 69, 0, 1.2)) 
                drop-shadow(0 0 32px rgba(255, 100, 100, 1.0))
                drop-shadow(0 0 48px rgba(255, 69, 0, 0.8))
                brightness(1.5);
    }
    80% {
        -webkit-filter: drop-shadow(0 0 20px rgba(255, 69, 0, 1.5)); 
        filter: drop-shadow(0 0 20px rgba(255, 69, 0, 1.5)) 
                drop-shadow(0 0 40px rgba(255, 100, 100, 1.2))
                drop-shadow(0 0 60px rgba(255, 69, 0, 1.0))
                brightness(1.7);
    }
    100% {
        -webkit-filter: drop-shadow(0 0 24px rgba(255, 69, 0, 1.8)); 
        filter: drop-shadow(0 0 24px rgba(255, 69, 0, 1.8)) 
                drop-shadow(0 0 48px rgba(255, 100, 100, 1.5))
                drop-shadow(0 0 72px rgba(255, 69, 0, 1.2))
                brightness(2.0);
    }
}

/* 敌方飞剑冲击波动画 - 已删除，不再使用 */

/* 我方飞剑快速穿透 - 增强穿透感 */
@keyframes fly-sword-penetrate-player {
    0% {
        left: var(--targetCenterX);
        top: var(--targetCenterY);
        transform: translate(-50%, -50%) rotate(900deg) scale(1.5);
        opacity: 1;
        -webkit-filter: drop-shadow(0 0 24px rgba(135, 206, 235, 1.8)); 
        filter: drop-shadow(0 0 24px rgba(135, 206, 235, 1.8)) 
                drop-shadow(0 0 48px rgba(255, 255, 255, 1.5))
                drop-shadow(0 0 72px rgba(135, 206, 235, 1.2))
                brightness(2.0);
    }
    50% {
        top: calc(var(--targetCenterY) + (var(--finalY) - var(--targetCenterY)) * 0.7);
        transform: translate(-50%, -50%) rotate(900deg) scale(1.8);
        opacity: 0.9;
        -webkit-filter: drop-shadow(0 0 18px rgba(135, 206, 235, 1.2)); 
        filter: drop-shadow(0 0 18px rgba(135, 206, 235, 1.2)) 
                drop-shadow(0 0 36px rgba(255, 255, 255, 1.0))
                drop-shadow(0 0 54px rgba(135, 206, 235, 0.8))
                brightness(1.5);
    }
    100% {
        left: var(--targetCenterX);
        top: var(--finalY);
        transform: translate(-50%, -50%) rotate(900deg) scale(2.0);
        opacity: 0;
        -webkit-filter: drop-shadow(0 0 12px rgba(135, 206, 235, 0.6)); 
        filter: drop-shadow(0 0 12px rgba(135, 206, 235, 0.6)) 
                drop-shadow(0 0 24px rgba(255, 255, 255, 0.4))
                drop-shadow(0 0 36px rgba(135, 206, 235, 0.2))
                brightness(1.0);
    }
}

/* 敌方飞剑快速穿透 - 增强穿透感 */
@keyframes fly-sword-penetrate-enemy {
    0% {
        left: var(--targetCenterX);
        top: var(--targetCenterY);
        transform: translate(-50%, -50%) rotate(-900deg) scale(1.5);
        opacity: 1;
        -webkit-filter: drop-shadow(0 0 24px rgba(255, 69, 0, 1.8));
        filter: drop-shadow(0 0 24px rgba(255, 69, 0, 1.8)) 
                drop-shadow(0 0 48px rgba(255, 100, 100, 1.5))
                drop-shadow(0 0 72px rgba(255, 69, 0, 1.2))
                brightness(2.0);
    }
    50% {
        top: calc(var(--targetCenterY) + (var(--finalY) - var(--targetCenterY)) * 0.7);
        transform: translate(-50%, -50%) rotate(-900deg) scale(1.8);
        opacity: 0.9;
        -webkit-filter: drop-shadow(0 0 18px rgba(255, 69, 0, 1.2)); 
        filter: drop-shadow(0 0 18px rgba(255, 69, 0, 1.2)) 
                drop-shadow(0 0 36px rgba(255, 100, 100, 1.0))
                drop-shadow(0 0 54px rgba(255, 69, 0, 0.8))
                brightness(1.5);
    }
    100% {
        left: var(--targetCenterX);
        top: var(--finalY);
        transform: translate(-50%, -50%) rotate(-900deg) scale(2.0);
        opacity: 0;
        -webkit-filter: drop-shadow(0 0 12px rgba(255, 69, 0, 0.6)); 
        filter: drop-shadow(0 0 12px rgba(255, 69, 0, 0.6)) 
                drop-shadow(0 0 24px rgba(255, 100, 100, 0.4))
                drop-shadow(0 0 36px rgba(255, 69, 0, 0.2))
                brightness(1.0);
    }
}

/* 移动端适配 */
@media (max-width: 480px) {
    .flying-sword {
        width: min(32px, 6vw);
        height: min(64px, 12vw);
    }
}

/* 🎯 击中特效样式 */
.sword-hit-flash {
    position: absolute;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: radial-gradient(circle,
        rgba(255, 255, 255, 1) 0%,
        rgba(200, 220, 255, 0.8) 30%,
        rgba(150, 180, 255, 0.4) 60%,
        transparent 100%
    );
    transform: translate(-50%, -50%);
    animation: sword-hit-flash-expand 0.3s ease-out forwards;
    -webkit-filter: blur(1px);
    filter: blur(1px);
    pointer-events: none;
    z-index: 300;
}

@keyframes sword-hit-flash-expand {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
    }
    50% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.8;
    }
    100% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0;
    }
}

.sword-hit-particle {
    position: absolute;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255, 255, 255, 1) 0%, rgba(200, 220, 255, 0.8) 100%);
    animation: sword-hit-particle-scatter 0.6s ease-out forwards;
    -webkit-filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.8));
    filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.8));
    pointer-events: none;
    z-index: 295;
}

@keyframes sword-hit-particle-scatter {
    0% {
        transform: translate(0, 0) scale(1);
        opacity: 1;
    }
    100% {
        transform: translate(var(--particleX), var(--particleY)) scale(0);
        opacity: 0;
    }
}

.sword-hit-shockwave {
    position: absolute;
    width: 80px;
    height: 80px;
    border: 2px solid rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: sword-hit-shockwave-expand 0.5s ease-out forwards;
    -webkit-filter: drop-shadow(0 0 10px rgba(200, 220, 255, 0.6));
    filter: drop-shadow(0 0 10px rgba(200, 220, 255, 0.6));
    pointer-events: none;
    z-index: 290;
}

@keyframes sword-hit-shockwave-expand {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
        border-color: rgba(255, 255, 255, 0.9);
    }
    50% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0.6;
        border-color: rgba(200, 220, 255, 0.7);
    }
    100% {
        transform: translate(-50%, -50%) scale(3);
        opacity: 0;
        border-color: rgba(150, 180, 255, 0.3);
    }
}

/* 🎭 角色受击动画 */
@keyframes sword-struck {
    0%, 100% {
        transform: translateX(0) translateY(0);
        -webkit-filter: brightness(1);
        filter: brightness(1);
    }
    10% {
        transform: translateX(-5px) translateY(-3px);
        -webkit-filter: brightness(1.8) saturate(1.5);
        filter: brightness(1.8) saturate(1.5);
    }
    20% {
        transform: translateX(4px) translateY(2px);
        -webkit-filter: brightness(1.4) saturate(1.2);
        filter: brightness(1.4) saturate(1.2);
    }
    30% {
        transform: translateX(-4px) translateY(4px);
        -webkit-filter: brightness(2.0) saturate(1.8);
        filter: brightness(2.0) saturate(1.8);
    }
    40% {
        transform: translateX(5px) translateY(-2px);
        -webkit-filter: brightness(1.6) saturate(1.4);
        filter: brightness(1.6) saturate(1.4);
    }
    50% {
        transform: translateX(-3px) translateY(-5px);
        -webkit-filter: brightness(1.9) saturate(1.7);
        filter: brightness(1.9) saturate(1.7);
    }
    60% {
        transform: translateX(3px) translateY(3px);
        -webkit-filter: brightness(1.3) saturate(1.1);
        filter: brightness(1.3) saturate(1.1);
    }
    70% {
        transform: translateX(-2px) translateY(1px);
        -webkit-filter: brightness(1.5) saturate(1.3);
        filter: brightness(1.5) saturate(1.3);
    }
    80% {
        transform: translateX(1px) translateY(-1px);
        -webkit-filter: brightness(1.2) saturate(1.05);
        filter: brightness(1.2) saturate(1.05);
    }
    90% {
        transform: translateX(-1px) translateY(0px);
        -webkit-filter: brightness(1.1) saturate(1.02);
        filter: brightness(1.1) saturate(1.02);
    }
}

@keyframes sword-hit-shake {
    0%, 100% {
        transform: translateX(0) translateY(0);
        -webkit-filter: brightness(1);
        filter: brightness(1);
    }
    10% {
        transform: translateX(-4px) translateY(-3px);
        -webkit-filter: brightness(1.5);
        filter: brightness(1.5);
    }
    20% {
        transform: translateX(3px) translateY(2px);
        -webkit-filter: brightness(1.2);
        filter: brightness(1.2);
    }
    30% {
        transform: translateX(-3px) translateY(4px);
        -webkit-filter: brightness(1.8);
        filter: brightness(1.8);
    }
    40% {
        transform: translateX(4px) translateY(-2px);
        -webkit-filter: brightness(1.3);
        filter: brightness(1.3);
    }
    50% {
        transform: translateX(-2px) translateY(-4px);
        -webkit-filter: brightness(1.6);
        filter: brightness(1.6);
    }
    60% {
        transform: translateX(3px) translateY(3px);
        -webkit-filter: brightness(1.2);
        filter: brightness(1.2);
    }
    70% {
        transform: translateX(-1px) translateY(1px);
        -webkit-filter: brightness(1.4);
        filter: brightness(1.4);
    }
    80% {
        transform: translateX(1px) translateY(-1px);
        -webkit-filter: brightness(1.1);
        filter: brightness(1.1);
    }
    90% {
        transform: translateX(-1px) translateY(0px);
        -webkit-filter: brightness(1.1);
        filter: brightness(1.1);
    }
} 