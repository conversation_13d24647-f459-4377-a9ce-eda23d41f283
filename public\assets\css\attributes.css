        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-20px) rotate(1deg); }
            66% { transform: translateY(10px) rotate(-1deg); }
        }
                    
        /* 🔧 PWA模式下的特殊处理 */
        @media (display-mode: standalone) {
            .main-container {
                height: 100vh !important;
                height: calc(var(--vh, 1vh) * 100) !important;
                overflow-y: auto !important;
                -webkit-overflow-scrolling: touch !important;
                position: relative !important;
                padding: 10px 10px 110px 10px !important; /* 🔧 同步增加底部padding到110px */
                box-sizing: border-box !important;
                /* 🔧 修复触摸滚动 */
                overscroll-behavior: contain !important;
                touch-action: pan-y !important;
            }
            
            .attributes-section {
                overflow-y: auto !important;
                -webkit-overflow-scrolling: touch !important;
                flex: 1 !important;
            }
            
            .attribute-panel {
                overflow-y: auto !important;
                -webkit-overflow-scrolling: touch !important;
                flex: 1 !important;
            }
            
            /* 确保滚动条在PWA模式下可见 */
            .main-container::-webkit-scrollbar {
                width: 4px !important;
                display: block !important;
            }
            
            .main-container::-webkit-scrollbar-track {
                background: rgba(255, 255, 255, 0.1) !important;
                border-radius: 2px !important;
            }
            
            .main-container::-webkit-scrollbar-thumb {
                background: rgba(212, 175, 55, 0.5) !important;
                border-radius: 2px !important;
            }
        }
        
        /* 🔧 iOS PWA模式特殊处理 */
        @supports (-webkit-touch-callout: none) {
            @media (display-mode: standalone) {
                .main-container {
                    /* iOS Safari PWA模式 */
                    -webkit-overflow-scrolling: touch !important;
                    overscroll-behavior: contain !important;
                    touch-action: pan-y !important;
                }
                
                .attributes-section,
                .attribute-panel {
                    -webkit-overflow-scrolling: touch !important;
                    overscroll-behavior: contain !important;
                    touch-action: pan-y !important;
                }
            }
        }

        /* 属性分类标题优化 - 🔧 减小尺寸 */
        .attribute-category {
            margin-bottom: 15px; /* 🔧 从25px减少到15px */
            animation: fadeInUp 0.5s ease-out;
        }

        .attribute-category-title {
            font-size: 15px; /* 🔧 从18px减少到15px */
            font-weight: bold;
            color: #fff;
            text-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
            margin-bottom: 10px; /* 🔧 从15px减少到10px */
            padding-left: 8px; /* 🔧 从10px减少到8px */
            border-left: 3px solid #d4af37; /* 🔧 从4px减少到3px */
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .attribute-category-title::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 100%;
            height: 1.5px; /* 🔧 从2px减少到1.5px */
            background: linear-gradient(to right, #d4af37, transparent);
        }

        /* 属性分类样式 */
        .battle-attributes {
            background: rgba(180, 50, 50, 0.2);
            border: 1px solid rgba(220, 50, 50, 0.3);
        }

        .dynamic-attributes {
            background: rgba(50, 120, 220, 0.2);
            border: 1px solid rgba(50, 120, 220, 0.3);
        }

        .cultivation-attributes {
            background: linear-gradient(135deg, rgba(140, 80, 200, 0.15), rgba(140, 80, 200, 0.05));
            border: 1px solid rgba(140, 80, 200, 0.3);
            box-shadow: 
                0 4px 15px rgba(140, 80, 200, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        /* 属性标签 */
        .attributes-tabs {
            display: flex;
            gap: 5px;
            margin-bottom: 15px;
        }

        .tab {
            flex: 1;
            padding: 6px 4px; /* 🔧 从8px 5px减少到6px 4px */
            text-align: center;
            cursor: pointer;
            border-radius: 8px; /* 🔧 从10px减少到8px */
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.3));
            border: 2px solid rgba(212, 175, 55, 0.3);
            color: #bdc3c7;
            font-size: 10px; /* 🔧 从11px减少到10px */
            font-weight: bold;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }

        .tab:hover {
            border-color: rgba(212, 175, 55, 0.6);
            transform: translateY(-1px);
        }

        .tab.active {
            background: linear-gradient(135deg, #d4af37, #b8941f);
            color: #1a3a5c;
            border-color: #d4af37;
            box-shadow: 
                0 4px 8px rgba(212, 175, 55, 0.4),
                0 0 10px rgba(212, 175, 55, 0.3);
        }

        /* 属性面板 - 🔧 减少间距 */
        .attribute-panel {
            display: flex;
            flex-direction: column;
            gap: 10px; /* 🔧 从15px减少到10px */
            overflow-y: auto;
            flex: 1;
            padding: 10px; /* 🔧 从15px减少到10px */
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.2));
            border-radius: 10px; /* 🔧 从12px减少到10px */
            border: 2px solid rgba(212, 175, 55, 0.4);
        }

        .attribute-panel.active {
            display: flex;
            height: 100%;
        }
        
        /* 🔧 属性区域滚动优化 */
        .attributes-section {
            flex: 1;
            -webkit-overflow-scrolling: touch;
            overscroll-behavior: contain;
            touch-action: pan-y;
            min-height: 0;
            /* iOS滚动优化 */
            will-change: scroll-position;
            transform: translateZ(0);
        }

        /* 属性详情项 - 🔧 减小尺寸 */
        .attribute-detail-item {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            border: 1px solid rgba(212, 175, 55, 0.3);
            border-radius: 10px; /* 🔧 从12px减少到10px */
            padding: 10px; /* 🔧 从12px减少到10px */
            display: flex;
            align-items: center;
            gap: 10px; /* 🔧 从12px减少到10px */
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        .attribute-detail-item:hover {
            transform: translateX(5px);
            border-color: rgba(212, 175, 55, 0.6);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .attribute-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            background: linear-gradient(135deg, rgba(212, 175, 55, 0.2), rgba(212, 175, 55, 0.1));
            border-radius: 10px;
            border: 1px solid rgba(212, 175, 55, 0.4);
            flex-shrink: 0;
        }

        .attribute-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .attribute-name {
            font-size: 14px;
            font-weight: bold;
            color: #d4af37;
            text-shadow: 0 0 5px rgba(212, 175, 55, 0.3);
        }

        .attribute-value {
            font-size: 18px;
            font-weight: bold;
            color: #fff;
            text-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
        }

        .attribute-description {
            font-size: 11px;
            color: #bdc3c7;
            opacity: 0.8;
        }

        /* 页面标题 */
        .page-title {
            text-align: center;
            font-size: 24px;
            color: #fff;
            margin-bottom: 25px;
            text-shadow: 
                0 0 20px rgba(255, 255, 255, 0.8),
                0 0 40px rgba(255, 215, 0, 0.6);
            font-weight: bold;
            position: relative;
            animation: titleGlow 3s ease-in-out infinite alternate;
        }

        @keyframes titleGlow {
            from { text-shadow: 0 0 20px rgba(255, 255, 255, 0.8), 0 0 40px rgba(255, 215, 0, 0.6); }
            to { text-shadow: 0 0 30px rgba(255, 255, 255, 1), 0 0 60px rgba(255, 215, 0, 0.8); }
        }

        .page-title::before {
            content: '✨';
            position: absolute;
            left: -30px;
            top: 50%;
            transform: translateY(-50%);
            animation: sparkle 2s ease-in-out infinite;
        }

        .page-title::after {
            content: '✨';
            position: absolute;
            right: -30px;
            top: 50%;
            transform: translateY(-50%);
            animation: sparkle 2s ease-in-out infinite 1s;
        }

        @keyframes sparkle {
            0%, 100% { opacity: 0.4; transform: translateY(-50%) scale(1); }
            50% { opacity: 1; transform: translateY(-50%) scale(1.3); }
        }

        /* 角色头像区域 */
        .character-header {
            background: linear-gradient(135deg, 
                rgba(255, 255, 255, 0.25) 0%,
                rgba(255, 255, 255, 0.1) 50%,
                rgba(255, 255, 255, 0.05) 100%);
            border-radius: 25px;
            padding: 25px;
            margin-bottom: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(20px);
            text-align: center;
            box-shadow: 
                0 8px 32px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.4);
            position: relative;
            overflow: hidden;
            animation: cardFloat 6s ease-in-out infinite;
        }

        @keyframes cardFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
        }

        .character-header::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
            background-size: 200% 200%;
            border-radius: 25px;
            z-index: -1;
            animation: borderGlow 3s linear infinite;
        }

        @keyframes borderGlow {
            0% { background-position: 0% 50%; }
            100% { background-position: 200% 50%; }
        }

        .character-level {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.9);
            background: linear-gradient(135deg, rgba(255, 215, 0, 0.3), rgba(255, 215, 0, 0.1));
            padding: 5px 15px;
            border-radius: 20px;
            display: inline-block;
            border: 1px solid rgba(255, 215, 0, 0.5);
        }

        /* 属性卡片 */
        .attribute-card {
            background: linear-gradient(135deg, 
                rgba(255, 255, 255, 0.2) 0%,
                rgba(255, 255, 255, 0.1) 50%,
                rgba(255, 255, 255, 0.05) 100%);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(20px);
            box-shadow: 
                0 8px 32px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
            animation: cardFloat 6s ease-in-out infinite;
            animation-delay: 0.5s;
        }

        .attribute-card:nth-child(even) {
            animation-delay: 1s;
        }

        .attribute-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .card-title {
            font-size: 18px;
            color: #fff;
            font-weight: bold;
            margin-bottom: 20px;
            text-align: center;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
            position: relative;
            z-index: 1;
        }

        .attributes-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            position: relative;
            z-index: 1;
        }

        .attribute-item {
            background: linear-gradient(135deg, 
                rgba(255, 255, 255, 0.15) 0%,
                rgba(255, 255, 255, 0.08) 50%,
                rgba(255, 255, 255, 0.03) 100%);
            padding: 20px 15px;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.25);
            text-align: center;
            backdrop-filter: blur(10px);
            box-shadow: 
                0 4px 15px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .attribute-item:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 
                0 8px 25px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.3),
                0 0 20px rgba(255, 255, 255, 0.3);
        }

        .attribute-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .attribute-item:hover::before {
            opacity: 1;
        }

        .attribute-label {
            font-size: 13px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 8px;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        /* 资源区域特殊样式 */
        .resources-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
            position: relative;
            z-index: 1;
        }

        .resource-item {
            background: linear-gradient(135deg, 
                rgba(255, 215, 0, 0.2) 0%,
                rgba(255, 215, 0, 0.1) 50%,
                rgba(255, 215, 0, 0.05) 100%);
            padding: 20px 10px;
            border-radius: 15px;
            border: 1px solid rgba(255, 215, 0, 0.4);
            text-align: center;
            backdrop-filter: blur(10px);
            box-shadow: 
                0 4px 15px rgba(255, 215, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .resource-item:hover {
            transform: translateY(-3px);
            box-shadow: 
                0 8px 25px rgba(255, 215, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.4);
        }

        .resource-icon {
            font-size: 24px;
            margin-bottom: 10px;
            animation: iconBounce 2s ease-in-out infinite;
        }

        @keyframes iconBounce {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .resource-label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 8px;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        .resource-value {
            font-size: 16px;
            color: #fff;
            font-weight: bold;
            text-shadow: 
                0 0 10px rgba(255, 215, 0, 0.7),
                0 2px 5px rgba(0, 0, 0, 0.3);
        }

        /* 底部导航栏 */
        .bottom-navigation {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 70px;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.7));
            border-top: 2px solid rgba(212, 175, 55, 0.5);
            display: flex;
            justify-content: space-around;
            align-items: center;
            z-index: 100;
            backdrop-filter: blur(10px);
            box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);
        }

        .nav-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: none;
            border: none;
            color: #fff;
            cursor: pointer;
            padding: 8px 16px;
            border-radius: 10px;
            transition: all 0.3s ease;
            min-width: 60px;
            text-decoration: none;
        }

        .nav-btn:hover {
            background: rgba(212, 175, 55, 0.2);
            transform: translateY(-2px);
            color: #fff;
        }

        .nav-btn:active {
            transform: scale(0.95);
        }

        .nav-btn.active {
            background: rgba(212, 175, 55, 0.3);
            color: #d4af37;
        }

        .nav-btn-icon {
            font-size: 20px;
            margin-bottom: 3px;
        }

        .nav-btn-text {
            font-size: 11px;
            color: #bdc3c7;
        }

        .nav-btn.active .nav-btn-text {
            color: #d4af37;
        }

        /* 响应式优化 */
        @media (max-width: 480px) {
            .main-container {
                padding: 8px 8px 120px 8px; /* 🔧 增加底部padding到120px，确保移动端有足够空间 */
            }
            
            .character-section {
                padding: 15px;
                min-height: 180px;
            }
            
            .equipment-arc {
                height: 160px;
            }
            
            .character-avatar {
                width: 70px;
                height: 85px;
                font-size: 24px;
            }
            
            .equipment-slot, .weapon-slot {
                width: 40px;
                height: 40px;
                font-size: 7px;
            }
            
            .weapon-slot {
                height: 40px;
                width: calc((100% - 35px) / 6);
            }
            
            .tab {
                font-size: 10px;
                padding: 6px 3px;
            }

            /* 480px屏幕装备槽位调整 */
            .equipment-slot[data-slot="ring"] {
                top: 8px;
                left: calc(50% - 100px);
            }

            .equipment-slot[data-slot="accessory"] {
                top: 8px;
                right: calc(50% - 100px);
            }

            .equipment-slot[data-slot="bracers"] {
                top: 50%;
                left: 8px;
                transform: translateY(-50%);
            }

            .equipment-slot[data-slot="chest"] {
                top: 50%;
                right: 8px;
                transform: translateY(-50%);
            }

            .equipment-slot[data-slot="belt"] {
                bottom: 8px;
                left: calc(50% - 100px);
            }

            .equipment-slot[data-slot="boots"] {
                bottom: 8px;
                right: calc(50% - 100px);
            }

            /* 保持居中变换 */
            .equipment-slot[data-slot="bracers"]:hover {
                transform: translateY(-50%) scale(1.05);
            }

            .equipment-slot[data-slot="chest"]:hover {
                transform: translateY(-50%) scale(1.05);
            }

            /* 弹窗响应式优化 */
            .attribute-detail-modal {
                padding: 15px 10px 120px 10px; /* 🔧 移动端调整：上15px，左右10px，下120px，与主容器保持一致 */
            }

            .attribute-detail-content {
                max-width: 100%; /* 🔧 移动端占满宽度 */
                border-radius: 10px; /* 🔧 移动端稍小的圆角 */
            }

            .attribute-detail-header {
                padding: 12px 15px;
            }

            .attribute-detail-title {
                font-size: 15px;
            }

            .attribute-detail-body {
                padding: 15px;
            }

            .dan-medicine-section h4 {
                font-size: 16px;
            }

            .dan-medicine-description {
                font-size: 12px;
                padding: 10px;
            }

            .dan-medicine-item {
                padding: 12px;
            }

            .dan-medicine-icon {
                width: 32px;
                height: 32px;
                font-size: 20px;
            }

            .dan-medicine-name {
                font-size: 14px;
            }

            .cultivation-stats {
                grid-template-columns: repeat(2, 1fr);
                gap: 8px;
            }

            .cultivation-stat-item {
                padding: 10px;
            }

            .cultivation-stat-icon {
                font-size: 18px;
            }

            .techniques-container {
                max-height: 220px;
            }

            .technique-item {
                padding: 12px;
            }

            .technique-name {
                font-size: 15px;
            }
        }

        @media (max-width: 360px) {
            .character-section {
                padding: 12px;
                min-height: 160px;
            }
            
            .equipment-arc {
                height: 140px;
            }
            
            .character-avatar {
                width: 60px;
                height: 75px;
                font-size: 20px;
            }
            
            .equipment-slot, .weapon-slot {
                width: 35px;
                height: 35px;
                font-size: 6px;
            }
            
            .weapon-slot {
                height: 35px;
                width: calc((100% - 30px) / 6);
            }
            
            .tab {
                font-size: 9px;
                padding: 5px 2px;
            }

            /* 360px屏幕装备槽位调整 */
            .equipment-slot[data-slot="ring"] {
                top: 5px;
                left: calc(50% - 80px);
            }

            .equipment-slot[data-slot="accessory"] {
                top: 5px;
                right: calc(50% - 80px);
            }

            .equipment-slot[data-slot="bracers"] {
                top: 50%;
                left: 5px;
                transform: translateY(-50%);
            }

            .equipment-slot[data-slot="chest"] {
                top: 50%;
                right: 5px;
                transform: translateY(-50%);
            }

            .equipment-slot[data-slot="belt"] {
                bottom: 5px;
                left: calc(50% - 80px);
            }

            .equipment-slot[data-slot="boots"] {
                bottom: 5px;
                right: calc(50% - 80px);
            }

            /* 保持居中变换 */
            .equipment-slot[data-slot="bracers"]:hover {
                transform: translateY(-50%) scale(1.05);
            }

            .equipment-slot[data-slot="chest"]:hover {
                transform: translateY(-50%) scale(1.05);
            }

            .attribute-detail-item {
                padding: 10px;
            }

            .attribute-icon {
                width: 35px;
                height: 35px;
                font-size: 18px;
            }

            .attribute-name {
                font-size: 13px;
            }

            .attribute-value {
                font-size: 16px;
            }

            .attribute-description {
                font-size: 10px;
            }

            /* 360px弹窗进一步优化 */
            .attribute-detail-modal {
                padding: 12px 5px 80px 5px; /* 调整底部间距 */
            }

            .attribute-detail-content {
                width: 98%;
                max-width: 320px;
                max-height: calc(100vh - 92px); /* 恢复最大高度限制 */
            }

            .attribute-detail-body {
                padding: 12px;
            }

            .dan-medicine-list {
                gap: 12px;
            }

            .dan-medicine-item {
                padding: 10px;
                gap: 10px;
            }

            .dan-medicine-info {
                gap: 8px;
            }

            .dan-medicine-icon {
                width: 28px;
                height: 28px;
                font-size: 18px;
            }

            .dan-medicine-name {
                font-size: 13px;
            }

            .dan-medicine-effect {
                font-size: 11px;
                padding: 3px 6px;
            }

            .cultivation-stats {
                grid-template-columns: 1fr;
                gap: 6px;
            }

            .cultivation-stat-item {
                padding: 8px;
            }

            .techniques-container {
                max-height: 180px;
            }

            .technique-item {
                padding: 10px;
            }
        }

        @media (max-width: 360px) {
            .attribute-detail-modal {
                padding: 10px 8px 80px 8px; /* 🔧 超小屏幕调整：上10px，左右8px，下80px */
            }

            .attribute-detail-content {
                max-width: 100%;
                border-radius: 8px; /* 🔧 超小屏幕更小的圆角 */
            }
        }

        .attribute-detail-description {
            font-size: 11px;
            color: #bdc3c7;
            opacity: 0.8;
            margin-top: 15px;
            padding: 15px;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.1));
            border-radius: 10px;
            border: 1px solid rgba(212, 175, 55, 0.2);
            backdrop-filter: blur(5px);
        }

        /* 属性说明详细样式 */
        .attribute-explanation {
            line-height: 1.6;
        }

        .attribute-explanation h4 {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid rgba(212, 175, 55, 0.3);
            font-size: 14px;
            font-weight: bold;
        }

        .attribute-explanation h5 {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            font-weight: bold;
            margin: 12px 0 6px 0;
        }

        .attribute-explanation p {
            margin: 8px 0;
            font-size: 11px;
            line-height: 1.5;
        }

        .attribute-explanation ul {
            margin: 8px 0;
            padding-left: 16px;
            list-style-type: none;
        }

        .attribute-explanation li {
            position: relative;
            margin-bottom: 4px;
            font-size: 11px;
            line-height: 1.4;
        }

        .attribute-explanation li::before {
            content: "▸";
            position: absolute;
            left: -12px;
            color: #d4af37;
            font-weight: bold;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .attribute-explanation h4 {
                font-size: 13px;
            }
            
            .attribute-explanation h5 {
                font-size: 11px;
            }
            
            .attribute-explanation p,
            .attribute-explanation li {
                font-size: 10px;
            }
        }

        /* 新增样式：属性概览框优化 */
        .attribute-summary-box {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            border: 1px solid rgba(212, 175, 55, 0.3);
            border-radius: 10px; /* 🔧 从12px减少到10px */
            padding: 10px; /* 🔧 从15px减少到10px */
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        .attribute-summary-box:hover {
            transform: translateY(-2px);
            border-color: rgba(212, 175, 55, 0.6);
            box-shadow: 
                0 4px 12px rgba(0, 0, 0, 0.3),
                0 0 15px rgba(212, 175, 55, 0.3);
        }

        .attribute-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px; /* 🔧 从12px减少到8px */
        }

        .attribute-compact-item {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            border: 1px solid rgba(212, 175, 55, 0.3);
            border-radius: 8px; /* 🔧 从10px减少到8px */
            padding: 8px; /* 🔧 从12px减少到8px */
            display: flex;
            align-items: center;
            gap: 8px; /* 🔧 从10px减少到8px */
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
            overflow: hidden;
        }

        .attribute-compact-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .attribute-compact-item:hover::before {
            left: 100%;
        }

        .attribute-compact-icon {
            font-size: 18px; /* 🔧 从24px减少到18px */
            min-width: 18px; /* 🔧 从24px减少到18px */
            height: 18px; /* 🔧 从24px减少到18px */
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: linear-gradient(135deg, rgba(212, 175, 55, 0.3), rgba(212, 175, 55, 0.1));
            border: 1px solid rgba(212, 175, 55, 0.5);
            flex-shrink: 0;
            transition: all 0.3s ease;
        }

        .attribute-compact-item:hover .attribute-compact-icon {
            background: linear-gradient(135deg, rgba(212, 175, 55, 0.6), rgba(212, 175, 55, 0.3));
            border-color: rgba(212, 175, 55, 0.8);
            transform: scale(1.1);
            box-shadow: 0 0 15px rgba(212, 175, 55, 0.5);
        }

        .attribute-compact-info {
            flex: 1;
            min-width: 0;
        }

        .attribute-compact-label {
            font-size: 11px; /* 🔧 从12px减少到11px */
            color: #bdc3c7;
            margin-bottom: 2px; /* 🔧 从4px减少到2px */
            line-height: 1.2;
        }

        .attribute-compact-value {
            font-size: 14px; /* 🔧 从16px减少到14px */
            font-weight: bold;
            color: #fff;
            text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
            line-height: 1.2;
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        /* 属性类型颜色 */
        .physical-attack {
            background: linear-gradient(135deg, rgba(231, 76, 60, 0.4), rgba(192, 57, 43, 0.2));
            border-color: rgba(231, 76, 60, 0.5);
        }

        .magical-attack {
            background: linear-gradient(135deg, rgba(155, 89, 182, 0.4), rgba(142, 68, 173, 0.2));
            border-color: rgba(155, 89, 182, 0.5);
        }

        .physical-defense {
            background: linear-gradient(135deg, rgba(41, 128, 185, 0.4), rgba(52, 152, 219, 0.2));
            border-color: rgba(41, 128, 185, 0.5);
        }

        .magical-defense {
            background: linear-gradient(135deg, rgba(106, 137, 204, 0.4), rgba(90, 125, 195, 0.2));
            border-color: rgba(106, 137, 204, 0.5);
        }

        .health {
            background: linear-gradient(135deg, rgba(231, 76, 60, 0.4), rgba(192, 57, 43, 0.2));
            border-color: rgba(231, 76, 60, 0.5);
        }

        .mana {
            background: linear-gradient(135deg, rgba(52, 152, 219, 0.4), rgba(41, 128, 185, 0.2));
            border-color: rgba(52, 152, 219, 0.5);
        }

        .speed {
            background: linear-gradient(135deg, rgba(46, 204, 113, 0.4), rgba(39, 174, 96, 0.2));
            border-color: rgba(46, 204, 113, 0.5);
        }

        /* 属性详情弹窗样式 */
        .attribute-detail-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 2000;
            backdrop-filter: blur(5px);
            padding: 20px 15px 90px 15px; /* 🔧 调整内边距：上20px，左右15px，下90px为导航栏留空间 */
        }

        .attribute-detail-content {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            border-radius: 12px;
            width: 100%;
            max-width: 420px;
            max-height: 100%; /* 🔧 让内容占满可用空间 */
            overflow-y: auto;
            position: relative;
            border: 2px solid #d4af37;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.6);
            animation: attribute-detail-slide-in 0.3s ease-out;
        }

        @keyframes attribute-detail-slide-in {
            from {
                opacity: 0;
                transform: translateY(20px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .attribute-detail-header {
            background: linear-gradient(135deg, rgba(50, 50, 70, 0.95), rgba(30, 30, 50, 0.95));
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid rgba(212, 175, 55, 0.3);
            flex-shrink: 0; /* 防止头部压缩 */
        }

        .attribute-detail-title {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: bold;
            color: #fff;
            font-size: 16px;
        }

        .attribute-detail-close {
            background: none;
            border: none;
            color: #d4af37;
            font-size: 20px;
            cursor: pointer;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.3s ease;
            font-weight: bold;
        }

        .attribute-detail-close:hover {
            background: rgba(212, 175, 55, 0.2);
            color: #fff;
            transform: scale(1.1);
        }

        .attribute-detail-body {
            padding: 20px;
            overflow-y: auto; /* 恢复内容滚动 */
            flex: 1; /* 占用剩余空间 */
            -webkit-overflow-scrolling: touch; /* 移动端滚动优化 */
        }

        .attribute-detail-total {
            display: flex;
            justify-content: space-between;
            padding: 10px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            margin-bottom: 12px;
            font-weight: bold;
        }

        .attribute-detail-sources {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .attribute-source-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 6px;
            border-left: 3px solid rgba(138, 43, 226, 0.5);
        }

        .source-name {
            color: #bdc3c7;
            font-size: 13px;
        }

        .source-value {
            font-weight: bold;
        }

        /* 属性颜色主题 */
        .attribute-detail-modal.physical-attack .attribute-detail-header,
        .attribute-compact-item.physical-attack {
            background: linear-gradient(to right, rgba(220, 50, 50, 0.3), rgba(220, 50, 50, 0.1));
        }

        .attribute-detail-modal.immortal-attack .attribute-detail-header,
        .attribute-compact-item.immortal-attack {
            background: linear-gradient(to right, rgba(150, 50, 220, 0.3), rgba(150, 50, 220, 0.1));
        }

        .attribute-detail-modal.physical-defense .attribute-detail-header,
        .attribute-compact-item.physical-defense {
            background: linear-gradient(to right, rgba(50, 120, 220, 0.3), rgba(50, 120, 220, 0.1));
        }

        .attribute-detail-modal.immortal-defense .attribute-detail-header,
        .attribute-compact-item.immortal-defense {
            background: linear-gradient(to right, rgba(80, 120, 180, 0.3), rgba(80, 120, 180, 0.1));
        }

        .attribute-detail-modal.speed .attribute-detail-header,
        .attribute-compact-item.speed {
            background: linear-gradient(to right, rgba(50, 220, 100, 0.3), rgba(50, 220, 100, 0.1));
        }

        .attribute-detail-modal.hp .attribute-detail-header,
        .attribute-compact-item.hp {
            background: linear-gradient(to right, rgba(220, 50, 100, 0.3), rgba(220, 50, 100, 0.1));
        }

        .attribute-detail-modal.accuracy-bonus .attribute-detail-header,
.attribute-compact-item.accuracy-bonus {
            background: linear-gradient(to right, rgba(220, 150, 50, 0.3), rgba(220, 150, 50, 0.1));
        }

        .attribute-detail-modal.dodge-bonus .attribute-detail-header,
.attribute-compact-item.dodge-bonus {
            background: linear-gradient(to right, rgba(60, 180, 220, 0.3), rgba(60, 180, 220, 0.1));
        }

        .attribute-detail-modal.critical-bonus .attribute-detail-header,
.attribute-compact-item.critical-bonus {
            background: linear-gradient(to right, rgba(220, 60, 60, 0.3), rgba(220, 60, 60, 0.1));
        }

        .attribute-detail-modal.critical-resistance .attribute-detail-header,
        .attribute-compact-item.critical-resistance {
            background: linear-gradient(to right, rgba(80, 80, 220, 0.3), rgba(80, 80, 220, 0.1));
        }

        .attribute-detail-modal.constitution .attribute-detail-header,
        .attribute-compact-item.constitution {
            background: linear-gradient(to right, rgba(180, 80, 80, 0.3), rgba(180, 80, 80, 0.1));
        }

        .attribute-detail-modal.wisdom .attribute-detail-header,
        .attribute-compact-item.wisdom {
            background: linear-gradient(to right, rgba(80, 80, 180, 0.3), rgba(80, 80, 180, 0.1));
        }

        .attribute-detail-modal.physique .attribute-detail-header,
        .attribute-compact-item.physique {
            background: linear-gradient(to right, rgba(180, 80, 120, 0.3), rgba(180, 80, 120, 0.1));
        }

        .attribute-detail-modal.soul .attribute-detail-header,
        .attribute-compact-item.soul {
            background: linear-gradient(to right, rgba(120, 80, 180, 0.3), rgba(120, 80, 180, 0.1));
        }

        .attribute-detail-modal.agility .attribute-detail-header,
        .attribute-compact-item.agility {
            background: linear-gradient(to right, rgba(80, 180, 120, 0.3), rgba(80, 180, 120, 0.1));
        }

        .attribute-detail-modal.level .attribute-detail-header,
        .attribute-compact-item.level {
            background: linear-gradient(to right, rgba(150, 150, 50, 0.3), rgba(150, 150, 50, 0.1));
        }

        .attribute-detail-modal.experience .attribute-detail-header,
        .attribute-compact-item.experience {
            background: linear-gradient(to right, rgba(220, 180, 50, 0.3), rgba(220, 180, 50, 0.1));
        }

        /* 源值颜色 */
        #detail-base-value {
            color: #4fc3f7;
        }

        #detail-equipment-value {
            color: #81c784;
        }

        #detail-weapon-value {
            color: #ffb74d;
        }

        #detail-cultivation-value {
            color: #ba68c8;
        }

        /* 属性列表项悬停效果 */
        .attribute-compact-item {
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .attribute-compact-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
        }

        .attribute-compact-item:active {
            transform: translateY(0);
        }
        
        /* 属性弹窗遮罩层 */
        .attribute-popup-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 950;
        }

        /* 修为属性项样式 */
        .attribute-compact-item.constitution,
        .attribute-compact-item.wisdom,
        .attribute-compact-item.physique,
        .attribute-compact-item.soul,
        .attribute-compact-item.agility,
        .attribute-compact-item.level,
        .attribute-compact-item.experience {
            background: linear-gradient(135deg, 
                rgba(140, 80, 200, 0.2) 0%,
                rgba(140, 80, 200, 0.1) 50%,
                rgba(140, 80, 200, 0.05) 100%);
            border: 1px solid rgba(140, 80, 200, 0.3);
            box-shadow: 
                0 2px 8px rgba(140, 80, 200, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        .attribute-compact-item.constitution:hover,
        .attribute-compact-item.wisdom:hover,
        .attribute-compact-item.physique:hover,
        .attribute-compact-item.soul:hover,
        .attribute-compact-item.agility:hover,
        .attribute-compact-item.level:hover,
        .attribute-compact-item.experience:hover {
            transform: translateY(-2px);
            border-color: rgba(140, 80, 200, 0.5);
            box-shadow: 
                0 4px 12px rgba(140, 80, 200, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        /* 修为属性图标样式 */
        .attribute-compact-item.constitution .attribute-compact-icon,
        .attribute-compact-item.wisdom .attribute-compact-icon,
        .attribute-compact-item.physique .attribute-compact-icon,
        .attribute-compact-item.soul .attribute-compact-icon,
        .attribute-compact-item.agility .attribute-compact-icon,
        .attribute-compact-item.level .attribute-compact-icon,
        .attribute-compact-item.experience .attribute-compact-icon {
            background: linear-gradient(135deg, rgba(140, 80, 200, 0.3), rgba(140, 80, 200, 0.1));
            border: 1px solid rgba(140, 80, 200, 0.4);
            box-shadow: 0 2px 5px rgba(140, 80, 200, 0.2);
        }

        /* 修为属性值样式 */
        .attribute-compact-item.constitution .attribute-compact-value,
        .attribute-compact-item.wisdom .attribute-compact-value,
        .attribute-compact-item.physique .attribute-compact-value,
        .attribute-compact-item.soul .attribute-compact-value,
        .attribute-compact-item.agility .attribute-compact-value,
        .attribute-compact-item.level .attribute-compact-value,
        .attribute-compact-item.experience .attribute-compact-value {
            color: #fff;
            text-shadow: 
                0 0 10px rgba(140, 80, 200, 0.8),
                0 0 20px rgba(140, 80, 200, 0.4);
            font-size: 18px;
            font-weight: bold;
        }

        /* 修为属性标签样式 */
        .attribute-compact-item.constitution .attribute-compact-label,
        .attribute-compact-item.wisdom .attribute-compact-label,
        .attribute-compact-item.physique .attribute-compact-label,
        .attribute-compact-item.soul .attribute-compact-label,
        .attribute-compact-item.agility .attribute-compact-label,
        .attribute-compact-item.level .attribute-compact-label,
        .attribute-compact-item.experience .attribute-compact-label {
            color: rgba(255, 255, 255, 0.9);
            font-size: 12px;
            font-weight: 500;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        /* 深色主题适配 */
        @media (prefers-color-scheme: dark) {
            .attribute-summary-box {
                background: rgba(0, 0, 0, 0.3);
            }

            .attribute-compact-item {
                background: linear-gradient(135deg, 
                    rgba(255, 255, 255, 0.08) 0%,
                    rgba(255, 255, 255, 0.03) 100%);
            }
        }

        /* 境界样式 */
        .realm-value {
            background: linear-gradient(45deg, var(--realm-color-start), var(--realm-color-end));
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
            font-weight: bold;
            font-size: 18px;
        }

        /* 灵根属性样式 */
        .attribute-compact-item.gold-root {
            background: linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(255, 215, 0, 0.05));
            border: 1px solid rgba(255, 215, 0, 0.4);
        }
        
        .attribute-compact-item.wood-root {
            background: linear-gradient(135deg, rgba(0, 128, 0, 0.2), rgba(0, 128, 0, 0.05));
            border: 1px solid rgba(0, 128, 0, 0.4);
        }
        
        .attribute-compact-item.water-root {
            background: linear-gradient(135deg, rgba(0, 0, 255, 0.2), rgba(0, 0, 255, 0.05));
            border: 1px solid rgba(0, 0, 255, 0.4);
        }
        
        .attribute-compact-item.fire-root {
            background: linear-gradient(135deg, rgba(255, 69, 0, 0.2), rgba(255, 69, 0, 0.05));
            border: 1px solid rgba(255, 69, 0, 0.4);
        }
        
        .attribute-compact-item.earth-root {
            background: linear-gradient(135deg, rgba(139, 69, 19, 0.2), rgba(139, 69, 19, 0.05));
            border: 1px solid rgba(139, 69, 19, 0.4);
        }
        
        .attribute-compact-item.gold-root .attribute-compact-icon,
        .attribute-compact-item.wood-root .attribute-compact-icon,
        .attribute-compact-item.water-root .attribute-compact-icon,
        .attribute-compact-item.fire-root .attribute-compact-icon,
        .attribute-compact-item.earth-root .attribute-compact-icon {
            font-size: 24px;
        }
        
        .attribute-compact-item.gold-root .attribute-compact-value {
            color: #FFD700;
            text-shadow: 0 0 10px rgba(255, 215, 0, 0.8);
        }
        
        .attribute-compact-item.wood-root .attribute-compact-value {
            color: #228B22;
            text-shadow: 0 0 10px rgba(0, 128, 0, 0.8);
        }
        
        .attribute-compact-item.water-root .attribute-compact-value {
            color: #1E90FF;
            text-shadow: 0 0 10px rgba(30, 144, 255, 0.8);
        }
        
        .attribute-compact-item.fire-root .attribute-compact-value {
            color: #FF4500;
            text-shadow: 0 0 10px rgba(255, 69, 0, 0.8);
        }
        
        .attribute-compact-item.earth-root .attribute-compact-value {
            color: #CD853F;
            text-shadow: 0 0 10px rgba(205, 133, 63, 0.8);
        }

        /* 丹药和修炼与丹药样式 */
        .cultivation-technique .attribute-compact-value,
        .technique-level .attribute-compact-value {
            background: linear-gradient(to right, #4facfe, #00f2fe);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: none;
            font-weight: bold;
        }
        
        .dan-medicine .attribute-compact-value {
            color: #ff6b6b;
            text-shadow: 0 0 10px rgba(255, 107, 107, 0.6);
        }
        
        .dan-medicine-section h4 {
            font-size: 18px;
            margin: 0 0 12px 0;
            color: #d4af37;
            text-shadow: 0 0 8px rgba(212, 175, 55, 0.6);
            font-weight: bold;
        }
        
        .dan-medicine-description {
            font-size: 13px;
            color: #bdc3c7;
            margin-bottom: 18px;
            line-height: 1.5;
            background: rgba(212, 175, 55, 0.1);
            padding: 12px;
            border-radius: 8px;
            border-left: 3px solid #d4af37;
        }
        
        .dan-medicine-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .dan-medicine-item {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            border: 1px solid rgba(212, 175, 55, 0.2);
            border-radius: 12px;
            padding: 15px;
            display: flex;
            flex-direction: column;
            gap: 12px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }
        
        .dan-medicine-item:hover {
            border-color: rgba(212, 175, 55, 0.4);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }
        
        .dan-medicine-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .dan-medicine-icon {
            font-size: 24px;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, rgba(212, 175, 55, 0.2), rgba(212, 175, 55, 0.1));
            border-radius: 8px;
            border: 1px solid rgba(212, 175, 55, 0.3);
        }
        
        .dan-medicine-name {
            font-weight: bold;
            color: #fff;
            font-size: 15px;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
        }
        
        .dan-medicine-effect {
            font-size: 12px;
            color: #bdc3c7;
            margin-left: auto;
            background: rgba(0, 0, 0, 0.3);
            padding: 4px 8px;
            border-radius: 12px;
        }
        
        .dan-medicine-usage {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .dan-medicine-progress-container {
            flex: 1;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .dan-medicine-progress {
            height: 100%;
            background: linear-gradient(to right, #4facfe, #00f2fe);
            border-radius: 3px;
            transition: width 0.3s ease;
            box-shadow: 0 0 8px rgba(79, 172, 254, 0.4);
        }
        
        .dan-medicine-count {
            font-size: 13px;
            color: #d4af37;
            width: 55px;
            text-align: right;
            font-weight: bold;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        }
        
        .dan-medicine-warning {
            margin-top: 20px;
            background: linear-gradient(135deg, rgba(231, 76, 60, 0.2), rgba(231, 76, 60, 0.1));
            border: 1px solid rgba(231, 76, 60, 0.4);
            border-radius: 12px;
            padding: 15px;
            display: flex;
            align-items: center;
            gap: 12px;
            box-shadow: 0 2px 8px rgba(231, 76, 60, 0.2);
        }
        
        .warning-icon {
            font-size: 24px;
            width: 32px;
            text-align: center;
        }
        
        .warning-text {
            font-size: 14px;
            color: #e74c3c;
            line-height: 1.4;
        }
        
        #warning-percent {
            font-weight: bold;
            font-size: 15px;
        }

        /* 自定义滚动条样式 */
        .attribute-detail-body::-webkit-scrollbar {
            width: 6px;
        }

        .attribute-detail-body::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }

        .attribute-detail-body::-webkit-scrollbar-thumb {
            background: linear-gradient(to bottom, #d4af37, #b8941f);
            border-radius: 3px;
            transition: all 0.3s ease;
        }

        .attribute-detail-body::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(to bottom, #f1c40f, #d4af37);
        }

        /* 修炼与丹药弹窗样式 */
        .cultivation-current {
            margin-bottom: 20px;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.2));
            border: 1px solid rgba(79, 172, 254, 0.2);
            border-radius: 12px;
            padding: 18px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }
        
        .cultivation-main-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
        }
        
        .cultivation-name {
            font-size: 20px;
            font-weight: bold;
            color: #4facfe;
            text-shadow: 0 0 10px rgba(79, 172, 254, 0.5);
        }
        
        .cultivation-level {
            background: linear-gradient(135deg, rgba(79, 172, 254, 0.2), rgba(0, 242, 254, 0.1));
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 14px;
            color: #fff;
            border: 1px solid rgba(79, 172, 254, 0.3);
        }
        
        .cultivation-progress {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            gap: 12px;
        }
        
        .cultivation-progress-label {
            font-size: 14px;
            color: #bdc3c7;
            width: 80px;
            flex-shrink: 0;
        }
        
        .cultivation-progress-container {
            flex: 1;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .cultivation-progress-bar {
            height: 100%;
            background: linear-gradient(to right, #4facfe, #00f2fe);
            border-radius: 3px;
            transition: width 0.3s ease;
            box-shadow: 0 0 8px rgba(79, 172, 254, 0.4);
        }
        
        .cultivation-progress-text {
            font-size: 12px;
            color: #d4af37;
            width: 65px;
            text-align: right;
            font-weight: bold;
        }
        
        .cultivation-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
        }
        
        .cultivation-stat-item {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.03));
            border: 1px solid rgba(79, 172, 254, 0.2);
            border-radius: 10px;
            padding: 12px;
            display: flex;
            flex-direction: column;
            align-items: center;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        
        .cultivation-stat-item:hover {
            transform: translateY(-2px);
            border-color: rgba(79, 172, 254, 0.4);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        }
        
        .cultivation-stat-icon {
            font-size: 20px;
            margin-bottom: 6px;
        }
        
        .cultivation-stat-label {
            font-size: 12px;
            color: #bdc3c7;
            margin-bottom: 6px;
            text-align: center;
        }
        
        .cultivation-stat-value {
            font-size: 14px;
            font-weight: bold;
            color: #4facfe;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
        }
        
        .cultivation-techniques-list {
            margin-top: 20px;
        }
        
        .cultivation-techniques-list h4 {
            font-size: 16px;
            margin: 0 0 15px 0;
            color: #d4af37;
            text-shadow: 0 0 5px rgba(212, 175, 55, 0.5);
        }
        
        .techniques-container {
            display: flex;
            flex-direction: column;
            gap: 12px;
            max-height: 280px;
            overflow-y: auto;
        }
        
        .techniques-container::-webkit-scrollbar {
            width: 4px;
        }

        .techniques-container::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
        }

        .techniques-container::-webkit-scrollbar-thumb {
            background: linear-gradient(to bottom, #4facfe, #00f2fe);
            border-radius: 2px;
        }
        
        .technique-item {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.2));
            border: 1px solid rgba(79, 172, 254, 0.2);
            border-radius: 10px;
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        
        .technique-item:hover {
            border-color: rgba(79, 172, 254, 0.4);
            transform: translateX(3px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }
        
        .technique-item.active {
            border-left-color: #4facfe;
            background: linear-gradient(90deg, rgba(79, 172, 254, 0.2), rgba(0, 0, 0, 0.2));
            box-shadow: 0 0 15px rgba(79, 172, 254, 0.3);
        }
        
        .technique-info {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }
        
        .technique-name-wrapper {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .technique-name {
            font-size: 16px;
            font-weight: bold;
            color: #fff;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
        }
        
        .current-technique-badge {
            background: linear-gradient(135deg, #d4af37, #f1c40f);
            color: #000;
            font-size: 10px;
            font-weight: bold;
            padding: 2px 6px;
            border-radius: 8px;
            text-shadow: none;
            box-shadow: 0 0 8px rgba(212, 175, 55, 0.4);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .technique-item.current-technique {
            border-left-color: #d4af37;
            background: linear-gradient(90deg, rgba(212, 175, 55, 0.2), rgba(0, 0, 0, 0.2));
            box-shadow: 0 0 15px rgba(212, 175, 55, 0.3);
        }
        
        .technique-type {
            font-size: 12px;
            color: #bdc3c7;
        }
        
        .technique-data {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
        }
        
        .technique-level-display {
            font-size: 14px;
            color: #4facfe;
            font-weight: bold;
        }
        
        .technique-source {
            font-size: 11px;
            color: #95a5a6;
        }

        /* 🎨 新版丹药界面样式 */
        .dan-medicine-category {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            margin-bottom: 15px;
            padding: 12px;
        }

        .dan-medicine-category-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
            padding-bottom: 8px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .dan-medicine-category-header .dan-medicine-icon {
            width: 30px;
            height: 30px;
            font-size: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .dan-medicine-category-header .dan-medicine-name {
            font-size: 14px;
            font-weight: bold;
            color: #fff;
        }

        .dan-medicine-tiers {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .dan-medicine-tier-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 6px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .tier-info {
            min-width: 70px;
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .tier-name {
            font-size: 12px;
            font-weight: bold;
            color: #fff;
        }

        .tier-count {
            font-size: 10px;
            color: rgba(255, 255, 255, 0.7);
        }

        .tier-progress-container {
            flex: 1;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
        }

        .tier-progress {
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .no-usage-hint {
            text-align: center;
            color: rgba(255, 255, 255, 0.5);
            font-size: 12px;
            padding: 10px;
            font-style: italic;
        }

        /* 丹毒警告样式优化 */
        .dan-medicine-warning {
            margin-top: 15px;
            padding: 12px;
            border-radius: 8px;
            border: 2px solid rgba(46, 204, 113, 0.3);
            background: rgba(46, 204, 113, 0.2);
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }

        .warning-icon {
            font-size: 20px;
        }

        .warning-text {
            font-size: 13px;
            font-weight: bold;
        }

        /* 响应式调整 */
        @media (max-width: 480px) {
            .dan-medicine-category {
                padding: 10px;
                margin-bottom: 12px;
            }

            .dan-medicine-tier-item {
                padding: 6px;
                gap: 8px;
            }

            .tier-info {
                min-width: 60px;
            }

            .tier-name {
                font-size: 11px;
            }

            .tier-count {
                font-size: 9px;
            }

            .tier-progress-container {
                height: 6px;
            }
        }

        /* 🔧 PWA模式下的属性详情弹窗特殊修复 */
        @media (display-mode: standalone) {
            .attribute-detail-modal {
                /* PWA模式下的z-index确保在最顶层 */
                z-index: 9999 !important;
                /* PWA模式下的定位修复 */
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                bottom: 0 !important;
                /* PWA模式下的尺寸修复 */
                width: 100vw !important;
                height: 100vh !important;
                /* PWA模式下的背景修复 */
                background: rgba(0, 0, 0, 0.8) !important;
                /* PWA模式下的对齐修复 */
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                /* PWA模式下的padding调整 */
                padding: env(safe-area-inset-top, 20px) 15px calc(90px + env(safe-area-inset-bottom, 20px)) 15px !important;
            }
            
            .attribute-detail-content {
                /* PWA模式下的内容区域修复 */
                width: 90% !important;
                max-width: 380px !important;
                max-height: calc(100vh - env(safe-area-inset-top, 20px) - env(safe-area-inset-bottom, 20px) - 140px) !important;
                /* PWA模式下的显示修复 */
                display: flex !important;
                flex-direction: column !important;
                /* PWA模式下的定位修复 */
                position: relative !important;
                /* PWA模式下的滚动修复 */
                overflow: hidden !important;
            }
            
            .attribute-detail-body {
                /* PWA模式下的滚动区域修复 */
                overflow-y: auto !important;
                -webkit-overflow-scrolling: touch !important;
                flex: 1 !important;
                /* PWA模式下的内容显示修复 */
                max-height: none !important;
            }
        }

        /* 🎭 套装特殊效果样式 */
        .set-effects-container {
            background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(255, 107, 107, 0.05));
            border: 1px solid rgba(255, 107, 107, 0.3);
            border-radius: 12px;
            padding: 15px;
            margin-top: 10px;
        }

        .set-effects-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .set-effect-item {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            border: 1px solid rgba(255, 107, 107, 0.4);
            border-radius: 8px;
            padding: 12px 15px;
            display: flex;
            align-items: center;
            gap: 12px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .set-effect-item:hover {
            background: linear-gradient(135deg, rgba(255, 107, 107, 0.2), rgba(255, 107, 107, 0.1));
            border-color: rgba(255, 107, 107, 0.6);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .set-effect-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(to bottom, #ff6b6b, #ff8e8e);
            border-radius: 0 2px 2px 0;
        }

        .set-effect-icon {
            font-size: 20px;
            min-width: 24px;
            text-align: center;
            filter: drop-shadow(0 0 3px rgba(255, 107, 107, 0.5));
        }

        .set-effect-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .set-effect-name {
            font-size: 14px;
            font-weight: bold;
            color: #ff6b6b;
            text-shadow: 0 0 5px rgba(255, 107, 107, 0.3);
        }

        .set-effect-description {
            font-size: 12px;
            color: #e0e0e0;
            line-height: 1.4;
        }

        .set-effect-pieces {
            font-size: 11px;
            color: #ffd700;
            font-weight: bold;
            background: rgba(255, 215, 0, 0.1);
            padding: 2px 6px;
            border-radius: 4px;
            border: 1px solid rgba(255, 215, 0, 0.3);
            min-width: fit-content;
        }

        /* 空状态样式 */
        .set-effects-empty {
            text-align: center;
            color: #888;
            font-style: italic;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border: 1px dashed rgba(255, 255, 255, 0.2);
        }