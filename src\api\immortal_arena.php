<?php

/**
 * 升仙大会竞技系统核心API
 * 功能：匹配、战斗、记录、排行等
 * 创建时间：2025年6月17日
 */



// 引入必要的文件 - functions.php会自动引入setting.php
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/equipment_stats_manager.php';

// 🔧 临时修复：前置声明关键函数，防止"未定义函数"错误
if (!function_exists('getArenaInfo')) {
    function getArenaInfo($pdo, $characterId)
    {
        try {
            // 检查每日重置
            checkDailyReset($pdo, $characterId);

            // 获取角色基本信息
            $stmt = $pdo->prepare("SELECT
                c.*,
                rl.realm_name,
                r.rank_name,
                r.rank_color,
                r.reward_multiplier,
                r.required_points as current_rank_required_points
            FROM characters c
            LEFT JOIN realm_levels rl ON c.realm_id = rl.id
            LEFT JOIN immortal_arena_ranks r ON c.arena_rank_level = r.rank_level
            WHERE c.id = ?");
            $stmt->execute([$characterId]);
            $character = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$character) {
                throw new Exception('角色不存在');
            }

            // 计算道行值
            $daoPower = calculateDaoPower($pdo, $character);

            // 更新道行值到数据库
            $updateStmt = $pdo->prepare("UPDATE characters SET arena_dao_power = ? WHERE id = ?");
            $updateStmt->execute([$daoPower, $characterId]);

            // 获取今日战绩
            $todayStmt = $pdo->prepare("SELECT
                COUNT(*) as total_today,
                SUM(CASE WHEN battle_result = 'win' THEN 1 ELSE 0 END) as wins_today,
                SUM(spirit_stone_reward) as stones_today
            FROM immortal_arena_records
            WHERE character_id = ? AND DATE(created_at) = CURDATE()");
            $todayStmt->execute([$characterId]);
            $todayStats = $todayStmt->fetch(PDO::FETCH_ASSOC);

            // 计算剩余挑战次数
            $remainingFreeAttempts = max(0, 10 - $character['arena_daily_attempts']);
            $purchasedAttempts = $character['arena_purchased_attempts'];
            $totalRemainingAttempts = $remainingFreeAttempts + $purchasedAttempts;
            $remainingPurchases = max(0, 10 - $character['arena_purchased_attempts']);

            // 获取下一个段位信息
            $nextRankStmt = $pdo->prepare("SELECT rank_name, rank_color, required_points FROM immortal_arena_ranks WHERE rank_level = ?");
            $nextRankStmt->execute([$character['arena_rank_level'] + 1]);
            $nextRank = $nextRankStmt->fetch(PDO::FETCH_ASSOC);

            echo json_encode([
                'success' => true,
                'arena_info' => [
                    'character_name' => $character['character_name'],
                    'character_avatar' => $character['avatar_image'],
                    'realm_name' => $character['realm_name'] ?: '开光期',
                    'realm_id' => $character['realm_id'],
                    'dao_power' => $daoPower,
                    'rank_level' => $character['arena_rank_level'],
                    'rank_name' => $character['rank_name'] ?: '练气期',
                    'rank_color' => $character['rank_color'] ?: '#FFFFFF',
                    'reward_multiplier' => floatval($character['reward_multiplier'] ?: 1.0),
                    'rank_points' => intval($character['arena_rank_points'] ?: 0), // 🔧 修复：使用真实积分
                    'rank_required_points' => intval($character['current_rank_required_points'] ?: 0), // 🔧 修复：当前段位所需积分
                    'next_rank' => $nextRank ? [
                        'rank_name' => $nextRank['rank_name'],
                        'required_points' => intval($nextRank['required_points'] ?: 0), // 🔧 修复：下一段位所需积分
                        'rank_color' => $nextRank['rank_color'],
                        'points_needed' => max(0, intval($nextRank['required_points'] ?: 0) - intval($character['arena_rank_points'] ?: 0)) // 🔧 修复：还需要的积分
                    ] : null,
                    'daily_attempts' => $character['arena_daily_attempts'],
                    'remaining_free_attempts' => $remainingFreeAttempts,
                    'purchased_attempts' => $purchasedAttempts,
                    'total_remaining_attempts' => $totalRemainingAttempts,
                    'remaining_purchases' => $remainingPurchases,
                    'total_wins' => $character['arena_total_wins'],
                    'total_battles' => $character['arena_total_battles'],
                    'win_streak' => $character['arena_win_streak'],
                    'best_streak' => $character['arena_best_streak'],
                    'win_rate' => $character['arena_total_battles'] > 0 ?
                        round(($character['arena_total_wins'] / $character['arena_total_battles']) * 100, 1) : 0,
                    'today_stats' => [
                        'battles' => intval($todayStats['total_today']),
                        'wins' => intval($todayStats['wins_today']),
                        'stones_earned' => intval($todayStats['stones_today'])
                    ]
                ]
            ]);
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
}

// 🔧 战力计算辅助函数
if (!function_exists('getCombatStats')) {
    function getCombatStats($pdo, $characterData)
    {
        $stats = [
            'physical_attack' => 0,
            'immortal_attack' => 0,
            'physical_defense' => 0,
            'immortal_defense' => 0,
            'hp_bonus' => 0,
            'mp_bonus' => 0,
            'speed_bonus' => 0,
            'critical_bonus' => 0,
            'critical_damage' => 0,
            'accuracy_bonus' => 0,
            'dodge_bonus' => 0,
            'critical_resistance' => 0
        ];

        try {
            // 🔧 修复：获取装备属性
            $equipmentStats = EquipmentStatsManager::getAllEquipmentStats($pdo, $characterData['id']);
            foreach ($equipmentStats as $key => $value) {
                if (isset($stats[$key])) {
                    $stats[$key] = floatval($value);
                }
            }

            // 🔧 修复：获取套装加成（这是之前遗漏的重要部分！）
            $setBonus = getCharacterSetBonus($pdo, $characterData['id']);

            // 🔧 修复：映射套装属性到战斗属性 - 统一使用正确的数据库字段名
            $setBonusMapping = [
                'physical_attack' => 'physical_attack',
                'immortal_attack' => 'immortal_attack',
                'physical_defense' => 'physical_defense',
                'immortal_defense' => 'immortal_defense',
                'hp_bonus' => 'hp_bonus',           // 修正：统一使用hp_bonus
                'mp_bonus' => 'mp_bonus',           // 修正：统一使用mp_bonus
                'speed_bonus' => 'speed_bonus',     // 修正：统一使用speed_bonus
                'critical_bonus' => 'critical_bonus',  // 修正：统一使用critical_bonus
                'critical_damage' => 'critical_damage',
                'accuracy_bonus' => 'accuracy_bonus',   // 修正：统一使用accuracy_bonus
                'dodge_bonus' => 'dodge_bonus',    // 修正：统一使用dodge_bonus
                'critical_resistance' => 'critical_resistance'
            ];

            foreach ($setBonusMapping as $setKey => $statKey) {
                if (isset($setBonus[$setKey]) && isset($stats[$statKey])) {
                    $stats[$statKey] += floatval($setBonus[$setKey]);
                }
            }
        } catch (Exception $e) {
            error_log("获取装备/套装属性失败: " . $e->getMessage());
        }

        // 添加基础属性
        $stats['base_physique'] = intval($characterData['physique'] ?: 10);
        $stats['base_spirit'] = intval($characterData['spirit'] ?: 10);
        $stats['base_constitution'] = intval($characterData['constitution'] ?: 10);
        $stats['base_comprehension'] = intval($characterData['comprehension'] ?: 10);
        $stats['base_agility'] = intval($characterData['agility'] ?: 10);

        // 如果装备+套装属性为0，使用基础属性补充
        if ($stats['physical_attack'] == 0 && $stats['immortal_attack'] == 0) {
            $stats['physical_attack'] = ($stats['base_physique'] + $stats['base_spirit']) * 2;
        }

        if ($stats['physical_defense'] == 0 && $stats['immortal_defense'] == 0) {
            $stats['physical_defense'] = $stats['base_constitution'] * 3;
        }

        return $stats;
    }
}

if (!function_exists('calculateAttackPower')) {
    function calculateAttackPower($stats)
    {
        // 基础攻击力
        $baseAttack = $stats['physical_attack'] + $stats['immortal_attack'];

        // 暴击加成
        $criticalMultiplier = 1 + ($stats['critical_bonus'] / 100) * (1 + $stats['critical_damage'] / 100);

        // 命中加成
        $accuracyMultiplier = 1 + ($stats['accuracy_bonus'] / 100) * 0.5;

        // 综合攻击力
        return $baseAttack * $criticalMultiplier * $accuracyMultiplier;
    }
}

if (!function_exists('calculateDefensePower')) {
    function calculateDefensePower($stats)
    {
        // 基础防御力
        $baseDefense = $stats['physical_defense'] + $stats['immortal_defense'];

        // 闪避加成
        $dodgeMultiplier = 1 + ($stats['dodge_bonus'] / 100) * 0.8;

        // 免暴加成
        $critResistMultiplier = 1 + ($stats['critical_resistance'] / 100) * 0.6;

        // 生命值加成
        $hpMultiplier = 1 + ($stats['hp_bonus'] / 1000); // 每1000血量增加1倍防御

        // 综合防御力
        return $baseDefense * $dodgeMultiplier * $critResistMultiplier * $hpMultiplier;
    }
}

if (!function_exists('calculateUtilityPower')) {
    function calculateUtilityPower($stats)
    {
        // 速度价值
        $speedValue = $stats['speed_bonus'] * 5;

        // 法力值价值
        $mpValue = $stats['mp_bonus'] * 0.5;

        // 基础属性价值
        $baseValue = ($stats['base_physique'] + $stats['base_spirit'] +
            $stats['base_constitution'] + $stats['base_comprehension'] +
            $stats['base_agility']) * 2;

        return $speedValue + $mpValue + $baseValue;
    }
}

// 🔧 重构：基于实际战斗属性的道行值计算函数
if (!function_exists('calculateDaoPower')) {
    function calculateDaoPower($pdo, $characterData)
    {
        // 🔧 重构：基于实际战斗属性的道行值计算系统

        // 1. 获取角色的完整战斗属性
        $combatStats = getCombatStats($pdo, $characterData);

        // 2. 计算核心战力分数
        $attackPower = calculateAttackPower($combatStats);
        $defensePower = calculateDefensePower($combatStats);
        $utilityPower = calculateUtilityPower($combatStats);

        // 3. 境界基础分数（作为基础，不是主导）
        $realmLevel = isset($characterData['realm_level']) ? intval($characterData['realm_level']) : intval($characterData['realm_id']);
        $realmBase = $realmLevel * 30; // 降低境界基础分数

        // 4. 战绩加成（经验值）
        $wins = intval($characterData['arena_total_wins'] ?: 0);
        $battles = intval($characterData['arena_total_battles'] ?: 0);
        $winRate = $battles > 0 ? $wins / $battles : 0;
        $experienceBonus = ($wins * 3) + ($winRate * 50); // 胜场数 + 胜率加成

        // 5. 🔧 修复：重新平衡道行值计算，避免装备差距过大
        $baseCombatPower = ($attackPower + $defensePower) * 0.3; // 降低装备影响
        $realmPower = $realmBase * 0.5; // 提高境界影响
        $utilityAndExperience = ($utilityPower + $experienceBonus) * 0.2;

        $totalDaoPower = intval($baseCombatPower + $realmPower + $utilityAndExperience);

        // 6. 🔧 修复：设置更高的最低道行值，缩小差距
        $minDaoPower = max(500, $realmLevel * 40 + 300);

        // 7. 记录详细计算过程（用于调试）
        error_log("🔧 [道行计算] 角色: {$characterData['character_name']} | 攻击力: " . round($attackPower) .
            " | 防御力: " . round($defensePower) . " | 辅助: " . round($utilityPower) .
            " | 境界基础: {$realmBase} | 经验: " . round($experienceBonus) .
            " | 总道行: {$totalDaoPower}");

        return max($minDaoPower, $totalDaoPower);
    }
}

// 🔧 前置定义checkDailyReset函数
if (!function_exists('checkDailyReset')) {
    function checkDailyReset($pdo, $characterId)
    {
        $stmt = $pdo->prepare("SELECT arena_last_reset FROM characters WHERE id = ?");
        $stmt->execute([$characterId]);
        $lastReset = $stmt->fetchColumn();

        $today = date('Y-m-d');
        if ($lastReset !== $today) {
            // 重置每日数据（只重置免费次数，不重置购买次数）
            $resetStmt = $pdo->prepare("UPDATE characters SET
                arena_daily_attempts = 0,
                arena_last_reset = ?
            WHERE id = ?");
            $resetStmt->execute([$today, $characterId]);
        }
    }
}

// 🔧 前置声明关键函数，防止"未定义函数"错误

// 🔧 前置定义verifyArenaAccess函数
if (!function_exists('verifyArenaAccess')) {
    function verifyArenaAccess($pdo, $characterId)
    {
        try {
            // 检查每日重置
            checkDailyReset($pdo, $characterId);

            // 获取角色竞技场数据
            $stmt = $pdo->prepare("SELECT
                arena_daily_attempts,
                arena_purchased_attempts
            FROM characters WHERE id = ?");
            $stmt->execute([$characterId]);
            $data = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$data) {
                throw new Exception('角色不存在');
            }

            // 计算剩余次数
            $remainingFree = max(0, 10 - $data['arena_daily_attempts']);
            $totalRemaining = $remainingFree + $data['arena_purchased_attempts'];

            echo json_encode([
                'success' => true,
                'remaining_attempts' => $totalRemaining,
                'can_battle' => $totalRemaining > 0,
                'message' => $totalRemaining > 0 ? '验证通过' : '挑战次数不足'
            ]);
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'remaining_attempts' => 0,
                'can_battle' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
}

// 🔧 前置定义getOpponentData函数
if (!function_exists('getOpponentData')) {
    function getOpponentData($pdo, $characterId)
    {
        error_log("🔧 [调试] getOpponentData函数被调用，characterId: $characterId");
        try {
            // 支持GET和POST请求
            $opponentId = null;
            $isAi = false;

            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                $input = json_decode(file_get_contents('php://input'), true);
                $opponentId = isset($input['opponent_id']) ? $input['opponent_id'] : null;
                $isAi = isset($input['is_ai']) ? $input['is_ai'] : false;
                $opponentName = isset($input['opponent_name']) ? $input['opponent_name'] : null;
            } else {
                $opponentId = isset($_GET['opponent_id']) ? $_GET['opponent_id'] : null;
                $isAi = isset($_GET['is_ai']) ? ($_GET['is_ai'] === '1' || $_GET['is_ai'] === 'true') : false;
                $opponentName = isset($_GET['opponent_name']) ? $_GET['opponent_name'] : null;
            }

            if ($isAi || strpos($opponentId, 'ai_') === 0) {
                // 🔧 修复：AI对手数据生成
                error_log("🔧 [调试] 开始生成AI对手数据，预设名字: " . ($opponentName ?: '无'));
                $opponentData = generateAiOpponentData($pdo, null, $characterId, $opponentName);
                error_log("🔧 [调试] AI对手数据生成完成");

                // 🔧 修复：简化武器技能设置，避免复杂函数调用导致超时
                error_log("🔧 [调试] 开始设置武器技能");

                // AI对手已经在generateAiOpponentData中设置了武器技能，这里只需要确保字段存在
                if (!isset($opponentData['weapon_skills']) || empty($opponentData['weapon_skills'])) {
                    $opponentData['weapon_skills'] = ['feijian', 'feijian', 'feijian', 'feijian', 'feijian', 'feijian'];
                }
                if (!isset($opponentData['skills']) || empty($opponentData['skills'])) {
                    $opponentData['skills'] = $opponentData['weapon_skills'];
                }
                if (!isset($opponentData['weapon_skills_data'])) {
                    $opponentData['weapon_skills_data'] = [];
                }

                error_log("🔧 [调试] 武器技能设置完成，技能数量: " . count($opponentData['weapon_skills']));

                // 🔧 设置技能序列
                $opponentData['skill_sequence'] = [0, 1, 2, 3, 4, 5];

                // 🔧 标记为AI对手，战斗系统将使用玩家技能而非怪物技能
                $opponentData['isPlayer'] = true;
                $opponentData['isRealPlayer'] = false;

                // 🔧 修复：AI对手已经在generateAiOpponentData中计算了道行值，不需要重复计算
                // $opponentData['dao_power'] = calculateDaoPower($pdo, $opponentData); // 删除这行，避免错误

            } else {
                // 🔧 修复：真实玩家对手数据
                $opponentData = getRealPlayerData($pdo, $opponentId);

                // 🔧 修复：获取真实玩家的武器技能
                $weaponSkills = getCharacterWeaponSkills($pdo, $opponentId);
                $opponentData['weapon_skills'] = $weaponSkills;

                // 🔧 修复：获取完整武器技能数据（包含animation_model）
                $weaponSkillsData = getCharacterWeaponSkillsData($pdo, $opponentId);
                $opponentData['weapon_skills_data'] = $weaponSkillsData;

                // 🔧 修复：获取技能序列
                $skillSequence = isset($opponentData['arena_skill_sequence']) ? $opponentData['arena_skill_sequence'] : '0,1,2,3,4,5';
                $opponentData['skill_sequence'] = array_map('intval', explode(',', $skillSequence));

                // 🔧 修复：转换为战斗系统需要的格式
                $opponentData['id'] = $opponentData['id'] ?: null;
                $opponentData['name'] = $opponentData['character_name'];
                $opponentData['hp'] = $opponentData['max_hp'];
                $opponentData['mp'] = $opponentData['max_mp'];
                $opponentData['currentHp'] = $opponentData['max_hp'];
                $opponentData['currentMp'] = $opponentData['max_mp'];

                // 🔧 修复：标记为真实玩家
                $opponentData['isPlayer'] = true;
                $opponentData['isRealPlayer'] = true;
                $opponentData['isAi'] = false;
                $opponentData['is_ai'] = false;

                // 🔧 修复：确保头像字段完整
                if (empty($opponentData['avatar_image'])) {
                    $opponentData['avatar_image'] = 'ck.png'; // 默认头像
                }
                $opponentData['character_avatar'] = $opponentData['avatar_image'];
                $opponentData['avatarImage'] = $opponentData['avatar_image'];
                $opponentData['modelImage'] = $opponentData['avatar_image'];

                // 🔧 修复：防御属性兼容性（战斗系统可能需要单一defense字段）
                $opponentData['attack'] = $opponentData['physical_attack']; // 兼容旧版本
                $opponentData['defense'] = $opponentData['physical_defense']; // 兼容旧版本

                // 🔧 修复：技能系统兼容性
                if (empty($opponentData['skills'])) {
                    $opponentData['skills'] = $weaponSkills; // 兼容怪物格式
                }

                error_log("🏆 [真实对手] 完整数据: {$opponentData['character_name']}, HP: {$opponentData['max_hp']}, 物攻: {$opponentData['physical_attack']}, 法攻: {$opponentData['immortal_attack']}, 头像: {$opponentData['avatar_image']}, 技能数: " . count($weaponSkills) . ", 技能数据数: " . count($weaponSkillsData));
                error_log("🎯 [技能数据调试] weapon_skills_data: " . json_encode($weaponSkillsData));
            }

            echo json_encode([
                'success' => true,
                'opponent_data' => $opponentData
            ]);
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
}

// 🔧 前置定义generateAiOpponentData函数
if (!function_exists('generateAiOpponentData')) {
    function generateAiOpponentData($pdo, $templateId, $playerCharacterId, $predefinedName = null)
    {
        error_log("🔧 [AI生成] 开始生成AI对手数据，玩家ID: $playerCharacterId");

        try {
            // 获取玩家基础数据
            $stmt = $pdo->prepare("SELECT c.*, COALESCE(r.realm_name, '未知境界') as realm_name
                FROM characters c
                LEFT JOIN realm_levels r ON c.realm_id = r.id
                WHERE c.id = ?");
            $stmt->execute([$playerCharacterId]);
            $playerData = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$playerData) {
                throw new Exception("无法获取玩家数据，玩家ID: $playerCharacterId");
            }

            error_log("🔧 [AI生成] 玩家基础数据获取成功: " . $playerData['character_name']);

            // 🔧 修复：生成AI名字 - 优先使用预设名字，确保一致性
            $aiNames = ['玄天', '紫霄', '青云', '明月', '星辰', '寒冰', '烈火', '疾风'];
            $seed = $templateId ? intval($templateId) : ($playerCharacterId * 7 + 13); // 固定算法
            $nameIndex = $seed % count($aiNames);

            if ($predefinedName && strpos($predefinedName, '灵智傀儡·') === 0) {
                // 如果已有预设名字，直接使用
                $aiCharacterName = $predefinedName;
                $randomName = str_replace('灵智傀儡·', '', $predefinedName);
                // 根据名字找到对应的索引，用于生成一致的ID
                $nameIndex = array_search($randomName, $aiNames);
                if ($nameIndex === false) $nameIndex = 0; // 如果找不到，使用默认索引
            } else {
                // 否则基于固定种子生成
                $randomName = $aiNames[$nameIndex];
                $aiCharacterName = '灵智傀儡·' . $randomName;
            }

            // 基于玩家数据生成相近实力的AI（90-95%强度）
            $strengthFactor = 0.9 + (rand(0, 5) * 0.01); // 90%-95%强度

            // 🔧 修复：获取玩家的完整属性（包括装备加成），确保AI强度准确
            $playerTotalAttributes = null;
            try {
                // 尝试获取玩家完整属性
                if (function_exists('EquipmentStatsManager') && class_exists('EquipmentStatsManager')) {
                    $equipmentStats = EquipmentStatsManager::getAllEquipmentStats($pdo, $playerCharacterId);
                    $rootAttributes = getRootAttributes($pdo, $playerCharacterId);
                    $rootBonus = isset($rootAttributes['spiritual_root_bonus']) ? $rootAttributes['spiritual_root_bonus'] : [];
                    $setBonus = getCharacterSetBonus($pdo, $playerCharacterId);
                    $playerTotalAttributes = calculateCharacterAttributes($playerData, $equipmentStats, [], $rootBonus, $setBonus);

                    error_log("🔧 [AI强度校准] 成功获取玩家完整属性");
                }
            } catch (Exception $e) {
                error_log("🔧 [AI强度校准] 完整属性计算失败，使用基础属性: " . $e->getMessage());
            }

            if ($playerTotalAttributes && isset($playerTotalAttributes['hp_total'])) {
                // 🔧 使用玩家完整属性计算AI（90-95%强度）
                error_log("🔧 [AI强度校准] 使用完整属性计算AI强度");

                $aiHp = max(100, intval($playerTotalAttributes['hp_total'] * $strengthFactor));
                $aiMp = max(80, intval($playerTotalAttributes['mp_total'] * $strengthFactor));
                $aiPhysicalAttack = max(10, intval($playerTotalAttributes['physical_attack_total'] * $strengthFactor));
                $aiImmortalAttack = max(10, intval($playerTotalAttributes['immortal_attack_total'] * $strengthFactor));
                $aiPhysicalDefense = max(5, intval($playerTotalAttributes['physical_defense_total'] * $strengthFactor));
                $aiImmortalDefense = max(5, intval($playerTotalAttributes['immortal_defense_total'] * $strengthFactor));
                $aiSpeed = max(10, intval($playerTotalAttributes['speed_total'] * $strengthFactor));

                error_log("🔧 [AI强度校准] 玩家完整属性: HP={$playerTotalAttributes['hp_total']}, 物攻={$playerTotalAttributes['physical_attack_total']}, 法攻={$playerTotalAttributes['immortal_attack_total']}");
            } else {
                // 🔧 备用方案：基于基础属性计算，但使用更高的倍数来补偿装备差距
                error_log("🔧 [AI强度校准] 使用基础属性计算AI强度（补偿装备差距）");

                // 🔧 提高基础属性倍数，补偿装备加成的缺失
                $baseHp = intval($playerData['physique']) * 25 + 500; // 提高倍数
                $baseMp = intval($playerData['spirit']) * 20 + 400;
                $basePhysicalAttack = intval($playerData['physique']) * 8 + intval($playerData['agility']) * 6; // 提高倍数
                $baseImmortalAttack = intval($playerData['spirit']) * 8 + intval($playerData['comprehension']) * 6;
                $basePhysicalDefense = intval($playerData['constitution']) * 6;
                $baseImmortalDefense = intval($playerData['spirit']) * 4 + intval($playerData['constitution']) * 4;
                $baseSpeed = intval($playerData['agility']) * 5;

                // 应用强度因子
                $aiHp = max(100, intval($baseHp * $strengthFactor));
                $aiMp = max(80, intval($baseMp * $strengthFactor));
                $aiPhysicalAttack = max(10, intval($basePhysicalAttack * $strengthFactor));
                $aiImmortalAttack = max(10, intval($baseImmortalAttack * $strengthFactor));
                $aiPhysicalDefense = max(5, intval($basePhysicalDefense * $strengthFactor));
                $aiImmortalDefense = max(5, intval($baseImmortalDefense * $strengthFactor));
                $aiSpeed = max(10, intval($baseSpeed * $strengthFactor));

                error_log("🔧 [AI强度校准] 玩家基础属性: 体质={$playerData['physique']}, 精神={$playerData['spirit']}, 敏捷={$playerData['agility']}");
            }

            error_log("🔧 [AI生成] AI最终属性: HP={$aiHp}, 物攻={$aiPhysicalAttack}, 法攻={$aiImmortalAttack}, 物防={$aiPhysicalDefense}, 法防={$aiImmortalDefense}");

            // 🔧 修复：使用实际存在的头像文件
            $playerAvatars = ['ck.png', 'gf.png', 'fy.png', 'my.png', 'xx.png', 'yx.png', 'yg.png', 'zd.png', 'zm.png', 'zj.png'];
            $randomAvatar = $playerAvatars[array_rand($playerAvatars)];

            error_log("🔧 [AI头像] 选择头像: $randomAvatar");

            // 计算道行值
            $playerDaoPower = intval($playerData['arena_dao_power'] ?: 1000);
            $aiDaoPower = max(100, intval($playerDaoPower * $strengthFactor));

            error_log("🔧 [AI道行值] 玩家道行: $playerDaoPower, AI道行: $aiDaoPower");

            // 获取武器技能（简化版）
            $weaponSkills = ['feijian', 'feijian', 'feijian', 'feijian', 'feijian', 'feijian']; // 默认技能
            $weaponSkillsData = [];

            // 尝试获取玩家武器技能，但不让它阻塞整个流程
            try {
                if (function_exists('getCharacterWeaponSkills')) {
                    $playerWeaponSkills = getCharacterWeaponSkills($pdo, $playerCharacterId);
                    if ($playerWeaponSkills && count($playerWeaponSkills) > 0) {
                        $weaponSkills = $playerWeaponSkills;
                    }
                }
                if (function_exists('getCharacterWeaponSkillsData')) {
                    $weaponSkillsData = getCharacterWeaponSkillsData($pdo, $playerCharacterId);
                }
                error_log("🔧 [AI生成] 武器技能获取成功，技能数量: " . count($weaponSkills));
            } catch (Exception $e) {
                error_log("🔧 [AI生成] 武器技能获取失败，使用默认技能: " . $e->getMessage());
            }

            // 🔧 修复：使用固定ID确保同一对手ID一致
            $aiId = 'ai_' . $playerCharacterId . '_' . $nameIndex . '_' . ($templateId ?: 'default');

            $aiData = [
                'id' => $aiId,
                'character_id' => $aiId,
                'character_name' => $aiCharacterName,
                'name' => $aiCharacterName,
                'realm_id' => $playerData['realm_id'], // 🔧 修复：使用玩家境界ID
                'realm_name' => $playerData['realm_name'], // 🔧 修复：使用玩家境界名称
                'level' => $playerData['realm_id'], // 🔧 修复：等级与境界ID一致
                'dao_power' => $aiDaoPower,

                // 战斗属性
                'physical_attack' => $aiPhysicalAttack,
                'immortal_attack' => $aiImmortalAttack,
                'physical_defense' => $aiPhysicalDefense,
                'immortal_defense' => $aiImmortalDefense,

                // HP/MP
                'hp' => $aiHp,
                'max_hp' => $aiHp,
                'currentHp' => $aiHp,
                'mp' => $aiMp,
                'max_mp' => $aiMp,
                'currentMp' => $aiMp,

                // 兼容字段
                'attack' => $aiPhysicalAttack,
                'defense' => $aiPhysicalDefense,
                'speed' => $aiSpeed,
                'speed_bonus' => $aiSpeed,
                'critical_bonus' => 5.0,
                'critical_damage' => 1.5,
                'critical_resistance' => 0,
                'accuracy_bonus' => 80.0,
                'dodge_bonus' => 10.0,

                // 头像设置
                'character_avatar' => $randomAvatar,
                'avatarImage' => $randomAvatar,
                'avatar_image' => $randomAvatar,
                'modelImage' => $randomAvatar,

                // AI标记
                'isPlayer' => true, // 用于技能系统
                'isRealPlayer' => false, // 标记为AI
                'isAi' => true,
                'is_ai' => true,
                'weapon_skills' => $weaponSkills,
                'weapon_skills_data' => $weaponSkillsData,
                'skill_sequence' => [0, 1, 2, 3, 4, 5],
                'skills' => $weaponSkills
            ];

            // 🔧 新增：为AI对手生成套装特殊效果
            $aiData['set_special_effects'] = generateAiSetEffects($pdo, $playerCharacterId, $aiData);
            error_log("🔧 [AI生成] AI套装特殊效果: " . json_encode($aiData['set_special_effects']));

            error_log("🔧 [AI生成] AI对手数据生成完成: " . $aiCharacterName . ", 套装效果数: " . count($aiData['set_special_effects']));
            return $aiData;
        } catch (Exception $e) {
            error_log("🔧 [AI生成] 生成AI对手数据失败: " . $e->getMessage());
            throw new Exception("生成AI对手失败: " . $e->getMessage());
        }
    }
}

// 🔧 新增：为AI对手生成套装特殊效果
if (!function_exists('generateAiSetEffects')) {
    function generateAiSetEffects($pdo, $playerCharacterId, $aiData)
    {
        try {
            // 获取所有套装特殊效果作为模板
            $stmt = $pdo->query("SELECT set_name, effects FROM game_item_sets WHERE effects IS NOT NULL ORDER BY RAND() LIMIT 3");
            $availableSets = $stmt->fetchAll(PDO::FETCH_ASSOC);

            if (empty($availableSets)) {
                return []; // 如果没有套装数据，返回空数组
            }

            $aiSetEffects = [];

            // 随机选择1-2个套装效果给AI
            $numEffects = rand(1, 2);
            $selectedSets = array_slice($availableSets, 0, $numEffects);

            foreach ($selectedSets as $set) {
                $effects = json_decode($set['effects'], true);
                if (!$effects) continue;

                // 随机选择4件套或6件套效果
                $pieceTypes = ['four_piece', 'six_piece'];
                $selectedPieceType = $pieceTypes[array_rand($pieceTypes)];

                if (isset($effects[$selectedPieceType]['special_effect'])) {
                    $aiSetEffects[] = [
                        'set_name' => $set['set_name'],
                        'pieces' => $selectedPieceType === 'four_piece' ? 4 : 6,
                        'effect' => $effects[$selectedPieceType]['special_effect']
                    ];
                }
            }

            error_log("🔧 [AI套装] 为AI生成了 " . count($aiSetEffects) . " 个套装特殊效果");
            return $aiSetEffects;
        } catch (Exception $e) {
            error_log("🔧 [AI套装] 生成AI套装特殊效果失败: " . $e->getMessage());
            return [];
        }
    }
}

// 🔧 前置定义getCharacterWeaponSkills函数
if (!function_exists('getCharacterWeaponSkills')) {
    function getCharacterWeaponSkills($pdo, $characterId)
    {
        $stmt = $pdo->prepare("SELECT
            e.id as equipment_id,
            gi.item_name,
            COALESCE(s.skill_name, 'feijian') as skill_name
        FROM character_equipment e
        JOIN game_items gi ON e.item_id = gi.id
        LEFT JOIN item_skills s ON gi.id = s.item_id
        WHERE e.character_id = ?
        AND e.slot_type = 'weapon'
        AND e.slot_index BETWEEN 1 AND 6
        ORDER BY e.slot_index ASC");

        $stmt->execute([$characterId]);
        $weapons = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $skills = [];
        for ($i = 0; $i < 6; $i++) {
            if (isset($weapons[$i])) {
                $skills[] = $weapons[$i]['skill_name'];
            } else {
                $skills[] = 'feijian'; // 默认技能
            }
        }

        return $skills;
    }
}

// 🔧 前置定义getCharacterWeaponSkillsData函数
if (!function_exists('getCharacterWeaponSkillsData')) {
    function getCharacterWeaponSkillsData($pdo, $characterId)
    {
        $stmt = $pdo->prepare("SELECT
            e.slot_index,
            e.item_id,
            gi.item_name,
            gi.icon_image,
            gi.model_image,
            COALESCE(s.skill_name, 'feijian') as skill_name,
            COALESCE(s.animation_model, 'feijian') as animation_model,
            COALESCE(s.skill_type, 'attack') as skill_type,
            COALESCE(s.mp_cost, 10) as mp_cost,
            COALESCE(s.damage_multiplier, 1.0) as damage_multiplier
        FROM character_equipment e
        JOIN game_items gi ON e.item_id = gi.id
        LEFT JOIN item_skills s ON gi.id = s.item_id
        WHERE e.character_id = ?
        AND e.slot_type = 'weapon'
        AND e.slot_index BETWEEN 1 AND 6
        ORDER BY e.slot_index ASC");

        $stmt->execute([$characterId]);
        $weapons = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $weaponSkillsData = [];
        for ($i = 0; $i < 6; $i++) {
            if (isset($weapons[$i])) {
                $weaponSkillsData[] = [
                    'slot_index' => $i,
                    'weapon_id' => $weapons[$i]['item_id'],
                    'weapon_name' => $weapons[$i]['item_name'],
                    'skill_name' => $weapons[$i]['skill_name'],
                    'animation_model' => $weapons[$i]['animation_model'],
                    'skill_type' => $weapons[$i]['skill_type'],
                    'mp_cost' => intval($weapons[$i]['mp_cost']),
                    'damage_multiplier' => floatval($weapons[$i]['damage_multiplier']),
                    'icon_image' => $weapons[$i]['icon_image'],
                    'model_image' => $weapons[$i]['model_image'] // 🔧 添加战斗武器图片
                ];
            } else {
                // 默认武器技能数据
                $weaponSkillsData[] = [
                    'slot_index' => $i,
                    'weapon_id' => null,
                    'weapon_name' => '空槽位',
                    'skill_name' => 'feijian',
                    'animation_model' => 'feijian',
                    'skill_type' => 'attack',
                    'mp_cost' => 10,
                    'damage_multiplier' => 1.0,
                    'icon_image' => null,
                    'model_image' => null // 🔧 空槽位没有武器图片
                ];
            }
        }

        return $weaponSkillsData;
    }
}

// 🔧 前置定义getRealPlayerData函数
if (!function_exists('getRealPlayerData')) {
    function getRealPlayerData($pdo, $opponentId)
    {
        // 🔧 修复：完整计算真实玩家的所有属性，包括装备、灵根、修炼加成

        // 1. 获取角色基础数据
        $stmt = $pdo->prepare("SELECT c.*,
            COALESCE(r.realm_name, '未知境界') as realm_name,
            r.realm_level,
            r.hp_multiplier,
            r.mp_multiplier,
            r.attack_multiplier,
            r.defense_multiplier,
            r.speed_multiplier,
            u.username
            FROM characters c
            LEFT JOIN realm_levels r ON c.realm_id = r.id
            LEFT JOIN users u ON c.user_id = u.id
            WHERE c.id = ?");
        $stmt->execute([$opponentId]);
        $opponent = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$opponent) {
            throw new Exception('对手数据不存在');
        }

        error_log("🏆 [真实对手] 获取基础数据: {$opponent['character_name']}, 筋骨: {$opponent['physique']}, 悟性: {$opponent['comprehension']}, 体魄: {$opponent['constitution']}, 神魂: {$opponent['spirit']}, 身法: {$opponent['agility']}");

        // 2. 引入完整的属性计算系统
        // functions.php和equipment_stats_manager.php已在文件开头包含

        // 3. 获取装备属性加成
        $equipmentStats = EquipmentStatsManager::getAllEquipmentStats($pdo, $opponentId);
        error_log("🔧 [真实对手] 装备属性: " . json_encode($equipmentStats));

        // 4. 获取灵根属性加成
        $rootAttributes = getRootAttributes($pdo, $opponentId);
        $rootBonus = isset($rootAttributes['spiritual_root_bonus']) ? $rootAttributes['spiritual_root_bonus'] : [];
        error_log("🔧 [真实对手] 灵根属性: " . json_encode($rootBonus));

        // 5. 获取套装属性加成
        $setBonus = getCharacterSetBonus($pdo, $opponentId);
        error_log("🔧 [真实对手] 套装属性: " . json_encode($setBonus));

        // 6. 计算完整属性
        $finalAttributes = calculateCharacterAttributes($opponent, $equipmentStats, [], $rootBonus, $setBonus);
        error_log("🔧 [真实对手] 最终属性: " . json_encode([
            'hp_total' => $finalAttributes['hp_total'],
            'mp_total' => $finalAttributes['mp_total'],
            'physical_attack_total' => $finalAttributes['physical_attack_total'],
            'immortal_attack_total' => $finalAttributes['immortal_attack_total'],
            'physical_defense_total' => $finalAttributes['physical_defense_total'],
            'immortal_defense_total' => $finalAttributes['immortal_defense_total'],
            'speed_total' => $finalAttributes['speed_total']
        ]));

        // 6. 使用计算出的完整属性
        $opponent['max_hp'] = intval($finalAttributes['hp_total']);
        $opponent['max_mp'] = intval($finalAttributes['mp_total']);
        $opponent['hp'] = $opponent['max_hp'];
        $opponent['mp'] = $opponent['max_mp'];
        $opponent['currentHp'] = $opponent['max_hp'];
        $opponent['currentMp'] = $opponent['max_mp'];

        // 🔧 使用正确计算的攻防属性
        $opponent['physical_attack'] = intval($finalAttributes['physical_attack_total']);
        $opponent['immortal_attack'] = intval($finalAttributes['immortal_attack_total']);
        $opponent['physical_defense'] = intval($finalAttributes['physical_defense_total']);
        $opponent['immortal_defense'] = intval($finalAttributes['immortal_defense_total']);
        $opponent['speed'] = intval($finalAttributes['speed_total']);

        // 7. 设置动态属性
        $opponent['accuracy_bonus'] = floatval($finalAttributes['accuracy_bonus_total']);
        $opponent['dodge_bonus'] = floatval($finalAttributes['dodge_bonus_total']);
        $opponent['critical_bonus'] = floatval($finalAttributes['critical_bonus_total']);
        $opponent['critical_damage'] = floatval($finalAttributes['critical_damage_total']);
        $opponent['critical_resistance'] = floatval($finalAttributes['critical_resistance_total']);

        // 8. 🔧 新增：将套装特殊效果添加到对手数据中
        $opponent['set_special_effects'] = $setBonus['special_effects'] ?? [];
        error_log("🔧 [真实对手] 套装特殊效果: " . json_encode($opponent['set_special_effects']));

        // 9. 计算并设置道行值
        $daoPower = calculateDaoPower($pdo, $opponent);
        $updateStmt = $pdo->prepare("UPDATE characters SET arena_dao_power = ? WHERE id = ?");
        $updateStmt->execute([$daoPower, $opponentId]);
        $opponent['arena_dao_power'] = $daoPower;
        $opponent['dao_power'] = $daoPower;

        error_log("🏆 [真实对手] 最终数据: {$opponent['character_name']}, 道行: $daoPower, 物攻: {$opponent['physical_attack']}, 法攻: {$opponent['immortal_attack']}, HP: {$opponent['max_hp']}, 速度: {$opponent['speed']}, 套装效果数: " . count($opponent['set_special_effects']));

        // 设置头像信息
        if (empty($opponent['avatar_image'])) {
            $opponent['avatar_image'] = 'ck.png';
        }
        $opponent['character_avatar'] = $opponent['avatar_image'];
        $opponent['avatarImage'] = $opponent['avatar_image'];
        $opponent['modelImage'] = $opponent['avatar_image'];

        // 🔧 修复：正确设置境界和等级信息
        $opponent['level'] = isset($opponent['realm_level']) ? $opponent['realm_level'] : (isset($opponent['realm_id']) ? $opponent['realm_id'] : 1);
        $opponent['realm_id'] = isset($opponent['realm_id']) ? $opponent['realm_id'] : 1;

        // 🔧 修复：确保realm_name存在且不为空
        if (empty($opponent['realm_name']) || $opponent['realm_name'] === 'null') {
            $opponent['realm_name'] = '未知境界';
        }

        error_log("🔧 [真实对手境界] 角色: {$opponent['character_name']}, realm_id: {$opponent['realm_id']}, realm_name: {$opponent['realm_name']}, level: {$opponent['level']}");

        // 🔧 新增：设置竞技场真实玩家的关键标识和技能数据
        $opponent['isPlayer'] = true; // 标记为真实玩家，触发技能序列
        $opponent['isRealPlayer'] = true; // 真实玩家标识
        $opponent['type'] = 'arena_player'; // 竞技场玩家类型
        $opponent['isAi'] = false; // 不是AI
        $opponent['is_ai'] = false; // 不是AI

        // 🔧 新增：获取真实玩家的武器技能
        $opponent['weapon_skills'] = getCharacterWeaponSkills($pdo, $opponentId);
        $opponent['weapon_skills_data'] = getCharacterWeaponSkillsData($pdo, $opponentId); // 🎯 新增：完整技能数据
        $opponent['skills'] = $opponent['weapon_skills']; // 复制技能数组
        $opponent['skill_sequence'] = [0, 1, 2, 3, 4, 5]; // 技能使用顺序

        error_log("🏆 [真实玩家] 技能设置: " . json_encode($opponent['weapon_skills']));

        error_log("🏆 [真实玩家] 完整数据: {$opponent['character_name']}, 道行: $daoPower, 物攻: {$opponent['physical_attack']}, 法攻: {$opponent['immortal_attack']}, HP: {$opponent['max_hp']}, 头像: {$opponent['avatar_image']}");

        return $opponent;
    }
}

if (!function_exists('startMatching')) {
    function startMatching($pdo, $characterId)
    {
        try {
            // 首先清理过期的匹配记录
            cleanupExpiredMatches($pdo);

            // 检查是否可以开始匹配
            $canMatch = checkCanStartMatch($pdo, $characterId);
            if (!$canMatch['success']) {
                echo json_encode($canMatch);
                return;
            }

            // 再次检查是否已在匹配池中（清理后重新检查）
            $checkStmt = $pdo->prepare("SELECT id FROM immortal_arena_match_pool WHERE character_id = ?");
            $checkStmt->execute([$characterId]);
            if ($checkStmt->rowCount() > 0) {
                // 如果仍在队列中，强制清理（可能是异常情况）
                $cleanStmt = $pdo->prepare("DELETE FROM immortal_arena_match_pool WHERE character_id = ?");
                $cleanStmt->execute([$characterId]);
            }

            // 获取角色数据
            $character = getCharacterSnapshot($pdo, $characterId);
            $daoPower = calculateDaoPower($pdo, $character);

            // 创建角色快照
            $snapshot = createCharacterSnapshot($character);

            // 尝试找到合适的对手
            $opponent = findSuitableOpponent($pdo, $characterId, $daoPower, $character['realm_id']);

            if ($opponent) {
                // 找到合适的真实玩家对手
                $powerDiff = abs($opponent['dao_power'] - $daoPower);
                $powerDiffPercent = round(($powerDiff / $daoPower) * 100, 1);

                error_log("✅ [匹配成功] 找到真实玩家对手: {$opponent['character_name']}, 道行差异: {$powerDiffPercent}%");

                echo json_encode([
                    'success' => true,
                    'match_found' => true,
                    'opponent' => [
                        'character_id' => $opponent['character_id'],
                        'character_name' => $opponent['character_name'],
                        'dao_power' => $opponent['dao_power'],
                        'realm_id' => $opponent['realm_id'],
                        'is_ai_puppet' => false,
                        'match_quality' => $powerDiffPercent <= 20 ? 'excellent' : ($powerDiffPercent <= 30 ? 'good' : 'fair')
                    ]
                ]);
            } else {
                // 🔧 优化：未找到合适对手时，直接生成AI对手而非进入匹配池
                error_log("🤖 [智能匹配] 未找到合适的真实玩家，直接生成AI对手");

                try {
                    // 生成AI对手
                    $aiOpponent = generateBalancedAiOpponent($pdo, $character, $daoPower);

                    if ($aiOpponent) {
                        echo json_encode([
                            'success' => true,
                            'match_found' => true,
                            'opponent' => $aiOpponent,
                            'message' => '为您匹配了实力相当的AI对手'
                        ]);
                    } else {
                        // AI生成失败，才进入匹配池
                        $timeoutTime = date('Y-m-d H:i:s', time() + 10); // 缩短等待时间

                        $insertStmt = $pdo->prepare("INSERT INTO immortal_arena_match_pool
                            (character_id, dao_power, realm_id, character_snapshot, match_timeout)
                            VALUES (?, ?, ?, ?, ?)");
                        $insertStmt->execute([
                            $characterId,
                            $daoPower,
                            $character['realm_id'],
                            json_encode($snapshot),
                            $timeoutTime
                        ]);

                        echo json_encode([
                            'success' => true,
                            'match_found' => false,
                            'message' => '正在寻找合适的对手...',
                            'estimated_wait' => 10
                        ]);
                    }
                } catch (Exception $e) {
                    error_log("AI对手生成失败: " . $e->getMessage());
                    echo json_encode([
                        'success' => false,
                        'message' => '匹配系统暂时不可用，请稍后再试'
                    ]);
                }
            }
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
}

if (!function_exists('cancelMatching')) {
    function cancelMatching($pdo, $characterId)
    {
        try {
            $stmt = $pdo->prepare("DELETE FROM immortal_arena_match_pool WHERE character_id = ?");
            $stmt->execute([$characterId]);

            echo json_encode([
                'success' => true,
                'message' => '已取消匹配'
            ]);
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
}

if (!function_exists('cleanupExpiredMatches')) {
    function cleanupExpiredMatches($pdo)
    {
        $stmt = $pdo->prepare("DELETE FROM immortal_arena_match_pool WHERE match_timeout < NOW()");
        $stmt->execute();
    }
}

if (!function_exists('checkCanStartMatch')) {
    function checkCanStartMatch($pdo, $characterId)
    {
        $stmt = $pdo->prepare("SELECT arena_daily_attempts, arena_purchased_attempts FROM characters WHERE id = ?");
        $stmt->execute([$characterId]);
        $data = $stmt->fetch(PDO::FETCH_ASSOC);

        $usedDaily = $data['arena_daily_attempts'];
        $purchasedAttempts = $data['arena_purchased_attempts'];
        $remainingFree = max(0, 10 - $usedDaily);
        $totalRemaining = $remainingFree + $purchasedAttempts;

        if ($totalRemaining <= 0) {
            return [
                'success' => false,
                'message' => '今日挑战次数已用完'
            ];
        }

        return ['success' => true];
    }
}

if (!function_exists('getCharacterSnapshot')) {
    function getCharacterSnapshot($pdo, $characterId)
    {
        $stmt = $pdo->prepare("SELECT * FROM characters WHERE id = ?");
        $stmt->execute([$characterId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
}

if (!function_exists('createCharacterSnapshot')) {
    function createCharacterSnapshot($character)
    {
        return [
            'character_id' => $character['id'],
            'character_name' => $character['character_name'],
            'realm_id' => $character['realm_id'],
            'physical_attack' => $character['physical_attack'] ?? 0,
            'immortal_attack' => $character['immortal_attack'] ?? 0,
            'physical_defense' => $character['physical_defense'] ?? 0,
            'immortal_defense' => $character['immortal_defense'] ?? 0,
            'max_hp' => $character['max_hp'] ?? 100,
            'max_mp' => $character['max_mp'] ?? 100,
            'speed_bonus' => $character['speed_bonus'] ?? 0,
            'critical_bonus' => $character['critical_bonus'] ?? 0,
            'critical_damage' => $character['critical_damage'] ?? 0,
            'accuracy_bonus' => $character['accuracy_bonus'] ?? 0,
            'dodge_bonus' => $character['dodge_bonus'] ?? 0,
            'skills' => $character['arena_skill_sequence'] ?: '0,1,2,3,4,5',
            'created_at' => date('Y-m-d H:i:s')
        ];
    }
}

if (!function_exists('findSuitableOpponent')) {
    function findSuitableOpponent($pdo, $characterId, $daoPower, $realmLevel)
    {
        error_log("🏆 [匹配开始] 玩家ID: $characterId, 道行值: $daoPower, 境界: $realmLevel");

        // 🔧 修复：实现完整的匹配逻辑，优先全局匹配

        // 1. 优先从所有玩家中查找（提高匹配成功率）
        $opponent = findOpponentFromAllPlayers($pdo, $characterId, $daoPower, $realmLevel);

        if ($opponent) {
            error_log("✅ [全局匹配成功] 找到对手: {$opponent['character_name']} (ID: {$opponent['character_id']})");
            return $opponent;
        }

        // 2. 备选：从匹配池中查找
        $opponent = findOpponentInMatchPool($pdo, $characterId, $daoPower, $realmLevel);

        if ($opponent) {
            error_log("✅ [匹配池成功] 找到对手: {$opponent['character_name']} (ID: {$opponent['character_id']})");
            return $opponent;
        }

        error_log("❌ [匹配失败] 未找到合适的真实玩家对手");
        return null;
    }
}

if (!function_exists('getMatchStatus')) {
    function getMatchStatus($pdo, $characterId)
    {
        try {
            // 检查挑战次数
            $canMatch = checkCanStartMatch($pdo, $characterId);
            if (!$canMatch['success']) {
                // 次数不足，强制取消匹配
                $cleanStmt = $pdo->prepare("DELETE FROM immortal_arena_match_pool WHERE character_id = ?");
                $cleanStmt->execute([$characterId]);

                echo json_encode([
                    'success' => false,
                    'message' => $canMatch['message'],
                    'attempts_exhausted' => true
                ]);
                return;
            }

            // 检查是否在匹配池中
            $stmt = $pdo->prepare("SELECT * FROM immortal_arena_match_pool WHERE character_id = ?");
            $stmt->execute([$characterId]);
            $matchData = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$matchData) {
                // 用户不在匹配池中，清理其他过期记录
                cleanupExpiredMatches($pdo);
                echo json_encode([
                    'success' => true,
                    'in_queue' => false,
                    'match_found' => false
                ]);
                return;
            }

            // 检查是否超时
            $timeout = strtotime($matchData['match_timeout']);
            $now = time();

            if ($timeout <= $now) {
                // 超时，清理记录并返回AI匹配
                cleanupExpiredMatches($pdo);

                echo json_encode([
                    'success' => true,
                    'in_queue' => false,
                    'match_found' => false,
                    'timeout' => true
                ]);
            } else {
                // 仍在队列中
                $waitTime = $timeout - $now;
                echo json_encode([
                    'success' => true,
                    'in_queue' => true,
                    'wait_time' => $waitTime
                ]);
            }
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
}

if (!function_exists('generateAiOpponentForMatching')) {
    function generateAiOpponentForMatching($pdo, $characterId)
    {
        try {
            // 检查挑战次数
            $canMatch = checkCanStartMatch($pdo, $characterId);
            if (!$canMatch['success']) {
                echo json_encode([
                    'success' => false,
                    'message' => $canMatch['message'],
                    'attempts_exhausted' => true
                ]);
                return;
            }

            // 获取角色数据
            $character = getCharacterSnapshot($pdo, $characterId);
            if (!$character) {
                throw new Exception('无法获取角色数据');
            }

            $daoPower = calculateDaoPower($pdo, $character);
            error_log("🏆 [竞技系统] 生成AI对手 - 角色ID: $characterId, 道行值: $daoPower");

            // 清理角色的匹配记录
            $cleanStmt = $pdo->prepare("DELETE FROM immortal_arena_match_pool WHERE character_id = ?");
            $cleanStmt->execute([$characterId]);

            // 🔧 修复：生成AI对手名称 - 使用固定种子确保一致性
            $aiNames = ['玄天', '紫霄', '青云', '明月', '星辰', '寒冰', '烈火', '疾风'];
            $seed = $characterId * 11 + 17; // 固定算法
            $nameIndex = $seed % count($aiNames);
            $randomName = $aiNames[$nameIndex];
            $aiName = '灵智傀儡·' . $randomName;

            // 🔧 修复：生成AI对手数据（使用固定ID）
            $aiId = 'ai_' . $characterId . '_' . $nameIndex . '_match';
            $aiOpponent = [
                'character_id' => $aiId,
                'character_name' => $aiName,
                'name' => $aiName,
                'dao_power' => $daoPower + rand(-100, 100),
                'realm_id' => $character['realm_id'], // 🔧 修复：使用玩家境界ID
                'character_avatar' => $character['avatar_image'] ?: '/yinian/public/assets/images/default-avatar.png',
                'is_ai_puppet' => true,
                'is_ai' => true,
                'template_id' => null
            ];

            // 获取境界名称
            $realmStmt = $pdo->prepare("SELECT realm_name FROM realm_levels WHERE id = ?");
            $realmStmt->execute([$character['realm_id']]);
            $realmData = $realmStmt->fetch(PDO::FETCH_ASSOC);
            $aiOpponent['realm_name'] = $realmData ? $realmData['realm_name'] : '开光期';
            $aiOpponent['level'] = $character['realm_id'];

            error_log("🏆 [竞技系统] AI对手生成成功: " . json_encode($aiOpponent));

            echo json_encode([
                'success' => true,
                'opponent' => $aiOpponent
            ]);
        } catch (Exception $e) {
            error_log("❌ [竞技系统] AI对手生成失败: " . $e->getMessage());
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
}

// 获取数据库连接
try {
    $pdo = getDatabase();
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => '数据库连接失败'
    ]);
    exit;
}

// 检查用户认证
if (!isset($_SESSION['user_id'])) {
    echo json_encode([
        'success' => false,
        'message' => '请先登录'
    ]);
    exit;
}

$userId = $_SESSION['user_id'];
$characterId = isset($_SESSION['character_id']) ? $_SESSION['character_id'] : null;

// 🔧 修复：如果会话中没有角色ID，尝试从数据库获取
if (!$characterId) {
    try {
        $stmt = $pdo->prepare("
            SELECT c.id, c.character_name, c.avatar_image, c.realm_id,
                   r.realm_name, r.realm_level
            FROM characters c
            LEFT JOIN realm_levels r ON c.realm_id = r.id
            WHERE c.user_id = ?
            ORDER BY c.id DESC
            LIMIT 1
        ");
        $stmt->execute([$userId]);
        $character = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($character) {
            // 自动修复会话信息
            $_SESSION['character_id'] = $character['id'];
            $_SESSION['character_name'] = $character['character_name'];
            $_SESSION['character_avatar'] = $character['avatar_image'];
            $_SESSION['realm_id'] = $character['realm_id'];
            $_SESSION['realm_name'] = $character['realm_name'];
            $_SESSION['realm_level'] = $character['realm_level'];

            $characterId = $character['id'];
            error_log("🔧 [竞技场] 自动修复角色会话信息: 角色ID $characterId, 角色名 {$character['character_name']}");
        } else {
            echo json_encode([
                'success' => false,
                'message' => '角色信息不存在，请先创建角色',
                'need_create_character' => true
            ]);
            exit;
        }
    } catch (Exception $e) {
        error_log("🔧 [竞技场] 获取角色信息失败: " . $e->getMessage());
        echo json_encode([
            'success' => false,
            'message' => '获取角色信息失败'
        ]);
        exit;
    }
}

// ==========================================
// 🎯 主要API路由处理
// ==========================================

// 只有在直接访问此文件时才执行路由逻辑
if (strpos($_SERVER['PHP_SELF'], 'immortal_arena.php') !== false || strpos($_SERVER['SCRIPT_NAME'], 'immortal_arena.php') !== false) {
    try {
        // 支持GET和POST请求的action参数
        $action = '';
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // 🔧 修复：优先检查FormData，然后检查JSON
            if (isset($_POST['action'])) {
                $action = $_POST['action'];
            } else {
                $input = json_decode(file_get_contents('php://input'), true);
                $action = isset($input['action']) ? $input['action'] : '';
            }
        } else {
            $action = isset($_GET['action']) ? $_GET['action'] : '';
        }

        switch ($action) {
            case 'get_arena_info':
                getArenaInfo($pdo, $characterId);
                break;

            case 'start_matching':
                startMatching($pdo, $characterId);
                break;

            case 'cancel_matching':
                cancelMatching($pdo, $characterId);
                break;

            case 'get_match_status':
                getMatchStatus($pdo, $characterId);
                break;

            case 'get_opponent_data':
                getOpponentData($pdo, $characterId);
                break;

            case 'submit_battle_result':
                submitBattleResult($pdo, $characterId);
                break;

            case 'get_battle_records':
                getBattleRecords($pdo, $characterId);
                break;

            case 'get_arena_rankings':
                getArenaRankings($pdo);
                break;

            case 'purchase_attempts':
                purchaseAttempts($pdo, $characterId);
                break;

            case 'generate_ai_opponent':
                generateAiOpponentForMatching($pdo, $characterId);
                break;

            case 'submit_arena_result':
                submitArenaResult($pdo, $characterId);
                break;

            case 'verify_arena_access':
                verifyArenaAccess($pdo, $characterId);
                break;

            default:
                echo json_encode([
                    'success' => false,
                    'message' => '未知的操作'
                ]);
                break;
        }
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => '服务器错误',
            'debug' => $e->getMessage()
        ]);
    }
}

// ==========================================
// 🔧 函数定义区域
// ==========================================

/**
 * 获取竞技场信息 (重复定义已删除)
 */
// 重复的getArenaInfo函数已删除，使用前面定义的版本

// 重复的startMatching函数已删除，使用前面的前置声明版本

// 重复的cancelMatching函数已删除，使用前面的前置声明版本

// 重复的getMatchStatus函数已删除，使用前面的前置声明版本

// 重复的calculateDaoPower函数已删除，使用前面定义的版本

// 重复的checkDailyReset函数已删除，使用前面定义的版本

// 重复的checkCanStartMatch函数已删除，使用前面的前置声明版本

// 重复的getCharacterSnapshot函数已删除，使用前面的前置声明版本

// 重复的createCharacterSnapshot函数已删除，使用前面的前置声明版本

// 重复的findSuitableOpponent函数已删除，使用前面的前置声明版本

/**
 * 在匹配池中查找对手
 */
function findOpponentInMatchPool($pdo, $characterId, $daoPower, $realmLevel)
{
    // 检查匹配池中是否有等待的玩家
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM immortal_arena_match_pool WHERE character_id != ?");
    $stmt->execute([$characterId]);
    $poolCount = $stmt->fetchColumn();

    error_log("🏆 [匹配池] 当前池中等待人数: $poolCount");

    if ($poolCount == 0) {
        return null; // 匹配池为空，直接跳过
    }

    // 严格匹配（±30%道行值，±30境界）
    $minPower = $daoPower * 0.7;
    $maxPower = $daoPower * 1.3;
    $minRealm = max(1, $realmLevel - 30);
    $maxRealm = $realmLevel + 30;

    $stmt = $pdo->prepare("SELECT 
        character_id, 
        dao_power, 
        realm_id,
        character_snapshot
    FROM immortal_arena_match_pool 
    WHERE character_id != ? 
    AND dao_power BETWEEN ? AND ?
    AND realm_id BETWEEN ? AND ?
    ORDER BY ABS(dao_power - ?) ASC
    LIMIT 1");

    $stmt->execute([$characterId, $minPower, $maxPower, $minRealm, $maxRealm, $daoPower]);
    $opponent = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($opponent) {
        // 从匹配池中移除对手
        $deleteStmt = $pdo->prepare("DELETE FROM immortal_arena_match_pool WHERE character_id = ?");
        $deleteStmt->execute([$opponent['character_id']]);

        // 解析对手数据
        $snapshot = json_decode($opponent['character_snapshot'], true);
        return [
            'character_id' => $opponent['character_id'],
            'character_name' => $snapshot['character_name'],
            'dao_power' => $opponent['dao_power'],
            'realm_id' => $opponent['realm_id']
        ];
    }

    return null;
}

/**
 * 🔧 强化版智能匹配系统：强制避免同一天内重复匹配
 */
function findOpponentFromAllPlayers($pdo, $characterId, $daoPower, $realmLevel)
{
    error_log("🔍 [强化匹配] 开始为玩家ID: $characterId (道行: $daoPower, 境界: $realmLevel) 查找对手");

    // 1. 🔧 强制获取今日已匹配的对手，绝对避免重复
    // 获取今日已匹配的对手
    $todayOpponents = [];
    try {
        $stmt = $pdo->prepare("
            SELECT DISTINCT opponent_character_id
            FROM immortal_arena_records
            WHERE character_id = ?
            AND opponent_character_id IS NOT NULL
            AND DATE(created_at) = CURDATE()
        ");
        $stmt->execute([$characterId]);

        while ($row = $stmt->fetch()) {
            if ($row['opponent_character_id'] && $row['opponent_character_id'] != $characterId) {
                $todayOpponents[] = intval($row['opponent_character_id']);
            }
        }
        error_log("今日已匹配对手: " . implode(',', $todayOpponents));
    } catch (Exception $e) {
        error_log("获取今日对手失败: " . $e->getMessage());
    }

    $excludeIds = array_merge([$characterId], $todayOpponents);

    error_log("� [强制避免重复] 排除今日已匹配对手: " . implode(',', $todayOpponents));

    // 2. 🔧 收紧匹配范围：±15%道行值（更严格）
    $strictMinPower = $daoPower * 0.85;
    $strictMaxPower = $daoPower * 1.15;
    $strictMinRealm = max(1, $realmLevel - 5);
    $strictMaxRealm = $realmLevel + 5;

    $opponent = findOpponentWithCriteria(
        $pdo,
        $excludeIds,
        $strictMinPower,
        $strictMaxPower,
        $strictMinRealm,
        $strictMaxRealm,
        $daoPower,
        "严格匹配(±15%)"
    );

    if ($opponent) {
        return $opponent;
    }

    // 3. 🔧 次级匹配：±25%道行值（仍然较严格）
    $looseMinPower = $daoPower * 0.75;
    $looseMaxPower = $daoPower * 1.25;
    $looseMinRealm = max(1, $realmLevel - 10);
    $looseMaxRealm = $realmLevel + 10;

    $opponent = findOpponentWithCriteria(
        $pdo,
        $excludeIds,
        $looseMinPower,
        $looseMaxPower,
        $looseMinRealm,
        $looseMaxRealm,
        $daoPower,
        "宽松匹配(±25%)"
    );

    if ($opponent) {
        return $opponent;
    }

    // 4. 🔧 最后尝试：±35%道行值（但仍排除今日对手）
    $finalMinPower = $daoPower * 0.65;
    $finalMaxPower = $daoPower * 1.35;
    $finalMinRealm = max(1, $realmLevel - 15);
    $finalMaxRealm = $realmLevel + 15;

    $opponent = findOpponentWithCriteria(
        $pdo,
        $excludeIds,
        $finalMinPower,
        $finalMaxPower,
        $finalMinRealm,
        $finalMaxRealm,
        $daoPower,
        "最终匹配(±35%)"
    );

    if ($opponent) {
        return $opponent;
    }

    // 5. 🔧 强制AI匹配：找不到合适真人玩家时直接返回null，触发AI生成
    error_log("🤖 [强制AI匹配] 未找到符合条件的真实玩家，将生成AI对手");
    return null;
}

/**
 * 🔧 强化版：生成高度平衡的AI对手
 */
function generateBalancedAiOpponent($pdo, $playerCharacter, $playerDaoPower)
{
    try {
        // 🔧 生成更接近玩家实力的AI对手（±10%道行值）
        $variationPercent = rand(-10, 10) / 100; // ±10%的变化
        $aiDaoPower = intval($playerDaoPower * (1 + $variationPercent));
        $aiDaoPower = max(300, $aiDaoPower); // 确保最低道行值

        // 🔧 选择相同或相近境界（±1级）
        $aiRealmId = $playerCharacter['realm_id'] + rand(-1, 1);
        $aiRealmId = max(1, min(280, $aiRealmId)); // 确保境界范围合理

        // 🔧 验证字段名：realm_levels表的realm_name字段 (需要验证)
        $stmt = $pdo->prepare("SELECT realm_name FROM realm_levels WHERE id = ?");
        $stmt->execute([$aiRealmId]);
        $realmInfo = $stmt->fetch();
        $aiRealmName = $realmInfo ? $realmInfo['realm_name'] : '未知境界';

        // 🔧 更丰富的AI名字库
        $aiNames = [
            '灵智傀儡·玄天',
            '灵智傀儡·紫霄',
            '灵智傀儡·青云',
            '灵智傀儡·赤焰',
            '灵智傀儡·寒冰',
            '灵智傀儡·雷鸣',
            '灵智傀儡·风暴',
            '灵智傀儡·山岳',
            '灵智傀儡·流水',
            '灵智傀儡·烈日',
            '灵智傀儡·明月',
            '灵智傀儡·星辰',
            '灵智傀儡·苍穹',
            '灵智傀儡·碧海',
            '灵智傀儡·金辉',
            '灵智傀儡·银月',
            '灵智傀儡·翠竹',
            '灵智傀儡·丹霞',
            '灵智傀儡·墨云',
            '灵智傀儡·琉璃'
        ];
        $aiName = $aiNames[array_rand($aiNames)];

        // 确保AI名字唯一性（添加时间戳后缀）
        $aiName .= '·' . sprintf('%03d', (time() % 1000));

        $powerDiff = abs($aiDaoPower - $playerDaoPower);
        $powerDiffPercent = round(($powerDiff / $playerDaoPower) * 100, 1);

        error_log("🤖 [强化AI生成] 为玩家 {$playerCharacter['character_name']} (道行: $playerDaoPower) 生成AI对手: $aiName (道行: $aiDaoPower, 差异: {$powerDiffPercent}%)");

        return [
            'character_id' => 'ai_' . time() . '_' . rand(1000, 9999),
            'character_name' => $aiName,
            'dao_power' => $aiDaoPower,
            'realm_id' => $aiRealmId,
            'realm_name' => $aiRealmName,
            'is_ai_puppet' => true,
            'match_quality' => $powerDiffPercent <= 5 ? 'excellent' : ($powerDiffPercent <= 10 ? 'good' : 'fair')
        ];
    } catch (Exception $e) {
        error_log("生成AI对手失败: " . $e->getMessage());
        return null;
    }
}

/**
 * 🔧 强化版：获取今日已匹配的对手列表（强制避免重复匹配）
 */
function getTodayOpponents($pdo, $characterId)
{
    try {
        // 🔧 验证字段名：character_id, opponent_character_id, created_at (已验证 2024-12-28)
        $stmt = $pdo->prepare("
            SELECT DISTINCT opponent_character_id as today_opponent_id
            FROM immortal_arena_records
            WHERE character_id = ?
            AND opponent_character_id IS NOT NULL
            AND DATE(created_at) = CURDATE()
            ORDER BY created_at DESC
        ");
        $stmt->execute([$characterId]);

        $opponents = [];
        while ($row = $stmt->fetch()) {
            if ($row['today_opponent_id'] && $row['today_opponent_id'] != $characterId) {
                $opponents[] = intval($row['today_opponent_id']);
            }
        }

        error_log("� [强制避免重复] 玩家 $characterId 今日已匹配的对手: " . implode(',', $opponents));
        return $opponents;
    } catch (Exception $e) {
        error_log("获取今日对手失败: " . $e->getMessage());
        return [];
    }
}

/**
 * 根据条件查找对手
 */
function findOpponentWithCriteria($pdo, $excludeIds, $minPower, $maxPower, $minRealm, $maxRealm, $targetPower, $matchType)
{
    try {
        $excludeClause = implode(',', array_fill(0, count($excludeIds), '?'));

        $sql = "SELECT
            c.id as character_id,
            c.character_name,
            c.realm_id,
            c.arena_dao_power as dao_power,
            ABS(c.arena_dao_power - ?) as power_diff
        FROM characters c
        WHERE c.id NOT IN ($excludeClause)
        AND c.realm_id BETWEEN ? AND ?
        AND c.arena_dao_power BETWEEN ? AND ?
        AND c.character_name IS NOT NULL
        AND c.character_name != ''
        AND c.arena_dao_power > 0
        ORDER BY power_diff ASC, RAND()
        LIMIT 1";

        $params = array_merge([$targetPower], $excludeIds, [$minRealm, $maxRealm, $minPower, $maxPower]);
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $opponent = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($opponent) {
            $powerDiffPercent = round(($opponent['power_diff'] / $targetPower) * 100, 1);
            error_log("✅ [智能匹配-{$matchType}] 找到对手: {$opponent['character_name']} (道行差异: {$opponent['power_diff']}, {$powerDiffPercent}%)");
        } else {
            error_log("❌ [智能匹配-{$matchType}] 未找到符合条件的对手");
        }

        return $opponent;
    } catch (Exception $e) {
        error_log("匹配查询失败: " . $e->getMessage());
        return null;
    }
}

/**
 * 考虑历史交手次数的匹配
 */
function findOpponentWithHistory($pdo, $characterId, $minPower, $maxPower, $minRealm, $maxRealm, $targetPower)
{
    try {
        $sql = "SELECT
            c.id as character_id,
            c.character_name,
            c.realm_id,
            c.arena_dao_power as dao_power,
            ABS(c.arena_dao_power - ?) as power_diff,
            COALESCE(battle_count.count, 0) as battle_count
        FROM characters c
        LEFT JOIN (
            SELECT
                opponent_character_id as opponent_id,
                COUNT(*) as count
            FROM immortal_arena_records
            WHERE character_id = ?
            AND opponent_character_id IS NOT NULL
            GROUP BY opponent_character_id
        ) battle_count ON c.id = battle_count.opponent_id
        WHERE c.id != ?
        AND c.realm_id BETWEEN ? AND ?
        AND c.arena_dao_power BETWEEN ? AND ?
        AND c.character_name IS NOT NULL
        AND c.character_name != ''
        AND c.arena_dao_power > 0
        ORDER BY battle_count ASC, power_diff ASC, RAND()
        LIMIT 1";

        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            $targetPower,
            $characterId,
            $characterId,
            $minRealm,
            $maxRealm,
            $minPower,
            $maxPower
        ]);
        $opponent = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($opponent) {
            $powerDiffPercent = round(($opponent['power_diff'] / $targetPower) * 100, 1);
            error_log("✅ [智能匹配-历史优化] 找到对手: {$opponent['character_name']} (交手次数: {$opponent['battle_count']}, 道行差异: {$powerDiffPercent}%)");
        }

        return $opponent;
    } catch (Exception $e) {
        error_log("历史匹配查询失败: " . $e->getMessage());
        return null;
    }
}

/**
 * 生成AI对手
 */
function generateAiOpponent($pdo, $daoPower, $realmLevel)
{
    // 🔧 修复：获取境界名称的查询
    $stmt = $pdo->prepare("SELECT id, character_name, realm_id FROM characters 
        WHERE realm_id BETWEEN ? AND ? 
        ORDER BY RAND() 
        LIMIT 1");
    $stmt->execute([max(1, $realmLevel - 10), $realmLevel + 10]);
    $template = $stmt->fetch(PDO::FETCH_ASSOC);

    // 🔧 获取境界名称
    $realmStmt = $pdo->prepare("SELECT realm_name FROM realm_levels WHERE id = ?");
    $realmStmt->execute([$realmLevel]);
    $realmData = $realmStmt->fetch(PDO::FETCH_ASSOC);
    $realmName = $realmData ? $realmData['realm_name'] : '开光期';

    if (!$template) {
        // 如果没有合适模板，生成默认AI
        $randomNames = ['无名', '慕容', '司马', '南宫', '欧阳', '轩辕', '公孙'];
        $randomName = $randomNames[array_rand($randomNames)];

        return [
            'name' => $randomName,
            'character_name' => $randomName,
            'dao_power' => $daoPower,
            'realm_id' => $realmLevel,
            'realm_name' => $realmName, // 🔧 添加境界名称
            'template_id' => null,
            'is_ai_puppet' => true
        ];
    }

    return [
        'name' => $template['character_name'],
        'character_name' => $template['character_name'],
        'dao_power' => $daoPower + rand(-100, 100),
        'realm_id' => $template['realm_id'],
        'realm_name' => $realmName, // 🔧 添加境界名称
        'template_id' => $template['id'],
        'is_ai_puppet' => true
    ];
}

// 重复的cleanupExpiredMatches函数已删除，使用前面的前置声明版本

// 重复的getOpponentData函数已删除，使用前面的前置声明版本



/**
 * 提交战斗结果
 */
function submitBattleResult($pdo, $characterId)
{
    try {
        // 获取POST数据
        $input = json_decode(file_get_contents('php://input'), true);

        $battleResult = $input['battle_result']; // 'win' or 'lose'
        $opponentId = isset($input['opponent_id']) ? $input['opponent_id'] : null;
        $isAiPuppet = isset($input['is_ai_puppet']) ? $input['is_ai_puppet'] : false;
        $battleData = isset($input['battle_data']) ? $input['battle_data'] : [];

        // 验证战斗结果的合理性
        if (!validateBattleResult($battleData)) {
            throw new Exception('战斗数据异常');
        }

        // 消耗挑战次数
        $consumeResult = consumeArenaAttempt($pdo, $characterId);
        if (!$consumeResult['success']) {
            throw new Exception($consumeResult['message']);
        }

        // 计算奖励
        $rewards = calculateArenaRewards($pdo, $characterId, $battleResult, $isAiPuppet);

        // 更新角色数据
        updateCharacterArenaStats($pdo, $characterId, $battleResult, $rewards);

        // 记录战斗记录
        recordArenaBattle($pdo, $characterId, $opponentId, $battleResult, $rewards, $battleData);

        // 检查段位晋升
        checkRankPromotion($pdo, $characterId);

        echo json_encode([
            'success' => true,
            'battle_result' => $battleResult,
            'rewards' => $rewards,
            'message' => $battleResult === 'win' ? '胜利！' : '败北...'
        ]);
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

/**
 * 获取战斗记录
 */
function getBattleRecords($pdo, $characterId)
{
    try {
        $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
        $limit = 20;
        $offset = ($page - 1) * $limit;

        // 获取总数
        $countStmt = $pdo->prepare("SELECT COUNT(*) FROM immortal_arena_records WHERE character_id = ?");
        $countStmt->execute([$characterId]);
        $total = $countStmt->fetchColumn();

        // 获取记录 - 修复：使用正确的字段名和处理AI对手名称
        $sql = "SELECT
            r.*,
            CASE
                WHEN r.is_ai_puppet = 1 THEN r.opponent_name
                ELSE COALESCE(c.character_name, r.opponent_name)
            END as opponent_name
        FROM immortal_arena_records r
        LEFT JOIN characters c ON r.opponent_character_id = c.id
        WHERE r.character_id = ?
        ORDER BY r.created_at DESC
        LIMIT $limit OFFSET $offset";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$characterId]);
        $records = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo json_encode([
            'success' => true,
            'records' => $records,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => ceil($total / $limit),
                'total_records' => $total
            ]
        ]);
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

/**
 * 获取竞技场排行榜
 */
function getArenaRankings($pdo)
{
    try {
        $type = isset($_GET['type']) ? $_GET['type'] : 'dao_power';
        $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
        $limit = 50;
        $offset = ($page - 1) * $limit;

        switch ($type) {
            case 'dao_power':
                $orderBy = 'arena_dao_power DESC';
                break;
            case 'win_rate':
                $orderBy = 'arena_total_battles > 0, (arena_total_wins / arena_total_battles) DESC';
                break;
            case 'win_streak':
                $orderBy = 'arena_win_streak DESC';
                break;
            default:
                $orderBy = 'arena_dao_power DESC';
        }

        $sql = "SELECT 
            c.character_name,
            c.arena_dao_power,
            c.arena_rank_level,
            c.arena_total_wins,
            c.arena_total_battles,
            c.arena_win_streak,
            c.arena_best_streak,
            r.rank_name,
            r.rank_color,
            CASE 
                WHEN c.arena_total_battles > 0 THEN ROUND((c.arena_total_wins / c.arena_total_battles) * 100, 1)
                ELSE 0 
            END as win_rate
        FROM characters c
        LEFT JOIN immortal_arena_ranks r ON c.arena_rank_level = r.rank_level
        WHERE c.arena_total_battles > 0
        ORDER BY {$orderBy}
        LIMIT $limit OFFSET $offset";
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $rankings = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo json_encode([
            'success' => true,
            'rankings' => $rankings,
            'type' => $type
        ]);
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

/**
 * 购买挑战次数
 */
function purchaseAttempts($pdo, $characterId)
{
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        $quantity = intval(isset($input['quantity']) ? $input['quantity'] : 1);

        if ($quantity < 1 || $quantity > 10) {
            throw new Exception('购买数量必须在1-10之间');
        }

        // 检查已购买次数
        $stmt = $pdo->prepare("SELECT c.arena_purchased_attempts, u.spirit_stones 
                              FROM characters c 
                              JOIN users u ON c.user_id = u.id 
                              WHERE c.id = ?");
        $stmt->execute([$characterId]);
        $data = $stmt->fetch(PDO::FETCH_ASSOC);

        $currentPurchased = $data['arena_purchased_attempts'];
        $spiritStones = $data['spirit_stones'];

        if ($currentPurchased + $quantity > 10) {
            throw new Exception('每日最多购买10次挑战机会');
        }

        // 计算费用（新的递增价格：首次500，然后递增）
        $totalCost = 0;
        for ($i = 0; $i < $quantity; $i++) {
            $nextPurchase = $currentPurchased + $i + 1;
            $cost = 500 + ($nextPurchase - 1) * 100; // 500, 600, 700, 800...
            $totalCost += $cost;
        }

        if ($spiritStones < $totalCost) {
            throw new Exception("灵石不足，需要{$totalCost}灵石");
        }

        // 扣除灵石，增加挑战次数
        $updateStmt = $pdo->prepare("UPDATE characters c 
                                    JOIN users u ON c.user_id = u.id 
                                    SET u.spirit_stones = u.spirit_stones - ?,
                                        c.arena_purchased_attempts = c.arena_purchased_attempts + ?
                                    WHERE c.id = ?");
        $updateStmt->execute([$totalCost, $quantity, $characterId]);

        echo json_encode([
            'success' => true,
            'message' => "成功购买{$quantity}次挑战机会",
            'cost' => $totalCost,
            'new_attempts' => $currentPurchased + $quantity
        ]);
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

// === 辅助函数 ===



function validateBattleResult($battleData)
{
    // 简单的战斗数据验证
    if (!isset($battleData['rounds']) || !isset($battleData['duration'])) {
        return false;
    }

    $rounds = intval($battleData['rounds']);
    $duration = intval($battleData['duration']);

    // 🔥 新增：回合数限制为30回合
    if ($rounds < 1 || $rounds > 30) {
        error_log("⚠️ 战斗验证失败：回合数超出限制 ($rounds > 30)");
        return false;
    }

    // 持续时间应在合理范围内（秒）
    if ($duration < 5 || $duration > 1800) { // 5秒到30分钟
        return false;
    }

    return true;
}

function consumeArenaAttempt($pdo, $characterId)
{
    // 🔧 修复：使用事务确保原子性，防止重复扣减
    $pdo->beginTransaction();

    try {
        // 锁定角色记录，防止并发修改
        $stmt = $pdo->prepare("SELECT arena_daily_attempts, arena_purchased_attempts FROM characters WHERE id = ? FOR UPDATE");
        $stmt->execute([$characterId]);
        $data = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$data) {
            $pdo->rollBack();
            return ['success' => false, 'message' => '角色不存在'];
        }

        $usedDaily = $data['arena_daily_attempts'];
        $purchasedAttempts = $data['arena_purchased_attempts'];

        // 计算总剩余次数
        $remainingFree = max(0, 10 - $usedDaily);
        $totalRemaining = $remainingFree + $purchasedAttempts;

        if ($totalRemaining <= 0) {
            $pdo->rollBack();
            return ['success' => false, 'message' => '挑战次数不足'];
        }

        // 优先消耗免费次数
        if ($remainingFree > 0) {
            $updateStmt = $pdo->prepare("UPDATE characters SET arena_daily_attempts = arena_daily_attempts + 1 WHERE id = ?");
            $updateStmt->execute([$characterId]);
        } else {
            // 免费次数用完，消耗购买次数
            $updateStmt = $pdo->prepare("UPDATE characters SET arena_purchased_attempts = arena_purchased_attempts - 1 WHERE id = ?");
            $updateStmt->execute([$characterId]);
        }

        $pdo->commit();
        return ['success' => true];
    } catch (Exception $e) {
        $pdo->rollBack();
        error_log("消耗竞技场挑战次数失败: " . $e->getMessage());
        return ['success' => false, 'message' => '系统错误，请重试'];
    }
}

function calculateArenaRewards($pdo, $characterId, $battleResult, $isAiPuppet)
{
    // 获取角色段位信息
    $stmt = $pdo->prepare("SELECT 
        arena_rank_level,
        r.reward_multiplier
    FROM characters c
    LEFT JOIN immortal_arena_ranks r ON c.arena_rank_level = r.rank_level
    WHERE c.id = ?");
    $stmt->execute([$characterId]);
    $data = $stmt->fetch(PDO::FETCH_ASSOC);

    // 🔧 修复：严格按照开发计划的奖励机制
    if ($isAiPuppet) {
        // AI傀儡奖励：胜80灵石，败40灵石
        $baseReward = $battleResult === 'win' ? 80 : 40;
    } else {
        // 真实玩家对战奖励：胜100灵石，败50灵石
        $baseReward = $battleResult === 'win' ? 100 : 50;
    }

    $multiplier = $data['reward_multiplier'] ?: 1.0;
    $spiritStones = intval($baseReward * $multiplier);

    // 🏆 段位积分奖励机制
    $rankPointsGained = 0;
    if ($battleResult === 'win') {
        $rankPointsGained = $isAiPuppet ? 10 : 15; // 胜利：AI对手10分，真实玩家15分
    } else {
        $rankPointsGained = $isAiPuppet ? -3 : -5; // 失败：AI对手-3分，真实玩家-5分
    }

    return [
        'spirit_stones' => $spiritStones,
        'experience' => 0, // 竞技场不给经验
        'gold' => 0, // 竞技场不给金币
        'adventure_value' => 0, // 竞技场不给奇遇值
        'rank_points' => $rankPointsGained
    ];
}

function updateCharacterArenaStats($pdo, $characterId, $battleResult, $rewards)
{
    // 🔧 修复：获取user_id来更新users表中的灵石
    $userStmt = $pdo->prepare("SELECT user_id FROM characters WHERE id = ?");
    $userStmt->execute([$characterId]);
    $userData = $userStmt->fetch(PDO::FETCH_ASSOC);
    $userId = $userData['user_id'];

    if ($battleResult === 'win') {
        // 更新characters表的竞技场统计
        $stmt = $pdo->prepare("UPDATE characters SET 
            arena_total_wins = arena_total_wins + 1,
            arena_total_battles = arena_total_battles + 1,
            arena_win_streak = arena_win_streak + 1,
            arena_best_streak = GREATEST(arena_best_streak, arena_win_streak + 1),
            arena_rank_points = GREATEST(0, arena_rank_points + ?)
            WHERE id = ?");
        $stmt->execute([$rewards['rank_points'], $characterId]);

        // 🔧 修复：更新users表的灵石
        $spiritStmt = $pdo->prepare("UPDATE users SET spirit_stones = spirit_stones + ? WHERE id = ?");
        $spiritStmt->execute([$rewards['spirit_stones'], $userId]);
    } else {
        // 更新characters表的竞技场统计
        $stmt = $pdo->prepare("UPDATE characters SET 
            arena_total_battles = arena_total_battles + 1,
            arena_win_streak = 0,
            arena_rank_points = GREATEST(0, arena_rank_points + ?)
            WHERE id = ?");
        $stmt->execute([$rewards['rank_points'], $characterId]);

        // 🔧 修复：更新users表的灵石
        $spiritStmt = $pdo->prepare("UPDATE users SET spirit_stones = spirit_stones + ? WHERE id = ?");
        $spiritStmt->execute([$rewards['spirit_stones'], $userId]);
    }
}



function checkRankPromotion($pdo, $characterId)
{
    // 🏆 段位晋升逻辑（基于积分）
    $stmt = $pdo->prepare("SELECT 
        arena_rank_points,
        arena_rank_level
    FROM characters WHERE id = ?");
    $stmt->execute([$characterId]);
    $data = $stmt->fetch(PDO::FETCH_ASSOC);

    $currentPoints = $data['arena_rank_points'];
    $currentRank = $data['arena_rank_level'];

    // 检查是否可以晋升
    $promotionStmt = $pdo->prepare("SELECT rank_level, required_points, rank_name FROM immortal_arena_ranks WHERE required_points <= ? AND rank_level > ? ORDER BY rank_level DESC LIMIT 1");
    $promotionStmt->execute([$currentPoints, $currentRank]);
    $newRankData = $promotionStmt->fetch(PDO::FETCH_ASSOC);

    // 检查是否需要降级
    $demotionStmt = $pdo->prepare("SELECT rank_level, required_points, rank_name FROM immortal_arena_ranks WHERE rank_level = ?");
    $demotionStmt->execute([$currentRank]);
    $currentRankData = $demotionStmt->fetch(PDO::FETCH_ASSOC);

    $promotionMessage = null;

    if ($newRankData && $newRankData['rank_level'] > $currentRank) {
        // 可以晋升
        $newRankLevel = $newRankData['rank_level'];
        $updateStmt = $pdo->prepare("UPDATE characters SET arena_rank_level = ? WHERE id = ?");
        $updateStmt->execute([$newRankLevel, $characterId]);

        $promotionMessage = "🎉 恭喜！段位晋升至：{$newRankData['rank_name']}！";
        error_log("🏆 [段位晋升] 角色ID{$characterId}从{$currentRank}级晋升至{$newRankLevel}级");
    } elseif ($currentRankData && $currentPoints < $currentRankData['required_points'] && $currentRank > 1) {
        // 需要降级
        $lowerRankStmt = $pdo->prepare("SELECT rank_level, rank_name FROM immortal_arena_ranks WHERE rank_level < ? ORDER BY rank_level DESC LIMIT 1");
        $lowerRankStmt->execute([$currentRank]);
        $lowerRankData = $lowerRankStmt->fetch(PDO::FETCH_ASSOC);

        if ($lowerRankData) {
            $newRankLevel = $lowerRankData['rank_level'];
            $updateStmt = $pdo->prepare("UPDATE characters SET arena_rank_level = ? WHERE id = ?");
            $updateStmt->execute([$newRankLevel, $characterId]);

            $promotionMessage = "⚠️ 段位降级至：{$lowerRankData['rank_name']}";
            error_log("🏆 [段位降级] 角色ID{$characterId}从{$currentRank}级降级至{$newRankLevel}级");
        }
    }

    return $promotionMessage;
}

// 重复的generateAiOpponentForMatching函数已删除，使用前面的前置声明版本

function generateRandomName()
{
    $surnames = ['李', '王', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴'];
    $names = ['剑仙', '法师', '道人', '真人', '上人', '居士', '散人', '子'];
    return $surnames[array_rand($surnames)] . $names[array_rand($names)];
}

// 🆕 新增：竞技场专用战斗结算API
function submitArenaResult($pdo, $characterId)
{
    // 🔧 清理可能的输出缓冲区，避免JSON格式错误
    if (ob_get_level()) {
        ob_clean();
    }

    try {
        // 🔧 修复：正确获取POST数据（支持FormData和JSON）
        $battleResult = '';
        $opponentId = null;
        $opponentName = null; // 🔧 新增：对手名字
        $isAiPuppet = false;
        $battleData = [];

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // 🔧 修复：优先检查FormData，然后检查JSON
            if (!empty($_POST['battle_result']) || !empty($_POST)) {
                // FormData格式
                $battleResult = isset($_POST['battle_result']) ? $_POST['battle_result'] : '';
                $opponentId = isset($_POST['opponent_id']) ? $_POST['opponent_id'] : null;
                $opponentName = isset($_POST['opponent_name']) ? $_POST['opponent_name'] : null; // 🔧 新增：获取对手名字
                $isAiPuppet = (isset($_POST['is_ai_puppet']) ? $_POST['is_ai_puppet'] : 'false') === 'true';
                $battleData = isset($_POST['battle_data']) ? json_decode($_POST['battle_data'], true) : [];

                error_log("🔧 [竞技场结算] 使用FormData格式: battle_result=$battleResult, opponent_id=$opponentId, opponent_name=$opponentName, is_ai_puppet=" . ($isAiPuppet ? 'true' : 'false'));
            } else {
                // JSON格式
                $input = json_decode(file_get_contents('php://input'), true);
                if ($input) {
                    $battleResult = isset($input['battle_result']) ? $input['battle_result'] : '';
                    $opponentId = isset($input['opponent_id']) ? $input['opponent_id'] : null;
                    $opponentName = isset($input['opponent_name']) ? $input['opponent_name'] : null; // 🔧 新增：获取对手名字
                    $isAiPuppet = (isset($input['is_ai_puppet']) ? $input['is_ai_puppet'] : false) === true ||
                        (isset($input['is_ai_puppet']) ? $input['is_ai_puppet'] : 'false') === 'true';
                    $battleData = isset($input['battle_data']) ? $input['battle_data'] : [];

                    error_log("🔧 [竞技场结算] 使用JSON格式: battle_result=$battleResult, opponent_id=$opponentId");
                } else {
                    error_log("🔧 [竞技场结算] 无法获取POST数据，$_POST和php://input都为空");
                }
            }
        }

        if (empty($battleResult) || !in_array($battleResult, ['win', 'lose'])) {
            throw new Exception('无效的战斗结果');
        }

        // 验证战斗结果的合理性
        if (!validateBattleResult($battleData)) {
            throw new Exception('战斗数据异常');
        }

        // 消耗挑战次数
        $consumeResult = consumeArenaAttempt($pdo, $characterId);
        if (!$consumeResult['success']) {
            throw new Exception($consumeResult['message']);
        }

        // 计算奖励
        $rewards = calculateArenaRewards($pdo, $characterId, $battleResult, $isAiPuppet);

        // 更新角色竞技场统计
        updateCharacterArenaStats($pdo, $characterId, $battleResult, $rewards);

        // 记录竞技场战斗
        recordArenaBattle($pdo, $characterId, $opponentId, $isAiPuppet, $battleResult, $battleData, $rewards, $opponentName);

        // 🏆 检查段位晋升/降级
        $promotionMessage = checkRankPromotion($pdo, $characterId);

        // 🔧 设置正确的Content-Type头部
        header('Content-Type: application/json; charset=utf-8');

        // 构建消息
        $baseMessage = $battleResult === 'win' ?
            ($isAiPuppet ? "击败灵智傀儡！获得{$rewards['spirit_stones']}灵石、{$rewards['rank_points']}积分" : "论道胜利！获得{$rewards['spirit_stones']}灵石、{$rewards['rank_points']}积分") : ($isAiPuppet ? "败于灵智傀儡，获得{$rewards['spirit_stones']}灵石安慰奖励、{$rewards['rank_points']}积分" : "论道失败，获得{$rewards['spirit_stones']}灵石安慰奖励、{$rewards['rank_points']}积分");

        $finalMessage = $promotionMessage ? $baseMessage . "\n" . $promotionMessage : $baseMessage;

        // 🔧 输出竞技场专用格式的JSON结果
        echo json_encode([
            'success' => true,
            'battle_result' => $battleResult,
            'is_arena_battle' => true, // 标记为竞技场战斗
            'rewards' => [
                'spirit_stones' => $rewards['spirit_stones'],
                'rank_points' => $rewards['rank_points'],
                'is_ai_opponent' => $isAiPuppet
            ],
            'promotion_message' => $promotionMessage,
            'arena_stats' => getUpdatedArenaStats($pdo, $characterId),
            'message' => $finalMessage
        ]);
    } catch (Exception $e) {
        error_log("竞技场结算失败: " . $e->getMessage());
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode([
            'success' => false,
            'message' => '竞技场结算失败: ' . $e->getMessage()
        ]);
    }
}

function recordArenaBattle($pdo, $characterId, $opponentId, $isAiPuppet, $battleResult, $battleData, $rewards, $predefinedOpponentName = null)
{
    // 🔧 修复：优先使用传递的对手名字
    $opponentName = $predefinedOpponentName ?: '未知对手';
    $opponentDaoPower = 0;
    $dbOpponentId = null; // 🔧 数据库中存储的对手ID

    if ($opponentId && !$isAiPuppet && is_numeric($opponentId)) {
        // 真实玩家对手
        if (!$predefinedOpponentName) {
            $stmt = $pdo->prepare("SELECT character_name, COALESCE(arena_dao_power, 1000) as dao_power FROM characters WHERE id = ?");
            $stmt->execute([$opponentId]);
            $opponent = $stmt->fetch(PDO::FETCH_ASSOC);
            if ($opponent) {
                $opponentName = $opponent['character_name'];
                $opponentDaoPower = $opponent['dao_power'];
                $dbOpponentId = intval($opponentId);
            }
        } else {
            // 使用预设名字，但仍需获取道行值
            $stmt = $pdo->prepare("SELECT COALESCE(arena_dao_power, 1000) as dao_power FROM characters WHERE id = ?");
            $stmt->execute([$opponentId]);
            $opponent = $stmt->fetch(PDO::FETCH_ASSOC);
            if ($opponent) {
                $opponentDaoPower = $opponent['dao_power'];
                $dbOpponentId = intval($opponentId);
            }
        }
    } else if ($isAiPuppet || strpos($opponentId, 'ai_') === 0) {
        // 🔧 修复：AI对手 - 优先使用预设名字
        if (!$predefinedOpponentName) {
            // 🔧 修复：根据对手ID生成一致的名称，而不是硬编码
            $aiNames = ['玄天', '紫霄', '青云', '明月', '星辰', '寒冰', '烈火', '疾风'];
            $seed = $characterId * 11 + 17; // 与生成时相同的算法
            $nameIndex = $seed % count($aiNames);
            $opponentName = '灵智傀儡·' . $aiNames[$nameIndex];
        }
        $opponentDaoPower = 1000; // 默认道行值
        $dbOpponentId = null; // AI对手ID存储为NULL
    }

    // 🔧 修复：记录战斗结果，使用新的简化字段结构
    $stmt = $pdo->prepare("
        INSERT INTO immortal_arena_records (
            character_id, opponent_character_id, opponent_name, opponent_dao_power,
            is_ai_puppet, battle_result, spirit_stone_reward, rank_points_change,
            battle_duration, battle_rounds, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
    ");

    $battleDuration = isset($battleData['duration']) ? intval($battleData['duration']) : 0;
    $battleRounds = isset($battleData['rounds']) ? intval($battleData['rounds']) : 0;
    $rankPointsChange = isset($rewards['rank_points']) ? intval($rewards['rank_points']) : 0;

    $stmt->execute([
        $characterId,
        $dbOpponentId, // 🔧 使用处理后的对手ID（AI为null，真实玩家为整数）
        $opponentName,
        $opponentDaoPower,
        $isAiPuppet ? 1 : 0,
        $battleResult,
        $rewards['spirit_stones'],
        $rankPointsChange, // 🔧 新增：记录段位积分变化
        $battleDuration,
        $battleRounds
    ]);
}

function getUpdatedArenaStats($pdo, $characterId)
{
    $stmt = $pdo->prepare("SELECT 
        arena_total_wins, arena_total_battles, arena_win_streak, arena_best_streak,
        arena_daily_attempts, arena_purchased_attempts
    FROM characters WHERE id = ?");
    $stmt->execute([$characterId]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}
