/* 战斗UI样式 */
.battle-ui-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: url('../../images/battle-bg.jpg') no-repeat center center;
    background-size: cover;
    position: relative;
    overflow: hidden;
}

.battle-ui-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg, rgba(26, 58, 92, 0.8) 0%, rgba(45, 89, 132, 0.6) 50%, rgba(26, 58, 92, 0.8) 100%);
    pointer-events: none;
    width: 100%;
    height: 100%;
}

/* 顶部信息栏 */
.battle-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5));
    border-bottom: 2px solid rgba(212, 175, 55, 0.6);
    backdrop-filter: blur(10px);
    position: relative;
    z-index: 10;
    min-height: 50px; /* 确保有足够高度 */
}

/* 🔥 新增：回合数显示样式 - 位于左上角 */
.round-display {
    color: #ffd700 !important;
    font-size: 14px !important;
    font-weight: bold !important;
    text-shadow: 0 0 4px rgba(255, 215, 0, 0.6) !important;
    padding: 6px 12px !important;
    background: rgba(0, 0, 0, 0.4) !important;
    border-radius: 8px !important;
    border: 1px solid rgba(255, 215, 0, 0.4) !important;
    white-space: nowrap !important;
    flex-shrink: 0 !important;
}

.area-info {
    display: flex;
    flex-direction: column;
    color: #ffd700;
    font-weight: bold;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
    flex: 0 0 120px; /* 固定宽度，不伸缩 */
}

.area-info span:first-child {
    font-size: 18px;
    margin-bottom: 4px;
    color: #ffd700;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.6);
    letter-spacing: 1px;
}

.area-info span:last-child {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.4);
}

.return-button {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    padding: 8px 15px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    flex: 0 0 auto; /* 不伸缩，保持原始大小 */
}

.return-button:hover {
    background: linear-gradient(135deg, #c0392b, #a93226);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.5);
}

/* 角色信息 */
.character-info {
    position: absolute;
    top: -35px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    z-index: 10;
    white-space: nowrap;
    pointer-events: none;
}

.character-name {
    font-size: 14px;
    font-weight: bold;
    color: rgba(255, 255, 255, 0.7);
    text-shadow: 0 0 8px rgba(0, 0, 0, 0.5);
    margin-bottom: 2px;
}

.character-level {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    text-shadow: 0 0 6px rgba(0, 0, 0, 0.4);
    background: none;
    padding: 1px 6px;
    border-radius: 10px;
    border: none;
}

/* 血量条样式 */
.hp-bar {
    position: relative;
    width: 100px;
    height: 16px;
    background: rgba(0, 0, 0, 0.4);
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

.hp-fill {
    height: 100%;
    background: linear-gradient(90deg, #e74c3c, #c0392b);
    transition: width 0.3s ease;
    border-radius: 6px;
}

.hp-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 11px;
    font-weight: bold;
    color: white;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.9);
    z-index: 5;
    pointer-events: none;
    white-space: nowrap;
}

/* 🔥 新增：法力值条样式 */
.mp-bar {
    position: relative;
    width: 100px;
    height: 12px;
    background: rgba(0, 0, 0, 0.4);
    border-radius: 6px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.2);
    margin-top: 2px;
}

.mp-fill {
    height: 100%;
    background: linear-gradient(90deg, #3498db, #2980b9);
    transition: width 0.3s ease;
    border-radius: 4px;
}

.mp-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 9px;
    font-weight: bold;
    color: white;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.9);
    z-index: 5;
    pointer-events: none;
    white-space: nowrap;
}

/* 战斗状态容器 */
.battle-status-container {
    padding: 8px;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.6));
    border-top: 2px solid rgba(255, 215, 0, 0.4);
    position: relative;
    z-index: 10;
    backdrop-filter: blur(10px);
    margin-top: auto;
}

.skill-info {
    font-size: 12px;
    color: #fff;
    text-align: center;
    opacity: 0.9;
}

/* 武器显示区域 */
.weapon-display {
    position: relative;
    margin: 10px auto 0;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.85), rgba(0, 0, 0, 0.7));
    border-radius: 8px;
    z-index: 100;
    backdrop-filter: blur(10px);
    width: 90%;
    max-width: 360px;
}

.weapon-list {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 4px;
}

.weapon-item {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    padding: 4px 2px;
    min-width: 0;
    text-align: center;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.weapon-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(255, 215, 0, 0.2);
}

.weapon-item.equipped {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(255, 215, 0, 0.1));
    border-color: rgba(255, 215, 0, 0.6);
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.2);
}

.weapon-item.default {
    background: linear-gradient(135deg, rgba(128, 128, 128, 0.2), rgba(128, 128, 128, 0.1));
    border-color: rgba(128, 128, 128, 0.4);
}

.weapon-name {
    font-size: 11px;
    color: #fff;
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0 2px;
}

/* 移除武器技能显示 */
.weapon-skill {
    display: none;
}

/* 武器槽位高亮效果 */
.weapon-item.weapon-active {
    animation: weapon-glow 1s ease-in-out;
    border-color: #ffd700;
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.6);
}

/* 闪光特效 */
.weapon-flash {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.9),
        transparent
    );
    animation: flash-animation 0.8s ease-out;
}

@keyframes weapon-glow {
    0% {
        box-shadow: 0 0 8px rgba(255, 215, 0, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(255, 215, 0, 0.8);
    }
    100% {
        box-shadow: 0 0 8px rgba(255, 215, 0, 0.5);
    }
}

@keyframes flash-animation {
    0% {
        left: -100%;
        opacity: 0.8;
    }
    100% {
        left: 100%;
        opacity: 0;
    }
}

/* 胜利面板样式 - 与合并样式统一 */

.victory-panel {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.8));
    border-radius: 15px;
    padding: 20px;
    width: 90%;
    max-width: 320px;
    border: 2px solid rgba(255, 215, 0, 0.4);
    backdrop-filter: blur(10px);
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.5);
}

.victory-title {
    font-size: 24px;
    color: #ffd700;
    text-align: center;
    margin-bottom: 10px;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.victory-subtitle {
    font-size: 16px;
    color: #fff;
    text-align: center;
    margin-bottom: 20px;
    opacity: 0.9;
}

.drops-section {
    margin: 15px 0;
}

.drops-title {
    font-size: 16px;
    color: #ffd700;
    margin-bottom: 10px;
    text-shadow: 0 0 8px rgba(255, 215, 0, 0.4);
}

.drops-list {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    margin-bottom: 20px;
}

.drop-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 8px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.drop-icon {
    width: 40px;
    height: 40px;
    margin: 0 auto 5px;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
}

.drop-quality {
    font-size: 12px;
    color: #fff;
}

.victory-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.victory-button {
    padding: 10px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.exit-button {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

.continue-button {
    background: linear-gradient(135deg, #27ae60, #219a52);
    color: white;
}

.victory-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 响应式调整 */
@media (max-height: 600px) {
    .battle-header {
        padding: 8px 12px;
    }
    
    .area-info span:first-child {
        font-size: 14px;
    }
    
    .area-info span:last-child {
        font-size: 11px;
    }
    
    .return-button {
        padding: 6px 12px;
        font-size: 12px;
    }
    
    .battle-container {
        padding: 15px;
        min-height: 180px;
    }
    
    .weapon-item {
        padding: 4px 2px;
    }
    
    .weapon-name {
        font-size: 11px;
        margin-bottom: 2px;
    }
    
    .weapon-skill {
        font-size: 10px;
    }
}

/* 品质颜色 - 与合并样式统一 */

/* 武器详情弹窗 */
.weapon-detail-popup {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(0, 0, 0, 0.85));
    border-radius: 12px;
    padding: 15px;
    z-index: 1000;
    border: 2px solid rgba(255, 215, 0, 0.4);
    backdrop-filter: blur(10px);
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.6);
    width: 90%;
    max-width: 300px;
    color: #fff;
}

.weapon-detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(255, 215, 0, 0.3);
}

.weapon-detail-title {
    font-size: 16px;
    font-weight: bold;
    color: #ffd700;
}

.weapon-detail-close {
    background: none;
    border: none;
    color: #fff;
    font-size: 20px;
    cursor: pointer;
    padding: 0 5px;
}

.weapon-detail-content {
    margin-bottom: 15px;
}

.weapon-detail-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    margin-top: 10px;
}

.weapon-stat-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 6px;
    border-radius: 4px;
    text-align: center;
    font-size: 12px;
}

.weapon-detail-description {
    font-size: 13px;
    color: #ccc;
    margin: 10px 0;
    line-height: 1.4;
}

/* 遮罩层 */
.popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 999;
    backdrop-filter: blur(2px);
}

/* 🔧 新增：飞剑武器图片样式 */
.flying-sword .weapon-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 4px;
    image-rendering: crisp-edges;
}

.flying-sword.skill-sword .weapon-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.8));
    image-rendering: crisp-edges;
}

/* 万剑诀技能剑的光晕效果 */
.flying-sword.skill-sword .weapon-image {
    animation: weaponGlow 2s ease-in-out infinite alternate;
}

@keyframes weaponGlow {
    from {
        filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.8));
    }
    to {
        filter: drop-shadow(0 0 12px rgba(255, 215, 0, 1));
    }
}

/* 添加角色头像相关样式 */
.character-sprite {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.character-avatar {
    max-width: 100%;
    max-height: 100%;
    object-fit: cover;
}

/* 战斗系统UI样式 */

/* 效果容器 - 与合并样式统一 */
.effects-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1000;
}

/* 当前技能 */
.current-skill {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    padding: 5px 15px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border-radius: 4px;
    font-size: 14px;
    z-index: 100;
}

/* 区域信息 */
.area-info {
    position: absolute;
    right: 10px;
    color: white;
    border-radius: 4px;
    font-size: 14px;
    z-index: 100;
}

/* 武器槽位 */
.weapon-slots {
    display: flex;
    gap: 10px;
    padding: 10px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 8px;
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 100;
}

.weapon-slot {
    width: 60px;
    height: 60px;
    border: 2px solid #666;
    border-radius: 4px;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.weapon-slot.has-weapon {
    border-color: #ffd700;
    background: rgba(255, 215, 0, 0.1);
}

/* 技能喊话 */
.skill-shout {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    padding: 5px 15px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border-radius: 4px;
    font-size: 16px;
    animation: shoutAnimation 1s ease-out;
    z-index: 1000;
}

.player-shout {
    bottom: 100px;
}

.enemy-shout {
    top: 100px;
}

@keyframes shoutAnimation {
    0% {
        opacity: 0;
        transform: translateX(-50%) translateY(20px);
    }
    20% {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
    80% {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
    100% {
        opacity: 0;
        transform: translateX(-50%) translateY(-20px);
    }
}

/* 胜利面板 */
.victory-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    animation: fadeIn 0.3s ease;
}

.victory-message {
    font-size: 24px;
    color: #ffd700;
    margin-bottom: 20px;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.drop-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-width: 80%;
    max-height: 60vh;
    overflow-y: auto;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
}

/* 物品详情 */
.item-detail-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10001;
}

.item-detail-content {
    background: rgba(30, 30, 30, 0.95);
    padding: 20px;
    border-radius: 8px;
    min-width: 300px;
    max-width: 80%;
    color: white;
}

/* 品质颜色 */
.normal-text { color: #ffffff; }
.rare-text { color: #00ff00; }
.epic-text { color: #0070dd; }
.legendary-text { color: #a335ee; }
.mythic-text { color: #ff8000; }

/* 加载动画 */
.battle-loader-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10001;
    backdrop-filter: blur(5px);
}

.battle-loader {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.battle-loader-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.battle-loader-text {
    color: white;
    font-size: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* 响应式适配 */
@media (max-width: 768px) {
    .weapon-slots {
        bottom: 10px;
    }
    
    .weapon-slot {
        width: 50px;
        height: 50px;
    }
    
    .victory-message {
        font-size: 20px;
    }
    
    .item-detail-content {
        min-width: 250px;
        padding: 15px;
    }
}

/* 胜利面板按钮样式 - 🔧 修改为一行显示，缩小尺寸 */
.victory-actions {
    margin-top: 15px;
    display: flex;
    gap: 8px;
    justify-content: center;
    flex-wrap: wrap;
    max-width: 350px;
    margin-left: auto;
    margin-right: auto;
}

.victory-btn {
    padding: 8px 12px;
    border: 2px solid rgba(212, 175, 55, 0.6);
    border-radius: 8px;
    font-size: 11px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 36px;
    min-width: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    line-height: 1.2;
    position: relative;
    overflow: hidden;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(10px);
    box-shadow: 
        0 2px 4px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.victory-btn-primary {
    background: linear-gradient(135deg, rgba(34, 139, 34, 0.8), rgba(50, 205, 50, 0.6));
    color: white;
    border-color: rgba(50, 205, 50, 0.8);
}

.victory-btn-primary:hover {
    background: linear-gradient(135deg, rgba(50, 205, 50, 0.9), rgba(144, 238, 144, 0.7));
    border-color: rgba(144, 238, 144, 1);
    transform: translateY(-1px);
    box-shadow: 
        0 4px 8px rgba(0, 0, 0, 0.4),
        0 0 15px rgba(50, 205, 50, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.victory-btn-secondary {
    background: linear-gradient(135deg, rgba(139, 69, 19, 0.8), rgba(160, 82, 45, 0.6));
    color: #FFF8DC;
    border-color: rgba(160, 82, 45, 0.8);
}

.victory-btn-secondary:hover {
    background: linear-gradient(135deg, rgba(160, 82, 45, 0.9), rgba(205, 133, 63, 0.7));
    border-color: rgba(205, 133, 63, 1);
    transform: translateY(-1px);
    box-shadow: 
        0 4px 8px rgba(0, 0, 0, 0.4),
        0 0 15px rgba(205, 133, 63, 0.3),
        inset 0 1px 0 rgba(255, 248, 220, 0.2);
}

/* 响应式设计 */
@media (max-width: 480px) {
    .victory-actions {
        gap: 6px;
        max-width: 300px;
    }
    
    .victory-btn {
        font-size: 10px;
        padding: 6px 8px;
        min-height: 32px;
        min-width: 60px;
    }
}

@media (max-width: 360px) {
    .victory-actions {
        gap: 5px;
        max-width: 280px;
    }
    
    .victory-btn {
        font-size: 9px;
        padding: 5px 6px;
        min-height: 30px;
        min-width: 55px;
    }
}

@media (max-width: 320px) {
    .victory-actions {
        gap: 4px;
        max-width: 260px;
        flex-direction: column;
    }
    
    .victory-btn {
        font-size: 9px;
        padding: 5px 6px;
        min-height: 28px;
        min-width: 50px;
        width: 100%;
    }
}

/* 低耐久度警告样式 */
.weapon-item.low-durability {
    animation: lowDurabilityPulse 2s infinite;
}

/* 🔧 新增：损坏武器样式 */
.weapon-item.broken-weapon {
    background: linear-gradient(135deg, rgba(244, 67, 54, 0.2), rgba(244, 67, 54, 0.1));
    border-color: rgba(244, 67, 54, 0.6);
    animation: brokenWeaponPulse 3s infinite;
}

.weapon-item.broken-weapon .weapon-name {
    color: #f44336 !important;
    text-shadow: 0 0 8px rgba(244, 67, 54, 0.5);
}

.weapon-item.broken-weapon .weapon-skill {
    color: #ff9800 !important;
    font-style: italic;
}

@keyframes lowDurabilityPulse {
    0%, 100% {
        box-shadow: 0 0 5px rgba(244, 67, 54, 0.3);
    }
    50% {
        box-shadow: 0 0 15px rgba(244, 67, 54, 0.8);
    }
}

/* 🔧 新增：损坏武器脉冲动画 */
@keyframes brokenWeaponPulse {
    0%, 100% {
        box-shadow: 0 0 8px rgba(244, 67, 54, 0.4);
        border-color: rgba(244, 67, 54, 0.6);
    }
    33% {
        box-shadow: 0 0 15px rgba(244, 67, 54, 0.7);
        border-color: rgba(244, 67, 54, 0.8);
    }
    66% {
        box-shadow: 0 0 20px rgba(244, 67, 54, 0.9);
        border-color: rgba(244, 67, 54, 1.0);
    }
}

/* 🔧 新增：无效武器样式 */
.weapon-item.invalid-weapon {
    background: linear-gradient(135deg, rgba(220, 20, 60, 0.2), rgba(139, 0, 0, 0.3));
    border: 2px solid rgba(220, 20, 60, 0.6);
    color: #ff6b6b;
    animation: invalidWeaponPulse 3s ease-in-out infinite;
}

.weapon-item.invalid-weapon .weapon-name {
    color: #ff6b6b !important;
    font-style: italic !important;
    text-shadow: 0 0 8px rgba(255, 107, 107, 0.8) !important;
}

.weapon-item.invalid-weapon .weapon-skill {
    color: #ff6b6b !important;
    font-style: italic !important;
}

@keyframes invalidWeaponPulse {
    0%, 100% {
        opacity: 0.7;
        transform: scale(1);
        box-shadow: 0 0 10px rgba(220, 20, 60, 0.4);
    }
    50% {
        opacity: 1;
        transform: scale(1.02);
        box-shadow: 0 0 20px rgba(220, 20, 60, 0.8);
    }
}

/* ========== 从styles.css合并的战斗相关样式 ========== */

/* 战斗容器基础样式 - 与现有样式合并 */
.battle-container {
    background: rgba(0, 0, 0, 0.3);
    width: 100%;
    height: 100%;
    max-width: 648px;
    margin: 0 auto;
    padding: 15px;
    position: relative;
}

/* 角色定位样式 */
.character {
    position: absolute;
    z-index: 1;
    width: 100px; /* PC端固定人物大小 */
    height: 100px;
}

.character-sprite {
    width: 100%;
    height: 100%;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    transform-origin: center;
}

.player {
    bottom: 10%; /* PC端玩家位置 */
    left: 50%;
    transform: translateX(-50%);
}

.enemy {
    top: 10%; /* PC端敌人位置 */
    left: 50%;
    transform: translateX(-50%);
}

/* 按钮样式 */
button {
    padding: 10px 20px;
    margin: 0 10px;
    font-size: 16px;
    background: #3498db;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background 0.3s ease;
}

button:hover {
    background: #2980b9;
}

/* 战斗动画 */
@keyframes attack {
    0% { transform: translateX(0); }
    25% { transform: translateX(20px); }
    100% { transform: translateX(0); }
}

@keyframes hit-flip {
    0% {
        transform: scale(1);
    }
    25% {
        transform: scale(-1, 1);
    }
    75% {
        transform: scale(-1, 1);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes hit-flip-enemy {
    0% {
        transform: scale(1);
    }
    25% {
        transform: scale(-1, 1);
    }
    75% {
        transform: scale(-1, 1);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes hit {
    0% { transform: translateX(0); }
    25% { transform: translateX(-10px); }
    100% { transform: translateX(0); }
}

/* 特效样式 */
.effect {
    position: absolute;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, #f1c40f, transparent);
    border-radius: 50%;
    pointer-events: none;
}

/* 伤害数字样式 */
.damage-number {
    position: absolute;
    color: #e74c3c;
    font-size: min(24px, 5vw);
    font-weight: bold;
    text-shadow: 2px 2px 2px rgba(0, 0, 0, 0.5);
    pointer-events: none;
    animation: float-damage 1s ease-out forwards;
    transform: translateX(-50%);
    z-index: 1000;
}

/* 暴击伤害样式 */
.damage-number.critical {
    color: #f39c12;
    font-size: min(28px, 6vw);
    text-shadow: 
        0 0 10px rgba(243, 156, 18, 0.8),
        2px 2px 4px rgba(0, 0, 0, 0.8);
    animation: float-critical-damage 1.2s ease-out forwards;
}

/* 未命中效果样式 */
.miss-effect {
    position: absolute;
    color: #95a5a6;
    font-size: min(22px, 5vw);
    font-weight: bold;
    text-shadow: 2px 2px 2px rgba(0, 0, 0, 0.5);
    pointer-events: none;
    animation: float-miss 1s ease-out forwards;
    transform: translateX(-50%);
    z-index: 1000;
}

/* 伤害数字动画 */
@keyframes float-damage {
    0% {
        transform: translateX(-50%) translateY(0) scale(0.8);
        opacity: 0;
    }
    20% {
        transform: translateX(-50%) translateY(-10px) scale(1.1);
        opacity: 1;
    }
    100% {
        transform: translateX(-50%) translateY(-50px) scale(1);
        opacity: 0;
    }
}

/* 暴击伤害动画 */
@keyframes float-critical-damage {
    0% {
        transform: translateX(-50%) translateY(0) scale(0.8);
        opacity: 0;
    }
    15% {
        transform: translateX(-50%) translateY(-15px) scale(1.3);
        opacity: 1;
    }
    30% {
        transform: translateX(-50%) translateY(-20px) scale(1.2);
        opacity: 1;
    }
    100% {
        transform: translateX(-50%) translateY(-60px) scale(1);
        opacity: 0;
    }
}

/* 未命中动画 */
@keyframes float-miss {
    0% {
        transform: translateX(-50%) translateY(0) scale(0.8);
        opacity: 0;
    }
    20% {
        transform: translateX(-50%) translateY(-8px) scale(1.1);
        opacity: 1;
    }
    40% {
        transform: translateX(-50%) translateY(-12px) scale(1);
        opacity: 0.8;
    }
    60% {
        transform: translateX(-50%) translateY(-16px) scale(1);
        opacity: 0.6;
    }
    100% {
        transform: translateX(-50%) translateY(-40px) scale(0.9);
        opacity: 0;
    }
}

/* 技能喊话样式 */
.skill-shout {
    position: absolute;
    color: #fff;
    font-size: min(24px, 5vw);
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    pointer-events: none;
    z-index: 1000;
    animation: skill-shout 1s ease-out forwards;
    white-space: nowrap;
}

@keyframes skill-shout {
    0% {
        transform: translateY(0) scale(0.8);
        opacity: 0;
    }
    20% {
        transform: translateY(-20px) scale(1.2);
        opacity: 1;
    }
    80% {
        transform: translateY(-30px) scale(1.2);
        opacity: 1;
    }
    100% {
        transform: translateY(-40px) scale(1);
        opacity: 0;
    }
}

/* 电场特效 */
.electric-field {
    position: absolute;
    background: radial-gradient(ellipse, rgba(150, 220, 255, 0.1) 0%, rgba(100, 200, 255, 0.05) 50%, transparent 100%);
    animation: electric-field-pulse 0.4s ease-out forwards;
    z-index: 180;
}

@keyframes electric-field-pulse {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.6;
    }
    100% {
        transform: scale(1.5);
        opacity: 0;
    }
}

/* 掉落物品系统 */
.drop-item-container {
    position: absolute;
    width: 60px;
    height: 80px;
    z-index: 150;
}

/* 游戏内浮窗 */
.game-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    animation: overlay-fade-in 0.5s ease-out forwards;
}

@keyframes overlay-fade-in {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.drop-item-display:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* 不同品质的边框颜色 */
.drop-item-display.quality-gray {
    border-color: rgba(128, 128, 128, 0.8);
}

.drop-item-display.quality-green {
    border-color: rgba(0, 255, 0, 0.8);
}

.drop-item-display.quality-blue {
    border-color: rgba(0, 100, 255, 0.8);
}

.drop-item-display.quality-purple {
    border-color: rgba(128, 0, 255, 0.8);
}

.drop-item-display.quality-gold {
    border-color: rgba(255, 215, 0, 0.8);
}

.drop-item-display.quality-red {
    border-color: rgba(255, 0, 0, 0.8);
}

/* 品质文字颜色 */
.quality-gray .drop-item-quality {
    color: #c0c0c0;
}

.quality-green .drop-item-quality {
    color: #00ff00;
}

.quality-blue .drop-item-quality {
    color: #4080ff;
}

.quality-purple .drop-item-quality {
    color: #c040ff;
}

.quality-gold .drop-item-quality {
    color: #ffd700;
}

.quality-red .drop-item-quality {
    color: #ff4040;
}

/* 关闭按钮 */
.close-button {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #1a1a2e;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-shadow: none;
    box-shadow: 
        0 4px 15px rgba(255, 215, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    min-width: 120px;
}

.close-button:hover {
    transform: translateY(-2px);
    box-shadow: 
        0 6px 20px rgba(255, 215, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.close-button:active {
    transform: scale(0.9);
}

/* 倒计时特效样式 */
.countdown-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, 
        rgba(0, 0, 0, 0.9) 0%, 
        rgba(20, 20, 40, 0.95) 50%, 
        rgba(0, 0, 0, 0.9) 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    overflow: hidden;
}

.countdown-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 300px;
    height: 300px;
}

.countdown-number {
    position: absolute;
    font-size: 120px;
    font-weight: bold;
    color: #ffd700;
    text-shadow: 
        0 0 20px rgba(255, 215, 0, 1),
        0 0 40px rgba(255, 215, 0, 0.8),
        0 0 60px rgba(255, 215, 0, 0.6),
        4px 4px 8px rgba(0, 0, 0, 0.8);
    transform: scale(0) rotate(180deg);
    opacity: 0;
    transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    user-select: none;
    animation: countdown-pulse 1s ease-in-out infinite alternate;
}

.countdown-number.countdown-show {
    transform: scale(1) rotate(0deg);
    opacity: 1;
}

.countdown-number.countdown-fade-out {
    transform: scale(0.3) rotate(-180deg);
    opacity: 0;
    transition: all 0.3s ease-in;
}

.countdown-number.countdown-start {
    font-size: 80px;
    color: #00ff00;
    text-shadow: 
        0 0 20px rgba(0, 255, 0, 1),
        0 0 40px rgba(0, 255, 0, 0.8),
        0 0 60px rgba(0, 255, 0, 0.6),
        4px 4px 8px rgba(0, 0, 0, 0.8);
    animation: countdown-start-pulse 0.8s ease-out forwards;
}

@keyframes countdown-pulse {
    0% {
        text-shadow: 
            0 0 20px rgba(255, 215, 0, 1),
            0 0 40px rgba(255, 215, 0, 0.8),
            0 0 60px rgba(255, 215, 0, 0.6),
            4px 4px 8px rgba(0, 0, 0, 0.8);
    }
    100% {
        text-shadow: 
            0 0 30px rgba(255, 215, 0, 1),
            0 0 60px rgba(255, 215, 0, 1),
            0 0 90px rgba(255, 215, 0, 0.8),
            4px 4px 8px rgba(0, 0, 0, 0.8);
    }
}

@keyframes countdown-start-pulse {
    0% {
        transform: scale(1) rotate(0deg);
        text-shadow: 
            0 0 20px rgba(0, 255, 0, 1),
            0 0 40px rgba(0, 255, 0, 0.8),
            0 0 60px rgba(0, 255, 0, 0.6),
            4px 4px 8px rgba(0, 0, 0, 0.8);
    }
    50% {
        transform: scale(1.2) rotate(0deg);
        text-shadow: 
            0 0 40px rgba(0, 255, 0, 1),
            0 0 80px rgba(0, 255, 0, 1),
            0 0 120px rgba(0, 255, 0, 0.8),
            4px 4px 8px rgba(0, 0, 0, 0.8);
    }
    100% {
        transform: scale(1) rotate(0deg);
        text-shadow: 
            0 0 30px rgba(0, 255, 0, 1),
            0 0 60px rgba(0, 255, 0, 0.9),
            0 0 90px rgba(0, 255, 0, 0.7),
            4px 4px 8px rgba(0, 0, 0, 0.8);
    }
}

/* 倒计时背景粒子 */
.countdown-particle {
    position: absolute;
    background: radial-gradient(circle, 
        rgba(255, 215, 0, 0.8) 0%, 
        rgba(255, 215, 0, 0.4) 50%, 
        transparent 100%);
    border-radius: 50%;
    animation: countdown-particle-float 3s ease-in-out infinite;
    pointer-events: none;
}

@keyframes countdown-particle-float {
    0%, 100% {
        transform: translateY(0px) translateX(0px) scale(1);
        opacity: 0.6;
    }
    25% {
        transform: translateY(-20px) translateX(10px) scale(1.2);
        opacity: 1;
    }
    50% {
        transform: translateY(-40px) translateX(-5px) scale(0.8);
        opacity: 0.8;
    }
    75% {
        transform: translateY(-20px) translateX(-15px) scale(1.1);
        opacity: 0.9;
    }
}

/* 倒计时环形特效 */
.countdown-container::before {
    content: '';
    position: absolute;
    width: 250px;
    height: 250px;
    border: 3px solid rgba(255, 215, 0, 0.3);
    border-radius: 50%;
    animation: countdown-ring-rotate 2s linear infinite;
}

.countdown-container::after {
    content: '';
    position: absolute;
    width: 200px;
    height: 200px;
    border: 2px solid rgba(255, 215, 0, 0.2);
    border-radius: 50%;
    animation: countdown-ring-rotate 3s linear infinite reverse;
}

@keyframes countdown-ring-rotate {
    0% {
        transform: rotate(0deg);
        border-color: rgba(255, 215, 0, 0.3);
    }
    50% {
        border-color: rgba(255, 215, 0, 0.6);
    }
    100% {
        transform: rotate(360deg);
        border-color: rgba(255, 215, 0, 0.3);
    }
}

/* 按钮容器 */
.button-container {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 20px;
}

.exit-button {
    background: linear-gradient(135deg, #f44336, #da190b) !important;
}

.continue-button {
    background: linear-gradient(135deg, #4CAF50, #45a049);
}

/* 武器耐久度样式 */
.weapon-durability {
    margin-top: 4px;
    font-size: 10px;
}

.durability-bar {
    width: 100%;
    height: 4px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 2px;
}

.durability-fill {
    height: 100%;
    transition: width 0.3s ease, background-color 0.3s ease;
    border-radius: 2px;
}

.durability-text {
    color: rgba(255, 255, 255, 0.8);
    font-size: 9px;
    text-align: center;
}

/* 品质颜色 */
.quality-common { border-color: #ffffff; }
.quality-uncommon { border-color: #1eff00; }
.quality-rare { border-color: #0070dd; }
.quality-epic { border-color: #a335ee; }
.quality-legendary { border-color: #ff8000; }

/* 警告动画 */
@keyframes warningPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* 动态战报系统样式 - 统一完整定义 */
.battle-status {
    /* 完整的战报样式定义 - 统一在此处管理 */
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    padding: 8px 16px;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5));
    color: white;
    border-radius: 16px;
    font-size: 13px;
    text-align: center;
    min-height: 20px;
    line-height: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    overflow: hidden;
    white-space: nowrap;
    z-index: 150; /* 确保在最上层 */
    max-width: 180px; /* 避免覆盖两侧元素 */
    min-width: 80px;
    pointer-events: none; /* 不阻挡交互 */
}

/* 战报类型样式 */
.battle-status.damage {
    background: linear-gradient(135deg, rgba(255,107,107,0.2), rgba(220,20,60,0.1)) !important;
    color: #ff6b6b !important;
    border-color: rgba(255,107,107,0.3) !important;
}

.battle-status.heal {
    background: linear-gradient(135deg, rgba(78,205,196,0.2), rgba(26,188,156,0.1)) !important;
    color: #4ecdc4 !important;
    border-color: rgba(78,205,196,0.3) !important;
}

.battle-status.skill {
    background: linear-gradient(135deg, rgba(255,217,61,0.2), rgba(255,165,0,0.1)) !important;
    color: #ffd93d !important;
    border-color: rgba(255,217,61,0.3) !important;
}

.battle-status.status {
    background: linear-gradient(135deg, rgba(162,155,254,0.2), rgba(116,185,255,0.1)) !important;
    color: #a29bfe !important;
    border-color: rgba(162,155,254,0.3) !important;
}

/* 🔥 新增：回合数警告样式 */
.battle-status.warning {
    background: linear-gradient(135deg, rgba(255,165,0,0.3), rgba(255,69,0,0.2)) !important;
    color: #ffa500 !important;
    border-color: rgba(255,165,0,0.4) !important;
    animation: warningPulse 1.5s ease-in-out infinite !important;
}

@keyframes warningPulse {
    0%, 100% { 
        transform: translate(-50%, -50%) scale(1);
        box-shadow: 0 2px 8px rgba(255,165,0,0.3);
    }
    50% { 
        transform: translate(-50%, -50%) scale(1.02);
        box-shadow: 0 4px 12px rgba(255,165,0,0.5);
    }
}

/* 战报出现动画 */
@keyframes battleReportPulse {
    0% { 
        transform: translate(-50%, -50%) scale(1); 
    }
    50% { 
        transform: translate(-50%, -50%) scale(1.05); 
    }
    100% { 
        transform: translate(-50%, -50%) scale(1); 
    }
}

.battle-status.animated {
    animation: battleReportPulse 0.3s ease !important;
}
  
/* 移动端战报适配 */
@media (max-width: 768px) {
    .battle-status {
        font-size: 12px !important;
        padding: 6px 12px !important;
        min-height: 18px !important;
        line-height: 18px !important;
        max-width: 160px !important; /* 移动端更小的最大宽度 */
        min-width: 80px !important;
    }
    
    /* 🔥 移动端回合显示适配 */
    .round-display {
        font-size: 11px !important;
        padding: 3px 6px !important;
        right: 8px !important;
    }
    
    .countdown-number {
        font-size: 80px;
    }
    
    .countdown-number.countdown-start {
        font-size: 60px;
    }
    
    .countdown-container {
        width: 250px;
        height: 250px;
    }
    
    .countdown-container::before {
        width: 200px;
        height: 200px;
    }
    
    .countdown-container::after {
        width: 150px;
        height: 150px;
    }
}

/* 超小屏幕适配 */
@media (max-width: 480px) {
    .battle-status {
        font-size: 11px !important;
        padding: 4px 10px !important;
        min-height: 16px !important;
        line-height: 16px !important;
        max-width: 140px !important;
        min-width: 70px !important;
        border-radius: 12px !important;
    }
    
    /* 确保在小屏幕上动画正常 */
    @keyframes battleReportPulse {
        0% { 
            transform: translate(-50%, -50%) scale(1); 
        }
        50% { 
            transform: translate(-50%, -50%) scale(1.03); 
        }
        100% { 
            transform: translate(-50%, -50%) scale(1); 
        }
    }
}
