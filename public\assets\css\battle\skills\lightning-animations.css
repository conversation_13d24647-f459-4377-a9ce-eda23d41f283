/**
 * 雷法技能动画样式
 * 包含掌心雷等所有雷法技能的动画效果
 */

/* 🌩️ 掌心雷技能样式 */
.lightning-container {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 200;
}

/* 蓄力阶段样式 */
.lightning-charge-container {
    position: absolute;
    transform: translate(-50%, -50%);
    pointer-events: none;
    z-index: 195;
}

.lightning-charge-core {
    position: absolute;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: radial-gradient(circle,
        rgba(255, 255, 255, 1) 0%,
        rgba(150, 220, 255, 0.9) 30%,
        rgba(100, 200, 255, 0.7) 60%,
        rgba(150, 220, 255, 0.3) 90%,
        transparent 100%
    );
    transform: translate(-50%, -50%);
    animation: lightning-charge-core-pulse 0.8s ease-out forwards;
    -webkit-filter: drop-shadow(0 0 20px rgba(150, 220, 255, 0.9));
    filter: drop-shadow(0 0 20px rgba(150, 220, 255, 0.9));
}

@keyframes lightning-charge-core-pulse {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0;
    }
    30% {
        transform: translate(-50%, -50%) scale(1.2);
        opacity: 1;
    }
    70% {
        transform: translate(-50%, -50%) scale(2);
        opacity: 0.8;
    }
    100% {
        transform: translate(-50%, -50%) scale(3);
        opacity: 0;
    }
}

.lightning-charge-field {
    position: absolute;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: radial-gradient(circle,
        transparent 60%,
        rgba(150, 220, 255, 0.2) 70%,
        rgba(100, 200, 255, 0.1) 85%,
        transparent 100%
    );
    transform: translate(-50%, -50%);
    animation: lightning-charge-field-pulse 0.8s ease-out forwards;
    -webkit-filter: blur(2px);
    filter: blur(2px);
}

@keyframes lightning-charge-field-pulse {
    0% {
        transform: translate(-50%, -50%) scale(0) rotate(0deg);
        opacity: 0;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.5) rotate(180deg);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(2.5) rotate(360deg);
        opacity: 0;
    }
}

/* 环绕小闪电样式 */
.charge-lightning {
    position: absolute;
    width: 20px;
    height: 4px;
    background: linear-gradient(90deg, 
        transparent 0%,
        rgba(150, 220, 255, 1) 20%,
        rgba(255, 255, 255, 1) 50%,
        rgba(150, 220, 255, 1) 80%,
        transparent 100%
    );
    transform-origin: left center;
    border-radius: 2px;
    animation: charge-lightning-rotate 0.8s linear infinite;
    -webkit-filter: drop-shadow(0 0 8px rgba(150, 220, 255, 0.8));
    filter: drop-shadow(0 0 8px rgba(150, 220, 255, 0.8));
}

@keyframes charge-lightning-rotate {
    0% {
        transform: rotate(var(--angle)) scaleX(0.5);
        opacity: 0.3;
    }
    50% {
        transform: rotate(calc(var(--angle) + 90deg)) scaleX(1.2);
        opacity: 1;
    }
    100% {
        transform: rotate(calc(var(--angle) + 180deg)) scaleX(0.8);
        opacity: 0.6;
    }
}

.lightning-charge-particle {
    position: absolute;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: radial-gradient(circle, 
        rgba(150, 220, 255, 1) 0%, 
        rgba(100, 200, 255, 0.8) 60%, 
        transparent 100%);
    animation: lightning-charge-particle-move 1.2s ease-out infinite;
    -webkit-filter: drop-shadow(0 0 8px rgba(150, 220, 255, 0.8));
    filter: drop-shadow(0 0 8px rgba(150, 220, 255, 0.8));
}

@keyframes lightning-charge-particle-move {
    0% {
        transform: translate(var(--chargeX), var(--chargeY)) scale(0.5);
        opacity: 0;
    }
    20% {
        transform: translate(calc(var(--chargeX) * 0.7), calc(var(--chargeY) * 0.7)) scale(1);
        opacity: 1;
    }
    80% {
        transform: translate(calc(var(--chargeX) * 0.2), calc(var(--chargeY) * 0.2)) scale(1.2);
        opacity: 0.8;
    }
    100% {
        transform: translate(0, 0) scale(0);
        opacity: 0;
    }
}

.lightning-charge-arc-ring {
    position: absolute;
    width: 60px;
    height: 60px;
    border: 2px solid rgba(150, 220, 255, 0.7);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: lightning-charge-arc-ring-expand 1.2s ease-out forwards;
    -webkit-filter: drop-shadow(0 0 10px rgba(150, 220, 255, 0.8));
    filter: drop-shadow(0 0 10px rgba(150, 220, 255, 0.8));
}

@keyframes lightning-charge-arc-ring-expand {
    0% {
        transform: translate(-50%, -50%) scale(0) rotate(0deg);
        opacity: 0;
        border-color: rgba(150, 220, 255, 0.9);
    }
    30% {
        transform: translate(-50%, -50%) scale(0.8) rotate(120deg);
        opacity: 1;
        border-color: rgba(100, 200, 255, 0.8);
    }
    60% {
        transform: translate(-50%, -50%) scale(1.5) rotate(240deg);
        opacity: 0.7;
        border-color: rgba(200, 230, 255, 0.6);
    }
    100% {
        transform: translate(-50%, -50%) scale(2.2) rotate(360deg);
        opacity: 0;
        border-color: rgba(180, 210, 255, 0.3);
    }
}

/* 预闪效果 */
.lightning-preflash {
    position: absolute;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: radial-gradient(circle, 
        rgba(255, 255, 255, 0.8) 0%, 
        rgba(150, 220, 255, 0.6) 30%, 
        transparent 70%);
    animation: preflash-pulse 0.2s ease-out forwards;
    z-index: 190;
}

@keyframes preflash-pulse {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.8;
    }
    100% {
        transform: scale(0);
        opacity: 0;
    }
}

/* 闪电路径样式 */
.zigzag-lightning {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 200;
}

/* 闪光效果 */
.lightning-flash-screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, 
        rgba(255, 255, 255, 0.4) 0%, 
        rgba(150, 220, 255, 0.2) 50%, 
        transparent 100%);
    animation: screen-flash 0.2s ease-out forwards;
    z-index: 250;
}

@keyframes screen-flash {
    0% {
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0;
    }
}

/* 电弧效果 */
.lightning-arc {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 185;
}

.lightning-particle {
    position: absolute;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: radial-gradient(circle, 
        rgba(255, 255, 255, 1) 0%, 
        rgba(150, 220, 255, 0.8) 50%, 
        transparent 100%);
    -webkit-filter: drop-shadow(0 0 6px rgba(150, 220, 255, 0.9));
    filter: drop-shadow(0 0 6px rgba(150, 220, 255, 0.9));
}

@keyframes lightning-particle {
    0% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) translate(var(--moveX), var(--moveY)) scale(0);
        opacity: 0;
    }
}

/* 冲击波效果 */
.lightning-impact {
    position: absolute;
    width: 80px;
    height: 80px;
    background: radial-gradient(circle, 
        rgba(255, 255, 255, 0.9) 0%,
        rgba(150, 220, 255, 0.7) 20%,
        rgba(100, 200, 255, 0.4) 40%,
        rgba(150, 220, 255, 0.2) 60%,
        transparent 100%
    );
    border-radius: 50%;
    pointer-events: none;
    z-index: 190;
}

@keyframes lightning-impact {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(3);
        opacity: 0;
    }
}

/* 电场效果 */
.electric-field {
    position: absolute;
    background: radial-gradient(ellipse, 
        rgba(150, 220, 255, 0.1) 0%, 
        rgba(100, 200, 255, 0.05) 50%, 
        transparent 100%);
    animation: electric-field-pulse 0.4s ease-out forwards;
    z-index: 180;
}

@keyframes electric-field-pulse {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.6;
    }
    100% {
        transform: scale(1.5);
        opacity: 0;
    }
}

/* 闪电绘制动画 */
@keyframes lightning-draw {
    0% {
        stroke-dasharray: 1000;
        stroke-dashoffset: 1000;
        opacity: 0;
    }
    100% {
        stroke-dasharray: 1000;
        stroke-dashoffset: 0;
        opacity: 1;
    }
}

@keyframes lightning-intense-flicker {
    0%, 100% {
        opacity: 1;
        -webkit-filter: brightness(1);
        filter: brightness(1);
    }
    20% {
        opacity: 0.7;
        -webkit-filter: brightness(1.5);
        filter: brightness(1.5);
    }
    40% {
        opacity: 1;
        -webkit-filter: brightness(0.8);
        filter: brightness(0.8);
    }
    60% {
        opacity: 0.9;
        -webkit-filter: brightness(1.3);
        filter: brightness(1.3);
    }
    80% {
        opacity: 0.8;
        -webkit-filter: brightness(1.1);
        filter: brightness(1.1);
    }
}

/* 角色受击动画 */
@keyframes lightning-hit {
    0%, 100% {
        -webkit-filter: brightness(1) saturate(1) hue-rotate(0deg);
        filter: brightness(1) saturate(1) hue-rotate(0deg);
    }
    25% {
        -webkit-filter: brightness(2.5) saturate(1.8) hue-rotate(200deg);
        filter: brightness(2.5) saturate(1.8) hue-rotate(200deg);
    }
    50% {
        -webkit-filter: brightness(3.0) saturate(2.0) hue-rotate(180deg);
        filter: brightness(3.0) saturate(2.0) hue-rotate(180deg);
    }
    75% {
        -webkit-filter: brightness(2.2) saturate(1.6) hue-rotate(240deg);
        filter: brightness(2.2) saturate(1.6) hue-rotate(240deg);
    }
}

@keyframes lightning-shake {
    0%, 100% {
        transform: translateX(0) translateY(0);
    }
    12.5% {
        transform: translateX(-6px) translateY(-4px);
    }
    25% {
        transform: translateX(5px) translateY(3px);
    }
    37.5% {
        transform: translateX(-5px) translateY(6px);
    }
    50% {
        transform: translateX(6px) translateY(-3px);
    }
    62.5% {
        transform: translateX(-4px) translateY(-6px);
    }
    75% {
        transform: translateX(4px) translateY(4px);
    }
    87.5% {
        transform: translateX(-3px) translateY(2px);
    }
}

/* 移动端适配 */
@media (max-width: 480px) {
    .lightning-charge-core {
        width: 20px;
        height: 20px;
    }
    
    .lightning-charge-field {
        width: 60px;
        height: 60px;
    }
    
    .charge-lightning {
        width: 15px;
        height: 3px;
    }
    
    .lightning-charge-particle {
        width: 4px;
        height: 4px;
    }
    
    .lightning-charge-arc-ring {
        width: 40px;
        height: 40px;
    }
    
    .lightning-preflash {
        width: 40px;
        height: 40px;
    }
    
    .lightning-impact {
        width: 60px;
        height: 60px;
    }
    
    .lightning-particle {
        width: 3px;
        height: 3px;
    }
}

/* 🗲⚔️ 雷剑技能动画样式 */

/* 雷剑容器 */
.leijian-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1000;
    will-change: auto;
    transform: translateZ(0);
}

/* 第一阶段：雁形阵雷剑样式 */
.leijian-formation-sword {
    position: absolute;
    width: 30px;
    height: 60px;
    opacity: 0;
    transform: translate(-50%, -50%);
    pointer-events: none;
    z-index: 200;
    
    /* 支持背景图片显示 */
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    
    /* 雷电光晕效果 */
    -webkit-filter: drop-shadow(0 0 8px #4fc3f7);
    filter: drop-shadow(0 0 8px #4fc3f7) 
            drop-shadow(0 0 16px #2196f3)
            drop-shadow(0 0 24px #1976d2);
    
    /* 雁形阵陆续出现动画 */
    animation: leijian-formation-appear 0.8s ease-out forwards;
    animation-delay: var(--delay, 0s);
}

/* 雁形阵武器图片样式 */
.leijian-formation-weapon-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    z-index: 1;
    position: relative;
    opacity: 1 !important;
    -webkit-filter: brightness(1.3) saturate(1.2);
    filter: brightness(1.3) saturate(1.2);
}

@keyframes leijian-formation-appear {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0);
        -webkit-filter: drop-shadow(0 0 4px #4fc3f7);
        filter: drop-shadow(0 0 4px #4fc3f7);
    }
    50% {
        opacity: 0.8;
        transform: translate(-50%, -50%) scale(1.2);
        -webkit-filter: drop-shadow(0 0 12px #4fc3f7);
        filter: drop-shadow(0 0 12px #4fc3f7) 
                drop-shadow(0 0 24px #2196f3);
    }
    100% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
        -webkit-filter: drop-shadow(0 0 8px #4fc3f7); 
        filter: drop-shadow(0 0 8px #4fc3f7) 
                drop-shadow(0 0 16px #2196f3)
                drop-shadow(0 0 24px #1976d2);
    }
}

@keyframes leijian-formation-rotate {
    0% {
        transform: translate(-50%, -50%) scale(1) rotate(0deg);
    }
    100% {
        transform: translate(-50%, -50%) scale(1) rotate(360deg);
    }
}

/* 雁形阵小剑跟随飞行样式 */
.leijian-formation-sword.following {
    animation: leijian-formation-follow 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    animation-delay: var(--follow-delay, 0s);
}

@keyframes leijian-formation-follow {
    0% {
        left: var(--startX);
        top: var(--startY);
        transform: translate(-50%, -50%) scale(1) rotate(calc(180deg + var(--angle, 0deg)));
        opacity: 1;
        -webkit-filter: drop-shadow(0 0 8px #4fc3f7); 
        filter: drop-shadow(0 0 8px #4fc3f7) 
                drop-shadow(0 0 16px #2196f3)
                drop-shadow(0 0 24px #1976d2);
    }
    40% {
        left: var(--targetX);
        top: var(--targetY);
        transform: translate(-50%, -50%) scale(1.1) rotate(calc(180deg + var(--angle, 0deg)));
        opacity: 1;
        -webkit-filter: drop-shadow(0 0 15px #4fc3f7); 
        filter: drop-shadow(0 0 15px #4fc3f7) 
                drop-shadow(0 0 30px #2196f3)
                drop-shadow(0 0 45px #1976d2);
    }
    100% {
        left: var(--penetrateX);
        top: var(--penetrateY);
        transform: translate(-50%, -50%) scale(0.6) rotate(calc(180deg + var(--angle, 0deg)));
        opacity: 0;
        -webkit-filter: drop-shadow(0 0 5px #4fc3f7);
        filter: drop-shadow(0 0 5px #4fc3f7);
    }
}

/* 雁形阵电弧连接效果 */
.leijian-formation-arc {
    position: absolute;
    pointer-events: none;
    z-index: 198;
}

.leijian-charge-container {
    position: absolute;
    pointer-events: none;
    z-index: 195;
}

.leijian-charge-core {
    position: absolute;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: radial-gradient(circle,
        rgba(255, 255, 255, 1) 0%,
        rgba(200, 230, 255, 0.9) 20%,
        rgba(150, 220, 255, 0.8) 40%,
        rgba(100, 200, 255, 0.6) 70%,
        transparent 100%
    );
    transform: translate(-50%, -50%);
    animation: leijian-charge-core-pulse 1.2s ease-out forwards;
    -webkit-filter: drop-shadow(0 0 25px rgba(150, 220, 255, 1));
    filter: drop-shadow(0 0 25px rgba(150, 220, 255, 1));
    box-shadow: 
        0 0 20px rgba(255, 255, 255, 0.8),
        0 0 40px rgba(150, 220, 255, 0.6),
        0 0 60px rgba(100, 200, 255, 0.4);
}

@keyframes leijian-charge-core-pulse {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0;
    }
    20% {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 1;
    }
    60% {
        transform: translate(-50%, -50%) scale(1.5) rotate(180deg);
        opacity: 0.9;
    }
    100% {
        transform: translate(-50%, -50%) scale(2.5) rotate(360deg);
        opacity: 0;
    }
}

.leijian-charge-lightning {
    position: absolute;
    width: 30px;
    height: 5px;
    background: linear-gradient(90deg, 
        transparent 0%,
        rgba(200, 230, 255, 0.8) 10%,
        rgba(255, 255, 255, 1) 30%,
        rgba(150, 220, 255, 1) 50%,
        rgba(255, 255, 255, 1) 70%,
        rgba(200, 230, 255, 0.8) 90%,
        transparent 100%
    );
    transform-origin: left center;
    border-radius: 3px;
    animation: leijian-charge-lightning-rotate 1.2s linear forwards;
    -webkit-filter: drop-shadow(0 0 12px rgba(150, 220, 255, 0.9));
    filter: drop-shadow(0 0 12px rgba(150, 220, 255, 0.9));
    box-shadow: 0 0 8px rgba(255, 255, 255, 0.6);
}

@keyframes leijian-charge-lightning-rotate {
    0% {
        transform: rotate(var(--angle)) translate(25px, 0) scaleX(0.3) scaleY(0.5);
        opacity: 0;
    }
    30% {
        transform: rotate(calc(var(--angle) + 120deg)) translate(35px, 0) scaleX(1.2) scaleY(1);
        opacity: 1;
    }
    70% {
        transform: rotate(calc(var(--angle) + 300deg)) translate(40px, 0) scaleX(0.8) scaleY(1.2);
        opacity: 0.8;
    }
    100% {
        transform: rotate(calc(var(--angle) + 480deg)) translate(50px, 0) scaleX(0.4) scaleY(0.6);
        opacity: 0;
    }
}

.leijian-charge-particle {
    position: absolute;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: radial-gradient(circle, 
        rgba(255, 255, 255, 1) 0%, 
        rgba(200, 230, 255, 0.9) 30%,
        rgba(150, 220, 255, 0.7) 60%, 
        transparent 100%);
    animation: leijian-charge-particle-move 1.2s ease-out forwards;
    animation-delay: var(--random-delay);
    -webkit-filter: drop-shadow(0 0 10px rgba(150, 220, 255, 0.8));
    filter: drop-shadow(0 0 10px rgba(150, 220, 255, 0.8));
}

@keyframes leijian-charge-particle-move {
    0% {
        transform: rotate(var(--random-angle)) translate(var(--random-distance), 0) scale(0.3);
        opacity: 0;
    }
    20% {
        transform: rotate(calc(var(--random-angle) + 60deg)) translate(calc(var(--random-distance) * 0.8), 0) scale(1);
        opacity: 1;
    }
    80% {
        transform: rotate(calc(var(--random-angle) + 200deg)) translate(calc(var(--random-distance) * 0.3), 0) scale(1.2);
        opacity: 0.8;
    }
    100% {
        transform: rotate(calc(var(--random-angle) + 360deg)) translate(0, 0) scale(0);
        opacity: 0;
    }
}

/* 第二阶段：雷剑生成样式 */
.leijian-sword {
    position: absolute;
    width: 45px;
    height: 90px;
    transform-origin: center;
    pointer-events: none;
    z-index: 200;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    animation: leijian-sword-materialize 0.5s ease-out forwards;
    -webkit-filter: drop-shadow(0 0 15px rgba(150, 220, 255, 0.8)); 
    filter: drop-shadow(0 0 15px rgba(150, 220, 255, 0.8))
            drop-shadow(0 0 30px rgba(33, 150, 243, 0.6))
            drop-shadow(0 0 45px rgba(25, 118, 210, 0.4));
}

.leijian-weapon-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    z-index: 1;
    position: relative;
    opacity: 1 !important;
    -webkit-filter: brightness(1.3) saturate(1.2);
    filter: brightness(1.3) saturate(1.2);
}

@keyframes leijian-sword-materialize {
    0% {
        transform: translate(-50%, -50%) scale(0) rotate(0deg);
        opacity: 0;
        -webkit-filter: blur(10px) drop-shadow(0 0 15px rgba(150, 220, 255, 0.8));
        filter: blur(10px) drop-shadow(0 0 15px rgba(150, 220, 255, 0.8));
    }
    30% {
        transform: translate(-50%, -50%) scale(1.3) rotate(180deg);
        opacity: 0.7;
        -webkit-filter: blur(5px) drop-shadow(0 0 20px rgba(150, 220, 255, 1));
        filter: blur(5px) drop-shadow(0 0 20px rgba(150, 220, 255, 1));
    }
    100% {
        transform: translate(-50%, -50%) scale(1) rotate(360deg);
        opacity: 1;
        -webkit-filter: blur(0px) drop-shadow(0 0 15px rgba(150, 220, 255, 0.8));
        filter: blur(0px) drop-shadow(0 0 15px rgba(150, 220, 255, 0.8));
    }
}

/* 第三阶段：雷剑穿透飞行样式 */
.leijian-sword.penetrating {
    animation: leijian-penetrate 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    transform-origin: center center;
}

@keyframes leijian-penetrate {
    0% {
        left: var(--startX);
        top: var(--startY);
        transform: translate(-50%, -50%) rotate(var(--angle, 0deg)) scale(1);
        opacity: 1;
        -webkit-filter: drop-shadow(0 0 15px rgba(150, 220, 255, 0.8));
        filter: drop-shadow(0 0 15px rgba(150, 220, 255, 0.8));
    }
    40% {
        left: var(--targetX);
        top: var(--targetY);
        transform: translate(-50%, -50%) rotate(var(--angle, 0deg)) scale(1.2);
        opacity: 1;
        -webkit-filter: drop-shadow(0 0 25px rgba(150, 220, 255, 1)) drop-shadow(0 0 50px rgba(100, 200, 255, 0.8));
        filter: drop-shadow(0 0 25px rgba(150, 220, 255, 1)) drop-shadow(0 0 50px rgba(100, 200, 255, 0.8));
    }
    100% {
        left: var(--penetrateX);
        top: var(--penetrateY);
        transform: translate(-50%, -50%) rotate(var(--angle, 0deg)) scale(0.6);
        opacity: 0;
        -webkit-filter: drop-shadow(0 0 5px rgba(150, 220, 255, 0.3));
        filter: drop-shadow(0 0 5px rgba(150, 220, 255, 0.3));
    }
}

/* 保留原有的flying动画作为备用 */
.leijian-sword.flying {
    animation: leijian-fly 1.5s ease-out forwards;
    transform-origin: center center;
}

@keyframes leijian-fly {
    0% {
        left: var(--startX);
        top: var(--startY);
        transform: translate(-50%, -50%) rotate(var(--angle, 0deg)) scale(1);
        opacity: 1;
    }
    100% {
        left: var(--targetX);
        top: var(--targetY);
        transform: translate(-50%, -50%) rotate(var(--angle, 0deg)) scale(1.2);
        opacity: 0.9;
    }
}

/* 雷剑专用闪电轨迹效果 */
.leijian-trail-lightning {
    position: absolute;
    pointer-events: none;
    z-index: 190;
}

/* SVG闪电路径动画 */
@keyframes leijian-trail-flicker {
    0% {
        opacity: 0;
        stroke-width: 5;
        -webkit-filter: drop-shadow(0 0 15px #4fc3f7);
        filter: drop-shadow(0 0 15px #4fc3f7);
    }
    20% {
        opacity: 1;
        stroke-width: 3;
        -webkit-filter: drop-shadow(0 0 20px #4fc3f7) drop-shadow(0 0 40px #2196f3);
        filter: drop-shadow(0 0 20px #4fc3f7) drop-shadow(0 0 40px #2196f3);
    }
    80% {
        opacity: 0.8;
        stroke-width: 2;
        -webkit-filter: drop-shadow(0 0 10px #4fc3f7) drop-shadow(0 0 20px #2196f3);
        filter: drop-shadow(0 0 10px #4fc3f7) drop-shadow(0 0 20px #2196f3);
    }
    100% {
        opacity: 0;
        stroke-width: 1;
        -webkit-filter: drop-shadow(0 0 5px #4fc3f7);
        filter: drop-shadow(0 0 5px #4fc3f7);
    }
}

@keyframes leijian-trail-core-flicker {
    0% {
        opacity: 0;
        stroke-width: 2;
    }
    30% {
        opacity: 1;
        stroke-width: 1;
    }
    100% {
        opacity: 0;
        stroke-width: 0.5;
    }
}

@keyframes leijian-trail-branch-flicker {
    0% {
        opacity: 0;
        stroke-width: 3;
    }
    50% {
        opacity: 0.7;
        stroke-width: 2;
    }
    100% {
        opacity: 0;
        stroke-width: 1;
    }
}

/* 雷剑专用敌人受击动画 */
@keyframes leijian-enemy-hit {
    0%, 100% {
        -webkit-filter: brightness(1) saturate(1) hue-rotate(0deg) drop-shadow(0 0 0 transparent);
        filter: brightness(1) saturate(1) hue-rotate(0deg) drop-shadow(0 0 0 transparent);
    }
    20% {
        -webkit-filter: brightness(2.8) saturate(2.2) hue-rotate(190deg); 
        filter: brightness(2.8) saturate(2.2) hue-rotate(190deg) 
                drop-shadow(0 0 15px rgba(79, 195, 247, 0.9))
                drop-shadow(0 0 30px rgba(33, 150, 243, 0.6));
    }
    40% {
        -webkit-filter: brightness(3.5) saturate(2.5) hue-rotate(180deg); 
        filter: brightness(3.5) saturate(2.5) hue-rotate(180deg) 
                drop-shadow(0 0 20px rgba(255, 255, 255, 0.9))
                drop-shadow(0 0 40px rgba(79, 195, 247, 0.8))
                drop-shadow(0 0 60px rgba(33, 150, 243, 0.5));
    }
    60% {
        -webkit-filter: brightness(2.5) saturate(2.0) hue-rotate(200deg); 
        filter: brightness(2.5) saturate(2.0) hue-rotate(200deg) 
                drop-shadow(0 0 18px rgba(79, 195, 247, 0.8))
                drop-shadow(0 0 35px rgba(33, 150, 243, 0.6));
    }
    80% {
        -webkit-filter: brightness(1.8) saturate(1.5) hue-rotate(220deg); 
        filter: brightness(1.8) saturate(1.5) hue-rotate(220deg) 
                drop-shadow(0 0 12px rgba(79, 195, 247, 0.6))
                drop-shadow(0 0 25px rgba(33, 150, 243, 0.4));
    }
}

@keyframes leijian-enemy-shake {
    0%, 100% {
        transform: translateX(0) translateY(0);
    }
    10% {
        transform: translateX(-7px) translateY(-5px);
    }
    20% {
        transform: translateX(6px) translateY(4px);
    }
    30% {
        transform: translateX(-6px) translateY(7px);
    }
    40% {
        transform: translateX(7px) translateY(-4px);
    }
    50% {
        transform: translateX(-5px) translateY(-7px);
    }
    60% {
        transform: translateX(5px) translateY(5px);
    }
    70% {
        transform: translateX(-4px) translateY(3px);
    }
    80% {
        transform: translateX(4px) translateY(-3px);
    }
    90% {
        transform: translateX(-3px) translateY(2px);
    }
}

/* 保留原有的雷电尾迹效果作为备用 */
.leijian-lightning-trail {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 6px;
    height: 0;
    background: linear-gradient(to bottom,
        transparent 0%,
        rgba(255, 255, 255, 0.9) 10%,
        rgba(200, 230, 255, 0.8) 30%,
        rgba(150, 220, 255, 0.7) 50%,
        rgba(100, 200, 255, 0.6) 70%,
        rgba(150, 220, 255, 0.4) 90%,
        transparent 100%
    );
    transform: translate(-50%, -100%);
    border-radius: 3px;
    animation: leijian-trail-extend 1.5s ease-out forwards;
    -webkit-filter: drop-shadow(0 0 8px rgba(150, 220, 255, 0.8));
    filter: drop-shadow(0 0 8px rgba(150, 220, 255, 0.8));
    z-index: -1;
}

@keyframes leijian-trail-extend {
    0% {
        height: 0;
        opacity: 0;
    }
    20% {
        height: 40px;
        opacity: 1;
    }
    80% {
        height: 120px;
        opacity: 0.9;
    }
    100% {
        height: 160px;
        opacity: 0.6;
    }
}

/* 第四阶段：雷电爆炸样式 */
.leijian-explosion {
    position: absolute;
    pointer-events: none;
    z-index: 190;
}

.leijian-explosion-branch {
    position: absolute;
    width: 4px;
    height: 60px;
    background: linear-gradient(to bottom,
        rgba(255, 255, 255, 1) 0%,
        rgba(200, 230, 255, 0.9) 20%,
        rgba(150, 220, 255, 0.8) 50%,
        rgba(100, 200, 255, 0.6) 80%,
        transparent 100%
    );
    transform-origin: bottom center;
    transform: translate(-50%, -100%) rotate(var(--angle)) scale(0);
    border-radius: 2px;
    animation: leijian-explosion-branch-expand 0.8s ease-out forwards;
    animation-delay: var(--delay);
    -webkit-filter: drop-shadow(0 0 10px rgba(150, 220, 255, 0.9));
    filter: drop-shadow(0 0 10px rgba(150, 220, 255, 0.9));
}

@keyframes leijian-explosion-branch-expand {
    0% {
        transform: translate(-50%, -100%) rotate(var(--angle)) scale(0);
        opacity: 0;
    }
    20% {
        transform: translate(-50%, -100%) rotate(var(--angle)) scale(1.2);
        opacity: 1;
    }
    60% {
        transform: translate(-50%, -100%) rotate(var(--angle)) scale(1.5);
        opacity: 0.8;
    }
    100% {
        transform: translate(-50%, -100%) rotate(var(--angle)) scale(0.3);
        opacity: 0;
    }
}

/* 爆炸粒子动画 */
.leijian-explosion-particle {
    position: absolute;
    left: 50%;
    top: 50%;
    width: 3px;
    height: 15px;
    background: linear-gradient(to bottom, #fff 0%, #96dcff 50%, #2196f3 100%);
    border-radius: 1px;
    animation: leijian-explosion-particle-scatter 0.6s ease-out forwards var(--random-delay, 0s);
}

@keyframes leijian-explosion-particle-scatter {
    0% {
        transform: translate(-50%, -50%) rotate(var(--random-angle, 0deg)) translateY(0) scale(1);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) rotate(var(--random-angle, 0deg)) translateY(var(--random-distance, 50px)) scale(0.3);
        opacity: 0;
    }
}

/* 移动端适配 */
@media (max-width: 768px) {
    .leijian-sword {
        width: 35px !important;
        height: 70px !important;
    }
    
    .leijian-formation-sword {
        width: 25px !important;
        height: 50px !important;
    }
    
    .leijian-charge-core {
        width: 30px !important;
        height: 30px !important;
    }
    
    .leijian-charge-lightning {
        width: 20px !important;
        height: 3px !important;
    }
    
    .leijian-explosion-branch {
        height: 40px !important;
    }
}

/* 减少动画的性能优化 */
@media (prefers-reduced-motion: reduce) {
    .leijian-sword,
    .leijian-formation-sword,
    .leijian-charge-core,
    .leijian-charge-lightning,
    .leijian-charge-particle,
    .leijian-lightning-trail,
    .leijian-explosion-branch,
    .leijian-explosion-particle {
        animation-duration: 0.2s !important;
    }
} 