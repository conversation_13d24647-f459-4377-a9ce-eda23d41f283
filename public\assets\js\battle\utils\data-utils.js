/**
 * 数据处理工具类
 * 提供安全的数据类型转换和处理方法
 */
class BattleDataUtils {
    
    /**
     * 安全的整数解析方法
     * @param {*} value 要解析的值
     * @returns {number} 解析后的整数，失败时返回0
     */
    static safeParseInt(value) {
        if (value === null || value === undefined || value === '') {
            return 0;
        }
        const parsed = parseInt(value);
        return isNaN(parsed) ? 0 : parsed;
    }
    
    /**
     * 安全的浮点数解析方法
     * @param {*} value 要解析的值
     * @returns {number} 解析后的浮点数，失败时返回0
     */
    static safeParseFloat(value) {
        if (value === null || value === undefined || value === '') {
            return 0;
        }
        const parsed = parseFloat(value);
        return isNaN(parsed) ? 0 : parsed;
    }
    
    /**
     * 根据品质获取属性倍率
     * @param {string} rarity 品质等级（中文或英文）
     * @returns {number} 属性倍率
     */
    static getRarityMultiplier(rarity) {
        const multipliers = {
            'common': 1.0,      // 普通 100%
            'uncommon': 1.3,    // 稀有 130%
            'rare': 1.6,        // 史诗 160%
            'epic': 2.0,        // 传说 200%
            'legendary': 2.5    // 神话 250%
        };
        
        // 支持中文品质
        const chineseMapping = {
            '普通': 'common',
            '稀有': 'uncommon',
            '史诗': 'rare',
            '传说': 'epic',
            '神话': 'legendary'
        };
        
        const englishRarity = chineseMapping[rarity] || rarity;
        return multipliers[englishRarity] || 1.0;
    }

    /**
     * 格式化战斗时间
     * @param {number} milliseconds 毫秒数
     * @returns {string} 格式化的时间字符串
     */
    static formatBattleTime(milliseconds) {
        const seconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        
        if (minutes > 0) {
            return `${minutes}分${remainingSeconds}秒`;
        } else {
            return `${remainingSeconds}秒`;
        }
    }

    /**
     * 生成唯一ID
     * @param {string} prefix 前缀
     * @returns {string} 唯一ID
     */
    static generateUniqueId(prefix = '') {
        return `${prefix}${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 深度克隆对象
     * @param {object} obj 要克隆的对象
     * @returns {object} 克隆后的对象
     */
    static deepClone(obj) {
        if (obj === null || typeof obj !== 'object') {
            return obj;
        }
        
        if (obj instanceof Date) {
            return new Date(obj.getTime());
        }
        
        if (obj instanceof Array) {
            return obj.map(item => this.deepClone(item));
        }
        
        const clonedObj = {};
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                clonedObj[key] = this.deepClone(obj[key]);
            }
        }
        
        return clonedObj;
    }

    /**
     * 验证数值范围
     * @param {number} value 要验证的值
     * @param {number} min 最小值
     * @param {number} max 最大值
     * @returns {number} 限制在范围内的值
     */
    static clamp(value, min, max) {
        return Math.min(Math.max(value, min), max);
    }

    /**
     * 计算百分比
     * @param {number} current 当前值
     * @param {number} total 总值
     * @returns {number} 百分比（0-100）
     */
    static calculatePercentage(current, total) {
        if (total <= 0) return 0;
        return Math.round((current / total) * 100);
    }
}

// 全局导出工具类
window.BattleDataUtils = BattleDataUtils;

if (window.BattleDebugConfig) {
    window.BattleDebugConfig.log('data-utils', '🔧 数据处理工具模块已加载');
} else {
    console.log('�� 数据处理工具模块已加载');
} 