# 🎮 一念修仙 - 网页修仙游戏

欢迎来到一念修仙的开发世界！这是一个基于 PHP + MySQL + JavaScript 的网页修仙类游戏，提供完整的修仙体验。

## 🚀 快速开始

### 📋 系统要求
- **PHP**: 7.43nts
- **MySQL**: 5.7.2+
- **Nginx**: 1.52.2
- **浏览器**: 支持 ES6+ 的现代浏览器

### 🎯 核心功能
- ✅ **8大系统**: 用户、修炼、装备、战斗、冒险、炼丹、精灵、兑换码
- ✅ **40个API**: 完整的后端接口支持
- ✅ **39个数据表**: 完善的数据库架构
- ✅ **25个页面**: 丰富的前端界面

### 📊 项目规模 (2024年12月19日)
- **代码量**: ~500KB PHP代码
- **装备总数**: 336件装备
- **境界**: 280个境界配置 (28×10)
- **武器技能**: 168个技能
- **地图关卡**: 1120个关卡 (8个地图)
- **怪物类型**: 104种不同怪物
- **完成度**: 95%

## 📚 文档导航

### 🏗️ 开发文档
- **[GAME_DEVELOPMENT_DOCS_UPDATED.md](GAME_DEVELOPMENT_DOCS_UPDATED.md)** - 游戏开发完整指南 (✨ 最新整理版)
  - 详细的项目结构说明
  - API接口完整文档
  - 开发规范和流程
  - 系统配置和常量

- **[GAME_DEVELOPMENT_DOCS.md](GAME_DEVELOPMENT_DOCS.md)** - 游戏开发完整指南 (原版)
  - 需要更新的原版文档

### 🗄️ 数据库文档
- **[DATABASE_SCHEMA.md](DATABASE_SCHEMA.md)** - 数据库结构详细说明 (✅ 已更新)
  - 39个表的完整结构
  - 表关系和外键说明
  - 数据统计和维护建议

### 📖 项目管理文档
- **[PROJECT_DOCUMENTS_INDEX.md](PROJECT_DOCUMENTS_INDEX.md)** - 项目文档索引 (✅ 已更新)
- **[project_status_tracker.md](project_status_tracker.md)** - 项目状态跟踪
- **[.cursorrules](.cursorrules)** - Cursor开发规则

### 📋 参考文档
- **[docs/guides/一念.md](docs/guides/一念.md)** - 游戏原始设定
- **[spirit_root_system.md](spirit_root_system.md)** - 五行灵根系统
- **[SECURITY_UPDATE_PLAN.md](SECURITY_UPDATE_PLAN.md)** - 安全更新计划

## 🎮 游戏特色

### 🏔️ 修炼系统
- **280个境界**: 从练气期到渡劫期的完整修炼体系
- **五行灵根**: 金、木、水、火、土五行属性影响修炼
- **功法系统**: 多种修炼功法，不同效果和要求
- **丹药辅助**: 渡劫丹、养魂丹等辅助突破

### ⚔️ 战斗系统
- **回合制战斗**: 策略性的自动战斗系统
- **168个技能**: 万剑诀、掌心雷、火球术等多种技能
- **装备影响**: 装备武器影响技能和战力
- **掉落奖励**: 复杂的战利品掉落机制

### 🗺️ 冒险探索
- **8个地图**: 太乙峰、碧水寒潭、赤焰谷、幽冥鬼域、青云仙山、星辰古战场、混元虚空、洪荒秘境
- **1120个关卡**: 每个地图140个关卡
- **进度记录**: 完整的探索进度追踪
- **BOSS战**: 特殊BOSS战和首杀奖励

### 🎒 装备系统
- **336件装备**: 完整的装备体系 (ID: 568-903)
- **7种槽位**: 武器、胸甲、腿甲、鞋子、戒指、项链、手镯
- **职业分化**: 剑修/法修装备体系，物理/法术攻防分离
- **耐久度**: 装备使用和修理系统
- **武器库**: 武器收集和展示功能

### 🌟 五行灵根系统
- **灵根生成**: 角色创建时自动生成五行灵根
- **品质等级**: 废灵根、下品、中品、上品、极品五个品质等级
- **属性加成**: 影响对应基础属性 (筋骨、悟性、体魄、神魂、身法)
- **战斗加成**: 五行相克关系影响战斗伤害

### 🎒 物品堆叠规则 (✨ 最新更新)
| 物品类型 | 堆叠上限 | 说明 |
|----------|----------|------|
| **材料类** | **999** | 属性丹材料、炼丹材料、渡劫材料等 |
| **消耗品类** | **99** | 丹药、药品等可使用物品 |
| **装备类** | **1** | 武器、防具、饰品等装备 |
| **丹炉类** | **99** | 各品质丹炉 |
| **特殊物品** | **1** | 丹方、功法等学习类物品 |

## 🛠️ 技术架构

### 📁 项目结构
```
yinian/
├── 📄 项目文档
│   ├── README.md                 # 项目概览
│   ├── GAME_DEVELOPMENT_DOCS_UPDATED.md  # 开发指南 (最新)
│   ├── DATABASE_SCHEMA.md        # 数据库文档
│   └── PROJECT_DOCUMENTS_INDEX.md # 文档索引
├── 🎨 前端资源 public/
│   ├── 25个功能页面
│   └── assets/ (CSS、图片、组件)
├── 🔧 后端代码 src/
│   ├── api/ (40个API文件)
│   ├── config/ (配置文件)
│   └── includes/ (公共库)
├── 🗄️ 数据库 database/
│   └── 39个数据表结构
├── 🔨 后端支持 backend/
│   ├── admin/ (管理后台)
│   └── scripts/ (维护脚本)
└── 📚 文档 docs/
    └── guides/ (设计指南)
```

### 🌐 API设计
- **RESTful风格**: 统一的API接口设计
- **错误处理**: 完善的异常捕获和日志记录
- **安全防护**: CSRF防护、SQL注入防护、输入验证
- **性能优化**: 参数化查询、慢查询监控

### 🗄️ 数据库设计
- **39个核心表**: 分为8大功能系统
- **外键关系**: 完整的数据关联和级联删除
- **JSON支持**: 复杂配置使用JSON存储
- **索引优化**: 查询频繁字段建立索引

## 🔧 开发指南

### 📋 开发规则
详见 **[.cursorrules](.cursorrules)**
- 始终使用中文简体
- 实际系统优先于设计文档
- 数据库变动更新DATABASE_SCHEMA.md
- 程序变动更新GAME_DEVELOPMENT_DOCS.md

### 🚀 快速部署
1. **环境准备**: 确保PHP、MySQL、Nginx版本符合要求
2. **数据库导入**: 执行 `database/init.sql` 初始化数据库
3. **配置修改**: 修改 `src/config/database.php` 中的数据库连接
4. **权限设置**: 确保 `logs/` 目录可写
5. **访问测试**: 浏览器访问项目首页测试

### 🔍 调试工具
- **show_tables.php**: 查看数据库表结构
- **manage_item_stack_limits.php**: 物品堆叠管理
- **public/test_api_web.html**: API测试工具
- **backend/admin/**: 后台管理和调试工具
- **logs/**: 系统日志和错误记录

## 🎯 发展路线

### 📅 已完成 (95%)
- ✅ 用户注册登录系统 (100%)
- ✅ 角色创建和管理 (100%)
- ✅ 修炼和境界突破 (100%)
- ✅ 装备和武器系统 (100%)
- ✅ 战斗和冒险系统 (95%)
- ✅ 炼丹和精灵系统 (100%)
- ✅ 兑换码和商城系统 (100%)
- ✅ 五行灵根系统 (100%)

### 🚧 规划中
- 🔄 **宗门系统**: 公会功能和团队玩法
- 🔄 **好友系统**: 社交功能和互动
- 🔄 **交易系统**: 玩家间物品交易
- 🔄 **PVP系统**: 玩家对战和竞技场
- 🔄 **移动端**: 响应式设计和移动APP

## 🔐 安全验证系统

### 🛡️ 三阶段验证策略
1. **第一阶段 - 基础验证** (✅已完成)
   - 数值上限检查 (经验≤50,000, 金币≤10,000, 掉落≤30)
   - 战斗时间验证 (2秒-30分钟)
   - 高品质物品监控
   - 完整日志记录

2. **第二阶段 - 中级验证** (🔄计划中)
   - 属性合理性验证
   - 战斗结果验证
   - 时间序列验证

3. **第三阶段 - 完整验证** (📋远期计划)
   - 服务器端战斗引擎
   - 加密验证机制

## 🤝 贡献指南

### 📝 代码规范
- **PHP**: 使用PDO、参数化查询、统一错误处理
- **JavaScript**: ES6+语法、async/await、模块化设计
- **SQL**: 规范化设计、适当索引、注释完整
- **前端**: 响应式设计、用户体验优先

### 🔒 安全要求
- 所有用户输入必须验证和过滤
- 使用参数化查询防止SQL注入
- 重要操作添加CSRF令牌验证
- 敏感信息加密存储

### 📊 性能标准
- API响应时间 < 200ms
- 数据库查询 < 100ms
- 页面加载时间 < 3s
- 并发支持 > 100用户

## 📞 联系方式

- **项目地址**: F:\phpstudy_pro\WWW\yinian
- **数据库**: yn_game (39个表)
- **技术栈**: PHP 7.43nts + MySQL 5.7.2 + Nginx 1.52.2

---

*欢迎来到修仙世界，祝您修炼顺利，早日飞升！* ✨

> **文档说明**: 本README提供项目概览，详细信息请查看对应的专门文档。所有文档都会随着项目更新保持同步。
> 
> **最新更新**: 2024年12月19日 - 文档全面整理，修复过时信息，添加物品堆叠规则等最新内容。 