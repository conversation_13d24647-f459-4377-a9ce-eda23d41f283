/**
 * 火法技能模块
 * 包含各种火系攻击技能
 */

/**
 * 火球术技能类
 */
class HuoQiuShuSkill extends BaseSkill {
    async execute(skillData, weaponImage) {
        // 🔧 修复：使用真实技能名称而不是动画名称
        const skillName = skillData?.skillName || skillData?.displayName || '火球术';
        console.log(`🔥 执行火球术技能，显示名称: ${skillName}`);
        
        await this.showSkillShout(skillName);
        await this.createFireball();
    }

    async createFireball() {
        // 🔧 动态判断位置映射
        let casterPos, targetPos;
        
        if (this.isEnemySkill) {
            // 敌方技能：敌人是施法者，玩家是目标
            casterPos = this.getCharacterPosition(false); // 敌人位置
            targetPos = this.getCharacterPosition(true);  // 玩家位置
        } else {
            // 我方技能：玩家是施法者，敌人是目标
            casterPos = this.getCharacterPosition(true);  // 玩家位置
            targetPos = this.getCharacterPosition(false); // 敌人位置
        }
        
        // 计算轨迹
        const deltaX = targetPos.x - casterPos.x;
        const deltaY = targetPos.y - casterPos.y;
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
        const angle = Math.atan2(deltaY, deltaX);
        
        console.log(`🔥 火球术轨迹计算:`, {
            起点: casterPos,
            终点: targetPos,
            距离: distance,
            角度: angle * 180 / Math.PI
        });

        // 创建火球容器
        const container = this.createElement('fireball-container', {
            style: {
                position: 'absolute',
                top: '0',
                left: '0',
                width: '100%',
                height: '100%',
                pointerEvents: 'none',
                zIndex: 1000
            }
        });
        
        this.effectsContainer.appendChild(container);
        
        try {
            // 第一阶段：火球发射
            const fireball = this.createElement('fireball', {
                style: {
                    position: 'absolute',
                    left: casterPos.x + 'px',
                    top: casterPos.y + 'px',
                    transform: 'translate(-50%, -50%)'
                }
            });

            // 设置CSS变量用于动画
            fireball.style.setProperty('--targetX', targetPos.x + 'px');
            fireball.style.setProperty('--targetY', targetPos.y + 'px');
            fireball.style.setProperty('--angle', angle + 'rad');
            fireball.style.setProperty('--distance', distance + 'px');
            
            container.appendChild(fireball);

            // 等待火球飞行完成
            const flightTime = Math.min(Math.max(distance / 200, 0.8), 2.0); // 0.8-2.0秒飞行时间
            console.log(`🔥 火球飞行时间: ${flightTime}秒`);
            
            await this.wait(flightTime * 1000);
            
            // 移除火球
            this.safeRemoveElement(fireball);

            // 第二阶段：爆炸效果
            await this.createRealisticExplosion(targetPos.x, targetPos.y, container);

            // 击中效果
            this.createHitEffect(targetPos.x, targetPos.y, true);

            // 敌人受击动画
            this.createFireEnemyHit();

            // 等待爆炸完成
            await this.wait(1500);
            
        } finally {
            // 清理容器
            setTimeout(() => {
                this.safeRemoveElement(container);
            }, 100);
        }
    }

    async createRealisticExplosion(centerX, centerY, container) {
        console.log(`🔥 在位置 (${centerX}, ${centerY}) 创建火焰爆炸`);
        
        // 第一阶段：爆炸闪光
        const explosionFlash = this.createElement('explosion-flash', {
            style: {
                position: 'absolute',
                left: centerX + 'px',
                top: centerY + 'px',
                transform: 'translate(-50%, -50%)'
            }
        });
        container.appendChild(explosionFlash);

        // 第二阶段：爆炸核心
        setTimeout(() => {
            const explosionCore = this.createElement('explosion-core', {
                style: {
                    position: 'absolute',
                    left: centerX + 'px',
                    top: centerY + 'px',
                    transform: 'translate(-50%, -50%)'
                }
            });
            container.appendChild(explosionCore);
        }, 50);

        // 第三阶段：冲击波
        setTimeout(() => {
            for (let i = 0; i < 3; i++) {
                const shockwave = this.createElement('explosion-shockwave', {
                    style: {
                        position: 'absolute',
                        left: centerX + 'px',
                        top: centerY + 'px',
                        transform: 'translate(-50%, -50%)',
                        animationDelay: `${i * 0.1}s`
                    }
                });
                container.appendChild(shockwave);
            }
        }, 100);

        // 第四阶段：火焰环
        setTimeout(() => {
            const fireRing = this.createElement('explosion-fire-ring', {
                style: {
                    position: 'absolute',
                    left: centerX + 'px',
                    top: centerY + 'px',
                    transform: 'translate(-50%, -50%)'
                }
            });
            container.appendChild(fireRing);
        }, 150);

        // 第五阶段：爆炸烟雾
        setTimeout(() => {
            for (let i = 0; i < 6; i++) {
                const smoke = this.createElement('explosion-smoke', {
                    style: {
                        position: 'absolute',
                        left: (centerX + (Math.random() - 0.5) * 60) + 'px',
                        top: (centerY + (Math.random() - 0.5) * 60) + 'px',
                        transform: 'translate(-50%, -50%)',
                        animationDelay: `${i * 0.1}s`
                    }
                });
                container.appendChild(smoke);
            }
        }, 200);

        // 第六阶段：火花粒子
        setTimeout(() => {
            for (let i = 0; i < 30; i++) {
                const particle = this.createElement('fire-particle', {
                    style: {
                        position: 'absolute',
                        left: centerX + 'px',
                        top: centerY + 'px',
                        transform: 'translate(-50%, -50%)',
                        animationDelay: `${i * 0.02}s`
                    }
                });
                
                const angle = Math.random() * Math.PI * 2;
                const distance = 30 + Math.random() * 80;
                const moveX = Math.cos(angle) * distance;
                const moveY = Math.sin(angle) * distance;
                
                particle.style.setProperty('--moveX', `${moveX}px`);
                particle.style.setProperty('--moveY', `${moveY}px`);
                particle.style.animation = 'fire-particle 1s ease-out forwards';
                
                container.appendChild(particle);
            }
        }, 250);

        // 第七阶段：热浪扭曲效果
        setTimeout(() => {
            const heatDistortion = this.createElement('explosion-heat-distortion', {
                style: {
                    position: 'absolute',
                    left: centerX + 'px',
                    top: centerY + 'px',
                    transform: 'translate(-50%, -50%)'
                }
            });
            container.appendChild(heatDistortion);
        }, 300);
    }
    
    createFireEnemyHit() {
        // 🔧 修复：动态判断被攻击的目标
        const targetSelector = !this.isEnemySkill ? '.enemy .character-sprite' : '.player .character-sprite';
        const targetSprite = document.querySelector(targetSelector);
        if (targetSprite) {
            targetSprite.style.animation = 'fire-hit 2s ease-out, fire-shake 0.5s ease-in-out 4';
            
            setTimeout(() => {
                if (targetSprite) {
                    targetSprite.style.animation = '';
                }
            }, 2000);
        }
    }
    
    safeRemoveElement(element) {
        if (element && element.parentNode) {
            try {
                element.parentNode.removeChild(element);
            } catch (error) {
                console.warn(`⚠️ 移除元素失败:`, error);
            }
        }
    }
}

/**
 * 火流星技能类 (武器技能)
 * 继承BaseSkill，遵循v2.0架构规范
 */
class HuoLiuXingSkill extends BaseSkill {
    constructor(battleSystem) {
        super(battleSystem);
        // 注意：技能名称从skillData中动态获取，不固定为"火流星"
        this.elementType = 'fire';
        
        // v2.0新增：技能实例管理
        this.animationContainers = new Set();
        this.activeTimers = new Set();
    }

    async execute(skillData, weaponImage) {
        try {
            // 使用武器技能的实际名称
            const skillName = skillData.skillName || skillData.displayName || skillData.name || '火流星';
            this.skillName = skillName;
            
            // 必须调用技能喊话
            await this.showSkillShout(skillName);
            
            // 调用具体的技能动画方法
            await this.createHuoLiuXing(weaponImage);
            
        } catch (error) {
            console.error(`❌ ${this.skillName} 执行失败:`, error);
            this.handleError(error, 'execute');
        }
    }
    
    async createHuoLiuXing(weaponImage) {
        console.log(`🔥 开始执行${this.skillName}技能动画`);
        
        // 🔧 动态判断位置映射
        let casterPos, targetPos;
        
        if (this.isEnemySkill) {
            // 敌方技能：敌人是施法者，玩家是目标
            casterPos = this.getCharacterPosition(false); // 敌人位置
            targetPos = this.getCharacterPosition(true);  // 玩家位置
        } else {
            // 我方技能：玩家是施法者，敌人是目标
            casterPos = this.getCharacterPosition(true);  // 玩家位置
            targetPos = this.getCharacterPosition(false); // 敌人位置
        }
        
        console.log(`🔥 ${this.skillName}位置计算:`, {
            施法者位置: {x: casterPos.x, y: casterPos.y},
            目标位置: {x: targetPos.x, y: targetPos.y}
        });
        
        // 创建技能动画容器（使用CSS类名，不是'div'）
        const container = this.createElement('huoliuxing-container', {
            style: {
                position: 'absolute',
                top: '0',
                left: '0',
                width: '100%',
                height: '100%',
                pointerEvents: 'none',
                zIndex: 1000
            }
        });
        
        // v2.0新增：容器管理
        this.animationContainers.add(container);
        this.effectsContainer.appendChild(container);
        
        try {
            // 第一阶段：蓄力阶段（参考藤蔓术，改成红色）2秒
            console.log(`🔥 第一阶段：红色蓄力开始`);
            await this.createFireChargeEffect(container, casterPos, weaponImage);
            
            // 第二阶段：三个火球从敌人头顶降下（参考金针风暴）1.5秒
            console.log(`🔥 第二阶段：火球降落开始`);
            await this.createMeteorFallEffect(container, targetPos);
            
            // 第三阶段：爆炸效果（参考火球术）1.5秒
            console.log(`🔥 第三阶段：火球爆炸开始`);
            await this.createFireExplosionEffect(container, targetPos);
            
            // 击中效果（必须调用）
            this.createHitEffect(targetPos.x, targetPos.y, true);
            
        } finally {
            // v2.0优化：安全清理机制
            this.safeCleanupContainer(container);
        }
        
        console.log(`✅ ${this.skillName}技能动画执行完成`);
    }
    
    // 第一阶段：红色蓄力效果（参考藤蔓术，改成红色）
    async createFireChargeEffect(container, casterPos, weaponImage) {
        console.log(`🔥 创建红色蓄力效果于位置: (${casterPos.x}, ${casterPos.y})`);
        
        // === 武器图片旋转蓄力 ===
        if (weaponImage) {
            // 🔧 动态计算武器朝向目标的角度
            const targetPos = this.isEnemySkill ? this.getCharacterPosition(true) : this.getCharacterPosition(false);
            const deltaX = targetPos.x - casterPos.x;
            const deltaY = targetPos.y - casterPos.y;
            const angle = Math.atan2(deltaY, deltaX);
            let angleDeg = (angle * 180 / Math.PI) - 90; // 剑尖指向目标
            
            // 🗡️ 动态调整：根据技能使用者调整基础角度
            angleDeg += this.calculateInitialSwordRotation();
            
            const weaponElement = this.createElement('huoliuxing-weapon', {
                style: {
                    position: 'absolute',
                    left: casterPos.x + 'px',
                    top: casterPos.y + 'px',
                    width: '40px',
                    height: '80px',
                    transform: `translate(-50%, -50%) rotate(${angleDeg}deg)`,
                    backgroundImage: `url(${weaponImage})`,
                    backgroundSize: 'contain',
                    backgroundRepeat: 'no-repeat',
                    backgroundPosition: 'center',
                    zIndex: '1005'
                }
            });
            
            // 武器发光效果（使用drop-shadow）
            weaponElement.style.filter = 'drop-shadow(0 0 15px rgba(255, 69, 0, 0.8)) drop-shadow(0 0 30px rgba(255, 140, 0, 0.6))';
            weaponElement.style.animation = 'huoliuxing-weapon-charge 2s ease-in-out';
            
            container.appendChild(weaponElement);
            console.log(`🔥 武器蓄力旋转效果已创建，角度: ${angleDeg}度`);
        }
        
        // === 红色能量环绕（改版藤蔓术） ===
        const energyField = this.createElement('huoliuxing-energy-field', {
            style: {
                position: 'absolute',
                left: casterPos.x + 'px',
                top: casterPos.y + 'px',
                transform: 'translate(-50%, -50%)',
                zIndex: '1002'
            }
        });
        container.appendChild(energyField);
        
        // === 红色蓄力粒子（环绕武器一周） ===
        for (let i = 0; i < 12; i++) {
            const particle = this.createElement('huoliuxing-charge-particle', {
                style: {
                    position: 'absolute',
                    left: casterPos.x + 'px',
                    top: casterPos.y + 'px',
                    transform: 'translate(-50%, -50%)',
                    animationDelay: `${i * 0.1}s`,
                    zIndex: '1003'
                }
            });
            
            // 设置粒子轨道参数 - 环绕武器
            const angle = (i / 12) * Math.PI * 2;
            particle.style.setProperty('--particleAngle', `${angle * 180 / Math.PI}deg`);
            particle.style.setProperty('--orbitRadius', '50px'); // 围绕武器的半径
            particle.style.setProperty('--weaponX', `${casterPos.x}px`);
            particle.style.setProperty('--weaponY', `${casterPos.y}px`);
            
            container.appendChild(particle);
        }
        
        // === 红色冲击波 ===
        for (let i = 0; i < 3; i++) {
            const shockwave = this.createElement('huoliuxing-charge-shockwave', {
                style: {
                    position: 'absolute',
                    left: casterPos.x + 'px',
                    top: casterPos.y + 'px',
                    transform: 'translate(-50%, -50%)',
                    animationDelay: `${i * 0.4}s`,
                    zIndex: '1000'
                }
            });
            container.appendChild(shockwave);
        }
        
        console.log(`🔥 等待1200毫秒蓄力完成...`);
        await this.wait(1200);
        
        // 清理蓄力效果
        container.querySelectorAll('.huoliuxing-weapon, .huoliuxing-energy-field, .huoliuxing-charge-particle, .huoliuxing-charge-shockwave').forEach(el => {
            this.safeRemoveElement(el);
        });
        
        console.log(`✅ 红色蓄力阶段完成`);
    }
    
    // 第二阶段：三个火球从天而降（参考金针风暴）
    async createMeteorFallEffect(container, targetPos) {
        console.log(`🔥 创建火球降落效果于目标位置: (${targetPos.x}, ${targetPos.y})`);
        
        // 计算敌人头顶位置
        const meteorStartY = Math.max(0, targetPos.y - 200);
        const meteorPositions = [
            {x: targetPos.x - 30, y: meteorStartY},     // 左侧火球
            {x: targetPos.x, y: meteorStartY - 20},     // 中央火球（稍高）
            {x: targetPos.x + 30, y: meteorStartY}      // 右侧火球
        ];
        
        console.log(`🔥 火球降落计算:`, {
            敌人位置: {x: targetPos.x, y: targetPos.y},
            起始高度: meteorStartY,
            三个火球位置: meteorPositions
        });
        
        // === 创建三个火球 ===
        const meteors = [];
        for (let i = 0; i < 3; i++) {
            const startPos = meteorPositions[i];
            
            // 流星主体（头大尾小的设计）
            const meteor = this.createElement('huoliuxing-meteor', {
                style: {
                    position: 'absolute',
                    left: startPos.x + 'px',
                    top: startPos.y + 'px',
                    transform: 'translate(-50%, -50%)',
                    animationDelay: `${i * 0.2}s`,
                    zIndex: '1004'
                }
            });
            
            // 设置CSS变量用于动画轨迹
            meteor.style.setProperty('--startX', `${startPos.x}px`);
            meteor.style.setProperty('--startY', `${startPos.y}px`);
            meteor.style.setProperty('--targetX', `${targetPos.x}px`);
            meteor.style.setProperty('--targetY', `${targetPos.y}px`);
            
            // 流星尾迹（头粗尾细的楔形设计）
            const trail = this.createElement('huoliuxing-meteor-trail', {
                style: {
                    position: 'absolute',
                    left: startPos.x + 'px',
                    top: startPos.y + 'px',
                    transform: 'translate(-50%, -50%)',
                    animationDelay: `${i * 0.2}s`,
                    zIndex: '1003'
                }
            });
            
            // 设置尾迹CSS变量
            trail.style.setProperty('--startX', `${startPos.x}px`);
            trail.style.setProperty('--startY', `${startPos.y}px`);
            trail.style.setProperty('--targetX', `${targetPos.x}px`);
            trail.style.setProperty('--targetY', `${targetPos.y}px`);
            
            container.appendChild(meteor);
            container.appendChild(trail);
            meteors.push({meteor, trail});
            
            console.log(`🔥 创建第${i+1}个火球，起始位置: (${startPos.x}, ${startPos.y})`);
        }
        
        // === 击中时的冲击波 ===
        setTimeout(() => {
            for (let i = 0; i < 3; i++) {
                setTimeout(() => {
                    const impactWave = this.createElement('huoliuxing-impact-wave', {
                        style: {
                            position: 'absolute',
                            left: targetPos.x + 'px',
                            top: targetPos.y + 'px',
                            transform: 'translate(-50%, -50%)',
                            zIndex: '1002'
                        }
                    });
                    container.appendChild(impactWave);
                }, i * 200);
            }
        }, 1200);
        
        console.log(`🔥 等待1300毫秒火球降落完成...`);
        await this.wait(1300);
        
        // 清理火球效果
        meteors.forEach(({meteor, trail}) => {
            this.safeRemoveElement(meteor);
            this.safeRemoveElement(trail);
        });
        container.querySelectorAll('.huoliuxing-impact-wave').forEach(el => {
            this.safeRemoveElement(el);
        });
        
        console.log(`✅ 火球降落阶段完成`);
    }
    
    // 第三阶段：火焰爆炸效果（参考火球术）
    async createFireExplosionEffect(container, targetPos) {
        console.log(`🔥 创建火焰爆炸效果于位置: (${targetPos.x}, ${targetPos.y})`);
        
        // 第一阶段：爆炸闪光
        const explosionFlash = this.createElement('huoliuxing-explosion-flash', {
            style: {
                position: 'absolute',
                left: targetPos.x + 'px',
                top: targetPos.y + 'px',
                transform: 'translate(-50%, -50%)',
                zIndex: '1010'
            }
        });
        container.appendChild(explosionFlash);

        // 第二阶段：爆炸核心
        setTimeout(() => {
            const explosionCore = this.createElement('huoliuxing-explosion-core', {
                style: {
                    position: 'absolute',
                    left: targetPos.x + 'px',
                    top: targetPos.y + 'px',
                    transform: 'translate(-50%, -50%)',
                    zIndex: '1009'
                }
            });
            container.appendChild(explosionCore);
        }, 50);

        // 第三阶段：冲击波
        setTimeout(() => {
            for (let i = 0; i < 3; i++) {
                const shockwave = this.createElement('huoliuxing-explosion-shockwave', {
                    style: {
                        position: 'absolute',
                        left: targetPos.x + 'px',
                        top: targetPos.y + 'px',
                        transform: 'translate(-50%, -50%)',
                        animationDelay: `${i * 0.1}s`,
                        zIndex: '1008'
                    }
                });
                container.appendChild(shockwave);
            }
        }, 100);

        // 第四阶段：火焰环（720度旋转）
        setTimeout(() => {
            const fireRing = this.createElement('huoliuxing-fire-ring', {
                style: {
                    position: 'absolute',
                    left: targetPos.x + 'px',
                    top: targetPos.y + 'px',
                    transform: 'translate(-50%, -50%)',
                    zIndex: '1007'
                }
            });
            container.appendChild(fireRing);
        }, 150);

        // 第五阶段：爆炸烟雾
        setTimeout(() => {
            for (let i = 0; i < 6; i++) {
                const smoke = this.createElement('huoliuxing-explosion-smoke', {
                    style: {
                        position: 'absolute',
                        left: (targetPos.x + (Math.random() - 0.5) * 60) + 'px',
                        top: (targetPos.y + (Math.random() - 0.5) * 60) + 'px',
                        transform: 'translate(-50%, -50%)',
                        animationDelay: `${i * 0.1}s`,
                        zIndex: '1006'
                    }
                });
                container.appendChild(smoke);
            }
        }, 200);

        // 第六阶段：火花粒子散射
        setTimeout(() => {
            for (let i = 0; i < 20; i++) {
                const particle = this.createElement('huoliuxing-fire-particle', {
                    style: {
                        position: 'absolute',
                        left: targetPos.x + 'px',
                        top: targetPos.y + 'px',
                        transform: 'translate(-50%, -50%)',
                        animationDelay: `${i * 0.02}s`,
                        zIndex: '1005'
                    }
                });
                
                const angle = Math.random() * Math.PI * 2;
                const distance = 30 + Math.random() * 60;
                const moveX = Math.cos(angle) * distance;
                const moveY = Math.sin(angle) * distance;
                
                particle.style.setProperty('--moveX', `${moveX}px`);
                particle.style.setProperty('--moveY', `${moveY}px`);
                
                container.appendChild(particle);
            }
        }, 300);

        // 敌人受击动画（使用drop-shadow发光）
        this.createFireEnemyHit();

        console.log(`🔥 等待500毫秒爆炸完成...`);
        await this.wait(500);
        
        // 清理爆炸效果
        container.querySelectorAll('.huoliuxing-explosion-flash, .huoliuxing-explosion-core, .huoliuxing-explosion-shockwave, .huoliuxing-fire-ring, .huoliuxing-explosion-smoke, .huoliuxing-fire-particle').forEach(el => {
            this.safeRemoveElement(el);
        });
        
        console.log(`✅ 火焰爆炸阶段完成`);
    }

    // 🔧 修复：动态判断受击动画位置
    createFireEnemyHit() {
        const targetSelector = !this.isEnemySkill ? '.enemy .character-sprite' : '.player .character-sprite';
        const targetSprite = document.querySelector(targetSelector);
        if (targetSprite) {
            targetSprite.classList.add('huoliuxing-enemy-hit');
            // 使用drop-shadow发光效果
            targetSprite.style.filter = 'drop-shadow(0 0 20px rgba(255, 69, 0, 0.8)) drop-shadow(0 0 40px rgba(255, 140, 0, 0.6))';
            
            setTimeout(() => {
                targetSprite.classList.remove('huoliuxing-enemy-hit');
                targetSprite.style.filter = '';
            }, 500);
        }
    }

    // v2.0新增：安全移除元素
    safeRemoveElement(element) {
        if (element && element.parentNode) {
            try {
                element.parentNode.removeChild(element);
            } catch (error) {
                console.warn(`⚠️ ${this.skillName}元素移除失败:`, error);
            }
        }
    }

    // v2.0新增：安全清理容器
    safeCleanupContainer(container) {
        if (container) {
            this.animationContainers.delete(container);
            
            // 延迟清理，确保动画完成
            const timer = setTimeout(() => {
                this.safeRemoveElement(container);
                this.activeTimers.delete(timer);
            }, 100);
            
            this.activeTimers.add(timer);
        }
    }

    // v2.0新增：技能实例清理方法
    cleanup() {
        // 清理所有容器
        this.animationContainers.forEach(container => {
            this.safeRemoveElement(container);
        });
        this.animationContainers.clear();

        // 清理所有定时器
        this.activeTimers.forEach(timer => {
            clearTimeout(timer);
        });
        this.activeTimers.clear();

        console.log(`✅ ${this.skillName || '火流星'} 实例已清理`);
    }

    // 🗡️ 动态计算剑的初始旋转角度
    calculateInitialSwordRotation() {
        // 敌方技能：剑尖向下（指向玩家），我方技能：剑尖向上（指向敌人）
        return this.isEnemySkill ? 0 : 180;
    }

    // v2.0新增：错误处理
    handleError(error, context) {
        const errorInfo = {
            skill: this.skillName || '火流星',
            context: context,
            error: error.message,
            timestamp: Date.now()
        };

        // 上报错误到调试面板
        if (window.BattleDebugPanel) {
            window.BattleDebugPanel.addLog('error', `${this.skillName || '火流星'} ${context} 失败`, errorInfo);
        }
    }
}

// 导出技能类（必须按此格式）
window.FireSkills = window.FireSkills || {};
window.FireSkills.HuoQiuShuSkill = HuoQiuShuSkill;
window.FireSkills.HuoLiuXingSkill = HuoLiuXingSkill; 