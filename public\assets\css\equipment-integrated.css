/* 🔧 新增：合并的角色装备区域 */
.character-equipment-section {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
    border-radius: 15px; /* 🔧 减小圆角 */
    border: 2px solid rgba(212, 175, 55, 0.4);
    backdrop-filter: blur(15px);
    box-shadow: 
        0 8px 25px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    display: flex;
    flex-direction: column;
    margin-bottom: 8px;
}

/* 角色装备区域 - 恢复原始尺寸 */
.character-section {
    position: relative;
    min-height: 150px; /* 🔧 恢复原始高度 */ 
    display: flex;
    justify-content: center;
    align-items: center;
}

/* 弧形装备槽位容器 */
.equipment-arc {
    position: relative;
    width: 100%;
    height: 200px;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* 角色头像 - 居中 */
.character-avatar {
    width: 120px;
    height: 150px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);    
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28px;
    color: white;    
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

/* 🔥 角色战力显示 - 位于角色脚下，使用幻彩效果 */
.character-power-display {
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
    display: flex;
    align-items: center;
    gap: 5px;
    
    /* 移除背景和边框 */
    background: none;
    border: none;
    padding: 0;
}

.character-power-display .power-icon {
    font-size: 10px;
}

.character-power-display .power-value {
    /* 幻彩文字效果 - 参考装备详情样式 */
    background: linear-gradient(135deg, #ff6b6b, #ffa726, #ffeb3b, #66bb6a, #42a5f5, #ab47bc);
    background-size: 300% 300%;
    
    /* 文字渐变效果 */
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    
    /* 兼容性回退 */
    color: #e67e22;
    
    animation: powerGradientFlow 5s ease-in-out infinite;
    font-size: 16px;
    font-weight: bold;
}

/* 兼容性处理：如果不支持background-clip: text */
@supports not (-webkit-background-clip: text) {
    .character-power-display .power-value {
        color: #e67e22;
        text-shadow: 0 0 10px rgba(230, 126, 34, 0.8);
    }
}

/* 图标脉冲动画 */
@keyframes powerIconPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.9;
    }
    50% {
        transform: scale(1.1);
        opacity: 1;
    }
}

/* 幻彩渐变动画 */
@keyframes powerGradientFlow {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}


/* 装备槽位 - 统一大小，优美弧形排?*/
.equipment-slot {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5));
    border: 2px solid rgba(212, 175, 55, 0.6);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 8px;
    text-align: center;
    backdrop-filter: blur(10px);
    line-height: 1;
    position: absolute;
    box-shadow: 
        0 4px 8px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* 重新设计装备槽位布局 - 充分利用空间，避免重?*/
/* 顶部装备 */
.equipment-slot[data-slot="ring"] {
    top: 10px;
    left: calc(50% - 130px);
}

.equipment-slot[data-slot="accessory"] {
    top: 10px;
    right: calc(50% - 130px);
}

/* 中间两侧装备 */
.equipment-slot[data-slot="bracers"] {
    top: 50%;
    left: 10px;
    transform: translateY(-50%);
}

.equipment-slot[data-slot="chest"] {
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
}

/* 底部装备 */
.equipment-slot[data-slot="belt"] {
    bottom: 10px;
    left: calc(50% - 130px);
}

.equipment-slot[data-slot="boots"] {
    bottom: 10px;
    right: calc(50% - 130px);
}

.equipment-slot:hover {
    transform: scale(1.05);
    border-color: #f4d03f;
    box-shadow: 
        0 6px 12px rgba(0, 0, 0, 0.4),
        0 0 15px rgba(212, 175, 55, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* 中间两侧装备悬停时保持居中变?*/
.equipment-slot[data-slot="bracers"]:hover {
    transform: translateY(-50%) scale(1.05);
}

.equipment-slot[data-slot="chest"]:hover {
    transform: translateY(-50%) scale(1.05);
}

.equipment-slot:active {
    transform: scale(0.95);
}

/* 中间两侧装备点击时保持居中变?*/
.equipment-slot[data-slot="bracers"]:active {
    transform: translateY(-50%) scale(0.95);
}

.equipment-slot[data-slot="chest"]:active {
    transform: translateY(-50%) scale(0.95);
}

.equipment-slot.equipped {
    border-color: #d4af37;
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.4), rgba(212, 175, 55, 0.2));
    box-shadow: 
        0 4px 8px rgba(0, 0, 0, 0.3),
        0 0 15px rgba(212, 175, 55, 0.6),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* 武器区域美化 */
.weapon-section {
    padding: 5px; 
    position: relative; /* 🔧 新增：为战力显示提供定位基准 */
}

.weapon-grid {
    display: flex;
    justify-content: space-between;
    gap: 6px; 
    max-width: 100%;
}

.weapon-slot {
    width: 45px; 
    height: 45px;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5));
    border: 2px solid rgba(212, 175, 55, 0.6);
    border-radius: 10px; /* 🔧 从12px减少到10px */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 7px; /* 🔧 从8px减少到7px */
    text-align: center;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
    box-shadow: 
        0 4px 8px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.weapon-slot:hover {
    transform: scale(1.05);
    border-color: #f4d03f;
    box-shadow: 
        0 6px 12px rgba(0, 0, 0, 0.4),
        0 0 15px rgba(212, 175, 55, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.weapon-slot:active {
    transform: scale(0.95);
}

.weapon-slot.equipped {
    border-color: #d4af37;
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.4), rgba(212, 175, 55, 0.2));
    box-shadow: 
        0 4px 8px rgba(0, 0, 0, 0.3),
        0 0 15px rgba(212, 175, 55, 0.6),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* 背包区域美化 */
.inventory-section {
    position: relative;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
    border-radius: 18px;
    padding: 10px;
    border: 2px solid rgba(212, 175, 55, 0.4);
    backdrop-filter: blur(15px);
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    min-height: 160px; /* 🔧 设置合理的最小高度 */
    max-height: calc(100vh - 280px); /* 🔧 设置最大高度，确保不会超出可用空间 */
    box-shadow: 
        0 8px 25px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* 🔧 新增：背包内容区域包装器 */
.inventory-content-wrapper {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 0; /* 允许flex项目收缩 */
}

.inventory-item {
    width: 100% !important;
    aspect-ratio: 1 !important;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5)) !important;
    border: 2px solid rgba(149, 165, 166, 0.6) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    font-size: 8px !important;
    text-align: center !important;
    backdrop-filter: blur(10px) !important;
    line-height: 1 !important;
    position: relative !important;
    box-shadow: 
        0 4px 8px rgba(0, 0, 0, 0.3),
        0 0 8px rgba(149, 165, 166, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

.inventory-item:hover {
    transform: scale(1.05);
}

.inventory-item:active {
    transform: scale(0.95);
}

/* 标签美化 */
.tabs {
    display: flex;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px 8px 0 0;
    margin-bottom: 2px; /* 🔧 缩小到2px，让标签贴合网格 */
    border-bottom: 1px solid rgba(212, 175, 55, 0.3);
    padding: 2px; /* 🔧 减少内边距从4px到2px */
}

.tab {
    flex: 1;
    padding: 6px 8px; /* 🔧 减少垂直内边距从10px到6px */
    background: transparent;
    border: none;
    color: #bdc3c7;
    font-size: 12px; /* 🔧 减少字体大小从14px到12px */
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 6px;
    text-align: center;
    height: 28px; /* 🔧 固定高度为28px，比之前更紧凑 */
    display: flex;
    align-items: center;
    justify-content: center;
}

.tab:hover {
    border-color: rgba(212, 175, 55, 0.6);
    transform: translateY(-1px);
}

.tab:active {
    transform: scale(0.95);
}

.tab.active {
    background: linear-gradient(135deg, #d4af37, #b8941f);
    color: #1a3a5c;
    border-color: #d4af37;
    box-shadow: 
        0 4px 8px rgba(212, 175, 55, 0.4),
        0 0 10px rgba(212, 175, 55, 0.3);
}

/* 物品图片样式 */
.item-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 60%;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    z-index: 1;
}

.item-name-box {
    position: absolute;
    bottom: 1px;
    left: 1px;
    right: 1px;
    height: 40%;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.7), transparent);
    display: flex;
    align-items: flex-end;
    justify-content: center;
    z-index: 2;
    border-radius: 0 0 10px 10px;
}

.item-name-text {
    color: #ecf0f1;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
    line-height: 1.1;
    text-align: center;
    word-break: break-all;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    line-clamp: 2;
    max-height: 14px;
}

/* 物品状态美?*/
.inventory-item.selected {
    border-color: #e74c3c;
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.4), rgba(231, 76, 60, 0.2));
    box-shadow: 
        0 4px 8px rgba(0, 0, 0, 0.3),
        0 0 15px rgba(231, 76, 60, 0.6);
}

.inventory-item.equipped {
    border-color: #27ae60;
    background: linear-gradient(135deg, rgba(39, 174, 96, 0.4), rgba(39, 174, 96, 0.2));
    box-shadow: 
        0 4px 8px rgba(0, 0, 0, 0.3),
        0 0 15px rgba(39, 174, 96, 0.6);
}

/* 🔧 新增：不同品质已装备物品的样?*/
.inventory-item.rarity-common.equipped {
    border-color: #95a5a6;
    background: linear-gradient(135deg, rgba(149, 165, 166, 0.4), rgba(149, 165, 166, 0.2));
    box-shadow: 
        0 4px 8px rgba(0, 0, 0, 0.3),
        0 0 15px rgba(149, 165, 166, 0.6);
}

.inventory-item.rarity-uncommon.equipped {
    border-color: #27ae60;
    background: linear-gradient(135deg, rgba(39, 174, 96, 0.4), rgba(39, 174, 96, 0.2));
    box-shadow: 
        0 4px 8px rgba(0, 0, 0, 0.3),
        0 0 15px rgba(39, 174, 96, 0.6);
}

.inventory-item.rarity-rare.equipped {
    border-color: #3498db;
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.4), rgba(52, 152, 219, 0.2));
    box-shadow: 
        0 4px 8px rgba(0, 0, 0, 0.3),
        0 0 15px rgba(52, 152, 219, 0.6);
}

.inventory-item.rarity-epic.equipped {
    border-color: #9b59b6;
    background: linear-gradient(135deg, rgba(155, 89, 182, 0.4), rgba(155, 89, 182, 0.2));
    box-shadow: 
        0 4px 8px rgba(0, 0, 0, 0.3),
        0 0 15px rgba(155, 89, 182, 0.6);
}

.inventory-item.rarity-legendary.equipped {
    border-color: #f39c12;
    background: linear-gradient(135deg, rgba(243, 156, 18, 0.4), rgba(243, 156, 18, 0.2));
    box-shadow: 
        0 4px 8px rgba(0, 0, 0, 0.3),
        0 0 15px rgba(243, 156, 18, 0.6);
}

.equipped-badge {
    position: absolute;
    top: -2px;
    left: -2px;
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
    font-size: 6px;
    padding: 2px 4px;
    border-radius: 6px;
    font-weight: bold;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* 武器耐久条美?*/
.weapon-durability {
    position: absolute;
    bottom: 3px;
    left: 3px;
    right: 3px;
    height: 4px;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 3px;
    overflow: hidden;
    border: 1px solid rgba(212, 175, 55, 0.3);
}

.durability-bar {
    height: 100%;
    background: linear-gradient(90deg, #e74c3c, #f39c12, #27ae60);
    transition: width 0.3s ease;
    border-radius: 2px;
    box-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
}

/* 物品相关样式 */
.item-quantity {
    position: absolute;
    top: -2px;
    right: -2px;
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    font-size: 7px;
    padding: 2px 4px;
    border-radius: 8px;
    min-width: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.2);
}
/* 响应式优?*/
@media (max-width: 480px) {
    
    .character-section {
        min-height: 150px; /* 🔧 恢复移动端原始高度 */
    }
    
    .equipment-arc {
        height: 160px; /* 🔧 适中的装备弧形区域高度 */
    }
    
    .character-avatar {
        font-size: 24px;
    }
    
    /* 🔥 480px屏幕战力显示适配 */
    .character-power-display {
        bottom: -5px;
        gap: 3px;
    }
    
    
    .character-power-display .power-value {
        font-size: 14px;
    }
    
    .equipment-slot, .weapon-slot, .inventory-item {
        width: 40px;
        height: 40px;
        font-size: 7px;
    }
    
    .weapon-slot {
        height: 40px;
        width: 40px;
    }
    
    .inventory-grid {
        /* 🔧 480px屏幕背包网格高度优化 */
        max-height: calc(100vh - 240px); /* 简化计算，确保按钮区域可见 */
        gap: 5px;
        min-height: 100px;
        padding: 6px;
    }
    
    .inventory-section {
        min-height: 150px; /* 🔧 480px屏幕背包区域最小高度 */
        max-height: calc(100vh - 220px); /* 🔧 480px屏幕背包区域最大高度 */
        padding: 8px;
    }
    
    .tab {
        font-size: 10px;
        padding: 4px 6px;
        height: 26px;
    }

    /* 480px屏幕装备槽位调整 */
    .equipment-slot[data-slot="ring"] {
        top: 8px;
        left: calc(50% - 110px);
    }

    .equipment-slot[data-slot="accessory"] {
        top: 8px;
        right: calc(50% - 110px);
    }

    .equipment-slot[data-slot="bracers"] {
        top: 50%;
        left: 8px;
        transform: translateY(-50%);
    }

    .equipment-slot[data-slot="chest"] {
        top: 50%;
        right: 8px;
        transform: translateY(-50%);
    }

    .equipment-slot[data-slot="belt"] {
        bottom: 8px;
        left: calc(50% - 110px);
    }

    .equipment-slot[data-slot="boots"] {
        bottom: 8px;
        right: calc(50% - 110px);
    }

    /* 保持居中变换 */
    .equipment-slot[data-slot="bracers"]:hover {
        transform: translateY(-50%) scale(1.05);
    }

    .equipment-slot[data-slot="chest"]:hover {
        transform: translateY(-50%) scale(1.05);
    }

    .equipment-slot[data-slot="bracers"]:active {
        transform: translateY(-50%) scale(0.95);
    }

    .equipment-slot[data-slot="chest"]:active {
        transform: translateY(-50%) scale(0.95);
    }
}

@media (max-width: 360px) {
    .character-section {
        min-height: 120px; /* 🔧 恢复360px移动端原始高度 */
    }
    
    .equipment-arc {
        height: 140px;
    }
    
    .character-avatar {
        font-size: 20px;
    }
    
    /* 🔥 360px屏幕战力显示适配 */
    .character-power-display {
        bottom: -5px;
        gap: 2px;
    }
    
   
    .character-power-display .power-value {
        font-size: 14px;
    }
    
    .equipment-slot, .weapon-slot, .inventory-item {
        width: 35px;
        height: 35px;
        font-size: 6px;
    }
    
    .weapon-slot {
        height: 35px;
        width: calc((100% - 35px) / 6);
    }
    
    .tab {
        font-size: 9px;
        padding: 5px 2px;
    }

    /* 360px屏幕装备槽位调整 */
    .equipment-slot[data-slot="ring"] {
        top: 5px;
        left: calc(50% - 80px);
    }

    .equipment-slot[data-slot="accessory"] {
        top: 5px;
        right: calc(50% - 80px);
    }

    .equipment-slot[data-slot="bracers"] {
        top: 50%;
        left: 5px;
        transform: translateY(-50%);
    }

    .equipment-slot[data-slot="chest"] {
        top: 50%;
        right: 5px;
        transform: translateY(-50%);
    }

    .equipment-slot[data-slot="belt"] {
        bottom: 5px;
        left: calc(50% - 80px);
    }

    .equipment-slot[data-slot="boots"] {
        bottom: 5px;
        right: calc(50% - 80px);
    }

    /* 保持居中变换 */
    .equipment-slot[data-slot="bracers"]:hover {
        transform: translateY(-50%) scale(1.05);
    }

    .equipment-slot[data-slot="chest"]:hover {
        transform: translateY(-50%) scale(1.05);
    }

    .equipment-slot[data-slot="bracers"]:active {
        transform: translateY(-50%) scale(0.95);
    }

    .equipment-slot[data-slot="chest"]:active {
        transform: translateY(-50%) scale(0.95);
    }
}

/* 品质颜色 - 增强版 */
.inventory-item.rarity-common { 
    border-color: #95a5a6 !important; 
    box-shadow: 
        0 4px 8px rgba(0, 0, 0, 0.3),
        0 0 8px rgba(149, 165, 166, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

.inventory-item.rarity-uncommon { 
    border-color: #27ae60 !important; 
    box-shadow: 
        0 4px 8px rgba(0, 0, 0, 0.3),
        0 0 12px rgba(39, 174, 96, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

.inventory-item.rarity-rare { 
    border-color: #3498db !important; 
    box-shadow: 
        0 4px 8px rgba(0, 0, 0, 0.3),
        0 0 15px rgba(52, 152, 219, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

.inventory-item.rarity-epic { 
    border-color: #9b59b6 !important; 
    box-shadow: 
        0 4px 8px rgba(0, 0, 0, 0.3),
        0 0 18px rgba(155, 89, 182, 0.6),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

.inventory-item.rarity-legendary { 
    border-color: #d4af37 !important; 
    box-shadow: 
        0 4px 8px rgba(0, 0, 0, 0.3),
        0 0 20px rgba(212, 175, 55, 0.7),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

/* 品质悬停效果增强 */
.inventory-item.rarity-common:hover {
    border-color: #bdc3c7 !important;
    box-shadow: 
        0 6px 12px rgba(0, 0, 0, 0.4),
        0 0 15px rgba(149, 165, 166, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
    transform: scale(1.05);
}

.inventory-item.rarity-uncommon:hover {
    border-color: #2ecc71 !important;
    box-shadow: 
        0 6px 12px rgba(0, 0, 0, 0.4),
        0 0 20px rgba(39, 174, 96, 0.6),
        inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
    transform: scale(1.05);
}

.inventory-item.rarity-rare:hover {
    border-color: #5dade2 !important;
    box-shadow: 
        0 6px 12px rgba(0, 0, 0, 0.4),
        0 0 25px rgba(52, 152, 219, 0.7),
        inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
    transform: scale(1.05);
}

.inventory-item.rarity-epic:hover {
    border-color: #bb8fce !important;
    box-shadow: 
        0 6px 12px rgba(0, 0, 0, 0.4),
        0 0 30px rgba(155, 89, 182, 0.8),
        inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
    transform: scale(1.05);
}

.inventory-item.rarity-legendary:hover {
    border-color: #f4d03f !important;
    box-shadow: 
        0 6px 12px rgba(0, 0, 0, 0.4),
        0 0 35px rgba(212, 175, 55, 0.9),
        inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
    transform: scale(1.05);
}

/* 通用品质颜色（用于其他元素） */
.rarity-common { 
    border-color: #95a5a6 !important; 
    box-shadow: 
        0 4px 8px rgba(0, 0, 0, 0.3),
        0 0 8px rgba(149, 165, 166, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

.rarity-uncommon { 
    border-color: #27ae60 !important; 
    box-shadow: 
        0 4px 8px rgba(0, 0, 0, 0.3),
        0 0 12px rgba(39, 174, 96, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

.rarity-rare { 
    border-color: #3498db !important; 
    box-shadow: 
        0 4px 8px rgba(0, 0, 0, 0.3),
        0 0 15px rgba(52, 152, 219, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

.rarity-epic { 
    border-color: #9b59b6 !important; 
    box-shadow: 
        0 4px 8px rgba(0, 0, 0, 0.3),
        0 0 18px rgba(155, 89, 182, 0.6),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

.rarity-legendary { 
    border-color: #d4af37 !important; 
    box-shadow: 
        0 4px 8px rgba(0, 0, 0, 0.3),
        0 0 20px rgba(212, 175, 55, 0.7),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

.rarity-common:hover {
    border-color: #bdc3c7 !important;
    box-shadow: 
        0 6px 12px rgba(0, 0, 0, 0.4),
        0 0 15px rgba(149, 165, 166, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
}

.rarity-uncommon:hover {
    border-color: #2ecc71 !important;
    box-shadow: 
        0 6px 12px rgba(0, 0, 0, 0.4),
        0 0 20px rgba(39, 174, 96, 0.6),
        inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
}

.rarity-rare:hover {
    border-color: #5dade2 !important;
    box-shadow: 
        0 6px 12px rgba(0, 0, 0, 0.4),
        0 0 25px rgba(52, 152, 219, 0.7),
        inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
}

.rarity-epic:hover {
    border-color: #bb8fce !important;
    box-shadow: 
        0 6px 12px rgba(0, 0, 0, 0.4),
        0 0 30px rgba(155, 89, 182, 0.8),
        inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
    transform: scale(1.05);
}

.rarity-legendary:hover {
    border-color: #f4d03f !important;
    box-shadow: 
        0 6px 12px rgba(0, 0, 0, 0.4),
        0 0 35px rgba(212, 175, 55, 0.9),
        inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
    transform: scale(1.05);
}

/* 消息样式 */
.message {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 12px 20px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: bold;
    z-index: 1000;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
}

.message.success {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    border: 1px solid #4CAF50;
}

.message.error {
    background: linear-gradient(135deg, #f44336, #da190b);
    color: white;
    border: 1px solid #f44336;
}

.message.info {
    background: linear-gradient(135deg, #2196F3, #1976D2);
    color: white;
    border: 1px solid #2196F3;
}

.message.warning {
    background: linear-gradient(135deg, #FF9800, #F57C00);
    color: white;
    border: 1px solid #FF9800;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 4px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
}

::-webkit-scrollbar-thumb {
    background: rgba(212, 175, 55, 0.5);
    border-radius: 2px;
}

/* 加载状态 */
.loading {
    text-align: center;
    padding: 20px;
    color: #d4af37;
    font-size: 12px;
}

/* 回收确认浮窗样式 */
.recycle-confirm-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 80px; /* 🎨 为底部导航栏留出空间 */
    background: rgba(0, 0, 0, 0.6);
    z-index: 10000; /* 🔧 修复：提高层级，确保在物品详情弹窗之前 */
    display: none;
    backdrop-filter: blur(5px);
    padding: 15px; /* 🎨 统一上下间距 */
    align-items: center;
    justify-content: center;
}

.recycle-confirm-popup {
    position: relative; /* 🎨 改为相对定位，由overlay控制居中 */
    background: linear-gradient(135deg, rgba(26, 58, 92, 0.95), rgba(45, 89, 132, 0.95));
    border: 2px solid #d4af37;
    border-radius: 20px;
    padding: 0;
    width: 320px;
    max-width: 90vw;
    max-height: calc(100vh - 160px); /* 🎨 确保不超出可视区域，预留上下间距 */
    z-index: 10001; /* 🔧 修复：提高层级，确保在物品详情弹窗之前 */
    display: block; /* 🎨 改为block，由overlay控制显示 */
    box-shadow: 
        0 15px 35px rgba(0, 0, 0, 0.5),
        0 0 20px rgba(212, 175, 55, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    overflow-y: auto; /* 🎨 添加滚动支持 */
    /* 🔧 移除弹出动画 */
    /* animation: popupSlideIn 0.3s ease-out; */
}

.recycle-confirm-header {
    background: linear-gradient(135deg, #d4af37, #b8941f);
    color: #1a3a5c;
    padding: 15px 20px;
    border-radius: 18px 18px 0 0;
    position: relative;
    text-align: center;
    font-weight: bold;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.recycle-confirm-header h3 {
    margin: 0;
    font-size: 16px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.close-recycle-popup {
    position: absolute;
    top: 50%;
    right: 15px;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #1a3a5c;
    font-size: 20px;
    cursor: pointer;
    font-weight: bold;
    width: 25px;
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-recycle-popup:hover {
    background: rgba(26, 58, 92, 0.2);
}

.recycle-confirm-content {
    padding: 20px;
    color: #ecf0f1;
}

.recycle-item-info {
    margin-bottom: 15px;
    padding: 15px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    border: 1px solid rgba(212, 175, 55, 0.2);
}

.recycle-item-name {
    font-size: 16px;
    font-weight: bold;
    color: #d4af37;
    margin-bottom: 5px;
    text-align: center;
}

.recycle-item-details {
    font-size: 12px;
    color: #bdc3c7;
    text-align: center;
    line-height: 1.4;
}

.recycle-price-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 12px 15px;
    background: rgba(212, 175, 55, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(212, 175, 55, 0.3);
}

.recycle-price-label {
    font-size: 14px;
    color: #ecf0f1;
}

.recycle-price-value {
    font-size: 16px;
    font-weight: bold;
    color: #d4af37;
    text-shadow: 0 0 10px rgba(212, 175, 55, 0.5);
}

.recycle-warning {
    background: rgba(231, 76, 60, 0.2);
    border: 1px solid rgba(231, 76, 60, 0.4);
    border-radius: 8px;
    padding: 10px;
    font-size: 12px;
    color: #e74c3c;
    text-align: center;
    line-height: 1.4;
}

.recycle-confirm-buttons {
    display: flex;
    gap: 10px;
    padding: 15px 20px 20px 20px;
}

.btn-recycle-cancel, .btn-recycle-confirm {
    flex: 1;
    padding: 12px 8px;
    border: none;
    border-radius: 10px;
    font-weight: bold;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
}

.btn-recycle-cancel {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
    color: white;
}

.btn-recycle-cancel:hover {
    background: linear-gradient(135deg, #7f8c8d, #6c7b7d);
    transform: translateY(-1px);
}

.btn-recycle-confirm {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

.btn-recycle-confirm:hover {
    background: linear-gradient(135deg, #c0392b, #a93226);
    transform: translateY(-1px);
}

.btn-recycle-cancel:active, .btn-recycle-confirm:active {
    transform: scale(0.95);
}

/* 角色头像选择弹窗样式 - 🎨 优化版 */
.avatar-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 80px; /* 🎨 为底部导航栏留出空间 */
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 15px; /* 🎨 统一上下间距 */
    overflow: hidden;
    -webkit-overflow-scrolling: touch;
}

.avatar-modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8); /* 🎨 增强背景遮罩 */
    backdrop-filter: blur(8px); /* 🎨 增强模糊效果 */
}

.avatar-modal-content {
    position: relative;
    background: linear-gradient(135deg, rgba(26, 58, 92, 0.95), rgba(45, 89, 132, 0.95)); /* 🎨 使用游戏主题色 */
    border-radius: 20px;
    padding: 0;
    width: 95%; /* 🎨 增加宽度 */
    max-width: 600px; /* 🎨 增加最大宽度 */
    max-height: calc(100vh - 160px); /* 🎨 确保不超出可视区域，预留上下间距 */
    border: 2px solid #d4af37;
    box-shadow: 
        0 0 40px rgba(212, 175, 55, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.1); /* 🎨 增强阴影效果 */
    overflow: hidden;
    display: flex; /* 🎨 使用flex布局控制高度分配 */
    flex-direction: column; /* 🎨 垂直排列 */
    -webkit-overflow-scrolling: touch;
}

.avatar-modal-header {
    padding: 16px 20px; /* 🎨 调整内边距 */
    border-bottom: 1px solid rgba(212, 175, 55, 0.3);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.15), rgba(212, 175, 55, 0.05)); /* 🎨 渐变背景 */
    flex-shrink: 0; /* 🎨 头部不收缩 */
}

.avatar-modal-header h3 {
    margin: 0;
    color: #d4af37;
    font-size: 18px;
    text-shadow: 0 0 10px rgba(212, 175, 55, 0.5); /* 🎨 添加发光效果 */
}

.avatar-modal-close {
    background: none;
    border: none;
    color: #fff;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 32px; /* 🎨 增大关闭按钮 */
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.avatar-modal-close:hover {
    background: rgba(212, 175, 55, 0.2); /* 🎨 使用主题色 */
    color: #d4af37;
    transform: scale(1.1); /* 🎨 添加缩放效果 */
}

.avatar-modal-body {
    padding: 20px;
    flex: 1; /* 🎨 主体区域占据剩余空间 */
    overflow-y: auto; /* 🎨 内容区域可滚动 */
    min-height: 0; /* 🎨 允许flex子项收缩 */
    -webkit-overflow-scrolling: touch;
}

.avatar-section {
    margin-bottom: 20px; /* 🎨 减少间距 */
}

.avatar-section:last-child {
    margin-bottom: 0;
}

.avatar-section-title {
    display: flex;
    align-items: center;
    gap: 10px; /* 🎨 增加间距 */
    margin-bottom: 16px; /* 🎨 调整间距 */
    font-size: 16px;
    font-weight: bold;
    color: #d4af37;
    text-shadow: 0 0 8px rgba(212, 175, 55, 0.3); /* 🎨 添加发光效果 */
}

.avatar-section-icon {
    font-size: 20px; /* 🎨 增大图标 */
}

.avatar-section-desc {
    color: #bdc3c7;
    font-size: 12px;
    font-weight: normal;
    margin-left: 5px;
    opacity: 0.8; /* 🎨 调整透明度 */
}

.avatar-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr); /* 🎨 固定3列布局 */
    gap: 15px; /* 🎨 增加间距 */
    justify-items: center; /* 🎨 居中对齐 */
}

.avatar-option {
    width: 100px; /* 🎨 增大头像尺寸 */
    height: 100px;
    border-radius: 18px; /* 🎨 增大圆角 */
    border: 3px solid rgba(255, 255, 255, 0.2); /* 🎨 增加边框宽度 */
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    cursor: pointer;
    transition: all 0.4s ease; /* 🎨 增加过渡时间 */
    position: relative;
    overflow: hidden;
    box-shadow: 
        0 4px 15px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1); /* 🎨 添加阴影 */
}

.avatar-option:hover {
    transform: translateY(-5px) scale(1.05); /* 🎨 增强悬停效果 */
    border-color: #d4af37;
    box-shadow: 
        0 8px 25px rgba(212, 175, 55, 0.4),
        0 0 20px rgba(212, 175, 55, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2); /* 🎨 增强发光效果 */
}

.avatar-option.selected {
    border-color: #d4af37;
    box-shadow: 
        0 0 25px rgba(212, 175, 55, 0.8),
        0 8px 20px rgba(212, 175, 55, 0.4),
        inset 0 2px 0 rgba(255, 255, 255, 0.3); /* 🎨 增强选中效果 */
    transform: scale(1.08); /* 🎨 增加选中缩放 */
}

.avatar-option.selected::before {
    content: '✓'; /* 🎨 添加选中标记 */
    position: absolute;
    top: 5px;
    right: 5px;
    background: linear-gradient(135deg, #d4af37, #f4d03f);
    color: #1a3a5c;
    font-size: 14px;
    font-weight: bold;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.avatar-option.locked {
    filter: grayscale(100%) brightness(0.4); /* 🎨 增强锁定效果 */
    cursor: not-allowed;
    border-color: rgba(149, 165, 166, 0.3);
}

.avatar-option.locked::after {
    content: '🔒';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 24px; /* 🎨 增大锁定图标 */
    background: rgba(0, 0, 0, 0.9); /* 🎨 增强背景 */
    border-radius: 50%;
    width: 36px; /* 🎨 增大锁定背景 */
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid rgba(149, 165, 166, 0.5);
}

.avatar-option .avatar-price {
    position: absolute;
    bottom: 4px; /* 🎨 调整位置 */
    right: 4px;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.7)); /* 🎨 渐变背景 */
    color: #d4af37;
    padding: 2px 6px; /* 🎨 增加内边距 */
    border-radius: 8px; /* 🎨 增大圆角 */
    font-size: 11px; /* 🎨 增大字体 */
    font-weight: bold;
    border: 1px solid rgba(212, 175, 55, 0.3); /* 🎨 添加边框 */
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3); /* 🎨 添加阴影 */
}

.avatar-modal-footer {
    padding: 18px 20px; /* 🎨 增加内边距 */
    border-top: 1px solid rgba(212, 175, 55, 0.3);
    display: flex;
    gap: 12px; /* 🎨 增加间距 */
    justify-content: flex-end;
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.08), rgba(212, 175, 55, 0.03)); /* 🎨 渐变背景 */
    flex-shrink: 0; /* 🎨 底部不收缩 */
}

.avatar-modal-btn {
    padding: 10px 24px; /* 🎨 增加内边距 */
    border: none;
    border-radius: 10px; /* 🎨 增大圆角 */
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 80px; /* 🎨 设置最小宽度 */
}

.avatar-modal-btn-cancel {
    background: linear-gradient(135deg, rgba(149, 165, 166, 0.8), rgba(127, 140, 141, 0.8)); /* 🎨 渐变背景 */
    color: #fff;
    border: 1px solid rgba(149, 165, 166, 0.5);
}

.avatar-modal-btn-cancel:hover {
    background: linear-gradient(135deg, rgba(127, 140, 141, 0.9), rgba(108, 123, 125, 0.9)); /* 🎨 悬停效果 */
    transform: translateY(-2px); /* 🎨 添加悬停动画 */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.avatar-modal-btn-confirm {
    background: linear-gradient(135deg, #d4af37, #b8941f);
    color: #1a3a5c;
    box-shadow: 0 4px 12px rgba(212, 175, 55, 0.3); /* 🎨 添加阴影 */
}

.avatar-modal-btn-confirm:hover {
    background: linear-gradient(135deg, #f4d03f, #d4af37);
    transform: translateY(-2px); /* 🎨 添加悬停动画 */
    box-shadow: 0 6px 18px rgba(212, 175, 55, 0.4); /* 🎨 增强阴影 */
}

.avatar-modal-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important; /* 🎨 禁用状态不变换 */
}

/* 🎨 头像选择弹窗移动端优化 */
@media (max-width: 768px) {
    .avatar-modal {
        padding: 10px; /* 🎨 统一移动端上下间距 */
        bottom: 80px; /* 🎨 为底部导航栏留出空间 */
    }
    
    .avatar-modal-content {
        width: 98%; /* 🎨 增加移动端宽度 */
        max-width: 500px;
        max-height: calc(100vh - 120px); /* 🎨 调整移动端最大高度，预留间距 */
        display: flex; /* 🎨 确保flex布局 */
        flex-direction: column; /* 🎨 垂直排列 */
    }
    
    .avatar-modal-header {
        padding: 12px 16px; /* 🎨 调整移动端内边距 */
        flex-shrink: 0; /* 🎨 头部不收缩 */
    }
    
    .avatar-modal-header h3 {
        font-size: 16px; /* 🎨 适当减小移动端标题字体 */
    }
    
    .avatar-modal-close {
        width: 28px; /* 🎨 适当减小移动端关闭按钮 */
        height: 28px;
        font-size: 22px;
    }
    
    .avatar-modal-body {
        padding: 16px; /* 🎨 减少移动端内边距 */
        flex: 1; /* 🎨 主体区域占据剩余空间 */
        overflow-y: auto; /* 🎨 内容区域可滚动 */
        min-height: 0; /* 🎨 允许flex子项收缩 */
    }
    
    .avatar-section {
        margin-bottom: 16px; /* 🎨 减少移动端间距 */
    }
    
    .avatar-section-title {
        gap: 8px; /* 🎨 减少移动端间距 */
        margin-bottom: 12px;
        font-size: 15px; /* 🎨 适当减小字体 */
    }
    
    .avatar-section-icon {
        font-size: 18px; /* 🎨 适当减小图标 */
    }
    
    .avatar-grid {
        gap: 12px; /* 🎨 减少移动端间距 */
    }
    
    .avatar-option {
        width: 90px; /* 🎨 适当减小移动端头像尺寸 */
        height: 90px;
        border-radius: 16px;
    }
    
    .avatar-option .avatar-price {
        font-size: 10px; /* 🎨 适当减小移动端价格字体 */
        padding: 1px 4px;
    }
    
    .avatar-modal-footer {
        padding: 14px 16px; /* 🎨 调整移动端内边距 */
        gap: 10px;
        flex-shrink: 0; /* 🎨 底部不收缩 */
    }
    
    .avatar-modal-btn {
        padding: 8px 20px; /* 🎨 调整移动端按钮内边距 */
        font-size: 13px;
        min-width: 70px;
    }
}


/* 整理按钮样式 */
.inventory-organize-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    padding: 6px 10px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    border: 1px solid rgba(52, 152, 219, 0.6);
    border-radius: 8px;
    color: white;
    font-size: 10px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    box-shadow: 
        0 2px 6px rgba(0, 0, 0, 0.3),
        0 0 8px rgba(52, 152, 219, 0.2);
    z-index: 50 !important;
    min-width: 50px;
    min-height: 24px;
}

.inventory-organize-btn:hover {
    background: linear-gradient(135deg, #2980b9, #1f639a);
    border-color: rgba(52, 152, 219, 0.8);
    transform: translateY(-1px);
    box-shadow: 
        0 3px 8px rgba(0, 0, 0, 0.4),
        0 0 12px rgba(52, 152, 219, 0.4);
}

.inventory-organize-btn:active {
    transform: scale(0.95);
}

.inventory-organize-btn:disabled {
    background: linear-gradient(135deg, #7f8c8d, #6c7b7d);
    border-color: rgba(127, 140, 141, 0.6);
    cursor: not-allowed;
    transform: none;
    opacity: 0.7;
}

.organize-icon {
    font-size: 12px;
    display: inline-block;
}

.organize-text {
    font-size: 9px;
}

.countdown-text {
    font-size: 9px;
    color: #ecf0f1;
    text-align: center;
    display: inline-block;
    width: 100%;
}

/* 🔧 新增：背包管理区域样式 */
.inventory-management {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 6px; /* 🔧 减少内边距 */
    background: rgba(0, 0, 0, 0.4);
    border-radius: 8px;
    border: 1px solid rgba(212, 175, 55, 0.3);
    /* 🔧 修改：固定在底部，距离容器底部8px */
    margin-top: 8px; /* 🔧 距离上方内容8px间距 */
    flex-shrink: 0; /* 🔧 防止被压缩 */
    height: auto; /* 🔧 高度自适应内容 */
    min-height: 42px; /* 🔧 最小高度确保按钮可见 */
}

/* 背包空间信息样式 */
.inventory-space-info {    
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    padding: 2.5px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    border: 1px solid rgba(52, 152, 219, 0.6);
    border-radius: 8px;
    color: white;
    font-size: 10px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3), 0 0 8px rgba(52, 152, 219, 0.2);
    z-index: 50 !important;
    min-width: 50px;
    min-height: 24px;
}

.space-icon {
    font-size: 14px;
}

.space-count {
    font-size: 12px;
    color: #fff;
}

.space-expand-btn {
    padding: 4px 8px;
    background: linear-gradient(135deg, rgba(0, 150, 0, 0.7), rgba(0, 100, 0, 0.5));
    border: 1px solid rgba(0, 150, 0, 0.8);
    border-radius: 5px;
    color: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 10px;
    font-weight: bold;
}

.space-expand-btn:hover {
    background: linear-gradient(135deg, rgba(0, 180, 0, 0.8), rgba(0, 120, 0, 0.6));
    border-color: rgba(0, 180, 0, 0.9);
    transform: scale(1.05);
}

.space-expand-btn:disabled {
    background: linear-gradient(135deg, rgba(100, 100, 100, 0.5), rgba(80, 80, 80, 0.3));
    border-color: rgba(100, 100, 100, 0.6);
    cursor: not-allowed;
    transform: none;
}

/* 🔧 修改：背包网格样式优化 */
.inventory-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 8px;
    /* 🔧 修改：使用固定行高，确保间距一致 */
    grid-auto-rows: minmax(45px, auto);
    /* 🔧 修改：固定内容对齐方式 */
    align-content: start;
    justify-content: center;
    /* 🔧 修改：使用flex布局自动占用剩余空间，距离底部管理区域2px */
    flex: 1;
    margin-bottom: 2px; /* 🔧 与管理区域保持2px间距 */
    overflow-y: auto;
    padding: 8px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 0 0 8px 8px;
    border: 1px solid rgba(212, 175, 55, 0.3);
    border-top: none; /* 与标签连接 */
    min-height: 120px; /* 🔧 设置合理的最小高度 */
}

/* 移动端响应式优化 */
@media (max-width: 768px) {

    .character-equipment-section {
        min-height: 120px; /* 🔧 减少角色装备区域高度 */
    }

    .character-avatar {

        font-size: 24px;
    }

    .equipment-slot {
        width: 38px;
        height: 38px;
        font-size: 8px;
    }

    .weapon-slot {
        width: 38px;
        height: 38px;
        font-size: 8px;
    }

    .weapon-grid {
        gap: 5px;
    }

    .inventory-grid {
        /* 🔧 移动端背包网格高度优化 */
        max-height: calc(100vh - 220px); /* 简化计算，确保有足够空间 */
        gap: 6px;
        /* 🔧 修改：移动端也使用固定行高 */
        grid-auto-rows: minmax(40px, auto);
        align-content: start;
        min-height: 100px; /* 🔧 移动端减少最小高度 */
        padding: 6px;
    }

    .inventory-section {
        min-height: 140px; /* 🔧 移动端背包区域最小高度 */
        max-height: calc(100vh - 200px); /* 🔧 移动端背包区域最大高度 */
        padding: 8px; /* 🔧 移动端内边距 */
    }

    .inventory-item {
        font-size: 10px;
    }

    .tab {
        font-size: 11px;
        padding: 4px 6px; /* 🔧 减少标签内边距 */
        height: 24px; /* 🔧 减少标签高度 */
    }

    .inventory-management {
        padding: 8px 6px;
        gap: 8px;
        flex-shrink: 0; /* 🔧 新增：防止管理区域被压缩 */
        height: 42px; /* 🔧 固定管理区域高度 */
    }

    .inventory-space-info {
        font-size: 11px;
    }

    .space-icon {
        font-size: 12px;
    }

    .space-count {
        font-size: 11px;
    }

    .space-expand-btn {
        font-size: 10px;
        padding: 4px 6px;
    }

    .inventory-organize-btn {
        font-size: 10px;
        padding: 6px 10px;
    }

    .organize-icon {
        font-size: 12px;
    }

    .organize-text {
        font-size: 10px;
    }
}

@media (max-width: 360px) {

    .inventory-grid {
        /* 🔧 超小屏幕背包网格高度优化 */
        max-height: calc(100vh - 200px); /* 简化计算，确保按钮区域可见 */
        gap: 4px;
        /* 🔧 修改：超小屏幕也使用固定行高 */
        grid-auto-rows: minmax(35px, auto);
        align-content: start;
        min-height: 80px; /* 🔧 超小屏幕最小高度 */
        padding: 4px; /* 🔧 减少内边距 */
    }

    .inventory-section {
        min-height: 120px; /* 🔧 超小屏幕背包区域最小高度 */
        max-height: calc(100vh - 180px); /* 🔧 超小屏幕背包区域最大高度 */
        padding: 6px; /* 🔧 超小屏幕内边距 */
    }
    
    .tab {
        font-size: 9px;
        padding: 3px 4px; /* 🔧 进一步减少标签内边距 */
        height: 20px; /* 🔧 减少标签高度 */
    }

    .inventory-management {
        padding: 4px;
        gap: 4px;
        height: 36px; /* 🔧 减少管理区域高度 */
    }

    .inventory-space-info, .inventory-organize-btn {
        font-size: 9px;
    }

    .space-expand-btn {
        padding: 2px 4px;
        font-size: 8px;
    }

    .character-section {
        min-height: 100px; /* 🔧 超小屏幕进一步减少角色区域高度 */
    }
    
    .equipment-arc {
        height: 120px; /* 🔧 减少装备弧形区域高度 */
    }
    
    .character-avatar {
        width: 85px;
        height: 105px;
        font-size: 22px;
    }
    
    .equipment-slot, .weapon-slot, .inventory-item {
        width: 30px;
        height: 30px;
        font-size: 7px;
    }
    
    .weapon-grid {
        gap: 4px;
    }

    /* 超小屏幕装备槽位调整 */
    .equipment-slot[data-slot="ring"] {
        top: 5px;
        left: calc(50% - 70px); /* 🔧 调整位置 */
    }

    .equipment-slot[data-slot="accessory"] {
        top: 5px;
        right: calc(50% - 70px); /* 🔧 调整位置 */
    }

    .equipment-slot[data-slot="bracers"] {
        top: 50%;
        left: 5px;
        transform: translateY(-50%);
    }

    .equipment-slot[data-slot="chest"] {
        top: 50%;
        right: 5px;
        transform: translateY(-50%);
    }

    .equipment-slot[data-slot="belt"] {
        bottom: 5px;
        left: calc(50% - 70px); /* 🔧 调整位置 */
    }

    .equipment-slot[data-slot="boots"] {
        bottom: 5px;
        right: calc(50% - 70px); /* 🔧 调整位置 */
    }

    /* 保持居中变换 */
    .equipment-slot[data-slot="bracers"]:hover {
        transform: translateY(-50%) scale(1.05);
    }

    .equipment-slot[data-slot="chest"]:hover {
        transform: translateY(-50%) scale(1.05);
    }

    .equipment-slot[data-slot="bracers"]:active {
        transform: translateY(-50%) scale(0.95);
    }

    .equipment-slot[data-slot="chest"]:active {
        transform: translateY(-50%) scale(0.95);
    }
}

/* PWA模式特殊处理 */
@media (display-mode: standalone) {
    
    .inventory-grid {
        -webkit-overflow-scrolling: touch !important;
        overscroll-behavior: contain !important;
    }
}

/* 🔧 新增：武器槽位选择弹窗样式 */
.weapon-slot-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    backdrop-filter: blur(5px);
}

.weapon-slot-popup {
    background: linear-gradient(135deg, rgba(20, 30, 40, 0.95), rgba(30, 40, 50, 0.95));
    border: 2px solid rgba(212, 175, 55, 0.8);
    border-radius: 15px;
    padding: 20px;
    max-width: 95%;
    width: 450px; /* 🔧 修改：固定宽度确保槽位显示 */
    max-height: 85vh; /* 🔧 修改：确保不超出屏幕 */
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.8);
}

.weapon-slot-popup .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px; /* 🔧 减少间距 */
    border-bottom: 1px solid rgba(212, 175, 55, 0.3);
    padding-bottom: 8px; /* 🔧 减少间距 */
}

.weapon-slot-popup .popup-header h3 {
    color: #d4af37;
    margin: 0;
    font-size: 16px; /* 🔧 减小字体 */
}

.weapon-slot-popup .close-btn {
    background: none;
    border: none;
    color: #bdc3c7;
    font-size: 20px; /* 🔧 减小字体 */
    cursor: pointer;
    padding: 0;
    width: 25px; /* 🔧 减小尺寸 */
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.weapon-slot-popup .close-btn:hover {
    background: rgba(255, 99, 99, 0.2);
    color: #ff6363;
}

.weapon-info {
    margin-bottom: 15px; /* 🔧 减少间距 */
    text-align: center;
}

.weapon-info .weapon-name {
    font-size: 14px; /* 🔧 减小字体 */
    font-weight: bold;
    margin-bottom: 5px;
}

.weapon-info .weapon-desc {
    color: #bdc3c7;
    font-size: 12px; /* 🔧 减小字体 */
}

.weapon-slots-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr); /* 🔧 修改：3列2行显示6个槽位 */
    gap: 8px; /* 🔧 减少间距 */
    margin-bottom: 15px; /* 🔧 减少间距 */
}

.weapon-slot-option {
    padding: 10px; /* 🔧 减少内边距 */
    border: 2px solid rgba(127, 140, 141, 0.3);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    background: rgba(44, 62, 80, 0.3);
    min-height: 70px; /* 🔧 设置最小高度确保内容显示 */
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.weapon-slot-option:hover {
    border-color: rgba(212, 175, 55, 0.6);
    background: rgba(212, 175, 55, 0.1);
    transform: translateY(-1px); /* 🔧 减少移动距离 */
}

.weapon-slot-option.slot-empty {
    border-color: rgba(46, 204, 113, 0.4);
}

.weapon-slot-option.slot-empty:hover {
    border-color: rgba(46, 204, 113, 0.8);
    background: rgba(46, 204, 113, 0.1);
}

.weapon-slot-option.slot-occupied {
    border-color: rgba(231, 76, 60, 0.4);
}

.weapon-slot-option.slot-occupied:hover {
    border-color: rgba(231, 76, 60, 0.8);
    background: rgba(231, 76, 60, 0.1);
}

.weapon-slot-option .slot-number {
    font-size: 14px; /* 🔧 减小字体 */
    font-weight: bold;
    color: #ecf0f1;
    margin-bottom: 3px; /* 🔧 减少间距 */
}

.weapon-slot-option .slot-status {
    font-size: 11px; /* 🔧 减小字体 */
    color: #bdc3c7;
    margin-bottom: 3px; /* 🔧 减少间距 */
    line-height: 1.2;
}

.weapon-slot-option .slot-warning {
    font-size: 10px; /* 🔧 减小字体 */
    color: #f39c12;
    font-style: italic;
    line-height: 1.1;
}

.weapon-slot-popup .popup-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px; /* 🔧 减少间距 */
    border-top: 1px solid rgba(212, 175, 55, 0.3);
    padding-top: 10px; /* 🔧 减少间距 */
}

.weapon-slot-popup .btn {
    padding: 6px 12px; /* 🔧 减少内边距 */
    border: 1px solid;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px; /* 🔧 减小字体 */
    transition: all 0.3s ease;
}

.weapon-slot-popup .btn-cancel {
    background: rgba(127, 140, 141, 0.2);
    border-color: rgba(127, 140, 141, 0.4);
    color: #bdc3c7;
}

.weapon-slot-popup .btn-cancel:hover {
    background: rgba(127, 140, 141, 0.3);
    border-color: rgba(127, 140, 141, 0.6);
}

/* 移动端适配 */
@media (max-width: 768px) {
    .weapon-slot-popup {
        max-width: 95%;
        width: auto;
        padding: 15px;
        max-height: 90vh; /* 🔧 移动端更高的高度限制 */
    }
    
    .weapon-slots-grid {
        grid-template-columns: repeat(2, 1fr); /* 🔧 移动端2列3行 */
        gap: 6px;
    }
    
    .weapon-slot-option {
        padding: 8px;
        min-height: 60px; /* 🔧 移动端减小高度 */
    }
    
    .weapon-slot-popup .popup-header h3 {
        font-size: 14px;
    }
    
    .weapon-info .weapon-name {
        font-size: 13px;
    }
    
    .weapon-info .weapon-desc {
        font-size: 11px;
    }
}

/* 超小屏幕适配 */
@media (max-width: 360px) {
    .weapon-slot-popup {
        padding: 12px;
        max-height: 95vh;
    }
    
    .weapon-slots-grid {
        gap: 4px;
    }
    
    .weapon-slot-option {
        padding: 6px;
        min-height: 50px;
    }
    
    .weapon-slot-option .slot-number {
        font-size: 12px;
    }
    
    .weapon-slot-option .slot-status {
        font-size: 10px;
    }
    
    .weapon-slot-option .slot-warning {
        font-size: 9px;
    }
}

/* 🆕 属性变化浮窗样式 */
.attribute-changes-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    opacity: 1;
    transition: opacity 0.3s ease;
    pointer-events: none; /* 不阻止用户操作 */
}

.attribute-changes-popup {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(30, 30, 30, 0.95));
    border: 2px solid rgba(212, 175, 55, 0.8);
    border-radius: 15px;
    padding: 20px;
    min-width: 280px;
    max-width: 400px;
    backdrop-filter: blur(15px);
    box-shadow: 
        0 15px 35px rgba(0, 0, 0, 0.5),
        0 0 25px rgba(212, 175, 55, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    animation: attributePopupIn 0.3s ease-out;
}

@keyframes attributePopupIn {
    0% {
        opacity: 0;
        transform: scale(0.8) translateY(-20px);
    }
    100% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.attribute-changes-popup .popup-header {
    text-align: center;
    margin-bottom: 15px;
    border-bottom: 1px solid rgba(212, 175, 55, 0.3);
    padding-bottom: 10px;
}

.attribute-changes-popup .popup-header h3 {
    color: #d4af37;
    font-size: 18px;
    font-weight: bold;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.attribute-changes-popup .popup-content {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.attribute-change-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.2s ease;
}

.attribute-change-item:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateX(2px);
}

.attribute-name {
    font-size: 14px;
    font-weight: 500;
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.attribute-value {
    font-size: 14px;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    gap: 4px;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .attribute-changes-popup {
        margin: 20px;
        min-width: 260px;
        padding: 15px;
    }
    
    .attribute-changes-popup .popup-header h3 {
        font-size: 16px;
    }
    
    .attribute-name,
    .attribute-value {
        font-size: 13px;
    }
    
    .attribute-change-item {
        padding: 6px 10px;
    }
}

@media (max-width: 360px) {
    .attribute-changes-popup {
        margin: 15px;
        min-width: 240px;
        padding: 12px;
    }
    
    .attribute-changes-popup .popup-header h3 {
        font-size: 15px;
    }
    
    .attribute-name,
    .attribute-value {
        font-size: 12px;
    }
    
    .attribute-change-item {
        padding: 5px 8px;
        gap: 6px;
    }
}
