<?php
// 战斗系统功能完整性测试
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== 战斗系统功能完整性测试 ===\n";

// 1. 测试数据库连接
echo "\n1. 测试数据库连接...\n";
try {
    // 使用正确的数据库配置
    $pdo = new PDO("mysql:host=localhost;dbname=yn_game", "ynxx", "mjlxz159");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ 数据库连接成功\n";
} catch (Exception $e) {
    echo "❌ 数据库连接失败: " . $e->getMessage() . "\n";
    exit(1);
}

// 2. 测试用户数据获取
echo "\n2. 测试用户数据获取...\n";
try {
    session_start();
    $_SESSION['user_id'] = 1; // 模拟登录用户
    
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([1]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user) {
        echo "✅ 用户数据获取成功: " . $user['username'] . "\n";
        echo "   - 等级: " . $user['level'] . "\n";
        echo "   - 生命值: " . $user['max_hp'] . "\n";
        echo "   - 物理攻击: " . $user['physical_attack'] . "\n";
    } else {
        echo "❌ 用户数据获取失败\n";
    }
} catch (Exception $e) {
    echo "❌ 用户数据获取异常: " . $e->getMessage() . "\n";
}

// 3. 测试武器数据获取
echo "\n3. 测试武器数据获取...\n";
try {
    $stmt = $pdo->prepare("
        SELECT ce.*, gi.name, gi.attack_power, gi.current_durability, gi.max_durability, gi.skill_name
        FROM character_equipment ce
        LEFT JOIN game_items gi ON ce.inventory_item_id = gi.id
        WHERE ce.character_id = ? AND ce.slot_type = 'weapon'
        ORDER BY ce.slot_index
    ");
    $stmt->execute([1]);
    $weapons = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "✅ 武器数据获取成功，共 " . count($weapons) . " 个槽位\n";
    foreach ($weapons as $weapon) {
        if ($weapon['inventory_item_id']) {
            echo "   - 槽位" . $weapon['slot_index'] . ": " . $weapon['name'] . 
                 " (攻击:" . $weapon['attack_power'] . 
                 ", 耐久:" . $weapon['current_durability'] . "/" . $weapon['max_durability'] . ")\n";
        } else {
            echo "   - 槽位" . $weapon['slot_index'] . ": 空\n";
        }
    }
} catch (Exception $e) {
    echo "❌ 武器数据获取异常: " . $e->getMessage() . "\n";
}

// 4. 测试套装效果数据
echo "\n4. 测试套装效果数据...\n";
try {
    $stmt = $pdo->prepare("
        SELECT gis.*, COUNT(ce.id) as equipped_count
        FROM game_item_sets gis
        LEFT JOIN game_items gi ON FIND_IN_SET(gi.id, gis.item_ids)
        LEFT JOIN character_equipment ce ON ce.inventory_item_id = gi.id AND ce.character_id = ?
        GROUP BY gis.id
        HAVING equipped_count >= gis.required_pieces
    ");
    $stmt->execute([1]);
    $activeSets = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "✅ 套装效果数据获取成功，激活 " . count($activeSets) . " 个套装\n";
    foreach ($activeSets as $set) {
        echo "   - " . $set['name'] . ": " . $set['effects'] . "\n";
    }
} catch (Exception $e) {
    echo "❌ 套装效果数据获取异常: " . $e->getMessage() . "\n";
}

// 5. 测试关卡数据获取
echo "\n5. 测试关卡数据获取...\n";
try {
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([1]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $currentStage = $user['current_stage'] ?? 1;
    echo "✅ 当前关卡: " . $currentStage . "\n";
    
    // 获取敌人数据
    $stmt = $pdo->prepare("SELECT * FROM game_monsters WHERE stage_level = ?");
    $stmt->execute([$currentStage]);
    $monster = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($monster) {
        echo "✅ 敌人数据获取成功: " . $monster['name'] . "\n";
        echo "   - 生命值: " . $monster['max_hp'] . "\n";
        echo "   - 攻击力: " . $monster['attack'] . "\n";
        echo "   - 防御力: " . $monster['defense'] . "\n";
    } else {
        echo "❌ 敌人数据获取失败\n";
    }
} catch (Exception $e) {
    echo "❌ 关卡数据获取异常: " . $e->getMessage() . "\n";
}

echo "\n=== 测试完成 ===\n";
?>
