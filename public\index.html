<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <!-- 移动端适配核心meta标签 -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover">
    
    <!-- 🔧 PWA模式底部白边强力修复 - 必须最早执行 -->
    <script src="assets/js/pwa-fix.js"></script>
    
    <!-- 🎛️ 全局调试开关 - 必须最早加载 -->
    <script src="assets/js/global-debug-switch.js"></script>

    <!-- 🔧 项目配置文件 - 必须早期加载 -->
    <script src="assets/js/config.js"></script>

    <!-- WebView优化配置 -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="screen-orientation" content="portrait">
    <meta name="x5-orientation" content="portrait">
    <meta name="full-screen" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta name="browsermode" content="application">
    <meta name="x5-page-mode" content="app">
    <!-- 禁用长按菜单 -->
    <meta name="format-detection" content="telephone=no,email=no,address=no">
    <!-- 强制使用最新版本 -->
    <meta http-equiv="Cache-Control" content="no-siteapp">
    <meta http-equiv="Cache-Control" content="no-transform">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <!-- 启用硬件加速 -->
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <!-- iOS图标 -->
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/images/app-icon-180.png">
    <link rel="apple-touch-icon" sizes="167x167" href="/assets/images/app-icon-167.png">
    <link rel="apple-touch-icon" sizes="152x152" href="/assets/images/app-icon-152.png">
    <link rel="apple-touch-icon" sizes="120x120" href="/assets/images/app-icon-120.png">
    <!-- PWA支持 -->
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#d4af37">

    <title>一念仙魔 - 主菜单</title>
    <link rel="stylesheet" href="assets/css/global.css">
    <link rel="stylesheet" href="assets/css/index.css">
    
    <!-- 🔧 视口高度修复脚本 -->
    <script src="assets/js/viewport-height-fix.js"></script>

</head>
<body>
    <div class="user-status" id="userStatus">
        欢迎回来，<span id="currentUser">用户</span>！
    </div>
    
    <div class="menu-container">
        <h1 class="game-title">一念仙魔</h1>
        <p class="game-subtitle">体验激烈的修仙战斗，掌握强大的法术技能</p>
        
        <div class="menu-buttons">
            <button class="menu-button" onclick="showInstructions()">游戏说明</button>
        </div>
        
        <div class="user-buttons" id="guestButtons">
            <a href="login.html" class="user-button primary">登录</a>
            <a href="register.html" class="user-button">注册</a>
        </div>
        
        <div class="user-buttons" id="userButtons" style="display: none;">
            <a href="game.html" class="user-button primary">进入游戏</a>
            <button class="user-button" onclick="logout()">退出登录</button>
        </div>
    </div>
    
    <div class="version-info">
        版本 1.0.0
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            checkUserStatus();
            
            // 全局音乐管理器会自动处理音乐播放，无需手动启动
        });
        
        function checkUserStatus() {
            fetch(window.GameConfig ? window.GameConfig.getApiUrl('user_info.php') : '../src/api/user_info.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 用户已登录，优先显示角色名（如果不同于用户名）
                        const displayName = (data.user.character_name && data.user.character_name !== data.user.username) 
                            ? data.user.character_name 
                            : data.user.username;
                        document.getElementById('currentUser').textContent = displayName;
                        document.getElementById('userStatus').style.display = 'block';
                        document.getElementById('guestButtons').style.display = 'none';
                        document.getElementById('userButtons').style.display = 'flex';
                        
                        // 存储用户信息供更名功能使用
                        window.currentUserData = data.user;
                    } else {
                        // 用户未登录，显示游客按钮
                        document.getElementById('userStatus').style.display = 'none';
                        document.getElementById('guestButtons').style.display = 'flex';
                        document.getElementById('userButtons').style.display = 'none';
                    }
                })
                .catch(error => {
                    // 网络错误或其他问题，显示游客按钮
                    console.log('检查用户状态失败:', error);
                    document.getElementById('userStatus').style.display = 'none';
                    document.getElementById('guestButtons').style.display = 'flex';
                    document.getElementById('userButtons').style.display = 'none';
                });
        }
        
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                fetch(window.GameConfig ? window.GameConfig.getApiUrl('logout.php') : '../src/api/logout.php', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        checkUserStatus(); // 重新检查状态
                    } else {
                        alert('退出登录失败');
                    }
                })
                .catch(error => {
                    console.error('退出登录失败:', error);
                    alert('退出登录失败');
                });
            }
        }
        
        function showInstructions() {
            alert('游戏说明：\n\n' +
                  '• 这是一个轻量级文字游戏\n' +
                  '• 玩家和敌人会自动使用各种技能战斗\n' +
                  '• 技能包括：飞剑、万剑诀、掌心雷、火球术、巨剑术\n' +
                  '• 击败敌人后会掉落各种品质的装备\n' +
                  '• 注册账号可以保存游戏进度和数据\n' +
                  '• 与其他玩家在排行榜上竞争\n');
        }
    </script>
    
</body>
</html> 