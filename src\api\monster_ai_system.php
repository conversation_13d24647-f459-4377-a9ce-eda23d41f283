<?php
/**
 * 一念修仙 - AI战斗系统
 * 实现四种AI模式的战斗逻辑
 */

class MonsterAI {
    
    /**
     * 获取AI行动策略
     * @param string $aiPattern AI模式 (conservative/balanced/aggressive/intelligent)
     * @param array $battleState 战斗状态
     * @return array 行动决策
     */
    static function getAIAction($aiPattern, $battleState) {
        $monster = $battleState['monster'];
        $player = $battleState['player'];
        
        // 计算怪物状态
        $monsterMaxHp = 1;
    if (isset($monster['hp_bonus']) && $monster['hp_bonus'] > 0) {
        $monsterMaxHp = $monster['hp_bonus'];
        }
        $hpPercentage = $monster['hp'] / $monsterMaxHp;
        
        // 使用怪物本身的base_mp数值（数据库中的实际字段）
        $monsterMaxMp = 100; // 默认值
        if (isset($monster['base_mp']) && $monster['base_mp'] > 0) {
            $monsterMaxMp = $monster['base_mp'];
        } elseif (isset($monster['mp_bonus']) && $monster['mp_bonus'] > 0) {
            $monsterMaxMp = $monster['mp_bonus'];
        } elseif (isset($monster['mp']) && $monster['mp'] > 0) {
            $monsterMaxMp = $monster['mp'];
        }
        $mpPercentage = $monster['mp'] / $monsterMaxMp;
        
        // 根据AI模式决定行动
        switch ($aiPattern) {
            case 'conservative':
                return self::getConservativeAction($monster, $player, $hpPercentage, $mpPercentage);
            
            case 'balanced':
                return self::getBalancedAction($monster, $player, $hpPercentage, $mpPercentage);
            
            case 'aggressive':
                return self::getAggressiveAction($monster, $player, $hpPercentage, $mpPercentage);
            
            case 'intelligent':
                return self::getIntelligentAction($monster, $player, $hpPercentage, $mpPercentage);
            
            default:
                return self::getConservativeAction($monster, $player, $hpPercentage, $mpPercentage);
        }
    }
    
    /**
     * 保守型AI：80%普攻，15%技能，5%防御
     */
    static function getConservativeAction($monster, $player, $hpPercentage, $mpPercentage) {
        $rand = rand(1, 100);
        
        if ($rand <= 5 && $hpPercentage < 0.3) {
            // 生命值低时防御
            return [
                'action' => 'defend',
                'skill' => null,
                'damage_modifier' => 0.5, // 防御时受到伤害减半
                'animation' => 'defend',
                'message' => '进入防御姿态'
            ];
        } elseif ($rand <= 20 && $mpPercentage > 0.3) {
            // 有足够法力时使用技能
            return [
                'action' => 'skill',
                'skill' => self::selectSkill($monster, 'conservative'),
                'damage_modifier' => 1.2,
                'animation' => 'skill',
                'message' => '使用了技能'
            ];
        } else {
            // 大部分时间普攻
            return [
                'action' => 'attack',
                'skill' => null,
                'damage_modifier' => 1.0,
                'animation' => 'attack',
                'message' => '发动普通攻击'
            ];
        }
    }
    
    /**
     * 均衡型AI：60%普攻，25%技能，10%防御，5%连击
     */
    static function getBalancedAction($monster, $player, $hpPercentage, $mpPercentage) {
        $rand = rand(1, 100);
        
        if ($rand <= 5 && $mpPercentage > 0.5) {
            // 连击技能
            return [
                'action' => 'combo',
                'skill' => self::selectSkill($monster, 'combo'),
                'damage_modifier' => 1.5,
                'hit_count' => 2,
                'animation' => 'combo',
                'message' => '发动连击'
            ];
        } elseif ($rand <= 15 && $hpPercentage < 0.5) {
            // 血量低时防御
            return [
                'action' => 'defend',
                'skill' => null,
                'damage_modifier' => 0.6,
                'animation' => 'defend',
                'message' => '进入防御姿态'
            ];
        } elseif ($rand <= 40 && $mpPercentage > 0.2) {
            // 使用技能
            return [
                'action' => 'skill',
                'skill' => self::selectSkill($monster, 'balanced'),
                'damage_modifier' => 1.3,
                'animation' => 'skill',
                'message' => '施展法术'
            ];
        } else {
            // 普通攻击
            return [
                'action' => 'attack',
                'skill' => null,
                'damage_modifier' => 1.0,
                'animation' => 'attack',
                'message' => '挥爪攻击'
            ];
        }
    }
    
    /**
     * 攻击型AI：50%普攻，35%技能，10%连击，5%防御
     */
    static function getAggressiveAction($monster, $player, $hpPercentage, $mpPercentage) {
        $rand = rand(1, 100);
        
        if ($rand <= 10 && $mpPercentage > 0.4) {
            // 强力连击
            return [
                'action' => 'combo',
                'skill' => self::selectSkill($monster, 'aggressive'),
                'damage_modifier' => 1.8,
                'hit_count' => rand(2, 3),
                'animation' => 'combo',
                'message' => '狂暴连击'
            ];
        } elseif ($rand <= 5 && $hpPercentage < 0.2) {
            // 血量极低时才防御
            return [
                'action' => 'defend',
                'skill' => null,
                'damage_modifier' => 0.7,
                'animation' => 'defend',
                'message' => '负伤防守'
            ];
        } elseif ($rand <= 45 && $mpPercentage > 0.1) {
            // 频繁使用技能
            return [
                'action' => 'skill',
                'skill' => self::selectSkill($monster, 'aggressive'),
                'damage_modifier' => 1.4,
                'animation' => 'skill',
                'message' => '释放强力技能'
            ];
        } else {
            // 猛烈普攻
            return [
                'action' => 'attack',
                'skill' => null,
                'damage_modifier' => 1.2,
                'animation' => 'attack',
                'message' => '猛烈攻击'
            ];
        }
    }
    
    /**
     * 智能型AI：40%普攻，40%技能，15%连击，5%防御（BOSS专用）
     */
    static function getIntelligentAction($monster, $player, $hpPercentage, $mpPercentage) {
        $rand = rand(1, 100);
        $playerHpPercentage = $player['hp'] / $player['hp_bonus'];
        
        // 智能判断：玩家血量低时加强攻击
        if ($playerHpPercentage < 0.3 && $mpPercentage > 0.3) {
            // 玩家血量低，使用强力技能追击
            return [
                'action' => 'skill',
                'skill' => self::selectSkill($monster, 'finishing'),
                'damage_modifier' => 2.0,
                'animation' => 'ultimate',
                'message' => '施展必杀技'
            ];
        } elseif ($rand <= 15 && $mpPercentage > 0.5) {
            // 智能连击
            return [
                'action' => 'combo',
                'skill' => self::selectSkill($monster, 'intelligent'),
                'damage_modifier' => 1.6,
                'hit_count' => rand(2, 4),
                'animation' => 'combo',
                'message' => '施展连环技'
            ];
        } elseif ($rand <= 5 && $hpPercentage < 0.4) {
            // 战术防御
            return [
                'action' => 'defend',
                'skill' => null,
                'damage_modifier' => 0.4,
                'animation' => 'defend',
                'message' => '战术防御'
            ];
        } elseif ($rand <= 45 && $mpPercentage > 0.2) {
            // 智能技能选择
            return [
                'action' => 'skill',
                'skill' => self::selectSkill($monster, 'intelligent'),
                'damage_modifier' => 1.5,
                'animation' => 'skill',
                'message' => '释放法术'
            ];
        } else {
            // 精准攻击
            return [
                'action' => 'attack',
                'skill' => null,
                'damage_modifier' => 1.1,
                'crit_bonus' => 0.1, // 额外10%暴击率
                'animation' => 'attack',
                'message' => '精准攻击'
            ];
        }
    }
    
    /**
     * 选择技能
     */
    static function selectSkill($monster, $aiType) {
        $skills = isset($monster['skills']) ? $monster['skills'] : ['普通攻击'];
        
        // 根据AI类型选择不同的技能
        switch ($aiType) {
            case 'conservative':
                return isset($skills[0]) ? $skills[0] : '普通攻击';
            
            case 'balanced':
            case 'combo':
                return count($skills) > 1 ? $skills[1] : (isset($skills[0]) ? $skills[0] : '普通攻击');
            
            case 'aggressive':
            case 'intelligent':
                return count($skills) > 2 ? $skills[2] : (count($skills) > 1 ? $skills[1] : $skills[0]);
            
            case 'finishing':
                // 选择最强技能
                return end($skills);
            
            default:
                return isset($skills[0]) ? $skills[0] : '普通攻击';
        }
    }
}
?>