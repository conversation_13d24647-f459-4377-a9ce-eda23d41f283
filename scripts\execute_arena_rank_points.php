<?php
// 竞技场段位积分系统数据库更新执行脚本
// 创建时间：2024年12月19日

require_once 'src/config/database.php';

try {
    $pdo = getDatabase();
    if (!$pdo) {
        throw new Exception('数据库连接失败');
    }
    
    echo "🚀 开始执行竞技场段位积分系统数据库更新...\n\n";
    
    // 1. 为角色表添加段位积分字段
    echo "1️⃣ 添加段位积分字段...\n";
    // 检查字段是否存在
    $stmt = $pdo->query("SHOW COLUMNS FROM characters LIKE 'arena_rank_points'");
    if ($stmt->rowCount() == 0) {
        try {
            $sql1 = "ALTER TABLE `characters` ADD COLUMN `arena_rank_points` int(11) DEFAULT 0 COMMENT '竞技场段位积分' AFTER `arena_rank_level`";
            $pdo->exec($sql1);
            echo "✅ 角色表段位积分字段添加成功\n";
        } catch (Exception $e) {
            echo "❌ 添加段位积分字段失败: " . $e->getMessage() . "\n";
        }
    } else {
        echo "⚠️ 段位积分字段已存在\n";
    }
    
    // 2. 为段位表添加积分要求字段
    echo "\n2️⃣ 添加段位积分要求字段...\n";
    // 检查字段是否存在
    $stmt = $pdo->query("SHOW COLUMNS FROM immortal_arena_ranks LIKE 'required_points'");
    if ($stmt->rowCount() == 0) {
        try {
            $sql2 = "ALTER TABLE `immortal_arena_ranks` ADD COLUMN `required_points` int(11) DEFAULT 0 COMMENT '晋升所需积分' AFTER `reward_multiplier`";
            $pdo->exec($sql2);
            echo "✅ 段位表积分要求字段添加成功\n";
        } catch (Exception $e) {
            echo "❌ 添加积分要求字段失败: " . $e->getMessage() . "\n";
        }
    } else {
        echo "⚠️ 积分要求字段已存在\n";
    }
    
    // 3. 更新段位表的积分要求
    echo "\n3️⃣ 更新段位积分要求...\n";
    $sql3 = "UPDATE `immortal_arena_ranks` SET `required_points` = 
    CASE `rank_level`
        WHEN 1 THEN 0      -- 练气期：0积分
        WHEN 2 THEN 50     -- 筑基期：50积分
        WHEN 3 THEN 150    -- 结丹期：150积分
        WHEN 4 THEN 300    -- 元婴期：300积分
        WHEN 5 THEN 500    -- 化神期：500积分
        WHEN 6 THEN 750    -- 合体期：750积分
        WHEN 7 THEN 1050   -- 大乘期：1050积分
        WHEN 8 THEN 1400   -- 渡劫期：1400积分
        WHEN 9 THEN 1800   -- 仙人境：1800积分
        WHEN 10 THEN 2250  -- 仙君境：2250积分
        ELSE 0
    END";
    $pdo->exec($sql3);
    echo "✅ 段位积分要求更新完成\n";
    
    // 4. 创建索引
    echo "\n4️⃣ 创建索引...\n";
    try {
        $sql4 = "CREATE INDEX `idx_arena_rank_points` ON `characters` (`arena_rank_points`)";
        $pdo->exec($sql4);
        echo "✅ 段位积分索引创建成功\n";
    } catch (Exception $e) {
        echo "⚠️ 索引可能已存在: " . substr($e->getMessage(), 0, 50) . "\n";
    }
    
    // 5. 初始化现有角色的段位积分
    echo "\n5️⃣ 初始化现有角色段位积分...\n";
    $sql5 = "UPDATE `characters` 
    SET `arena_rank_points` = GREATEST(0, 
        (`arena_total_wins` * 15) - (`arena_total_battles` - `arena_total_wins`) * 5
    )
    WHERE `arena_rank_points` = 0 OR `arena_rank_points` IS NULL";
    $result = $pdo->exec($sql5);
    echo "✅ 更新了 {$result} 个角色的段位积分\n";
    
    // 6. 验证数据
    echo "\n6️⃣ 验证更新结果...\n";
    
    // 检查字段是否存在
    $stmt = $pdo->query("SHOW COLUMNS FROM characters LIKE 'arena_rank_points'");
    if ($stmt->rowCount() > 0) {
        echo "✅ 角色表段位积分字段存在\n";
    }
    
    $stmt = $pdo->query("SHOW COLUMNS FROM immortal_arena_ranks LIKE 'required_points'");
    if ($stmt->rowCount() > 0) {
        echo "✅ 段位表积分要求字段存在\n";
    }
    
    // 显示段位积分配置
    echo "\n🏆 段位积分配置:\n";
    $stmt = $pdo->query("SELECT rank_level, rank_name, required_points FROM immortal_arena_ranks ORDER BY rank_level");
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "   等级{$row['rank_level']}: {$row['rank_name']} ({$row['required_points']} 积分)\n";
    }
    
    // 显示角色积分统计
    echo "\n📊 角色积分统计:\n";
    $stmt = $pdo->query("SELECT COUNT(*) as count, MIN(arena_rank_points) as min_points, MAX(arena_rank_points) as max_points, AVG(arena_rank_points) as avg_points FROM characters WHERE arena_rank_points > 0");
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "   总角色数: {$stats['count']}\n";
    echo "   最低积分: {$stats['min_points']}\n";
    echo "   最高积分: {$stats['max_points']}\n";
    echo "   平均积分: " . round($stats['avg_points'], 1) . "\n";
    
    echo "\n🎉 竞技场段位积分系统数据库更新完成！\n";
    echo "📋 更新内容:\n";
    echo "   🏷️  段位积分字段 (characters.arena_rank_points)\n";
    echo "   🎯 积分要求字段 (immortal_arena_ranks.required_points)\n";
    echo "   📈 积分获取机制 (胜+15分，败-5分)\n";
    echo "   🏆 段位晋升条件 (基于积分)\n";
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}
?> 