/**
 * 升仙大会竞技系统 JavaScript
 * 功能：匹配、战斗、记录、排行等
 * 创建时间：2025年6月17日
 */

class ImmortalArena {
    constructor() {
        this.apiUrl = window.GameConfig ? window.GameConfig.getApiUrl('immortal_arena.php') : './src/api/immortal_arena.php';
        this.matchingTimer = null;
        this.resetTimer = null;
        this.isMatching = false;
        this.currentOpponent = null;
        this.purchaseCosts = [50, 70, 90, 110, 130, 150, 170, 190, 210, 230]; // 购买费用递增
        
        console.log('🏆 [竞技系统] ImmortalArena 初始化完成');
    }

    /**
     * 初始化竞技系统
     */
    async init() {
        try {
            console.log('🏆 [竞技系统] 开始初始化');
            
            // 重置匹配状态（页面刷新后确保状态正确）
            this.isMatching = false;
            this.matchingTimer = null;
            this.currentOpponent = null;
            
            // 清理任何残留的匹配记录
            await this.forceCleanupMatch();
            
            // 加载角色信息
            await this.loadArenaInfo();
            
            // 绑定事件
            this.bindEvents();
            
            // 🔧 检查是否需要自动匹配
            const urlParams = new URLSearchParams(window.location.search);
            const autoMatch = urlParams.get('auto_match');
            if (autoMatch === '1') {
                console.log('🔄 [竞技系统] 检测到自动匹配参数，延迟启动匹配');
                setTimeout(() => {
                    this.startMatching();
                }, 1000); // 延迟1秒确保页面完全加载
            }
            
            console.log('🏆 [竞技系统] 初始化完成');
        } catch (error) {
            console.error('❌ [竞技系统] 初始化失败:', error);
            this.showMessage('系统初始化失败，请刷新页面重试', 'error');
        }
    }

    /**
     * 强制清理匹配状态（页面初始化时调用）
     */
    async forceCleanupMatch() {
        try {
            const response = await fetch(this.apiUrl, {
                method: 'POST',
                credentials: 'same-origin',
                headers: {
                    'Content-Type': 'application/json; charset=utf-8'
                },
                body: JSON.stringify({
                    action: 'cancel_matching'
                })
            });
            // 不需要检查结果，只是为了清理后端状态
        } catch (error) {
            // 忽略错误，这只是清理操作
        }
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 为全局函数提供引用
        window.startMatching = () => this.startMatching();
        window.openRecordsModal = () => this.openRecordsModal();
        window.closeRecordsModal = () => this.closeRecordsModal();
        window.openRankingsModal = () => this.openRankingsModal();
        window.closeRankingsModal = () => this.closeRankingsModal();
        window.openRulesModal = () => this.openRulesModal();
        window.closeRulesModal = () => this.closeRulesModal();
    }

    /**
     * 加载竞技场信息
     */
    async loadArenaInfo() {
        try {
            const response = await fetch(`${this.apiUrl}?action=get_arena_info`, {
                method: 'GET',
                credentials: 'same-origin',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            const data = await response.json();

            if (data.success) {
                this.updateArenaInfo(data.arena_info);
                console.log('🏆 [竞技系统] 角色信息加载成功');
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            console.error('❌ [竞技系统] 加载角色信息失败:', error);
            this.showMessage('加载角色信息失败', 'error');
        }
    }

    /**
     * 更新竞技场信息显示
     */
    updateArenaInfo(info) {
        // 更新角色基本信息
        const characterName = document.getElementById('characterName');
        const characterRealm = document.getElementById('characterRealm');
        const characterPower = document.getElementById('characterPower');
        const rankText = document.getElementById('currentRank');
        const rankProgress = document.getElementById('rankProgress');
        const remainingAttempts = document.getElementById('remainingAttempts');
        
        if (characterName) characterName.textContent = info.character_name || '未知角色';
        if (characterRealm) characterRealm.textContent = info.realm_name || '开光期';
        if (characterPower) characterPower.textContent = (info.dao_power || 1000).toLocaleString();
        
        // 更新段位信息
        if (rankText) {
            const rankTextElement = rankText.querySelector('.rank-text');
            if (rankTextElement) {
                rankTextElement.textContent = info.rank_name || '练气期';
            }
        }
        
        // 更新段位进度
        if (rankProgress) {
            const totalWins = info.total_wins || 0;
            const totalBattles = info.total_battles || 0;
            rankProgress.textContent = `${totalWins}胜/${totalBattles}战`;
        }
        
        // 🏆 更新段位积分信息
        const rankPoints = document.getElementById('rankPoints');
        const pointsNeeded = document.getElementById('pointsNeeded');
        
        if (rankPoints) {
            rankPoints.textContent = info.rank_points || 0;
        }
        
        if (pointsNeeded && info.next_rank) {
            pointsNeeded.textContent = `下一段位(${info.next_rank.rank_name})需要${info.next_rank.points_needed}积分`;
        } else if (pointsNeeded) {
            pointsNeeded.textContent = '已达最高段位';
        }
        
        // 更新挑战次数 - 使用API返回的准确数据
        const totalRemainingAttempts = info.total_remaining_attempts || 0;
        
        if (remainingAttempts) {
            remainingAttempts.textContent = totalRemainingAttempts;
        }
        
        // 更新今日战绩
        const todayWins = document.getElementById('todayWins');
        const todayLoses = document.getElementById('todayLoses');
        const todayRewards = document.getElementById('todayRewards');
        
        // 🔧 修复：使用正确的数据字段路径
        const todayStats = info.today_stats || {};
        if (todayWins) todayWins.textContent = todayStats.wins || 0;
        if (todayLoses) todayLoses.textContent = (todayStats.battles || 0) - (todayStats.wins || 0);
        if (todayRewards) todayRewards.textContent = todayStats.stones_earned || 0;
        
        // 更新角色头像
        this.updateCharacterAvatar(info.character_avatar);
        
        // 更新按钮状态
        this.updateButtonStates(totalRemainingAttempts > 0);
    }

    /**
     * 更新角色头像
     */
    updateCharacterAvatar(avatarImage) {
        const avatarContent = document.getElementById('characterAvatarContent');
        if (avatarContent && avatarImage) {
            avatarContent.style.backgroundImage = `url('assets/images/char/${avatarImage}')`;
            avatarContent.style.backgroundSize = 'cover';
            avatarContent.style.backgroundPosition = 'center 15%';
            avatarContent.style.backgroundRepeat = 'no-repeat';
            avatarContent.textContent = ''; // 清空文字
        }
    }

    /**
     * 更新按钮状态
     */
    updateButtonStates(hasAttempts) {
        const matchBtn = document.getElementById('matchBtn');
        
        if (matchBtn) {
            // 🔧 修复：只有正在匹配时才禁用按钮，没有次数时保持可点击
            if (this.isMatching) {
                matchBtn.disabled = true;
                matchBtn.classList.add('disabled');
            } else {
                matchBtn.disabled = false;
                matchBtn.classList.remove('disabled');
                
                // 🔧 根据次数状态更新按钮样式（但不禁用）
                if (hasAttempts) {
                    matchBtn.classList.remove('no-attempts');
            } else {
                    matchBtn.classList.add('no-attempts');
                }
            }
        }
    }

    /**
     * 开始匹配
     */
    async startMatching() {
        // 如果正在匹配，则取消匹配
        if (this.isMatching) {
            await this.cancelMatching();
            return;
        }
        
        // 🔧 修复：实时从后端获取最新挑战次数，而不是依赖前端显示
        try {
            const attemptsResponse = await fetch(`${this.apiUrl}?action=get_arena_info`, {
                method: 'GET',
                credentials: 'same-origin',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            const attemptsData = await attemptsResponse.json();
            
            if (attemptsData.success) {
                const actualRemainingAttempts = attemptsData.arena_info.total_remaining_attempts || 0;
                if (actualRemainingAttempts <= 0) {
                    console.log('🏆 [竞技系统] 实时检查：没有剩余次数，显示购买提示');
                    this.showAttemptsExhaustedModal();
                    return;
                }
                
                // 🔧 同步更新前端显示的次数
                const remainingAttemptsElement = document.getElementById('remainingAttempts');
                if (remainingAttemptsElement) {
                    remainingAttemptsElement.textContent = actualRemainingAttempts;
                }
            } else {
                throw new Error('获取挑战次数失败');
            }
        } catch (error) {
            console.error('❌ [竞技系统] 检查挑战次数失败:', error);
            this.showMessage('检查挑战次数失败，请刷新页面重试', 'error');
            return;
        }
        
        try {
            console.log('🏆 [竞技系统] 开始匹配');
            this.isMatching = true;
            
            // 更新匹配状态和按钮文字
            this.updateMatchStatus('正在寻找对手...');
            this.updateButtonText('取消匹配');
            
            const response = await fetch(this.apiUrl, {
                method: 'POST',
                credentials: 'same-origin',
                headers: {
                    'Content-Type': 'application/json; charset=utf-8'
                },
                body: JSON.stringify({
                    action: 'start_matching'
                })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.success) {
                if (data.match_found) {
                    // 找到对手，显示对手信息
                    this.currentOpponent = data.opponent;
                    this.showOpponentFound(data.opponent);
                } else {
                    // 继续匹配
                    this.continueMatching();
                }
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            console.error('❌ [竞技系统] 匹配失败:', error);
            this.showMessage('匹配失败：' + error.message, 'error');
            await this.cancelMatching();
        }
    }

    /**
     * 更新匹配状态
     */
    updateMatchStatus(message) {
        const matchStatus = document.getElementById('matchStatus');
        if (matchStatus) {
            matchStatus.textContent = message;
        }
    }

    /**
     * 更新按钮文字
     */
    updateButtonText(text) {
        const matchText = document.querySelector('.match-text');
        if (matchText) {
            matchText.textContent = text;
        }
    }

    /**
     * 继续匹配
     */
    continueMatching() {
        let seconds = 0;
        this.matchingTimer = setInterval(async () => {
            seconds++;
            
            // 🔧 优化匹配提示信息
            if (seconds <= 5) {
                this.updateMatchStatus(`🔍 寻找真实玩家... ${seconds}秒`);
            } else if (seconds <= 10) {
                this.updateMatchStatus(`🔍 扩大匹配范围... ${seconds}秒`);
            } else {
                this.updateMatchStatus(`⏰ 即将匹配灵智傀儡... ${seconds}秒`);
            }
            
            // 🔧 修改：缩短到15秒就匹配AI，快速匹配
            if (seconds >= 15) {
                clearInterval(this.matchingTimer);
                this.updateMatchStatus('💫 正在为您匹配灵智傀儡...');
                await this.matchAI();
                return;
            }
            
            // 每2秒检查匹配状态
            if (seconds % 2 === 0) {
                try {
                    const response = await fetch(this.apiUrl, {
                        method: 'POST',
                        credentials: 'same-origin',
                        headers: {
                            'Content-Type': 'application/json; charset=utf-8'
                        },
                        body: JSON.stringify({
                            action: 'get_match_status'
                        })
                    });
                    const data = await response.json();

                    
                    if (data.success) {
                        if (data.match_found) {
                            clearInterval(this.matchingTimer);
                            
                            // 🔧 修复：正确处理真实玩家匹配
                            const opponent = data.opponent;
                            const isRealPlayer = !opponent.is_ai_puppet && !opponent.is_ai;
                            
                            console.log('🎉 [匹配成功]', {
                                opponent: opponent,
                                isRealPlayer: isRealPlayer,
                                opponentType: isRealPlayer ? '真实玩家' : 'AI傀儡'
                            });
                            
                            this.currentOpponent = opponent;
                            this.showOpponentFound(opponent);
                        } else if (!data.in_queue && !data.match_found) {
                            // 🔧 修复：检查是否是超时，如果是超时则匹配AI
                            if (data.timeout) {
                                console.log('⏰ [匹配超时] 开始匹配AI对手');
                                clearInterval(this.matchingTimer);
                                // 匹配AI对手
                                await this.matchAI();
                            } else {
                                console.log('⚠️ [匹配] 不在队列且未找到对手，取消匹配');
                                clearInterval(this.matchingTimer);
                                await this.cancelMatching();
                            }
                        }
                        // 🔧 其他情况（仍在队列中）继续等待
                    } else {
                        // 🔧 新增：处理后端返回的错误（如挑战次数不足）
                        if (data.attempts_exhausted) {
                            console.log('❌ [匹配失败] 挑战次数不足，取消匹配');
                            clearInterval(this.matchingTimer);
                            await this.cancelMatching();
                            this.showAttemptsExhaustedModal();
                        } else {
                            console.error('❌ [匹配状态检查失败]', data.message);
                        }
                    }
                } catch (error) {
                    console.error('❌ [匹配] 状态检查失败:', error);
                }
            }
        }, 1000);
    }

    /**
     * 匹配AI对手
     */
    async matchAI() {
        try {
            this.updateMatchStatus('正在匹配灵智傀儡...');
            
            console.log('🏆 [竞技系统] 开始匹配AI对手');
            
            const response = await fetch(this.apiUrl, {
                method: 'POST',
                credentials: 'same-origin',
                headers: {
                    'Content-Type': 'application/json; charset=utf-8'
                },
                body: JSON.stringify({
                    action: 'generate_ai_opponent'
                })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status}`);
            }
            
            const data = await response.json();
            console.log('🏆 [竞技系统] AI匹配响应:', data);
            
            if (data.success && data.opponent) {
                this.currentOpponent = data.opponent;
                this.showOpponentFound(data.opponent);
            } else {
                // 🔧 新增：特殊处理挑战次数不足的情况
                if (data.attempts_exhausted) {
                    console.log('❌ [AI匹配失败] 挑战次数不足');
                    this.showAttemptsExhaustedModal();
                    return;
                }
                throw new Error(data.message || '生成AI对手失败');
            }
        } catch (error) {
            console.error('❌ [竞技系统] AI匹配失败:', error);
            this.showMessage('匹配失败：' + error.message, 'error');
            await this.cancelMatching();
        }
    }

    /**
     * 显示找到对手
     */
    showOpponentFound(opponent) {
        console.log('🏆 [竞技系统] 显示对手信息:', opponent);
        console.log('🔧 [详细调试] 完整对手数据:', JSON.stringify(opponent, null, 2));
        
        const modal = document.getElementById('matchResultModal');
        const opponentInfo = document.getElementById('opponentInfo');
        
        if (modal && opponentInfo) {
            // 🔧 使用项目的realm-system.js计算境界名称
            let realmName = '未知境界';
            
            // 优先使用数据库真实境界名称
            if (opponent.realm_name && opponent.realm_name !== 'null' && opponent.realm_name !== '') {
                realmName = opponent.realm_name;
                console.log('✅ [境界] 使用数据库真实境界名称:', realmName);
            }
            // 兜底方案：使用RealmSystem计算境界名称
            else if (typeof RealmSystem !== 'undefined' && opponent.realm_id) {
                const level = parseInt(opponent.realm_id) || 1;
                realmName = RealmSystem.getLevelRealm(level, true);
                console.log('✅ [境界] 使用RealmSystem计算:', level, '->', realmName);
            } 
            // 最后兜底：直接显示等级
            else if (opponent.level) {
                const level = parseInt(opponent.level) || 1;
                realmName = `境界${level}级`;
                console.log('⚠️ [境界] 使用等级兜底方案，level:', level, '->', realmName);
            }
            
            console.log('🔧 [境界最终] 对手境界信息:', {
                'realm_name': opponent.realm_name,
                'realm_id': opponent.realm_id,
                'level': opponent.level,
                '最终显示': realmName
            });
            
            // 填充对手信息
            // 🔧 修复：统一AI对手名称显示格式
            let displayName = opponent.character_name || opponent.name || '未知对手';
            if (opponent.is_ai_puppet || opponent.is_ai) {
                if (displayName === '灵智傀儡' || displayName === '未知对手') {
                    displayName = '灵智傀儡·玄天'; // 默认名称
                } else if (!displayName.includes('灵智傀儡')) {
                    displayName = '灵智傀儡·' + displayName;
                }
            }

            opponentInfo.innerHTML = `
                <div style="text-align: center; margin-bottom: 20px;">
                    <div style="font-size: 18px; color: #d4af37; margin-bottom: 10px;">
                        ${displayName}
                    </div>
                    <div style="font-size: 14px; color: #4CAF50; margin-bottom: 5px;">
                        ${realmName}
                    </div>
                    <div style="font-size: 12px; color: #bdc3c7;">
                        道行值: ${(opponent.dao_power || 0).toLocaleString()}
                    </div>
                    ${opponent.is_ai_puppet || opponent.is_ai ? '<div style="font-size: 10px; color: #e74c3c; margin-top: 5px;">AI对手</div>' : ''}
                </div>
            `;
            
            // 显示弹窗
            modal.classList.add('show');
            
            // 开始倒计时
            this.startBattleCountdown();
        }
    }

    /**
     * 开始战斗倒计时
     */
    startBattleCountdown() {
        const countdownNumber = document.getElementById('countdownNumber');
        let count = 3;
        
        const countdownTimer = setInterval(() => {
            if (countdownNumber) {
                countdownNumber.textContent = count;
            }
            
            count--;
            if (count < 0) {
                clearInterval(countdownTimer);
                this.startBattle();
            }
        }, 1000);
    }

    /**
     * 开始战斗
     */
    async startBattle() {
        try {
            // 关闭匹配结果弹窗
            const modal = document.getElementById('matchResultModal');
            if (modal) {
                modal.classList.remove('show');
            }
            
            // 构建战斗URL
            const battleUrl = this.buildBattleUrl(this.currentOpponent);
            
            console.log('🏆 [竞技系统] 跳转到战斗页面:', battleUrl);
            
            // 跳转到战斗页面
            window.location.href = battleUrl;
            
        } catch (error) {
            console.error('❌ [竞技系统] 开始战斗失败:', error);
            this.showMessage('开始战斗失败：' + error.message, 'error');
            this.cancelMatching();
        }
    }

    /**
     * 构建战斗URL
     */
    buildBattleUrl(opponentData) {
        // 🔧 修复：确保对手名称正确传递
        let opponentName = '';
        if (opponentData.character_name) {
            opponentName = opponentData.character_name;
        } else if (opponentData.name) {
            opponentName = opponentData.name;
        } else {
            opponentName = '未知对手';
        }
        
        // 🔧 修复：处理AI傀儡名称显示 - 统一格式
        if (opponentData.is_ai_puppet || opponentData.is_ai) {
            // 如果已经包含"灵智傀儡"，直接使用；否则添加前缀
            if (!opponentName.includes('灵智傀儡')) {
                opponentName = '灵智傀儡·' + opponentName;
            }
        }
        
        const params = new URLSearchParams({
            arena: '1',
            opponent_id: opponentData.character_id || 'ai_' + Date.now(),
            opponent_name: opponentName,
            opponent_power: opponentData.dao_power || 1000,
            is_ai: opponentData.is_ai_puppet || opponentData.is_ai ? '1' : '0',
            arena_battle_id: Date.now() // 用于标识这场战斗
        });
        
        console.log('🏆 [竞技系统] 构建战斗URL:', {
            opponent: opponentData,
            opponentName: opponentName,
            params: params.toString()
        });
        
        return `battle.html?${params.toString()}`;
    }

    /**
     * 取消匹配
     */
    async cancelMatching() {
        console.log('🏆 [竞技系统] 取消匹配');
        
        this.isMatching = false;
        if (this.matchingTimer) {
            clearInterval(this.matchingTimer);
            this.matchingTimer = null;
        }
        
        // 调用后端API取消匹配
        try {
            await fetch(this.apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json; charset=utf-8'
                },
                body: JSON.stringify({
                    action: 'cancel_matching'
                })
            });
        } catch (error) {
            console.error('❌ [竞技系统] 取消匹配API调用失败:', error);
        }
        
        this.updateMatchStatus('点击开始匹配');
        this.updateButtonText('寻找对手');
        this.updateButtonStates(true);
        
        // 关闭可能存在的匹配结果弹窗
        const modal = document.getElementById('matchResultModal');
        if (modal) {
            modal.classList.remove('show');
        }
    }



    /**
     * 打开战绩弹窗
     */
    async openRecordsModal() {
        const modal = document.getElementById('recordsModal');
        const recordsList = document.getElementById('recordsList');
        
        if (modal && recordsList) {
            modal.classList.add('show');
            recordsList.innerHTML = '<div class="loading">加载中...</div>';
            
            try {
                const response = await fetch(`${this.apiUrl}?action=get_battle_records`, {
                    method: 'GET',
                    credentials: 'same-origin',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                const data = await response.json();
                
                if (data.success) {
                    this.displayBattleRecords(data.records);
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                console.error('❌ [战绩] 加载失败:', error);
                recordsList.innerHTML = '<div class="loading">加载失败</div>';
            }
        }
    }

    /**
     * 显示战斗记录
     */
    displayBattleRecords(records) {
        const recordsList = document.getElementById('recordsList');
        if (!recordsList) return;
        
        if (!records || records.length === 0) {
            recordsList.innerHTML = '<div class="loading">暂无战斗记录</div>';
            return;
        }
        
        const recordsHtml = records.map(record => {
            const isAiPuppet = record.is_ai_puppet == 1; // 🔧 修复：数字比较

            // 🔧 修复：统一AI对手名称显示格式
            let opponentName = record.opponent_name || '未知对手';
            if (isAiPuppet) {
                // 确保AI对手名称统一显示为"灵智傀儡·[名称]"格式
                if (opponentName === '灵智傀儡' || opponentName === '未知对手') {
                    opponentName = '灵智傀儡·玄天'; // 默认名称
                } else if (!opponentName.includes('灵智傀儡')) {
                    opponentName = '灵智傀儡·' + opponentName;
                }
            }

            const opponentDisplay = isAiPuppet ? `🤖 ${opponentName}` : `👤 ${opponentName}`;
            const rankPointsChange = parseInt(record.rank_points_change) || 0; // 🔧 确保为数字
            
            return `
            <div class="record-item">
                <div class="record-opponent">
                    ${opponentDisplay}
                    <div class="record-details">
                        道行: ${(record.opponent_dao_power || 0).toLocaleString()}
                        ${rankPointsChange !== 0 ? ` | 积分: ${rankPointsChange >= 0 ? '+' : ''}${rankPointsChange}` : ''}
                    </div>
                </div>
                <div class="record-result ${record.battle_result}">
                    ${record.battle_result === 'win' ? '胜' : '败'}
                </div>
                <div class="record-reward">+${record.spirit_stone_reward || 0}灵石</div>
                <div class="record-time">${this.formatTime(record.created_at)}</div>
            </div>
            `;
        }).join('');
        
        recordsList.innerHTML = recordsHtml;
    }

    /**
     * 关闭战绩弹窗
     */
    closeRecordsModal() {
        const modal = document.getElementById('recordsModal');
        if (modal) {
            modal.classList.remove('show');
        }
    }

    /**
     * 打开排行榜弹窗
     */
    async openRankingsModal() {
        const modal = document.getElementById('rankingsModal');
        const rankingsList = document.getElementById('rankingsList');
        
        if (modal && rankingsList) {
            modal.classList.add('show');
            rankingsList.innerHTML = '<div class="loading">加载中...</div>';
            
            try {
                const response = await fetch(`${this.apiUrl}?action=get_arena_rankings`, {
                    method: 'GET',
                    credentials: 'same-origin',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                const data = await response.json();
                
                if (data.success) {
                    this.displayRankings(data.rankings);
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                console.error('❌ [排行榜] 加载失败:', error);
                rankingsList.innerHTML = '<div class="loading">加载失败</div>';
            }
        }
    }

    /**
     * 显示排行榜
     */
    displayRankings(rankings) {
        const rankingsList = document.getElementById('rankingsList');
        if (!rankingsList) return;
        
        if (!rankings || rankings.length === 0) {
            rankingsList.innerHTML = '<div class="loading">暂无排行数据</div>';
            return;
        }
        
        const rankingsHtml = rankings.map((player, index) => {
            const winRate = player.win_rate || 0;
            const totalBattles = player.arena_total_battles || 0;
            return `
            <div class="ranking-item">
                <div class="ranking-position">${index + 1}</div>
                <div class="ranking-name">
                    ${player.character_name}
                    <div class="ranking-stats">${totalBattles}战 ${winRate}%胜率</div>
                </div>
                <div class="ranking-power">${(player.arena_dao_power || 0).toLocaleString()}</div>
                <div class="ranking-rank">${player.rank_name || '练气期'}</div>
            </div>
            `;
        }).join('');
        
        rankingsList.innerHTML = rankingsHtml;
    }

    /**
     * 关闭排行榜弹窗
     */
    closeRankingsModal() {
        const modal = document.getElementById('rankingsModal');
        if (modal) {
            modal.classList.remove('show');
        }
    }

    /**
     * 打开规则弹窗
     */
    openRulesModal() {
        const modal = document.getElementById('rulesModal');
        if (modal) {
            modal.classList.add('show');
        }
    }

    /**
     * 关闭规则弹窗
     */
    closeRulesModal() {
        const modal = document.getElementById('rulesModal');
        if (modal) {
            modal.classList.remove('show');
        }
    }

    /**
     * 格式化时间
     */
    formatTime(timeString) {
        const date = new Date(timeString);
        const now = new Date();
        const diffTime = now - date;
        const diffHours = Math.floor(diffTime / (1000 * 60 * 60));
        
        if (diffHours < 1) {
            const diffMinutes = Math.floor(diffTime / (1000 * 60));
            return `${diffMinutes}分钟前`;
        } else if (diffHours < 24) {
            return `${diffHours}小时前`;
        } else {
            return date.toLocaleDateString();
        }
    }

    /**
     * 显示消息
     */
    showMessage(message, type = 'info') {
        // 特殊处理挑战次数用完的消息
        if (message.includes('今日挑战次数已用完') || message.includes('挑战次数已用完')) {
            this.showAttemptsExhaustedModal();
            return;
        }
        
        // 其他消息的处理
        if (type === 'error') {
            console.error('❌ ' + message);
            this.showCustomAlert(message, 'error');
        } else if (type === 'success') {
            console.log('✅ ' + message);
            this.showCustomAlert(message, 'success');
        } else {
            console.log('ℹ️ ' + message);
            this.showCustomAlert(message, 'info');
        }
    }

    /**
     * 显示挑战次数用完的美观弹窗
     */
    showAttemptsExhaustedModal() {
        // 创建弹窗元素
        const modal = document.createElement('div');
        modal.className = 'attempts-exhausted-modal';
        modal.innerHTML = `
            <div class="attempts-exhausted-overlay"></div>
            <div class="attempts-exhausted-content">
                <div class="attempts-exhausted-header">
                    <div class="attempts-exhausted-icon">🗡️</div>
                    <h3>论道次数已尽</h3>
                </div>
                <div class="attempts-exhausted-body">
                    <p>道友，今日的论道次数已经用完。</p>
                    <p>明日卯时三刻，论道次数将重新恢复。</p>
                    <div class="attempts-exhausted-options">
                        <p class="purchase-hint">
                            <span class="highlight">💎 可用灵石购买额外论道次数</span>
                        </p>
                        <p class="time-hint">
                            或待明日重新挑战群雄
                        </p>
                    </div>
                </div>
                                 <div class="attempts-exhausted-actions">
                     <button class="btn-purchase-attempts" onclick="immortalArena.directPurchaseAttempt();">
                         💰 直接购买 (500灵石)
                     </button>
                     <button class="btn-close-modal" onclick="immortalArena.closeTempModal();">
                         🚪 离开
                     </button>
                 </div>
            </div>
        `;

        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            .attempts-exhausted-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 9999;
                display: flex;
                align-items: center;
                justify-content: center;
                animation: fadeIn 0.3s ease-out;
            }

            .attempts-exhausted-overlay {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                backdrop-filter: blur(3px);
            }

            .attempts-exhausted-content {
                position: relative;
                background: linear-gradient(135deg, #2c1810 0%, #4a2c20 50%, #2c1810 100%);
                border: 2px solid #d4af37;
                border-radius: 15px;
                box-shadow: 0 10px 30px rgba(212, 175, 55, 0.3);
                max-width: 400px;
                width: 90%;
                padding: 0;
                animation: slideInUp 0.4s ease-out;
                overflow: hidden;
            }

            .attempts-exhausted-header {
                background: linear-gradient(135deg, #d4af37, #f4e887);
                color: #2c1810;
                padding: 20px;
                text-align: center;
                border-bottom: 1px solid #d4af37;
            }

            .attempts-exhausted-icon {
                font-size: 24px;
                margin-bottom: 8px;
            }

            .attempts-exhausted-header h3 {
                margin: 0;
                font-size: 18px;
                font-weight: bold;
                text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
            }

            .attempts-exhausted-body {
                padding: 25px;
                color: #e8dcc6;
                text-align: center;
                line-height: 1.6;
            }

            .attempts-exhausted-body p {
                margin: 0 0 15px 0;
                font-size: 14px;
            }

            .attempts-exhausted-options {
                margin-top: 20px;
                padding: 15px;
                background: rgba(212, 175, 55, 0.1);
                border-radius: 8px;
                border: 1px solid rgba(212, 175, 55, 0.3);
            }

            .purchase-hint {
                color: #d4af37 !important;
                font-weight: bold;
                margin-bottom: 8px !important;
            }

            .highlight {
                background: linear-gradient(90deg, #d4af37, #f4e887);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
                font-weight: bold;
            }

            .time-hint {
                color: #bdc3c7 !important;
                font-size: 12px !important;
                font-style: italic;
            }

            .attempts-exhausted-actions {
                padding: 20px 25px 25px;
                display: flex;
                gap: 15px;
                justify-content: center;
            }

            .attempts-exhausted-actions button {
                flex: 1;
                padding: 12px 20px;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                cursor: pointer;
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;
            }

            .btn-purchase-attempts {
                background: linear-gradient(135deg, #d4af37, #f4e887);
                color: #2c1810;
                box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
            }

            .btn-purchase-attempts:hover {
                background: linear-gradient(135deg, #f4e887, #d4af37);
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(212, 175, 55, 0.5);
            }

            .btn-close-modal {
                background: linear-gradient(135deg, #6c5ce7, #a29bfe);
                color: white;
                box-shadow: 0 4px 15px rgba(108, 92, 231, 0.3);
            }

            .btn-close-modal:hover {
                background: linear-gradient(135deg, #a29bfe, #6c5ce7);
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(108, 92, 231, 0.5);
            }

            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }

            @keyframes slideInUp {
                from {
                    opacity: 0;
                    transform: translateY(50px) scale(0.8);
                }
                to {
                    opacity: 1;
                    transform: translateY(0) scale(1);
                }
            }

            /* 移动端适配 */
            @media (max-width: 480px) {
                .attempts-exhausted-content {
                    width: 95%;
                    margin: 10px;
                }

                .attempts-exhausted-actions {
                    flex-direction: column;
                }

                .attempts-exhausted-actions button {
                    width: 100%;
                }
            }
        `;

        // 添加样式和弹窗到页面
        document.head.appendChild(style);
        document.body.appendChild(modal);

        // 保存引用以便关闭
        this.tempModal = modal;
        this.tempStyle = style;

        // 点击遮罩关闭
        modal.querySelector('.attempts-exhausted-overlay').addEventListener('click', () => {
            this.closeTempModal();
        });
    }

    /**
     * 关闭临时弹窗
     */
    closeTempModal() {
        if (this.tempModal) {
            this.tempModal.remove();
            this.tempModal = null;
        }
        if (this.tempStyle) {
            this.tempStyle.remove();
            this.tempStyle = null;
        }
    }

    /**
     * 🔧 新增：直接购买挑战次数（从弹窗中）
     */
    async directPurchaseAttempt() {
        try {
            // 关闭当前弹窗
            this.closeTempModal();
            
            // 显示购买中提示
            this.showCustomAlert('正在购买挑战次数...', 'info');
            
            const response = await fetch(this.apiUrl, {
                method: 'POST',
                credentials: 'same-origin',
                headers: {
                    'Content-Type': 'application/json; charset=utf-8',
                },
                body: JSON.stringify({
                    action: 'purchase_attempts',
                    quantity: 1
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showMessage('论道次数已增加！道友可继续挑战群雄了！', 'success');
                await this.loadArenaInfo(); // 重新加载信息
                
                // 购买成功后自动开始匹配
                setTimeout(() => {
                    this.startMatching();
                }, 1000);
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            console.error('❌ [直接购买] 购买失败:', error);
            this.showMessage('购买失败：' + error.message, 'error');
        }
    }

    /**
     * 显示自定义提示
     */
    showCustomAlert(message, type = 'info') {
        // 创建仙侠风格的提示
        const alertDiv = document.createElement('div');
        alertDiv.className = `custom-alert alert-${type}`;
        
        // 根据类型设置图标和颜色
        let icon, bgColor, borderColor;
        switch(type) {
            case 'success':
                icon = '✨';
                bgColor = 'linear-gradient(135deg, #d4af37, #f4e887)';
                borderColor = '#d4af37';
                break;
            case 'error':
                icon = '⚠️';
                bgColor = 'linear-gradient(135deg, #e74c3c, #c0392b)';
                borderColor = '#e74c3c';
                break;
            default:
                icon = 'ℹ️';
                bgColor = 'linear-gradient(135deg, #3498db, #2980b9)';
                borderColor = '#3498db';
        }
        
        alertDiv.innerHTML = `
            <div class="alert-icon">${icon}</div>
            <div class="alert-content">${message}</div>
        `;
        
        alertDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${bgColor};
            color: ${type === 'success' ? '#2c1810' : 'white'};
            padding: 15px 20px;
            border-radius: 10px;
            border: 2px solid ${borderColor};
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
            z-index: 9999;
            animation: slideInRight 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            max-width: 350px;
            min-width: 200px;
            font-size: 14px;
            line-height: 1.5;
            font-weight: ${type === 'success' ? 'bold' : 'normal'};
            display: flex;
            align-items: center;
            gap: 10px;
            backdrop-filter: blur(5px);
        `;

        // 添加动画和特效样式
        const alertStyle = document.createElement('style');
        alertStyle.textContent = `
            @keyframes slideInRight {
                from {
                    opacity: 0;
                    transform: translateX(100%) scale(0.8) rotate(5deg);
                }
                50% {
                    transform: translateX(-10px) scale(1.05) rotate(-1deg);
                }
                to {
                    opacity: 1;
                    transform: translateX(0) scale(1) rotate(0deg);
                }
            }
            
            @keyframes fadeOutUp {
                to {
                    opacity: 0;
                    transform: translateY(-20px) scale(0.9);
                }
            }
            
            .alert-icon {
                font-size: 18px;
                flex-shrink: 0;
                animation: pulse 2s infinite;
            }
            
            .alert-content {
                flex: 1;
            }
            
            @keyframes pulse {
                0%, 100% {
                    transform: scale(1);
                }
                50% {
                    transform: scale(1.1);
                }
            }
        `;
        document.head.appendChild(alertStyle);
        document.body.appendChild(alertDiv);

        // 4秒后开始消失动画
        setTimeout(() => {
            alertDiv.style.animation = 'fadeOutUp 0.3s ease-out forwards';
            setTimeout(() => {
                alertDiv.remove();
                alertStyle.remove();
            }, 300);
        }, 4000);
        
        // 点击可手动关闭
        alertDiv.addEventListener('click', () => {
            alertDiv.style.animation = 'fadeOutUp 0.2s ease-out forwards';
            setTimeout(() => {
                alertDiv.remove();
                alertStyle.remove();
            }, 200);
        });
        
        alertDiv.style.cursor = 'pointer';
    }
}

// 全局实例
window.immortalArena = new ImmortalArena();

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('🏆 [竞技系统] 页面加载完成');
    
    if (window.immortalArena) {
        window.immortalArena.init();
    } else {
        console.error('❌ [竞技系统] ImmortalArena 对象未找到');
    }
}); 