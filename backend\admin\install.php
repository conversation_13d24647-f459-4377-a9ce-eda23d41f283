<?php
// 显示所有错误
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 数据库连接配置 - 使用主游戏数据库
$db_config = [
    'host' => 'localhost',
    'username' => 'ynxx',
    'password' => 'mjlxz159',
    'database' => 'yn_game'
];

try {
    // 连接MySQL（不指定数据库）
    $conn = new PDO(
        "mysql:host={$db_config['host']};charset=utf8mb4",
        $db_config['username'],
        $db_config['password']
    );
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 选择数据库
    $conn->exec("USE {$db_config['database']}");
    echo "连接数据库成功<br>";
    
    // 创建管理员表
    $conn->exec("CREATE TABLE IF NOT EXISTS admin_users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        role ENUM('admin', 'super_admin') NOT NULL DEFAULT 'admin',
        last_login DATETIME,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB");
    echo "管理员表创建成功<br>";
    
    // 创建日志表
    $conn->exec("CREATE TABLE IF NOT EXISTS admin_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        admin_id INT NOT NULL,
        action VARCHAR(50) NOT NULL,
        target_type VARCHAR(50) NOT NULL,
        target_id INT NOT NULL,
        details TEXT,
        ip VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB");
    echo "日志表创建成功<br>";
    
    // 创建游戏物品表
    $conn->exec("CREATE TABLE IF NOT EXISTS game_items (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        type VARCHAR(50) NOT NULL,
        description TEXT,
        properties JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB");
    echo "游戏物品表创建成功<br>";
    
    // 创建地图表
    $conn->exec("CREATE TABLE IF NOT EXISTS game_maps (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        level_required INT NOT NULL DEFAULT 1,
        description TEXT,
        properties JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB");
    echo "地图表创建成功<br>";
    
    // 创建怪物表
    $conn->exec("CREATE TABLE IF NOT EXISTS game_monsters (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        level INT NOT NULL DEFAULT 1,
        hp INT NOT NULL,
        mp INT NOT NULL,
        attack INT NOT NULL,
        defense INT NOT NULL,
        properties JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB");
    echo "怪物表创建成功<br>";
    
    // 创建掉落表
    $conn->exec("CREATE TABLE IF NOT EXISTS game_drops (
        id INT AUTO_INCREMENT PRIMARY KEY,
        monster_id INT NOT NULL,
        item_id INT NOT NULL,
        probability DECIMAL(5,2) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB");
    echo "掉落表创建成功<br>";
    
    // 创建默认管理员账号
    $stmt = $conn->prepare("INSERT IGNORE INTO admin_users (username, password, role) VALUES (?, ?, ?)");
    $defaultPassword = password_hash('admin123', PASSWORD_DEFAULT);
    $stmt->execute(['admin', $defaultPassword, 'super_admin']);
    echo "默认管理员账号创建成功<br>";
    echo "<strong>登录信息：</strong><br>";
    echo "用户名: admin<br>";
    echo "密码: admin123<br><br>";
    
    echo "<div style='margin-top: 20px; padding: 20px; border: 1px solid #ddd; border-radius: 5px; background: #f9f9f9;'>";
    echo "<h3>管理后台已安装完成！</h3>";
    echo "<p>请访问：<a href='login.html' target='_blank'>管理后台登录页面</a></p>";
    echo "<p>现在您可以管理以下数据：</p>";
    echo "<ul>";
    echo "<li>✅ 用户管理 - 玩家账号和属性</li>";
    echo "<li>✅ 物品管理 - 游戏道具和装备</li>";
    echo "<li>✅ 技能管理 - 修仙技能系统</li>";
    echo "<li>✅ 宠物管理 - 灵兽伙伴</li>";
    echo "<li>✅ 地图管理 - 修仙世界</li>";
    echo "<li>✅ 怪物管理 - 妖魔鬼怪</li>";
    echo "<li>✅ 任务管理 - 修仙任务</li>";
    echo "<li>✅ 商店管理 - 交易系统</li>";
    echo "<li>✅ 背包管理 - 玩家背包</li>";
    echo "<li>✅ 战斗日志 - 战斗记录</li>";
    echo "</ul>";
    echo "</div>";
    
} catch(PDOException $e) {
    die("数据库操作失败: " . $e->getMessage());
} 