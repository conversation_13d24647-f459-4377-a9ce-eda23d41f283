<?php
session_start();
echo "开始测试套装特殊效果系统...\n";

// 数据库连接
$host = 'localhost';
$dbname = 'yn_game';
$username = 'ynxx';
$password = 'mjlxz159';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ 数据库连接成功\n";
} catch (PDOException $e) {
    die("❌ 数据库连接失败: " . $e->getMessage() . "\n");
}

// 获取测试用户
$stmt = $pdo->query("SELECT c.id as character_id, c.user_id, u.username FROM characters c JOIN users u ON c.user_id = u.id LIMIT 1");
$testData = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$testData) {
    die("❌ 没有找到角色数据\n");
}

$character_id = $testData['character_id'];
$user_id = $testData['user_id'];
$_SESSION['user_id'] = $user_id; // 设置正确的用户ID用于API测试

echo "测试角色: 角色ID {$character_id}, 用户ID {$user_id}, 用户名: {$testData['username']}\n";

// 1. 检查game_item_sets表结构
echo "\n=== 1. 检查game_item_sets表结构 ===\n";
$stmt = $pdo->query("DESCRIBE game_item_sets");
$columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
foreach ($columns as $col) {
    echo "  - {$col['Field']} ({$col['Type']})\n";
}

// 2. 检查套装数据
echo "\n=== 2. 检查套装数据 ===\n";
$stmt = $pdo->query("SELECT * FROM game_item_sets LIMIT 5");
$sets = $stmt->fetchAll(PDO::FETCH_ASSOC);
if ($sets) {
    echo "找到 " . count($sets) . " 个套装:\n";
    foreach ($sets as $set) {
        echo "  - ID: {$set['id']}, 名称: {$set['set_name']}\n";
        if (isset($set['special_effects_2']) && $set['special_effects_2']) {
            echo "    2件套效果: {$set['special_effects_2']}\n";
        }
        if (isset($set['special_effects_4']) && $set['special_effects_4']) {
            echo "    4件套效果: {$set['special_effects_4']}\n";
        }
    }
} else {
    echo "❌ 没有找到套装数据\n";
}

// 3. 为测试角色添加套装装备
echo "\n=== 3. 为测试角色添加套装装备 ===\n";

// 首先检查角色当前装备
$stmt = $pdo->prepare("SELECT COUNT(*) as count FROM character_equipment WHERE character_id = ?");
$stmt->execute([$character_id]);
$equipCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
echo "角色当前装备数量: $equipCount\n";

// 获取第一个套装的装备
$stmt = $pdo->query("SELECT gi.id, gi.item_name, gi.set_id, gi.slot_type FROM game_items gi WHERE gi.set_id IS NOT NULL AND gi.set_id > 0 LIMIT 4");
$setItems = $stmt->fetchAll(PDO::FETCH_ASSOC);

if ($setItems) {
    echo "找到套装装备，为角色装备:\n";
    
    // 清除角色现有装备
    $stmt = $pdo->prepare("DELETE FROM character_equipment WHERE character_id = ?");
    $stmt->execute([$character_id]);
    
    // 装备套装物品
    foreach ($setItems as $item) {
        $stmt = $pdo->prepare("INSERT INTO character_equipment (character_id, item_id, slot_type, equipped_at) VALUES (?, ?, ?, NOW())");
        $stmt->execute([$character_id, $item['id'], $item['slot_type']]);
        echo "  - 装备: {$item['item_name']} (套装ID: {$item['set_id']}, 槽位: {$item['slot_type']})\n";
    }
} else {
    echo "❌ 没有找到套装装备\n";
}

// 4. 测试套装特殊效果函数
echo "\n=== 4. 测试套装特殊效果函数 ===\n";
require_once 'src/includes/functions.php';

$setBonus = getCharacterSetBonus($pdo, $character_id);

echo "套装加成函数返回结果:\n";
print_r($setBonus);

if (isset($setBonus['special_effects']) && !empty($setBonus['special_effects'])) {
    echo "✅ 套装特殊效果函数正常，找到 " . count($setBonus['special_effects']) . " 个效果:\n";
    foreach ($setBonus['special_effects'] as $effect) {
        echo "  - {$effect['set_name']} ({$effect['pieces']}件套): {$effect['effect']}\n";
    }
} else {
    echo "❌ 套装特殊效果函数未返回数据\n";
}

// 5. 测试cultivation.php API
echo "\n=== 5. 测试cultivation.php API ===\n";
$_POST['action'] = 'get_attributes';
ob_start();
include 'src/api/cultivation.php';
$apiResponse = ob_get_clean();

echo "API响应: $apiResponse\n";

$apiData = json_decode($apiResponse, true);
if ($apiData && $apiData['success']) {
    if (isset($apiData['attributes']['set_special_effects']) && !empty($apiData['attributes']['set_special_effects'])) {
        echo "✅ cultivation.php API 正确返回套装特殊效果数据:\n";
        foreach ($apiData['attributes']['set_special_effects'] as $effect) {
            echo "  - {$effect['set_name']} ({$effect['pieces']}件套): {$effect['effect']}\n";
        }
    } else {
        echo "❌ cultivation.php API 未返回套装特殊效果数据\n";
        if (isset($apiData['attributes'])) {
            echo "API返回的属性字段: " . implode(', ', array_keys($apiData['attributes'])) . "\n";
        }
    }
} else {
    echo "❌ cultivation.php API 调用失败\n";
}

// 6. 测试battle_unified.php API
echo "\n=== 6. 测试battle_unified.php API ===\n";
$_GET['action'] = 'init_battle';
$_GET['map_id'] = '1';
$_GET['map_code'] = 'qingshan';
$_GET['stage_number'] = '1';

ob_start();
include 'src/api/battle_unified.php';
$battleResponse = ob_get_clean();

echo "Battle API响应长度: " . strlen($battleResponse) . " 字符\n";

$battleData = json_decode($battleResponse, true);
if ($battleData && $battleData['success']) {
    if (isset($battleData['data']['player_data']['set_special_effects']) && !empty($battleData['data']['player_data']['set_special_effects'])) {
        echo "✅ battle_unified.php API 正确返回套装特殊效果数据:\n";
        foreach ($battleData['data']['player_data']['set_special_effects'] as $effect) {
            echo "  - {$effect['set_name']} ({$effect['pieces']}件套): {$effect['effect']}\n";
        }
    } else {
        echo "❌ battle_unified.php API 未返回套装特殊效果数据\n";
        if (isset($battleData['data']['player_data'])) {
            echo "Player data字段: " . implode(', ', array_keys($battleData['data']['player_data'])) . "\n";
        }
    }
} else {
    echo "❌ battle_unified.php API 调用失败\n";
    if ($battleData) {
        echo "错误信息: " . ($battleData['message'] ?? '未知错误') . "\n";
    } else {
        echo "响应解析失败，前100字符: " . substr($battleResponse, 0, 100) . "\n";
    }
}

echo "\n=== 套装特殊效果系统测试完成 ===\n";
