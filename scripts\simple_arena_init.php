<?php
/**
 * 简化版升仙大会数据库初始化脚本
 */

// 数据库配置
$host = 'localhost';
$dbname = 'yn_game';
$username = 'ynxx';
$password = 'mjlxz159';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "连接数据库成功\n";
    
    // 1. 创建竞技记录表
    $sql1 = "CREATE TABLE IF NOT EXISTS `immortal_arena_records` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `character_id` int(11) NOT NULL,
        `opponent_character_id` int(11) DEFAULT NULL,
        `opponent_name` varchar(50) NOT NULL,
        `opponent_dao_power` int(11) NOT NULL,
        `is_ai_puppet` tinyint(1) DEFAULT 0,
        `ai_template_id` int(11) DEFAULT NULL,
        `battle_result` enum('win','lose','draw') NOT NULL,
        `dao_power_before` int(11) NOT NULL,
        `dao_power_after` int(11) NOT NULL,
        `dao_power_change` int(11) NOT NULL DEFAULT 0,
        `spirit_stone_reward` int(11) NOT NULL DEFAULT 0,
        `battle_duration` int(11) DEFAULT NULL,
        `battle_rounds` int(11) DEFAULT NULL,
        `battle_snapshot` text DEFAULT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    
    $pdo->exec($sql1);
    echo "✅ 创建表 immortal_arena_records\n";
    
    // 2. 创建匹配池表
    $sql2 = "CREATE TABLE IF NOT EXISTS `immortal_arena_match_pool` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `character_id` int(11) NOT NULL,
        `dao_power` int(11) NOT NULL,
        `realm_level` int(11) NOT NULL,
        `character_snapshot` text NOT NULL,
        `match_timeout` timestamp NOT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    
    $pdo->exec($sql2);
    echo "✅ 创建表 immortal_arena_match_pool\n";
    
    // 3. 创建段位表
    $sql3 = "CREATE TABLE IF NOT EXISTS `immortal_arena_ranks` (
        `rank_level` int(11) NOT NULL,
        `rank_name` varchar(20) NOT NULL,
        `min_dao_power` int(11) NOT NULL,
        `max_dao_power` int(11) DEFAULT NULL,
        `rank_color` varchar(7) DEFAULT '#FFFFFF',
        `reward_multiplier` decimal(3,2) DEFAULT 1.00,
        PRIMARY KEY (`rank_level`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    
    $pdo->exec($sql3);
    echo "✅ 创建表 immortal_arena_ranks\n";
    
    // 4. 检查characters表是否有arena字段
    $stmt = $pdo->query("SHOW COLUMNS FROM characters LIKE 'arena_%'");
    if ($stmt->rowCount() == 0) {
        echo "开始添加characters表字段...\n";
        
        $alterSqls = [
            "ALTER TABLE `characters` ADD COLUMN `arena_dao_power` int(11) DEFAULT 0 AFTER `spiritual_root_usage`",
            "ALTER TABLE `characters` ADD COLUMN `arena_daily_attempts` int(11) DEFAULT 0 AFTER `arena_dao_power`",
            "ALTER TABLE `characters` ADD COLUMN `arena_purchased_attempts` int(11) DEFAULT 0 AFTER `arena_daily_attempts`",
            "ALTER TABLE `characters` ADD COLUMN `arena_last_reset` date DEFAULT NULL AFTER `arena_purchased_attempts`",
            "ALTER TABLE `characters` ADD COLUMN `arena_rank_level` int(11) DEFAULT 1 AFTER `arena_last_reset`",
            "ALTER TABLE `characters` ADD COLUMN `arena_total_wins` int(11) DEFAULT 0 AFTER `arena_rank_level`",
            "ALTER TABLE `characters` ADD COLUMN `arena_total_battles` int(11) DEFAULT 0 AFTER `arena_total_wins`",
            "ALTER TABLE `characters` ADD COLUMN `arena_win_streak` int(11) DEFAULT 0 AFTER `arena_total_battles`",
            "ALTER TABLE `characters` ADD COLUMN `arena_best_streak` int(11) DEFAULT 0 AFTER `arena_win_streak`",
            "ALTER TABLE `characters` ADD COLUMN `arena_skill_sequence` varchar(50) DEFAULT '0,1,2,3,4,5' AFTER `arena_best_streak`"
        ];
        
        foreach ($alterSqls as $sql) {
            try {
                $pdo->exec($sql);
                echo "✅ 添加字段成功\n";
            } catch (Exception $e) {
                echo "⚠️ 字段可能已存在: " . substr($e->getMessage(), 0, 50) . "\n";
            }
        }
    } else {
        echo "characters表arena字段已存在\n";
    }
    
    // 5. 插入段位配置
    $stmt = $pdo->query("SELECT COUNT(*) FROM immortal_arena_ranks");
    if ($stmt->fetchColumn() == 0) {
        echo "插入段位配置数据...\n";
        
        $ranks = [
            [1, '练气期', 0, 1999, '#FFFFFF', 1.00],
            [2, '筑基期', 2000, 3999, '#C0C0C0', 1.10],
            [3, '结丹期', 4000, 5999, '#FFB366', 1.20],
            [4, '元婴期', 6000, 7999, '#66B3FF', 1.30],
            [5, '化神期', 8000, 9999, '#B366FF', 1.40],
            [6, '合体期', 10000, 12999, '#FF6B6B', 1.50],
            [7, '大乘期', 13000, 15999, '#FFD700', 1.75],
            [8, '渡劫期', 16000, 19999, '#FF4500', 2.00],
            [9, '仙人境', 20000, 29999, '#00FFFF', 2.50],
            [10, '仙君境', 30000, null, '#FF1493', 3.00]
        ];
        
        $insertSql = "INSERT INTO immortal_arena_ranks VALUES (?, ?, ?, ?, ?, ?)";
        $stmt = $pdo->prepare($insertSql);
        
        foreach ($ranks as $rank) {
            $stmt->execute($rank);
        }
        echo "✅ 段位配置数据插入完成\n";
    } else {
        echo "段位配置数据已存在\n";
    }
    
    echo "\n🎉 升仙大会竞技系统数据库初始化完成！\n";
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}
?> 