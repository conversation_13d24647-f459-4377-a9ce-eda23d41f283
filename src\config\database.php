<?php
// 数据库配置 - 避免重复定义
if (!defined('DB_HOST')) {
    define('DB_HOST', 'localhost');
}
if (!defined('DB_NAME')) {
    define('DB_NAME', 'yn_game');
}
if (!defined('DB_USER')) {
    define('DB_USER', 'ynxx');
}
if (!defined('DB_PASS')) {
    define('DB_PASS', 'mjlxz159');
}
if (!defined('DB_CHARSET')) {
    define('DB_CHARSET', 'utf8mb4');
}

// Database类
class Database {
    private $connection = null;
    
    public function getConnection() {
        if ($this->connection === null) {
            try {
                $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
                $this->connection = new PDO($dsn, DB_USER, DB_PASS);
                $this->connection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                $this->connection->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
            } catch(PDOException $e) {
                error_log("数据库连接失败: " . $e->getMessage());
                return false;
            }
        }
        return $this->connection;
    }
}

// 创建数据库连接
function getDatabase() {
    try {
        $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
        $pdo = new PDO($dsn, DB_USER, DB_PASS);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        return $pdo;
    } catch(PDOException $e) {
        error_log("数据库连接失败: " . $e->getMessage());
        return false;
    }
}

// 检查数据库连接
function checkDatabaseConnection() {
    try {
        $pdo = getDatabase();
        $result = $pdo->query("SELECT 1")->fetch();
        return ($result && isset($result[1]) && $result[1] == 1);
    } catch(PDOException $e) {
        error_log("数据库连接检查失败: " . $e->getMessage());
        return false;
    }
}
?> 