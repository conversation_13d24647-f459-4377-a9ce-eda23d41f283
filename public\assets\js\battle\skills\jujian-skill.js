/**
 * 巨剑术技能模块
 * 对应 animation_model = 'jujian'
 */

/**
 * 巨剑术技能 - 旋转残影版本
 */
class JuJianSkill extends BaseSkill {
    async execute(skillData, weaponImage) {
        // 使用数据库中的真实技能名称，而不是硬编码
        const skillName = skillData?.skillName || skillData?.displayName || '巨剑术'; // 提供默认值作为后备
        await this.showSkillShout(skillName);
        // 🔧 修复：根据技能使用者动态确定攻击方向
        // 如果是敌方技能，则isPlayer应该为false；反之为true
        const isPlayerAttack = !this.isEnemySkill;
        await this.createRotatingGiantSword(isPlayerAttack, skillData, weaponImage);
    }

    async createRotatingGiantSword(isPlayer = true, currentSkillData, weaponImage) {
        // 🔧 动态判断位置映射
        let casterPos, targetPos;
        
        if (this.isEnemySkill) {
            // 敌方技能：敌人是施法者，玩家是目标
            casterPos = this.getCharacterPosition(false); // 敌人位置
            targetPos = this.getCharacterPosition(true);  // 玩家位置
        } else {
            // 我方技能：玩家是施法者，敌人是目标
            casterPos = this.getCharacterPosition(true);  // 玩家位置
            targetPos = this.getCharacterPosition(false); // 敌人位置
        }
        
        // 使用新的位置数据
        const startX = casterPos.x;
        const startY = casterPos.y;
        const targetCenterX = targetPos.x;
        const targetCenterY = targetPos.y;

        // 创建巨剑容器
        const giantSwordContainer = document.createElement('div');
        giantSwordContainer.className = 'giant-sword-container';
        this.effectsContainer.appendChild(giantSwordContainer);

        // 第一阶段：主剑旋转 (1.5秒 - 旋转1.3圈)
        const swords = [];
        
        // 创建主剑
        const mainSword = this.createRotatingSword(weaponImage, startX, startY, 0);
        giantSwordContainer.appendChild(mainSword);
        swords.push(mainSword);
        
        // 创建2个简单残影
        const shadow1 = this.createRotatingSword(weaponImage, startX, startY, 0);
        shadow1.style.opacity = '0.3';
        shadow1.style.filter = 'blur(1px)';
        shadow1.style.transform = 'translate(-50%, -100%) scaleY(-1) scale(0.9)';
        shadow1.style.zIndex = '199';
        giantSwordContainer.appendChild(shadow1);
        swords.push(shadow1);
        
        const shadow2 = this.createRotatingSword(weaponImage, startX, startY, 0);
        shadow2.style.opacity = '0.2';
        shadow2.style.filter = 'blur(2px)';
        shadow2.style.transform = 'translate(-50%, -100%) scaleY(-1) scale(0.8)';
        shadow2.style.zIndex = '198';
        giantSwordContainer.appendChild(shadow2);
        swords.push(shadow2);
        
        // 开始主剑旋转1.3圈
        mainSword.style.animation = 'main-sword-rotate-1-5 1.5s ease-out forwards';
        
        // 残影延迟启动，形成拖尾效果
        setTimeout(() => {
            shadow1.style.animation = 'main-sword-rotate-1-5 1.5s ease-out forwards';
        }, 100);
        
        setTimeout(() => {
            shadow2.style.animation = 'main-sword-rotate-1-5 1.5s ease-out forwards';
        }, 200);
        
        // 等待旋转完成
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // 第二阶段：在旋转中变大并飞向敌人 (1.2秒)
        // 移除残影剑，只保留主剑
        for (let i = 1; i < swords.length; i++) {
            swords[i].remove();
        }
        
        // 设置CSS变量用于飞行
        mainSword.style.setProperty('--startX', `${startX}px`);
        mainSword.style.setProperty('--startY', `${startY}px`);
        mainSword.style.setProperty('--targetX', `${targetCenterX}px`);
        mainSword.style.setProperty('--targetY', `${targetCenterY}px`);
        
        // 开始旋转变大飞行动画
        const flyAnimation = isPlayer ? 'giant-sword-center-rotate-fly-player' : 'giant-sword-center-rotate-fly-enemy';
        mainSword.style.animation = `${flyAnimation} 1.2s ease-out forwards`;
        
        // 🔧 修复：在击中时创建受击特效和动画
        setTimeout(() => {
            // 🔧 修复：使用正确的击中特效
            this.createHitEffect(targetCenterX, targetCenterY, true);
            
            // 🔧 修复：为被攻击者添加受击动画
            this.addEnemyHitAnimation();
        }, 1200);
        
        await new Promise(resolve => setTimeout(resolve, 1200));
        
        // 清理
        giantSwordContainer.remove();
    }
    
    createRotatingSword(weaponImage, centerX, centerY, startAngle) {
        const sword = document.createElement('div');
        sword.className = 'rotating-mini-sword';
        sword.style.left = `${centerX}px`;
        sword.style.top = `${centerY}px`;
        sword.style.transform = `translate(-50%, -100%) scaleY(-1) rotate(${startAngle}deg)`;
        sword.style.opacity = '1';
        
        // 武器图片处理
        if (weaponImage) {
            const weaponImg = document.createElement('img');
            weaponImg.src = weaponImage;
            weaponImg.className = 'weapon-image';
            // 🗡️ 动态调整武器图片角度
            weaponImg.style.transform = `rotate(${this.calculateInitialSwordRotation()}deg)`;
            weaponImg.onerror = () => {
                if (window.ImagePathManager) {
                    weaponImg.src = window.ImagePathManager.getWeaponImage('battle_sword.png');
                } else {
                    weaponImg.src = 'assets/images/battle_sword.png';
                }
            };
            sword.appendChild(weaponImg);
        } else {
            if (window.ImagePathManager) {
                sword.style.backgroundImage = `url('${window.ImagePathManager.getWeaponImage('battle_sword.png')}')`;
            } else {
                sword.style.backgroundImage = `url('assets/images/battle_sword.png')`;
            }
            sword.style.backgroundSize = 'contain';
            sword.style.backgroundRepeat = 'no-repeat';
            sword.style.backgroundPosition = 'center';
        }
        
        return sword;
    }
    
    createGiantSwordShadow(weaponImage, startX, startY, targetX, targetY, isPlayer, shadowIndex) {
        const shadow = document.createElement('div');
        shadow.className = 'rotating-mini-sword giant-sword-shadow';
        shadow.style.left = `${startX}px`;
        shadow.style.top = `${startY}px`;
        shadow.style.transform = `translate(-50%, -100%) scaleY(-1) rotate(468deg) scale(${1.5 - shadowIndex * 0.1})`; // 残影逐渐变小
        shadow.style.opacity = `${0.6 - shadowIndex * 0.1}`; // 逐渐变淡
        shadow.style.filter = `blur(${shadowIndex}px)`; // 逐渐模糊
        
        // 设置CSS变量用于飞行
        shadow.style.setProperty('--startX', `${startX}px`);
        shadow.style.setProperty('--startY', `${startY}px`);
        shadow.style.setProperty('--targetX', `${targetX}px`);
        shadow.style.setProperty('--targetY', `${targetY}px`);
        
        // 武器图片处理
        if (weaponImage) {
            const weaponImg = document.createElement('img');
            weaponImg.src = weaponImage;
            weaponImg.className = 'weapon-image';
            weaponImg.onerror = () => {
                if (window.ImagePathManager) {
                    weaponImg.src = window.ImagePathManager.getWeaponImage('battle_sword.png');
                } else {
                    weaponImg.src = 'assets/images/battle_sword.png';
                }
            };
            shadow.appendChild(weaponImg);
        } else {
            if (window.ImagePathManager) {
                shadow.style.backgroundImage = `url('${window.ImagePathManager.getWeaponImage('battle_sword.png')}')`;
            } else {
                shadow.style.backgroundImage = `url('assets/images/battle_sword.png')`;
            }
            shadow.style.backgroundSize = 'contain';
            shadow.style.backgroundRepeat = 'no-repeat';
            shadow.style.backgroundPosition = 'center';
        }
        
        // 创建残影专用的飞行动画（比主剑小）
        const shadowFlyAnimation = isPlayer ? 'giant-sword-shadow-fly-player' : 'giant-sword-shadow-fly-enemy';
        setTimeout(() => {
            shadow.style.animation = `${shadowFlyAnimation} 1.2s ease-out forwards`;
        }, shadowIndex * 100 + 100); // 残影动画延迟更长时间，确保主剑在前
        
        return shadow;
    }
    
    /**
     * 🔧 修复：动态判断受击动画位置
     */
    addEnemyHitAnimation() {
        // 🔧 修复：动态判断被攻击目标
        const targetSelector = !this.isEnemySkill ? '.enemy .character-sprite' : '.player .character-sprite';
        const targetSprite = document.querySelector(targetSelector);
        
        if (targetSprite) {
            // 清除之前的动画
            targetSprite.style.animation = '';
            
            // 强制重绘
            targetSprite.offsetHeight;
            
            // 添加巨剑受击动画 - 使用重击效果的剑类受击动画
            targetSprite.style.animation = 'sword-hit-shake 0.6s ease-out';
            
            console.log(`⚔️ 巨剑术：为${!this.isEnemySkill ? '敌人' : '玩家'}添加受击动画`);
            
            // 动画结束后清理
            setTimeout(() => {
                if (targetSprite && targetSprite.style.animation.includes('sword-hit-shake')) {
                    targetSprite.style.animation = '';
                }
            }, 600);
        } else {
            console.warn(`⚠️ 巨剑术：未找到目标元素，无法添加受击动画`);
        }
    }

    // 🗡️ 动态计算剑的初始旋转角度
    calculateInitialSwordRotation() {
        // 敌方技能：剑尖向下（指向玩家），我方技能：剑尖向上（指向敌人）
        return this.isEnemySkill ? 0 : 180;
    }
}

// 导出技能类（必须按此格式）
window.JuJianSkills = { JuJianSkill }; 