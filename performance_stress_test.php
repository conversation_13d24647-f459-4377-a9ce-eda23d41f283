<?php
/**
 * 一念修仙项目性能压力测试
 * 测试系统在高并发下的稳定性和响应时间
 */

require_once __DIR__ . '/setting.php';

echo "=== 一念修仙项目性能压力测试 ===\n\n";

// 测试配置
$testConfig = [
    'concurrent_users' => 10,      // 并发用户数
    'requests_per_user' => 5,      // 每用户请求数
    'timeout' => 30,               // 超时时间(秒)
    'base_url' => 'http://localhost/yinian'
];

$testResults = [];
$performanceIssues = [];
$performanceWarnings = [];
$performancePassed = [];

// 1. 数据库连接性能测试
echo "1. 数据库连接性能测试:\n";
echo "----------------------------------------\n";

$dbTestResults = [];
for ($i = 0; $i < 10; $i++) {
    $startTime = microtime(true);
    try {
        $pdo = getDatabaseConnection();
        if ($pdo) {
            $stmt = $pdo->query("SELECT 1");
            $result = $stmt->fetch();
            $endTime = microtime(true);
            $dbTestResults[] = ($endTime - $startTime) * 1000; // 转换为毫秒
        }
    } catch (Exception $e) {
        $performanceIssues[] = "数据库连接失败: " . $e->getMessage();
        echo "   ❌ 数据库连接失败\n";
        break;
    }
}

if (!empty($dbTestResults)) {
    $avgDbTime = array_sum($dbTestResults) / count($dbTestResults);
    $maxDbTime = max($dbTestResults);
    $minDbTime = min($dbTestResults);
    
    echo "   数据库连接测试结果:\n";
    echo "     平均响应时间: " . number_format($avgDbTime, 2) . "ms\n";
    echo "     最大响应时间: " . number_format($maxDbTime, 2) . "ms\n";
    echo "     最小响应时间: " . number_format($minDbTime, 2) . "ms\n";
    
    if ($avgDbTime < 50) {
        $performancePassed[] = "数据库连接性能优秀";
        echo "   ✅ 数据库连接性能优秀 (< 50ms)\n";
    } elseif ($avgDbTime < 100) {
        $performanceWarnings[] = "数据库连接性能一般";
        echo "   ⚠️  数据库连接性能一般 (50-100ms)\n";
    } else {
        $performanceIssues[] = "数据库连接性能较差";
        echo "   ❌ 数据库连接性能较差 (> 100ms)\n";
    }
}

// 2. 核心API性能测试
echo "\n2. 核心API性能测试:\n";
echo "----------------------------------------\n";

$apiEndpoints = [
    'user_info.php' => '用户信息',
    'cultivation.php?action=get_attributes' => '修炼属性',
    'equipment_integrated.php?action=get_inventory' => '装备背包',
    'immortal_arena.php?action=get_arena_info' => '竞技场信息',
    'shop.php?action=get_items' => '商店物品'
];

foreach ($apiEndpoints as $endpoint => $description) {
    echo "   测试 $description ($endpoint):\n";
    
    $apiTestResults = [];
    for ($i = 0; $i < 5; $i++) {
        $startTime = microtime(true);
        
        // 模拟HTTP请求
        $url = $testConfig['base_url'] . "/src/api/" . $endpoint;
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'timeout' => $testConfig['timeout'],
                'header' => "User-Agent: Performance Test\r\n"
            ]
        ]);
        
        $result = @file_get_contents($url, false, $context);
        $endTime = microtime(true);
        
        if ($result !== false) {
            $responseTime = ($endTime - $startTime) * 1000;
            $apiTestResults[] = $responseTime;
        } else {
            echo "     ❌ 请求失败\n";
            break;
        }
    }
    
    if (!empty($apiTestResults)) {
        $avgApiTime = array_sum($apiTestResults) / count($apiTestResults);
        echo "     平均响应时间: " . number_format($avgApiTime, 2) . "ms\n";
        
        if ($avgApiTime < 200) {
            echo "     ✅ 性能优秀\n";
        } elseif ($avgApiTime < 500) {
            echo "     ⚠️  性能一般\n";
            $performanceWarnings[] = "$description API响应较慢";
        } else {
            echo "     ❌ 性能较差\n";
            $performanceIssues[] = "$description API响应过慢";
        }
    }
}

// 3. 数据库查询性能测试
echo "\n3. 数据库查询性能测试:\n";
echo "----------------------------------------\n";

try {
    $pdo = getDatabaseConnection();
    
    $queryTests = [
        "SELECT COUNT(*) FROM users" => "用户表查询",
        "SELECT COUNT(*) FROM characters" => "角色表查询", 
        "SELECT COUNT(*) FROM game_items" => "物品表查询",
        "SELECT COUNT(*) FROM character_equipment" => "装备表查询",
        "SELECT c.*, u.username FROM characters c JOIN users u ON c.user_id = u.id LIMIT 10" => "关联查询测试"
    ];
    
    foreach ($queryTests as $query => $description) {
        $queryResults = [];
        
        for ($i = 0; $i < 3; $i++) {
            $startTime = microtime(true);
            $stmt = $pdo->query($query);
            $result = $stmt->fetchAll();
            $endTime = microtime(true);
            
            $queryResults[] = ($endTime - $startTime) * 1000;
        }
        
        $avgQueryTime = array_sum($queryResults) / count($queryResults);
        echo "   $description: " . number_format($avgQueryTime, 2) . "ms\n";
        
        if ($avgQueryTime > 100) {
            $performanceWarnings[] = "$description 查询较慢";
        }
    }
    
    $performancePassed[] = "数据库查询测试完成";
    
} catch (Exception $e) {
    $performanceIssues[] = "数据库查询测试失败: " . $e->getMessage();
    echo "   ❌ 数据库查询测试失败\n";
}

// 4. 内存使用测试
echo "\n4. 内存使用测试:\n";
echo "----------------------------------------\n";

$memoryStart = memory_get_usage(true);
$memoryPeakStart = memory_get_peak_usage(true);

// 模拟一些操作
try {
    $pdo = getDatabaseConnection();
    $stmt = $pdo->query("SELECT * FROM game_items LIMIT 100");
    $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 处理数据
    foreach ($items as $item) {
        $processed[] = array_merge($item, ['processed' => true]);
    }
    
} catch (Exception $e) {
    echo "   ⚠️  内存测试中发生错误: " . $e->getMessage() . "\n";
}

$memoryEnd = memory_get_usage(true);
$memoryPeakEnd = memory_get_peak_usage(true);

$memoryUsed = ($memoryEnd - $memoryStart) / 1024 / 1024; // MB
$memoryPeak = $memoryPeakEnd / 1024 / 1024; // MB

echo "   当前内存使用: " . number_format($memoryEnd / 1024 / 1024, 2) . " MB\n";
echo "   峰值内存使用: " . number_format($memoryPeak, 2) . " MB\n";
echo "   测试期间增加: " . number_format($memoryUsed, 2) . " MB\n";

if ($memoryPeak < 64) {
    $performancePassed[] = "内存使用合理";
    echo "   ✅ 内存使用合理 (< 64MB)\n";
} elseif ($memoryPeak < 128) {
    $performanceWarnings[] = "内存使用较高";
    echo "   ⚠️  内存使用较高 (64-128MB)\n";
} else {
    $performanceIssues[] = "内存使用过高";
    echo "   ❌ 内存使用过高 (> 128MB)\n";
}

// 5. 文件I/O性能测试
echo "\n5. 文件I/O性能测试:\n";
echo "----------------------------------------\n";

$testFile = LOGS_DIR . '/performance_test.tmp';
$testData = str_repeat("Performance test data\n", 1000);

// 写入测试
$startTime = microtime(true);
file_put_contents($testFile, $testData);
$writeTime = (microtime(true) - $startTime) * 1000;

// 读取测试
$startTime = microtime(true);
$readData = file_get_contents($testFile);
$readTime = (microtime(true) - $startTime) * 1000;

// 清理测试文件
@unlink($testFile);

echo "   文件写入时间: " . number_format($writeTime, 2) . "ms\n";
echo "   文件读取时间: " . number_format($readTime, 2) . "ms\n";

if ($writeTime < 10 && $readTime < 10) {
    $performancePassed[] = "文件I/O性能优秀";
    echo "   ✅ 文件I/O性能优秀\n";
} elseif ($writeTime < 50 && $readTime < 50) {
    $performanceWarnings[] = "文件I/O性能一般";
    echo "   ⚠️  文件I/O性能一般\n";
} else {
    $performanceIssues[] = "文件I/O性能较差";
    echo "   ❌ 文件I/O性能较差\n";
}

// 生成性能测试报告
echo "\n=== 性能测试总结 ===\n";
echo "性能测试通过: " . count($performancePassed) . " 项\n";
echo "性能警告: " . count($performanceWarnings) . " 项\n";
echo "性能问题: " . count($performanceIssues) . " 项\n\n";

if (count($performanceIssues) > 0) {
    echo "❌ 性能问题 (需要优化):\n";
    foreach ($performanceIssues as $issue) {
        echo "   • $issue\n";
    }
    echo "\n";
}

if (count($performanceWarnings) > 0) {
    echo "⚠️  性能警告 (建议优化):\n";
    foreach ($performanceWarnings as $warning) {
        echo "   • $warning\n";
    }
    echo "\n";
}

// 性能评级
$totalTests = count($performancePassed) + count($performanceWarnings) + count($performanceIssues);
$performanceScore = ($totalTests > 0) ? (count($performancePassed) / $totalTests) * 100 : 0;

echo "⚡ 性能评级: ";
if ($performanceScore >= 90) {
    echo "优秀 ({$performanceScore}%)\n";
} elseif ($performanceScore >= 80) {
    echo "良好 ({$performanceScore}%)\n";
} elseif ($performanceScore >= 70) {
    echo "一般 ({$performanceScore}%)\n";
} else {
    echo "需要优化 ({$performanceScore}%)\n";
}

echo "\n建议下一步操作:\n";
if (count($performanceIssues) > 0) {
    echo "1. 优化性能问题项\n";
    echo "2. 重新进行性能测试\n";
} else {
    echo "1. 进行更大规模的压力测试\n";
    echo "2. 建立性能监控机制\n";
    echo "3. 优化数据库索引\n";
}

?>
