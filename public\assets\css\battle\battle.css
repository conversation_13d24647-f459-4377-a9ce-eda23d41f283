/* 🏆 竞技场专用胜利面板样式 */
.arena-victory-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
}

.arena-victory-panel {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    border: 3px solid #ffd700;
    border-radius: 20px;
    padding: 30px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(255, 215, 0, 0.3);
    animation: arenaVictorySlideIn 0.5s ease-out;
}

@keyframes arenaVictorySlideIn {
    from {
        transform: translateY(-50px) scale(0.9);
        opacity: 0;
    }
    to {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

.arena-victory-header {
    text-align: center;
    margin-bottom: 25px;
    border-bottom: 2px solid #ffd700;
    padding-bottom: 15px;
}

.arena-victory-title {
    font-size: 2.2rem;
    font-weight: bold;
    margin: 0 0 10px 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    background: linear-gradient(45deg, #ffd700, #ffa500);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.arena-victory-subtitle {
    font-size: 1.1rem;
    color: #e6e6e6;
    margin: 0;
    opacity: 0.9;
}

.arena-rewards-section {
    margin-bottom: 25px;
    background: rgba(255, 215, 0, 0.1);
    border-radius: 15px;
    padding: 20px;
    border: 1px solid rgba(255, 215, 0, 0.3);
}

.arena-rewards-section h3 {
    color: #ffd700;
    margin: 0 0 15px 0;
    font-size: 1.3rem;
    text-align: center;
}

.arena-reward-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    padding: 10px 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    border: 1px solid rgba(255, 215, 0, 0.2);
}

.arena-reward-item:last-child {
    margin-bottom: 0;
}

.reward-icon {
    font-size: 1.2rem;
    margin-right: 10px;
}

.reward-label {
    color: #e6e6e6;
    font-weight: 500;
    flex: 1;
}

.reward-value {
    font-weight: bold;
    font-size: 1.1rem;
}

.reward-value.spirit-stones {
    color: #00d4aa;
    text-shadow: 0 0 5px rgba(0, 212, 170, 0.5);
}

.reward-value.rank-points.positive {
    color: #4caf50;
    text-shadow: 0 0 5px rgba(76, 175, 80, 0.5);
}

.reward-value.rank-points.negative {
    color: #f44336;
    text-shadow: 0 0 5px rgba(244, 67, 54, 0.5);
}

.ai-puppet-notice {
    color: #ffa500;
    font-size: 0.9rem;
    text-align: center;
    margin: 15px 0 0 0;
    font-style: italic;
    opacity: 0.8;
}

.arena-stats-section {
    margin-bottom: 25px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.arena-stats-section h3 {
    color: #e6e6e6;
    margin: 0 0 15px 0;
    font-size: 1.2rem;
    text-align: center;
}

.arena-stats-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 10px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-label {
    color: #b0b0b0;
    font-size: 0.95rem;
}

.stat-value {
    color: #ffd700;
    font-weight: bold;
    font-size: 1rem;
}

.arena-victory-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.arena-victory-buttons .victory-btn {
    flex: 1;
    padding: 12px 20px;
    border: none;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.arena-victory-buttons .continue-btn {
    background: linear-gradient(135deg, #4caf50, #45a049);
    color: white;
    border: 2px solid #4caf50;
}

.arena-victory-buttons .continue-btn:hover {
    background: linear-gradient(135deg, #45a049, #3d8b40);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
}

.arena-victory-buttons .exit-btn {
    background: linear-gradient(135deg, #607d8b, #546e7a);
    color: white;
    border: 2px solid #607d8b;
}

.arena-victory-buttons .exit-btn:hover {
    background: linear-gradient(135deg, #546e7a, #455a64);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(96, 125, 139, 0.4);
}

/* 📱 移动端适配 */
@media (max-width: 768px) {
    .arena-victory-panel {
        padding: 20px;
        margin: 20px;
        max-height: 85vh;
    }

    .arena-victory-title {
        font-size: 1.8rem;
    }

    .arena-victory-subtitle {
        font-size: 1rem;
    }

    .arena-rewards-section,
    .arena-stats-section {
        padding: 15px;
    }

    .arena-reward-item {
        padding: 8px 12px;
    }

    .arena-victory-buttons {
        flex-direction: column;
        gap: 10px;
    }

    .arena-victory-buttons .victory-btn {
        padding: 15px 20px;
        font-size: 1.1rem;
    }
}

/* 🛡️ 护盾显示样式 */
.shield-display {
    color: #4da6ff;
    font-weight: bold;
    text-shadow: 0 0 4px #4da6ff;
    margin-left: 8px;
    font-size: 0.9em;
}

/* 🛡️ 护盾吸收效果动画 */
.shield-absorb-effect {
    position: absolute;
    font-size: 20px;
    font-weight: bold;
    color: #4da6ff;
    text-shadow: 0 0 8px #4da6ff, 2px 2px 4px rgba(0, 0, 0, 0.8);
    pointer-events: none;
    z-index: 1000;
    animation: shieldAbsorbFloat 1.5s ease-out forwards;
    border: 2px solid #4da6ff;
    border-radius: 8px;
    padding: 4px 8px;
    background: rgba(77, 166, 255, 0.2);
    backdrop-filter: blur(4px);
}

@keyframes shieldAbsorbFloat {
    0% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    50% {
        opacity: 0.8;
        transform: translateY(-20px) scale(1.1);
    }
    100% {
        opacity: 0;
        transform: translateY(-40px) scale(0.8);
    }
}
