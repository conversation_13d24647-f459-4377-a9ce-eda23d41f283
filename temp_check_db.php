<?php
try {
    $pdo = new PDO('mysql:host=127.0.0.1;port=3306;dbname=yn_game;charset=utf8mb4', 'root', 'root');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== 数据库连接成功 ===\n";
    
    // 检查表是否存在
    $tables = ['equipment_sets', 'game_item_sets'];
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            $exists = $stmt->rowCount() > 0;
            echo "表 $table: " . ($exists ? '存在' : '不存在') . "\n";
            
            if ($exists) {
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
                $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
                echo "  - 记录数: $count\n";
                
                if ($count > 0) {
                    $stmt = $pdo->query("SELECT * FROM $table LIMIT 3");
                    $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    echo "  - 示例数据:\n";
                    foreach ($rows as $row) {
                        echo "    ID: {$row['id']}, 名称: " . (isset($row['set_name']) ? $row['set_name'] : 'N/A') . "\n";
                    }
                }
            }
        } catch (Exception $e) {
            echo "检查表 $table 时出错: " . $e->getMessage() . "\n";
        }
    }
    
    // 检查game_items表中的set_id字段
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM game_items WHERE set_id IS NOT NULL");
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "\ngame_items表中有set_id的物品数: $count\n";
        
        if ($count > 0) {
            $stmt = $pdo->query("SELECT id, item_name, set_id FROM game_items WHERE set_id IS NOT NULL LIMIT 5");
            $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "示例套装物品:\n";
            foreach ($items as $item) {
                echo "  ID: {$item['id']}, 名称: {$item['item_name']}, 套装ID: {$item['set_id']}\n";
            }
        }
    } catch (Exception $e) {
        echo "检查game_items表时出错: " . $e->getMessage() . "\n";
    }
    
} catch (Exception $e) {
    echo '数据库连接失败: ' . $e->getMessage() . "\n";
}
?>
